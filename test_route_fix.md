# Route Fix for Solicitor Claim Detail Page

## Problem
When tapping the "View" button in the My Claims List page (`/solicitor/my-claims`), the app was throwing an error:

```
Need to implement /solicitor/claim-detail
```

This was happening because the route `/solicitor/claim-detail` was not registered in the app's routing system.

## Root Cause Analysis

1. **Two ClaimDetailPage classes exist**:
   - **Solicitor Portal**: `lib/src/features/solicitor_portal/presentation/pages/claim_detail_page.dart` 
     - Route: `/solicitor/claim-detail`
   - **Claimant Portal**: `lib/src/features/claimant_portal/presentation/pages/claim_detail_page.dart`
     - Route: `/claimant/claim-detail`

2. **My Claims List Page** imports the solicitor version and uses `ClaimDetailPage.routeName` which is `/solicitor/claim-detail`

3. **App Router** (`lib/src/core/app_widget.dart`) only had:
   - The claimant route registered in `onGenerateRoute` (line 197)
   - An old `/solicitor-case-detail` route (line 283) but not the correct `/solicitor/claim-detail`

## Solution

Added the missing route handler in `app_widget.dart`:

```dart
if (settings.name == ClaimDetailPage.routeName) {
  final caseId = settings.arguments as String;
  return MaterialPageRoute(
    builder: (context) {
      return ClaimDetailPage(claimId: caseId);
    },
  );
}
```

## Files Modified

### `lib/src/core/app_widget.dart`
- Added route handler for `/solicitor/claim-detail` in the `onGenerateRoute` method
- This allows the solicitor portal's ClaimDetailPage to be properly navigated to

## Testing

1. **Navigation Flow**:
   - Solicitor Dashboard → My Claims → View button → Claim Detail Page
   - Should now work without throwing the "Need to implement" error

2. **Route Parameters**:
   - The claim ID is properly passed as arguments
   - The ClaimDetailPage receives the correct claimId parameter

3. **Existing Functionality**:
   - Claimant portal claim detail page still works (different route)
   - Other solicitor portal routes remain unaffected

## Verification Steps

1. Navigate to Solicitor Dashboard
2. Go to "My Claims" page
3. Click "View" button on any claim
4. Should navigate to claim detail page without errors
5. Verify claim details load correctly with the passed claim ID

## Notes

- The fix maintains backward compatibility
- Both solicitor and claimant claim detail pages can coexist
- The route naming follows the established pattern: `/portal-type/page-name`
- No changes needed to existing components or data flow
