# Use official Flutter Docker image with latest stable version
FROM ghcr.io/cirruslabs/flutter:3.32.2 AS build-env

# Configure Flutter for web and disable analytics
RUN flutter config --no-analytics && \
    flutter config --enable-web && \
    flutter doctor -v

# Set working directory
WORKDIR /usr/src/app

# Copy pubspec files first for better Docker layer caching
COPY pubspec.yaml ./
COPY pubspec.lock* ./

# Get dependencies (this layer will be cached if pubspec files don't change)
RUN flutter pub get

# Copy source code and assets
COPY lib/ lib/
COPY web/ web/
COPY assets/ assets/

# Copy analysis_options.yaml if it exists
COPY analysis_options.yaml* ./

# Copy additional configuration files if they exist
COPY dotenv* ./

# Verify Flutter installation and dependencies
RUN flutter doctor -v && flutter pub deps

# Build the Flutter web app with optimizations
RUN flutter build web \
    --release \
    --dart-define=FLUTTER_WEB_USE_SKIA=true \
    --dart-define=FLUTTER_WEB_AUTO_DETECT=true \
    --verbose

# Copy environment files to build output (if they exist)
RUN if [ -f dotenv ]; then \
        cp dotenv /usr/src/app/build/web/ && \
        echo "Copied dotenv file to build output"; \
    fi

# Copy nginx.conf to build directory if it exists
RUN if [ -f /usr/src/app/web/nginx.conf ]; then \
    cp /usr/src/app/web/nginx.conf /usr/src/app/build/web/nginx.conf; \
    echo "Found and copied nginx.conf"; \
fi

# Create a lightweight production image with latest nginx
FROM nginx:1.27-alpine

# Install security updates, curl for health checks, and clean up
RUN apk update && \
    apk upgrade && \
    apk add --no-cache ca-certificates curl && \
    rm -rf /var/cache/apk/*

# Copy the built app to the web directory
COPY --from=build-env /usr/src/app/build/web /usr/share/nginx/html

# Copy nginx config if it exists using a run command to avoid linting issues
RUN if [ -f /usr/share/nginx/html/nginx.conf ]; then \
    cp /usr/share/nginx/html/nginx.conf /etc/nginx/conf.d/default.conf; \
    echo "Found and applied custom nginx config"; \
fi

# Create an optimized nginx configuration if not provided
RUN if [ ! -f /etc/nginx/conf.d/default.conf ]; then \
    echo 'server { \
        listen 80; \
        server_name _; \
        root /usr/share/nginx/html; \
        index index.html; \
        \
        # Security headers \
        add_header X-Frame-Options "SAMEORIGIN" always; \
        add_header X-Content-Type-Options "nosniff" always; \
        add_header X-XSS-Protection "1; mode=block" always; \
        add_header Referrer-Policy "strict-origin-when-cross-origin" always; \
        \
        # Gzip compression \
        gzip on; \
        gzip_vary on; \
        gzip_min_length 1024; \
        gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json; \
        \
        # Cache static assets \
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ { \
            expires 1y; \
            add_header Cache-Control "public, immutable"; \
        } \
        \
        # Flutter web routing \
        location / { \
            try_files $uri $uri/ /index.html; \
            add_header Cache-Control "no-cache, no-store, must-revalidate"; \
        } \
    }' > /etc/nginx/conf.d/default.conf; \
    echo "Created optimized nginx configuration"; \
fi

# Add metadata
LABEL maintainer="3Pay Global Team" \
      description="3Pay Global Flutter Web Application" \
      version="1.0.0"

# Nginx user already exists in Alpine image, just ensure proper setup

# Set proper permissions and create necessary directories
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    chown nginx:nginx /var/run

# Switch to non-root user
USER nginx

# Expose the web server port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Start NGINX
CMD ["nginx", "-g", "daemon off;"]
