import 'dart:math' as math;
import '../models/cache_entry.dart';
import '../utils/cache_utils.dart';

/// Cache eviction policies
enum EvictionPolicy {
  /// Least Recently Used
  lru,

  /// Least Frequently Used
  lfu,

  /// First In First Out
  fifo,

  /// Time-based expiration
  ttl,

  /// Size-based eviction
  size,
}

/// Cache policy configuration
class CachePolicy {
  /// Maximum cache size in bytes
  final int maxSizeBytes;

  /// Maximum number of entries
  final int maxEntries;

  /// Default TTL for cache entries
  final Duration defaultTtl;

  /// Eviction policy to use
  final EvictionPolicy evictionPolicy;

  /// Memory pressure threshold (0.0 to 1.0)
  final double memoryPressureThreshold;

  /// Cleanup interval
  final Duration cleanupInterval;

  /// Whether to enable cache compression
  final bool enableCompression;

  /// Whether to enable cache persistence
  final bool enablePersistence;

  /// Maximum age for cache entries before forced eviction
  final Duration maxAge;

  /// Minimum access count to keep entries during pressure
  final int minAccessCount;

  const CachePolicy({
    this.maxSizeBytes = 100 * 1024 * 1024, // 100MB default
    this.maxEntries = 10000,
    this.defaultTtl = const Duration(hours: 6),
    this.evictionPolicy = EvictionPolicy.lru,
    this.memoryPressureThreshold = 0.8,
    this.cleanupInterval = const Duration(minutes: 30),
    this.enableCompression = true,
    this.enablePersistence = true,
    this.maxAge = const Duration(days: 7),
    this.minAccessCount = 2,
  });

  /// Create a policy for URL caching
  factory CachePolicy.forUrls() {
    return const CachePolicy(
      maxSizeBytes: 10 * 1024 * 1024, // 10MB
      maxEntries: 5000,
      defaultTtl: Duration(hours: 1),
      evictionPolicy: EvictionPolicy.ttl,
      maxAge: Duration(hours: 6),
    );
  }

  /// Create a policy for metadata caching
  factory CachePolicy.forMetadata() {
    return const CachePolicy(
      maxSizeBytes: 50 * 1024 * 1024, // 50MB
      maxEntries: 10000,
      defaultTtl: Duration(hours: 6),
      evictionPolicy: EvictionPolicy.lru,
      maxAge: Duration(days: 1),
    );
  }

  /// Create a policy for content caching
  factory CachePolicy.forContent() {
    return const CachePolicy(
      maxSizeBytes: 200 * 1024 * 1024, // 200MB
      maxEntries: 1000,
      defaultTtl: Duration(hours: 24),
      evictionPolicy: EvictionPolicy.lfu,
      maxAge: Duration(days: 7),
      minAccessCount: 1,
    );
  }

  /// Create a policy for permission caching
  factory CachePolicy.forPermissions() {
    return const CachePolicy(
      maxSizeBytes: 5 * 1024 * 1024, // 5MB
      maxEntries: 2000,
      defaultTtl: Duration(hours: 2),
      evictionPolicy: EvictionPolicy.ttl,
      maxAge: Duration(hours: 12),
    );
  }

  /// Check if cache is under memory pressure
  bool isMemoryPressure(int currentSize) {
    return currentSize > (maxSizeBytes * memoryPressureThreshold);
  }

  /// Check if cache has too many entries
  bool hasTooManyEntries(int currentEntries) {
    return currentEntries > maxEntries;
  }

  /// Get TTL for a specific cache type
  Duration getTtlForType(CacheType type) {
    return type.defaultTtl;
  }

  /// Check if entry should be evicted based on policy
  bool shouldEvict(CacheEntry entry, int currentSize, int currentEntries) {
    // Always evict expired entries
    if (entry.isExpired) return true;

    // Evict if too old
    if (entry.age > maxAge) return true;

    // Apply eviction policy
    switch (evictionPolicy) {
      case EvictionPolicy.ttl:
        return entry.isExpired;

      case EvictionPolicy.size:
        return isMemoryPressure(currentSize);

      case EvictionPolicy.lru:
      case EvictionPolicy.lfu:
      case EvictionPolicy.fifo:
        // These require comparison with other entries
        return false;
    }
  }

  /// Get eviction candidates based on policy
  List<CacheEntry> getEvictionCandidates(
    List<CacheEntry> allEntries,
    int targetEvictionCount,
  ) {
    final candidates = <CacheEntry>[];

    // First, add all expired entries
    final expired = allEntries.where((e) => e.isExpired).toList();
    candidates.addAll(expired);

    if (candidates.length >= targetEvictionCount) {
      return candidates.take(targetEvictionCount).toList();
    }

    // Then apply eviction policy for remaining entries
    final remaining = allEntries.where((e) => !e.isExpired).toList();
    final needed = targetEvictionCount - candidates.length;

    switch (evictionPolicy) {
      case EvictionPolicy.lru:
        remaining.sort((a, b) => a.lastAccessedAt.compareTo(b.lastAccessedAt));
        candidates.addAll(remaining.take(needed));
        break;

      case EvictionPolicy.lfu:
        remaining.sort((a, b) => a.accessCount.compareTo(b.accessCount));
        candidates.addAll(remaining.take(needed));
        break;

      case EvictionPolicy.fifo:
        remaining.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        candidates.addAll(remaining.take(needed));
        break;

      case EvictionPolicy.ttl:
        // Sort by expiration time (closest to expiring first)
        remaining.sort((a, b) {
          final aExpiry =
              a.expiresAt ?? DateTime.now().add(const Duration(days: 365));
          final bExpiry =
              b.expiresAt ?? DateTime.now().add(const Duration(days: 365));
          return aExpiry.compareTo(bExpiry);
        });
        candidates.addAll(remaining.take(needed));
        break;

      case EvictionPolicy.size:
        // Sort by size (largest first)
        remaining.sort((a, b) => b.sizeBytes.compareTo(a.sizeBytes));
        candidates.addAll(remaining.take(needed));
        break;
    }

    return candidates;
  }

  /// Get priority-based eviction candidates
  List<CacheEntry> getPriorityEvictionCandidates(
    List<CacheEntry> allEntries,
    int targetEvictionCount,
  ) {
    // Calculate priority for each entry
    final entriesWithPriority =
        allEntries.map((entry) {
          final priority = CacheUtils.getCachePriority(entry);
          return MapEntry(entry, priority);
        }).toList();

    // Sort by priority (lowest first = first to evict)
    entriesWithPriority.sort((a, b) => a.value.compareTo(b.value));

    return entriesWithPriority
        .take(targetEvictionCount)
        .map((e) => e.key)
        .toList();
  }

  /// Calculate how many entries to evict
  int calculateEvictionCount(int currentEntries, int currentSize) {
    int evictionCount = 0;

    // Evict based on entry count
    if (hasTooManyEntries(currentEntries)) {
      evictionCount = currentEntries - (maxEntries * 0.8).round();
    }

    // Evict based on memory pressure
    if (isMemoryPressure(currentSize)) {
      final targetSize = (maxSizeBytes * 0.7).round();
      final sizeReduction = currentSize - targetSize;
      // Estimate entries to evict based on average entry size
      final avgEntrySize =
          currentEntries > 0 ? currentSize / currentEntries : 1024;
      final sizeBasedEviction = (sizeReduction / avgEntrySize).ceil();
      evictionCount = math.max(evictionCount, sizeBasedEviction);
    }

    return evictionCount;
  }

  /// Check if cache should be cleaned up
  bool shouldCleanup(DateTime lastCleanup) {
    return DateTime.now().difference(lastCleanup) > cleanupInterval;
  }

  /// Create a copy with modified values
  CachePolicy copyWith({
    int? maxSizeBytes,
    int? maxEntries,
    Duration? defaultTtl,
    EvictionPolicy? evictionPolicy,
    double? memoryPressureThreshold,
    Duration? cleanupInterval,
    bool? enableCompression,
    bool? enablePersistence,
    Duration? maxAge,
    int? minAccessCount,
  }) {
    return CachePolicy(
      maxSizeBytes: maxSizeBytes ?? this.maxSizeBytes,
      maxEntries: maxEntries ?? this.maxEntries,
      defaultTtl: defaultTtl ?? this.defaultTtl,
      evictionPolicy: evictionPolicy ?? this.evictionPolicy,
      memoryPressureThreshold:
          memoryPressureThreshold ?? this.memoryPressureThreshold,
      cleanupInterval: cleanupInterval ?? this.cleanupInterval,
      enableCompression: enableCompression ?? this.enableCompression,
      enablePersistence: enablePersistence ?? this.enablePersistence,
      maxAge: maxAge ?? this.maxAge,
      minAccessCount: minAccessCount ?? this.minAccessCount,
    );
  }

  @override
  String toString() {
    return 'CachePolicy(maxSize: ${CacheUtils.formatSize(maxSizeBytes)}, '
        'maxEntries: $maxEntries, defaultTtl: ${CacheUtils.formatDuration(defaultTtl)}, '
        'policy: $evictionPolicy)';
  }
}

/// Cache configuration for different environments
class CacheConfiguration {
  /// Development environment configuration
  static CachePolicy get development {
    return const CachePolicy(
      maxSizeBytes: 50 * 1024 * 1024, // 50MB
      maxEntries: 5000,
      defaultTtl: Duration(minutes: 30),
      cleanupInterval: Duration(minutes: 10),
      enablePersistence: false, // Disable persistence in development
    );
  }

  /// Production environment configuration
  static CachePolicy get production {
    return const CachePolicy(
      maxSizeBytes: 200 * 1024 * 1024, // 200MB
      maxEntries: 20000,
      defaultTtl: Duration(hours: 6),
      cleanupInterval: Duration(hours: 1),
      enablePersistence: true,
    );
  }

  /// Testing environment configuration
  static CachePolicy get testing {
    return const CachePolicy(
      maxSizeBytes: 10 * 1024 * 1024, // 10MB
      maxEntries: 1000,
      defaultTtl: Duration(minutes: 5),
      cleanupInterval: Duration(minutes: 1),
      enablePersistence: false,
    );
  }
}
