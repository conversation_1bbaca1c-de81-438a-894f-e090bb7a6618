import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../models/cache_entry.dart';
import '../services/logger_service.dart';

/// Interface for cache storage implementations
abstract class CacheStorage {
  /// Store a cache entry
  Future<void> store(String key, CacheEntry entry);

  /// Retrieve a cache entry
  Future<CacheEntry?> retrieve(String key);

  /// Remove a cache entry
  Future<void> remove(String key);

  /// Clear all cache entries
  Future<void> clear();

  /// Get all cache keys
  Future<List<String>> getAllKeys();

  /// Get storage size in bytes
  Future<int> getStorageSize();

  /// Check if storage is available
  Future<bool> isAvailable();

  /// Initialize storage
  Future<void> initialize();

  /// Dispose storage resources
  Future<void> dispose();
}

/// In-memory cache storage implementation
class MemoryCacheStorage implements CacheStorage {
  final Map<String, CacheEntry> _cache = {};
  bool _isInitialized = false;

  @override
  Future<void> initialize() async {
    _isInitialized = true;
    LoggerService.debug('Memory cache storage initialized');
  }

  @override
  Future<void> store(String key, CacheEntry entry) async {
    if (!_isInitialized) await initialize();
    _cache[key] = entry;
    LoggerService.debug('Stored cache entry in memory: $key');
  }

  @override
  Future<CacheEntry?> retrieve(String key) async {
    if (!_isInitialized) await initialize();
    final entry = _cache[key];
    if (entry != null && entry.isValid) {
      // Update access information
      _cache[key] = entry.copyWithAccess();
      LoggerService.debug('Retrieved cache entry from memory: $key');
      return _cache[key];
    } else if (entry != null && entry.isExpired) {
      // Remove expired entry
      _cache.remove(key);
      LoggerService.debug('Removed expired cache entry from memory: $key');
    }
    return null;
  }

  @override
  Future<void> remove(String key) async {
    if (!_isInitialized) await initialize();
    _cache.remove(key);
    LoggerService.debug('Removed cache entry from memory: $key');
  }

  @override
  Future<void> clear() async {
    if (!_isInitialized) await initialize();
    final count = _cache.length;
    _cache.clear();
    LoggerService.debug('Cleared $count cache entries from memory');
  }

  @override
  Future<List<String>> getAllKeys() async {
    if (!_isInitialized) await initialize();
    return _cache.keys.toList();
  }

  @override
  Future<int> getStorageSize() async {
    if (!_isInitialized) await initialize();
    int totalSize = 0;
    for (final entry in _cache.values) {
      totalSize += entry.sizeBytes;
    }
    return totalSize;
  }

  @override
  Future<bool> isAvailable() async {
    return true; // Memory storage is always available
  }

  @override
  Future<void> dispose() async {
    _cache.clear();
    _isInitialized = false;
    LoggerService.debug('Memory cache storage disposed');
  }

  /// Get number of entries in memory cache
  int get entryCount => _cache.length;

  /// Get all entries (for debugging)
  Map<String, CacheEntry> get allEntries => Map.unmodifiable(_cache);
}

/// Disk-based cache storage implementation
class DiskCacheStorage implements CacheStorage {
  late Directory _cacheDirectory;
  bool _isInitialized = false;
  static const String _cacheDirectoryName = '3pay_document_cache';

  @override
  Future<void> initialize() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = Directory('${appDir.path}/$_cacheDirectoryName');

      if (!await _cacheDirectory.exists()) {
        await _cacheDirectory.create(recursive: true);
      }

      _isInitialized = true;
      LoggerService.debug(
        'Disk cache storage initialized at: ${_cacheDirectory.path}',
      );
    } catch (e) {
      LoggerService.error('Failed to initialize disk cache storage', e);
      rethrow;
    }
  }

  @override
  Future<void> store(String key, CacheEntry entry) async {
    if (!_isInitialized) await initialize();

    try {
      final file = File(
        '${_cacheDirectory.path}/${_sanitizeFileName(key)}.json',
      );
      // Simple JSON encoding without using generated methods
      final jsonData = jsonEncode({
        'data': entry.data,
        'createdAt': entry.createdAt.toIso8601String(),
        'lastAccessedAt': entry.lastAccessedAt.toIso8601String(),
        'expiresAt': entry.expiresAt?.toIso8601String(),
        'sizeBytes': entry.sizeBytes,
        'accessCount': entry.accessCount,
        'key': entry.key,
        'type': entry.type.value,
        'metadata': entry.metadata,
      });
      await file.writeAsString(jsonData);
      LoggerService.debug('Stored cache entry to disk: $key');
    } catch (e) {
      LoggerService.error('Failed to store cache entry to disk: $key', e);
      rethrow;
    }
  }

  @override
  Future<CacheEntry?> retrieve(String key) async {
    if (!_isInitialized) await initialize();

    try {
      final file = File(
        '${_cacheDirectory.path}/${_sanitizeFileName(key)}.json',
      );

      if (!await file.exists()) {
        return null;
      }

      final jsonData = await file.readAsString();
      final json = jsonDecode(jsonData) as Map<String, dynamic>;

      // Parse cache type
      CacheType? type;
      for (final cacheType in CacheType.values) {
        if (cacheType.value == json['type']) {
          type = cacheType;
          break;
        }
      }
      if (type == null) return null;

      // Create cache entry from JSON
      final entry = CacheEntry(
        data: json['data'],
        createdAt: DateTime.parse(json['createdAt']),
        lastAccessedAt: DateTime.parse(json['lastAccessedAt']),
        expiresAt:
            json['expiresAt'] != null
                ? DateTime.parse(json['expiresAt'])
                : null,
        sizeBytes: json['sizeBytes'],
        accessCount: json['accessCount'],
        key: json['key'],
        type: type,
        metadata: json['metadata'],
      );

      if (entry.isValid) {
        // Update access information and store back
        final updatedEntry = entry.copyWithAccess();
        await store(key, updatedEntry);
        LoggerService.debug('Retrieved cache entry from disk: $key');
        return updatedEntry;
      } else {
        // Remove expired entry
        await file.delete();
        LoggerService.debug('Removed expired cache entry from disk: $key');
        return null;
      }
    } catch (e) {
      LoggerService.error('Failed to retrieve cache entry from disk: $key', e);
      return null;
    }
  }

  @override
  Future<void> remove(String key) async {
    if (!_isInitialized) await initialize();

    try {
      final file = File(
        '${_cacheDirectory.path}/${_sanitizeFileName(key)}.json',
      );
      if (await file.exists()) {
        await file.delete();
        LoggerService.debug('Removed cache entry from disk: $key');
      }
    } catch (e) {
      LoggerService.error('Failed to remove cache entry from disk: $key', e);
    }
  }

  @override
  Future<void> clear() async {
    if (!_isInitialized) await initialize();

    try {
      final files = await _cacheDirectory.list().toList();
      int count = 0;

      for (final file in files) {
        if (file is File && file.path.endsWith('.json')) {
          await file.delete();
          count++;
        }
      }

      LoggerService.debug('Cleared $count cache entries from disk');
    } catch (e) {
      LoggerService.error('Failed to clear disk cache', e);
    }
  }

  @override
  Future<List<String>> getAllKeys() async {
    if (!_isInitialized) await initialize();

    try {
      final files = await _cacheDirectory.list().toList();
      final keys = <String>[];

      for (final file in files) {
        if (file is File && file.path.endsWith('.json')) {
          final fileName = file.path.split('/').last;
          final key = fileName.substring(
            0,
            fileName.length - 5,
          ); // Remove .json
          keys.add(_unsanitizeFileName(key));
        }
      }

      return keys;
    } catch (e) {
      LoggerService.error('Failed to get all cache keys from disk', e);
      return [];
    }
  }

  @override
  Future<int> getStorageSize() async {
    if (!_isInitialized) await initialize();

    try {
      final files = await _cacheDirectory.list().toList();
      int totalSize = 0;

      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      LoggerService.error('Failed to calculate disk cache size', e);
      return 0;
    }
  }

  @override
  Future<bool> isAvailable() async {
    try {
      if (!_isInitialized) await initialize();
      return await _cacheDirectory.exists();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> dispose() async {
    _isInitialized = false;
    LoggerService.debug('Disk cache storage disposed');
  }

  /// Sanitize filename for disk storage
  String _sanitizeFileName(String key) {
    return key.replaceAll(RegExp(r'[^a-zA-Z0-9_-]'), '_');
  }

  /// Unsanitize filename from disk storage
  String _unsanitizeFileName(String sanitizedKey) {
    // For now, just return as-is since we can't reverse the sanitization
    // In a more sophisticated implementation, we might store a mapping
    return sanitizedKey;
  }

  /// Clean up expired entries
  Future<void> cleanupExpired() async {
    if (!_isInitialized) await initialize();

    try {
      final keys = await getAllKeys();
      int cleanedCount = 0;

      for (final key in keys) {
        final entry = await retrieve(key);
        if (entry == null) {
          cleanedCount++;
        }
      }

      LoggerService.debug(
        'Cleaned up $cleanedCount expired entries from disk cache',
      );
    } catch (e) {
      LoggerService.error('Failed to cleanup expired disk cache entries', e);
    }
  }
}
