import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as li;
import '../models/performance_metrics.dart';
import '../services/performance_monitoring_service.dart';
import '../services/logger_service.dart';

/// Performance monitoring dashboard widget
class PerformanceDashboard extends ConsumerStatefulWidget {
  final bool showDetailedMetrics;
  final Duration refreshInterval;

  const PerformanceDashboard({
    super.key,
    this.showDetailedMetrics = true,
    this.refreshInterval = const Duration(seconds: 30),
  });

  @override
  ConsumerState<PerformanceDashboard> createState() =>
      _PerformanceDashboardState();
}

class _PerformanceDashboardState extends ConsumerState<PerformanceDashboard> {
  final PerformanceMonitoringService _performanceService =
      PerformanceMonitoringService();
  PerformanceSnapshot? _currentSnapshot;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadPerformanceData();
    _startAutoRefresh();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: theme.radius,
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme),
          const SizedBox(height: 16),
          if (_isLoading && _currentSnapshot == null)
            _buildLoadingState(theme)
          else if (_errorMessage != null)
            _buildErrorState(theme)
          else if (_currentSnapshot != null)
            _buildDashboardContent(theme)
          else
            _buildEmptyState(theme),
        ],
      ),
    );
  }

  Widget _buildHeader(ShadThemeData theme) {
    return Row(
      children: [
        Icon(
          li.LucideIcons.activity,
          size: 24,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Performance Dashboard',
                style: theme.textTheme.h3.copyWith(
                  color: theme.colorScheme.foreground,
                ),
              ),
              if (_currentSnapshot != null)
                Row(
                  children: [
                    _buildHealthIndicator(
                      theme,
                      _currentSnapshot!.overallHealth,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _getHealthStatusText(_currentSnapshot!.overallHealth),
                      style: theme.textTheme.small.copyWith(
                        color: _getHealthStatusColor(
                          theme,
                          _currentSnapshot!.overallHealth,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      'Last updated: ${_formatTime(_currentSnapshot!.timestamp)}',
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
        ShadTooltip(
          builder: (context) => const Text('Refresh performance data'),
          child: ShadIconButton.ghost(
            icon: Icon(
              _isLoading ? li.LucideIcons.loader2 : li.LucideIcons.refreshCw,
              size: 16,
            ),
            onPressed: _isLoading ? null : _loadPerformanceData,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState(ShadThemeData theme) {
    return const Center(
      child: Column(
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading performance data...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.destructive.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.destructive.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            li.LucideIcons.alertCircle,
            size: 20,
            color: theme.colorScheme.destructive,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Failed to load performance data',
                  style: theme.textTheme.large.copyWith(
                    color: theme.colorScheme.destructive,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  _errorMessage!,
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.destructive,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ShadThemeData theme) {
    return Center(
      child: Column(
        children: [
          Icon(
            li.LucideIcons.barChart3,
            size: 48,
            color: theme.colorScheme.mutedForeground,
          ),
          const SizedBox(height: 16),
          Text(
            'No performance data available',
            style: theme.textTheme.h4.copyWith(
              color: theme.colorScheme.foreground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Performance metrics will appear here once operations are performed',
            style: theme.textTheme.large.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardContent(ShadThemeData theme) {
    final snapshot = _currentSnapshot!;

    return Column(
      children: [
        _buildOverviewCards(theme, snapshot),
        if (widget.showDetailedMetrics) ...[
          const SizedBox(height: 24),
          _buildOperationMetrics(theme, snapshot),
          const SizedBox(height: 24),
          _buildSystemMetrics(theme, snapshot),
        ],
      ],
    );
  }

  Widget _buildOverviewCards(
    ShadThemeData theme,
    PerformanceSnapshot snapshot,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildMetricCard(
            theme,
            'Operations',
            snapshot.operationStats.values
                .map((s) => s.totalOperations)
                .fold(0, (a, b) => a + b)
                .toString(),
            li.LucideIcons.activity,
            _getOverallSuccessRate(snapshot),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildMetricCard(
            theme,
            'Avg Response',
            '${_getOverallAverageResponse(snapshot).toStringAsFixed(0)}ms',
            li.LucideIcons.clock,
            _getResponseTimeHealth(snapshot),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildMetricCard(
            theme,
            'Cache Hit Rate',
            '${snapshot.cacheMetrics.hitRatePercent.toStringAsFixed(1)}%',
            li.LucideIcons.database,
            snapshot.cacheMetrics.hitRatePercent >= 80 ? 1.0 : 0.5,
          ),
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    ShadThemeData theme,
    String title,
    String value,
    IconData icon,
    double healthScore,
  ) {
    final healthColor =
        healthScore >= 0.8
            ? Colors.green
            : healthScore >= 0.6
            ? Colors.orange
            : theme.colorScheme.destructive;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: theme.colorScheme.primary),
              const Spacer(),
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: healthColor,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: theme.textTheme.h3.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.foreground,
            ),
          ),
          Text(
            title,
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOperationMetrics(
    ShadThemeData theme,
    PerformanceSnapshot snapshot,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Operation Performance',
            style: theme.textTheme.h4.copyWith(
              color: theme.colorScheme.foreground,
            ),
          ),
          const SizedBox(height: 16),
          ...snapshot.operationStats.entries.map(
            (entry) => _buildOperationRow(theme, entry.key, entry.value),
          ),
        ],
      ),
    );
  }

  Widget _buildOperationRow(
    ShadThemeData theme,
    String operationType,
    PerformanceStatistics stats,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              operationType.toUpperCase(),
              style: theme.textTheme.large.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.foreground,
              ),
            ),
          ),
          Expanded(
            child: Text(
              '${stats.totalOperations}',
              style: theme.textTheme.large.copyWith(
                color: theme.colorScheme.foreground,
              ),
            ),
          ),
          Expanded(
            child: Text(
              '${(stats.successRate * 100).toStringAsFixed(1)}%',
              style: theme.textTheme.large.copyWith(
                color: _getSuccessRateColor(theme, stats.successRate),
              ),
            ),
          ),
          Expanded(
            child: Text(
              '${stats.averageDurationMs.toStringAsFixed(0)}ms',
              style: theme.textTheme.large.copyWith(
                color: theme.colorScheme.foreground,
              ),
            ),
          ),
          _buildHealthIndicator(theme, stats.healthStatus),
        ],
      ),
    );
  }

  Widget _buildSystemMetrics(
    ShadThemeData theme,
    PerformanceSnapshot snapshot,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildSystemMetricCard(theme, 'System Resources', [
            'CPU: ${snapshot.systemMetrics.cpuUsagePercent.toStringAsFixed(1)}%',
            'Memory: ${snapshot.systemMetrics.memoryUsagePercent.toStringAsFixed(1)}%',
            'Battery: ${(snapshot.systemMetrics.batteryLevel * 100).toStringAsFixed(0)}%',
          ], li.LucideIcons.cpu),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSystemMetricCard(theme, 'Network', [
            'Type: ${snapshot.networkMetrics.connectionType}',
            'Speed: ${snapshot.networkMetrics.bandwidthMbps.toStringAsFixed(1)} Mbps',
            'Latency: ${snapshot.networkMetrics.latencyMs.toStringAsFixed(0)}ms',
          ], li.LucideIcons.wifi),
        ),
      ],
    );
  }

  Widget _buildSystemMetricCard(
    ShadThemeData theme,
    String title,
    List<String> metrics,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: theme.colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                title,
                style: theme.textTheme.large.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.foreground,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...metrics.map(
            (metric) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                metric,
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthIndicator(
    ShadThemeData theme,
    PerformanceHealthStatus status,
  ) {
    IconData icon;
    Color color;

    switch (status) {
      case PerformanceHealthStatus.healthy:
        icon = li.LucideIcons.checkCircle2;
        color = Colors.green;
        break;
      case PerformanceHealthStatus.degraded:
        icon = li.LucideIcons.alertTriangle;
        color = Colors.yellow;
        break;
      case PerformanceHealthStatus.warning:
        icon = li.LucideIcons.alertTriangle;
        color = Colors.orange;
        break;
      case PerformanceHealthStatus.critical:
        icon = li.LucideIcons.alertCircle;
        color = theme.colorScheme.destructive;
        break;
    }

    return Icon(icon, size: 16, color: color);
  }

  String _getHealthStatusText(PerformanceHealthStatus status) {
    switch (status) {
      case PerformanceHealthStatus.healthy:
        return 'Healthy';
      case PerformanceHealthStatus.degraded:
        return 'Degraded';
      case PerformanceHealthStatus.warning:
        return 'Warning';
      case PerformanceHealthStatus.critical:
        return 'Critical';
    }
  }

  Color _getHealthStatusColor(
    ShadThemeData theme,
    PerformanceHealthStatus status,
  ) {
    switch (status) {
      case PerformanceHealthStatus.healthy:
        return Colors.green;
      case PerformanceHealthStatus.degraded:
        return Colors.yellow;
      case PerformanceHealthStatus.warning:
        return Colors.orange;
      case PerformanceHealthStatus.critical:
        return theme.colorScheme.destructive;
    }
  }

  Color _getSuccessRateColor(ShadThemeData theme, double successRate) {
    if (successRate >= 0.95) return Colors.green;
    if (successRate >= 0.90) return Colors.orange;
    return theme.colorScheme.destructive;
  }

  double _getOverallSuccessRate(PerformanceSnapshot snapshot) {
    if (snapshot.operationStats.isEmpty) return 1.0;

    final totalOps = snapshot.operationStats.values
        .map((s) => s.totalOperations)
        .fold(0, (a, b) => a + b);

    final successfulOps = snapshot.operationStats.values
        .map((s) => s.successfulOperations)
        .fold(0, (a, b) => a + b);

    return totalOps > 0 ? successfulOps / totalOps : 1.0;
  }

  double _getOverallAverageResponse(PerformanceSnapshot snapshot) {
    if (snapshot.operationStats.isEmpty) return 0.0;

    final avgTimes =
        snapshot.operationStats.values.map((s) => s.averageDurationMs).toList();

    return avgTimes.reduce((a, b) => a + b) / avgTimes.length;
  }

  double _getResponseTimeHealth(PerformanceSnapshot snapshot) {
    final avgResponse = _getOverallAverageResponse(snapshot);
    if (avgResponse <= 2000) return 1.0; // Excellent
    if (avgResponse <= 5000) return 0.8; // Good
    if (avgResponse <= 10000) return 0.6; // Fair
    return 0.3; // Poor
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final diff = now.difference(time);

    if (diff.inMinutes < 1) return 'Just now';
    if (diff.inMinutes < 60) return '${diff.inMinutes}m ago';
    if (diff.inHours < 24) return '${diff.inHours}h ago';
    return '${diff.inDays}d ago';
  }

  Future<void> _loadPerformanceData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final snapshot = await _performanceService.getPerformanceSnapshot();

      if (mounted) {
        setState(() {
          _currentSnapshot = snapshot;
          _isLoading = false;
        });
      }
    } catch (e) {
      LoggerService.error('Failed to load performance data', e);

      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _startAutoRefresh() {
    Timer.periodic(widget.refreshInterval, (_) {
      if (mounted && !_isLoading) {
        _loadPerformanceData();
      }
    });
  }
}
