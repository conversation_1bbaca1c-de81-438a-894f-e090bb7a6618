import 'dart:convert';
import 'dart:io' as io;
import 'package:flutter/services.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:http/http.dart' as http;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/security_event.dart';
import 'package:three_pay_group_litigation_platform/src/core/utils/security_utils.dart';

/// Enhanced Google Drive authentication service with security features
class GoogleDriveAuthService {
  static final GoogleDriveAuthService _instance =
      GoogleDriveAuthService._internal();
  factory GoogleDriveAuthService() => _instance;
  GoogleDriveAuthService._internal();

  final PocketBaseService _pocketBaseService = PocketBaseService();

  // Authentication state
  AuthClient? _authenticatedClient;
  ServiceAccountCredentials? _credentials;
  DateTime? _tokenExpiry;
  String? _currentSessionId;
  DateTime? _sessionStartTime;

  // Security configuration
  static const List<String> _requiredScopes = [
    'https://www.googleapis.com/auth/drive',
    'https://www.googleapis.com/auth/drive.file',
  ];

  static const Duration _tokenRefreshThreshold = Duration(minutes: 5);
  static const Duration _maxSessionDuration = Duration(hours: 24);

  /// Initialize the authentication service
  Future<void> initialize() async {
    try {
      LoggerService.info(
        'Initializing enhanced Google Drive authentication...',
      );

      _currentSessionId = SecurityUtils.generateSessionToken();
      _sessionStartTime = DateTime.now();

      // Load and validate credentials
      await _loadAndValidateCredentials();

      // Create authenticated client
      await _createAuthenticatedClient();

      // Log successful initialization
      await _logSecurityEvent(
        SecurityEvent.authentication(
          id: SecurityUtils.generateSecureId(),
          success: true,
          userId: _getCurrentUserId() ?? 'system',
          details: {
            'action': 'service_initialization',
            'session_id': _currentSessionId,
          },
        ),
      );

      LoggerService.info(
        'Enhanced Google Drive authentication initialized successfully',
      );
    } catch (e) {
      LoggerService.error(
        'Failed to initialize Google Drive authentication',
        e,
      );

      // Log failed initialization
      await _logSecurityEvent(
        SecurityEvent.authentication(
          id: SecurityUtils.generateSecureId(),
          success: false,
          userId: _getCurrentUserId() ?? 'system',
          errorMessage: e.toString(),
          details: {
            'action': 'service_initialization',
            'session_id': _currentSessionId,
          },
        ),
      );

      rethrow;
    }
  }

  /// Get authenticated HTTP client with security validation
  Future<http.Client> getAuthenticatedClient() async {
    try {
      // Validate session
      if (!_isSessionValid()) {
        LoggerService.warning(
          'Session expired, re-initializing authentication',
        );
        await initialize();
      }

      // Check if token needs refresh
      if (_needsTokenRefresh()) {
        LoggerService.info('Token refresh required');
        await _refreshToken();
      }

      if (_authenticatedClient == null) {
        throw GoogleDriveAuthException('No authenticated client available');
      }

      // Log successful client access
      await _logSecurityEvent(
        SecurityEvent.authentication(
          id: SecurityUtils.generateSecureId(),
          success: true,
          userId: _getCurrentUserId() ?? 'system',
          details: {
            'action': 'client_access',
            'session_id': _currentSessionId,
            'token_expiry': _tokenExpiry?.toIso8601String(),
          },
        ),
      );

      return _authenticatedClient!;
    } catch (e) {
      LoggerService.error('Failed to get authenticated client', e);

      // Log failed client access
      await _logSecurityEvent(
        SecurityEvent.authentication(
          id: SecurityUtils.generateSecureId(),
          success: false,
          userId: _getCurrentUserId() ?? 'system',
          errorMessage: e.toString(),
          details: {'action': 'client_access', 'session_id': _currentSessionId},
        ),
      );

      rethrow;
    }
  }

  /// Load and validate service account credentials
  Future<void> _loadAndValidateCredentials() async {
    try {
      LoggerService.debug('Loading service account credentials...');

      // Try loading from the specified file path
      final credentialsFile = io.File(
        'lib/pay-global-document-storage-9179f2284308.json',
      );

      if (await credentialsFile.exists()) {
        LoggerService.debug('Loading credentials from file');
        final credentialsContent = await credentialsFile.readAsString();
        final credentialsMap =
            jsonDecode(credentialsContent) as Map<String, dynamic>;

        // Validate credentials structure
        _validateCredentialsStructure(credentialsMap);

        _credentials = ServiceAccountCredentials.fromJson(credentialsMap);
        LoggerService.info(
          'Service account credentials loaded and validated successfully',
        );
        return;
      }

      // Fallback to assets
      try {
        LoggerService.debug('Loading credentials from assets...');
        final credentialsContent = await rootBundle.loadString(
          'assets/credentials/service-account.json',
        );
        final credentialsMap =
            jsonDecode(credentialsContent) as Map<String, dynamic>;

        // Validate credentials structure
        _validateCredentialsStructure(credentialsMap);

        _credentials = ServiceAccountCredentials.fromJson(credentialsMap);
        LoggerService.info('Service account credentials loaded from assets');
        return;
      } catch (e) {
        LoggerService.debug('Failed to load credentials from assets: $e');
      }

      throw GoogleDriveAuthException(
        'No valid service account credentials found',
      );
    } catch (e) {
      LoggerService.error('Failed to load credentials', e);
      rethrow;
    }
  }

  /// Validate credentials structure
  void _validateCredentialsStructure(Map<String, dynamic> credentials) {
    final requiredFields = [
      'type',
      'project_id',
      'private_key_id',
      'private_key',
      'client_email',
      'client_id',
      'auth_uri',
      'token_uri',
    ];

    for (final field in requiredFields) {
      if (!credentials.containsKey(field) || credentials[field] == null) {
        throw GoogleDriveAuthException(
          'Missing required credential field: $field',
        );
      }
    }

    // Validate credential type
    if (credentials['type'] != 'service_account') {
      throw GoogleDriveAuthException(
        'Invalid credential type: ${credentials['type']}',
      );
    }

    // Validate email format
    final clientEmail = credentials['client_email'] as String;
    if (!SecurityUtils.isValidEmail(clientEmail)) {
      throw GoogleDriveAuthException('Invalid client email format');
    }

    LoggerService.debug('Credentials structure validation passed');
  }

  /// Create authenticated HTTP client
  Future<void> _createAuthenticatedClient() async {
    try {
      if (_credentials == null) {
        throw GoogleDriveAuthException('No credentials available');
      }

      LoggerService.debug('Creating authenticated HTTP client...');

      // Create authenticated client with required scopes
      _authenticatedClient = await clientViaServiceAccount(
        _credentials!,
        _requiredScopes,
      );

      // Store token expiry information
      final accessCredentials = _authenticatedClient!.credentials;
      _tokenExpiry = accessCredentials.accessToken.expiry;

      LoggerService.debug('Token expires at: $_tokenExpiry');
      LoggerService.info('Authenticated HTTP client created successfully');
    } catch (e) {
      LoggerService.error('Failed to create authenticated client', e);
      rethrow;
    }
  }

  /// Check if token needs refresh
  bool _needsTokenRefresh() {
    if (_tokenExpiry == null) return true;

    final now = DateTime.now();
    final timeUntilExpiry = _tokenExpiry!.difference(now);

    return timeUntilExpiry <= _tokenRefreshThreshold;
  }

  /// Check if session is valid
  bool _isSessionValid() {
    if (_currentSessionId == null || _authenticatedClient == null) return false;

    // Check session duration
    if (_sessionStartTime != null) {
      final sessionDuration = DateTime.now().difference(_sessionStartTime!);
      if (sessionDuration > _maxSessionDuration) {
        LoggerService.warning(
          'Session exceeded maximum duration: ${sessionDuration.inHours}h',
        );
        return false;
      }
    }

    // Check token expiry
    if (_tokenExpiry != null && DateTime.now().isAfter(_tokenExpiry!)) {
      return false;
    }

    return true;
  }

  /// Refresh authentication token
  Future<void> _refreshToken() async {
    try {
      LoggerService.info('Refreshing authentication token...');

      // Close existing client
      _authenticatedClient?.close();

      // Create new authenticated client
      await _createAuthenticatedClient();

      // Log successful token refresh
      await _logSecurityEvent(
        SecurityEvent.authentication(
          id: SecurityUtils.generateSecureId(),
          success: true,
          userId: _getCurrentUserId() ?? 'system',
          details: {
            'action': 'token_refresh',
            'session_id': _currentSessionId,
            'new_expiry': _tokenExpiry?.toIso8601String(),
          },
        ),
      );

      LoggerService.info('Authentication token refreshed successfully');
    } catch (e) {
      LoggerService.error('Failed to refresh token', e);

      // Log failed token refresh
      await _logSecurityEvent(
        SecurityEvent.authentication(
          id: SecurityUtils.generateSecureId(),
          success: false,
          userId: _getCurrentUserId() ?? 'system',
          errorMessage: e.toString(),
          details: {'action': 'token_refresh', 'session_id': _currentSessionId},
        ),
      );

      rethrow;
    }
  }

  /// Log security events
  Future<void> _logSecurityEvent(SecurityEvent event) async {
    try {
      await _pocketBaseService.createRecord(
        collectionName: 'security_events',
        data: event.toJson(),
      );
    } catch (e) {
      LoggerService.warning('Failed to log security event: $e');
      // Don't fail the operation if logging fails
    }
  }

  /// Get current user ID
  String? _getCurrentUserId() {
    return _pocketBaseService.currentUser?.id;
  }

  /// Dispose resources
  void dispose() {
    try {
      _authenticatedClient?.close();
      _authenticatedClient = null;
      _credentials = null;
      _tokenExpiry = null;
      _currentSessionId = null;
      _sessionStartTime = null;

      LoggerService.info('Google Drive authentication service disposed');
    } catch (e) {
      LoggerService.warning(
        'Error disposing Google Drive authentication service: $e',
      );
    }
  }

  /// Get authentication status
  Map<String, dynamic> getAuthStatus() {
    final sessionDuration =
        _sessionStartTime != null
            ? DateTime.now().difference(_sessionStartTime!)
            : null;

    return {
      'is_authenticated': _authenticatedClient != null,
      'has_credentials': _credentials != null,
      'token_expiry': _tokenExpiry?.toIso8601String(),
      'session_id': _currentSessionId,
      'session_start_time': _sessionStartTime?.toIso8601String(),
      'session_duration_hours': sessionDuration?.inHours,
      'session_valid': _isSessionValid(),
      'needs_refresh': _needsTokenRefresh(),
    };
  }
}

/// Custom exception for Google Drive authentication errors
class GoogleDriveAuthException implements Exception {
  final String message;

  GoogleDriveAuthException(this.message);

  @override
  String toString() => 'GoogleDriveAuthException: $message';
}
