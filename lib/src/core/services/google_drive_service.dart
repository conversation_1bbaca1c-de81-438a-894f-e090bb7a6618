import 'dart:async';
import 'dart:io' as io;
import 'dart:typed_data';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/google_drive_auth.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/google_drive_config.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/google_drive_file.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/google_drive_permission.dart';
import 'package:three_pay_group_litigation_platform/src/core/utils/google_drive_utils.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/performance_monitoring_service.dart';

/// Main Google Drive service for handling all Google Drive operations
class GoogleDriveService {
  static final GoogleDriveService _instance = GoogleDriveService._internal();
  factory GoogleDriveService() => _instance;
  GoogleDriveService._internal();

  final GoogleDriveAuthService _authService = GoogleDriveAuthService();
  final GoogleDriveConfigService _configService = GoogleDriveConfigService();
  final PocketBaseService _pocketBaseService = PocketBaseService();
  final PerformanceMonitoringService _performanceService =
      PerformanceMonitoringService();

  drive.DriveApi? _driveApi;
  bool _isInitialized = false;
  final Map<String, GoogleDriveFile> _fileCache = {};
  final Map<String, List<GoogleDriveFile>> _folderCache = {};
  Timer? _cacheCleanupTimer;

  /// Initialize the Google Drive service
  Future<void> initialize() async {
    if (_isInitialized) {
      LoggerService.debug('Google Drive service already initialized');
      return;
    }

    try {
      LoggerService.info('Initializing Google Drive service...');

      // Initialize authentication
      await _authService.initialize();

      // Validate authentication
      final isValid = await _authService.validateAuthentication();
      if (!isValid) {
        throw GoogleDriveServiceException('Authentication validation failed');
      }

      // Create Drive API instance
      final client = await _authService.getAuthenticatedClient();
      _driveApi = drive.DriveApi(client);

      // Start cache cleanup timer
      _startCacheCleanup();

      _isInitialized = true;
      LoggerService.info('Google Drive service initialized successfully');
    } catch (e) {
      LoggerService.error('Failed to initialize Google Drive service', e);
      rethrow;
    }
  }

  /// Ensure service is initialized
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Get Drive API instance
  Future<drive.DriveApi> _getDriveApi() async {
    await _ensureInitialized();

    if (_driveApi == null) {
      throw GoogleDriveServiceException('Drive API not available');
    }

    return _driveApi!;
  }

  // ============================================================================
  // FILE OPERATIONS
  // ============================================================================

  /// Upload a file to Google Drive
  Future<GoogleDriveFile> uploadFile({
    required io.File file,
    required String fileName,
    String? folderId,
    Map<String, String>? metadata,
    Function(GoogleDriveUploadProgress)? onProgress,
  }) async {
    return await _performanceService.trackFileOperation(
      'upload',
      () => _uploadFileInternal(
        file: file,
        fileName: fileName,
        folderId: folderId,
        metadata: metadata,
        onProgress: onProgress,
      ),
      fileSizeBytes: await file.length(),
    );
  }

  /// Internal upload implementation with performance optimizations
  Future<GoogleDriveFile> _uploadFileInternal({
    required io.File file,
    required String fileName,
    String? folderId,
    Map<String, String>? metadata,
    Function(GoogleDriveUploadProgress)? onProgress,
  }) async {
    try {
      LoggerService.info('Uploading file: $fileName');

      final driveApi = await _getDriveApi();

      // Validate file
      await _validateFile(file, fileName);

      // Prepare file metadata
      final fileMetadata =
          drive.File()
            ..name = GoogleDriveUtils.sanitizeFileName(fileName)
            ..parents = folderId != null ? [folderId] : null;

      // Add custom metadata if provided
      if (metadata != null) {
        fileMetadata.properties = metadata;
      }

      // Detect MIME type
      final mimeType =
          GoogleDriveUtils.detectMimeType(file.path) ??
          'application/octet-stream';

      final fileSize = await file.length();

      // Choose upload method based on file size
      drive.File uploadedFile;
      if (GoogleDriveUtils.shouldUseChunkedUpload(fileSize)) {
        uploadedFile = await _uploadFileChunked(
          driveApi,
          file,
          fileMetadata,
          mimeType,
          onProgress,
        );
      } else {
        uploadedFile = await _uploadFileSimple(
          driveApi,
          file,
          fileMetadata,
          mimeType,
          onProgress,
        );
      }

      // Convert to our model
      final googleDriveFile = GoogleDriveFile.fromGoogleDriveApi(
        _convertFileToJson(uploadedFile),
      );

      // Cache the file
      _fileCache[googleDriveFile.id] = googleDriveFile;

      // Update quota usage
      await _updateQuotaUsage(1);

      // Apply public permissions to the uploaded file
      await _applyPublicPermissions(uploadedFile.id!);

      // Log access
      await _logFileAccess(
        fileId: googleDriveFile.id,
        action: 'upload',
        success: true,
        fileSize: fileSize,
      );

      LoggerService.info('File uploaded successfully: ${googleDriveFile.id}');
      return googleDriveFile;
    } catch (e) {
      LoggerService.error('Failed to upload file: $fileName', e);

      // Log failed access
      await _logFileAccess(
        fileName: fileName,
        action: 'upload',
        success: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// Download a file from Google Drive
  Future<Uint8List> downloadFile(String fileId) async {
    // Get file metadata first to determine size for performance tracking
    final fileMetadata = await getFileMetadata(fileId);
    final fileSizeBytes = fileMetadata.size ?? 0;

    return await _performanceService.trackFileOperation(
      'download',
      () => _downloadFileInternal(fileId),
      fileSizeBytes: fileSizeBytes,
    );
  }

  /// Internal download implementation with performance optimizations
  Future<Uint8List> _downloadFileInternal(String fileId) async {
    try {
      LoggerService.info('Downloading file: $fileId');

      final driveApi = await _getDriveApi();

      // Download file content
      final media =
          await driveApi.files.get(
                fileId,
                downloadOptions: drive.DownloadOptions.fullMedia,
              )
              as drive.Media;

      final bytes = <int>[];
      await for (final chunk in media.stream) {
        bytes.addAll(chunk);
      }

      final fileBytes = Uint8List.fromList(bytes);

      // Update quota usage
      await _updateQuotaUsage(1);

      // Log access
      await _logFileAccess(
        fileId: fileId,
        action: 'download',
        success: true,
        fileSize: fileBytes.length,
      );

      LoggerService.info(
        'File downloaded successfully: $fileId (${GoogleDriveUtils.formatFileSize(fileBytes.length)})',
      );
      return fileBytes;
    } catch (e) {
      LoggerService.error('Failed to download file: $fileId', e);

      // Log failed access
      await _logFileAccess(
        fileId: fileId,
        action: 'download',
        success: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// Get file metadata
  Future<GoogleDriveFile> getFileMetadata(String fileId) async {
    return await _performanceService.trackOperation(
      'api_call',
      () => _getFileMetadataInternal(fileId),
      metadata: {'operation': 'getFileMetadata', 'fileId': fileId},
    );
  }

  /// Internal metadata retrieval with caching
  Future<GoogleDriveFile> _getFileMetadataInternal(String fileId) async {
    try {
      // Check cache first
      if (_fileCache.containsKey(fileId)) {
        LoggerService.debug('Returning cached file metadata: $fileId');
        return _fileCache[fileId]!;
      }

      LoggerService.debug('Fetching file metadata: $fileId');

      final driveApi = await _getDriveApi();

      final file =
          await driveApi.files.get(
                fileId,
                $fields:
                    'id,name,mimeType,size,createdTime,modifiedTime,webViewLink,webContentLink,parents,properties,md5Checksum,sha1Checksum,trashed,description,capabilities',
              )
              as drive.File;

      final googleDriveFile = GoogleDriveFile.fromGoogleDriveApi(
        _convertFileToJson(file),
      );

      // Cache the file metadata
      _fileCache[fileId] = googleDriveFile;

      // Update quota usage
      await _updateQuotaUsage(1);

      return googleDriveFile;
    } catch (e) {
      LoggerService.error('Failed to get file metadata: $fileId', e);
      rethrow;
    }
  }

  /// Delete a file from Google Drive
  Future<void> deleteFile(String fileId) async {
    try {
      LoggerService.info('Deleting file: $fileId');

      final driveApi = await _getDriveApi();

      // Get file metadata before deletion for logging
      GoogleDriveFile? fileMetadata;
      try {
        fileMetadata = await getFileMetadata(fileId);
      } catch (e) {
        LoggerService.warning(
          'Could not get file metadata before deletion: $e',
        );
      }

      // Delete the file
      await driveApi.files.delete(fileId);

      // Remove from cache
      _fileCache.remove(fileId);

      // Update quota usage
      await _updateQuotaUsage(1);

      // Log access
      await _logFileAccess(
        fileId: fileId,
        action: 'delete',
        success: true,
        fileName: fileMetadata?.name,
      );

      LoggerService.info('File deleted successfully: $fileId');
    } catch (e) {
      LoggerService.error('Failed to delete file: $fileId', e);

      // Log failed access
      await _logFileAccess(
        fileId: fileId,
        action: 'delete',
        success: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// Check if file exists
  Future<bool> fileExists(String fileId) async {
    try {
      await getFileMetadata(fileId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get file download URL
  Future<String> getFileDownloadUrl(String fileId) async {
    try {
      final fileMetadata = await getFileMetadata(fileId);

      LoggerService.debug('File metadata for $fileId:');
      LoggerService.debug('  webContentLink: ${fileMetadata.webContentLink}');
      LoggerService.debug('  webViewLink: ${fileMetadata.webViewLink}');

      // For public files, try to use a direct download URL that works with PDF viewers
      // First check if the file has public permissions
      if (fileMetadata.webContentLink != null) {
        LoggerService.debug('Using webContentLink for download');
        return fileMetadata.webContentLink!;
      }

      // For public files, we can use a direct download URL format
      // This format works better with PDF viewers: https://drive.google.com/uc?export=download&id=FILE_ID
      final publicDownloadUrl =
          'https://drive.google.com/uc?export=download&id=$fileId';
      LoggerService.debug('Using public download URL: $publicDownloadUrl');

      return publicDownloadUrl;
    } catch (e) {
      LoggerService.error('Failed to get download URL for file: $fileId', e);
      rethrow;
    }
  }

  /// Get file view URL
  Future<String> getFileViewUrl(String fileId) async {
    try {
      final fileMetadata = await getFileMetadata(fileId);

      if (fileMetadata.webViewLink != null) {
        return fileMetadata.webViewLink!;
      }

      // Generate view URL
      return GoogleDriveUtils.generateFileUrl(fileId);
    } catch (e) {
      LoggerService.error('Failed to get view URL for file: $fileId', e);
      rethrow;
    }
  }

  // ============================================================================
  // FOLDER OPERATIONS
  // ============================================================================

  /// Create a folder in Google Drive
  Future<GoogleDriveFile> createFolder({
    required String folderName,
    String? parentFolderId,
    Map<String, String>? metadata,
  }) async {
    try {
      LoggerService.info('Creating folder: $folderName');

      final driveApi = await _getDriveApi();

      // Validate folder name
      if (!GoogleDriveUtils.isValidFolderName(folderName)) {
        throw GoogleDriveServiceException('Invalid folder name: $folderName');
      }

      // Prepare folder metadata
      final folderMetadata =
          drive.File()
            ..name = GoogleDriveUtils.sanitizeFileName(folderName)
            ..mimeType = GoogleDriveUtils.folderMimeType
            ..parents = parentFolderId != null ? [parentFolderId] : null;

      // Add custom metadata if provided
      if (metadata != null) {
        folderMetadata.properties = metadata;
      }

      // Create the folder
      final createdFolder = await driveApi.files.create(folderMetadata);

      // Convert to our model
      final googleDriveFolder = GoogleDriveFile.fromGoogleDriveApi(
        _convertFileToJson(createdFolder),
      );

      // Apply public permissions to the folder
      await _applyPublicPermissions(createdFolder.id!);

      // Cache the folder
      _fileCache[googleDriveFolder.id] = googleDriveFolder;

      // Update quota usage
      await _updateQuotaUsage(1);

      LoggerService.info(
        'Folder created successfully: ${googleDriveFolder.id}',
      );
      return googleDriveFolder;
    } catch (e) {
      LoggerService.error('Failed to create folder: $folderName', e);
      rethrow;
    }
  }

  /// List files in a folder
  Future<List<GoogleDriveFile>> listFilesInFolder({
    String? folderId,
    String? query,
    int? maxResults,
    bool includeSubfolders = false,
  }) async {
    try {
      LoggerService.debug('Listing files in folder: ${folderId ?? "root"}');

      // Check cache first
      final cacheKey = '${folderId ?? "root"}_$query';
      if (_folderCache.containsKey(cacheKey)) {
        LoggerService.debug('Returning cached folder contents');
        return _folderCache[cacheKey]!;
      }

      final driveApi = await _getDriveApi();

      // Build query
      String searchQuery = '';
      if (folderId != null) {
        searchQuery = "'$folderId' in parents";
      }
      if (query != null && query.isNotEmpty) {
        if (searchQuery.isNotEmpty) searchQuery += ' and ';
        searchQuery += query;
      }
      if (!includeSubfolders) {
        if (searchQuery.isNotEmpty) searchQuery += ' and ';
        searchQuery += "trashed = false";
      }

      // List files
      final fileList = await driveApi.files.list(
        q: searchQuery.isNotEmpty ? searchQuery : null,
        pageSize: maxResults ?? 100,
        $fields:
            'files(id,name,mimeType,size,createdTime,modifiedTime,webViewLink,webContentLink,parents,properties,md5Checksum,sha1Checksum,trashed,description,capabilities)',
      );

      // Convert to our models
      final files =
          fileList.files
              ?.map(
                (file) => GoogleDriveFile.fromGoogleDriveApi(
                  _convertFileToJson(file),
                ),
              )
              .toList() ??
          [];

      // Cache the results
      _folderCache[cacheKey] = files;

      // Update quota usage
      await _updateQuotaUsage(1);

      LoggerService.debug('Found ${files.length} files in folder');
      return files;
    } catch (e) {
      LoggerService.error(
        'Failed to list files in folder: ${folderId ?? "root"}',
        e,
      );
      rethrow;
    }
  }

  /// Create folder structure for a claim
  Future<Map<String, String>> createClaimFolderStructure({
    required String claimId,
    required String claimTitle,
    List<String>? documentCategories,
  }) async {
    try {
      LoggerService.info('Creating folder structure for claim: $claimId');

      final config = await _configService.loadConfig();
      final rootFolderId = config.rootFolderId;

      // Create main claim folder
      final claimFolderName = GoogleDriveUtils.generateClaimFolderName(
        claimId,
        claimTitle,
      );
      final claimFolder = await createFolder(
        folderName: claimFolderName,
        parentFolderId: rootFolderId,
        metadata: {
          'claim_id': claimId,
          'claim_title': claimTitle,
          'folder_type': 'claim_root',
        },
      );

      final folderStructure = <String, String>{'root': claimFolder.id};

      // Create document category folders
      if (documentCategories != null) {
        for (final category in documentCategories) {
          final categoryFolderName =
              GoogleDriveUtils.generateCategoryFolderName(category);
          final categoryFolder = await createFolder(
            folderName: categoryFolderName,
            parentFolderId: claimFolder.id,
            metadata: {
              'claim_id': claimId,
              'document_category': category,
              'folder_type': 'document_category',
            },
          );
          folderStructure[category] = categoryFolder.id;
        }
      }

      LoggerService.info(
        'Folder structure created successfully for claim: $claimId',
      );
      return folderStructure;
    } catch (e) {
      LoggerService.error(
        'Failed to create folder structure for claim: $claimId',
        e,
      );
      rethrow;
    }
  }

  /// Delete a folder and its contents
  Future<void> deleteFolder(String folderId, {bool recursive = false}) async {
    try {
      LoggerService.info('Deleting folder: $folderId');

      if (recursive) {
        // List and delete all contents first
        final contents = await listFilesInFolder(folderId: folderId);
        for (final item in contents) {
          if (item.isFolder) {
            await deleteFolder(item.id, recursive: true);
          } else {
            await deleteFile(item.id);
          }
        }
      }

      // Delete the folder itself
      await deleteFile(folderId);

      // Clear folder from cache
      _folderCache.removeWhere((key, value) => key.startsWith(folderId));

      LoggerService.info('Folder deleted successfully: $folderId');
    } catch (e) {
      LoggerService.error('Failed to delete folder: $folderId', e);
      rethrow;
    }
  }

  // ============================================================================
  // PERMISSION MANAGEMENT
  // ============================================================================

  /// Set file permissions
  Future<void> setFilePermissions(
    String fileId,
    List<Map<String, dynamic>> permissions,
  ) async {
    try {
      LoggerService.info('Setting permissions for file: $fileId');

      final driveApi = await _getDriveApi();

      for (final permissionData in permissions) {
        final permission =
            drive.Permission()
              ..type = permissionData['type']
              ..role = permissionData['role']
              ..emailAddress = permissionData['emailAddress']
              ..domain = permissionData['domain']
              ..allowFileDiscovery = permissionData['allowFileDiscovery']
              ..expirationTime = permissionData['expirationTime'];

        await driveApi.permissions.create(permission, fileId);
      }

      // Update quota usage
      await _updateQuotaUsage(permissions.length);

      LoggerService.info('Permissions set successfully for file: $fileId');
    } catch (e) {
      LoggerService.error('Failed to set permissions for file: $fileId', e);
      rethrow;
    }
  }

  /// Apply public permissions to a file (Anyone with the link can view)
  Future<void> _applyPublicPermissions(String fileId) async {
    try {
      LoggerService.debug('Applying public permissions to file: $fileId');

      final driveApi = await _getDriveApi();

      // Check if public permission already exists
      final existingPermissions = await driveApi.permissions.list(fileId);

      bool hasPublicPermission = false;
      if (existingPermissions.permissions != null) {
        for (final permission in existingPermissions.permissions!) {
          if (permission.type == 'anyone' && permission.role == 'reader') {
            hasPublicPermission = true;
            LoggerService.debug('File already has public permission: $fileId');
            break;
          }
        }
      }

      if (!hasPublicPermission) {
        // Create public permission: "Anyone with the link" can view
        final publicPermission =
            drive.Permission()
              ..type = 'anyone'
              ..role = 'reader'; // Read-only access for security

        await driveApi.permissions.create(
          publicPermission,
          fileId,
          sendNotificationEmail: false,
        );

        LoggerService.info('Applied public permission to file: $fileId');
      }
    } catch (e) {
      LoggerService.warning(
        'Failed to apply public permissions to file $fileId: $e',
      );
      // Don't rethrow - this shouldn't break the upload process
    }
  }

  /// Get file permissions
  Future<List<GoogleDrivePermission>> getFilePermissions(String fileId) async {
    try {
      LoggerService.debug('Getting permissions for file: $fileId');

      final driveApi = await _getDriveApi();

      final permissionList = await driveApi.permissions.list(fileId);

      final permissions =
          permissionList.permissions
              ?.map(
                (permission) => GoogleDrivePermission.fromGoogleDriveApi(
                  _convertPermissionToJson(permission),
                ),
              )
              .toList() ??
          [];

      // Update quota usage
      await _updateQuotaUsage(1);

      return permissions;
    } catch (e) {
      LoggerService.error('Failed to get permissions for file: $fileId', e);
      rethrow;
    }
  }

  /// Share file with user
  Future<void> shareFileWithUser({
    required String fileId,
    required String emailAddress,
    required GoogleDrivePermissionRole role,
    DateTime? expirationTime,
  }) async {
    try {
      LoggerService.info(
        'Sharing file $fileId with $emailAddress as ${role.value}',
      );

      final permissionData =
          GoogleDrivePermissionBuilder()
              .type(GoogleDrivePermissionType.user)
              .role(role)
              .emailAddress(emailAddress)
              .expirationTime(expirationTime)
              .build();

      await setFilePermissions(fileId, [permissionData]);

      LoggerService.info('File shared successfully');
    } catch (e) {
      LoggerService.error('Failed to share file', e);
      rethrow;
    }
  }

  /// Create public sharing link
  Future<String> createPublicSharingLink(String fileId) async {
    try {
      LoggerService.info('Creating public sharing link for file: $fileId');

      final permissionData = GoogleDrivePermissionTemplates.publicReader();
      await setFilePermissions(fileId, [permissionData]);

      final viewUrl = await getFileViewUrl(fileId);

      LoggerService.info('Public sharing link created successfully');
      return viewUrl;
    } catch (e) {
      LoggerService.error(
        'Failed to create public sharing link for file: $fileId',
        e,
      );
      rethrow;
    }
  }

  /// Revoke file permission
  Future<void> revokeFilePermission(String fileId, String permissionId) async {
    try {
      LoggerService.info('Revoking permission $permissionId for file: $fileId');

      final driveApi = await _getDriveApi();
      await driveApi.permissions.delete(fileId, permissionId);

      // Update quota usage
      await _updateQuotaUsage(1);

      LoggerService.info('Permission revoked successfully');
    } catch (e) {
      LoggerService.error('Failed to revoke permission', e);
      rethrow;
    }
  }

  // ============================================================================
  // BATCH OPERATIONS
  // ============================================================================

  /// Upload multiple files
  Future<GoogleDriveBatchResult<GoogleDriveFile>> uploadFiles({
    required List<io.File> files,
    required List<String> fileNames,
    String? folderId,
    Function(GoogleDriveUploadProgress)? onProgress,
  }) async {
    try {
      LoggerService.info('Starting batch upload of ${files.length} files');

      if (files.length != fileNames.length) {
        throw GoogleDriveServiceException(
          'Files and file names lists must have the same length',
        );
      }

      final successful = <GoogleDriveFile>[];
      final failed = <GoogleDriveError>[];

      for (int i = 0; i < files.length; i++) {
        try {
          final uploadedFile = await uploadFile(
            file: files[i],
            fileName: fileNames[i],
            folderId: folderId,
            onProgress: onProgress,
          );
          successful.add(uploadedFile);
        } catch (e) {
          failed.add(
            GoogleDriveError.fromException(
              'upload',
              e is Exception ? e : Exception(e.toString()),
              fileName: fileNames[i],
            ),
          );
        }
      }

      final result = GoogleDriveBatchResult<GoogleDriveFile>(
        successful: successful,
        failed: failed,
        totalCount: files.length,
      );

      LoggerService.info(
        'Batch upload completed: ${successful.length} successful, ${failed.length} failed',
      );
      return result;
    } catch (e) {
      LoggerService.error('Batch upload failed', e);
      rethrow;
    }
  }

  /// Delete multiple files
  Future<GoogleDriveBatchResult<String>> deleteFiles(
    List<String> fileIds,
  ) async {
    try {
      LoggerService.info('Starting batch deletion of ${fileIds.length} files');

      final successful = <String>[];
      final failed = <GoogleDriveError>[];

      for (final fileId in fileIds) {
        try {
          await deleteFile(fileId);
          successful.add(fileId);
        } catch (e) {
          failed.add(
            GoogleDriveError.fromException(
              'delete',
              e is Exception ? e : Exception(e.toString()),
              fileId: fileId,
            ),
          );
        }
      }

      final result = GoogleDriveBatchResult<String>(
        successful: successful,
        failed: failed,
        totalCount: fileIds.length,
      );

      LoggerService.info(
        'Batch deletion completed: ${successful.length} successful, ${failed.length} failed',
      );
      return result;
    } catch (e) {
      LoggerService.error('Batch deletion failed', e);
      rethrow;
    }
  }

  // ============================================================================
  // UTILITY AND HELPER METHODS
  // ============================================================================

  /// Convert Google Drive API File object to JSON safely
  Map<String, dynamic> _convertFileToJson(drive.File file) {
    final json = <String, dynamic>{};

    // Basic fields
    if (file.id != null) json['id'] = file.id;
    if (file.name != null) json['name'] = file.name;
    if (file.mimeType != null) json['mimeType'] = file.mimeType;
    if (file.size != null) json['size'] = file.size;
    if (file.createdTime != null) {
      json['createdTime'] = file.createdTime!.toIso8601String();
    }
    if (file.modifiedTime != null) {
      json['modifiedTime'] = file.modifiedTime!.toIso8601String();
    }
    if (file.webViewLink != null) json['webViewLink'] = file.webViewLink;
    if (file.webContentLink != null) {
      json['webContentLink'] = file.webContentLink;
    }
    if (file.parents != null) json['parents'] = file.parents;
    if (file.properties != null) json['properties'] = file.properties;
    if (file.md5Checksum != null) json['md5Checksum'] = file.md5Checksum;
    if (file.sha1Checksum != null) json['sha1Checksum'] = file.sha1Checksum;
    if (file.trashed != null) json['trashed'] = file.trashed;
    if (file.description != null) json['description'] = file.description;

    // Handle capabilities field safely
    if (file.capabilities != null) {
      final capabilities = <String, dynamic>{};
      final caps = file.capabilities!;

      // Convert FileCapabilities to Map<String, dynamic>
      if (caps.canDownload != null) {
        capabilities['canDownload'] = caps.canDownload;
      }
      if (caps.canEdit != null) capabilities['canEdit'] = caps.canEdit;
      if (caps.canShare != null) capabilities['canShare'] = caps.canShare;
      if (caps.canDelete != null) capabilities['canDelete'] = caps.canDelete;
      if (caps.canCopy != null) capabilities['canCopy'] = caps.canCopy;
      if (caps.canComment != null) capabilities['canComment'] = caps.canComment;
      if (caps.canRename != null) capabilities['canRename'] = caps.canRename;
      if (caps.canAddChildren != null) {
        capabilities['canAddChildren'] = caps.canAddChildren;
      }
      if (caps.canRemoveChildren != null) {
        capabilities['canRemoveChildren'] = caps.canRemoveChildren;
      }
      if (caps.canListChildren != null) {
        capabilities['canListChildren'] = caps.canListChildren;
      }
      // Note: TeamDrive capabilities are deprecated and removed
      if (caps.canReadRevisions != null) {
        capabilities['canReadRevisions'] = caps.canReadRevisions;
      }
      if (caps.canModifyContent != null) {
        capabilities['canModifyContent'] = caps.canModifyContent;
      }
      if (caps.canTrash != null) capabilities['canTrash'] = caps.canTrash;
      if (caps.canUntrash != null) capabilities['canUntrash'] = caps.canUntrash;

      json['capabilities'] = capabilities;
    }

    return json;
  }

  /// Convert Google Drive API Permission object to JSON safely
  Map<String, dynamic> _convertPermissionToJson(drive.Permission permission) {
    final json = <String, dynamic>{};

    if (permission.id != null) json['id'] = permission.id;
    if (permission.type != null) json['type'] = permission.type;
    if (permission.role != null) json['role'] = permission.role;
    if (permission.emailAddress != null) {
      json['emailAddress'] = permission.emailAddress;
    }
    if (permission.domain != null) {
      json['domain'] = permission.domain;
    }
    if (permission.displayName != null) {
      json['displayName'] = permission.displayName;
    }
    if (permission.photoLink != null) {
      json['photoLink'] = permission.photoLink;
    }
    if (permission.allowFileDiscovery != null) {
      json['allowFileDiscovery'] = permission.allowFileDiscovery;
    }
    if (permission.expirationTime != null) {
      json['expirationTime'] = permission.expirationTime!.toIso8601String();
    }
    if (permission.deleted != null) json['deleted'] = permission.deleted;

    return json;
  }

  // ============================================================================
  // ERROR HANDLING AND RETRY MECHANISMS
  // ============================================================================

  /// Check service health
  Future<Map<String, dynamic>> checkServiceHealth() async {
    try {
      final healthData = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'service_initialized': _isInitialized,
        'authentication_valid': false,
        'config_loaded': false,
        'quota_status': 'unknown',
        'errors': <String>[],
      };

      // Check authentication
      try {
        healthData['authentication_valid'] =
            await _authService.validateAuthentication();
      } catch (e) {
        healthData['errors'].add('Authentication failed: $e');
      }

      // Check configuration
      try {
        final config = await _configService.loadConfig();
        healthData['config_loaded'] = true;
        healthData['quota_status'] = {
          'used': config.apiQuotaUsed,
          'limit': config.apiQuotaLimit,
          'percentage': config.quotaUsagePercentage,
          'near_limit': config.isQuotaNearLimit,
          'exceeded': config.isQuotaExceeded,
        };
      } catch (e) {
        healthData['errors'].add('Configuration failed: $e');
      }

      // Test basic API operation
      try {
        final driveApi = await _getDriveApi();
        await driveApi.about.get($fields: 'user');
        healthData['api_accessible'] = true;
      } catch (e) {
        healthData['api_accessible'] = false;
        healthData['errors'].add('API test failed: $e');
      }

      return healthData;
    } catch (e) {
      LoggerService.error('Health check failed', e);
      return {
        'timestamp': DateTime.now().toIso8601String(),
        'service_initialized': false,
        'errors': ['Health check failed: $e'],
      };
    }
  }

  // ============================================================================
  // UTILITY AND HELPER METHODS
  // ============================================================================

  /// Search files by name or content
  Future<List<GoogleDriveFile>> searchFiles({
    String? nameQuery,
    String? contentQuery,
    String? mimeType,
    String? folderId,
    int? maxResults,
  }) async {
    try {
      LoggerService.debug('Searching files with query: $nameQuery');

      final driveApi = await _getDriveApi();

      // Build search query
      final queryParts = <String>[];

      if (nameQuery != null && nameQuery.isNotEmpty) {
        queryParts.add("name contains '$nameQuery'");
      }

      if (contentQuery != null && contentQuery.isNotEmpty) {
        queryParts.add("fullText contains '$contentQuery'");
      }

      if (mimeType != null) {
        queryParts.add("mimeType = '$mimeType'");
      }

      if (folderId != null) {
        queryParts.add("'$folderId' in parents");
      }

      queryParts.add("trashed = false");

      final query = queryParts.join(' and ');

      // Execute search
      final fileList = await driveApi.files.list(
        q: query,
        pageSize: maxResults ?? 50,
        $fields:
            'files(id,name,mimeType,size,createdTime,modifiedTime,webViewLink,webContentLink,parents,properties,md5Checksum,sha1Checksum,trashed,description,capabilities)',
      );

      // Convert to our models
      final files =
          fileList.files
              ?.map(
                (file) => GoogleDriveFile.fromGoogleDriveApi(
                  _convertFileToJson(file),
                ),
              )
              .toList() ??
          [];

      // Update quota usage
      await _updateQuotaUsage(1);

      LoggerService.debug('Search completed: ${files.length} files found');
      return files;
    } catch (e) {
      LoggerService.error('File search failed', e);
      rethrow;
    }
  }

  /// Get storage usage information
  Future<Map<String, dynamic>> getStorageUsage() async {
    try {
      final driveApi = await _getDriveApi();

      final about = await driveApi.about.get($fields: 'storageQuota,user');

      final storageQuota = about.storageQuota;

      return {
        'limit':
            storageQuota?.limit != null
                ? int.parse(storageQuota!.limit!)
                : null,
        'usage':
            storageQuota?.usage != null
                ? int.parse(storageQuota!.usage!)
                : null,
        'usage_in_drive':
            storageQuota?.usageInDrive != null
                ? int.parse(storageQuota!.usageInDrive!)
                : null,
        'user_email': about.user?.emailAddress,
        'user_name': about.user?.displayName,
      };
    } catch (e) {
      LoggerService.error('Failed to get storage usage', e);
      rethrow;
    }
  }

  /// Copy file
  Future<GoogleDriveFile> copyFile({
    required String sourceFileId,
    required String newName,
    String? destinationFolderId,
  }) async {
    try {
      LoggerService.info('Copying file: $sourceFileId to $newName');

      final driveApi = await _getDriveApi();

      final fileMetadata =
          drive.File()
            ..name = GoogleDriveUtils.sanitizeFileName(newName)
            ..parents =
                destinationFolderId != null ? [destinationFolderId] : null;

      final copiedFile = await driveApi.files.copy(fileMetadata, sourceFileId);

      final googleDriveFile = GoogleDriveFile.fromGoogleDriveApi(
        _convertFileToJson(copiedFile),
      );

      // Cache the file
      _fileCache[googleDriveFile.id] = googleDriveFile;

      // Update quota usage
      await _updateQuotaUsage(1);

      LoggerService.info('File copied successfully: ${googleDriveFile.id}');
      return googleDriveFile;
    } catch (e) {
      LoggerService.error('Failed to copy file: $sourceFileId', e);
      rethrow;
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /// Validate file before upload
  Future<void> _validateFile(io.File file, String fileName) async {
    // Check if file exists
    if (!await file.exists()) {
      throw GoogleDriveServiceException('File does not exist: ${file.path}');
    }

    // Check file name
    if (!GoogleDriveUtils.isValidFileName(fileName)) {
      throw GoogleDriveServiceException('Invalid file name: $fileName');
    }

    // Check file size
    final fileSize = await file.length();
    if (!GoogleDriveUtils.isValidFileSize(fileSize)) {
      throw GoogleDriveServiceException(
        'File size ${GoogleDriveUtils.formatFileSize(fileSize)} exceeds maximum allowed size',
      );
    }

    // Check quota
    final config = await _configService.loadConfig();
    if (config.isQuotaExceeded) {
      throw GoogleDriveServiceException('API quota exceeded');
    }
  }

  /// Upload file using simple method (for small files)
  Future<drive.File> _uploadFileSimple(
    drive.DriveApi driveApi,
    io.File file,
    drive.File fileMetadata,
    String mimeType,
    Function(GoogleDriveUploadProgress)? onProgress,
  ) async {
    LoggerService.debug('Using simple upload for file: ${fileMetadata.name}');

    final fileBytes = await file.readAsBytes();
    final media = drive.Media(
      Stream.value(fileBytes),
      fileBytes.length,
      contentType: mimeType,
    );

    // Report progress
    onProgress?.call(
      GoogleDriveUploadProgress(
        fileId: 'uploading',
        fileName: fileMetadata.name ?? 'unknown',
        bytesUploaded: 0,
        totalBytes: fileBytes.length,
        progress: 0.0,
        status: UploadStatus.uploading,
      ),
    );

    final uploadedFile = await driveApi.files.create(
      fileMetadata,
      uploadMedia: media,
    );

    // Report completion
    onProgress?.call(
      GoogleDriveUploadProgress(
        fileId: uploadedFile.id ?? 'unknown',
        fileName: fileMetadata.name ?? 'unknown',
        bytesUploaded: fileBytes.length,
        totalBytes: fileBytes.length,
        progress: 1.0,
        status: UploadStatus.completed,
      ),
    );

    return uploadedFile;
  }

  /// Upload file using chunked method (for large files)
  Future<drive.File> _uploadFileChunked(
    drive.DriveApi driveApi,
    io.File file,
    drive.File fileMetadata,
    String mimeType,
    Function(GoogleDriveUploadProgress)? onProgress,
  ) async {
    LoggerService.debug('Using chunked upload for file: ${fileMetadata.name}');

    final fileSize = await file.length();
    final fileStream = file.openRead();
    final media = drive.Media(fileStream, fileSize, contentType: mimeType);

    // Report initial progress
    onProgress?.call(
      GoogleDriveUploadProgress(
        fileId: 'uploading',
        fileName: fileMetadata.name ?? 'unknown',
        bytesUploaded: 0,
        totalBytes: fileSize,
        progress: 0.0,
        status: UploadStatus.uploading,
      ),
    );

    // TODO: Implement actual chunked upload with progress tracking
    // For now, use the standard upload method
    final uploadedFile = await driveApi.files.create(
      fileMetadata,
      uploadMedia: media,
    );

    // Report completion
    onProgress?.call(
      GoogleDriveUploadProgress(
        fileId: uploadedFile.id ?? 'unknown',
        fileName: fileMetadata.name ?? 'unknown',
        bytesUploaded: fileSize,
        totalBytes: fileSize,
        progress: 1.0,
        status: UploadStatus.completed,
      ),
    );

    return uploadedFile;
  }

  /// Update API quota usage
  Future<void> _updateQuotaUsage(int operations) async {
    try {
      final config = await _configService.loadConfig();
      final newUsage = config.apiQuotaUsed + operations;
      await _configService.updateQuotaUsage(newUsage);
    } catch (e) {
      LoggerService.warning('Failed to update quota usage: $e');
    }
  }

  /// Log file access to audit trail
  Future<void> _logFileAccess({
    String? fileId,
    String? fileName,
    String? documentId, // Optional claim_documents record ID
    required String action,
    required bool success,
    String? errorMessage,
    int? fileSize,
    int? durationMs,
  }) async {
    try {
      final userId = _pocketBaseService.currentUser?.id;
      if (userId == null) return;

      final logData = {
        'user_id': userId,
        'action': action,
        'timestamp': DateTime.now().toIso8601String(),
        'success': success,
        'error_message': errorMessage,
        'storage_type': 'google_drive',
        'google_drive_file_id': fileId,
        'file_size': fileSize,
        'duration_ms': durationMs,
      };

      // Only include document_id if we have it
      if (documentId != null) {
        logData['document_id'] = documentId;
      }

      await _pocketBaseService.createRecord(
        collectionName: 'document_access_logs',
        data: logData,
      );

      LoggerService.debug(
        'Logged Google Drive $action: fileId=$fileId, success=$success, documentId=$documentId',
      );
    } catch (e) {
      LoggerService.warning('Failed to log file access: $e');
      // Also log to internal logs as fallback
      LoggerService.info(
        'Google Drive $action ${success ? 'successful' : 'failed'}: '
        'fileId=$fileId, fileName=$fileName, fileSize=$fileSize, '
        'error=${errorMessage ?? 'none'}',
      );
    }
  }

  /// Start cache cleanup timer
  void _startCacheCleanup() {
    _cacheCleanupTimer = Timer.periodic(const Duration(minutes: 30), (_) {
      _cleanupCache();
    });
  }

  /// Clean up expired cache entries
  void _cleanupCache() {
    LoggerService.debug('Cleaning up Google Drive cache...');

    // For now, just clear the cache periodically
    // In a more sophisticated implementation, you would track access times
    // and only remove entries that haven't been accessed recently
    _fileCache.clear();
    _folderCache.clear();

    LoggerService.debug('Cache cleanup completed');
  }

  /// Dispose of the service
  void dispose() {
    _cacheCleanupTimer?.cancel();
    _authService.close();
    _fileCache.clear();
    _folderCache.clear();
    _isInitialized = false;
    LoggerService.debug('Google Drive service disposed');
  }

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Get current authentication status
  bool get isAuthenticated => _authService.isValid;
}

/// Exception for Google Drive service errors
class GoogleDriveServiceException implements Exception {
  final String message;
  final Object? cause;

  const GoogleDriveServiceException(this.message, [this.cause]);

  @override
  String toString() =>
      'GoogleDriveServiceException: $message${cause != null ? ' (caused by: $cause)' : ''}';
}
