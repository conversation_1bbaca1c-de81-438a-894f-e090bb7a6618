import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';

/// Examples of how the error mapping system works
/// This file demonstrates the user-friendly error messages for common scenarios
class ErrorMappingExamples {
  /// Example: Email already exists error
  static void demonstrateEmailAlreadyExistsError() {
    // Simulate a PocketBase ClientException for email already exists
    final error = ClientException(
      url: Uri.parse('https://test.com'),
      statusCode: 400,
      response: {
        'code': 400,
        'message': 'Failed to create record.',
        'data': {
          'email': {
            'code': 'validation_not_unique',
            'message': 'Value must be unique.',
          },
        },
      },
    );

    final friendlyError = PocketBaseService.mapPocketBaseError(error);

    // Result:
    // Title: "Email Already Registered"
    // Message: "This email address is already registered. Please try signing in instead."
    print('Email Already Exists:');
    print('Title: ${friendlyError.title}');
    print('Message: ${friendlyError.message}');
    print('Retryable: ${friendlyError.isRetryable}');
    print('---');
  }

  /// Example: Validation error
  static void demonstrateValidationError() {
    final error = ClientException(
      url: Uri.parse('https://test.com'),
      statusCode: 400,
      response: {
        'code': 400,
        'message': 'Failed to create record.',
        'data': {
          'password': {
            'code': 'validation_length_out_of_range',
            'message': 'The length must be at least 8 characters.',
          },
          'email': {
            'code': 'validation_is_email',
            'message': 'Must be a valid email address.',
          },
        },
      },
    );

    final friendlyError = PocketBaseService.mapPocketBaseError(error);

    // Result:
    // Title: "Validation Error"
    // Message: "Please check your information:\n• The length must be at least 8 characters.\n• Must be a valid email address."
    print('Validation Error:');
    print('Title: ${friendlyError.title}');
    print('Message: ${friendlyError.message}');
    print('Retryable: ${friendlyError.isRetryable}');
    print('---');
  }

  /// Example: Server error
  static void demonstrateServerError() {
    final error = ClientException(
      url: Uri.parse('https://test.com'),
      statusCode: 500,
      response: {'code': 500, 'message': 'Internal server error'},
    );

    final friendlyError = PocketBaseService.mapPocketBaseError(error);

    // Result:
    // Title: "Server Error"
    // Message: "Something went wrong on our end. Please try again in a moment."
    print('Server Error:');
    print('Title: ${friendlyError.title}');
    print('Message: ${friendlyError.message}');
    print('Retryable: ${friendlyError.isRetryable}');
    print('---');
  }

  /// Example: Network connection error
  static void demonstrateNetworkError() {
    // Simulate a SocketException
    final error = Exception('SocketException: Failed host lookup');

    final friendlyError = PocketBaseService.mapPocketBaseError(error);

    // Result:
    // Title: "Unexpected Error"
    // Message: "An unexpected error occurred. Please try again."
    print('Network Error:');
    print('Title: ${friendlyError.title}');
    print('Message: ${friendlyError.message}');
    print('Retryable: ${friendlyError.isRetryable}');
    print('---');
  }

  /// Example: Authentication error
  static void demonstrateAuthError() {
    final error = ClientException(
      url: Uri.parse('https://test.com'),
      statusCode: 401,
      response: {'code': 401, 'message': 'Invalid credentials'},
    );

    final friendlyError = PocketBaseService.mapPocketBaseError(error);

    // Result:
    // Title: "Authentication Failed"
    // Message: "Invalid email or password. Please try again."
    print('Authentication Error:');
    print('Title: ${friendlyError.title}');
    print('Message: ${friendlyError.message}');
    print('Retryable: ${friendlyError.isRetryable}');
    print('---');
  }

  /// Example: Rate limiting error
  static void demonstrateRateLimitError() {
    final error = ClientException(
      url: Uri.parse('https://test.com'),
      statusCode: 429,
      response: {'code': 429, 'message': 'Too many requests'},
    );

    final friendlyError = PocketBaseService.mapPocketBaseError(error);

    // Result:
    // Title: "Too Many Attempts"
    // Message: "Too many requests. Please wait a moment and try again."
    print('Rate Limit Error:');
    print('Title: ${friendlyError.title}');
    print('Message: ${friendlyError.message}');
    print('Retryable: ${friendlyError.isRetryable}');
    print('---');
  }

  /// Run all examples
  static void runAllExamples() {
    print('=== PocketBase Error Mapping Examples ===\n');

    demonstrateEmailAlreadyExistsError();
    demonstrateValidationError();
    demonstrateServerError();
    demonstrateNetworkError();
    demonstrateAuthError();
    demonstrateRateLimitError();

    print('=== End of Examples ===');
  }
}

/// Common error scenarios that the system handles
class CommonErrorScenarios {
  /// Scenarios for registration pages
  static const registrationScenarios = [
    'Email already registered',
    'Password too short',
    'Invalid email format',
    'Passwords don\'t match',
    'Required field missing',
    'Network connection lost',
    'Server temporarily unavailable',
    'Rate limit exceeded',
  ];

  /// Expected user-friendly messages for registration scenarios
  static const registrationMessages = {
    'Email already registered':
        'This email address is already registered. Please try signing in instead.',
    'Password too short': 'Please check your information and try again.',
    'Invalid email format': 'Please check your information and try again.',
    'Network connection lost':
        'No internet connection. Please check your network and try again.',
    'Server temporarily unavailable':
        'Something went wrong on our end. Please try again in a moment.',
    'Rate limit exceeded':
        'Too many requests. Please wait a moment and try again.',
  };
}
