import 'dart:async';
import 'dart:collection';
import '../models/performance_metrics.dart';
import '../utils/performance_utils.dart';
import 'logger_service.dart';
import 'pocketbase_service.dart';

/// Comprehensive performance monitoring service
class PerformanceMonitoringService {
  static final PerformanceMonitoringService _instance =
      PerformanceMonitoringService._internal();
  factory PerformanceMonitoringService() => _instance;
  PerformanceMonitoringService._internal();

  final PocketBaseService _pocketBaseService = PocketBaseService();

  // In-memory storage for recent metrics
  final Queue<PerformanceMetrics> _recentMetrics = Queue<PerformanceMetrics>();
  final Map<String, List<PerformanceMetrics>> _metricsByOperation = {};
  final Map<String, Timer> _activeOperations = {};

  // Performance thresholds and targets
  final Map<String, int> _performanceTargets = {
    'upload': 30000, // 30 seconds for uploads
    'download': 10000, // 10 seconds for downloads
    'api_call': 2000, // 2 seconds for API calls
    'cache_access': 100, // 100ms for cache access
  };

  // Statistics tracking
  Timer? _statisticsTimer;
  Timer? _alertTimer;
  bool _isInitialized = false;

  // Alert thresholds
  static const double _criticalSuccessRate = 0.90;
  static const double _warningSuccessRate = 0.95;
  static const int _maxRecentMetrics = 1000;
  static const Duration _statisticsInterval = Duration(minutes: 5);
  static const Duration _alertCheckInterval = Duration(minutes: 1);

  /// Initialize the performance monitoring service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggerService.info('Initializing Performance Monitoring Service...');

      // Start periodic statistics collection
      _startStatisticsTimer();

      // Start alert monitoring
      _startAlertTimer();

      _isInitialized = true;
      LoggerService.info(
        'Performance Monitoring Service initialized successfully',
      );
    } catch (e) {
      LoggerService.error(
        'Failed to initialize Performance Monitoring Service',
        e,
      );
      rethrow;
    }
  }

  /// Track a performance operation
  Future<T> trackOperation<T>(
    String operationType,
    Future<T> Function() operation, {
    String? operationId,
    Map<String, dynamic>? metadata,
    String? userId,
    String? sessionId,
  }) async {
    final id =
        operationId ?? PerformanceUtils.generateOperationId(operationType);
    final startTime = DateTime.now();

    LoggerService.debug('Starting tracked operation: $operationType ($id)');

    try {
      // Start tracking
      _startOperation(id, operationType, startTime);

      // Execute operation
      final result = await operation();

      // Record success
      await _recordOperationComplete(
        operationType: operationType,
        operationId: id,
        startTime: startTime,
        endTime: DateTime.now(),
        success: true,
        metadata: metadata,
        userId: userId,
        sessionId: sessionId,
      );

      return result;
    } catch (e) {
      // Record failure
      await _recordOperationComplete(
        operationType: operationType,
        operationId: id,
        startTime: startTime,
        endTime: DateTime.now(),
        success: false,
        errorMessage: e.toString(),
        metadata: metadata,
        userId: userId,
        sessionId: sessionId,
      );

      rethrow;
    } finally {
      _endOperation(id);
    }
  }

  /// Track a file operation with size information
  Future<T> trackFileOperation<T>(
    String operationType,
    Future<T> Function() operation, {
    required int fileSizeBytes,
    String? operationId,
    Map<String, dynamic>? metadata,
    String? userId,
    String? sessionId,
  }) async {
    final enhancedMetadata = {'fileSizeBytes': fileSizeBytes, ...?metadata};

    return trackOperation(
      operationType,
      operation,
      operationId: operationId,
      metadata: enhancedMetadata,
      userId: userId,
      sessionId: sessionId,
    );
  }

  /// Record operation completion
  Future<void> _recordOperationComplete({
    required String operationType,
    required String operationId,
    required DateTime startTime,
    required DateTime endTime,
    required bool success,
    String? errorMessage,
    Map<String, dynamic>? metadata,
    String? userId,
    String? sessionId,
  }) async {
    try {
      // Calculate network speed if file size is available
      double? networkSpeed;
      final fileSizeBytes = metadata?['fileSizeBytes'] as int?;
      if (fileSizeBytes != null) {
        final durationMs = endTime.difference(startTime).inMilliseconds;
        networkSpeed = PerformanceUtils.calculateNetworkSpeed(
          fileSizeBytes,
          durationMs,
        );
      }

      // Create performance metrics
      final metrics = PerformanceMetrics(
        operationType: operationType,
        operationId: operationId,
        startTime: startTime,
        endTime: endTime,
        success: success,
        errorMessage: errorMessage,
        metadata: metadata ?? {},
        fileSizeBytes: fileSizeBytes,
        networkSpeedBytesPerSecond: networkSpeed,
        userId: userId,
        sessionId: sessionId,
      );

      // Validate metrics
      if (!PerformanceUtils.isValidMetrics(metrics)) {
        LoggerService.warning('Invalid performance metrics recorded: $metrics');
        return;
      }

      // Store in memory
      _addMetricsToMemory(metrics);

      // Log performance
      PerformanceUtils.logPerformanceMetrics(metrics);

      // Store in database (async, don't wait)
      _storeMetricsInDatabase(metrics);

      // Check for alerts
      _checkForAlerts(metrics);
    } catch (e) {
      LoggerService.error('Failed to record operation completion', e);
    }
  }

  /// Add metrics to in-memory storage
  void _addMetricsToMemory(PerformanceMetrics metrics) {
    // Add to recent metrics queue
    _recentMetrics.add(metrics);
    if (_recentMetrics.length > _maxRecentMetrics) {
      _recentMetrics.removeFirst();
    }

    // Add to operation-specific metrics
    _metricsByOperation.putIfAbsent(metrics.operationType, () => []);
    _metricsByOperation[metrics.operationType]!.add(metrics);

    // Keep only recent metrics per operation
    final operationMetrics = _metricsByOperation[metrics.operationType]!;
    if (operationMetrics.length > 100) {
      operationMetrics.removeAt(0);
    }
  }

  /// Store metrics in database
  Future<void> _storeMetricsInDatabase(PerformanceMetrics metrics) async {
    try {
      await _pocketBaseService.pb
          .collection('performance_metrics')
          .create(body: metrics.toJson());
    } catch (e) {
      LoggerService.debug('Failed to store metrics in database: $e');
      // Don't fail the operation if database storage fails
    }
  }

  /// Check for performance alerts
  void _checkForAlerts(PerformanceMetrics metrics) {
    // Check if operation meets performance targets
    if (!PerformanceUtils.meetsPerformanceTargets(metrics)) {
      _triggerPerformanceAlert(
        'Performance target missed',
        'Operation ${metrics.operationType} took ${metrics.durationMs}ms '
            '(target: ${_performanceTargets[metrics.operationType]}ms)',
        PerformanceHealthStatus.warning,
      );
    }

    // Check for failures
    if (!metrics.success) {
      _triggerPerformanceAlert(
        'Operation failed',
        'Operation ${metrics.operationType} failed: ${metrics.errorMessage}',
        PerformanceHealthStatus.critical,
      );
    }
  }

  /// Get performance statistics for an operation type
  PerformanceStatistics? getOperationStatistics(
    String operationType, {
    Duration? period,
  }) {
    final metrics = _metricsByOperation[operationType];
    if (metrics == null || metrics.isEmpty) return null;

    // Filter by period if specified
    final filteredMetrics =
        period != null
            ? metrics
                .where((m) => DateTime.now().difference(m.endTime) <= period)
                .toList()
            : metrics;

    if (filteredMetrics.isEmpty) return null;

    return _calculateStatistics(operationType, filteredMetrics);
  }

  /// Get overall performance snapshot
  Future<PerformanceSnapshot> getPerformanceSnapshot() async {
    final operationStats = <String, PerformanceStatistics>{};

    // Calculate statistics for each operation type
    for (final operationType in _metricsByOperation.keys) {
      final stats = getOperationStatistics(operationType);
      if (stats != null) {
        operationStats[operationType] = stats;
      }
    }

    // Get system and network metrics
    final systemMetrics = await PerformanceUtils.getSystemResourceMetrics();
    final networkMetrics = await PerformanceUtils.getNetworkMetrics();

    // Get cache metrics (simplified)
    const cacheMetrics = CacheMetrics(
      hitRatePercent: 85.0,
      totalRequests: 1000,
      cacheHits: 850,
      cacheMisses: 150,
      averageAccessTimeMs: 25.0,
      totalSizeBytes: 50 * 1024 * 1024,
      entryCount: 500,
    );

    return PerformanceSnapshot(
      timestamp: DateTime.now(),
      operationStats: operationStats,
      systemMetrics: systemMetrics,
      networkMetrics: networkMetrics,
      cacheMetrics: cacheMetrics,
    );
  }

  /// Calculate statistics from metrics list
  PerformanceStatistics _calculateStatistics(
    String operationType,
    List<PerformanceMetrics> metrics,
  ) {
    final durations = metrics.map((m) => m.durationMs).toList();
    final percentiles = PerformanceUtils.calculatePercentiles(durations);

    final successfulOps = metrics.where((m) => m.success).length;
    final failedOps = metrics.length - successfulOps;
    final successRate =
        metrics.isNotEmpty ? successfulOps / metrics.length : 0.0;

    final avgScore =
        metrics.isNotEmpty
            ? metrics.map((m) => m.performanceScore).reduce((a, b) => a + b) /
                metrics.length
            : 0.0;

    // Count errors
    final errorCounts = <String, int>{};
    for (final metric in metrics.where((m) => !m.success)) {
      final error = metric.errorMessage ?? 'Unknown error';
      errorCounts[error] = (errorCounts[error] ?? 0) + 1;
    }

    return PerformanceStatistics(
      operationType: operationType,
      totalOperations: metrics.length,
      successfulOperations: successfulOps,
      failedOperations: failedOps,
      averageDurationMs: percentiles['avg']!,
      medianDurationMs: percentiles['p50']!,
      p95DurationMs: percentiles['p95']!,
      p99DurationMs: percentiles['p99']!,
      minDurationMs: percentiles['min']!,
      maxDurationMs: percentiles['max']!,
      successRate: successRate,
      averagePerformanceScore: avgScore,
      periodStart: metrics.first.startTime,
      periodEnd: metrics.last.endTime,
      errorCounts: errorCounts,
    );
  }

  /// Start tracking an operation
  void _startOperation(
    String operationId,
    String operationType,
    DateTime startTime,
  ) {
    // This could be used for timeout monitoring
    _activeOperations[operationId] = Timer(
      Duration(minutes: 5), // 5 minute timeout
      () => _handleOperationTimeout(operationId, operationType),
    );
  }

  /// End tracking an operation
  void _endOperation(String operationId) {
    _activeOperations[operationId]?.cancel();
    _activeOperations.remove(operationId);
  }

  /// Handle operation timeout
  void _handleOperationTimeout(String operationId, String operationType) {
    LoggerService.warning('Operation timeout: $operationType ($operationId)');
    _triggerPerformanceAlert(
      'Operation timeout',
      'Operation $operationType ($operationId) timed out after 5 minutes',
      PerformanceHealthStatus.critical,
    );
    _activeOperations.remove(operationId);
  }

  /// Start statistics collection timer
  void _startStatisticsTimer() {
    _statisticsTimer = Timer.periodic(_statisticsInterval, (_) {
      _collectAndStoreStatistics();
    });
  }

  /// Start alert monitoring timer
  void _startAlertTimer() {
    _alertTimer = Timer.periodic(_alertCheckInterval, (_) {
      _checkSystemAlerts();
    });
  }

  /// Collect and store aggregated statistics
  Future<void> _collectAndStoreStatistics() async {
    try {
      final snapshot = await getPerformanceSnapshot();

      // Store snapshot in database
      await _pocketBaseService.pb
          .collection('performance_snapshots')
          .create(body: snapshot.toJson());

      LoggerService.debug('Performance statistics collected and stored');
    } catch (e) {
      LoggerService.error('Failed to collect performance statistics', e);
    }
  }

  /// Check for system-wide alerts
  void _checkSystemAlerts() {
    for (final operationType in _metricsByOperation.keys) {
      final stats = getOperationStatistics(
        operationType,
        period: const Duration(minutes: 10),
      );

      if (stats != null) {
        if (stats.successRate < _criticalSuccessRate) {
          _triggerPerformanceAlert(
            'Critical success rate',
            'Operation $operationType has ${(stats.successRate * 100).toStringAsFixed(1)}% success rate',
            PerformanceHealthStatus.critical,
          );
        } else if (stats.successRate < _warningSuccessRate) {
          _triggerPerformanceAlert(
            'Low success rate',
            'Operation $operationType has ${(stats.successRate * 100).toStringAsFixed(1)}% success rate',
            PerformanceHealthStatus.warning,
          );
        }
      }
    }
  }

  /// Trigger a performance alert
  void _triggerPerformanceAlert(
    String title,
    String message,
    PerformanceHealthStatus severity,
  ) {
    LoggerService.warning('Performance Alert [$severity]: $title - $message');

    // In a real implementation, you would:
    // - Send notifications to administrators
    // - Update monitoring dashboards
    // - Trigger automated recovery procedures
  }

  /// Get recent metrics
  List<PerformanceMetrics> getRecentMetrics({int? limit}) {
    final metrics = _recentMetrics.toList();
    if (limit != null && metrics.length > limit) {
      return metrics.sublist(metrics.length - limit);
    }
    return metrics;
  }

  /// Clear all metrics
  void clearMetrics() {
    _recentMetrics.clear();
    _metricsByOperation.clear();
    LoggerService.info('Performance metrics cleared');
  }

  /// Dispose the service
  void dispose() {
    _statisticsTimer?.cancel();
    _alertTimer?.cancel();
    for (final timer in _activeOperations.values) {
      timer.cancel();
    }
    _activeOperations.clear();
    _isInitialized = false;
    LoggerService.debug('Performance Monitoring Service disposed');
  }
}
