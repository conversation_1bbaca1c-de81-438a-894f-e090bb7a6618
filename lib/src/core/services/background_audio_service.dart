import 'dart:async';
import 'package:just_audio/just_audio.dart';
import 'package:audio_service/audio_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_podcast_model.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';

// Global provider for the background audio service
final backgroundAudioServiceProvider = Provider<BackgroundAudioService>((ref) {
  return BackgroundAudioService._instance;
});

// Provider for current playing state
final currentPlayingPodcastProvider = StateProvider<CoFunderPodcastModel?>(
  (ref) => null,
);

// Provider for playback state that listens to the audio service
final audioPlaybackStateProvider = StreamProvider<AudioPlaybackState>((ref) {
  final audioService = ref.watch(backgroundAudioServiceProvider);
  return audioService.playbackStateStream;
});

// Provider for current podcast that listens to the audio service
final currentPodcastStreamProvider = StreamProvider<CoFunderPodcastModel?>((
  ref,
) {
  final audioService = ref.watch(backgroundAudioServiceProvider);
  return audioService.currentPodcastStream;
});

// Audio playback state model
class AudioPlaybackState {
  final bool isPlaying;
  final bool isLoading;
  final Duration position;
  final Duration duration;
  final double volume;
  final String? error;

  const AudioPlaybackState({
    this.isPlaying = false,
    this.isLoading = false,
    this.position = Duration.zero,
    this.duration = Duration.zero,
    this.volume = 1.0,
    this.error,
  });

  AudioPlaybackState copyWith({
    bool? isPlaying,
    bool? isLoading,
    Duration? position,
    Duration? duration,
    double? volume,
    String? error,
  }) {
    return AudioPlaybackState(
      isPlaying: isPlaying ?? this.isPlaying,
      isLoading: isLoading ?? this.isLoading,
      position: position ?? this.position,
      duration: duration ?? this.duration,
      volume: volume ?? this.volume,
      error: error,
    );
  }
}

// Background audio service implementation
class BackgroundAudioService extends BaseAudioHandler with SeekHandler {
  static final BackgroundAudioService _instance =
      BackgroundAudioService._internal();
  factory BackgroundAudioService() => _instance;
  BackgroundAudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  final PocketBaseService _pbService = PocketBaseService();

  CoFunderPodcastModel? _currentPodcast;
  bool _hasPlayedOnce = false;

  // Stream controllers for state management
  final StreamController<AudioPlaybackState> _stateController =
      StreamController<AudioPlaybackState>.broadcast();
  final StreamController<CoFunderPodcastModel?> _podcastController =
      StreamController<CoFunderPodcastModel?>.broadcast();

  // Getters for streams
  Stream<AudioPlaybackState> get playbackStateStream => _stateController.stream;
  Stream<CoFunderPodcastModel?> get currentPodcastStream =>
      _podcastController.stream;

  CoFunderPodcastModel? get currentPodcast => _currentPodcast;
  AudioPlaybackState get currentState => _currentPlaybackState;

  AudioPlaybackState _currentPlaybackState = const AudioPlaybackState();

  @override
  Future<void> prepare() async {
    await _initializeAudioPlayer();
  }

  Future<void> _initializeAudioPlayer() async {
    try {
      // Listen to player state changes
      _audioPlayer.playerStateStream.listen((playerState) {
        final isPlaying = playerState.playing;
        final processingState = playerState.processingState;

        _updatePlaybackState(
          isPlaying: isPlaying,
          isLoading:
              processingState == ProcessingState.loading ||
              processingState == ProcessingState.buffering,
        );

        // Update AudioService playback state
        playbackState.add(
          PlaybackState(
            controls: [
              MediaControl.skipToPrevious,
              isPlaying ? MediaControl.pause : MediaControl.play,
              MediaControl.stop,
              MediaControl.skipToNext,
            ],
            systemActions: const {
              MediaAction.seek,
              MediaAction.seekForward,
              MediaAction.seekBackward,
            },
            androidCompactActionIndices: const [0, 1, 3],
            processingState: _mapProcessingState(processingState),
            playing: isPlaying,
            updatePosition: _audioPlayer.position,
            bufferedPosition: _audioPlayer.bufferedPosition,
            speed: _audioPlayer.speed,
            queueIndex: 0,
          ),
        );
      });

      // Listen to position changes
      _audioPlayer.positionStream.listen((position) {
        _updatePlaybackState(position: position);
      });

      // Listen to duration changes
      _audioPlayer.durationStream.listen((duration) {
        if (duration != null) {
          _updatePlaybackState(duration: duration);
        }
      });

      LoggerService.info('Background audio service initialized successfully');
    } catch (e) {
      LoggerService.error('Error initializing background audio service', e);
      _updatePlaybackState(error: 'Failed to initialize audio service');
    }
  }

  AudioProcessingState _mapProcessingState(ProcessingState state) {
    switch (state) {
      case ProcessingState.idle:
        return AudioProcessingState.idle;
      case ProcessingState.loading:
        return AudioProcessingState.loading;
      case ProcessingState.buffering:
        return AudioProcessingState.buffering;
      case ProcessingState.ready:
        return AudioProcessingState.ready;
      case ProcessingState.completed:
        return AudioProcessingState.completed;
    }
  }

  void _updatePlaybackState({
    bool? isPlaying,
    bool? isLoading,
    Duration? position,
    Duration? duration,
    double? volume,
    String? error,
  }) {
    _currentPlaybackState = _currentPlaybackState.copyWith(
      isPlaying: isPlaying,
      isLoading: isLoading,
      position: position,
      duration: duration,
      volume: volume,
      error: error,
    );

    if (!_stateController.isClosed) {
      _stateController.add(_currentPlaybackState);
    }
  }

  Future<void> playPodcast(
    CoFunderPodcastModel podcast,
    String audioUrl,
  ) async {
    try {
      _updatePlaybackState(isLoading: true, error: null);

      // Set current podcast
      _currentPodcast = podcast;
      _hasPlayedOnce = false;

      // Update media item for system controls
      mediaItem.add(
        MediaItem(
          id: podcast.id,
          album: 'Educational Content',
          title: 'Episode ${podcast.episodeNumber ?? 'Unknown'}',
          artist: '3Pay Global',
          duration:
              podcast.durationSeconds != null
                  ? Duration(seconds: podcast.durationSeconds!)
                  : null,
          // Remove artUri to avoid network issues - system will use default icon
          extras: {
            'podcastId': podcast.id,
            'contentSlug': podcast.contentItemSlug,
          },
        ),
      );

      // Set audio source and play
      await _audioPlayer.setAudioSource(AudioSource.uri(Uri.parse(audioUrl)));
      await _audioPlayer.play();

      // Notify listeners about current podcast
      if (!_podcastController.isClosed) {
        _podcastController.add(_currentPodcast);
      }

      LoggerService.info('Started playing podcast: ${podcast.id}');
    } catch (e) {
      LoggerService.error('Error playing podcast', e);
      _updatePlaybackState(
        isLoading: false,
        error: 'Failed to play podcast: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> play() async {
    try {
      await _audioPlayer.play();

      // Increment play count on first play
      if (!_hasPlayedOnce && _currentPodcast != null) {
        _hasPlayedOnce = true;
        await _incrementPlayCount(_currentPodcast!);
      }
    } catch (e) {
      LoggerService.error('Error playing audio', e);
      _updatePlaybackState(error: 'Failed to play audio');
    }
  }

  @override
  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      LoggerService.error('Error pausing audio', e);
      _updatePlaybackState(error: 'Failed to pause audio');
    }
  }

  @override
  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _currentPodcast = null;
      _hasPlayedOnce = false;

      if (!_podcastController.isClosed) {
        _podcastController.add(null);
      }

      _updatePlaybackState(isPlaying: false, position: Duration.zero);
    } catch (e) {
      LoggerService.error('Error stopping audio', e);
      _updatePlaybackState(error: 'Failed to stop audio');
    }
  }

  @override
  Future<void> seek(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      LoggerService.error('Error seeking audio', e);
      _updatePlaybackState(error: 'Failed to seek audio');
    }
  }

  Future<void> setVolume(double volume) async {
    try {
      await _audioPlayer.setVolume(volume);
      _updatePlaybackState(volume: volume);
    } catch (e) {
      LoggerService.error('Error setting volume', e);
      _updatePlaybackState(error: 'Failed to set volume');
    }
  }

  Future<void> skipForward([Duration? interval]) async {
    final currentPosition = _audioPlayer.position;
    final skipDuration = interval ?? const Duration(seconds: 15);
    final newPosition = currentPosition + skipDuration;
    await seek(newPosition);
  }

  Future<void> skipBackward([Duration? interval]) async {
    final currentPosition = _audioPlayer.position;
    final skipDuration = interval ?? const Duration(seconds: 15);
    final newPosition = currentPosition - skipDuration;
    await seek(newPosition > Duration.zero ? newPosition : Duration.zero);
  }

  Future<void> _incrementPlayCount(CoFunderPodcastModel podcast) async {
    try {
      final currentPlayCount = podcast.playCount ?? 0;
      await _pbService.client
          .collection('cofunder_podcasts')
          .update(podcast.id, body: {'play_count': currentPlayCount + 1});

      LoggerService.info('Incremented play count for podcast: ${podcast.id}');
    } catch (e) {
      LoggerService.error('Error incrementing play count', e);
    }
  }

  @override
  Future<void> onTaskRemoved() async {
    // Handle task removal - decide whether to stop playback or continue
    await stop();
  }

  void dispose() {
    _audioPlayer.dispose();
    _stateController.close();
    _podcastController.close();
  }
}
