import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/utils/security_utils.dart';

/// Encryption service for data protection
class EncryptionService {
  static final EncryptionService _instance = EncryptionService._internal();
  factory EncryptionService() => _instance;
  EncryptionService._internal();

  static const int _keyLength = 32; // 256 bits
  static const int _ivLength = 16; // 128 bits
  static const int _saltLength = 16; // 128 bits

  /// Encrypt data using AES-256-GCM (simulated with available crypto)
  Future<EncryptionResult> encryptData(
    String data, {
    String? key,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      LoggerService.debug('Encrypting data...');

      // Generate or use provided key
      final encryptionKey = key ?? _generateEncryptionKey();

      // Generate random IV and salt
      final iv = _generateRandomBytes(_ivLength);
      final salt = _generateRandomBytes(_saltLength);

      // For demonstration, we'll use a simple XOR cipher with SHA-256 hash
      // In production, use proper AES-GCM encryption
      final keyHash =
          sha256.convert(utf8.encode(encryptionKey + base64Encode(salt))).bytes;
      final dataBytes = utf8.encode(data);
      final encryptedBytes = _xorEncrypt(dataBytes, keyHash, iv);

      // Create checksum for integrity verification
      final checksum = _generateChecksum(encryptedBytes, keyHash);

      final result = EncryptionResult(
        encryptedData: base64Encode(encryptedBytes),
        iv: base64Encode(iv),
        salt: base64Encode(salt),
        checksum: checksum,
        algorithm: 'AES-256-GCM-SIMULATED',
        keyId: SecurityUtils.generateHash(encryptionKey).substring(0, 16),
        metadata: metadata ?? {},
        timestamp: DateTime.now(),
      );

      LoggerService.debug('Data encrypted successfully');
      return result;
    } catch (e) {
      LoggerService.error('Failed to encrypt data', e);
      rethrow;
    }
  }

  /// Decrypt data
  Future<String> decryptData(
    EncryptionResult encryptionResult,
    String key,
  ) async {
    try {
      LoggerService.debug('Decrypting data...');

      // Validate key
      final expectedKeyId = SecurityUtils.generateHash(key).substring(0, 16);
      if (encryptionResult.keyId != expectedKeyId) {
        throw EncryptionException('Invalid decryption key');
      }

      // Decode components
      final encryptedBytes = base64Decode(encryptionResult.encryptedData);
      final iv = base64Decode(encryptionResult.iv);

      // Regenerate key hash using salt from encryption result
      final keyHash =
          sha256.convert(utf8.encode(key + encryptionResult.salt)).bytes;

      // Verify integrity
      final expectedChecksum = _generateChecksum(encryptedBytes, keyHash);
      if (encryptionResult.checksum != expectedChecksum) {
        throw EncryptionException('Data integrity check failed');
      }

      // Decrypt data
      final decryptedBytes = _xorDecrypt(encryptedBytes, keyHash, iv);
      final decryptedData = utf8.decode(decryptedBytes);

      LoggerService.debug('Data decrypted successfully');
      return decryptedData;
    } catch (e) {
      LoggerService.error('Failed to decrypt data', e);
      rethrow;
    }
  }

  /// Encrypt file content
  Future<EncryptionResult> encryptFile(
    Uint8List fileBytes, {
    String? key,
    String? filename,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      LoggerService.debug('Encrypting file: ${filename ?? 'unknown'}');

      // Generate or use provided key
      final encryptionKey = key ?? _generateEncryptionKey();

      // Generate random IV and salt
      final iv = _generateRandomBytes(_ivLength);
      final salt = _generateRandomBytes(_saltLength);

      // Generate key hash
      final keyHash =
          sha256.convert(utf8.encode(encryptionKey + base64Encode(salt))).bytes;

      // Encrypt file bytes
      final encryptedBytes = _xorEncrypt(fileBytes, keyHash, iv);

      // Create checksum
      final checksum = _generateChecksum(encryptedBytes, keyHash);

      final fileMetadata = Map<String, dynamic>.from(metadata ?? {});
      if (filename != null) {
        fileMetadata['original_filename'] = filename;
      }
      fileMetadata['file_size'] = fileBytes.length;
      fileMetadata['encrypted_size'] = encryptedBytes.length;

      final result = EncryptionResult(
        encryptedData: base64Encode(encryptedBytes),
        iv: base64Encode(iv),
        salt: base64Encode(salt),
        checksum: checksum,
        algorithm: 'AES-256-GCM-SIMULATED',
        keyId: SecurityUtils.generateHash(encryptionKey).substring(0, 16),
        metadata: fileMetadata,
        timestamp: DateTime.now(),
      );

      LoggerService.debug('File encrypted successfully');
      return result;
    } catch (e) {
      LoggerService.error('Failed to encrypt file', e);
      rethrow;
    }
  }

  /// Decrypt file content
  Future<Uint8List> decryptFile(
    EncryptionResult encryptionResult,
    String key,
  ) async {
    try {
      LoggerService.debug('Decrypting file...');

      // Validate key
      final expectedKeyId = SecurityUtils.generateHash(key).substring(0, 16);
      if (encryptionResult.keyId != expectedKeyId) {
        throw EncryptionException('Invalid decryption key');
      }

      // Decode components
      final encryptedBytes = base64Decode(encryptionResult.encryptedData);
      final iv = base64Decode(encryptionResult.iv);

      // Regenerate key hash using salt from encryption result
      final keyHash =
          sha256.convert(utf8.encode(key + encryptionResult.salt)).bytes;

      // Verify integrity
      final expectedChecksum = _generateChecksum(encryptedBytes, keyHash);
      if (encryptionResult.checksum != expectedChecksum) {
        throw EncryptionException('File integrity check failed');
      }

      // Decrypt file bytes
      final decryptedBytes = _xorDecrypt(encryptedBytes, keyHash, iv);

      LoggerService.debug('File decrypted successfully');
      return Uint8List.fromList(decryptedBytes);
    } catch (e) {
      LoggerService.error('Failed to decrypt file', e);
      rethrow;
    }
  }

  /// Generate encryption key
  String _generateEncryptionKey() {
    return SecurityUtils.generateSecureId(
      length: _keyLength * 2,
    ); // Hex representation
  }

  /// Generate random bytes
  List<int> _generateRandomBytes(int length) {
    final random = Random.secure();
    return List.generate(length, (_) => random.nextInt(256));
  }

  /// Simple XOR encryption (for demonstration - use proper AES in production)
  List<int> _xorEncrypt(List<int> data, List<int> key, List<int> iv) {
    final result = <int>[];
    final keyStream = _generateKeyStream(key, iv, data.length);

    for (int i = 0; i < data.length; i++) {
      result.add(data[i] ^ keyStream[i]);
    }

    return result;
  }

  /// Simple XOR decryption
  List<int> _xorDecrypt(List<int> encryptedData, List<int> key, List<int> iv) {
    return _xorEncrypt(encryptedData, key, iv); // XOR is symmetric
  }

  /// Generate key stream for encryption
  List<int> _generateKeyStream(List<int> key, List<int> iv, int length) {
    final keyStream = <int>[];
    final combined = [...key, ...iv];

    for (int i = 0; i < length; i++) {
      final hash = sha256.convert([...combined, i]).bytes;
      keyStream.add(hash[i % hash.length]);
    }

    return keyStream;
  }

  /// Generate checksum for integrity verification
  String _generateChecksum(List<int> data, List<int> key) {
    final combined = [...data, ...key];
    return sha256.convert(combined).toString();
  }

  /// Validate encryption result
  bool validateEncryptionResult(EncryptionResult result) {
    try {
      // Check required fields
      if (result.encryptedData.isEmpty ||
          result.iv.isEmpty ||
          result.salt.isEmpty ||
          result.checksum.isEmpty ||
          result.keyId.isEmpty) {
        return false;
      }

      // Validate base64 encoding
      base64Decode(result.encryptedData);
      base64Decode(result.iv);
      base64Decode(result.salt);

      // Check timestamp
      if (result.timestamp.isAfter(DateTime.now())) {
        return false;
      }

      return true;
    } catch (e) {
      LoggerService.warning('Invalid encryption result: $e');
      return false;
    }
  }

  /// Get encryption status
  Map<String, dynamic> getEncryptionStatus() {
    return {
      'service_initialized': true,
      'algorithm': 'AES-256-GCM-SIMULATED',
      'key_length': _keyLength,
      'iv_length': _ivLength,
      'salt_length': _saltLength,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// Encryption result containing encrypted data and metadata
class EncryptionResult {
  final String encryptedData;
  final String iv;
  final String salt;
  final String checksum;
  final String algorithm;
  final String keyId;
  final Map<String, dynamic> metadata;
  final DateTime timestamp;

  EncryptionResult({
    required this.encryptedData,
    required this.iv,
    required this.salt,
    required this.checksum,
    required this.algorithm,
    required this.keyId,
    required this.metadata,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'encrypted_data': encryptedData,
      'iv': iv,
      'salt': salt,
      'checksum': checksum,
      'algorithm': algorithm,
      'key_id': keyId,
      'metadata': metadata,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory EncryptionResult.fromJson(Map<String, dynamic> json) {
    return EncryptionResult(
      encryptedData: json['encrypted_data'] as String,
      iv: json['iv'] as String,
      salt: json['salt'] as String,
      checksum: json['checksum'] as String,
      algorithm: json['algorithm'] as String,
      keyId: json['key_id'] as String,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  @override
  String toString() {
    return 'EncryptionResult{algorithm: $algorithm, keyId: $keyId, timestamp: $timestamp}';
  }
}

/// Custom exception for encryption errors
class EncryptionException implements Exception {
  final String message;

  EncryptionException(this.message);

  @override
  String toString() => 'EncryptionException: $message';
}
