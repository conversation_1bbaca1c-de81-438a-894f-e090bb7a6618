import 'dart:async';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/enhanced_audit_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/security_event.dart';
import 'package:three_pay_group_litigation_platform/src/core/utils/security_utils.dart';

/// Security incident detection and response service
class SecurityIncidentService {
  static final SecurityIncidentService _instance =
      SecurityIncidentService._internal();
  factory SecurityIncidentService() => _instance;
  SecurityIncidentService._internal();

  final PocketBaseService _pocketBaseService = PocketBaseService();
  final EnhancedAuditService _auditService = EnhancedAuditService();

  // Incident detection configuration
  static const int _maxFailedAttempts = 5;
  static const Duration _failedAttemptWindow = Duration(minutes: 15);
  static const int _suspiciousActivityThreshold = 80;
  static const Duration _incidentResponseTime = Duration(minutes: 15);

  // Active monitoring
  final Map<String, List<DateTime>> _failedAttempts = {};
  final Map<String, SecurityIncident> _activeIncidents = {};
  Timer? _monitoringTimer;

  /// Initialize security incident service
  Future<void> initialize() async {
    try {
      LoggerService.info('Initializing security incident service...');

      // Start continuous monitoring
      _startContinuousMonitoring();

      LoggerService.info('Security incident service initialized successfully');
    } catch (e) {
      LoggerService.error('Failed to initialize security incident service', e);
      rethrow;
    }
  }

  /// Detect and respond to security incidents
  Future<void> analyzeSecurityEvent(SecurityEvent event) async {
    try {
      LoggerService.debug('Analyzing security event: ${event.type}');

      // Check for suspicious patterns
      final suspiciousPatterns = await _detectSuspiciousPatterns(event);

      if (suspiciousPatterns.isNotEmpty) {
        await _handleSuspiciousActivity(event, suspiciousPatterns);
      }

      // Check for failed authentication attempts
      if (event.type == 'authentication' && !event.success) {
        await _handleFailedAuthentication(event);
      }

      // Check for high-risk activities
      if (event.riskScore >= _suspiciousActivityThreshold) {
        await _handleHighRiskActivity(event);
      }

      // Check for anomalous behavior
      final anomalies = await _detectAnomalies(event);
      if (anomalies.isNotEmpty) {
        await _handleAnomalousActivity(event, anomalies);
      }

      LoggerService.debug('Security event analysis completed');
    } catch (e) {
      LoggerService.error('Failed to analyze security event', e);
    }
  }

  /// Handle failed authentication attempts
  Future<void> _handleFailedAuthentication(SecurityEvent event) async {
    try {
      final userId = event.userId ?? 'unknown';
      final ipAddress = event.ipAddress ?? 'unknown';
      final key = '$userId:$ipAddress';

      // Track failed attempts
      _failedAttempts.putIfAbsent(key, () => []);
      _failedAttempts[key]!.add(event.timestamp);

      // Clean old attempts
      final cutoff = DateTime.now().subtract(_failedAttemptWindow);
      _failedAttempts[key]!.removeWhere((attempt) => attempt.isBefore(cutoff));

      // Check if threshold exceeded
      if (_failedAttempts[key]!.length >= _maxFailedAttempts) {
        await _createSecurityIncident(
          type: SecurityIncidentType.bruteForceAttack,
          severity: SecurityIncidentSeverity.high,
          description: 'Multiple failed authentication attempts detected',
          affectedUser: userId,
          sourceIp: ipAddress,
          evidence: {
            'failed_attempts': _failedAttempts[key]!.length,
            'time_window': _failedAttemptWindow.inMinutes,
            'attempts_timestamps':
                _failedAttempts[key]!.map((t) => t.toIso8601String()).toList(),
          },
          relatedEvents: [event.id],
        );

        // Clear failed attempts for this key
        _failedAttempts.remove(key);
      }
    } catch (e) {
      LoggerService.error('Failed to handle failed authentication', e);
    }
  }

  /// Handle suspicious activity
  Future<void> _handleSuspiciousActivity(
    SecurityEvent event,
    List<String> patterns,
  ) async {
    try {
      await _createSecurityIncident(
        type: SecurityIncidentType.suspiciousActivity,
        severity: SecurityIncidentSeverity.medium,
        description:
            'Suspicious activity patterns detected: ${patterns.join(', ')}',
        affectedUser: event.userId,
        sourceIp: event.ipAddress,
        evidence: {
          'patterns': patterns,
          'risk_score': event.riskScore,
          'event_type': event.type,
          'event_details': event.details,
        },
        relatedEvents: [event.id],
      );
    } catch (e) {
      LoggerService.error('Failed to handle suspicious activity', e);
    }
  }

  /// Handle high-risk activity
  Future<void> _handleHighRiskActivity(SecurityEvent event) async {
    try {
      await _createSecurityIncident(
        type: SecurityIncidentType.highRiskActivity,
        severity: SecurityIncidentSeverity.high,
        description:
            'High-risk activity detected with score: ${event.riskScore}',
        affectedUser: event.userId,
        sourceIp: event.ipAddress,
        evidence: {
          'risk_score': event.riskScore,
          'event_type': event.type,
          'action': event.action,
          'resource': event.resource,
          'event_details': event.details,
        },
        relatedEvents: [event.id],
      );
    } catch (e) {
      LoggerService.error('Failed to handle high-risk activity', e);
    }
  }

  /// Handle anomalous activity
  Future<void> _handleAnomalousActivity(
    SecurityEvent event,
    List<String> anomalies,
  ) async {
    try {
      await _createSecurityIncident(
        type: SecurityIncidentType.anomalousActivity,
        severity: SecurityIncidentSeverity.medium,
        description: 'Anomalous activity detected: ${anomalies.join(', ')}',
        affectedUser: event.userId,
        sourceIp: event.ipAddress,
        evidence: {
          'anomalies': anomalies,
          'event_type': event.type,
          'timestamp': event.timestamp.toIso8601String(),
          'event_details': event.details,
        },
        relatedEvents: [event.id],
      );
    } catch (e) {
      LoggerService.error('Failed to handle anomalous activity', e);
    }
  }

  /// Create security incident
  Future<SecurityIncident> _createSecurityIncident({
    required SecurityIncidentType type,
    required SecurityIncidentSeverity severity,
    required String description,
    String? affectedUser,
    String? sourceIp,
    Map<String, dynamic>? evidence,
    List<String>? relatedEvents,
  }) async {
    try {
      final incidentId = SecurityUtils.generateSecureId();

      final incident = SecurityIncident(
        id: incidentId,
        type: type,
        severity: severity,
        status: SecurityIncidentStatus.open,
        description: description,
        detectedAt: DateTime.now(),
        affectedUser: affectedUser,
        sourceIp: sourceIp,
        evidence: evidence ?? {},
        relatedEvents: relatedEvents ?? [],
        responseActions: [],
        assignedTo: null,
        resolvedAt: null,
        resolution: null,
      );

      // Store incident
      await _pocketBaseService.createRecord(
        collectionName: 'security_incidents',
        data: incident.toJson(),
      );

      // Add to active incidents
      _activeIncidents[incidentId] = incident;

      // Log incident creation
      await _auditService.logSecurityEvent(
        SecurityEvent(
          id: SecurityUtils.generateSecureId(),
          type: 'security_incident_created',
          timestamp: DateTime.now(),
          success: true,
          userId: _getCurrentUserId(),
          resource: incidentId,
          action: 'create_incident',
          details: {
            'incident_type': type.value,
            'severity': severity.value,
            'affected_user': affectedUser,
            'source_ip': sourceIp,
          },
          severity: SecurityEventSeverity.high,
          riskScore: 70,
          requiresAttention: true,
          tags: ['incident', 'security', 'created'],
        ),
      );

      // Trigger immediate response for critical incidents
      if (severity == SecurityIncidentSeverity.critical) {
        await _triggerImmediateResponse(incident);
      }

      LoggerService.warning(
        'Security incident created: $incidentId - ${type.value} - ${severity.value}',
      );
      return incident;
    } catch (e) {
      LoggerService.error('Failed to create security incident', e);
      rethrow;
    }
  }

  /// Detect suspicious patterns
  Future<List<String>> _detectSuspiciousPatterns(SecurityEvent event) async {
    final patterns = <String>[];

    try {
      // Check for off-hours access
      final hour = event.timestamp.hour;
      if (hour < 6 || hour > 22) {
        patterns.add('off_hours_access');
      }

      // Check for unusual user agent
      final userAgent = event.details?['user_agent'] as String?;
      if (userAgent != null && _isSuspiciousUserAgent(userAgent)) {
        patterns.add('suspicious_user_agent');
      }

      // Check for rapid successive actions
      if (event.userId != null) {
        final recentEvents = await _getRecentUserEvents(
          event.userId!,
          Duration(minutes: 5),
        );
        if (recentEvents.length > 10) {
          patterns.add('rapid_successive_actions');
        }
      }

      // Check for unusual IP address patterns
      if (event.ipAddress != null &&
          !SecurityUtils.isValidIpAddress(event.ipAddress)) {
        patterns.add('invalid_ip_address');
      }

      return patterns;
    } catch (e) {
      LoggerService.warning('Failed to detect suspicious patterns: $e');
      return patterns;
    }
  }

  /// Detect anomalies
  Future<List<String>> _detectAnomalies(SecurityEvent event) async {
    final anomalies = <String>[];

    try {
      // Check for unusual resource access patterns
      if (event.resource != null && event.userId != null) {
        final userResourceHistory = await _getUserResourceHistory(
          event.userId!,
          event.resource!,
        );
        if (userResourceHistory.isEmpty) {
          anomalies.add('first_time_resource_access');
        }
      }

      // Check for unusual action patterns
      if (event.action != null && event.userId != null) {
        final userActionHistory = await _getUserActionHistory(
          event.userId!,
          event.action!,
        );
        if (userActionHistory.length < 3) {
          anomalies.add('unusual_action_for_user');
        }
      }

      // Check for geographic anomalies (simplified)
      if (event.ipAddress != null && event.userId != null) {
        final userIpHistory = await _getUserIpHistory(event.userId!);
        if (userIpHistory.isNotEmpty &&
            !userIpHistory.contains(event.ipAddress)) {
          anomalies.add('new_ip_address');
        }
      }

      return anomalies;
    } catch (e) {
      LoggerService.warning('Failed to detect anomalies: $e');
      return anomalies;
    }
  }

  /// Check if user agent is suspicious
  bool _isSuspiciousUserAgent(String userAgent) {
    final suspiciousPatterns = [
      'bot',
      'crawler',
      'spider',
      'scraper',
      'curl',
      'wget',
      'python',
      'java',
    ];

    final lowerUserAgent = userAgent.toLowerCase();
    return suspiciousPatterns.any(
      (pattern) => lowerUserAgent.contains(pattern),
    );
  }

  /// Get recent user events
  Future<List<Map<String, dynamic>>> _getRecentUserEvents(
    String userId,
    Duration timeWindow,
  ) async {
    try {
      final cutoff = DateTime.now().subtract(timeWindow);
      final records = await _pocketBaseService.getFullList(
        collectionName: 'security_events',
        filter:
            'user_id = "$userId" && timestamp >= "${cutoff.toIso8601String()}"',
      );
      return records.map((record) => record.data).toList();
    } catch (e) {
      LoggerService.warning('Failed to get recent user events: $e');
      return [];
    }
  }

  /// Get user resource history
  Future<List<Map<String, dynamic>>> _getUserResourceHistory(
    String userId,
    String resource,
  ) async {
    try {
      final records = await _pocketBaseService.getFullList(
        collectionName: 'security_events',
        filter: 'user_id = "$userId" && resource = "$resource"',
      );
      return records.map((record) => record.data).toList();
    } catch (e) {
      LoggerService.warning('Failed to get user resource history: $e');
      return [];
    }
  }

  /// Get user action history
  Future<List<Map<String, dynamic>>> _getUserActionHistory(
    String userId,
    String action,
  ) async {
    try {
      final records = await _pocketBaseService.getFullList(
        collectionName: 'security_events',
        filter: 'user_id = "$userId" && action = "$action"',
      );
      return records.map((record) => record.data).toList();
    } catch (e) {
      LoggerService.warning('Failed to get user action history: $e');
      return [];
    }
  }

  /// Get user IP history
  Future<List<String>> _getUserIpHistory(String userId) async {
    try {
      final records = await _pocketBaseService.getFullList(
        collectionName: 'security_events',
        filter: 'user_id = "$userId"',
      );

      final ipAddresses =
          records
              .map((record) => record.data['ip_address'] as String?)
              .where((ip) => ip != null)
              .cast<String>()
              .toSet()
              .toList();

      return ipAddresses;
    } catch (e) {
      LoggerService.warning('Failed to get user IP history: $e');
      return [];
    }
  }

  /// Trigger immediate response for critical incidents
  Future<void> _triggerImmediateResponse(SecurityIncident incident) async {
    try {
      LoggerService.warning(
        'Triggering immediate response for critical incident: ${incident.id}',
      );

      // Add immediate response actions
      final responseActions = <String>[
        'Incident escalated to security team',
        'Automated monitoring increased',
        'User account flagged for review',
      ];

      // Update incident with response actions
      await _updateIncident(incident.id, {
        'response_actions': responseActions,
        'escalated_at': DateTime.now().toIso8601String(),
        'auto_response_triggered': true,
      });

      // Log immediate response
      await _auditService.logSecurityEvent(
        SecurityEvent(
          id: SecurityUtils.generateSecureId(),
          type: 'security_incident_response',
          timestamp: DateTime.now(),
          success: true,
          userId: _getCurrentUserId(),
          resource: incident.id,
          action: 'immediate_response',
          details: {
            'incident_type': incident.type.value,
            'severity': incident.severity.value,
            'response_actions': responseActions,
          },
          severity: SecurityEventSeverity.high,
          riskScore: 80,
          requiresAttention: true,
          tags: ['incident', 'response', 'immediate'],
        ),
      );
    } catch (e) {
      LoggerService.error('Failed to trigger immediate response', e);
    }
  }

  /// Update security incident
  Future<void> _updateIncident(
    String incidentId,
    Map<String, dynamic> updates,
  ) async {
    try {
      await _pocketBaseService.updateRecord(
        collectionName: 'security_incidents',
        recordId: incidentId,
        data: updates,
      );

      // Update local cache
      if (_activeIncidents.containsKey(incidentId)) {
        // In a real implementation, you'd properly update the incident object
        LoggerService.debug('Updated incident in cache: $incidentId');
      }
    } catch (e) {
      LoggerService.error('Failed to update incident', e);
      rethrow;
    }
  }

  /// Start continuous monitoring
  void _startContinuousMonitoring() {
    _monitoringTimer = Timer.periodic(Duration(minutes: 5), (_) async {
      await _performPeriodicChecks();
    });
    LoggerService.info('Continuous security monitoring started');
  }

  /// Perform periodic security checks
  Future<void> _performPeriodicChecks() async {
    try {
      LoggerService.debug('Performing periodic security checks...');

      // Check for stale incidents
      await _checkStaleIncidents();

      // Clean up old failed attempt records
      _cleanupFailedAttempts();

      // Check system health
      await _checkSystemHealth();

      LoggerService.debug('Periodic security checks completed');
    } catch (e) {
      LoggerService.error('Failed to perform periodic checks', e);
    }
  }

  /// Check for stale incidents
  Future<void> _checkStaleIncidents() async {
    try {
      final cutoff = DateTime.now().subtract(_incidentResponseTime);
      final staleIncidents =
          _activeIncidents.values
              .where(
                (incident) =>
                    incident.status == SecurityIncidentStatus.open &&
                    incident.detectedAt.isBefore(cutoff),
              )
              .toList();

      for (final incident in staleIncidents) {
        LoggerService.warning('Stale incident detected: ${incident.id}');

        // Escalate stale incidents
        await _updateIncident(incident.id, {
          'status': SecurityIncidentStatus.escalated.value,
          'escalated_at': DateTime.now().toIso8601String(),
          'escalation_reason':
              'No response within ${_incidentResponseTime.inMinutes} minutes',
        });
      }
    } catch (e) {
      LoggerService.error('Failed to check stale incidents', e);
    }
  }

  /// Clean up old failed attempt records
  void _cleanupFailedAttempts() {
    final cutoff = DateTime.now().subtract(_failedAttemptWindow);

    _failedAttempts.forEach((key, attempts) {
      attempts.removeWhere((attempt) => attempt.isBefore(cutoff));
    });

    // Remove empty entries
    _failedAttempts.removeWhere((key, attempts) => attempts.isEmpty);
  }

  /// Check system health
  Future<void> _checkSystemHealth() async {
    try {
      // Check database connectivity
      await _pocketBaseService.getList(
        collectionName: 'security_events',
        perPage: 1,
      );

      // Check for unusual system activity
      final recentEvents = await _pocketBaseService.getFullList(
        collectionName: 'security_events',
        filter:
            'timestamp >= "${DateTime.now().subtract(Duration(hours: 1)).toIso8601String()}"',
      );

      if (recentEvents.length > 1000) {
        LoggerService.warning(
          'High volume of security events detected: ${recentEvents.length}',
        );
      }
    } catch (e) {
      LoggerService.error('System health check failed', e);
    }
  }

  /// Get current user ID
  String? _getCurrentUserId() {
    return _pocketBaseService.currentUser?.id;
  }

  /// Get active incidents
  List<SecurityIncident> getActiveIncidents() {
    return _activeIncidents.values.toList();
  }

  /// Get service status
  Map<String, dynamic> getStatus() {
    return {
      'service_initialized': true,
      'active_incidents': _activeIncidents.length,
      'monitoring_active': _monitoringTimer?.isActive ?? false,
      'failed_attempt_tracking': _failedAttempts.length,
      'max_failed_attempts': _maxFailedAttempts,
      'failed_attempt_window_minutes': _failedAttemptWindow.inMinutes,
      'suspicious_activity_threshold': _suspiciousActivityThreshold,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Dispose resources
  void dispose() {
    _monitoringTimer?.cancel();
    _activeIncidents.clear();
    _failedAttempts.clear();
    LoggerService.info('Security incident service disposed');
  }
}

/// Security incident model
class SecurityIncident {
  final String id;
  final SecurityIncidentType type;
  final SecurityIncidentSeverity severity;
  final SecurityIncidentStatus status;
  final String description;
  final DateTime detectedAt;
  final String? affectedUser;
  final String? sourceIp;
  final Map<String, dynamic> evidence;
  final List<String> relatedEvents;
  final List<String> responseActions;
  final String? assignedTo;
  final DateTime? resolvedAt;
  final String? resolution;

  SecurityIncident({
    required this.id,
    required this.type,
    required this.severity,
    required this.status,
    required this.description,
    required this.detectedAt,
    this.affectedUser,
    this.sourceIp,
    this.evidence = const {},
    this.relatedEvents = const [],
    this.responseActions = const [],
    this.assignedTo,
    this.resolvedAt,
    this.resolution,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.value,
      'severity': severity.value,
      'status': status.value,
      'description': description,
      'detected_at': detectedAt.toIso8601String(),
      'affected_user': affectedUser,
      'source_ip': sourceIp,
      'evidence': evidence,
      'related_events': relatedEvents,
      'response_actions': responseActions,
      'assigned_to': assignedTo,
      'resolved_at': resolvedAt?.toIso8601String(),
      'resolution': resolution,
    };
  }

  factory SecurityIncident.fromJson(Map<String, dynamic> json) {
    return SecurityIncident(
      id: json['id'] as String,
      type: SecurityIncidentTypeExtension.fromString(json['type'] as String),
      severity: SecurityIncidentSeverityExtension.fromString(
        json['severity'] as String,
      ),
      status: SecurityIncidentStatusExtension.fromString(
        json['status'] as String,
      ),
      description: json['description'] as String,
      detectedAt: DateTime.parse(json['detected_at'] as String),
      affectedUser: json['affected_user'] as String?,
      sourceIp: json['source_ip'] as String?,
      evidence: Map<String, dynamic>.from(json['evidence'] as Map? ?? {}),
      relatedEvents: List<String>.from(json['related_events'] as List? ?? []),
      responseActions: List<String>.from(
        json['response_actions'] as List? ?? [],
      ),
      assignedTo: json['assigned_to'] as String?,
      resolvedAt:
          json['resolved_at'] != null
              ? DateTime.parse(json['resolved_at'] as String)
              : null,
      resolution: json['resolution'] as String?,
    );
  }

  @override
  String toString() {
    return 'SecurityIncident{id: $id, type: $type, severity: $severity, status: $status}';
  }
}

/// Security incident types
enum SecurityIncidentType {
  bruteForceAttack,
  suspiciousActivity,
  highRiskActivity,
  anomalousActivity,
  dataExfiltration,
  unauthorizedAccess,
  systemCompromise,
}

/// Security incident severities
enum SecurityIncidentSeverity { low, medium, high, critical }

/// Security incident statuses
enum SecurityIncidentStatus { open, investigating, escalated, resolved, closed }

/// Extensions for enums
extension SecurityIncidentTypeExtension on SecurityIncidentType {
  String get value {
    switch (this) {
      case SecurityIncidentType.bruteForceAttack:
        return 'brute_force_attack';
      case SecurityIncidentType.suspiciousActivity:
        return 'suspicious_activity';
      case SecurityIncidentType.highRiskActivity:
        return 'high_risk_activity';
      case SecurityIncidentType.anomalousActivity:
        return 'anomalous_activity';
      case SecurityIncidentType.dataExfiltration:
        return 'data_exfiltration';
      case SecurityIncidentType.unauthorizedAccess:
        return 'unauthorized_access';
      case SecurityIncidentType.systemCompromise:
        return 'system_compromise';
    }
  }

  static SecurityIncidentType fromString(String value) {
    switch (value) {
      case 'brute_force_attack':
        return SecurityIncidentType.bruteForceAttack;
      case 'suspicious_activity':
        return SecurityIncidentType.suspiciousActivity;
      case 'high_risk_activity':
        return SecurityIncidentType.highRiskActivity;
      case 'anomalous_activity':
        return SecurityIncidentType.anomalousActivity;
      case 'data_exfiltration':
        return SecurityIncidentType.dataExfiltration;
      case 'unauthorized_access':
        return SecurityIncidentType.unauthorizedAccess;
      case 'system_compromise':
        return SecurityIncidentType.systemCompromise;
      default:
        throw ArgumentError('Unknown security incident type: $value');
    }
  }
}

extension SecurityIncidentSeverityExtension on SecurityIncidentSeverity {
  String get value {
    switch (this) {
      case SecurityIncidentSeverity.low:
        return 'low';
      case SecurityIncidentSeverity.medium:
        return 'medium';
      case SecurityIncidentSeverity.high:
        return 'high';
      case SecurityIncidentSeverity.critical:
        return 'critical';
    }
  }

  static SecurityIncidentSeverity fromString(String value) {
    switch (value) {
      case 'low':
        return SecurityIncidentSeverity.low;
      case 'medium':
        return SecurityIncidentSeverity.medium;
      case 'high':
        return SecurityIncidentSeverity.high;
      case 'critical':
        return SecurityIncidentSeverity.critical;
      default:
        throw ArgumentError('Unknown security incident severity: $value');
    }
  }
}

extension SecurityIncidentStatusExtension on SecurityIncidentStatus {
  String get value {
    switch (this) {
      case SecurityIncidentStatus.open:
        return 'open';
      case SecurityIncidentStatus.investigating:
        return 'investigating';
      case SecurityIncidentStatus.escalated:
        return 'escalated';
      case SecurityIncidentStatus.resolved:
        return 'resolved';
      case SecurityIncidentStatus.closed:
        return 'closed';
    }
  }

  static SecurityIncidentStatus fromString(String value) {
    switch (value) {
      case 'open':
        return SecurityIncidentStatus.open;
      case 'investigating':
        return SecurityIncidentStatus.investigating;
      case 'escalated':
        return SecurityIncidentStatus.escalated;
      case 'resolved':
        return SecurityIncidentStatus.resolved;
      case 'closed':
        return SecurityIncidentStatus.closed;
      default:
        throw ArgumentError('Unknown security incident status: $value');
    }
  }
}
