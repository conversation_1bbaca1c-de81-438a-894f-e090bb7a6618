import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';

/// Service to handle migration from the old notification system to the new enhanced system
class NotificationMigrationService {
  final PocketBaseService _pocketBaseService;

  NotificationMigrationService(this._pocketBaseService);

  /// Migrates existing global notifications to use the new read state system
  /// This should be run once during the transition period
  Future<void> migrateGlobalNotifications() async {
    try {
      LoggerService.info('Starting global notification migration...');

      // Get all global notifications (empty recipientId)
      final globalNotifications = await _pocketBaseService.getFullList(
        collectionName: 'notifications',
        filter: 'recipientId:length < 1',
      );

      LoggerService.info('Found ${globalNotifications.length} global notifications to migrate');

      // Get all users to create read states for
      final users = await _pocketBaseService.getFullList(
        collectionName: 'users',
      );

      LoggerService.info('Found ${users.length} users for migration');

      int migratedCount = 0;
      int errorCount = 0;

      for (final notification in globalNotifications) {
        final notificationId = notification.id;
        final isRead = notification.data['isRead'] ?? false;

        // If the notification was marked as read in the old system,
        // create read state records for all users
        if (isRead) {
          for (final user in users) {
            try {
              await _createReadStateIfNotExists(
                notificationId,
                user.id,
                isRead: true,
                readAt: DateTime.tryParse(notification.updated),
              );
              migratedCount++;
            } catch (e) {
              LoggerService.error(
                'Error creating read state for notification $notificationId, user ${user.id}',
                e,
              );
              errorCount++;
            }
          }

          // Reset the global notification's isRead to false
          // since individual read states now handle this
          try {
            await _pocketBaseService.updateRecord(
              collectionName: 'notifications',
              recordId: notificationId,
              data: {'isRead': false},
            );
          } catch (e) {
            LoggerService.error(
              'Error resetting isRead for global notification $notificationId',
              e,
            );
          }
        }
      }

      LoggerService.info(
        'Migration completed. Created $migratedCount read states with $errorCount errors.',
      );
    } catch (e) {
      LoggerService.error('Error during global notification migration', e);
      rethrow;
    }
  }

  /// Creates a read state record if it doesn't already exist
  Future<void> _createReadStateIfNotExists(
    String notificationId,
    String userId, {
    bool isRead = false,
    DateTime? readAt,
  }) async {
    try {
      // Check if read state already exists
      await _pocketBaseService.pb
          .collection('notification_read_states')
          .getFirstListItem(
            'notification_id = "$notificationId" && user_id = "$userId"',
          );
      
      // If we get here, the record already exists
      LoggerService.info(
        'Read state already exists for notification $notificationId, user $userId',
      );
    } catch (e) {
      // Record doesn't exist, create it
      try {
        await _pocketBaseService.createRecord(
          collectionName: 'notification_read_states',
          data: {
            'notification_id': notificationId,
            'user_id': userId,
            'is_read': isRead,
            'read_at': readAt?.toIso8601String(),
          },
        );
      } catch (createError) {
        LoggerService.error(
          'Error creating read state for notification $notificationId, user $userId',
          createError,
        );
        rethrow;
      }
    }
  }

  /// Validates the migration by checking consistency between old and new systems
  Future<MigrationValidationResult> validateMigration() async {
    try {
      LoggerService.info('Starting migration validation...');

      final issues = <String>[];
      int globalNotificationsChecked = 0;
      int userSpecificNotificationsChecked = 0;

      // Check global notifications
      final globalNotifications = await _pocketBaseService.getFullList(
        collectionName: 'notifications',
        filter: 'recipientId:length < 1',
      );

      for (final notification in globalNotifications) {
        globalNotificationsChecked++;
        
        // Global notifications should have isRead = false after migration
        if (notification.data['isRead'] == true) {
          issues.add(
            'Global notification ${notification.id} still has isRead = true',
          );
        }

        // Check if read states exist for users who should have them
        final readStates = await _pocketBaseService.getFullList(
          collectionName: 'notification_read_states',
          filter: 'notification_id = "${notification.id}"',
        );

        if (readStates.isEmpty) {
          // This is okay - it means no users have read this notification yet
          continue;
        }

        // Validate read state records
        for (final readState in readStates) {
          if (readState.data['notification_id'] != notification.id) {
            issues.add(
              'Read state ${readState.id} has incorrect notification_id',
            );
          }
        }
      }

      // Check user-specific notifications
      final userSpecificNotifications = await _pocketBaseService.getFullList(
        collectionName: 'notifications',
        filter: 'recipientId:length > 0',
      );

      for (final notification in userSpecificNotifications) {
        userSpecificNotificationsChecked++;
        
        // User-specific notifications should not have read states
        final readStates = await _pocketBaseService.getFullList(
          collectionName: 'notification_read_states',
          filter: 'notification_id = "${notification.id}"',
        );

        if (readStates.isNotEmpty) {
          issues.add(
            'User-specific notification ${notification.id} has read states (should not)',
          );
        }
      }

      final result = MigrationValidationResult(
        isValid: issues.isEmpty,
        issues: issues,
        globalNotificationsChecked: globalNotificationsChecked,
        userSpecificNotificationsChecked: userSpecificNotificationsChecked,
      );

      LoggerService.info(
        'Migration validation completed. Valid: ${result.isValid}, Issues: ${issues.length}',
      );

      return result;
    } catch (e) {
      LoggerService.error('Error during migration validation', e);
      rethrow;
    }
  }

  /// Cleans up orphaned read state records
  Future<void> cleanupOrphanedReadStates() async {
    try {
      LoggerService.info('Starting cleanup of orphaned read states...');

      final readStates = await _pocketBaseService.getFullList(
        collectionName: 'notification_read_states',
      );

      int deletedCount = 0;

      for (final readState in readStates) {
        final notificationId = readState.data['notification_id'];
        
        try {
          // Check if the notification still exists
          await _pocketBaseService.pb
              .collection('notifications')
              .getOne(notificationId);
        } catch (e) {
          // Notification doesn't exist, delete the read state
          try {
            await _pocketBaseService.deleteRecord(
              collectionName: 'notification_read_states',
              recordId: readState.id,
            );
            deletedCount++;
            LoggerService.info(
              'Deleted orphaned read state ${readState.id} for missing notification $notificationId',
            );
          } catch (deleteError) {
            LoggerService.error(
              'Error deleting orphaned read state ${readState.id}',
              deleteError,
            );
          }
        }
      }

      LoggerService.info('Cleanup completed. Deleted $deletedCount orphaned read states.');
    } catch (e) {
      LoggerService.error('Error during cleanup of orphaned read states', e);
      rethrow;
    }
  }

  /// Creates the notification_read_states collection if it doesn't exist
  Future<void> ensureReadStatesCollectionExists() async {
    try {
      // Try to access the collection
      await _pocketBaseService.pb.collection('notification_read_states').getList();
      LoggerService.info('notification_read_states collection already exists');
    } catch (e) {
      LoggerService.info('notification_read_states collection does not exist, needs to be created');
      // Collection doesn't exist - this should be created manually using the JSON schema
      throw Exception(
        'notification_read_states collection does not exist. '
        'Please create it using the provided JSON schema file.',
      );
    }
  }
}

/// Result of migration validation
class MigrationValidationResult {
  final bool isValid;
  final List<String> issues;
  final int globalNotificationsChecked;
  final int userSpecificNotificationsChecked;

  MigrationValidationResult({
    required this.isValid,
    required this.issues,
    required this.globalNotificationsChecked,
    required this.userSpecificNotificationsChecked,
  });

  @override
  String toString() {
    return 'MigrationValidationResult('
        'isValid: $isValid, '
        'issues: ${issues.length}, '
        'globalNotifications: $globalNotificationsChecked, '
        'userSpecificNotifications: $userSpecificNotificationsChecked'
        ')';
  }
}
