import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/enhanced_audit_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/utils/security_utils.dart';

/// Privacy protection service for GDPR compliance and data protection
class PrivacyProtectionService {
  static final PrivacyProtectionService _instance =
      PrivacyProtectionService._internal();
  factory PrivacyProtectionService() => _instance;
  PrivacyProtectionService._internal();

  final PocketBaseService _pocketBaseService = PocketBaseService();
  final EnhancedAuditService _auditService = EnhancedAuditService();

  // Data retention policies (in days)
  static const Map<String, int> _dataRetentionPolicies = {
    'audit_logs': 2555, // 7 years
    'security_events': 2555, // 7 years
    'user_sessions': 90, // 3 months
    'document_access_logs': 2555, // 7 years
    'user_data': 2555, // 7 years (unless user requests deletion)
    'temporary_files': 30, // 1 month
  };

  /// Initialize privacy protection service
  Future<void> initialize() async {
    try {
      LoggerService.info('Initializing privacy protection service...');

      // Schedule data retention cleanup
      _scheduleDataRetentionCleanup();

      LoggerService.info('Privacy protection service initialized successfully');
    } catch (e) {
      LoggerService.error('Failed to initialize privacy protection service', e);
      rethrow;
    }
  }

  /// Process data subject access request (GDPR Article 15)
  Future<Map<String, dynamic>> processDataSubjectAccessRequest({
    required String userId,
    String? requestId,
  }) async {
    try {
      LoggerService.info(
        'Processing data subject access request for user: $userId',
      );

      final requestIdGenerated = requestId ?? SecurityUtils.generateSecureId();

      // Log the request
      await _auditService.logDataProcessing(
        action: 'data_subject_access_request',
        dataType: 'personal_data',
        success: true,
        userId: userId,
        metadata: {'request_id': requestIdGenerated, 'request_type': 'access'},
      );

      // Collect all personal data for the user
      final personalData = await _collectUserPersonalData(userId);

      // Create response
      final response = {
        'request_id': requestIdGenerated,
        'user_id': userId,
        'request_type': 'access',
        'processed_at': DateTime.now().toIso8601String(),
        'data': personalData,
        'retention_info': _getDataRetentionInfo(),
        'processing_purposes': _getProcessingPurposes(),
        'data_sources': _getDataSources(),
        'third_party_sharing': _getThirdPartySharing(),
      };

      LoggerService.info('Data subject access request processed successfully');
      return response;
    } catch (e) {
      LoggerService.error('Failed to process data subject access request', e);

      await _auditService.logDataProcessing(
        action: 'data_subject_access_request',
        dataType: 'personal_data',
        success: false,
        userId: userId,
        errorMessage: e.toString(),
        metadata: {'request_id': requestId, 'request_type': 'access'},
      );

      rethrow;
    }
  }

  /// Process right to be forgotten request (GDPR Article 17)
  Future<Map<String, dynamic>> processRightToBeForgottenRequest({
    required String userId,
    String? requestId,
    bool forceDelete = false,
  }) async {
    try {
      LoggerService.info(
        'Processing right to be forgotten request for user: $userId',
      );

      final requestIdGenerated = requestId ?? SecurityUtils.generateSecureId();

      // Check if deletion is legally permissible
      if (!forceDelete && !_canDeleteUserData(userId)) {
        throw PrivacyException(
          'User data cannot be deleted due to legal obligations',
        );
      }

      // Log the request
      await _auditService.logDataProcessing(
        action: 'right_to_be_forgotten',
        dataType: 'personal_data',
        success: true,
        userId: userId,
        metadata: {
          'request_id': requestIdGenerated,
          'request_type': 'deletion',
          'force_delete': forceDelete,
        },
      );

      // Anonymize or delete user data
      final deletionResult = await _anonymizeUserData(userId);

      // Create response
      final response = {
        'request_id': requestIdGenerated,
        'user_id': userId,
        'request_type': 'deletion',
        'processed_at': DateTime.now().toIso8601String(),
        'deletion_result': deletionResult,
        'retained_data': _getRetainedDataInfo(userId),
      };

      LoggerService.info(
        'Right to be forgotten request processed successfully',
      );
      return response;
    } catch (e) {
      LoggerService.error('Failed to process right to be forgotten request', e);

      await _auditService.logDataProcessing(
        action: 'right_to_be_forgotten',
        dataType: 'personal_data',
        success: false,
        userId: userId,
        errorMessage: e.toString(),
        metadata: {'request_id': requestId, 'request_type': 'deletion'},
      );

      rethrow;
    }
  }

  /// Process data portability request (GDPR Article 20)
  Future<Map<String, dynamic>> processDataPortabilityRequest({
    required String userId,
    String? requestId,
    String format = 'json',
  }) async {
    try {
      LoggerService.info(
        'Processing data portability request for user: $userId',
      );

      final requestIdGenerated = requestId ?? SecurityUtils.generateSecureId();

      // Log the request
      await _auditService.logDataProcessing(
        action: 'data_portability_request',
        dataType: 'personal_data',
        success: true,
        userId: userId,
        metadata: {
          'request_id': requestIdGenerated,
          'request_type': 'portability',
          'format': format,
        },
      );

      // Collect portable data
      final portableData = await _collectPortableUserData(userId);

      // Format data according to request
      final formattedData = _formatDataForPortability(portableData, format);

      // Create response
      final response = {
        'request_id': requestIdGenerated,
        'user_id': userId,
        'request_type': 'portability',
        'processed_at': DateTime.now().toIso8601String(),
        'format': format,
        'data': formattedData,
        'data_categories': _getDataCategories(),
      };

      LoggerService.info('Data portability request processed successfully');
      return response;
    } catch (e) {
      LoggerService.error('Failed to process data portability request', e);

      await _auditService.logDataProcessing(
        action: 'data_portability_request',
        dataType: 'personal_data',
        success: false,
        userId: userId,
        errorMessage: e.toString(),
        metadata: {'request_id': requestId, 'request_type': 'portability'},
      );

      rethrow;
    }
  }

  /// Anonymize personal data
  Future<String> anonymizePersonalData(
    String data,
    DataAnonymizationLevel level,
  ) async {
    try {
      switch (level) {
        case DataAnonymizationLevel.basic:
          return _basicAnonymization(data);
        case DataAnonymizationLevel.advanced:
          return _advancedAnonymization(data);
        case DataAnonymizationLevel.complete:
          return _completeAnonymization(data);
      }
    } catch (e) {
      LoggerService.error('Failed to anonymize personal data', e);
      rethrow;
    }
  }

  /// Check data processing consent
  Future<bool> hasValidConsent({
    required String userId,
    required String processingPurpose,
  }) async {
    try {
      final records = await _pocketBaseService.getFullList(
        collectionName: 'user_consents',
        filter:
            'user_id = "$userId" && purpose = "$processingPurpose" && is_active = true',
      );

      if (records.isEmpty) return false;

      final consent = records.first.data;
      final expiryDate = consent['expires_at'] as String?;

      if (expiryDate != null) {
        final expiry = DateTime.parse(expiryDate);
        if (DateTime.now().isAfter(expiry)) {
          return false;
        }
      }

      return true;
    } catch (e) {
      LoggerService.error('Failed to check consent', e);
      return false;
    }
  }

  /// Record user consent
  Future<void> recordConsent({
    required String userId,
    required String processingPurpose,
    required bool granted,
    String? legalBasis,
    DateTime? expiryDate,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final consentData = {
        'user_id': userId,
        'purpose': processingPurpose,
        'granted': granted,
        'legal_basis': legalBasis ?? 'consent',
        'granted_at': DateTime.now().toIso8601String(),
        'expires_at': expiryDate?.toIso8601String(),
        'is_active': granted,
        'metadata': metadata ?? {},
      };

      await _pocketBaseService.createRecord(
        collectionName: 'user_consents',
        data: consentData,
      );

      // Log consent recording
      await _auditService.logDataProcessing(
        action: 'consent_recorded',
        dataType: 'consent',
        success: true,
        userId: userId,
        metadata: {
          'purpose': processingPurpose,
          'granted': granted,
          'legal_basis': legalBasis,
        },
      );

      LoggerService.info(
        'User consent recorded: $userId - $processingPurpose - $granted',
      );
    } catch (e) {
      LoggerService.error('Failed to record consent', e);
      rethrow;
    }
  }

  /// Withdraw user consent
  Future<void> withdrawConsent({
    required String userId,
    required String processingPurpose,
  }) async {
    try {
      // Deactivate existing consents
      final records = await _pocketBaseService.getFullList(
        collectionName: 'user_consents',
        filter:
            'user_id = "$userId" && purpose = "$processingPurpose" && is_active = true',
      );

      for (final record in records) {
        await _pocketBaseService.updateRecord(
          collectionName: 'user_consents',
          recordId: record.id,
          data: {
            'is_active': false,
            'withdrawn_at': DateTime.now().toIso8601String(),
          },
        );
      }

      // Log consent withdrawal
      await _auditService.logDataProcessing(
        action: 'consent_withdrawn',
        dataType: 'consent',
        success: true,
        userId: userId,
        metadata: {
          'purpose': processingPurpose,
          'withdrawn_consents': records.length,
        },
      );

      LoggerService.info(
        'User consent withdrawn: $userId - $processingPurpose',
      );
    } catch (e) {
      LoggerService.error('Failed to withdraw consent', e);
      rethrow;
    }
  }

  /// Collect all personal data for a user
  Future<Map<String, dynamic>> _collectUserPersonalData(String userId) async {
    final personalData = <String, dynamic>{};

    try {
      // User profile data
      final userRecords = await _pocketBaseService.getFullList(
        collectionName: 'users',
        filter: 'id = "$userId"',
      );
      if (userRecords.isNotEmpty) {
        personalData['profile'] = _sanitizeUserData(userRecords.first.data);
      }

      // Solicitor profile data
      final solicitorRecords = await _pocketBaseService.getFullList(
        collectionName: 'solicitor_profiles',
        filter: 'user_id = "$userId"',
      );
      if (solicitorRecords.isNotEmpty) {
        personalData['solicitor_profile'] = _sanitizeUserData(
          solicitorRecords.first.data,
        );
      }

      // Co-funder profile data
      final coFunderRecords = await _pocketBaseService.getFullList(
        collectionName: 'co_funder_profiles',
        filter: 'user_id = "$userId"',
      );
      if (coFunderRecords.isNotEmpty) {
        personalData['co_funder_profile'] = _sanitizeUserData(
          coFunderRecords.first.data,
        );
      }

      // Document access logs
      final accessLogs = await _pocketBaseService.getFullList(
        collectionName: 'document_access_logs',
        filter: 'user_id = "$userId"',
      );
      personalData['document_access_history'] =
          accessLogs.map((log) => _sanitizeLogData(log.data)).toList();

      // Audit logs
      final auditLogs = await _pocketBaseService.getFullList(
        collectionName: 'audit_logs',
        filter: 'user_id = "$userId"',
      );
      personalData['audit_history'] =
          auditLogs.map((log) => _sanitizeLogData(log.data)).toList();

      // Consent records
      final consents = await _pocketBaseService.getFullList(
        collectionName: 'user_consents',
        filter: 'user_id = "$userId"',
      );
      personalData['consents'] =
          consents.map((consent) => consent.data).toList();

      return personalData;
    } catch (e) {
      LoggerService.error('Failed to collect user personal data', e);
      rethrow;
    }
  }

  /// Collect portable user data
  Future<Map<String, dynamic>> _collectPortableUserData(String userId) async {
    // Similar to _collectUserPersonalData but only includes data that can be ported
    final portableData = await _collectUserPersonalData(userId);

    // Remove non-portable data (like audit logs, system-generated IDs, etc.)
    portableData.remove('audit_history');
    portableData.remove('document_access_history');

    return portableData;
  }

  /// Anonymize user data
  Future<Map<String, dynamic>> _anonymizeUserData(String userId) async {
    final result = <String, dynamic>{
      'anonymized_collections': [],
      'deleted_collections': [],
      'retained_collections': [],
    };

    try {
      // Collections that can be anonymized
      final anonymizableCollections = [
        'users',
        'solicitor_profiles',
        'co_funder_profiles',
      ];

      for (final collection in anonymizableCollections) {
        final records = await _pocketBaseService.getFullList(
          collectionName: collection,
          filter: 'user_id = "$userId"',
        );

        for (final record in records) {
          final anonymizedData = _anonymizeRecord(record.data);
          await _pocketBaseService.updateRecord(
            collectionName: collection,
            recordId: record.id,
            data: anonymizedData,
          );
        }

        if (records.isNotEmpty) {
          result['anonymized_collections'].add(collection);
        }
      }

      // Collections that must be retained for legal reasons
      final retainedCollections = ['audit_logs', 'document_access_logs'];
      result['retained_collections'] = retainedCollections;

      return result;
    } catch (e) {
      LoggerService.error('Failed to anonymize user data', e);
      rethrow;
    }
  }

  /// Anonymize a record
  Map<String, dynamic> _anonymizeRecord(Map<String, dynamic> data) {
    final anonymized = Map<String, dynamic>.from(data);

    // Fields to anonymize
    final fieldsToAnonymize = [
      'email',
      'name',
      'first_name',
      'last_name',
      'phone',
      'address',
      'company_name',
      'contact_person',
      'billing_address',
    ];

    for (final field in fieldsToAnonymize) {
      if (anonymized.containsKey(field)) {
        anonymized[field] = '[ANONYMIZED]';
      }
    }

    // Add anonymization metadata
    anonymized['anonymized_at'] = DateTime.now().toIso8601String();
    anonymized['anonymization_reason'] = 'right_to_be_forgotten';

    return anonymized;
  }

  /// Basic anonymization
  String _basicAnonymization(String data) {
    // Replace with asterisks, keeping first and last characters
    if (data.length <= 2) return '*' * data.length;
    return '${data[0]}${'*' * (data.length - 2)}${data[data.length - 1]}';
  }

  /// Advanced anonymization
  String _advancedAnonymization(String data) {
    // Hash the data
    return SecurityUtils.generateHash(data).substring(0, 16);
  }

  /// Complete anonymization
  String _completeAnonymization(String data) {
    return '[ANONYMIZED]';
  }

  /// Sanitize user data for export
  Map<String, dynamic> _sanitizeUserData(Map<String, dynamic> data) {
    final sanitized = Map<String, dynamic>.from(data);

    // Remove system fields
    sanitized.remove('password');
    sanitized.remove('passwordHash');
    sanitized.remove('tokenKey');
    sanitized.remove('verified');

    return sanitized;
  }

  /// Sanitize log data for export
  Map<String, dynamic> _sanitizeLogData(Map<String, dynamic> data) {
    final sanitized = Map<String, dynamic>.from(data);

    // Remove sensitive system information
    sanitized.remove('ip_address');
    sanitized.remove('user_agent');
    sanitized.remove('session_id');

    return sanitized;
  }

  /// Check if user data can be deleted
  bool _canDeleteUserData(String userId) {
    // In a real implementation, this would check for:
    // - Active legal proceedings
    // - Regulatory retention requirements
    // - Outstanding financial obligations
    // For now, we'll assume deletion is allowed
    return true;
  }

  /// Format data for portability
  dynamic _formatDataForPortability(Map<String, dynamic> data, String format) {
    switch (format.toLowerCase()) {
      case 'json':
        return data;
      case 'csv':
        // Convert to CSV format (simplified)
        return _convertToCSV(data);
      case 'xml':
        // Convert to XML format (simplified)
        return _convertToXML(data);
      default:
        return data;
    }
  }

  /// Convert data to CSV format
  String _convertToCSV(Map<String, dynamic> data) {
    // Simplified CSV conversion
    final buffer = StringBuffer();

    void processMap(Map<String, dynamic> map, String prefix) {
      map.forEach((key, value) {
        final fullKey = prefix.isEmpty ? key : '$prefix.$key';
        if (value is Map<String, dynamic>) {
          processMap(value, fullKey);
        } else if (value is List) {
          buffer.writeln('$fullKey,${value.join(';')}');
        } else {
          buffer.writeln('$fullKey,$value');
        }
      });
    }

    processMap(data, '');
    return buffer.toString();
  }

  /// Convert data to XML format
  String _convertToXML(Map<String, dynamic> data) {
    // Simplified XML conversion
    final buffer = StringBuffer();
    buffer.writeln('<?xml version="1.0" encoding="UTF-8"?>');
    buffer.writeln('<user_data>');

    void processMap(Map<String, dynamic> map, int indent) {
      final spaces = '  ' * indent;
      map.forEach((key, value) {
        if (value is Map<String, dynamic>) {
          buffer.writeln('$spaces<$key>');
          processMap(value, indent + 1);
          buffer.writeln('$spaces</$key>');
        } else if (value is List) {
          buffer.writeln('$spaces<$key>${value.join(', ')}</$key>');
        } else {
          buffer.writeln('$spaces<$key>$value</$key>');
        }
      });
    }

    processMap(data, 1);
    buffer.writeln('</user_data>');
    return buffer.toString();
  }

  /// Get data retention information
  Map<String, dynamic> _getDataRetentionInfo() {
    return {
      'retention_policies': _dataRetentionPolicies,
      'retention_basis': 'Legal obligation and legitimate business interests',
      'contact_for_questions': '<EMAIL>',
    };
  }

  /// Get processing purposes
  List<String> _getProcessingPurposes() {
    return [
      'Litigation funding platform operation',
      'User account management',
      'Document storage and management',
      'Communication between parties',
      'Legal compliance and audit',
      'Security and fraud prevention',
    ];
  }

  /// Get data sources
  List<String> _getDataSources() {
    return [
      'User registration and profile creation',
      'Document uploads and interactions',
      'System logs and audit trails',
      'Communication records',
      'Third-party integrations (Google Drive)',
    ];
  }

  /// Get third-party sharing information
  Map<String, dynamic> _getThirdPartySharing() {
    return {
      'google_drive': {
        'purpose': 'Document storage and management',
        'data_types': ['Documents', 'File metadata'],
        'legal_basis': 'Legitimate business interests',
      },
      'no_other_sharing':
          'We do not share personal data with other third parties',
    };
  }

  /// Get data categories
  List<String> _getDataCategories() {
    return [
      'Identity data (name, email)',
      'Contact data (phone, address)',
      'Professional data (company, role)',
      'Usage data (document access, system interactions)',
      'Technical data (IP address, browser information)',
    ];
  }

  /// Get retained data information
  Map<String, dynamic> _getRetainedDataInfo(String userId) {
    return {
      'retained_for_legal_compliance': [
        'Audit logs (7 years)',
        'Document access logs (7 years)',
        'Financial transaction records (7 years)',
      ],
      'anonymized_data': [
        'User profile (anonymized)',
        'System usage statistics (anonymized)',
      ],
      'contact_for_questions': '<EMAIL>',
    };
  }

  /// Schedule data retention cleanup
  void _scheduleDataRetentionCleanup() {
    // In a real implementation, this would schedule periodic cleanup tasks
    LoggerService.info('Data retention cleanup scheduled');
  }

  /// Get service status
  Map<String, dynamic> getStatus() {
    return {
      'service_initialized': true,
      'data_retention_policies': _dataRetentionPolicies.length,
      'gdpr_compliance_enabled': true,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// Data anonymization levels
enum DataAnonymizationLevel { basic, advanced, complete }

/// Custom exception for privacy protection errors
class PrivacyException implements Exception {
  final String message;

  PrivacyException(this.message);

  @override
  String toString() => 'PrivacyException: $message';
}
