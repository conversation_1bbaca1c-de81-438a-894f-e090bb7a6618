import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/local_notification_service.dart';

/// Firebase API service for handling push notifications and FCM token management
/// Integrates with PocketBase for token storage and LocalNotificationService for display
class FirebaseApiService {
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;
  static final PocketBaseService _pocketBaseService = PocketBaseService();
  static bool _isInitialized = false;
  static bool _hasPermission = false;
  static String? _currentToken;

  // Navigation key for routing (to be set by main app)
  static GlobalKey<NavigatorState>? navigatorKey;

  /// Initialize Firebase and notification services
  static Future<void> initNotifications() async {
    if (_isInitialized) {
      LoggerService.info('FirebaseApiService already initialized');
      return;
    }

    try {
      LoggerService.info('Initializing FirebaseApiService...');

      // Initialize local notification service first
      await LocalNotificationService.initialize();

      // Request notification permissions
      await _requestNotificationPermissions();

      if (_hasPermission) {
        // Set up message handlers
        await _setupMessageHandlers();

        // Get and store FCM token
        await _initializeFCMToken();

        // Set up token refresh listener
        _setupTokenRefreshListener();

        _isInitialized = true;
        LoggerService.info('FirebaseApiService initialized successfully');
      } else {
        LoggerService.warning(
          'Notification permissions denied - Firebase service partially initialized',
        );
      }
    } catch (e) {
      LoggerService.error('Error initializing FirebaseApiService', e);
      // Don't rethrow to prevent app crashes when Firebase is not available
    }
  }

  /// Request notification permissions from user
  static Future<void> _requestNotificationPermissions() async {
    try {
      LoggerService.info('Requesting notification permissions...');

      final NotificationSettings settings = await _firebaseMessaging
          .requestPermission(
            alert: true,
            announcement: false,
            badge: true,
            carPlay: false,
            criticalAlert: false,
            provisional: false,
            sound: true,
          );

      _hasPermission =
          settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional;

      LoggerService.info(
        'Notification permission status: ${settings.authorizationStatus}',
      );
      LoggerService.info('Notification permissions granted: $_hasPermission');
    } catch (e) {
      LoggerService.error('Error requesting notification permissions', e);
      _hasPermission = false;
    }
  }

  /// Set up Firebase message handlers for different app states
  static Future<void> _setupMessageHandlers() async {
    try {
      LoggerService.info('Setting up Firebase message handlers...');

      // Handle messages when app is in foreground
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle messages when app is opened from background
      FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);

      // Handle messages when app is opened from terminated state
      final RemoteMessage? initialMessage =
          await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        LoggerService.info('App opened from terminated state via notification');
        _handleBackgroundMessage(initialMessage);
      }

      // Set background message handler
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

      LoggerService.info('Firebase message handlers set up successfully');
    } catch (e) {
      LoggerService.error('Error setting up Firebase message handlers', e);
      rethrow;
    }
  }

  /// Initialize and store FCM token
  static Future<void> _initializeFCMToken() async {
    try {
      LoggerService.info('Initializing FCM token...');

      final String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        _currentToken = token;
        LoggerService.info('FCM token obtained: ${token.substring(0, 20)}...');
        await _storeFCMToken(token);
      } else {
        LoggerService.warning('Failed to obtain FCM token');
      }
    } catch (e) {
      LoggerService.error('Error initializing FCM token', e);
      rethrow;
    }
  }

  /// Set up token refresh listener
  static void _setupTokenRefreshListener() {
    try {
      _firebaseMessaging.onTokenRefresh.listen((String token) async {
        LoggerService.info('FCM token refreshed');
        _currentToken = token;
        await _storeFCMToken(token);
      });
      LoggerService.info('FCM token refresh listener set up');
    } catch (e) {
      LoggerService.error('Error setting up token refresh listener', e);
    }
  }

  /// Store FCM token in PocketBase user record
  static Future<void> _storeFCMToken(String? token) async {
    if (token == null || token.isEmpty) {
      LoggerService.warning('Cannot store empty FCM token');
      return;
    }

    try {
      // Check if user is authenticated
      if (!_pocketBaseService.isSignedIn) {
        LoggerService.warning(
          'User not authenticated - cannot store FCM token',
        );
        return;
      }

      final String? userId = _pocketBaseService.currentUser?.id;
      if (userId == null) {
        LoggerService.warning('User ID not available - cannot store FCM token');
        return;
      }

      LoggerService.info('Storing FCM token for user: $userId');

      // Update user record with FCM token
      await _pocketBaseService.updateRecord(
        collectionName: 'users',
        recordId: userId,
        data: {
          'fcm_token': token,
          'fcm_token_updated': DateTime.now().toIso8601String(),
        },
      );

      LoggerService.info('FCM token stored successfully');
    } catch (e) {
      LoggerService.error('Error storing FCM token', e);
      // Don't rethrow - token storage failure shouldn't break the app
    }
  }

  /// Refresh FCM token and update storage
  static Future<void> refreshToken() async {
    try {
      LoggerService.info('Refreshing FCM token...');

      await _firebaseMessaging.deleteToken();
      final String? newToken = await _firebaseMessaging.getToken();

      if (newToken != null) {
        _currentToken = newToken;
        await _storeFCMToken(newToken);
        LoggerService.info('FCM token refreshed successfully');
      } else {
        LoggerService.warning('Failed to refresh FCM token');
      }
    } catch (e) {
      LoggerService.error('Error refreshing FCM token', e);
      // Don't rethrow to prevent app crashes when Firebase is not available
    }
  }

  /// Clear FCM token from storage and Firebase
  static Future<void> clearToken() async {
    try {
      LoggerService.info('Clearing FCM token...');

      // Try to clear token from Firebase
      try {
        await _firebaseMessaging.deleteToken();
      } catch (e) {
        LoggerService.warning('Failed to delete FCM token from Firebase: $e');
      }

      // Clear token from PocketBase if user is authenticated
      if (_pocketBaseService.isSignedIn) {
        final String? userId = _pocketBaseService.currentUser?.id;
        if (userId != null) {
          await _pocketBaseService.updateRecord(
            collectionName: 'users',
            recordId: userId,
            data: {
              'fcm_token': null,
              'fcm_token_updated': DateTime.now().toIso8601String(),
            },
          );
        }
      }

      _currentToken = null;
      LoggerService.info('FCM token cleared successfully');
    } catch (e) {
      LoggerService.error('Error clearing FCM token', e);
      // Don't rethrow to prevent app crashes when Firebase is not available
    }
  }

  /// Handle messages received when app is in foreground
  static void _handleForegroundMessage(RemoteMessage message) {
    try {
      LoggerService.info('Received foreground message: ${message.messageId}');
      _logMessageDetails(message);

      // Show local notification for foreground messages
      _showLocalNotification(message);
    } catch (e) {
      LoggerService.error('Error handling foreground message', e);
    }
  }

  /// Handle messages when app is opened from background/terminated
  static void _handleBackgroundMessage(RemoteMessage message) {
    try {
      LoggerService.info('Received background message: ${message.messageId}');
      _logMessageDetails(message);

      // Handle navigation based on message data
      _handleNotificationNavigation(message);
    } catch (e) {
      LoggerService.error('Error handling background message', e);
    }
  }

  /// Handle navigation based on notification data
  static void _handleNotificationNavigation(RemoteMessage message) {
    try {
      final Map<String, dynamic> data = message.data;
      final String? type = data['type'];
      final String? claimId = data['claim_id'];
      final String? route = data['route'];

      LoggerService.info(
        'Handling notification navigation: type=$type, claimId=$claimId, route=$route',
      );

      if (navigatorKey?.currentContext == null) {
        LoggerService.warning('Navigator context not available for navigation');
        return;
      }

      // Navigate based on notification type
      switch (type) {
        case 'claim_update':
          if (claimId != null) {
            LoggerService.info('Navigating to claim details: $claimId');
            // TODO: Navigate to claim details page
            // navigatorKey!.currentState?.pushNamed('/claims/$claimId');
          } else {
            LoggerService.info('Navigating to claims list');
            // TODO: Navigate to claims list
            // navigatorKey!.currentState?.pushNamed('/claims');
          }
          break;

        case 'funding_opportunity':
          LoggerService.info('Navigating to funding opportunities');
          // TODO: Navigate to funding page
          // navigatorKey!.currentState?.pushNamed('/funding');
          break;

        case 'message':
          LoggerService.info('Navigating to messages');
          // TODO: Navigate to messages page
          // navigatorKey!.currentState?.pushNamed('/messages');
          break;

        case 'document_upload':
          if (claimId != null) {
            LoggerService.info('Navigating to documents for claim: $claimId');
            // TODO: Navigate to documents page
            // navigatorKey!.currentState?.pushNamed('/claims/$claimId/documents');
          }
          break;

        default:
          if (route != null && route.isNotEmpty) {
            LoggerService.info('Navigating to custom route: $route');
            // TODO: Navigate to custom route
            // navigatorKey!.currentState?.pushNamed(route);
          } else {
            LoggerService.info('Navigating to default dashboard');
            // TODO: Navigate to appropriate dashboard based on user type
            // navigatorKey!.currentState?.pushNamedAndRemoveUntil('/dashboard', (route) => false);
          }
          break;
      }
    } catch (e) {
      LoggerService.error('Error handling notification navigation', e);
    }
  }

  /// Show local notification for foreground FCM messages
  static void _showLocalNotification(RemoteMessage message) {
    try {
      final String title = message.notification?.title ?? 'New Notification';
      final String body =
          message.notification?.body ?? 'You have a new notification';
      final String? type = message.data['type'];

      // Generate unique notification ID
      final int notificationId =
          LocalNotificationService.generateNotificationId();

      // Create payload for navigation
      final String payload = LocalNotificationService.createNotificationPayload(
        type: type ?? 'default',
        id: message.data['claim_id'] ?? message.data['user_id'],
        route: message.data['route'],
        additionalData: message.data,
      );

      LoggerService.info('Showing local notification for FCM message: $title');

      // Show notification using appropriate channel based on type
      switch (type) {
        case 'claim_update':
          LocalNotificationService.showClaimNotification(
            id: notificationId,
            title: title,
            body: body,
            payload: payload,
          );
          break;

        case 'funding_opportunity':
          LocalNotificationService.showFundingNotification(
            id: notificationId,
            title: title,
            body: body,
            payload: payload,
          );
          break;

        case 'message':
          LocalNotificationService.showMessageNotification(
            id: notificationId,
            title: title,
            body: body,
            payload: payload,
          );
          break;

        default:
          LocalNotificationService.showNotification(
            id: notificationId,
            title: title,
            body: body,
            payload: payload,
          );
          break;
      }
    } catch (e) {
      LoggerService.error(
        'Error showing local notification for FCM message',
        e,
      );
    }
  }

  /// Log detailed message information for debugging
  static void _logMessageDetails(RemoteMessage message) {
    try {
      LoggerService.info('=== FCM Message Details ===');
      LoggerService.info('Message ID: ${message.messageId}');
      LoggerService.info('From: ${message.from}');
      LoggerService.info('Sent Time: ${message.sentTime}');
      LoggerService.info('TTL: ${message.ttl}');

      if (message.notification != null) {
        LoggerService.info(
          'Notification Title: ${message.notification!.title}',
        );
        LoggerService.info('Notification Body: ${message.notification!.body}');
        LoggerService.info(
          'Notification Android Channel: ${message.notification!.android?.channelId}',
        );
      }

      if (message.data.isNotEmpty) {
        LoggerService.info('Data: ${jsonEncode(message.data)}');
      }

      LoggerService.info('=== End FCM Message Details ===');
    } catch (e) {
      LoggerService.error('Error logging message details', e);
    }
  }

  // Service status and utility methods

  /// Check if Firebase service is initialized
  static bool isInitialized() => _isInitialized;

  /// Check if notification permissions are granted
  static bool hasPermission() => _hasPermission;

  /// Get current FCM token
  static String? getCurrentToken() => _currentToken;

  /// Check if FCM token is available
  static bool getTokenStatus() =>
      _currentToken != null && _currentToken!.isNotEmpty;

  /// Update notification settings
  static Future<void> updateNotificationSettings({
    bool? enableClaims,
    bool? enableFunding,
    bool? enableMessages,
  }) async {
    try {
      LoggerService.info('Updating notification settings');

      // TODO: Implement notification settings persistence
      // This could be stored in SharedPreferences or PocketBase user preferences

      LoggerService.info('Notification settings updated');
    } catch (e) {
      LoggerService.error('Error updating notification settings', e);
      rethrow;
    }
  }

  /// Get service health status
  static Map<String, dynamic> getServiceStatus() {
    return {
      'isInitialized': _isInitialized,
      'hasPermission': _hasPermission,
      'hasToken': getTokenStatus(),
      'tokenLength': _currentToken?.length ?? 0,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Validate FCM token format
  static bool validateToken(String? token) {
    if (token == null || token.isEmpty) return false;

    // Basic FCM token validation (tokens are typically 152+ characters)
    return token.length > 100 && token.contains(':');
  }

  /// Set navigator key for routing
  static void setNavigatorKey(GlobalKey<NavigatorState> key) {
    navigatorKey = key;
    LoggerService.info('Navigator key set for Firebase navigation');
  }

  /// Dispose of service resources
  static Future<void> dispose() async {
    try {
      LoggerService.info('Disposing FirebaseApiService...');

      // Clear token
      await clearToken();

      _isInitialized = false;
      _hasPermission = false;
      _currentToken = null;

      LoggerService.info('FirebaseApiService disposed successfully');
    } catch (e) {
      LoggerService.error('Error disposing FirebaseApiService', e);
      rethrow;
    }
  }
}

/// Background message handler - must be top-level function
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    LoggerService.info('Background message received: ${message.messageId}');
    LoggerService.info('Background message from: ${message.from}');

    if (message.notification != null) {
      LoggerService.info(
        'Background notification title: ${message.notification!.title}',
      );
      LoggerService.info(
        'Background notification body: ${message.notification!.body}',
      );
    }

    if (message.data.isNotEmpty) {
      LoggerService.info('Background message data: ${message.data}');
    }

    // Note: Background message handler should not perform UI operations
    // or show notifications - this is handled by the system
  } catch (e) {
    LoggerService.error('Error in background message handler', e);
  }
}
