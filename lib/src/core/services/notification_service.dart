import 'dart:async';
import 'package:flutter/material.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/local_notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/data/models/enhanced_notification_model.dart';

class NotificationService {
  final PocketBaseService _pocketBaseService;
  UnsubscribeFunc? _notificationSubscription;
  UnsubscribeFunc? _readStateSubscription;
  final ValueNotifier<List<EnhancedNotificationModel>> _notifications =
      ValueNotifier<List<EnhancedNotificationModel>>([]);

  NotificationService(this._pocketBaseService);

  ValueNotifier<List<EnhancedNotificationModel>> get notifications =>
      _notifications;

  Future<void> initialize() async {
    await _fetchInitialNotifications();
    await _subscribeToNotifications();
    await _subscribeToReadStates();
  }

  Future<void> _fetchInitialNotifications() async {
    if (!_pocketBaseService.pb.authStore.isValid) {
      LoggerService.info(
        'User not authenticated. Skipping notification fetch.',
      );
      return;
    }

    try {
      final String currentUserId = _pocketBaseService.pb.authStore.record!.id;

      // Fetch notifications
      final result = await _pocketBaseService.getFullList(
        collectionName: 'notifications',
        filter: '(recipientId ~ "$currentUserId" || recipientId:length < 1)',
        sort: '-created',
      );

      final fetchedNotifications = <EnhancedNotificationModel>[];

      for (final record in result) {
        final notification = await _buildEnhancedNotification(
          record,
          currentUserId,
        );
        fetchedNotifications.add(notification);
      }

      _notifications.value = fetchedNotifications;
      LoggerService.info(
        'Fetched ${fetchedNotifications.length} notifications with read states.',
      );
    } catch (e) {
      LoggerService.error('Error fetching initial notifications', e);
    }
  }

  Future<EnhancedNotificationModel> _buildEnhancedNotification(
    RecordModel record,
    String currentUserId,
  ) async {
    final recipientIds = record.data['recipientId'] as List<dynamic>?;
    final isGlobalNotification = recipientIds == null || recipientIds.isEmpty;

    bool isReadForUser;
    DateTime? readAt;

    if (isGlobalNotification) {
      // For global notifications, check user-specific read state
      final readState = await _getUserReadState(record.id, currentUserId);
      isReadForUser = readState?.isRead ?? false;
      readAt = readState?.readAt;
    } else {
      // For user-specific notifications, use the original isRead field
      isReadForUser = record.data['isRead'] ?? false;
      readAt =
          isReadForUser
              ? DateTime.tryParse(record.get<String>('updated'))
              : null;
    }

    return EnhancedNotificationModel(
      id: record.id,
      title: record.data['title'] ?? '',
      message: record.data['message'] ?? '',
      type: record.data['type'] ?? 'info',
      isRead: isReadForUser,
      recipientId: recipientIds?.cast<String>(),
      created: DateTime.tryParse(record.get<String>('created')),
      updated: DateTime.tryParse(record.get<String>('updated')),
      readAt: readAt,
      isGlobal: isGlobalNotification,
    );
  }

  Future<UserReadState?> _getUserReadState(
    String notificationId,
    String userId,
  ) async {
    try {
      final result = await _pocketBaseService.pb
          .collection('notification_read_states')
          .getFirstListItem(
            'notification_id = "$notificationId" && user_id = "$userId"',
          );

      return UserReadState(
        id: result.id,
        notificationId: notificationId,
        userId: userId,
        isRead: result.data['is_read'] ?? false,
        readAt:
            result.data['read_at'] != null
                ? DateTime.tryParse(result.data['read_at'])
                : null,
      );
    } catch (e) {
      // Record doesn't exist, return null (unread state)
      return null;
    }
  }

  Future<void> _subscribeToNotifications() async {
    if (!_pocketBaseService.pb.authStore.isValid) {
      LoggerService.info(
        'User not authenticated. Skipping notification subscription.',
      );
      return;
    }

    final String currentUserId = _pocketBaseService.pb.authStore.record!.id;

    try {
      _notificationSubscription = await _pocketBaseService.pb
          .collection('notifications')
          .subscribe(
            '*',
            (e) async {
              LoggerService.info(
                'Notification event: ${e.action} - Record: ${e.record?.id}',
              );

              if (e.record != null) {
                final recipientIds =
                    e.record!.data['recipientId'] as List<dynamic>?;
                final isForUser =
                    (recipientIds != null &&
                        recipientIds.contains(currentUserId)) ||
                    (recipientIds == null || recipientIds.isEmpty);

                if (isForUser) {
                  final notification = await _buildEnhancedNotification(
                    e.record!,
                    currentUserId,
                  );

                  final currentNotifications =
                      List<EnhancedNotificationModel>.from(
                        _notifications.value,
                      );

                  if (e.action == 'create') {
                    if (!currentNotifications.any(
                      (n) => n.id == notification.id,
                    )) {
                      currentNotifications.insert(0, notification);
                      if (!notification.isRead) {
                        _showToast(notification);
                      }
                    }
                  } else if (e.action == 'update') {
                    final index = currentNotifications.indexWhere(
                      (n) => n.id == notification.id,
                    );
                    if (index != -1) {
                      currentNotifications[index] = notification;
                      LoggerService.info(
                        'Updated notification ${notification.id} - isRead: ${notification.isRead}',
                      );
                    } else if (!notification.isRead) {
                      currentNotifications.insert(0, notification);
                      _showToast(notification);
                    }
                  } else if (e.action == 'delete') {
                    currentNotifications.removeWhere(
                      (n) => n.id == notification.id,
                    );
                  }

                  _notifications.value = currentNotifications;
                }
              }
            },
            filter:
                '(recipientId ~ "$currentUserId" || recipientId:length < 1)',
          );

      LoggerService.info(
        'Real-time notification subscription enabled successfully.',
      );
    } catch (e) {
      LoggerService.error('Error subscribing to notifications', e);
    }
  }

  Future<void> _subscribeToReadStates() async {
    if (!_pocketBaseService.pb.authStore.isValid) {
      LoggerService.info(
        'User not authenticated. Skipping read state subscription.',
      );
      return;
    }

    final String currentUserId = _pocketBaseService.pb.authStore.record!.id;

    try {
      _readStateSubscription = await _pocketBaseService.pb
          .collection('notification_read_states')
          .subscribe('*', (e) async {
            LoggerService.info(
              'Read state event: ${e.action} - Record: ${e.record?.id}',
            );

            if (e.record != null &&
                e.record!.data['user_id'] == currentUserId) {
              final notificationId = e.record!.data['notification_id'];
              final isRead = e.record!.data['is_read'] ?? false;

              // Update the corresponding notification in our list
              await _updateLocalNotificationReadState(notificationId, isRead);

              LoggerService.info(
                'Updated notification $notificationId read state to $isRead via real-time subscription',
              );
            }
          }, filter: 'user_id = "$currentUserId"');

      LoggerService.info(
        'Real-time read state subscription enabled successfully.',
      );
    } catch (e) {
      LoggerService.error('Error subscribing to read states', e);
    }
  }

  void _showToast(EnhancedNotificationModel notification) {
    try {
      // Generate consistent notification ID using notification record ID hash
      final int notificationId = _generateNotificationId(notification);

      // Create payload for navigation with enhanced data
      final String payload = LocalNotificationService.createNotificationPayload(
        type: notification.type,
        id: notification.id,
        route: _getRouteForNotificationType(notification.type),
        additionalData: {
          'title': notification.title,
          'message': notification.message,
          'created':
              notification.created?.toIso8601String() ??
              DateTime.now().toIso8601String(),
          'link': notification.link,
          'icon': notification.icon,
        },
      );

      LoggerService.info(
        'Showing local notification: ${notification.title} (Type: ${notification.type}, ID: $notificationId)',
      );

      // Map notification type to appropriate channel with expanded types
      switch (notification.type) {
        case 'claim_update':
        case 'claim_status_change':
        case 'claim_milestone':
          LocalNotificationService.showClaimNotification(
            id: notificationId,
            title: notification.title,
            body: notification.message,
            payload: payload,
          );
          break;
        case 'funding_opportunity':
        case 'funding_application_status':
        case 'investment_opportunity':
          LocalNotificationService.showFundingNotification(
            id: notificationId,
            title: notification.title,
            body: notification.message,
            payload: payload,
          );
          break;
        case 'message':
        case 'agent_message':
        case 'communication':
          LocalNotificationService.showMessageNotification(
            id: notificationId,
            title: notification.title,
            body: notification.message,
            payload: payload,
          );
          break;
        case 'document_upload':
        case 'document_approval':
        case 'document_expiration':
        case 'profile_change':
        case 'permission_change':
        case 'security_alert':
          LocalNotificationService.showNotification(
            id: notificationId,
            title: notification.title,
            body: notification.message,
            payload: payload,
            channelId: LocalNotificationService.DEFAULT_CHANNEL_ID,
          );
          break;
        default:
          LocalNotificationService.showNotification(
            id: notificationId,
            title: notification.title,
            body: notification.message,
            payload: payload,
          );
          break;
      }

      // Log notification event for audit purposes
      _logNotificationEvent(notification, notificationId);
    } catch (e) {
      LoggerService.error('Error showing local notification', e);
      // Fallback to just logging
      LoggerService.info('New notification: ${notification.title}');
    }
  }

  /// Generate consistent notification ID using notification record ID hash
  int _generateNotificationId(EnhancedNotificationModel notification) {
    // Use notification ID hash combined with type for uniqueness
    final String combinedId = '${notification.id}_${notification.type}';
    return combinedId.hashCode.abs();
  }

  /// Get route for notification type to enable proper navigation
  String _getRouteForNotificationType(String type) {
    switch (type) {
      case 'claim_update':
      case 'claim_status_change':
      case 'claim_milestone':
        return '/claims';
      case 'funding_opportunity':
      case 'funding_application_status':
      case 'investment_opportunity':
        return '/funding';
      case 'message':
      case 'agent_message':
      case 'communication':
        return '/messages';
      case 'document_upload':
      case 'document_approval':
      case 'document_expiration':
        return '/documents';
      case 'profile_change':
      case 'permission_change':
        return '/profile';
      case 'security_alert':
        return '/security';
      default:
        return '/notifications';
    }
  }

  /// Log notification event for audit purposes
  void _logNotificationEvent(
    EnhancedNotificationModel notification,
    int localNotificationId,
  ) {
    try {
      LoggerService.info(
        'Notification Event - ID: ${notification.id}, '
        'Type: ${notification.type}, '
        'LocalID: $localNotificationId, '
        'Title: ${notification.title}, '
        'Created: ${notification.created}',
      );
    } catch (e) {
      LoggerService.error('Error logging notification event', e);
    }
  }

  Future<void> markAsRead(String notificationId) async {
    try {
      final String currentUserId = _pocketBaseService.pb.authStore.record!.id;

      // First, fetch the notification directly from PocketBase to determine if it's global
      final notificationRecord = await _pocketBaseService.pb
          .collection('notifications')
          .getOne(notificationId);

      final recipientIds =
          notificationRecord.data['recipientId'] as List<dynamic>?;
      final isGlobalNotification = recipientIds == null || recipientIds.isEmpty;

      if (isGlobalNotification) {
        // For global notifications, create/update read state record
        await _createOrUpdateReadState(notificationId, currentUserId);
      } else {
        // For user-specific notifications, update the notification directly
        await _pocketBaseService.updateRecord(
          collectionName: 'notifications',
          recordId: notificationId,
          data: {'isRead': true},
        );
      }

      LoggerService.info(
        'Notification $notificationId marked as read (global: $isGlobalNotification).',
      );
    } catch (e) {
      LoggerService.error(
        'Error marking notification $notificationId as read',
        e,
      );
      rethrow;
    }
  }

  Future<void> _createOrUpdateReadState(
    String notificationId,
    String userId,
  ) async {
    try {
      // Try to find existing read state
      final existingReadState = await _getUserReadState(notificationId, userId);

      final data = {
        'is_read': true,
        'read_at': DateTime.now().toIso8601String(),
      };

      if (existingReadState != null) {
        // Update existing record
        LoggerService.info(
          'Updating existing read state for notification $notificationId',
        );
        await _pocketBaseService.updateRecord(
          collectionName: 'notification_read_states',
          recordId: existingReadState.id,
          data: data,
        );
      } else {
        // Create new record
        LoggerService.info(
          'Creating new read state for notification $notificationId',
        );
        await _pocketBaseService.createRecord(
          collectionName: 'notification_read_states',
          data: {'notification_id': notificationId, 'user_id': userId, ...data},
        );
      }

      // Manually update the local notification list to reflect the change immediately
      await _updateLocalNotificationReadState(notificationId, true);
    } catch (e) {
      LoggerService.error('Error creating/updating read state', e);
      rethrow;
    }
  }

  /// Manually update the local notification list to reflect read state changes
  Future<void> _updateLocalNotificationReadState(
    String notificationId,
    bool isRead,
  ) async {
    final currentNotifications = List<EnhancedNotificationModel>.from(
      _notifications.value,
    );
    final index = currentNotifications.indexWhere(
      (n) => n.id == notificationId,
    );

    if (index != -1) {
      final updatedNotification = currentNotifications[index].copyWith(
        isRead: isRead,
        readAt: isRead ? DateTime.now() : null,
      );
      currentNotifications[index] = updatedNotification;
      _notifications.value = currentNotifications;
      LoggerService.info(
        'Updated local notification $notificationId read state to $isRead',
      );
    } else {
      LoggerService.warning(
        'Notification $notificationId not found in local list for read state update',
      );
    }
  }

  Future<int> getUnreadCount() async {
    final unreadNotifications =
        _notifications.value.where((n) => !n.isRead).toList();
    return unreadNotifications.length;
  }

  void dispose() {
    _notificationSubscription?.call();
    _readStateSubscription?.call();
    _notifications.dispose();
    LoggerService.info('EnhancedNotificationService disposed.');
  }
}

class UserReadState {
  final String id;
  final String notificationId;
  final String userId;
  final bool isRead;
  final DateTime? readAt;

  UserReadState({
    required this.id,
    required this.notificationId,
    required this.userId,
    required this.isRead,
    this.readAt,
  });
}
