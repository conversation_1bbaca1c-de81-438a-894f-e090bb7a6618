import 'dart:async';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/audit_log_entry.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/security_event.dart';
import 'package:three_pay_group_litigation_platform/src/core/utils/security_utils.dart';

/// Enhanced audit service for comprehensive logging and compliance
class EnhancedAuditService {
  static final EnhancedAuditService _instance =
      EnhancedAuditService._internal();
  factory EnhancedAuditService() => _instance;
  EnhancedAuditService._internal();

  final PocketBaseService _pocketBaseService = PocketBaseService();

  // Audit configuration
  static const int _maxBatchSize = 100;
  static const Duration _batchFlushInterval = Duration(seconds: 30);

  // Batch processing
  final List<AuditLogEntry> _auditBatch = [];
  final List<SecurityEvent> _securityEventBatch = [];

  // Digital signature key (in production, use proper key management)
  static const String _auditSignatureKey =
      'audit-signature-key-change-in-production';

  /// Initialize the audit service
  Future<void> initialize() async {
    try {
      LoggerService.info('Initializing enhanced audit service...');

      // Start batch processing timer
      _startBatchProcessing();

      LoggerService.info('Enhanced audit service initialized successfully');
    } catch (e) {
      LoggerService.error('Failed to initialize enhanced audit service', e);
      rethrow;
    }
  }

  /// Log document access with comprehensive details
  Future<void> logDocumentAccess({
    required String documentId,
    required String action,
    required bool success,
    String? userId,
    String? userEmail,
    String? userRole,
    String? ipAddress,
    String? userAgent,
    String? sessionId,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final auditId = SecurityUtils.generateSecureId();

      final auditEntry = AuditLogEntry.documentAccess(
        id: auditId,
        userId: userId ?? _getCurrentUserId() ?? 'anonymous',
        action: action,
        documentId: documentId,
        success: success,
        userEmail: userEmail ?? _getCurrentUserEmail(),
        userRole: userRole ?? _getCurrentUserRole(),
        errorMessage: errorMessage,
        ipAddress: ipAddress,
        metadata: {
          ...metadata ?? {},
          'user_agent': userAgent,
          'session_id': sessionId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // Add digital signature
      final signedEntry = _addDigitalSignature(auditEntry);

      // Add to batch for processing
      _auditBatch.add(signedEntry);

      // Flush batch if it's full
      if (_auditBatch.length >= _maxBatchSize) {
        await _flushAuditBatch();
      }

      LoggerService.debug('Document access logged: $documentId - $action');
    } catch (e) {
      LoggerService.error('Failed to log document access', e);
      // Don't fail the operation if audit logging fails
    }
  }

  /// Log authentication events
  Future<void> logAuthentication({
    required String userId,
    required bool success,
    String? userEmail,
    String? errorMessage,
    String? ipAddress,
    String? userAgent,
    String? sessionId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final auditId = SecurityUtils.generateSecureId();

      final auditEntry = AuditLogEntry.authentication(
        id: auditId,
        userId: userId,
        success: success,
        userEmail: userEmail,
        errorMessage: errorMessage,
        ipAddress: ipAddress,
        userAgent: userAgent,
        sessionId: sessionId,
        metadata: metadata,
      );

      // Add digital signature
      final signedEntry = _addDigitalSignature(auditEntry);

      // Add to batch
      _auditBatch.add(signedEntry);

      // Also create security event for authentication
      final securityEvent = SecurityEvent.authentication(
        id: SecurityUtils.generateSecureId(),
        success: success,
        userId: userId,
        ipAddress: ipAddress,
        userAgent: userAgent,
        errorMessage: errorMessage,
        details: metadata,
      );

      _securityEventBatch.add(securityEvent);

      LoggerService.debug(
        'Authentication logged: $userId - ${success ? 'success' : 'failure'}',
      );
    } catch (e) {
      LoggerService.error('Failed to log authentication', e);
    }
  }

  /// Log permission changes
  Future<void> logPermissionChange({
    required String resourceId,
    required Map<String, dynamic> changes,
    required bool success,
    String? userId,
    String? userEmail,
    String? userRole,
    String? errorMessage,
    String? ipAddress,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final auditId = SecurityUtils.generateSecureId();

      final auditEntry = AuditLogEntry.permissionChange(
        id: auditId,
        userId: userId ?? _getCurrentUserId() ?? 'system',
        resourceId: resourceId,
        changes: changes,
        success: success,
        userEmail: userEmail ?? _getCurrentUserEmail(),
        userRole: userRole ?? _getCurrentUserRole(),
        errorMessage: errorMessage,
        ipAddress: ipAddress,
        metadata: metadata,
      );

      // Add digital signature
      final signedEntry = _addDigitalSignature(auditEntry);

      // Add to batch
      _auditBatch.add(signedEntry);

      // Create security event for permission change
      final securityEvent = SecurityEvent.permissionChange(
        id: SecurityUtils.generateSecureId(),
        success: success,
        userId: userId ?? _getCurrentUserId() ?? 'system',
        resource: resourceId,
        ipAddress: ipAddress,
        errorMessage: errorMessage,
        details: {'changes': changes, ...metadata ?? {}},
      );

      _securityEventBatch.add(securityEvent);

      LoggerService.debug('Permission change logged: $resourceId');
    } catch (e) {
      LoggerService.error('Failed to log permission change', e);
    }
  }

  /// Log data processing activities for GDPR compliance
  Future<void> logDataProcessing({
    required String action,
    required String dataType,
    required bool success,
    String? userId,
    String? userEmail,
    String? userRole,
    String? errorMessage,
    String? ipAddress,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final auditId = SecurityUtils.generateSecureId();

      final auditEntry = AuditLogEntry.dataProcessing(
        id: auditId,
        userId: userId ?? _getCurrentUserId() ?? 'system',
        action: action,
        dataType: dataType,
        success: success,
        userEmail: userEmail ?? _getCurrentUserEmail(),
        userRole: userRole ?? _getCurrentUserRole(),
        errorMessage: errorMessage,
        ipAddress: ipAddress,
        metadata: metadata,
      );

      // Add digital signature
      final signedEntry = _addDigitalSignature(auditEntry);

      // Add to batch
      _auditBatch.add(signedEntry);

      LoggerService.debug('Data processing logged: $action - $dataType');
    } catch (e) {
      LoggerService.error('Failed to log data processing', e);
    }
  }

  /// Log security events
  Future<void> logSecurityEvent(SecurityEvent event) async {
    try {
      _securityEventBatch.add(event);

      // Flush immediately for critical events
      if (event.severity == SecurityEventSeverity.critical) {
        await _flushSecurityEventBatch();
      }

      LoggerService.debug(
        'Security event logged: ${event.type} - ${event.severity.value}',
      );
    } catch (e) {
      LoggerService.error('Failed to log security event', e);
    }
  }

  /// Generate compliance report
  Future<Map<String, dynamic>> generateComplianceReport({
    required DateTime startDate,
    required DateTime endDate,
    List<String>? complianceTags,
    String? userId,
  }) async {
    try {
      LoggerService.info('Generating compliance report...');

      // Build filter for audit logs
      String filter =
          'timestamp >= "${startDate.toIso8601String()}" && timestamp <= "${endDate.toIso8601String()}"';

      if (userId != null) {
        filter += ' && user_id = "$userId"';
      }

      // Get audit logs
      final auditLogs = await _pocketBaseService.getFullList(
        collectionName: 'audit_logs',
        filter: filter,
      );

      // Get security events
      final securityEvents = await _pocketBaseService.getFullList(
        collectionName: 'security_events',
        filter: filter,
      );

      // Process data for report
      final report = {
        'report_id': SecurityUtils.generateSecureId(),
        'generated_at': DateTime.now().toIso8601String(),
        'period': {
          'start_date': startDate.toIso8601String(),
          'end_date': endDate.toIso8601String(),
        },
        'summary': {
          'total_audit_entries': auditLogs.length,
          'total_security_events': securityEvents.length,
          'successful_operations':
              auditLogs.where((log) => log.data['success'] == true).length,
          'failed_operations':
              auditLogs.where((log) => log.data['success'] == false).length,
          'high_risk_events':
              securityEvents
                  .where(
                    (event) => (event.data['risk_score'] as int? ?? 0) >= 70,
                  )
                  .length,
        },
        'compliance_tags':
            complianceTags ?? ['GDPR', 'security', 'document_access'],
        'audit_entries': auditLogs.map((log) => log.data).toList(),
        'security_events': securityEvents.map((event) => event.data).toList(),
      };

      LoggerService.info('Compliance report generated successfully');
      return report;
    } catch (e) {
      LoggerService.error('Failed to generate compliance report', e);
      rethrow;
    }
  }

  /// Verify audit log integrity
  Future<bool> verifyAuditIntegrity({required String auditId}) async {
    try {
      final records = await _pocketBaseService.getFullList(
        collectionName: 'audit_logs',
        filter: 'id = "$auditId"',
      );

      if (records.isEmpty) {
        LoggerService.warning('Audit log not found: $auditId');
        return false;
      }

      final auditData = records.first.data;
      final digitalSignature = auditData['digital_signature'] as String?;

      if (digitalSignature == null) {
        LoggerService.warning(
          'No digital signature found for audit log: $auditId',
        );
        return false;
      }

      // Remove signature from data for verification
      final dataForVerification = Map<String, dynamic>.from(auditData);
      dataForVerification.remove('digital_signature');

      // Verify signature
      final isValid = SecurityUtils.verifyDigitalSignature(
        dataForVerification,
        digitalSignature,
        _auditSignatureKey,
      );

      if (!isValid) {
        LoggerService.warning(
          'Digital signature verification failed for audit log: $auditId',
        );
      }

      return isValid;
    } catch (e) {
      LoggerService.error('Failed to verify audit integrity', e);
      return false;
    }
  }

  /// Add digital signature to audit entry
  AuditLogEntry _addDigitalSignature(AuditLogEntry entry) {
    try {
      final entryData = entry.toJson();
      entryData.remove('digital_signature'); // Remove if exists

      final signature = SecurityUtils.generateDigitalSignature(
        entryData,
        _auditSignatureKey,
      );

      // Create new entry with signature in metadata
      final metadata = Map<String, dynamic>.from(entry.metadata ?? {});
      metadata['digital_signature'] = signature;

      return AuditLogEntry(
        id: entry.id,
        timestamp: entry.timestamp,
        userId: entry.userId,
        userEmail: entry.userEmail,
        userRole: entry.userRole,
        action: entry.action,
        resource: entry.resource,
        resourceType: entry.resourceType,
        resourceId: entry.resourceId,
        success: entry.success,
        errorMessage: entry.errorMessage,
        ipAddress: entry.ipAddress,
        userAgent: entry.userAgent,
        sessionId: entry.sessionId,
        metadata: metadata,
        changes: entry.changes,
        riskLevel: entry.riskLevel,
        complianceTags: entry.complianceTags,
        dataClassification: entry.dataClassification,
        retentionDays: entry.retentionDays,
        isImmutable: entry.isImmutable,
        digitalSignature: signature,
      );
    } catch (e) {
      LoggerService.warning(
        'Failed to add digital signature to audit entry: $e',
      );
      return entry; // Return original entry if signing fails
    }
  }

  /// Start batch processing timer
  void _startBatchProcessing() {
    Timer.periodic(_batchFlushInterval, (_) async {
      await _flushAuditBatch();
      await _flushSecurityEventBatch();
    });
  }

  /// Flush audit batch to database
  Future<void> _flushAuditBatch() async {
    if (_auditBatch.isEmpty) return;

    try {
      final batch = List<AuditLogEntry>.from(_auditBatch);
      _auditBatch.clear();

      for (final entry in batch) {
        await _pocketBaseService.createRecord(
          collectionName: 'audit_logs',
          data: entry.toJson(),
        );
      }

      LoggerService.debug('Flushed ${batch.length} audit entries');
    } catch (e) {
      LoggerService.error('Failed to flush audit batch', e);
      // Re-add entries to batch for retry
      _auditBatch.addAll(_auditBatch);
    }
  }

  /// Flush security event batch to database
  Future<void> _flushSecurityEventBatch() async {
    if (_securityEventBatch.isEmpty) return;

    final batch = List<SecurityEvent>.from(_securityEventBatch);
    _securityEventBatch.clear();

    try {
      for (final event in batch) {
        await _pocketBaseService.createRecord(
          collectionName: 'security_events',
          data: event.toJson(),
        );
      }

      LoggerService.debug('Flushed ${batch.length} security events');
    } catch (e) {
      LoggerService.error('Failed to flush security event batch', e);
      // Re-add events to batch for retry
      _securityEventBatch.addAll(batch);
    }
  }

  /// Get current user ID
  String? _getCurrentUserId() {
    return _pocketBaseService.currentUser?.id;
  }

  /// Get current user email
  String? _getCurrentUserEmail() {
    return _pocketBaseService.currentUser?.data['email'] as String?;
  }

  /// Get current user role
  String? _getCurrentUserRole() {
    // This would need to be implemented based on your user role system
    return _pocketBaseService.currentUser?.data['role'] as String?;
  }

  /// Get service status
  Map<String, dynamic> getStatus() {
    return {
      'service_initialized': true,
      'pending_audit_entries': _auditBatch.length,
      'pending_security_events': _securityEventBatch.length,
      'batch_size_limit': _maxBatchSize,
      'flush_interval_seconds': _batchFlushInterval.inSeconds,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Dispose resources
  void dispose() {
    // Flush remaining entries
    _flushAuditBatch();
    _flushSecurityEventBatch();

    LoggerService.info('Enhanced audit service disposed');
  }
}
