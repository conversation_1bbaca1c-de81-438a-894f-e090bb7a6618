import 'dart:convert';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/encryption_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/security_event.dart';
import 'package:three_pay_group_litigation_platform/src/core/utils/security_utils.dart';

/// Secure credential management service
class CredentialManagementService {
  static final CredentialManagementService _instance =
      CredentialManagementService._internal();
  factory CredentialManagementService() => _instance;
  CredentialManagementService._internal();

  final EncryptionService _encryptionService = EncryptionService();
  final PocketBaseService _pocketBaseService = PocketBaseService();

  // Credential cache
  final Map<String, SecureCredential> _credentialCache = {};

  // Master encryption key (in production, this should be from secure key management)
  static const String _masterKey =
      'your-master-encryption-key-here-change-in-production';

  /// Store credentials securely
  Future<String> storeCredentials({
    required String credentialId,
    required Map<String, dynamic> credentials,
    required CredentialType type,
    String? description,
    Duration? expiryDuration,
    List<String> allowedUsers = const [],
  }) async {
    try {
      LoggerService.info('Storing credentials: $credentialId');

      // Validate credentials
      _validateCredentials(credentials, type);

      // Encrypt credentials
      final encryptionResult = await _encryptionService.encryptData(
        jsonEncode(credentials),
        key: _masterKey,
        metadata: {
          'credential_id': credentialId,
          'type': type.value,
          'description': description,
          'allowed_users': allowedUsers,
        },
      );

      // Calculate expiry
      final expiryDate =
          expiryDuration != null ? DateTime.now().add(expiryDuration) : null;

      // Create secure credential
      final secureCredential = SecureCredential(
        id: credentialId,
        type: type,
        encryptedData: encryptionResult.encryptedData,
        iv: encryptionResult.iv,
        salt: encryptionResult.salt,
        checksum: encryptionResult.checksum,
        keyId: encryptionResult.keyId,
        description: description,
        allowedUsers: allowedUsers,
        createdAt: DateTime.now(),
        expiresAt: expiryDate,
        lastAccessedAt: null,
        accessCount: 0,
        isActive: true,
      );

      // Store in PocketBase
      await _pocketBaseService.createRecord(
        collectionName: 'secure_credentials',
        data: secureCredential.toJson(),
      );

      // Cache credential
      _credentialCache[credentialId] = secureCredential;

      // Log credential storage
      await _logCredentialEvent(
        action: 'store',
        credentialId: credentialId,
        success: true,
        details: {
          'type': type.value,
          'has_expiry': expiryDate != null,
          'allowed_users_count': allowedUsers.length,
        },
      );

      LoggerService.info('Credentials stored successfully: $credentialId');
      return credentialId;
    } catch (e) {
      LoggerService.error('Failed to store credentials: $credentialId', e);

      await _logCredentialEvent(
        action: 'store',
        credentialId: credentialId,
        success: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// Retrieve credentials securely
  Future<Map<String, dynamic>> retrieveCredentials(String credentialId) async {
    try {
      LoggerService.debug('Retrieving credentials: $credentialId');

      // Check cache first
      SecureCredential? credential = _credentialCache[credentialId];

      // If not in cache, load from PocketBase
      if (credential == null) {
        final records = await _pocketBaseService.getFullList(
          collectionName: 'secure_credentials',
          filter: 'id = "$credentialId"',
        );

        if (records.isEmpty) {
          throw CredentialException('Credential not found: $credentialId');
        }

        credential = SecureCredential.fromJson(records.first.data);
        _credentialCache[credentialId] = credential;
      }

      // Validate credential
      _validateCredentialAccess(credential);

      // Decrypt credentials
      final encryptionResult = EncryptionResult(
        encryptedData: credential.encryptedData,
        iv: credential.iv,
        salt: credential.salt,
        checksum: credential.checksum,
        algorithm: 'AES-256-GCM-SIMULATED',
        keyId: credential.keyId,
        metadata: {},
        timestamp: credential.createdAt,
      );

      final decryptedData = await _encryptionService.decryptData(
        encryptionResult,
        _masterKey,
      );

      final credentials = jsonDecode(decryptedData) as Map<String, dynamic>;

      // Update access tracking
      await _updateCredentialAccess(credentialId);

      // Log credential access
      await _logCredentialEvent(
        action: 'retrieve',
        credentialId: credentialId,
        success: true,
        details: {
          'type': credential.type.value,
          'access_count': credential.accessCount + 1,
        },
      );

      LoggerService.debug('Credentials retrieved successfully: $credentialId');
      return credentials;
    } catch (e) {
      LoggerService.error('Failed to retrieve credentials: $credentialId', e);

      await _logCredentialEvent(
        action: 'retrieve',
        credentialId: credentialId,
        success: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// Update credentials
  Future<void> updateCredentials({
    required String credentialId,
    required Map<String, dynamic> newCredentials,
    String? description,
  }) async {
    try {
      LoggerService.info('Updating credentials: $credentialId');

      // Get existing credential
      final existingCredential = await _getCredentialById(credentialId);

      // Validate new credentials
      _validateCredentials(newCredentials, existingCredential.type);

      // Encrypt new credentials
      final encryptionResult = await _encryptionService.encryptData(
        jsonEncode(newCredentials),
        key: _masterKey,
        metadata: {
          'credential_id': credentialId,
          'type': existingCredential.type.value,
          'description': description ?? existingCredential.description,
        },
      );

      // Update credential
      final updatedCredential = existingCredential.copyWith(
        encryptedData: encryptionResult.encryptedData,
        iv: encryptionResult.iv,
        salt: encryptionResult.salt,
        checksum: encryptionResult.checksum,
        keyId: encryptionResult.keyId,
        description: description ?? existingCredential.description,
        updatedAt: DateTime.now(),
      );

      // Update in PocketBase
      await _pocketBaseService.updateRecord(
        collectionName: 'secure_credentials',
        recordId: credentialId,
        data: updatedCredential.toJson(),
      );

      // Update cache
      _credentialCache[credentialId] = updatedCredential;

      // Log credential update
      await _logCredentialEvent(
        action: 'update',
        credentialId: credentialId,
        success: true,
        details: {
          'type': existingCredential.type.value,
          'description_changed': description != null,
        },
      );

      LoggerService.info('Credentials updated successfully: $credentialId');
    } catch (e) {
      LoggerService.error('Failed to update credentials: $credentialId', e);

      await _logCredentialEvent(
        action: 'update',
        credentialId: credentialId,
        success: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// Delete credentials
  Future<void> deleteCredentials(String credentialId) async {
    try {
      LoggerService.info('Deleting credentials: $credentialId');

      // Get credential for logging
      final credential = await _getCredentialById(credentialId);

      // Delete from PocketBase
      await _pocketBaseService.deleteRecord(
        collectionName: 'secure_credentials',
        recordId: credentialId,
      );

      // Remove from cache
      _credentialCache.remove(credentialId);

      // Log credential deletion
      await _logCredentialEvent(
        action: 'delete',
        credentialId: credentialId,
        success: true,
        details: {
          'type': credential.type.value,
          'was_active': credential.isActive,
        },
      );

      LoggerService.info('Credentials deleted successfully: $credentialId');
    } catch (e) {
      LoggerService.error('Failed to delete credentials: $credentialId', e);

      await _logCredentialEvent(
        action: 'delete',
        credentialId: credentialId,
        success: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// List available credentials
  Future<List<SecureCredential>> listCredentials({
    CredentialType? type,
    bool activeOnly = true,
  }) async {
    try {
      LoggerService.debug('Listing credentials');

      String filter = '';
      if (type != null) {
        filter = 'type = "${type.value}"';
      }
      if (activeOnly) {
        filter =
            filter.isEmpty ? 'is_active = true' : '$filter && is_active = true';
      }

      final records = await _pocketBaseService.getFullList(
        collectionName: 'secure_credentials',
        filter: filter.isEmpty ? null : filter,
      );

      final credentials =
          records
              .map((record) => SecureCredential.fromJson(record.data))
              .toList();

      // Update cache
      for (final credential in credentials) {
        _credentialCache[credential.id] = credential;
      }

      LoggerService.debug('Found ${credentials.length} credentials');
      return credentials;
    } catch (e) {
      LoggerService.error('Failed to list credentials', e);
      rethrow;
    }
  }

  /// Rotate credentials (create new version)
  Future<String> rotateCredentials({
    required String credentialId,
    required Map<String, dynamic> newCredentials,
    Duration? expiryDuration,
  }) async {
    try {
      LoggerService.info('Rotating credentials: $credentialId');

      // Get existing credential
      final existingCredential = await _getCredentialById(credentialId);

      // Deactivate old credential
      await _deactivateCredential(credentialId);

      // Create new credential with rotated ID
      final newCredentialId =
          '${credentialId}_${DateTime.now().millisecondsSinceEpoch}';

      await storeCredentials(
        credentialId: newCredentialId,
        credentials: newCredentials,
        type: existingCredential.type,
        description: '${existingCredential.description} (rotated)',
        expiryDuration: expiryDuration,
        allowedUsers: existingCredential.allowedUsers,
      );

      // Log credential rotation
      await _logCredentialEvent(
        action: 'rotate',
        credentialId: credentialId,
        success: true,
        details: {
          'new_credential_id': newCredentialId,
          'type': existingCredential.type.value,
        },
      );

      LoggerService.info(
        'Credentials rotated successfully: $credentialId -> $newCredentialId',
      );
      return newCredentialId;
    } catch (e) {
      LoggerService.error('Failed to rotate credentials: $credentialId', e);

      await _logCredentialEvent(
        action: 'rotate',
        credentialId: credentialId,
        success: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// Validate credentials based on type
  void _validateCredentials(
    Map<String, dynamic> credentials,
    CredentialType type,
  ) {
    switch (type) {
      case CredentialType.googleServiceAccount:
        final requiredFields = [
          'type',
          'project_id',
          'private_key_id',
          'private_key',
          'client_email',
          'client_id',
          'auth_uri',
          'token_uri',
        ];
        for (final field in requiredFields) {
          if (!credentials.containsKey(field)) {
            throw CredentialException(
              'Missing required field for Google Service Account: $field',
            );
          }
        }
        break;
      case CredentialType.apiKey:
        if (!credentials.containsKey('api_key') ||
            credentials['api_key'].toString().isEmpty) {
          throw CredentialException('API key cannot be empty');
        }
        break;
      case CredentialType.oauth2:
        final requiredFields = ['client_id', 'client_secret'];
        for (final field in requiredFields) {
          if (!credentials.containsKey(field)) {
            throw CredentialException(
              'Missing required field for OAuth2: $field',
            );
          }
        }
        break;
      case CredentialType.database:
        final requiredFields = ['host', 'username', 'password'];
        for (final field in requiredFields) {
          if (!credentials.containsKey(field)) {
            throw CredentialException(
              'Missing required field for database: $field',
            );
          }
        }
        break;
    }
  }

  /// Validate credential access
  void _validateCredentialAccess(SecureCredential credential) {
    // Check if credential is active
    if (!credential.isActive) {
      throw CredentialException('Credential is not active: ${credential.id}');
    }

    // Check expiry
    if (credential.expiresAt != null &&
        DateTime.now().isAfter(credential.expiresAt!)) {
      throw CredentialException('Credential has expired: ${credential.id}');
    }

    // Check user access (if applicable)
    final currentUserId = _pocketBaseService.currentUser?.id;
    if (credential.allowedUsers.isNotEmpty &&
        currentUserId != null &&
        !credential.allowedUsers.contains(currentUserId)) {
      throw CredentialException(
        'Access denied to credential: ${credential.id}',
      );
    }
  }

  /// Get credential by ID
  Future<SecureCredential> _getCredentialById(String credentialId) async {
    // Check cache first
    if (_credentialCache.containsKey(credentialId)) {
      return _credentialCache[credentialId]!;
    }

    // Load from PocketBase
    final records = await _pocketBaseService.getFullList(
      collectionName: 'secure_credentials',
      filter: 'id = "$credentialId"',
    );

    if (records.isEmpty) {
      throw CredentialException('Credential not found: $credentialId');
    }

    final credential = SecureCredential.fromJson(records.first.data);
    _credentialCache[credentialId] = credential;
    return credential;
  }

  /// Update credential access tracking
  Future<void> _updateCredentialAccess(String credentialId) async {
    try {
      final credential = _credentialCache[credentialId];
      if (credential != null) {
        final updatedCredential = credential.copyWith(
          lastAccessedAt: DateTime.now(),
          accessCount: credential.accessCount + 1,
        );

        await _pocketBaseService.updateRecord(
          collectionName: 'secure_credentials',
          recordId: credentialId,
          data: {
            'last_accessed_at':
                updatedCredential.lastAccessedAt?.toIso8601String(),
            'access_count': updatedCredential.accessCount,
          },
        );

        _credentialCache[credentialId] = updatedCredential;
      }
    } catch (e) {
      LoggerService.warning('Failed to update credential access tracking: $e');
      // Don't fail the operation if tracking fails
    }
  }

  /// Deactivate credential
  Future<void> _deactivateCredential(String credentialId) async {
    await _pocketBaseService.updateRecord(
      collectionName: 'secure_credentials',
      recordId: credentialId,
      data: {
        'is_active': false,
        'updated_at': DateTime.now().toIso8601String(),
      },
    );

    // Update cache
    final credential = _credentialCache[credentialId];
    if (credential != null) {
      _credentialCache[credentialId] = credential.copyWith(isActive: false);
    }
  }

  /// Log credential events
  Future<void> _logCredentialEvent({
    required String action,
    required String credentialId,
    required bool success,
    String? errorMessage,
    Map<String, dynamic>? details,
  }) async {
    try {
      final event = SecurityEvent(
        id: SecurityUtils.generateSecureId(),
        type: 'credential_management',
        timestamp: DateTime.now(),
        success: success,
        userId: _pocketBaseService.currentUser?.id,
        resource: credentialId,
        action: action,
        errorMessage: errorMessage,
        details: details,
        severity:
            success ? SecurityEventSeverity.low : SecurityEventSeverity.medium,
        riskScore: success ? 10 : 40,
        requiresAttention: !success,
        tags: ['credential', action, success ? 'success' : 'failure'],
      );

      await _pocketBaseService.createRecord(
        collectionName: 'security_events',
        data: event.toJson(),
      );
    } catch (e) {
      LoggerService.warning('Failed to log credential event: $e');
    }
  }

  /// Clear credential cache
  void clearCache() {
    _credentialCache.clear();
    LoggerService.debug('Credential cache cleared');
  }

  /// Get service status
  Map<String, dynamic> getStatus() {
    return {
      'service_initialized': true,
      'cached_credentials': _credentialCache.length,
      'encryption_service_available': true,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// Secure credential model
class SecureCredential {
  final String id;
  final CredentialType type;
  final String encryptedData;
  final String iv;
  final String salt;
  final String checksum;
  final String keyId;
  final String? description;
  final List<String> allowedUsers;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? expiresAt;
  final DateTime? lastAccessedAt;
  final int accessCount;
  final bool isActive;

  SecureCredential({
    required this.id,
    required this.type,
    required this.encryptedData,
    required this.iv,
    required this.salt,
    required this.checksum,
    required this.keyId,
    this.description,
    this.allowedUsers = const [],
    required this.createdAt,
    this.updatedAt,
    this.expiresAt,
    this.lastAccessedAt,
    this.accessCount = 0,
    this.isActive = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.value,
      'encrypted_data': encryptedData,
      'iv': iv,
      'salt': salt,
      'checksum': checksum,
      'key_id': keyId,
      'description': description,
      'allowed_users': allowedUsers,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'last_accessed_at': lastAccessedAt?.toIso8601String(),
      'access_count': accessCount,
      'is_active': isActive,
    };
  }

  factory SecureCredential.fromJson(Map<String, dynamic> json) {
    return SecureCredential(
      id: json['id'] as String,
      type: CredentialTypeExtension.fromString(json['type'] as String),
      encryptedData: json['encrypted_data'] as String,
      iv: json['iv'] as String,
      salt: json['salt'] as String,
      checksum: json['checksum'] as String,
      keyId: json['key_id'] as String,
      description: json['description'] as String?,
      allowedUsers: List<String>.from(json['allowed_users'] as List? ?? []),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : null,
      expiresAt:
          json['expires_at'] != null
              ? DateTime.parse(json['expires_at'] as String)
              : null,
      lastAccessedAt:
          json['last_accessed_at'] != null
              ? DateTime.parse(json['last_accessed_at'] as String)
              : null,
      accessCount: json['access_count'] as int? ?? 0,
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  SecureCredential copyWith({
    String? id,
    CredentialType? type,
    String? encryptedData,
    String? iv,
    String? salt,
    String? checksum,
    String? keyId,
    String? description,
    List<String>? allowedUsers,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? expiresAt,
    DateTime? lastAccessedAt,
    int? accessCount,
    bool? isActive,
  }) {
    return SecureCredential(
      id: id ?? this.id,
      type: type ?? this.type,
      encryptedData: encryptedData ?? this.encryptedData,
      iv: iv ?? this.iv,
      salt: salt ?? this.salt,
      checksum: checksum ?? this.checksum,
      keyId: keyId ?? this.keyId,
      description: description ?? this.description,
      allowedUsers: allowedUsers ?? this.allowedUsers,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
      accessCount: accessCount ?? this.accessCount,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'SecureCredential{id: $id, type: $type, isActive: $isActive}';
  }
}

/// Types of credentials
enum CredentialType { googleServiceAccount, apiKey, oauth2, database }

/// Extension for CredentialType
extension CredentialTypeExtension on CredentialType {
  String get value {
    switch (this) {
      case CredentialType.googleServiceAccount:
        return 'google_service_account';
      case CredentialType.apiKey:
        return 'api_key';
      case CredentialType.oauth2:
        return 'oauth2';
      case CredentialType.database:
        return 'database';
    }
  }

  static CredentialType fromString(String value) {
    switch (value) {
      case 'google_service_account':
        return CredentialType.googleServiceAccount;
      case 'api_key':
        return CredentialType.apiKey;
      case 'oauth2':
        return CredentialType.oauth2;
      case 'database':
        return CredentialType.database;
      default:
        throw ArgumentError('Unknown credential type: $value');
    }
  }
}

/// Custom exception for credential management errors
class CredentialException implements Exception {
  final String message;

  CredentialException(this.message);

  @override
  String toString() => 'CredentialException: $message';
}
