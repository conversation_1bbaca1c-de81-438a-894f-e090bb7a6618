import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Service to handle system UI overlays and edge-to-edge display
class SystemUIService {
  static bool _isInitialized = false;

  /// Initialize system UI settings for edge-to-edge display
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggerService.info(
        'Initializing SystemUIService for edge-to-edge display...',
      );

      // Configure system UI overlays for edge-to-edge
      await _configureSystemUIOverlays();

      _isInitialized = true;
      LoggerService.info('SystemUIService initialized successfully');
    } catch (e) {
      LoggerService.error('Failed to initialize SystemUIService', e);
    }
  }

  /// Configure system UI overlays for edge-to-edge display
  static Future<void> _configureSystemUIOverlays() async {
    if (Platform.isAndroid) {
      // For Android 15+, avoid deprecated APIs by using minimal configuration
      // The native MainActivity handles edge-to-edge setup

      // Only set essential system UI properties that are not deprecated
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          // Avoid setting statusBarColor and systemNavigationBarColor as they're deprecated in Android 15
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      );

      // Enable edge-to-edge mode using the non-deprecated approach
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

      LoggerService.info(
        'Android edge-to-edge system UI configured (Android 15 compatible)',
      );
    } else if (Platform.isIOS) {
      // iOS handles edge-to-edge differently, mainly through SafeArea
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
      );

      LoggerService.info('iOS system UI configured');
    }
  }

  /// Update system UI overlay style based on theme
  static void updateSystemUIForTheme(ThemeMode themeMode) {
    try {
      final isDark = themeMode == ThemeMode.dark;

      if (Platform.isAndroid) {
        // For Android 15+, avoid deprecated color APIs
        // Only set icon brightness which is not deprecated
        SystemChrome.setSystemUIOverlayStyle(
          SystemUiOverlayStyle(
            // Avoid setting statusBarColor and systemNavigationBarColor (deprecated in Android 15)
            statusBarIconBrightness:
                isDark ? Brightness.light : Brightness.dark,
            statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
            systemNavigationBarIconBrightness:
                isDark ? Brightness.light : Brightness.dark,
            // systemNavigationBarDividerColor removed as it's deprecated
          ),
        );
      } else if (Platform.isIOS) {
        SystemChrome.setSystemUIOverlayStyle(
          SystemUiOverlayStyle(
            statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
            statusBarIconBrightness:
                isDark ? Brightness.light : Brightness.dark,
          ),
        );
      }

      LoggerService.debug(
        'System UI updated for ${isDark ? 'dark' : 'light'} theme (Android 15 compatible)',
      );
    } catch (e) {
      LoggerService.error('Failed to update system UI for theme', e);
    }
  }

  /// Get safe area padding for edge-to-edge layouts
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return EdgeInsets.only(
      top: mediaQuery.padding.top,
      bottom: mediaQuery.padding.bottom,
      left: mediaQuery.padding.left,
      right: mediaQuery.padding.right,
    );
  }

  /// Get status bar height
  static double getStatusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// Get navigation bar height
  static double getNavigationBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }

  /// Check if the device supports edge-to-edge
  static bool get supportsEdgeToEdge {
    if (Platform.isAndroid) {
      // Android 15+ (API 35+) supports edge-to-edge by default
      // Earlier versions can still use it with proper configuration
      return true;
    } else if (Platform.isIOS) {
      // iOS has always supported edge-to-edge through SafeArea
      return true;
    }
    return false;
  }

  /// Reset system UI to default state
  static Future<void> reset() async {
    try {
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
      );

      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
      LoggerService.info('System UI reset to default state');
    } catch (e) {
      LoggerService.error('Failed to reset system UI', e);
    }
  }
}
