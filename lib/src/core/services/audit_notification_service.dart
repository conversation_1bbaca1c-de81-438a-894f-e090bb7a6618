import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/local_notification_service.dart';

/// Service for handling audit-related notifications and logging
/// Integrates with existing services to provide comprehensive audit trails
class AuditNotificationService {
  final PocketBaseService _pocketBaseService;

  AuditNotificationService(this._pocketBaseService);

  /// Log user action with optional notification
  Future<void> logUserAction({
    required String userId,
    required String action,
    required String entityType,
    String? entityId,
    Map<String, dynamic>? details,
    bool createNotification = false,
    String? notificationTitle,
    String? notificationMessage,
  }) async {
    try {
      // Create audit log entry
      final auditData = {
        'user_id': userId,
        'action': action,
        'entity_type': entityType,
        'entity_id': entityId,
        'details': details ?? {},
        'ip_address': 'unknown', // Could be enhanced with actual IP detection
        'user_agent': 'mobile_app',
        // Note: 'created' and 'updated' timestamps are automatically managed by PocketBase autodate fields
      };

      await _pocketBaseService.createRecord(
        collectionName: 'audit_logs',
        data: auditData,
      );

      LoggerService.info(
        'Audit log created: $action on $entityType${entityId != null ? ' ($entityId)' : ''} by user $userId',
      );

      // Create notification if requested
      if (createNotification &&
          notificationTitle != null &&
          notificationMessage != null) {
        await _createAuditNotification(
          userId: userId,
          title: notificationTitle,
          message: notificationMessage,
          action: action,
          entityType: entityType,
          entityId: entityId,
        );
      }
    } catch (e) {
      LoggerService.error('Error creating audit log', e);
      rethrow;
    }
  }

  /// Log claim status change with notification
  Future<void> logClaimStatusChange({
    required String claimId,
    required String userId,
    required String oldStatus,
    required String newStatus,
    String? reason,
    bool notifyClaimant = true,
  }) async {
    try {
      final details = {
        'old_status': oldStatus,
        'new_status': newStatus,
        'reason': reason,
      };

      await logUserAction(
        userId: userId,
        action: 'claim_status_change',
        entityType: 'claim',
        entityId: claimId,
        details: details,
        createNotification: notifyClaimant,
        notificationTitle: 'Claim Status Updated',
        notificationMessage:
            'Your claim status has been updated from $oldStatus to $newStatus',
      );

      // Show local notification for immediate feedback
      if (notifyClaimant) {
        try {
          final notificationId =
              LocalNotificationService.generateNotificationId();
          final payload = LocalNotificationService.createNotificationPayload(
            type: 'claim_status_change',
            id: claimId,
            route: '/claims/$claimId',
            additionalData: {
              'oldStatus': oldStatus,
              'newStatus': newStatus,
              'reason': reason,
            },
          );

          await LocalNotificationService.showClaimNotification(
            id: notificationId,
            title: 'Claim Status Updated',
            body:
                'Your claim status has been updated from $oldStatus to $newStatus',
            payload: payload,
          );

          LoggerService.info(
            'Local notification shown for claim status change: $claimId',
          );
        } catch (e) {
          LoggerService.error(
            'Error showing local notification for claim status change',
            e,
          );
        }
      }
    } catch (e) {
      LoggerService.error('Error logging claim status change', e);
      rethrow;
    }
  }

  /// Log document upload with notification
  Future<void> logDocumentUpload({
    required String documentId,
    required String userId,
    required String documentName,
    required String entityType,
    String? entityId,
    bool notifyRelevantUsers = true,
  }) async {
    try {
      final details = {
        'document_name': documentName,
        'document_id': documentId,
      };

      await logUserAction(
        userId: userId,
        action: 'document_upload',
        entityType: entityType,
        entityId: entityId,
        details: details,
        createNotification: notifyRelevantUsers,
        notificationTitle: 'Document Uploaded',
        notificationMessage: 'A new document "$documentName" has been uploaded',
      );

      // Show local notification
      if (notifyRelevantUsers) {
        try {
          final notificationId =
              LocalNotificationService.generateNotificationId();
          final payload = LocalNotificationService.createNotificationPayload(
            type: 'document_upload',
            id: documentId,
            route: '/documents/$documentId',
            additionalData: {
              'documentName': documentName,
              'entityType': entityType,
              'entityId': entityId,
            },
          );

          await LocalNotificationService.showNotification(
            id: notificationId,
            title: 'Document Uploaded',
            body: 'A new document "$documentName" has been uploaded',
            payload: payload,
          );

          LoggerService.info(
            'Local notification shown for document upload: $documentName',
          );
        } catch (e) {
          LoggerService.error(
            'Error showing local notification for document upload',
            e,
          );
        }
      }
    } catch (e) {
      LoggerService.error('Error logging document upload', e);
      rethrow;
    }
  }

  /// Log user profile change with notification
  Future<void> logProfileChange({
    required String userId,
    required String changeType,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues,
    bool notifyUser = true,
  }) async {
    try {
      final details = {
        'change_type': changeType,
        'old_values': oldValues ?? {},
        'new_values': newValues ?? {},
      };

      await logUserAction(
        userId: userId,
        action: 'profile_change',
        entityType: 'user_profile',
        entityId: userId,
        details: details,
        createNotification: notifyUser,
        notificationTitle: 'Profile Updated',
        notificationMessage: 'Your profile has been updated successfully',
      );

      // Show local notification
      if (notifyUser) {
        try {
          final notificationId =
              LocalNotificationService.generateNotificationId();
          final payload = LocalNotificationService.createNotificationPayload(
            type: 'profile_change',
            id: userId,
            route: '/profile',
            additionalData: {'changeType': changeType},
          );

          await LocalNotificationService.showNotification(
            id: notificationId,
            title: 'Profile Updated',
            body: 'Your profile has been updated successfully',
            payload: payload,
          );

          LoggerService.info(
            'Local notification shown for profile change: $changeType',
          );
        } catch (e) {
          LoggerService.error(
            'Error showing local notification for profile change',
            e,
          );
        }
      }
    } catch (e) {
      LoggerService.error('Error logging profile change', e);
      rethrow;
    }
  }

  /// Create audit notification in PocketBase
  Future<void> _createAuditNotification({
    required String userId,
    required String title,
    required String message,
    required String action,
    required String entityType,
    String? entityId,
  }) async {
    try {
      final notificationData = {
        'recipientId': [userId],
        'title': title,
        'message': message,
        'type': 'audit_notification',
        'isRead': false,
        'link': entityId != null ? '/$entityType/$entityId' : null,
        'metadata': {
          'action': action,
          'entity_type': entityType,
          'entity_id': entityId,
          'audit_timestamp': DateTime.now().toIso8601String(),
        },
      };

      await _pocketBaseService.createRecord(
        collectionName: 'notifications',
        data: notificationData,
      );

      LoggerService.info('Audit notification created for user $userId: $title');
    } catch (e) {
      LoggerService.error('Error creating audit notification', e);
      rethrow;
    }
  }

  /// Get audit logs for a specific entity
  Future<List<Map<String, dynamic>>> getAuditLogs({
    required String entityType,
    String? entityId,
    String? userId,
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      String filter = 'entity_type = "$entityType"';

      if (entityId != null) {
        filter += ' && entity_id = "$entityId"';
      }

      if (userId != null) {
        filter += ' && user_id = "$userId"';
      }

      final result = await _pocketBaseService.getList(
        collectionName: 'audit_logs',
        page: page,
        perPage: perPage,
        filter: filter,
        sort: '-created',
      );

      return result.items.map((record) => record.data).toList();
    } catch (e) {
      LoggerService.error('Error fetching audit logs', e);
      rethrow;
    }
  }

  /// Get audit statistics
  Future<Map<String, int>> getAuditStatistics({
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      String filter = '';

      if (userId != null) {
        filter = 'user_id = "$userId"';
      }

      if (startDate != null) {
        final dateFilter = 'created >= "${startDate.toIso8601String()}"';
        filter = filter.isEmpty ? dateFilter : '$filter && $dateFilter';
      }

      if (endDate != null) {
        final dateFilter = 'created <= "${endDate.toIso8601String()}"';
        filter = filter.isEmpty ? dateFilter : '$filter && $dateFilter';
      }

      final result = await _pocketBaseService.getFullList(
        collectionName: 'audit_logs',
        filter: filter.isNotEmpty ? filter : null,
      );

      // Count by action type
      final actionCounts = <String, int>{};
      for (final record in result) {
        final action = record.data['action'] as String? ?? 'unknown';
        actionCounts[action] = (actionCounts[action] ?? 0) + 1;
      }

      return actionCounts;
    } catch (e) {
      LoggerService.error('Error fetching audit statistics', e);
      rethrow;
    }
  }
}
