import 'package:pocketbase/pocketbase.dart';
import '../../features/auth/data/models/user_model.dart';
import '../../features/solicitor_portal/data/models/solicitor_profile_model.dart';
import 'logger_service.dart';
import 'pocketbase_service.dart';

/// Service for user-related operations including name resolution
class UserService {
  static final PocketBase _pb = PocketBaseService().pb;

  // Cache for user display names to avoid repeated API calls
  static final Map<String, String> _userNameCache = {};

  /// Get user display name by user ID
  /// Returns the most appropriate display name for the user
  static Future<String> getUserDisplayName(String userId) async {
    try {
      // Check cache first
      if (_userNameCache.containsKey(userId)) {
        return _userNameCache[userId]!;
      }

      // Fetch user record from PocketBase
      final userRecord = await _pb.collection('users').getOne(userId);
      final user = User.fromJson(userRecord.toJson());

      String displayName;

      // For solicitors, try to get the solicitor name from their profile
      if (user.userType == 'solicitor') {
        displayName = await _getSolicitorDisplayName(userId, user);
      } else {
        // For other user types, use the standard display name logic
        displayName = user.displayName;
      }

      // Cache the result
      _userNameCache[userId] = displayName;

      LoggerService.debug(
        'Resolved user display name for $userId: $displayName',
      );
      return displayName;
    } catch (e) {
      LoggerService.warning('Failed to get user display name for $userId: $e');

      // Return a fallback name
      final fallbackName = 'User ($userId)';
      _userNameCache[userId] = fallbackName;
      return fallbackName;
    }
  }

  /// Get solicitor display name from their profile
  static Future<String> _getSolicitorDisplayName(
    String userId,
    User user,
  ) async {
    try {
      // Try to get solicitor profile
      final profileRecords = await _pb
          .collection('solicitor_profiles')
          .getList(filter: 'user_id = "$userId"');

      if (profileRecords.items.isNotEmpty) {
        final profile = SolicitorProfileModel.fromJson(
          profileRecords.items.first.toJson(),
        );

        // Use solicitor name from profile if available
        if (profile.solicitorName.isNotEmpty) {
          return profile.solicitorName;
        }
      }

      // Fallback to user's display name
      return user.displayName;
    } catch (e) {
      LoggerService.warning('Failed to get solicitor profile for $userId: $e');
      return user.displayName;
    }
  }

  /// Get multiple user display names efficiently
  static Future<Map<String, String>> getUserDisplayNames(
    List<String> userIds,
  ) async {
    final result = <String, String>{};
    final uncachedIds = <String>[];

    // Check cache for existing names
    for (final userId in userIds) {
      if (_userNameCache.containsKey(userId)) {
        result[userId] = _userNameCache[userId]!;
      } else {
        uncachedIds.add(userId);
      }
    }

    // Fetch uncached user names
    for (final userId in uncachedIds) {
      try {
        final displayName = await getUserDisplayName(userId);
        result[userId] = displayName;
      } catch (e) {
        LoggerService.warning(
          'Failed to get display name for user $userId: $e',
        );
        result[userId] = 'User ($userId)';
      }
    }

    return result;
  }

  /// Clear the user name cache
  static void clearCache() {
    _userNameCache.clear();
    LoggerService.debug('User name cache cleared');
  }

  /// Clear cache for a specific user
  static void clearUserCache(String userId) {
    _userNameCache.remove(userId);
    LoggerService.debug('Cleared cache for user: $userId');
  }

  /// Get user record by ID
  static Future<User?> getUserById(String userId) async {
    try {
      final userRecord = await _pb.collection('users').getOne(userId);
      return User.fromJson(userRecord.toJson());
    } catch (e) {
      LoggerService.error('Failed to get user by ID $userId: $e');
      return null;
    }
  }

  /// Check if a user exists
  static Future<bool> userExists(String userId) async {
    try {
      await _pb.collection('users').getOne(userId);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get user email by ID
  static Future<String?> getUserEmail(String userId) async {
    try {
      final user = await getUserById(userId);
      return user?.email;
    } catch (e) {
      LoggerService.warning('Failed to get user email for $userId: $e');
      return null;
    }
  }

  /// Preload user names for a list of user IDs (useful for performance)
  static Future<void> preloadUserNames(List<String> userIds) async {
    final uncachedIds =
        userIds.where((id) => !_userNameCache.containsKey(id)).toList();

    if (uncachedIds.isEmpty) return;

    LoggerService.debug('Preloading ${uncachedIds.length} user names');

    // Load names in parallel for better performance
    await Future.wait(uncachedIds.map((userId) => getUserDisplayName(userId)));
  }
}
