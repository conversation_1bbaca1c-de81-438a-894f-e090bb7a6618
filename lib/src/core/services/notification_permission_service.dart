import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Service for managing notification permissions and status
class NotificationPermissionService {
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;

  /// Check current notification permission status
  static Future<NotificationPermissionStatus> checkPermissionStatus() async {
    try {
      final settings = await _firebaseMessaging.getNotificationSettings();

      switch (settings.authorizationStatus) {
        case AuthorizationStatus.authorized:
          return NotificationPermissionStatus.granted;
        case AuthorizationStatus.denied:
          return NotificationPermissionStatus.denied;
        case AuthorizationStatus.notDetermined:
          return NotificationPermissionStatus.notDetermined;
        case AuthorizationStatus.provisional:
          return NotificationPermissionStatus.provisional;
      }
    } catch (e) {
      LoggerService.error('Error checking notification permission status', e);
      return NotificationPermissionStatus.unknown;
    }
  }

  /// Request notification permissions from user
  static Future<NotificationPermissionStatus> requestPermissions({
    bool alert = true,
    bool announcement = false,
    bool badge = true,
    bool carPlay = false,
    bool criticalAlert = false,
    bool provisional = false,
    bool sound = true,
  }) async {
    try {
      LoggerService.info('Requesting notification permissions...');

      final settings = await _firebaseMessaging.requestPermission(
        alert: alert,
        announcement: announcement,
        badge: badge,
        carPlay: carPlay,
        criticalAlert: criticalAlert,
        provisional: provisional,
        sound: sound,
      );

      final status = _mapAuthorizationStatus(settings.authorizationStatus);

      LoggerService.info(
        'Notification permission request result: ${status.name}',
      );
      return status;
    } catch (e) {
      LoggerService.error('Error requesting notification permissions', e);
      return NotificationPermissionStatus.unknown;
    }
  }

  /// Get detailed notification settings
  static Future<Map<String, dynamic>> getDetailedSettings() async {
    try {
      final settings = await _firebaseMessaging.getNotificationSettings();

      return {
        'authorizationStatus': settings.authorizationStatus.name,
        'alert': settings.alert.name,
        'announcement': settings.announcement.name,
        'badge': settings.badge.name,
        'carPlay': settings.carPlay.name,
        'lockScreen': settings.lockScreen.name,
        'notificationCenter': settings.notificationCenter.name,
        'showPreviews': settings.showPreviews.name,
        'sound': settings.sound.name,
        'criticalAlert': settings.criticalAlert.name,
        'timeSensitive': settings.timeSensitive.name,
      };
    } catch (e) {
      LoggerService.error('Error getting detailed notification settings', e);
      return {};
    }
  }

  /// Check if notifications are effectively enabled
  static Future<bool> areNotificationsEnabled() async {
    try {
      final status = await checkPermissionStatus();
      return status == NotificationPermissionStatus.granted ||
          status == NotificationPermissionStatus.provisional;
    } catch (e) {
      LoggerService.error('Error checking if notifications are enabled', e);
      return false;
    }
  }

  /// Get user-friendly permission status message
  static String getPermissionStatusMessage(
    NotificationPermissionStatus status,
  ) {
    switch (status) {
      case NotificationPermissionStatus.granted:
        return 'Notifications are enabled';
      case NotificationPermissionStatus.denied:
        return 'Notifications are disabled. Enable them in Settings to receive updates.';
      case NotificationPermissionStatus.notDetermined:
        return 'Notification permissions not set. Tap to enable notifications.';
      case NotificationPermissionStatus.provisional:
        return 'Notifications are enabled (provisional)';
      case NotificationPermissionStatus.unknown:
        return 'Unable to determine notification status';
    }
  }

  /// Get troubleshooting instructions for disabled notifications
  static List<String> getTroubleshootingInstructions() {
    return [
      '1. Open your device Settings',
      '2. Find and tap "3Pay Global" in the app list',
      '3. Tap "Notifications"',
      '4. Enable "Allow Notifications"',
      '5. Configure your preferred notification types',
      '6. Return to the app and try again',
    ];
  }

  /// Check if user can request permissions (not permanently denied)
  static Future<bool> canRequestPermissions() async {
    try {
      final status = await checkPermissionStatus();
      // On iOS, once denied, user needs to go to settings
      // On Android, we can request again unless permanently denied
      return status == NotificationPermissionStatus.notDetermined;
    } catch (e) {
      LoggerService.error('Error checking if can request permissions', e);
      return false;
    }
  }

  /// Map Firebase authorization status to our enum
  static NotificationPermissionStatus _mapAuthorizationStatus(
    AuthorizationStatus status,
  ) {
    switch (status) {
      case AuthorizationStatus.authorized:
        return NotificationPermissionStatus.granted;
      case AuthorizationStatus.denied:
        return NotificationPermissionStatus.denied;
      case AuthorizationStatus.notDetermined:
        return NotificationPermissionStatus.notDetermined;
      case AuthorizationStatus.provisional:
        return NotificationPermissionStatus.provisional;
    }
  }

  /// Request permissions with user-friendly flow
  static Future<NotificationPermissionResult>
  requestPermissionsWithFlow() async {
    try {
      // Check current status first
      final currentStatus = await checkPermissionStatus();

      if (currentStatus == NotificationPermissionStatus.granted) {
        return NotificationPermissionResult(
          status: currentStatus,
          message: 'Notifications are already enabled',
          canRetry: false,
        );
      }

      if (currentStatus == NotificationPermissionStatus.denied) {
        return NotificationPermissionResult(
          status: currentStatus,
          message:
              'Notifications are disabled. Please enable them in Settings.',
          canRetry: false,
          needsSettingsRedirect: true,
        );
      }

      // Request permissions
      final newStatus = await requestPermissions();

      return NotificationPermissionResult(
        status: newStatus,
        message: getPermissionStatusMessage(newStatus),
        canRetry: newStatus == NotificationPermissionStatus.denied,
        needsSettingsRedirect: newStatus == NotificationPermissionStatus.denied,
      );
    } catch (e) {
      LoggerService.error('Error in permission request flow', e);
      return NotificationPermissionResult(
        status: NotificationPermissionStatus.unknown,
        message: 'Unable to request notification permissions',
        canRetry: true,
      );
    }
  }
}

/// Notification permission status enum
enum NotificationPermissionStatus {
  granted,
  denied,
  notDetermined,
  provisional,
  unknown,
}

/// Result of permission request with additional context
class NotificationPermissionResult {
  final NotificationPermissionStatus status;
  final String message;
  final bool canRetry;
  final bool needsSettingsRedirect;

  const NotificationPermissionResult({
    required this.status,
    required this.message,
    this.canRetry = false,
    this.needsSettingsRedirect = false,
  });

  bool get isGranted =>
      status == NotificationPermissionStatus.granted ||
      status == NotificationPermissionStatus.provisional;
}
