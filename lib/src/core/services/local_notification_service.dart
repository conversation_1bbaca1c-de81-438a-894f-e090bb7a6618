import 'dart:convert';
import 'dart:io';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Local notification service for handling local notifications across the app
/// Provides initialization, channel management, and notification lifecycle management
class LocalNotificationService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  static bool _isInitialized = false;
  static bool _permissionGranted = false;

  // Notification channel constants
  static const String DEFAULT_CHANNEL_ID = "3pay_default_channel";
  static const String CLAIMS_CHANNEL_ID = "3pay_claims_channel";
  static const String FUNDING_CHANNEL_ID = "3pay_funding_channel";
  static const String MESSAGES_CHANNEL_ID = "3pay_messages_channel";

  // Channel names and descriptions
  static const String DEFAULT_CHANNEL_NAME = "General Notifications";
  static const String DEFAULT_CHANNEL_DESCRIPTION =
      "General app notifications and updates";
  static const String CLAIMS_CHANNEL_NAME = "Claim Updates";
  static const String CLAIMS_CHANNEL_DESCRIPTION =
      "Important updates about your claims and litigation progress";
  static const String FUNDING_CHANNEL_NAME = "Funding Opportunities";
  static const String FUNDING_CHANNEL_DESCRIPTION =
      "New funding opportunities and investment updates";
  static const String MESSAGES_CHANNEL_NAME = "Agent Messages";
  static const String MESSAGES_CHANNEL_DESCRIPTION =
      "Direct messages from 3Pay Global agents and support";

  /// Initialize the local notification service
  static Future<void> initialize() async {
    if (_isInitialized) {
      LoggerService.info('LocalNotificationService already initialized');
      return;
    }

    try {
      LoggerService.info('Initializing LocalNotificationService...');

      // Initialize timezone data
      tz.initializeTimeZones();

      // Configure Android settings
      const AndroidInitializationSettings androidSettings =
          AndroidInitializationSettings('@drawable/notification_icon');

      // Configure iOS settings
      const DarwinInitializationSettings iosSettings =
          DarwinInitializationSettings(
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
          );

      // Initialize plugin
      const InitializationSettings initializationSettings =
          InitializationSettings(android: androidSettings, iOS: iosSettings);

      await _notificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onDidReceiveNotificationResponse,
      );

      // Create notification channels for Android
      await _createNotificationChannels();

      // Request permissions for iOS
      if (Platform.isIOS) {
        _permissionGranted = await requestPermissions();
      } else {
        _permissionGranted =
            true; // Android permissions are handled in manifest
      }

      _isInitialized = true;
      LoggerService.info('LocalNotificationService initialized successfully');
    } catch (e) {
      LoggerService.error('Error initializing LocalNotificationService', e);
      rethrow;
    }
  }

  /// Create notification channels for Android
  static Future<void> _createNotificationChannels() async {
    if (!Platform.isAndroid) return;

    try {
      LoggerService.info('Creating notification channels for Android...');

      final List<AndroidNotificationChannel> channels = [
        // Default channel
        const AndroidNotificationChannel(
          DEFAULT_CHANNEL_ID,
          DEFAULT_CHANNEL_NAME,
          description: DEFAULT_CHANNEL_DESCRIPTION,
          importance: Importance.defaultImportance,
          playSound: true,
          enableVibration: true,
        ),
        // Claims channel (high importance)
        const AndroidNotificationChannel(
          CLAIMS_CHANNEL_ID,
          CLAIMS_CHANNEL_NAME,
          description: CLAIMS_CHANNEL_DESCRIPTION,
          importance: Importance.high,
          playSound: true,
          enableVibration: true,
        ),
        // Funding channel
        const AndroidNotificationChannel(
          FUNDING_CHANNEL_ID,
          FUNDING_CHANNEL_NAME,
          description: FUNDING_CHANNEL_DESCRIPTION,
          importance: Importance.defaultImportance,
          playSound: true,
          enableVibration: true,
        ),
        // Messages channel (high importance)
        const AndroidNotificationChannel(
          MESSAGES_CHANNEL_ID,
          MESSAGES_CHANNEL_NAME,
          description: MESSAGES_CHANNEL_DESCRIPTION,
          importance: Importance.high,
          playSound: true,
          enableVibration: true,
        ),
      ];

      for (final channel in channels) {
        await _notificationsPlugin
            .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin
            >()
            ?.createNotificationChannel(channel);
      }

      LoggerService.info('Created ${channels.length} notification channels');
    } catch (e) {
      LoggerService.error('Error creating notification channels', e);
      rethrow;
    }
  }

  /// Request notification permissions for iOS
  static Future<bool> requestPermissions() async {
    if (!Platform.isIOS) return true;

    try {
      LoggerService.info('Requesting notification permissions for iOS...');

      final bool? result = await _notificationsPlugin
          .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin
          >()
          ?.requestPermissions(alert: true, badge: true, sound: true);

      _permissionGranted = result ?? false;
      LoggerService.info(
        'iOS notification permissions granted: $_permissionGranted',
      );
      return _permissionGranted;
    } catch (e) {
      LoggerService.error('Error requesting iOS notification permissions', e);
      return false;
    }
  }

  /// Show an immediate notification
  static Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
    String channelId = DEFAULT_CHANNEL_ID,
  }) async {
    if (!_isInitialized) {
      LoggerService.warning(
        'LocalNotificationService not initialized. Initializing now...',
      );
      await initialize();
    }

    if (!_permissionGranted && Platform.isIOS) {
      LoggerService.warning('Notification permissions not granted');
      return;
    }

    try {
      // Validate input
      if (!_validateNotificationInput(title, body)) {
        return;
      }

      LoggerService.info('Showing notification: $title');

      // Configure platform-specific details
      final androidDetails = AndroidNotificationDetails(
        channelId,
        _getChannelName(channelId),
        channelDescription: _getChannelDescription(channelId),
        importance: _getChannelImportance(channelId),
        priority: Priority.defaultPriority,
        icon: '@drawable/notification_icon',
        playSound: true,
        enableVibration: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notificationsPlugin.show(
        id,
        formatNotificationTitle(title),
        formatNotificationBody(body),
        notificationDetails,
        payload: payload,
      );

      LoggerService.info('Notification shown successfully: ID $id');
    } catch (e) {
      LoggerService.error('Error showing notification', e);
      rethrow;
    }
  }

  /// Schedule a notification for a future time
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    String channelId = DEFAULT_CHANNEL_ID,
  }) async {
    if (!_isInitialized) {
      LoggerService.warning(
        'LocalNotificationService not initialized. Initializing now...',
      );
      await initialize();
    }

    if (!_permissionGranted && Platform.isIOS) {
      LoggerService.warning('Notification permissions not granted');
      return;
    }

    try {
      // Validate input
      if (!_validateNotificationInput(title, body)) {
        return;
      }

      if (!_validateScheduledDate(scheduledDate)) {
        return;
      }

      LoggerService.info('Scheduling notification for: $scheduledDate');

      // Configure platform-specific details
      final androidDetails = AndroidNotificationDetails(
        channelId,
        _getChannelName(channelId),
        channelDescription: _getChannelDescription(channelId),
        importance: _getChannelImportance(channelId),
        priority: Priority.defaultPriority,
        icon: '@drawable/notification_icon',
        playSound: true,
        enableVibration: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notificationsPlugin.zonedSchedule(
        id,
        formatNotificationTitle(title),
        formatNotificationBody(body),
        tz.TZDateTime.from(scheduledDate, tz.local),
        notificationDetails,
        payload: payload,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      );

      LoggerService.info(
        'Notification scheduled successfully: ID $id for $scheduledDate',
      );
    } catch (e) {
      LoggerService.error('Error scheduling notification', e);
      rethrow;
    }
  }

  /// Cancel a specific notification
  static Future<void> cancelNotification(int id) async {
    try {
      await _notificationsPlugin.cancel(id);
      LoggerService.info('Cancelled notification: ID $id');
    } catch (e) {
      LoggerService.error('Error cancelling notification $id', e);
      rethrow;
    }
  }

  /// Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    try {
      await _notificationsPlugin.cancelAll();
      LoggerService.info('Cancelled all notifications');
    } catch (e) {
      LoggerService.error('Error cancelling all notifications', e);
      rethrow;
    }
  }

  /// Get list of pending notifications
  static Future<List<PendingNotificationRequest>>
  getPendingNotifications() async {
    try {
      final pendingNotifications =
          await _notificationsPlugin.pendingNotificationRequests();
      LoggerService.info(
        'Retrieved ${pendingNotifications.length} pending notifications',
      );
      return pendingNotifications;
    } catch (e) {
      LoggerService.error('Error getting pending notifications', e);
      rethrow;
    }
  }

  // Channel-specific notification methods

  /// Show a claim-related notification
  static Future<void> showClaimNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    await showNotification(
      id: id,
      title: title,
      body: body,
      payload: payload,
      channelId: CLAIMS_CHANNEL_ID,
    );
  }

  /// Show a funding-related notification
  static Future<void> showFundingNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    await showNotification(
      id: id,
      title: title,
      body: body,
      payload: payload,
      channelId: FUNDING_CHANNEL_ID,
    );
  }

  /// Show a message-related notification
  static Future<void> showMessageNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    await showNotification(
      id: id,
      title: title,
      body: body,
      payload: payload,
      channelId: MESSAGES_CHANNEL_ID,
    );
  }

  // Notification handling callbacks

  /// Handle notification tap response
  static void _onDidReceiveNotificationResponse(NotificationResponse response) {
    LoggerService.info('Notification tapped: ${response.payload}');
    _handleNotificationResponse(response.payload);
  }

  /// Handle notification response and navigation
  static void _handleNotificationResponse(String? payload) {
    if (payload == null || payload.isEmpty) {
      LoggerService.info('No payload in notification response');
      return;
    }

    try {
      final Map<String, dynamic> data = jsonDecode(payload);
      _handleNotificationNavigation(data);
    } catch (e) {
      LoggerService.error('Error parsing notification payload', e);
    }
  }

  /// Handle navigation based on notification payload
  static void _handleNotificationNavigation(Map<String, dynamic> data) {
    try {
      final String? type = data['type'];
      final String? id = data['id'];
      final String? route = data['route'];

      LoggerService.info(
        'Handling notification navigation: type=$type, id=$id, route=$route',
      );

      // TODO: Implement navigation logic based on app's routing system
      // This will be implemented when the app's navigation system is available

      switch (type) {
        case 'claim':
          LoggerService.info('Navigate to claim details: $id');
          // Navigate to claim details page
          break;
        case 'funding':
          LoggerService.info('Navigate to funding opportunity: $id');
          // Navigate to funding details page
          break;
        case 'message':
          LoggerService.info('Navigate to message: $id');
          // Navigate to messages page
          break;
        default:
          LoggerService.info('Navigate to default route: $route');
          // Navigate to default route or home page
          break;
      }
    } catch (e) {
      LoggerService.error('Error handling notification navigation', e);
    }
  }

  // Utility methods

  /// Generate a unique notification ID
  static int generateNotificationId() {
    return DateTime.now().millisecondsSinceEpoch.remainder(100000);
  }

  /// Validate notification input parameters
  static bool _validateNotificationInput(String title, String body) {
    if (title.trim().isEmpty) {
      LoggerService.warning('Notification title is empty');
      return false;
    }
    if (body.trim().isEmpty) {
      LoggerService.warning('Notification body is empty');
      return false;
    }
    return true;
  }

  /// Validate scheduled date
  static bool _validateScheduledDate(DateTime scheduledDate) {
    if (scheduledDate.isBefore(DateTime.now())) {
      LoggerService.warning('Scheduled date is in the past: $scheduledDate');
      return false;
    }
    return true;
  }

  /// Format notification title
  static String formatNotificationTitle(String title) {
    const int maxLength = 50; // Platform-specific limit
    if (title.length <= maxLength) return title;
    return '${title.substring(0, maxLength - 3)}...';
  }

  /// Format notification body
  static String formatNotificationBody(String body) {
    const int maxLength = 200; // Platform-specific limit
    if (body.length <= maxLength) return body;
    return '${body.substring(0, maxLength - 3)}...';
  }

  /// Get channel name by ID
  static String _getChannelName(String channelId) {
    switch (channelId) {
      case CLAIMS_CHANNEL_ID:
        return CLAIMS_CHANNEL_NAME;
      case FUNDING_CHANNEL_ID:
        return FUNDING_CHANNEL_NAME;
      case MESSAGES_CHANNEL_ID:
        return MESSAGES_CHANNEL_NAME;
      default:
        return DEFAULT_CHANNEL_NAME;
    }
  }

  /// Get channel description by ID
  static String _getChannelDescription(String channelId) {
    switch (channelId) {
      case CLAIMS_CHANNEL_ID:
        return CLAIMS_CHANNEL_DESCRIPTION;
      case FUNDING_CHANNEL_ID:
        return FUNDING_CHANNEL_DESCRIPTION;
      case MESSAGES_CHANNEL_ID:
        return MESSAGES_CHANNEL_DESCRIPTION;
      default:
        return DEFAULT_CHANNEL_DESCRIPTION;
    }
  }

  /// Get channel importance by ID
  static Importance _getChannelImportance(String channelId) {
    switch (channelId) {
      case CLAIMS_CHANNEL_ID:
      case MESSAGES_CHANNEL_ID:
        return Importance.high;
      case FUNDING_CHANNEL_ID:
      case DEFAULT_CHANNEL_ID:
      default:
        return Importance.defaultImportance;
    }
  }

  // Service lifecycle methods

  /// Check if service is initialized
  static bool get isInitialized => _isInitialized;

  /// Check if permissions are granted
  static bool get isPermissionGranted => _permissionGranted;

  /// Update service configuration
  static Future<void> updateConfiguration({
    bool? enableClaims,
    bool? enableFunding,
    bool? enableMessages,
  }) async {
    try {
      LoggerService.info('Updating notification service configuration');

      // TODO: Implement configuration persistence
      // This could be stored in SharedPreferences or app settings

      LoggerService.info('Notification service configuration updated');
    } catch (e) {
      LoggerService.error(
        'Error updating notification service configuration',
        e,
      );
      rethrow;
    }
  }

  /// Dispose of service resources
  static Future<void> dispose() async {
    try {
      LoggerService.info('Disposing LocalNotificationService...');

      // Cancel all pending notifications
      await cancelAllNotifications();

      _isInitialized = false;
      _permissionGranted = false;

      LoggerService.info('LocalNotificationService disposed successfully');
    } catch (e) {
      LoggerService.error('Error disposing LocalNotificationService', e);
      rethrow;
    }
  }

  /// Create notification payload
  static String createNotificationPayload({
    required String type,
    String? id,
    String? route,
    Map<String, dynamic>? additionalData,
  }) {
    final Map<String, dynamic> payload = {
      'type': type,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (id != null) payload['id'] = id;
    if (route != null) payload['route'] = route;
    if (additionalData != null) payload.addAll(additionalData);

    return jsonEncode(payload);
  }

  /// Parse notification payload
  static Map<String, dynamic> parseNotificationPayload(String payload) {
    try {
      if (payload.isEmpty) {
        return <String, dynamic>{};
      }

      final Map<String, dynamic> parsed = jsonDecode(payload);
      return parsed;
    } catch (e) {
      LoggerService.error('Error parsing notification payload', e);
      return <String, dynamic>{};
    }
  }

  /// Get service health status
  static Map<String, dynamic> getServiceStatus() {
    return {
      'isInitialized': _isInitialized,
      'isPermissionGranted': _permissionGranted,
      'platform': Platform.operatingSystem,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
