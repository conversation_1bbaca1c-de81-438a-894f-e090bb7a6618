import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Configuration model for Google Drive service
class GoogleDriveConfig {
  final String serviceAccountEmail;
  final String rootFolderId;
  final int apiQuotaLimit;
  final int apiQuotaUsed;
  final DateTime lastQuotaReset;
  final bool encryptionEnabled;
  final bool backupEnabled;
  final String environment;
  final Map<String, dynamic>? serviceAccountCredentials;

  const GoogleDriveConfig({
    required this.serviceAccountEmail,
    required this.rootFolderId,
    required this.apiQuotaLimit,
    required this.apiQuotaUsed,
    required this.lastQuotaReset,
    required this.encryptionEnabled,
    required this.backupEnabled,
    required this.environment,
    this.serviceAccountCredentials,
  });

  factory GoogleDriveConfig.fromPocketBase(Map<String, dynamic> data) {
    return GoogleDriveConfig(
      serviceAccountEmail: data['service_account_email'] as String,
      rootFolderId: data['root_folder_id'] as String,
      apiQuotaLimit: data['api_quota_limit'] as int,
      apiQuotaUsed: data['api_quota_used'] as int,
      lastQuotaReset: DateTime.parse(data['last_quota_reset'] as String),
      encryptionEnabled: data['encryption_enabled'] as bool,
      backupEnabled: data['backup_enabled'] as bool,
      environment: data['environment'] as String,
    );
  }

  Map<String, dynamic> toPocketBase() {
    return {
      'service_account_email': serviceAccountEmail,
      'root_folder_id': rootFolderId,
      'api_quota_limit': apiQuotaLimit,
      'api_quota_used': apiQuotaUsed,
      'last_quota_reset': lastQuotaReset.toIso8601String(),
      'encryption_enabled': encryptionEnabled,
      'backup_enabled': backupEnabled,
      'environment': environment,
    };
  }

  GoogleDriveConfig copyWith({
    String? serviceAccountEmail,
    String? rootFolderId,
    int? apiQuotaLimit,
    int? apiQuotaUsed,
    DateTime? lastQuotaReset,
    bool? encryptionEnabled,
    bool? backupEnabled,
    String? environment,
    Map<String, dynamic>? serviceAccountCredentials,
  }) {
    return GoogleDriveConfig(
      serviceAccountEmail: serviceAccountEmail ?? this.serviceAccountEmail,
      rootFolderId: rootFolderId ?? this.rootFolderId,
      apiQuotaLimit: apiQuotaLimit ?? this.apiQuotaLimit,
      apiQuotaUsed: apiQuotaUsed ?? this.apiQuotaUsed,
      lastQuotaReset: lastQuotaReset ?? this.lastQuotaReset,
      encryptionEnabled: encryptionEnabled ?? this.encryptionEnabled,
      backupEnabled: backupEnabled ?? this.backupEnabled,
      environment: environment ?? this.environment,
      serviceAccountCredentials:
          serviceAccountCredentials ?? this.serviceAccountCredentials,
    );
  }

  /// Check if quota is near limit (80% threshold)
  bool get isQuotaNearLimit => apiQuotaUsed >= (apiQuotaLimit * 0.8);

  /// Check if quota is exceeded
  bool get isQuotaExceeded => apiQuotaUsed >= apiQuotaLimit;

  /// Get remaining quota
  int get remainingQuota => apiQuotaLimit - apiQuotaUsed;

  /// Get quota usage percentage
  double get quotaUsagePercentage =>
      apiQuotaLimit > 0 ? (apiQuotaUsed / apiQuotaLimit) * 100 : 0;

  @override
  String toString() =>
      'GoogleDriveConfig(environment: $environment, email: $serviceAccountEmail)';
}

/// Service for managing Google Drive configuration
class GoogleDriveConfigService {
  static final GoogleDriveConfigService _instance =
      GoogleDriveConfigService._internal();
  factory GoogleDriveConfigService() => _instance;
  GoogleDriveConfigService._internal();

  final PocketBaseService _pocketBaseService = PocketBaseService();
  GoogleDriveConfig? _cachedConfig;
  DateTime? _lastConfigLoad;
  static const Duration _configCacheDuration = Duration(minutes: 15);

  /// Load configuration for current environment
  Future<GoogleDriveConfig> loadConfig({String? environment}) async {
    try {
      // Use cached config if still valid
      if (_cachedConfig != null &&
          _lastConfigLoad != null &&
          DateTime.now().difference(_lastConfigLoad!) < _configCacheDuration) {
        LoggerService.debug('Using cached Google Drive configuration');
        return _cachedConfig!;
      }

      // Determine environment
      final env = environment ?? _getCurrentEnvironment();
      LoggerService.info(
        'Loading Google Drive configuration for environment: $env',
      );

      // Query configuration from PocketBase
      final records = await _pocketBaseService.getFullList(
        collectionName: 'google_drive_config',
        filter: 'environment = "$env"',
      );

      if (records.isEmpty) {
        // For development environment, create a default configuration
        if (env == 'development') {
          LoggerService.warning(
            'No Google Drive configuration found for development environment. Creating default configuration.',
          );
          return await _createDefaultDevelopmentConfig();
        }

        throw GoogleDriveConfigException(
          'No Google Drive configuration found for environment: $env',
        );
      }

      if (records.length > 1) {
        LoggerService.warning(
          'Multiple configurations found for environment $env, using first one',
        );
      }

      final configData = records.first.data;
      final config = GoogleDriveConfig.fromPocketBase(configData);

      // Load service account credentials if available
      final configWithCredentials = await _loadServiceAccountCredentials(
        config,
      );

      // Cache the configuration
      _cachedConfig = configWithCredentials;
      _lastConfigLoad = DateTime.now();

      LoggerService.info('Google Drive configuration loaded successfully');
      return configWithCredentials;
    } catch (e) {
      LoggerService.error('Failed to load Google Drive configuration', e);
      rethrow;
    }
  }

  /// Update API quota usage
  Future<void> updateQuotaUsage(int newUsage) async {
    try {
      if (_cachedConfig == null) {
        throw GoogleDriveConfigException('Configuration not loaded');
      }

      final env = _cachedConfig!.environment;
      LoggerService.debug(
        'Updating quota usage to $newUsage for environment: $env',
      );

      // Find the configuration record
      final records = await _pocketBaseService.getFullList(
        collectionName: 'google_drive_config',
        filter: 'environment = "$env"',
      );

      if (records.isEmpty) {
        throw GoogleDriveConfigException(
          'Configuration record not found for environment: $env',
        );
      }

      final record = records.first;

      // Update quota usage
      await _pocketBaseService.updateRecord(
        collectionName: 'google_drive_config',
        recordId: record.id,
        data: {'api_quota_used': newUsage},
      );

      // Update cached config
      _cachedConfig = _cachedConfig!.copyWith(apiQuotaUsed: newUsage);

      LoggerService.debug('Quota usage updated successfully');
    } catch (e) {
      LoggerService.error('Failed to update quota usage', e);
      rethrow;
    }
  }

  /// Reset quota usage (typically called daily)
  Future<void> resetQuota() async {
    try {
      if (_cachedConfig == null) {
        throw GoogleDriveConfigException('Configuration not loaded');
      }

      final env = _cachedConfig!.environment;
      LoggerService.info('Resetting quota for environment: $env');

      // Find the configuration record
      final records = await _pocketBaseService.getFullList(
        collectionName: 'google_drive_config',
        filter: 'environment = "$env"',
      );

      if (records.isEmpty) {
        throw GoogleDriveConfigException(
          'Configuration record not found for environment: $env',
        );
      }

      final record = records.first;
      final now = DateTime.now();

      // Reset quota usage and update reset time
      await _pocketBaseService.updateRecord(
        collectionName: 'google_drive_config',
        recordId: record.id,
        data: {'api_quota_used': 0, 'last_quota_reset': now.toIso8601String()},
      );

      // Update cached config
      _cachedConfig = _cachedConfig!.copyWith(
        apiQuotaUsed: 0,
        lastQuotaReset: now,
      );

      LoggerService.info('Quota reset successfully');
    } catch (e) {
      LoggerService.error('Failed to reset quota', e);
      rethrow;
    }
  }

  /// Check if quota reset is needed (daily reset)
  Future<bool> isQuotaResetNeeded() async {
    try {
      final config = await loadConfig();
      final now = DateTime.now();
      final lastReset = config.lastQuotaReset;

      // Check if it's been more than 24 hours since last reset
      return now.difference(lastReset).inHours >= 24;
    } catch (e) {
      LoggerService.error('Failed to check quota reset status', e);
      return false;
    }
  }

  /// Get current configuration (cached)
  GoogleDriveConfig? get currentConfig => _cachedConfig;

  /// Clear cached configuration
  void clearCache() {
    _cachedConfig = null;
    _lastConfigLoad = null;
    LoggerService.debug('Google Drive configuration cache cleared');
  }

  /// Load service account credentials from secure storage
  Future<GoogleDriveConfig> _loadServiceAccountCredentials(
    GoogleDriveConfig config,
  ) async {
    try {
      // In a real implementation, you would load credentials from:
      // 1. Environment variables
      // 2. Secure file storage
      // 3. Key management service
      // 4. Encrypted configuration

      // For now, we'll assume credentials are loaded from environment or secure storage
      // This is a placeholder - implement actual credential loading based on your security requirements

      LoggerService.debug('Loading service account credentials');

      // Example: Load from environment variable
      // final credentialsJson = Platform.environment['GOOGLE_SERVICE_ACCOUNT_CREDENTIALS'];
      // if (credentialsJson != null) {
      //   final credentials = jsonDecode(credentialsJson) as Map<String, dynamic>;
      //   return config.copyWith(serviceAccountCredentials: credentials);
      // }

      // For development, you might load from a secure file
      // For production, use proper key management

      return config;
    } catch (e) {
      LoggerService.error('Failed to load service account credentials', e);
      rethrow;
    }
  }

  /// Create default development configuration
  Future<GoogleDriveConfig> _createDefaultDevelopmentConfig() async {
    try {
      LoggerService.info(
        'Creating default Google Drive configuration for development',
      );

      // Create default configuration data
      final configData = {
        'service_account_email':
            '<EMAIL>',
        'root_folder_id': '1KEh7_66La9HtcZBBHeho1YzIjJuJjeNw',
        'api_quota_limit': 1000000,
        'api_quota_used': 0,
        'last_quota_reset': DateTime.now().toIso8601String(),
        'encryption_enabled': true,
        'backup_enabled': true,
        'environment': 'production',
      };

      // Try to create the configuration record in PocketBase
      try {
        final record = await _pocketBaseService.pb
            .collection('google_drive_config')
            .create(body: configData);

        LoggerService.info(
          'Default development configuration created with ID: ${record.id}',
        );

        // Return the created configuration
        final config = GoogleDriveConfig.fromPocketBase(configData);

        // Cache the configuration
        _cachedConfig = config;
        _lastConfigLoad = DateTime.now();

        return config;
      } catch (e) {
        LoggerService.warning(
          'Failed to create configuration in PocketBase: $e',
        );

        // If PocketBase creation fails, return an in-memory configuration
        LoggerService.info(
          'Using in-memory default configuration for development',
        );

        final config = GoogleDriveConfig.fromPocketBase(configData);

        // Cache the configuration
        _cachedConfig = config;
        _lastConfigLoad = DateTime.now();

        return config;
      }
    } catch (e) {
      LoggerService.error(
        'Failed to create default development configuration',
        e,
      );
      rethrow;
    }
  }

  /// Determine current environment
  String _getCurrentEnvironment() {
    // In a real implementation, this would check:
    // 1. Environment variables
    // 2. Build configuration
    // 3. Runtime flags

    // For now, default to development
    return 'production';
  }
}

/// Exception for Google Drive configuration errors
class GoogleDriveConfigException implements Exception {
  final String message;
  final Object? cause;

  const GoogleDriveConfigException(this.message, [this.cause]);

  @override
  String toString() =>
      'GoogleDriveConfigException: $message${cause != null ? ' (caused by: $cause)' : ''}';
}
