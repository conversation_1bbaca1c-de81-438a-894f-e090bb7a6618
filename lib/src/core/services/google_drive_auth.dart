import 'dart:convert';
import 'dart:io' as io;
import 'package:flutter/services.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:http/http.dart' as http;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/google_drive_config.dart';

/// Service for handling Google Drive authentication
class GoogleDriveAuthService {
  static final GoogleDriveAuthService _instance =
      GoogleDriveAuthService._internal();
  factory GoogleDriveAuthService() => _instance;
  GoogleDriveAuthService._internal();

  final GoogleDriveConfigService _configService = GoogleDriveConfigService();

  http.Client? _authenticatedClient;
  DateTime? _tokenExpiry;
  ServiceAccountCredentials? _credentials;

  /// Google Drive API scopes
  static const List<String> _scopes = [
    drive.DriveApi.driveScope, // Full access to Google Drive
    // Alternative: drive.DriveApi.driveFileScope for restricted access to app-created files only
  ];

  /// Initialize authentication with service account
  Future<void> initialize() async {
    try {
      LoggerService.info('Initializing Google Drive authentication...');

      // Load configuration
      final config = await _configService.loadConfig();

      // Load service account credentials
      _credentials = await _loadServiceAccountCredentials(config);

      // Create authenticated client
      _authenticatedClient = await _createAuthenticatedClient(_credentials!);

      LoggerService.info(
        'Google Drive authentication initialized successfully',
      );
    } catch (e) {
      LoggerService.error(
        'Failed to initialize Google Drive authentication',
        e,
      );
      rethrow;
    }
  }

  /// Get authenticated HTTP client
  Future<http.Client> getAuthenticatedClient() async {
    try {
      // Check if client exists and token is still valid
      if (_authenticatedClient != null && !_isTokenExpired()) {
        return _authenticatedClient!;
      }

      // Re-authenticate if needed
      LoggerService.info('Re-authenticating Google Drive client...');
      await initialize();

      if (_authenticatedClient == null) {
        throw GoogleDriveAuthException('Failed to create authenticated client');
      }

      return _authenticatedClient!;
    } catch (e) {
      LoggerService.error('Failed to get authenticated client', e);
      rethrow;
    }
  }

  /// Check if current token is expired
  bool _isTokenExpired() {
    if (_tokenExpiry == null) return true;

    // Consider token expired if it expires within the next 5 minutes
    final buffer = Duration(minutes: 5);
    return DateTime.now().add(buffer).isAfter(_tokenExpiry!);
  }

  /// Load service account credentials
  Future<ServiceAccountCredentials> _loadServiceAccountCredentials(
    GoogleDriveConfig config,
  ) async {
    try {
      LoggerService.debug('Loading service account credentials...');

      // Method 1: Load from environment variable (recommended for production)
      final credentialsJson =
          io.Platform.environment['GOOGLE_SERVICE_ACCOUNT_CREDENTIALS'];
      if (credentialsJson != null && credentialsJson.isNotEmpty) {
        LoggerService.debug('Loading credentials from environment variable');
        final credentialsMap =
            jsonDecode(credentialsJson) as Map<String, dynamic>;
        return ServiceAccountCredentials.fromJson(credentialsMap);
      }

      // Method 2: Load from file (for development)
      final credentialsFile =
          io.Platform.environment['GOOGLE_SERVICE_ACCOUNT_FILE'];
      if (credentialsFile != null && credentialsFile.isNotEmpty) {
        LoggerService.debug('Loading credentials from file: $credentialsFile');
        final file = io.File(credentialsFile);
        if (await file.exists()) {
          final credentialsContent = await file.readAsString();
          final credentialsMap =
              jsonDecode(credentialsContent) as Map<String, dynamic>;
          return ServiceAccountCredentials.fromJson(credentialsMap);
        }
      }

      // Method 3: Load from configuration (if stored securely)
      if (config.serviceAccountCredentials != null) {
        LoggerService.debug('Loading credentials from configuration');
        return ServiceAccountCredentials.fromJson(
          config.serviceAccountCredentials!,
        );
      }

      // Method 4: Load from assets (recommended for Flutter apps)
      try {
        LoggerService.debug('Loading credentials from assets...');
        final credentialsContent = await rootBundle.loadString(
          'assets/credentials/service-account.json',
        );
        final credentialsMap =
            jsonDecode(credentialsContent) as Map<String, dynamic>;
        return ServiceAccountCredentials.fromJson(credentialsMap);
      } catch (e) {
        LoggerService.debug('Failed to load credentials from assets: $e');
      }

      // Method 5: Default development credentials (fallback)
      final defaultCredentialsPath = 'assets/credentials/service-account.json';
      final defaultFile = io.File(defaultCredentialsPath);
      if (await defaultFile.exists()) {
        LoggerService.debug(
          'Loading credentials from default path: $defaultCredentialsPath',
        );
        final credentialsContent = await defaultFile.readAsString();
        final credentialsMap =
            jsonDecode(credentialsContent) as Map<String, dynamic>;
        return ServiceAccountCredentials.fromJson(credentialsMap);
      }

      throw GoogleDriveAuthException(
        'No service account credentials found. Please set GOOGLE_SERVICE_ACCOUNT_CREDENTIALS environment variable or GOOGLE_SERVICE_ACCOUNT_FILE path.',
      );
    } catch (e) {
      LoggerService.error('Failed to load service account credentials', e);
      rethrow;
    }
  }

  /// Create authenticated HTTP client
  Future<http.Client> _createAuthenticatedClient(
    ServiceAccountCredentials credentials,
  ) async {
    try {
      LoggerService.debug('Creating authenticated HTTP client...');

      // Create authenticated client using service account
      final client = await clientViaServiceAccount(credentials, _scopes);

      // Store token expiry information
      final accessCredentials = client.credentials;
      _tokenExpiry = accessCredentials.accessToken.expiry;
      LoggerService.debug('Token expires at: $_tokenExpiry');

      LoggerService.debug('Authenticated HTTP client created successfully');
      return client;
    } catch (e) {
      LoggerService.error('Failed to create authenticated client', e);
      rethrow;
    }
  }

  /// Validate authentication by making a test API call
  Future<bool> validateAuthentication() async {
    try {
      LoggerService.debug('Validating Google Drive authentication...');

      final client = await getAuthenticatedClient();
      final driveApi = drive.DriveApi(client);

      // Make a simple API call to validate authentication
      final about = await driveApi.about.get($fields: 'user,storageQuota');

      LoggerService.info('Authentication validated successfully');
      LoggerService.debug(
        'Authenticated as: ${about.user?.displayName} (${about.user?.emailAddress})',
      );

      return true;
    } catch (e) {
      LoggerService.error('Authentication validation failed', e);
      return false;
    }
  }

  /// Get current user information
  Future<Map<String, dynamic>?> getCurrentUserInfo() async {
    try {
      final client = await getAuthenticatedClient();
      final driveApi = drive.DriveApi(client);

      final about = await driveApi.about.get($fields: 'user,storageQuota');

      return {
        'displayName': about.user?.displayName,
        'emailAddress': about.user?.emailAddress,
        'photoLink': about.user?.photoLink,
        'storageQuota': {
          'limit': about.storageQuota?.limit,
          'usage': about.storageQuota?.usage,
          'usageInDrive': about.storageQuota?.usageInDrive,
        },
      };
    } catch (e) {
      LoggerService.error('Failed to get current user info', e);
      return null;
    }
  }

  /// Refresh authentication token
  Future<void> refreshToken() async {
    try {
      LoggerService.debug('Refreshing authentication token...');

      if (_credentials == null) {
        throw GoogleDriveAuthException(
          'No credentials available for token refresh',
        );
      }

      // Close existing client
      _authenticatedClient?.close();

      // Create new authenticated client
      _authenticatedClient = await _createAuthenticatedClient(_credentials!);

      LoggerService.debug('Authentication token refreshed successfully');
    } catch (e) {
      LoggerService.error('Failed to refresh authentication token', e);
      rethrow;
    }
  }

  /// Close authentication client
  void close() {
    _authenticatedClient?.close();
    _authenticatedClient = null;
    _tokenExpiry = null;
    _credentials = null;
    LoggerService.debug('Google Drive authentication client closed');
  }

  /// Check if authentication is initialized
  bool get isInitialized =>
      _authenticatedClient != null && _credentials != null;

  /// Check if authentication is valid
  bool get isValid => isInitialized && !_isTokenExpired();

  /// Get token expiry time
  DateTime? get tokenExpiry => _tokenExpiry;

  /// Get service account email
  String? get serviceAccountEmail => _credentials?.email;
}

/// Exception for Google Drive authentication errors
class GoogleDriveAuthException implements Exception {
  final String message;
  final Object? cause;

  const GoogleDriveAuthException(this.message, [this.cause]);

  @override
  String toString() =>
      'GoogleDriveAuthException: $message${cause != null ? ' (caused by: $cause)' : ''}';
}

/// Authentication status enumeration
enum GoogleDriveAuthStatus {
  notInitialized,
  initializing,
  authenticated,
  expired,
  failed,
}

/// Authentication info model
class GoogleDriveAuthInfo {
  final GoogleDriveAuthStatus status;
  final String? serviceAccountEmail;
  final DateTime? tokenExpiry;
  final String? error;
  final Map<String, dynamic>? userInfo;

  const GoogleDriveAuthInfo({
    required this.status,
    this.serviceAccountEmail,
    this.tokenExpiry,
    this.error,
    this.userInfo,
  });

  bool get isAuthenticated => status == GoogleDriveAuthStatus.authenticated;
  bool get isExpired => status == GoogleDriveAuthStatus.expired;
  bool get hasFailed => status == GoogleDriveAuthStatus.failed;

  @override
  String toString() =>
      'GoogleDriveAuthInfo(status: $status, email: $serviceAccountEmail)';
}
