import 'package:logger/logger.dart';

/// Centralized logger service for the application
/// Provides consistent logging configuration across the entire codebase
class LoggerService {
  static Logger? _instance;

  /// Get the singleton logger instance
  static Logger get instance {
    _instance ??= Logger(
      printer: PrettyPrinter(
        methodCount: 0,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
      ),
    );
    return _instance!;
  }

  /// Create a logger with custom configuration for specific use cases
  static Logger createCustomLogger({
    int methodCount = 0,
    int errorMethodCount = 8,
    int lineLength = 120,
    bool colors = true,
    bool printEmojis = true,
  }) {
    return Logger(
      printer: PrettyPrinter(
        methodCount: methodCount,
        errorMethodCount: errorMethodCount,
        lineLength: lineLength,
        colors: colors,
        printEmojis: printEmojis,
      ),
    );
  }

  /// Convenience methods for common logging operations
  static void info(String message) => instance.i(message);
  static void debug(String message) => instance.d(message);
  static void warning(String message) => instance.w(message);
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    instance.e(message, error: error, stackTrace: stackTrace);
  }

  static void fatal(String message, [Object? error, StackTrace? stackTrace]) {
    instance.f(message, error: error, stackTrace: stackTrace);
  }

  static void trace(String message) => instance.t(message);
}
