import 'dart:async';
import 'dart:typed_data';
import '../models/cache_entry.dart';
import '../models/cache_statistics.dart';
import '../cache/cache_storage.dart';
import '../cache/cache_policy.dart';
import '../utils/cache_utils.dart';
import 'logger_service.dart';
import 'performance_monitoring_service.dart';

/// Main document cache service for intelligent caching of Google Drive data
class DocumentCacheService {
  static final DocumentCacheService _instance =
      DocumentCacheService._internal();
  factory DocumentCacheService() => _instance;
  DocumentCacheService._internal();

  // Storage layers
  late final MemoryCacheStorage _memoryStorage;
  late final DiskCacheStorage _diskStorage;
  final PerformanceMonitoringService _performanceService =
      PerformanceMonitoringService();

  // Cache policies by type
  final Map<CacheType, CachePolicy> _policies = {};

  // Statistics tracking
  final Map<CacheType, CacheTypeStatistics> _typeStatistics = {};
  int _totalHits = 0;
  int _totalMisses = 0;
  int _totalEvictions = 0;
  int _totalInvalidations = 0;
  final List<double> _accessTimes = [];

  // Cleanup timer
  Timer? _cleanupTimer;

  bool _isInitialized = false;

  /// Initialize the cache service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggerService.info('Initializing Document Cache Service...');

      // Initialize storage layers
      _memoryStorage = MemoryCacheStorage();
      _diskStorage = DiskCacheStorage();

      await _memoryStorage.initialize();
      await _diskStorage.initialize();

      // Initialize cache policies
      _initializePolicies();

      // Initialize statistics
      _initializeStatistics();

      // Start cleanup timer
      _startCleanupTimer();

      _isInitialized = true;
      LoggerService.info('Document Cache Service initialized successfully');
    } catch (e) {
      LoggerService.error('Failed to initialize Document Cache Service', e);
      rethrow;
    }
  }

  /// Initialize cache policies for different types
  void _initializePolicies() {
    _policies[CacheType.url] = CachePolicy.forUrls();
    _policies[CacheType.metadata] = CachePolicy.forMetadata();
    _policies[CacheType.content] = CachePolicy.forContent();
    _policies[CacheType.permission] = CachePolicy.forPermissions();
    _policies[CacheType.folder] = CachePolicy.forMetadata();
    _policies[CacheType.search] = CachePolicy.forUrls();
  }

  /// Initialize statistics tracking
  void _initializeStatistics() {
    for (final type in CacheType.values) {
      _typeStatistics[type] = CacheTypeStatistics.empty(type);
    }
  }

  /// Cache a URL with expiration
  Future<void> cacheUrl(String fileId, String url, {Duration? ttl}) async {
    await _cacheData(
      key: CacheUtils.generateKey(fileId, CacheType.url),
      data: url,
      type: CacheType.url,
      ttl: ttl,
    );
  }

  /// Get cached URL
  Future<String?> getCachedUrl(String fileId) async {
    final key = CacheUtils.generateKey(fileId, CacheType.url);
    final entry = await _getData<String>(key, CacheType.url);
    return entry;
  }

  /// Cache file metadata
  Future<void> cacheMetadata(
    String fileId,
    Map<String, dynamic> metadata, {
    Duration? ttl,
  }) async {
    await _cacheData(
      key: CacheUtils.generateKey(fileId, CacheType.metadata),
      data: metadata,
      type: CacheType.metadata,
      ttl: ttl,
    );
  }

  /// Get cached metadata
  Future<Map<String, dynamic>?> getCachedMetadata(String fileId) async {
    final key = CacheUtils.generateKey(fileId, CacheType.metadata);
    return await _getData<Map<String, dynamic>>(key, CacheType.metadata);
  }

  /// Cache file content (for small files)
  Future<void> cacheContent(
    String fileId,
    Uint8List content, {
    Duration? ttl,
  }) async {
    // Only cache small files
    if (content.length > 1024 * 1024) {
      // 1MB limit
      LoggerService.debug(
        'Skipping content cache for large file: $fileId (${content.length} bytes)',
      );
      return;
    }

    await _cacheData(
      key: CacheUtils.generateKey(fileId, CacheType.content),
      data: content,
      type: CacheType.content,
      ttl: ttl,
    );
  }

  /// Get cached content
  Future<Uint8List?> getCachedContent(String fileId) async {
    final key = CacheUtils.generateKey(fileId, CacheType.content);
    return await _getData<Uint8List>(key, CacheType.content);
  }

  /// Cache permissions
  Future<void> cachePermissions(
    String fileId,
    Map<String, dynamic> permissions, {
    Duration? ttl,
  }) async {
    await _cacheData(
      key: CacheUtils.generateKey(fileId, CacheType.permission),
      data: permissions,
      type: CacheType.permission,
      ttl: ttl,
    );
  }

  /// Get cached permissions
  Future<Map<String, dynamic>?> getCachedPermissions(String fileId) async {
    final key = CacheUtils.generateKey(fileId, CacheType.permission);
    return await _getData<Map<String, dynamic>>(key, CacheType.permission);
  }

  /// Cache folder contents
  Future<void> cacheFolderContents(
    String? folderId,
    String? query,
    List<Map<String, dynamic>> contents, {
    Duration? ttl,
  }) async {
    final key = CacheUtils.generateFolderKey(folderId, query);
    await _cacheData(
      key: key,
      data: contents,
      type: CacheType.folder,
      ttl: ttl,
    );
  }

  /// Get cached folder contents
  Future<List<Map<String, dynamic>>?> getCachedFolderContents(
    String? folderId,
    String? query,
  ) async {
    final key = CacheUtils.generateFolderKey(folderId, query);
    return await _getData<List<Map<String, dynamic>>>(key, CacheType.folder);
  }

  /// Cache search results
  Future<void> cacheSearchResults(
    String query,
    int? maxResults,
    List<Map<String, dynamic>> results, {
    Duration? ttl,
  }) async {
    final key = CacheUtils.generateSearchKey(query, maxResults);
    await _cacheData(key: key, data: results, type: CacheType.search, ttl: ttl);
  }

  /// Get cached search results
  Future<List<Map<String, dynamic>>?> getCachedSearchResults(
    String query,
    int? maxResults,
  ) async {
    final key = CacheUtils.generateSearchKey(query, maxResults);
    return await _getData<List<Map<String, dynamic>>>(key, CacheType.search);
  }

  /// Generic method to cache data
  Future<void> _cacheData<T>({
    required String key,
    required T data,
    required CacheType type,
    Duration? ttl,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      final startTime = DateTime.now();

      // Check if data should be cached
      if (!CacheUtils.shouldCache(data, type)) {
        LoggerService.debug(
          'Skipping cache for data that should not be cached: $key',
        );
        return;
      }

      // Calculate size and TTL
      final sizeBytes = CacheUtils.calculateSize(data);
      final effectiveTtl =
          ttl ?? _policies[type]?.getTtlForType(type) ?? type.defaultTtl;

      // Create cache entry
      final entry = CacheEntry.create(
        data: data,
        key: key,
        type: type,
        sizeBytes: sizeBytes,
        ttl: effectiveTtl,
      );

      // Store in memory cache first
      await _memoryStorage.store(key, entry);

      // Store in disk cache if persistence is enabled
      final policy = _policies[type];
      if (policy?.enablePersistence == true) {
        await _diskStorage.store(key, entry);
      }

      // Update statistics
      _updateStatistics(
        type,
        hit: false,
        accessTimeMs:
            DateTime.now().difference(startTime).inMilliseconds.toDouble(),
      );

      LoggerService.debug(
        'Cached data: $key (${CacheUtils.formatSize(sizeBytes)})',
      );
    } catch (e) {
      LoggerService.error('Failed to cache data: $key', e);
    }
  }

  /// Generic method to get cached data
  Future<T?> _getData<T>(String key, CacheType type) async {
    if (!_isInitialized) await initialize();

    return await _performanceService.trackOperation(
      'cache_access',
      () => _getDataInternal<T>(key, type),
      metadata: {'cacheType': type.name, 'key': key},
    );
  }

  /// Internal cache data retrieval
  Future<T?> _getDataInternal<T>(String key, CacheType type) async {
    final startTime = DateTime.now();

    try {
      // Try memory cache first
      CacheEntry? entry = await _memoryStorage.retrieve(key);

      if (entry == null) {
        // Try disk cache
        entry = await _diskStorage.retrieve(key);
        if (entry != null) {
          // Promote to memory cache
          await _memoryStorage.store(key, entry);
        }
      }

      final accessTime =
          DateTime.now().difference(startTime).inMilliseconds.toDouble();

      if (entry != null && entry.isValid) {
        _updateStatistics(type, hit: true, accessTimeMs: accessTime);
        LoggerService.debug('Cache hit: $key');
        return entry.data as T;
      } else {
        _updateStatistics(type, hit: false, accessTimeMs: accessTime);
        LoggerService.debug('Cache miss: $key');
        return null;
      }
    } catch (e) {
      LoggerService.error('Failed to get cached data: $key', e);
      _updateStatistics(
        type,
        hit: false,
        accessTimeMs:
            DateTime.now().difference(startTime).inMilliseconds.toDouble(),
      );
      return null;
    }
  }

  /// Invalidate cache for a specific file
  Future<void> invalidateCache(String fileId) async {
    if (!_isInitialized) await initialize();

    try {
      final keys = [
        CacheUtils.generateKey(fileId, CacheType.url),
        CacheUtils.generateKey(fileId, CacheType.metadata),
        CacheUtils.generateKey(fileId, CacheType.content),
        CacheUtils.generateKey(fileId, CacheType.permission),
      ];

      for (final key in keys) {
        await _memoryStorage.remove(key);
        await _diskStorage.remove(key);
      }

      _totalInvalidations++;
      LoggerService.debug('Invalidated cache for file: $fileId');
    } catch (e) {
      LoggerService.error('Failed to invalidate cache for file: $fileId', e);
    }
  }

  /// Clear all cache
  Future<void> clearCache() async {
    if (!_isInitialized) await initialize();

    try {
      await _memoryStorage.clear();
      await _diskStorage.clear();

      // Reset statistics
      _totalHits = 0;
      _totalMisses = 0;
      _totalEvictions = 0;
      _totalInvalidations = 0;
      _accessTimes.clear();
      _initializeStatistics();

      LoggerService.info('Cleared all cache data');
    } catch (e) {
      LoggerService.error('Failed to clear cache', e);
    }
  }

  /// Get cache statistics
  CacheStatistics getStatistics() {
    final memorySize = _memoryStorage.entryCount;
    final avgAccessTime =
        _accessTimes.isEmpty
            ? 0.0
            : _accessTimes.reduce((a, b) => a + b) / _accessTimes.length;

    return CacheStatistics(
      totalHits: _totalHits,
      totalMisses: _totalMisses,
      totalEntries: memorySize,
      totalSizeBytes: 0, // Will be calculated asynchronously if needed
      typeStatistics: Map.from(_typeStatistics),
      lastUpdated: DateTime.now(),
      averageAccessTimeMs: avgAccessTime,
      memoryUsageBytes: 0, // Will be calculated asynchronously if needed
      diskUsageBytes: 0, // Will be calculated asynchronously if needed
      totalEvictions: _totalEvictions,
      totalInvalidations: _totalInvalidations,
    );
  }

  /// Update statistics for cache operations
  void _updateStatistics(
    CacheType type, {
    required bool hit,
    required double accessTimeMs,
  }) {
    if (hit) {
      _totalHits++;
    } else {
      _totalMisses++;
    }

    _accessTimes.add(accessTimeMs);
    if (_accessTimes.length > 1000) {
      _accessTimes.removeAt(0); // Keep only recent access times
    }

    final currentStats = _typeStatistics[type]!;
    _typeStatistics[type] = currentStats.copyWith(
      hits: hit ? currentStats.hits + 1 : currentStats.hits,
      misses: hit ? currentStats.misses : currentStats.misses + 1,
      averageAccessTimeMs: accessTimeMs,
    );
  }

  /// Start cleanup timer
  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(const Duration(minutes: 30), (_) {
      _performCleanup();
    });
  }

  /// Perform cache cleanup
  Future<void> _performCleanup() async {
    try {
      LoggerService.debug('Starting cache cleanup...');

      // Cleanup memory cache
      await _cleanupMemoryCache();

      // Cleanup disk cache
      await _diskStorage.cleanupExpired();

      LoggerService.debug('Cache cleanup completed');
    } catch (e) {
      LoggerService.error('Cache cleanup failed', e);
    }
  }

  /// Cleanup memory cache based on policies
  Future<void> _cleanupMemoryCache() async {
    // This is a simplified cleanup - in a full implementation,
    // we would implement proper LRU/LFU eviction based on policies
    final keys = await _memoryStorage.getAllKeys();
    int removedCount = 0;

    for (final key in keys) {
      final entry = await _memoryStorage.retrieve(key);
      if (entry == null || entry.isExpired) {
        await _memoryStorage.remove(key);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      _totalEvictions += removedCount;
      LoggerService.debug(
        'Cleaned up $removedCount expired entries from memory cache',
      );
    }
  }

  /// Dispose the cache service
  Future<void> dispose() async {
    _cleanupTimer?.cancel();
    await _memoryStorage.dispose();
    await _diskStorage.dispose();
    _isInitialized = false;
    LoggerService.debug('Document Cache Service disposed');
  }
}
