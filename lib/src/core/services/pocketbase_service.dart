import 'package:pocketbase/pocketbase.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Added Riverpod import
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert'; // For jsonEncode and jsonDecode
import 'dart:io'; // For SocketException
import 'package:http/http.dart' as http; // Import for MultipartFile
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';

// Global provider for the PocketBase client instance
final pocketBaseClientProvider = Provider<PocketBase>((ref) {
  return PocketBaseService().pb;
});

class PocketBaseService {
  // SharedPreferences keys
  static const String _prefsAuthTokenKey = 'pb_auth_token';
  static const String _prefsAuthModelKey = 'pb_auth_model';

  // Private constructor
  PocketBaseService._internal() {
    // Listen to auth changes to persist/clear them
    client.authStore.onChange.listen((_) {
      _persistAuthStore();
    });
  }

  // Static instance
  static final PocketBaseService _instance = PocketBaseService._internal();

  // Factory constructor to return the same instance
  factory PocketBaseService() {
    return _instance;
  }

  // PocketBase client instance
  // Ensure this URL is correct for your setup.
  final PocketBase client = PocketBase(
    'https://3paydb-new-production.up.railway.app',
  );
  // final PocketBase client = PocketBase('http://127.0.0.1:8090');

  // Public getter for the PocketBase client instance
  PocketBase get pb => client;

  // Getter for easy access to the client's authStore
  AuthStore get authStore => client.authStore;

  // Getter for easy access to UsersService (Removed due to UsersService not found and client.users not defined errors for PocketBase v0.22.0 as per analyzer)
  // UsersService get usersService => client.users;

  // Method to initialize and load auth state from SharedPreferences
  Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(_prefsAuthTokenKey);
    final modelString = prefs.getString(_prefsAuthModelKey);

    if (token != null &&
        token.isNotEmpty &&
        modelString != null &&
        modelString.isNotEmpty) {
      try {
        final modelJson = jsonDecode(modelString) as Map<String, dynamic>;
        final model = RecordModel.fromJson(modelJson);
        client.authStore.save(token, model);
      } catch (e) {
        LoggerService.error('Error loading auth from prefs', e);
        await prefs.remove(_prefsAuthTokenKey);
        await prefs.remove(_prefsAuthModelKey);
        client.authStore.clear();
      }
    }
  }

  // Persist auth store to SharedPreferences
  Future<void> _persistAuthStore() async {
    final prefs = await SharedPreferences.getInstance();
    if (client.authStore.isValid && client.authStore.record != null) {
      await prefs.setString(_prefsAuthTokenKey, client.authStore.token);
      final record = client.authStore.record;
      if (record != null) {
        await prefs.setString(_prefsAuthModelKey, jsonEncode(record.toJson()));
      } else {
        await prefs.remove(_prefsAuthModelKey);
      }
    } else {
      await prefs.remove(_prefsAuthTokenKey);
      await prefs.remove(_prefsAuthModelKey);
    }
  }

  // Check if a user is currently authenticated
  bool get isSignedIn => authStore.isValid;

  // Get the current authenticated user model
  RecordModel? get currentUser => authStore.record;

  // Sign In
  Future<RecordAuth> signIn(String email, String password) async {
    try {
      final authData = await client
          .collection('users')
          .authWithPassword(email, password);

      // Initialize FCM token after successful sign in
      try {
        await FirebaseApiService.refreshToken();
        LoggerService.info('FCM token refreshed after sign in');
      } catch (e) {
        LoggerService.warning('Failed to refresh FCM token after sign in: $e');
        // Don't fail the sign in process if FCM token refresh fails
      }

      return authData;
    } catch (e) {
      LoggerService.error('Sign In Error', e);
      rethrow;
    }
  }

  Future<RecordModel> signUp({
    required String email,
    required String password,
    required String passwordConfirm,
    required String userType,
    required String name,
    String? firstName,
    String? lastName,
    String? level,
    String? status,
    Map<String, dynamic> customData = const {},
  }) async {
    try {
      // Prepare user data for auth collection creation
      // Note: Do not include 'id' or 'tokenKey' as these are auto-generated by PocketBase for auth collections
      final body = <String, dynamic>{
        'email': email,
        'password': password,
        'passwordConfirm': passwordConfirm,
        'user_type': userType,
        'name': name,
        'emailVisibility': false,
        'verified': false,
        if (firstName != null) 'first_name': firstName,
        if (lastName != null) 'last_name': lastName,
        if (level != null) 'level': level,
        if (status != null) 'status': status,
        ...customData,
      };

      LoggerService.info('Creating user with fields: ${body.keys.toList()}');
      LoggerService.info('User type: ${body['user_type']}');

      // For auth collections, use the standard create method but ensure we don't pass system fields
      // PocketBase will auto-generate 'id' and 'tokenKey' for auth collections
      final record = await client.collection('users').create(body: body);

      LoggerService.info('User created successfully: ${record.id}');

      // Now authenticate the user to populate auth store
      try {
        await client.collection('users').authWithPassword(email, password);
        LoggerService.info('User authenticated after creation');

        // Initialize FCM token after successful sign up and auth
        try {
          await FirebaseApiService.refreshToken();
          LoggerService.info('FCM token refreshed after sign up');
        } catch (e) {
          LoggerService.warning(
            'Failed to refresh FCM token after sign up: $e',
          );
          // Don't fail the sign up process if FCM token refresh fails
        }
      } catch (authError) {
        LoggerService.warning(
          'Failed to authenticate after user creation: $authError',
        );
        // User was created but couldn't be authenticated
        // You might want to handle this case differently
      }

      return record;
    } catch (e) {
      LoggerService.error('Sign Up Error', e);

      // Map the error to a user-friendly message
      final friendlyError = PocketBaseService.mapPocketBaseError(e);
      LoggerService.info(
        'Mapped error: ${friendlyError.title} - ${friendlyError.message}',
      );

      rethrow;
    }
  }

  Future<void> signOut() async {
    // Clear FCM token before signing out
    try {
      await FirebaseApiService.clearToken();
      LoggerService.info('FCM token cleared on sign out');
    } catch (e) {
      LoggerService.warning('Failed to clear FCM token on sign out: $e');
      // Continue with sign out even if FCM token clearing fails
    }

    authStore.clear();
  }

  /// Handle session expiration with FCM token cleanup
  Future<void> handleSessionExpiration() async {
    try {
      LoggerService.info('Handling session expiration');

      // Clear FCM token for expired session
      await FirebaseApiService.clearToken();
      LoggerService.info('FCM token cleared for expired session');
    } catch (e) {
      LoggerService.warning(
        'Failed to clear FCM token for expired session: $e',
      );
    }

    // Clear auth store
    authStore.clear();
  }

  /// Refresh FCM token for current authenticated user
  Future<void> refreshFCMToken() async {
    if (!isSignedIn) {
      LoggerService.warning(
        'Cannot refresh FCM token - user not authenticated',
      );
      return;
    }

    try {
      await FirebaseApiService.refreshToken();
      LoggerService.info('FCM token refreshed successfully');
    } catch (e) {
      LoggerService.error('Failed to refresh FCM token: $e');
      rethrow;
    }
  }

  // Password Management Methods

  /// Request password reset email
  Future<void> requestPasswordReset(String email) async {
    try {
      await client.collection('users').requestPasswordReset(email);
      LoggerService.info('Password reset requested for: $email');
    } catch (e) {
      LoggerService.error('Password reset request error', e);
      rethrow;
    }
  }

  /// Confirm password reset with token
  Future<void> confirmPasswordReset({
    required String token,
    required String password,
    required String passwordConfirm,
  }) async {
    try {
      await client
          .collection('users')
          .confirmPasswordReset(token, password, passwordConfirm);
      LoggerService.info('Password reset confirmed successfully');
    } catch (e) {
      LoggerService.error('Password reset confirmation error', e);
      rethrow;
    }
  }

  /// Request email verification
  Future<void> requestEmailVerification(String email) async {
    try {
      await client.collection('users').requestVerification(email);
      LoggerService.info('Email verification requested for: $email');
    } catch (e) {
      LoggerService.error('Email verification request error', e);
      rethrow;
    }
  }

  /// Confirm email verification with token
  Future<void> confirmEmailVerification(String token) async {
    try {
      await client.collection('users').confirmVerification(token);
      LoggerService.info('Email verification confirmed successfully');
    } catch (e) {
      LoggerService.error('Email verification confirmation error', e);
      rethrow;
    }
  }

  /// Change password for authenticated user
  Future<void> changePassword({
    required String oldPassword,
    required String newPassword,
    required String newPasswordConfirm,
  }) async {
    try {
      if (!isSignedIn) {
        throw Exception('User not authenticated');
      }

      final userId = currentUser!.id;

      // Update password with old password verification
      await client
          .collection('users')
          .update(
            userId,
            body: {
              'oldPassword': oldPassword,
              'password': newPassword,
              'passwordConfirm': newPasswordConfirm,
            },
          );

      LoggerService.info('Password changed successfully for user: $userId');
    } catch (e) {
      LoggerService.error('Password change error', e);
      rethrow;
    }
  }

  /// Request OTP for email verification
  Future<void> requestOTP(String email) async {
    try {
      await client.collection('users').requestVerification(email);
      LoggerService.info('OTP requested for: $email');
    } catch (e) {
      LoggerService.error('OTP request error', e);
      rethrow;
    }
  }

  /// Verify OTP token
  Future<bool> verifyOTP(String token) async {
    try {
      await client.collection('users').confirmVerification(token);
      LoggerService.info('OTP verified successfully');
      return true;
    } catch (e) {
      LoggerService.error('OTP verification error', e);
      return false;
    }
  }

  /// Opt out user account
  Future<void> optOutAccount() async {
    try {
      if (!isSignedIn) {
        throw Exception('User not authenticated');
      }

      final userId = currentUser!.id;

      await client.collection('users').update(userId, body: {'opt_out': true});

      LoggerService.info('Account opted out successfully for user: $userId');
    } catch (e) {
      LoggerService.error('Account opt-out error', e);
      rethrow;
    }
  }

  Future<RecordModel> createSolicitorProfile({
    required String userId,
    required String lawFirmName,
    required String solicitorName,
    required String position,
    required String contactNumber,
    required String firmAddress,
    required String firmRegistrationNumber,
    String puStatus = 'pending',
  }) async {
    try {
      final body = <String, dynamic>{
        "user_id": userId,
        "law_firm_name": lawFirmName,
        "solicitor_name": solicitorName,
        "position": position,
        "contact_number": contactNumber,
        "firm_address": firmAddress,
        "firm_registration_number": firmRegistrationNumber,
        "pu_status": puStatus,
      };
      final record = await client
          .collection('solicitor_profiles')
          .create(body: body);
      return record;
    } catch (e) {
      LoggerService.error('Create Solicitor Profile Error', e);
      rethrow;
    }
  }

  Future<RecordModel> createCoFunderProfile({
    required String userId,
    int currentLevel = 1,
  }) async {
    try {
      final body = <String, dynamic>{
        "user_id": userId,
        "current_level": currentLevel,
        "aml_kyc_status": "pending",
        "read_educational_content": [],
        "investment_preferences": {},
        "notification_preferences": {},
        "preferred_sectors": [],
      };
      final record = await client
          .collection('co_funder_profiles')
          .create(body: body);
      return record;
    } catch (e) {
      LoggerService.error('Create CoFunder Profile Error', e);
      rethrow;
    }
  }

  Future<RecordModel> createRecord({
    required String collectionName,
    required Map<String, dynamic> data,
    Map<String, dynamic> query = const {},
    List<http.MultipartFile> files = const [],
  }) async {
    try {
      final record = await client
          .collection(collectionName)
          .create(body: data, query: query, files: files);
      return record;
    } catch (e) {
      LoggerService.error('Create Record Error in $collectionName', e);
      rethrow;
    }
  }

  Future<RecordModel> updateRecord({
    required String collectionName,
    required String recordId,
    required Map<String, dynamic> data,
    Map<String, dynamic> query = const {},
    List<http.MultipartFile> files = const [],
  }) async {
    try {
      final record = await client
          .collection(collectionName)
          .update(recordId, body: data, query: query, files: files);
      return record;
    } catch (e) {
      LoggerService.error(
        'Update Record Error in $collectionName (ID: $recordId)',
        e,
      );
      rethrow;
    }
  }

  Future<List<RecordModel>> getFullList({
    required String collectionName,
    int batch = 200,
    String? filter,
    String? sort,
  }) async {
    try {
      final records = await client
          .collection(collectionName)
          .getFullList(batch: batch, filter: filter, sort: sort);
      return records;
    } catch (e) {
      LoggerService.error('Get Full List Error from $collectionName', e);
      rethrow;
    }
  }

  /// Get paginated list of records from a collection
  Future<ResultList<RecordModel>> getList({
    required String collectionName,
    int page = 1,
    int perPage = 30,
    String? filter,
    String? sort,
  }) async {
    try {
      final result = await client
          .collection(collectionName)
          .getList(page: page, perPage: perPage, filter: filter, sort: sort);
      return result;
    } catch (e) {
      LoggerService.error('Get List Error from $collectionName', e);
      rethrow;
    }
  }

  Future<RecordModel> getOne({
    required String collectionName,
    required String recordId,
  }) async {
    try {
      final record = await client.collection(collectionName).getOne(recordId);
      return record;
    } catch (e) {
      LoggerService.error(
        'Get One Record Error from $collectionName (ID: $recordId)',
        e,
      );
      rethrow;
    }
  }

  Future<void> deleteRecord({
    required String collectionName,
    required String recordId,
  }) async {
    try {
      await client.collection(collectionName).delete(recordId);
    } catch (e) {
      LoggerService.error(
        'Delete Record Error in $collectionName (ID: $recordId)',
        e,
      );
      rethrow;
    }
  }

  // Method to upload a file and return its ID and filename
  // Assumes a collection (e.g., 'generic_files') exists for storing these files
  // and has at least a 'file_upload' field of type 'file'.
  Future<Map<String, String>> uploadFileAndGetId({
    required String
    targetCollectionName, // The collection where the file record will be created
    required http.MultipartFile multipartFile,
    Map<String, String> body =
        const {}, // Optional: other fields for the file record
  }) async {
    try {
      final record = await client
          .collection(targetCollectionName)
          .create(
            body:
                body, // e.g., {'original_filename': multipartFile.filename, 'uploader': currentUser?.id}
            files: [multipartFile],
          );
      // Assuming the filename is stored in a field named 'filename' on the record,
      // or can be derived. PocketBase file fields store the filename.
      // The 'file' field itself in the response for a record with a file field contains the filename.
      // Let's assume the file field in `targetCollectionName` is named 'file_upload_field'.
      // String filename = record.getStringValue('file_upload_field');
      // If not, multipartFile.filename is a good fallback.
      String filename =
          multipartFile.filename ??
          record.getStringValue(
            'filename',
          ); // Adjust if filename field is different

      if (filename.isEmpty && record.data.containsKey(multipartFile.field)) {
        // PocketBase often returns the filename as the value of the field key used in MultipartFile
        filename = record.data[multipartFile.field].toString();
      }

      return {'id': record.id, 'filename': filename};
    } catch (e) {
      LoggerService.error('Upload File Error to $targetCollectionName', e);
      rethrow;
    }
  }

  /// Maps PocketBase errors to user-friendly messages
  /// This method provides centralized error handling for consistent user experience
  static UserFriendlyError mapPocketBaseError(dynamic error) {
    // Log the original error for debugging
    LoggerService.error('PocketBase Error Occurred', error);

    // Handle different types of errors
    if (error is ClientException) {
      return _mapClientException(error);
    } else if (error is SocketException) {
      return UserFriendlyError(
        title: 'Connection Problem',
        message:
            'No internet connection. Please check your network and try again.',
        isRetryable: true,
      );
    } else if (error.toString().toLowerCase().contains('timeout')) {
      return UserFriendlyError(
        title: 'Connection Timeout',
        message:
            'The request took too long. Please check your internet connection and try again.',
        isRetryable: true,
      );
    } else {
      return UserFriendlyError(
        title: 'Unexpected Error',
        message: 'An unexpected error occurred. Please try again.',
        isRetryable: true,
      );
    }
  }

  /// Maps ClientException (PocketBase specific errors) to user-friendly messages
  static UserFriendlyError _mapClientException(ClientException error) {
    final statusCode = error.statusCode;
    final response = error.response;

    // Extract error message from response
    String? errorMessage;
    try {
      errorMessage = response['message']?.toString();

      // Check for validation errors
      if (response.containsKey('data') && response['data'] is Map) {
        final data = response['data'] as Map<String, dynamic>;
        if (data.isNotEmpty) {
          // Check for system field errors (id, tokenKey, etc.)
          final systemFieldErrors = ['id', 'tokenKey', 'created', 'updated'];
          final hasSystemFieldError = data.keys.any(
            (key) => systemFieldErrors.contains(key),
          );

          if (hasSystemFieldError) {
            return UserFriendlyError(
              title: 'Configuration Error',
              message:
                  'There\'s a configuration issue with user registration. Please contact support.',
              isRetryable: false,
            );
          }

          // Check for specific email uniqueness error first
          if (data.containsKey('email') && data['email'] is Map) {
            final emailError = data['email'] as Map;
            if (emailError.containsKey('code') &&
                emailError['code'] == 'validation_not_unique') {
              return UserFriendlyError(
                title: 'Email Already Registered',
                message:
                    'This email address is already registered. Please try signing in instead.',
                isRetryable: false,
              );
            }
          }

          // Extract field-specific validation errors with improved messaging
          final fieldErrors = <String>[];
          data.forEach((field, errors) {
            if (errors is Map && errors.containsKey('message')) {
              String errorMessage = errors['message'].toString();
              String errorCode = errors['code']?.toString() ?? '';

              // Improve specific field error messages
              if (field == 'email') {
                if (errorCode == 'validation_not_unique') {
                  errorMessage = 'Email address is already registered';
                } else if (errorCode == 'validation_is_email') {
                  errorMessage = 'Please enter a valid email address';
                } else if (errorCode == 'validation_required') {
                  errorMessage = 'Email address is required';
                }
              } else if (field == 'password') {
                if (errorCode == 'validation_length_out_of_range') {
                  errorMessage = 'Password must be at least 8 characters long';
                } else if (errorCode == 'validation_required') {
                  errorMessage = 'Password is required';
                }
              } else if (field == 'passwordConfirm') {
                if (errorCode == 'validation_required') {
                  errorMessage = 'Password confirmation is required';
                } else {
                  errorMessage = 'Password confirmation does not match';
                }
              } else if (errorCode == 'validation_required') {
                errorMessage =
                    '${_capitalizeFirst(field.replaceAll('_', ' '))} is required';
              }

              fieldErrors.add(errorMessage);
            }
          });

          if (fieldErrors.isNotEmpty) {
            return UserFriendlyError(
              title: 'Validation Error',
              message:
                  fieldErrors.length == 1
                      ? fieldErrors.first
                      : 'Please check the following:\n• ${fieldErrors.join('\n• ')}',
              isRetryable: false,
            );
          }
        }
      }
    } catch (e) {
      // If response parsing fails, continue with status code mapping
      LoggerService.error('Error parsing response in error mapping', e);
    }

    // Map based on status code
    switch (statusCode) {
      case 400:
        // Check for specific error messages
        if (errorMessage != null) {
          if (errorMessage.toLowerCase().contains('email') &&
              errorMessage.toLowerCase().contains('already')) {
            return UserFriendlyError(
              title: 'Email Already Registered',
              message:
                  'This email address is already registered. Please try signing in instead.',
              isRetryable: false,
            );
          }
          if (errorMessage.toLowerCase().contains('password')) {
            return UserFriendlyError(
              title: 'Password Error',
              message: 'Please check your password and try again.',
              isRetryable: false,
            );
          }
          if (errorMessage.toLowerCase().contains('validation') ||
              errorMessage.toLowerCase().contains('required') ||
              errorMessage.toLowerCase().contains('invalid')) {
            return UserFriendlyError(
              title: 'Invalid Information',
              message: 'Please check your information and try again.',
              isRetryable: false,
            );
          }
        }
        return UserFriendlyError(
          title: 'Invalid Request',
          message: 'Please check your information and try again.',
          isRetryable: false,
        );

      case 401:
        return UserFriendlyError(
          title: 'Authentication Failed',
          message: 'Invalid email or password. Please try again.',
          isRetryable: false,
        );

      case 403:
        return UserFriendlyError(
          title: 'Access Denied',
          message: 'You don\'t have permission to perform this action.',
          isRetryable: false,
        );

      case 404:
        return UserFriendlyError(
          title: 'Not Found',
          message: 'The requested information could not be found.',
          isRetryable: false,
        );

      case 409:
        return UserFriendlyError(
          title: 'Conflict',
          message:
              'This information conflicts with existing data. Please check and try again.',
          isRetryable: false,
        );

      case 422:
        return UserFriendlyError(
          title: 'Validation Error',
          message: 'Please check your information and try again.',
          isRetryable: false,
        );

      case 429:
        return UserFriendlyError(
          title: 'Too Many Attempts',
          message: 'Too many requests. Please wait a moment and try again.',
          isRetryable: true,
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return UserFriendlyError(
          title: 'Server Error',
          message:
              'Something went wrong on our end. Please try again in a moment.',
          isRetryable: true,
        );

      default:
        if (statusCode >= 500) {
          return UserFriendlyError(
            title: 'Server Error',
            message:
                'Something went wrong on our end. Please try again in a moment.',
            isRetryable: true,
          );
        } else {
          return UserFriendlyError(
            title: 'Request Failed',
            message: errorMessage ?? 'An error occurred. Please try again.',
            isRetryable: true,
          );
        }
    }
  }

  // Helper method to capitalize first letter
  static String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }
}

/// Data class for user-friendly error information
class UserFriendlyError {
  final String title;
  final String message;
  final bool isRetryable;

  const UserFriendlyError({
    required this.title,
    required this.message,
    required this.isRetryable,
  });

  @override
  String toString() => '$title: $message';

  /// Creates a UserFriendlyError for testing purposes
  factory UserFriendlyError.test({
    required String title,
    required String message,
    bool isRetryable = false,
  }) {
    return UserFriendlyError(
      title: title,
      message: message,
      isRetryable: isRetryable,
    );
  }
}

/// Test function to verify email uniqueness error handling
/// This can be called during development to test the error mapping
void testEmailUniquenessError() {
  // Simulate the exact error structure from your log
  final testError = ClientException(
    url: Uri.parse('http://127.0.0.1:8090/api/collections/users/records'),
    statusCode: 400,
    response: {
      'data': {
        'email': {
          'code': 'validation_not_unique',
          'message': 'Value must be unique.',
        },
      },
      'message': 'Failed to create record.',
      'status': 400,
    },
  );

  final friendlyError = PocketBaseService.mapPocketBaseError(testError);

  // This should now return:
  // Title: "Email Already Registered"
  // Message: "This email address is already registered. Please try signing in instead."

  LoggerService.info('Test Email Uniqueness Error Mapping:');
  LoggerService.info('Title: ${friendlyError.title}');
  LoggerService.info('Message: ${friendlyError.message}');
  LoggerService.info('Is Retryable: ${friendlyError.isRetryable}');
}
