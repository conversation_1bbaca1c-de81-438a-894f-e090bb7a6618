import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/local_notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/audit_notification_service.dart';

/// Service for coordinating between different notification systems
/// Provides a unified interface for creating notifications across all channels
class NotificationIntegrationService {
  final PocketBaseService _pocketBaseService;
  final AuditNotificationService _auditService;

  NotificationIntegrationService(this._pocketBaseService)
    : _auditService = AuditNotificationService(_pocketBaseService);

  /// Create a comprehensive notification across all channels
  Future<void> createNotification({
    required String title,
    required String message,
    required String type,
    required List<String> recipientIds,
    String? entityId,
    String? entityType,
    Map<String, dynamic>? additionalData,
    bool showLocalNotification = true,
    bool createAuditLog = true,
    String? deepLinkRoute,
  }) async {
    try {
      LoggerService.info(
        'Creating comprehensive notification: $title (Type: $type, Recipients: ${recipientIds.length})',
      );

      // 1. Create notification in PocketBase
      final notificationData = {
        'recipientId': recipientIds,
        'title': title,
        'message': message,
        'type': type,
        'isRead': false,
        'link': deepLinkRoute,
        'metadata': {
          'entity_id': entityId,
          'entity_type': entityType,
          'additional_data': additionalData ?? {},
          // Note: 'created' timestamp is automatically managed by PocketBase autodate fields
        },
      };

      final notificationRecord = await _pocketBaseService.createRecord(
        collectionName: 'notifications',
        data: notificationData,
      );

      LoggerService.info(
        'PocketBase notification created: ${notificationRecord.id}',
      );

      // 2. Show local notification if requested
      if (showLocalNotification) {
        await _showLocalNotification(
          title: title,
          message: message,
          type: type,
          entityId: entityId,
          route: deepLinkRoute,
          additionalData: additionalData,
        );
      }

      // 3. Create audit log if requested
      if (createAuditLog && recipientIds.isNotEmpty) {
        for (final recipientId in recipientIds) {
          await _auditService.logUserAction(
            userId: recipientId,
            action: 'notification_received',
            entityType: 'notification',
            entityId: notificationRecord.id,
            details: {
              'notification_type': type,
              'title': title,
              'entity_id': entityId,
              'entity_type': entityType,
            },
          );
        }
      }

      LoggerService.info('Comprehensive notification creation completed');
    } catch (e) {
      LoggerService.error('Error creating comprehensive notification', e);
      rethrow;
    }
  }

  /// Create claim-related notification
  Future<void> createClaimNotification({
    required String claimId,
    required String title,
    required String message,
    required List<String> recipientIds,
    String? claimStatus,
    Map<String, dynamic>? claimData,
  }) async {
    await createNotification(
      title: title,
      message: message,
      type: 'claim_update',
      recipientIds: recipientIds,
      entityId: claimId,
      entityType: 'claim',
      additionalData: {
        'claim_status': claimStatus,
        'claim_data': claimData ?? {},
      },
      deepLinkRoute: '/claims/$claimId',
    );
  }

  /// Create funding-related notification
  Future<void> createFundingNotification({
    required String fundingId,
    required String title,
    required String message,
    required List<String> recipientIds,
    String? fundingStatus,
    double? amount,
    Map<String, dynamic>? fundingData,
  }) async {
    await createNotification(
      title: title,
      message: message,
      type: 'funding_opportunity',
      recipientIds: recipientIds,
      entityId: fundingId,
      entityType: 'funding',
      additionalData: {
        'funding_status': fundingStatus,
        'amount': amount,
        'funding_data': fundingData ?? {},
      },
      deepLinkRoute: '/funding/$fundingId',
    );
  }

  /// Create message notification
  Future<void> createMessageNotification({
    required String messageId,
    required String title,
    required String message,
    required List<String> recipientIds,
    required String senderId,
    String? senderName,
    String? conversationId,
  }) async {
    await createNotification(
      title: title,
      message: message,
      type: 'message',
      recipientIds: recipientIds,
      entityId: messageId,
      entityType: 'message',
      additionalData: {
        'sender_id': senderId,
        'sender_name': senderName,
        'conversation_id': conversationId,
      },
      deepLinkRoute:
          conversationId != null ? '/messages/$conversationId' : '/messages',
    );
  }

  /// Create document notification
  Future<void> createDocumentNotification({
    required String documentId,
    required String title,
    required String message,
    required List<String> recipientIds,
    required String documentName,
    String? documentType,
    String? uploaderId,
  }) async {
    await createNotification(
      title: title,
      message: message,
      type: 'document_upload',
      recipientIds: recipientIds,
      entityId: documentId,
      entityType: 'document',
      additionalData: {
        'document_name': documentName,
        'document_type': documentType,
        'uploader_id': uploaderId,
      },
      deepLinkRoute: '/documents/$documentId',
    );
  }

  /// Create system notification
  Future<void> createSystemNotification({
    required String title,
    required String message,
    required List<String> recipientIds,
    String? systemAction,
    Map<String, dynamic>? systemData,
  }) async {
    await createNotification(
      title: title,
      message: message,
      type: 'system',
      recipientIds: recipientIds,
      entityType: 'system',
      additionalData: {
        'system_action': systemAction,
        'system_data': systemData ?? {},
      },
      deepLinkRoute: '/dashboard',
    );
  }

  /// Show local notification with appropriate channel
  Future<void> _showLocalNotification({
    required String title,
    required String message,
    required String type,
    String? entityId,
    String? route,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final notificationId = LocalNotificationService.generateNotificationId();
      final payload = LocalNotificationService.createNotificationPayload(
        type: type,
        id: entityId ?? 'unknown',
        route: route ?? '/notifications',
        additionalData: additionalData,
      );

      switch (type) {
        case 'claim_update':
        case 'claim_status_change':
        case 'claim_milestone':
          await LocalNotificationService.showClaimNotification(
            id: notificationId,
            title: title,
            body: message,
            payload: payload,
          );
          break;

        case 'funding_opportunity':
        case 'funding_application_status':
        case 'investment_opportunity':
          await LocalNotificationService.showFundingNotification(
            id: notificationId,
            title: title,
            body: message,
            payload: payload,
          );
          break;

        case 'message':
        case 'agent_message':
        case 'communication':
          await LocalNotificationService.showMessageNotification(
            id: notificationId,
            title: title,
            body: message,
            payload: payload,
          );
          break;

        default:
          await LocalNotificationService.showNotification(
            id: notificationId,
            title: title,
            body: message,
            payload: payload,
          );
          break;
      }

      LoggerService.info('Local notification shown: $title (Type: $type)');
    } catch (e) {
      LoggerService.error('Error showing local notification', e);
    }
  }

  /// Batch create notifications for multiple recipients with different messages
  Future<void> batchCreateNotifications({
    required List<Map<String, dynamic>> notificationBatch,
  }) async {
    try {
      LoggerService.info(
        'Creating batch notifications: ${notificationBatch.length} items',
      );

      for (final notificationData in notificationBatch) {
        await createNotification(
          title: notificationData['title'] ?? 'Notification',
          message: notificationData['message'] ?? 'You have a new notification',
          type: notificationData['type'] ?? 'info',
          recipientIds: List<String>.from(
            notificationData['recipientIds'] ?? [],
          ),
          entityId: notificationData['entityId'],
          entityType: notificationData['entityType'],
          additionalData: notificationData['additionalData'],
          showLocalNotification:
              notificationData['showLocalNotification'] ?? true,
          createAuditLog: notificationData['createAuditLog'] ?? true,
          deepLinkRoute: notificationData['deepLinkRoute'],
        );
      }

      LoggerService.info('Batch notification creation completed');
    } catch (e) {
      LoggerService.error('Error creating batch notifications', e);
      rethrow;
    }
  }

  /// Get notification statistics
  Future<Map<String, dynamic>> getNotificationStatistics({
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      String filter = '';

      if (userId != null) {
        filter = 'recipientId ~ "$userId"';
      }

      if (startDate != null) {
        final dateFilter = 'created >= "${startDate.toIso8601String()}"';
        filter = filter.isEmpty ? dateFilter : '$filter && $dateFilter';
      }

      if (endDate != null) {
        final dateFilter = 'created <= "${endDate.toIso8601String()}"';
        filter = filter.isEmpty ? dateFilter : '$filter && $dateFilter';
      }

      final result = await _pocketBaseService.getFullList(
        collectionName: 'notifications',
        filter: filter.isNotEmpty ? filter : null,
      );

      // Calculate statistics
      final stats = {
        'total': result.length,
        'unread': result.where((r) => !(r.data['isRead'] ?? false)).length,
        'read': result.where((r) => r.data['isRead'] ?? false).length,
        'by_type': <String, int>{},
      };

      // Count by type
      for (final record in result) {
        final type = record.data['type'] as String? ?? 'unknown';
        final typeStats = stats['by_type'] as Map<String, int>;
        typeStats[type] = (typeStats[type] ?? 0) + 1;
      }

      return stats;
    } catch (e) {
      LoggerService.error('Error fetching notification statistics', e);
      rethrow;
    }
  }
}
