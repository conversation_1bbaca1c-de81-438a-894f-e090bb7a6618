import 'dart:io';
import 'package:app_badge_plus/app_badge_plus.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Service for managing app icon badge functionality
/// Handles displaying unread notification count on the app icon
class AppBadgeService {
  static bool _isSupported = false;
  static bool _isInitialized = false;

  /// Initialize the badge service and check platform support
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _isSupported = await AppBadgePlus.isSupported();
      _isInitialized = true;

      LoggerService.info(
        'AppBadgeService initialized. Badge support: $_isSupported',
      );

      if (_isSupported) {
        // Clear any existing badge on app start
        await clearBadge();
      }
    } catch (e) {
      LoggerService.error('Error initializing AppBadgeService', e);
      _isSupported = false;
      _isInitialized = true;
    }
  }

  /// Check if app badge is supported on the current platform
  static bool get isSupported => _isSupported;

  /// Check if the service has been initialized
  static bool get isInitialized => _isInitialized;

  /// Update the app icon badge with the given count
  ///
  /// [count] - The number to display on the badge
  ///
  /// Platform-specific behavior:
  /// - iOS: Shows "99+" for counts over 99
  /// - Android: Shows actual number (platform dependent)
  static Future<void> updateBadge(int count) async {
    if (!_isInitialized) {
      LoggerService.warning(
        'AppBadgeService not initialized. Call initialize() first.',
      );
      return;
    }

    if (!_isSupported) {
      LoggerService.debug(
        'App badge not supported on this platform. Skipping badge update.',
      );
      return;
    }

    try {
      if (count <= 0) {
        await clearBadge();
        return;
      }

      // Handle platform-specific count limits
      int displayCount = count;
      if (Platform.isIOS && count > 99) {
        // iOS typically shows "99+" for counts over 99
        // FlutterAppBadger will handle this automatically
        displayCount = 99;
      }

      await AppBadgePlus.updateBadge(displayCount);

      LoggerService.info(
        'App badge updated to $displayCount (original count: $count)',
      );
    } catch (e) {
      LoggerService.error('Error updating app badge', e);
    }
  }

  /// Clear the app icon badge (set count to 0)
  static Future<void> clearBadge() async {
    if (!_isInitialized) {
      LoggerService.warning(
        'AppBadgeService not initialized. Call initialize() first.',
      );
      return;
    }

    if (!_isSupported) {
      LoggerService.debug(
        'App badge not supported on this platform. Skipping badge clear.',
      );
      return;
    }

    try {
      await AppBadgePlus.updateBadge(0);
      LoggerService.info('App badge cleared');
    } catch (e) {
      LoggerService.error('Error clearing app badge', e);
    }
  }

  /// Get platform-specific information about badge support
  static Map<String, dynamic> getPlatformInfo() {
    return {
      'platform': Platform.operatingSystem,
      'isSupported': _isSupported,
      'isInitialized': _isInitialized,
      'maxDisplayCount': Platform.isIOS ? 99 : null,
      'supportsCustomBadge': Platform.isAndroid,
    };
  }

  /// Dispose the service (cleanup if needed)
  static Future<void> dispose() async {
    if (_isSupported && _isInitialized) {
      await clearBadge();
    }
    _isInitialized = false;
    LoggerService.info('AppBadgeService disposed');
  }
}
