import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:google_fonts/google_fonts.dart'; // Example for custom fonts

class AppTheme {
  // Brand Colors
  static const Color primaryColor = Color(0xFF1F3752);
  static const Color secondaryColor = Color(0xFF1B4C39);
  static const Color tertiaryColor = Color(
    0xFF004D2E,
  ); // For background elements

  // Text Colors
  static const Color textOnLight = Color(0xFF01502E);
  static const Color textOnDark = Color(
    0xFFE0E0E0,
  ); // Adjusted for dark mode contrast

  // Background Colors
  static const Color backgroundLight = Color(0xFFFFFFFF);
  static const Color backgroundDark = Color(
    0xFF121212,
  ); // A common dark mode background
  static const Color surfaceLight = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(
    0xFF1E1E1E,
  ); // Slightly lighter dark surface

  // --- Standard Flutter ThemeData ---
  static ThemeData get lightTheme {
    return ThemeData(
      brightness: Brightness.light,
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundLight,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        onPrimary: Colors.white, // Text/icon color on primary color
        secondary: secondaryColor,
        onSecondary: Colors.white, // Text/icon color on secondary color
        tertiary: tertiaryColor,
        onTertiary: Colors.white,
        surface: surfaceLight,
        onSurface: textOnLight,
        error: Colors.red,
        onError: Colors.white,
      ),
      textTheme: _materialTextTheme(
        GoogleFonts.interTextTheme(
          ThemeData(brightness: Brightness.light).textTheme,
        ),
        textOnLight,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        titleTextStyle: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      // Add other Material theme customizations if needed
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      brightness: Brightness.dark,
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundDark,
      colorScheme: ColorScheme.dark(
        primary: primaryColor,
        onPrimary: textOnDark, // Adjusted for contrast on dark primary
        secondary: secondaryColor,
        onSecondary: textOnDark,
        tertiary: tertiaryColor,
        onTertiary: textOnDark,
        surface: surfaceDark,
        onSurface: textOnDark,
        error: Colors.redAccent,
        onError: Colors.black,
      ),
      textTheme: _materialTextTheme(
        GoogleFonts.interTextTheme(
          ThemeData(brightness: Brightness.dark).textTheme,
        ),
        textOnDark,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor, // Or a darker shade like surfaceDark
        foregroundColor: textOnDark,
        elevation: 0,
        titleTextStyle: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: textOnDark,
        ),
      ),
      // Add other Material theme customizations if needed
    );
  }

  static TextTheme _materialTextTheme(TextTheme base, Color color) {
    return base.copyWith(
      displayLarge: base.displayLarge?.copyWith(
        color: color,
        fontWeight: FontWeight.bold,
      ),
      displayMedium: base.displayMedium?.copyWith(
        color: color,
        fontWeight: FontWeight.bold,
      ),
      displaySmall: base.displaySmall?.copyWith(
        color: color,
        fontWeight: FontWeight.bold,
      ),
      headlineMedium: base.headlineMedium?.copyWith(
        color: color,
        fontWeight: FontWeight.bold,
      ),
      headlineSmall: base.headlineSmall?.copyWith(
        color: color,
        fontWeight: FontWeight.w600,
      ),
      titleLarge: base.titleLarge?.copyWith(
        color: color,
        fontWeight: FontWeight.w600,
      ),
      bodyLarge: base.bodyLarge?.copyWith(color: color),
      bodyMedium: base.bodyMedium?.copyWith(color: color),
      labelLarge: base.labelLarge?.copyWith(
        color: color,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  // --- ShadcnUI ThemeData ---
  static ShadThemeData get shadLightTheme {
    return ShadThemeData(
      brightness: Brightness.light,
      colorScheme: const ShadSlateColorScheme.light(
        // Using a base, can be customized
        primary: primaryColor,
        secondary: secondaryColor,
        background: backgroundLight,
        foreground: textOnLight,
        card: surfaceLight,
        cardForeground: textOnLight,
        popover: surfaceLight,
        popoverForeground: textOnLight,
        border: Color(0xFFE2E8F0), // Example border color
        input: Color(0xFFCBD5E1), // Example input border
        ring: primaryColor, // Typically primary or accent
        destructive: Colors.red,
        destructiveForeground: Colors.white,
        // success: Color(0xFF22C55E), // Removed: Not a direct param
        // successForeground: Colors.white, // Removed: Not a direct param
      ),
      // Example: Using GoogleFonts for Shadcn text theme
      textTheme: ShadTextTheme.fromGoogleFont(
        GoogleFonts.inter, // Switch to Inter
        // colorScheme is inherited from the parent ShadThemeData
      ).copyWith(
        // Further customize specific Shadcn text styles if needed
        // h1: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
      ),
      // Add other Shadcn specific theme customizations
      // primaryButtonTheme: ShadButtonTheme(backgroundColor: primaryColor),
    );
  }

  static ShadThemeData get shadDarkTheme {
    return ShadThemeData(
      brightness: Brightness.dark,
      colorScheme: const ShadSlateColorScheme.dark(
        // Using a base, can be customized
        primary: primaryColor,
        secondary: secondaryColor,
        background: backgroundDark,
        foreground: textOnDark,
        card: surfaceDark,
        cardForeground: textOnDark,
        popover: surfaceDark,
        popoverForeground: textOnDark,
        border: Color(0xFF334155), // Example dark border color
        input: Color(0xFF475569), // Example dark input border
        ring: primaryColor,
        destructive: Colors.redAccent,
        destructiveForeground:
            Colors.white, // Corrected: Should be a light color for contrast
        // success: Color(0xFF22C55E), // Removed: Not a direct param
        // successForeground: Colors.white, // Removed: Not a direct param
      ),
      textTheme: ShadTextTheme.fromGoogleFont(
        GoogleFonts.inter, // Switch to Inter
        // colorScheme is inherited from the parent ShadThemeData
      ).copyWith(
        // Further customize specific Shadcn text styles if needed
      ),
      // Add other Shadcn specific theme customizations
    );
  }
}
