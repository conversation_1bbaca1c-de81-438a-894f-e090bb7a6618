import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class StandardDialogs {
  static Future<T?> _showDialog<T>(BuildContext context, {required Widget title, required Widget description, required List<Widget> actions}) {
    return showShadDialog<T>( // Assuming showShadDialog is the correct function from shadcn_ui
      context: context,
      builder: (context) => ShadDialog(
        title: title,
        description: description,
        actions: actions,
      ),
    );
  }

  static Future<void> showServerErrorDialog(BuildContext context, {String? title, String? description}) {
    return _showDialog<void>(
      context, // Positional argument
      title: Text(title ?? 'Server Error'),
      description: Text(description ?? 'An unexpected error occurred on the server. Please try again later.'),
      actions: [
        ShadButton(
          child: const Text('OK'),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }

  static Future<void> showAuthorizationErrorDialog(BuildContext context, {String? title, String? description}) {
    return _showDialog<void>(
      context, // Positional argument
      title: Text(title ?? 'Authorization Error'),
      description: Text(description ?? 'You are not authorized to perform this action or your session may have expired. Please log in again.'),
      actions: [
        ShadButton(
          child: const Text('OK'),
          onPressed: () {
            Navigator.of(context).pop();
            // TODO: Add navigation to login screen or token refresh logic here
          },
        ),
      ],
    );
  }

  static Future<void> showAccessDeniedDialog(BuildContext context) {
    return _showDialog<void>(
      context, // Positional argument
      title: const Text('Access Denied'),
      description: const Text('You do not have permission to access this resource.'),
      actions: [
        ShadButton(
          child: const Text('OK'),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }

   static Future<void> showSessionExpiredDialog(BuildContext context) {
    return _showDialog<void>(
      context, // Positional argument
      title: const Text('Session Expired'),
      description: const Text('Your session has expired. Please log in again to continue.'),
      actions: [
        ShadButton(
          child: const Text('Login'),
          onPressed: () {
            Navigator.of(context).pop();
            // TODO: Navigate to login screen
            // Example: Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
          },
        ),
      ],
    );
  }
}