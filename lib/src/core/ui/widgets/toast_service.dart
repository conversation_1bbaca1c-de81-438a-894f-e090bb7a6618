import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

/// A utility class for showing toast messages consistently across the application
/// This provides static methods for common toast types (error, success, info)
class ToastService {
  /// Shows an error toast message
  static void showError(BuildContext context, String message, {String? title}) {
    if (!context.mounted) return;

    try {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: Text(title ?? 'Error'),
          description: Text(message),
          duration: const Duration(seconds: 5),
        ),
      );
    } catch (e) {
      // Fallback: print to console if toast fails
      debugPrint('Toast Error: ${title ?? 'Error'}: $message');
    }
  }

  /// Shows a success toast message
  static void showSuccess(
    BuildContext context,
    String message, {
    String? title,
  }) {
    if (!context.mounted) return;

    try {
      ShadToaster.of(context).show(
        ShadToast(
          title: Text(title ?? 'Success'),
          description: Text(message),
          duration: const Duration(seconds: 4),
        ),
      );
    } catch (e) {
      // Fallback: print to console if toast fails
      debugPrint('Toast Success: ${title ?? 'Success'}: $message');
    }
  }

  /// Shows an info toast message
  static void showInfo(BuildContext context, String message, {String? title}) {
    if (!context.mounted) return;

    try {
      ShadToaster.of(context).show(
        ShadToast(
          title: Text(title ?? 'Info'),
          description: Text(message),
          duration: const Duration(seconds: 4),
        ),
      );
    } catch (e) {
      // Fallback: print to console if toast fails
      debugPrint('Toast Info: ${title ?? 'Info'}: $message');
    }
  }

  /// Shows a warning toast message
  static void showWarning(
    BuildContext context,
    String message, {
    String? title,
  }) {
    if (!context.mounted) return;

    try {
      ShadToaster.of(context).show(
        ShadToast(
          title: Text(title ?? 'Warning'),
          description: Text(message),
          duration: const Duration(seconds: 4),
        ),
      );
    } catch (e) {
      // Fallback: print to console if toast fails
      debugPrint('Toast Warning: ${title ?? 'Warning'}: $message');
    }
  }
}

/// Legacy NotificationService class for backward compatibility
/// This provides the same interface that was expected by existing code
class NotificationService {
  /// Shows an error toast message
  static void showError(BuildContext context, String message, {String? title}) {
    ToastService.showError(context, message, title: title);
  }

  /// Shows a success toast message
  static void showSuccess(
    BuildContext context,
    String message, {
    String? title,
  }) {
    ToastService.showSuccess(context, message, title: title);
  }

  /// Shows an info toast message
  static void showInfo(BuildContext context, String message, {String? title}) {
    ToastService.showInfo(context, message, title: title);
  }

  /// Shows a warning toast message
  static void showWarning(
    BuildContext context,
    String message, {
    String? title,
  }) {
    ToastService.showWarning(context, message, title: title);
  }
}
