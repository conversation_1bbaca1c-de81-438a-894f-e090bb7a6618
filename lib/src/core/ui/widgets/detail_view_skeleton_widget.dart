import 'package:flutter/material.dart';
// TODO: Replace CustomSkeletonWidget with the actual shadcn_ui skeleton once identified.
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/custom_skeleton_widget.dart';

class DetailViewSkeletonWidget extends StatelessWidget {
  const DetailViewSkeletonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomSkeletonWidget(height: 24, widthFactor: 0.6), // Title
          const SizedBox(height: 16),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.4), // Subtitle or date
          const SizedBox(height: 24),
          const CustomSkeletonWidget(height: 200, widthFactor: 1), // Image or large content block
          const SizedBox(height: 24),
          const CustomSkeletonWidget(height: 16, widthFactor: 1), // Line of text
          const SizedBox(height: 8),
          const CustomSkeletonWidget(height: 16, widthFactor: 1), // Line of text
          const SizedBox(height: 8),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.8), // Shorter line of text
          const SizedBox(height: 24),
          const CustomSkeletonWidget(height: 16, widthFactor: 1), // Another line
          const SizedBox(height: 8),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.7), // Shorter line
        ],
      ),
    );
  }
}