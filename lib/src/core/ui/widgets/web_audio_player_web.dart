import 'dart:js_interop';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:web/web.dart' as web;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Web-specific implementation using HTML5 audio
class PlatformAudioPlayer {
  web.HTMLAudioElement? _audioElement;
  Timer? _positionTimer;

  // Callbacks
  late Function(Duration) _onPositionChanged;
  late Function(Duration) _onDurationChanged;
  late Function(bool) _onPlayingChanged;
  late Function(String) _onError;

  Future<void> initialize({
    required String audioUrl,
    required Function(Duration) onPositionChanged,
    required Function(Duration) onDurationChanged,
    required Function(bool) onPlayingChanged,
    required Function(String) onError,
  }) async {
    _onPositionChanged = onPositionChanged;
    _onDurationChanged = onDurationChanged;
    _onPlayingChanged = onPlayingChanged;
    _onError = onError;

    try {
      _audioElement = web.HTMLAudioElement();

      // Set CORS and audio attributes for production compatibility
      _audioElement!.crossOrigin = 'anonymous';
      _audioElement!.preload = 'metadata';

      // Set additional attributes for better browser compatibility
      _audioElement!.setAttribute('controls', 'false');
      _audioElement!.setAttribute('playsinline', 'true');
      _audioElement!.setAttribute('webkit-playsinline', 'true');

      // Set up event listeners before setting src
      _setupEventListeners();

      // Set the audio source
      _audioElement!.src = audioUrl;

      // Load the audio
      _audioElement!.load();

      LoggerService.info('Web audio player initialized for URL: $audioUrl');
    } catch (e) {
      LoggerService.error('Failed to initialize web audio player', e);
      _onError('Failed to initialize audio player: $e');
    }
  }

  void _setupEventListeners() {
    if (_audioElement == null) return;

    // Duration loaded
    _audioElement!.addEventListener(
      'loadedmetadata',
      (web.Event event) {
        final duration = _audioElement!.duration;
        if (!duration.isNaN && duration.isFinite) {
          _onDurationChanged(Duration(seconds: duration.toInt()));
        }
      }.toJS,
    );

    // Playback started
    _audioElement!.addEventListener(
      'play',
      (web.Event event) {
        _onPlayingChanged(true);
        _startPositionTimer();
      }.toJS,
    );

    // Playback paused
    _audioElement!.addEventListener(
      'pause',
      (web.Event event) {
        _onPlayingChanged(false);
        _stopPositionTimer();
      }.toJS,
    );

    // Playback ended
    _audioElement!.addEventListener(
      'ended',
      (web.Event event) {
        _onPlayingChanged(false);
        _stopPositionTimer();
      }.toJS,
    );

    // Error handling
    _audioElement!.addEventListener(
      'error',
      (web.Event event) {
        final error = _audioElement!.error;
        String errorMessage = 'Unknown audio error';

        if (error != null) {
          switch (error.code) {
            case 1: // MEDIA_ERR_ABORTED
              errorMessage = 'Audio loading was aborted';
              break;
            case 2: // MEDIA_ERR_NETWORK
              errorMessage = 'Network error while loading audio';
              break;
            case 3: // MEDIA_ERR_DECODE
              errorMessage = 'Audio decoding error';
              break;
            case 4: // MEDIA_ERR_SRC_NOT_SUPPORTED
              errorMessage = 'Audio format not supported';
              break;
            default:
              errorMessage = 'Audio playback error (code: ${error.code})';
          }
        }

        LoggerService.error('Web audio error: $errorMessage');
        _onError(errorMessage);
      }.toJS,
    );

    // Can play through (enough data loaded)
    _audioElement!.addEventListener(
      'canplaythrough',
      (web.Event event) {
        LoggerService.info('Web audio can play through');
      }.toJS,
    );

    // Waiting for data
    _audioElement!.addEventListener(
      'waiting',
      (web.Event event) {
        LoggerService.info('Web audio waiting for data');
      }.toJS,
    );
  }

  void _startPositionTimer() {
    _stopPositionTimer();
    _positionTimer = Timer.periodic(const Duration(milliseconds: 100), (_) {
      if (_audioElement != null) {
        final currentTime = _audioElement!.currentTime;
        if (!currentTime.isNaN && currentTime.isFinite) {
          _onPositionChanged(Duration(seconds: currentTime.toInt()));
        }
      }
    });
  }

  void _stopPositionTimer() {
    _positionTimer?.cancel();
    _positionTimer = null;
  }

  Future<void> play() async {
    if (_audioElement == null) {
      throw Exception('Audio player not initialized');
    }

    try {
      _audioElement!.play();
      LoggerService.info('Web audio playback started');
    } catch (e) {
      LoggerService.error('Failed to start web audio playback', e);
      _onError('Failed to start playback: $e');
      rethrow;
    }
  }

  Future<void> pause() async {
    if (_audioElement == null) {
      throw Exception('Audio player not initialized');
    }

    try {
      _audioElement!.pause();
      LoggerService.info('Web audio playback paused');
    } catch (e) {
      LoggerService.error('Failed to pause web audio playback', e);
      _onError('Failed to pause playback: $e');
      rethrow;
    }
  }

  Future<void> seek(Duration position) async {
    if (_audioElement == null) {
      throw Exception('Audio player not initialized');
    }

    try {
      _audioElement!.currentTime = position.inSeconds.toDouble();
      LoggerService.info('Web audio seeked to: ${position.inSeconds}s');
    } catch (e) {
      LoggerService.error('Failed to seek web audio', e);
      _onError('Failed to seek: $e');
      rethrow;
    }
  }

  void dispose() {
    _stopPositionTimer();

    if (_audioElement != null) {
      _audioElement!.pause();
      _audioElement!.src = '';
      _audioElement!.load(); // This helps clean up resources
      _audioElement = null;
    }

    LoggerService.info('Web audio player disposed');
  }

  static void openInNewTab(String url) {
    try {
      web.window.open(url, '_blank');
      LoggerService.info('Opened audio URL in new tab: $url');
    } catch (e) {
      LoggerService.error('Failed to open audio URL in new tab', e);
      if (kDebugMode) {
        print('Failed to open audio URL: $url, Error: $e');
      }
    }
  }
}
