import 'package:flutter/material.dart';
// TODO: Replace CustomSkeletonWidget with the actual shadcn_ui skeleton once identified.
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/custom_skeleton_widget.dart'; 

class ListItemSkeletonWidget extends StatelessWidget {
  const ListItemSkeletonWidget({super.key, this.hasLeading = true, this.hasSubtitle = true});

  final bool hasLeading;
  final bool hasSubtitle;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: Row(
        children: [
          if (hasLeading)
            const CustomSkeletonWidget( // Using placeholder
              width: 48,
              height: 48,
              cornerRadius: BorderRadius.all(Radius.circular(24)), 
            ),
          if (hasLeading) const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CustomSkeletonWidget(height: 16, widthFactor: 0.7), // Using placeholder
                if (hasSubtitle) const SizedBox(height: 8),
                if (hasSubtitle) const CustomSkeletonWidget(height: 12, widthFactor: 0.5), // Using placeholder
              ],
            ),
          ),
        ],
      ),
    );
  }
}