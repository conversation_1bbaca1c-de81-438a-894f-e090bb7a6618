import 'dart:async';
import 'package:just_audio/just_audio.dart';
import 'package:flutter/foundation.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Mobile-specific implementation using just_audio
class PlatformAudioPlayer {
  AudioPlayer? _audioPlayer;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration?>? _durationSubscription;
  StreamSubscription<bool>? _playingSubscription;
  
  // Callbacks
  late Function(Duration) _onPositionChanged;
  late Function(Duration) _onDurationChanged;
  late Function(bool) _onPlayingChanged;
  late Function(String) _onError;

  Future<void> initialize({
    required String audioUrl,
    required Function(Duration) onPositionChanged,
    required Function(Duration) onDurationChanged,
    required Function(bool) onPlayingChanged,
    required Function(String) onError,
  }) async {
    _onPositionChanged = onPositionChanged;
    _onDurationChanged = onDurationChanged;
    _onPlayingChanged = onPlayingChanged;
    _onError = onError;

    try {
      _audioPlayer = AudioPlayer();
      
      // Set up stream subscriptions
      _setupStreamSubscriptions();
      
      // Load the audio
      await _audioPlayer!.setUrl(audioUrl);
      
      LoggerService.info('Mobile audio player initialized for URL: $audioUrl');
    } catch (e) {
      LoggerService.error('Failed to initialize mobile audio player', e);
      _onError('Failed to initialize audio player: $e');
    }
  }

  void _setupStreamSubscriptions() {
    if (_audioPlayer == null) return;

    // Position changes
    _positionSubscription = _audioPlayer!.positionStream.listen(
      (position) => _onPositionChanged(position),
      onError: (error) {
        LoggerService.error('Position stream error', error);
        _onError('Position tracking error: $error');
      },
    );

    // Duration changes
    _durationSubscription = _audioPlayer!.durationStream.listen(
      (duration) {
        if (duration != null) {
          _onDurationChanged(duration);
        }
      },
      onError: (error) {
        LoggerService.error('Duration stream error', error);
        _onError('Duration tracking error: $error');
      },
    );

    // Playing state changes
    _playingSubscription = _audioPlayer!.playingStream.listen(
      (isPlaying) => _onPlayingChanged(isPlaying),
      onError: (error) {
        LoggerService.error('Playing stream error', error);
        _onError('Playback state error: $error');
      },
    );
  }

  Future<void> play() async {
    if (_audioPlayer == null) {
      throw Exception('Audio player not initialized');
    }

    try {
      await _audioPlayer!.play();
      LoggerService.info('Mobile audio playback started');
    } catch (e) {
      LoggerService.error('Failed to start mobile audio playback', e);
      _onError('Failed to start playback: $e');
      rethrow;
    }
  }

  Future<void> pause() async {
    if (_audioPlayer == null) {
      throw Exception('Audio player not initialized');
    }

    try {
      await _audioPlayer!.pause();
      LoggerService.info('Mobile audio playback paused');
    } catch (e) {
      LoggerService.error('Failed to pause mobile audio playback', e);
      _onError('Failed to pause playback: $e');
      rethrow;
    }
  }

  Future<void> seek(Duration position) async {
    if (_audioPlayer == null) {
      throw Exception('Audio player not initialized');
    }

    try {
      await _audioPlayer!.seek(position);
      LoggerService.info('Mobile audio seeked to: ${position.inSeconds}s');
    } catch (e) {
      LoggerService.error('Failed to seek mobile audio', e);
      _onError('Failed to seek: $e');
      rethrow;
    }
  }

  void dispose() {
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _playingSubscription?.cancel();
    
    _audioPlayer?.dispose();
    _audioPlayer = null;
    
    LoggerService.info('Mobile audio player disposed');
  }

  static void openInNewTab(String url) {
    // On mobile, we could potentially use url_launcher
    // For now, just log the action
    LoggerService.info('Open in new tab requested on mobile: $url');
    if (kDebugMode) {
      print('Mobile platform: Cannot open audio URL in new tab: $url');
    }
  }
}
