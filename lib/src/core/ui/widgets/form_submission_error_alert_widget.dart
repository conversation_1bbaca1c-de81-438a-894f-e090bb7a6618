import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class FormSubmissionErrorAlertWidget extends StatelessWidget {
  const FormSubmissionErrorAlertWidget({
    super.key,
    required this.title,
    required this.description,
    this.iconData, // Renamed for clarity
  });

  final String title;
  final String description;
  final IconData? iconData; // Renamed for clarity

  @override
  Widget build(BuildContext context) {
    return ShadAlert.destructive(
      icon: Icon(iconData ?? LucideIcons.circleAlert, size: 16), // Trying circleAlert
      title: Text(title),
      description: Text(description),
    );
  }
}