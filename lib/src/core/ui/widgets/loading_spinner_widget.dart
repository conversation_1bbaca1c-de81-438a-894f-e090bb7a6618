import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class LoadingSpinnerWidget extends StatefulWidget {
  const LoadingSpinnerWidget({
    super.key,
    this.size = 24,
    this.color,
    this.strokeWidth = 2.0, // strokeWidth is not directly used by Icon, but kept for potential future native spinner
  });

  final double size;
  final Color? color;
  final double strokeWidth;

  @override
  State<LoadingSpinnerWidget> createState() => _LoadingSpinnerWidgetState();
}

class _LoadingSpinnerWidgetState extends State<LoadingSpinnerWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return RotationTransition(
      turns: _controller,
      child: Icon(
        LucideIcons.loader, // Trying LucideIcons.loader
        size: widget.size,
        color: widget.color ?? theme.colorScheme.primary,
      ),
    );
  }
}

// Helper widget to easily embed a spinner in a button or elsewhere
class InlineLoadingSpinner extends StatelessWidget {
  const InlineLoadingSpinner({super.key, this.size = 16, this.color, this.padding = const EdgeInsets.only(right: 8.0)});
  final double size;
  final Color? color;
  final EdgeInsetsGeometry padding;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: LoadingSpinnerWidget(size: size, color: color),
    );
  }
}