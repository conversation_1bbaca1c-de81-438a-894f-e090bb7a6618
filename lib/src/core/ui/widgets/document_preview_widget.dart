import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:photo_view/photo_view.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../services/logger_service.dart';

class DocumentPreviewWidget extends StatefulWidget {
  final String url;
  final String fileName;

  const DocumentPreviewWidget({
    super.key,
    required this.url,
    required this.fileName,
  });

  @override
  State<DocumentPreviewWidget> createState() => _DocumentPreviewWidgetState();
}

class _DocumentPreviewWidgetState extends State<DocumentPreviewWidget> {
  bool _isLoading = true;
  String? _errorMessage;
  String? _localFilePath;
  bool _useDirectUrl = false;
  final Dio _dio = Dio();

  @override
  void initState() {
    super.initState();
    _initializeViewer();
  }

  Future<void> _initializeViewer() async {
    final extension = _getFileExtension(widget.fileName).toLowerCase();

    LoggerService.info(
      'Initializing viewer for file: ${widget.fileName} ($extension)',
    );

    // For PDFs, use native approach (no download needed)
    if (extension == '.pdf') {
      LoggerService.info('Using native PDF viewer approach');
      setState(() {
        _isLoading = false;
      });
    } else {
      // For other file types, download first
      await _downloadFile();
    }
  }

  Future<void> _downloadFile() async {
    try {
      setState(() => _isLoading = true);

      LoggerService.info('Starting download for: ${widget.url}');
      LoggerService.info('File name: ${widget.fileName}');

      // Get temp directory
      final tempDir = await getTemporaryDirectory();
      final fileName = widget.fileName;
      final savePath = '${tempDir.path}/$fileName';

      LoggerService.info('Saving to: $savePath');

      // Configure Dio with better error handling
      _dio.options.connectTimeout = const Duration(seconds: 30);
      _dio.options.receiveTimeout = const Duration(seconds: 60);
      _dio.options.followRedirects = true;
      _dio.options.maxRedirects = 5;
      _dio.options.headers = {
        'User-Agent': 'Mozilla/5.0 (compatible; 3PayGlobal/1.0)',
        'Accept': '*/*',
      };

      // Download file
      final response = await _dio.download(
        widget.url,
        savePath,
        onReceiveProgress: (received, total) {
          if (total > 0) {
            final progress = received / total;
            LoggerService.debug(
              'Download progress: ${(progress * 100).toStringAsFixed(1)}%',
            );
          }
        },
      );

      LoggerService.info('Download response status: ${response.statusCode}');

      // Verify file exists and has content
      final file = File(savePath);
      if (await file.exists()) {
        final fileSize = await file.length();
        LoggerService.info(
          'File downloaded successfully. Size: $fileSize bytes',
        );

        if (fileSize == 0) {
          throw Exception('Downloaded file is empty');
        }

        setState(() {
          _localFilePath = savePath;
          _isLoading = false;
        });
      } else {
        throw Exception('Downloaded file does not exist');
      }
    } catch (e) {
      LoggerService.error('Error downloading file', e);
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to download file: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.fileName),
        actions: [
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () => _openInBrowser(widget.url),
            tooltip: 'Download file',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading document...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red),
            SizedBox(height: 16),
            Text(_errorMessage!),
            SizedBox(height: 16),
            ShadButton(
              onPressed: () => _openInBrowser(widget.url),
              child: const Text('Open in Browser'),
            ),
          ],
        ),
      );
    }

    return _buildPreview();
  }

  Widget _buildPreview() {
    final extension = _getFileExtension(widget.fileName).toLowerCase();
    LoggerService.info('Building preview for file extension: $extension');
    LoggerService.info('Local file path: $_localFilePath');

    switch (extension) {
      case '.pdf':
        return _buildPdfViewer();

      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
        return _buildImageViewer();

      default:
        return _buildUnsupportedFileView(extension);
    }
  }

  Widget _buildPdfViewer() {
    try {
      LoggerService.info(
        'Creating PDF viewer. UseDirectUrl: $_useDirectUrl, LocalPath: $_localFilePath',
      );

      return Column(
        children: [
          // Add a retry button in case of issues
          Container(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'PDF Document',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      onPressed: _retryPdfLoad,
                      tooltip: 'Reload document',
                    ),
                    IconButton(
                      icon: const Icon(Icons.open_in_browser),
                      onPressed: () => _openInBrowser(widget.url),
                      tooltip: 'Open in browser',
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(child: _buildPdfViewerWidget()),
        ],
      );
    } catch (e) {
      LoggerService.error('Error creating PDF viewer', e);
      return _buildErrorView('Error loading PDF: $e');
    }
  }

  Widget _buildPdfViewerWidget() {
    // For Google Drive URLs, try to download the file locally first
    if (_shouldDownloadLocally()) {
      return _buildLocalPdfViewer();
    }

    // Use Syncfusion PDF viewer for in-app PDF viewing
    return _buildSyncfusionPdfViewer();
  }

  /// Check if we should download the file locally before viewing
  bool _shouldDownloadLocally() {
    // Download locally for Google Drive URLs to avoid CORS and authentication issues
    return widget.url.contains('drive.google.com') && _localFilePath == null;
  }

  /// Build PDF viewer with local file download
  Widget _buildLocalPdfViewer() {
    return FutureBuilder<String?>(
      future: _downloadFileLocally(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Downloading PDF...'),
              ],
            ),
          );
        }

        if (snapshot.hasError) {
          LoggerService.error('Error downloading PDF locally', snapshot.error);
          return _buildErrorView('Failed to download PDF: ${snapshot.error}');
        }

        if (snapshot.hasData && snapshot.data != null) {
          // Update local file path and rebuild with local file
          _localFilePath = snapshot.data;
          return _buildSyncfusionPdfViewerFromFile();
        }

        return _buildErrorView('Failed to download PDF file');
      },
    );
  }

  Widget _buildSyncfusionPdfViewer() {
    // Log the URL being used for debugging
    LoggerService.info('PDF Viewer URL: ${widget.url}');

    return Column(
      children: [
        // PDF info and controls
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              const Icon(Icons.picture_as_pdf, color: Colors.red),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.fileName,
                      style: Theme.of(context).textTheme.titleMedium,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      'PDF Document',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(
                          context,
                        ).textTheme.bodySmall?.color?.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              ShadButton.outline(
                onPressed: () => _openInBrowser(widget.url),
                child: const Text('Open in Browser'),
              ),
            ],
          ),
        ),
        // Error display for PDF load failures
        if (_errorMessage != null)
          Container(
            padding: const EdgeInsets.all(16.0),
            margin: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              border: Border.all(color: Colors.red.shade200),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.error, color: Colors.red.shade600),
                    const SizedBox(width: 8.0),
                    Expanded(
                      child: Text(
                        'Document Load Error',
                        style: TextStyle(
                          color: Colors.red.shade800,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8.0),
                Text(
                  _errorMessage!,
                  style: TextStyle(color: Colors.red.shade700),
                ),
                const SizedBox(height: 12.0),
                Row(
                  children: [
                    ShadButton.outline(
                      onPressed: _retryPdfLoad,
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.refresh, size: 16),
                          SizedBox(width: 4),
                          Text('Retry'),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8.0),
                    ShadButton.outline(
                      onPressed: () => _openInBrowser(widget.url),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.open_in_browser, size: 16),
                          SizedBox(width: 4),
                          Text('Open in Browser'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

        // Syncfusion PDF Viewer
        if (_errorMessage == null)
          Expanded(
            child: SfPdfViewer.network(
              widget.url,
              onDocumentLoaded: (PdfDocumentLoadedDetails details) {
                LoggerService.info(
                  'PDF loaded successfully: ${details.document.pages.count} pages',
                );
                // Clear any previous error message on successful load
                if (_errorMessage != null) {
                  setState(() {
                    _errorMessage = null;
                  });
                }
              },
              onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
                LoggerService.error(
                  'PDF load failed: ${details.error}',
                  details.description,
                );
                setState(() {
                  _errorMessage = 'Failed to load PDF: ${details.description}';
                });
              },
              enableDoubleTapZooming: true,
              enableTextSelection: true,
              canShowScrollHead: true,
              canShowScrollStatus: true,
              canShowPaginationDialog: true,
            ),
          ),
      ],
    );
  }

  Future<void> _retryPdfLoad() async {
    setState(() {
      _errorMessage = null;
      _isLoading = true;
      _useDirectUrl = true; // Try direct URL first again
    });
    await _initializeViewer();
  }

  String _getFileExtension(String fileName) {
    final lastDot = fileName.lastIndexOf('.');
    if (lastDot == -1) return '';
    return fileName.substring(lastDot);
  }

  Widget _buildImageViewer() {
    try {
      return PhotoView(
        imageProvider: FileImage(File(_localFilePath!)),
        backgroundDecoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
        ),
        loadingBuilder:
            (context, event) => Center(
              child: CircularProgressIndicator(
                value:
                    event == null
                        ? 0
                        : event.cumulativeBytesLoaded /
                            (event.expectedTotalBytes ?? 1),
              ),
            ),
        errorBuilder: (context, error, stackTrace) {
          LoggerService.error('Error loading image', error);
          return _buildErrorView('Error loading image: $error');
        },
      );
    } catch (e) {
      LoggerService.error('Error creating image viewer', e);
      return _buildErrorView('Error loading image: $e');
    }
  }

  Widget _buildUnsupportedFileView(String extension) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.insert_drive_file, size: 64),
          SizedBox(height: 16),
          Text(
            'Preview not available for ${extension.replaceAll('.', '')} files',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: 24),
          ShadButton(
            onPressed: () => _openInBrowser(widget.url),
            child: const Text('Open in Browser'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 48, color: Colors.red),
          SizedBox(height: 16),
          Text(
            message,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ShadButton.outline(
                onPressed: _downloadFile,
                child: const Text('Retry'),
              ),
              SizedBox(width: 16),
              ShadButton(
                onPressed: () => _openInBrowser(widget.url),
                child: const Text('Open in Browser'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _openInBrowser(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Error'),
            description: const Text('Could not open the file'),
          ),
        );
      }
    }
  }

  /// Download file locally for better PDF viewing
  Future<String?> _downloadFileLocally() async {
    try {
      // Convert Google Drive view URL to download URL
      String downloadUrl = widget.url;
      if (widget.url.contains('drive.google.com/file/d/')) {
        final fileIdMatch = RegExp(
          r'/file/d/([a-zA-Z0-9_-]+)',
        ).firstMatch(widget.url);
        if (fileIdMatch != null) {
          final fileId = fileIdMatch.group(1);
          downloadUrl =
              'https://drive.google.com/uc?export=download&id=$fileId';
          LoggerService.info(
            'Converted view URL to download URL: $downloadUrl',
          );
        }
      }

      LoggerService.info('Downloading file locally from: $downloadUrl');

      // Create a temporary file path
      final tempDir = Directory.systemTemp;
      final fileName = widget.fileName.replaceAll(RegExp(r'[^\w\s\-\.]'), '_');
      final tempFile = File('${tempDir.path}/$fileName');

      // Configure Dio for download
      _dio.options.followRedirects = true;
      _dio.options.maxRedirects = 5;
      _dio.options.headers = {
        'User-Agent': 'Mozilla/5.0 (compatible; 3PayGlobal/1.0)',
        'Accept': 'application/pdf,*/*',
      };

      // Download the file
      final response = await _dio.get(
        downloadUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final data = response.data as List<int>;
        await tempFile.writeAsBytes(data);

        // Verify the file is actually a PDF
        final fileBytes = await tempFile.readAsBytes();
        if (fileBytes.length < 4 ||
            String.fromCharCodes(fileBytes.take(4)) != '%PDF') {
          LoggerService.warning('Downloaded file does not appear to be a PDF');
          LoggerService.warning(
            'First 100 bytes: ${String.fromCharCodes(fileBytes.take(100))}',
          );
          throw Exception('Downloaded file is not a valid PDF');
        }

        LoggerService.info('File downloaded successfully to: ${tempFile.path}');
        LoggerService.info('File size: ${fileBytes.length} bytes');
        return tempFile.path;
      } else {
        throw Exception(
          'HTTP ${response.statusCode}: ${response.statusMessage ?? 'Unknown error'}',
        );
      }
    } catch (e) {
      LoggerService.error('Failed to download file locally', e);
      return null;
    }
  }

  /// Build Syncfusion PDF viewer from local file
  Widget _buildSyncfusionPdfViewerFromFile() {
    if (_localFilePath == null) {
      return _buildErrorView('No local file path available');
    }

    LoggerService.info('Building PDF viewer from local file: $_localFilePath');

    return SfPdfViewer.file(
      File(_localFilePath!),
      onDocumentLoaded: (PdfDocumentLoadedDetails details) {
        LoggerService.info(
          'PDF loaded successfully from local file: ${details.document.pages.count} pages',
        );
        // Clear any previous error message on successful load
        if (_errorMessage != null) {
          setState(() {
            _errorMessage = null;
          });
        }
      },
      onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
        LoggerService.error(
          'PDF load failed from local file: ${details.error}',
          details.description,
        );
        setState(() {
          _errorMessage =
              'Failed to load PDF from local file: ${details.description}';
        });
      },
      enableDoubleTapZooming: true,
      enableTextSelection: true,
      canShowScrollHead: true,
      canShowScrollStatus: true,
      canShowPaginationDialog: true,
    );
  }

  @override
  void dispose() {
    // Clean up temp file
    if (_localFilePath != null) {
      try {
        File(_localFilePath!).delete();
      } catch (e) {
        LoggerService.error('Error deleting temp file', e);
      }
    }
    super.dispose();
  }
}
