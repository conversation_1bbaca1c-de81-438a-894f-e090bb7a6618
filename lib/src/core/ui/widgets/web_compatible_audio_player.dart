import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

// Conditional imports for platform-specific implementations
import 'web_audio_player_stub.dart'
    if (dart.library.html) 'web_audio_player_web.dart'
    if (dart.library.io) 'web_audio_player_mobile.dart';

/// A web-compatible audio player widget that works across all platforms
class WebCompatibleAudioPlayer extends StatefulWidget {
  final String audioUrl;
  final String title;
  final VoidCallback? onPlayCountIncrement;
  final bool showDownloadButton;

  const WebCompatibleAudioPlayer({
    super.key,
    required this.audioUrl,
    required this.title,
    this.onPlayCountIncrement,
    this.showDownloadButton = true,
  });

  @override
  State<WebCompatibleAudioPlayer> createState() =>
      _WebCompatibleAudioPlayerState();
}

class _WebCompatibleAudioPlayerState extends State<WebCompatibleAudioPlayer> {
  late final PlatformAudioPlayer _audioPlayer;
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  String? _error;

  @override
  void initState() {
    super.initState();
    _audioPlayer = PlatformAudioPlayer();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() => _isLoading = true);
      
      await _audioPlayer.initialize(
        audioUrl: widget.audioUrl,
        onPositionChanged: (position) {
          if (mounted) {
            setState(() => _position = position);
          }
        },
        onDurationChanged: (duration) {
          if (mounted) {
            setState(() => _duration = duration);
          }
        },
        onPlayingChanged: (isPlaying) {
          if (mounted) {
            setState(() => _isPlaying = isPlaying);
          }
        },
        onError: (error) {
          if (mounted) {
            setState(() {
              _error = error;
              _isLoading = false;
            });
          }
          LoggerService.error('Audio player error: $error');
        },
      );

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() {
        _error = 'Failed to initialize audio player: $e';
        _isLoading = false;
      });
      LoggerService.error('Failed to initialize audio player', e);
    }
  }

  Future<void> _togglePlayPause() async {
    if (_error != null) return;

    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
      } else {
        await _audioPlayer.play();
        widget.onPlayCountIncrement?.call();
      }
    } catch (e) {
      setState(() => _error = 'Playback error: $e');
      LoggerService.error('Audio playback error', e);
    }
  }

  Future<void> _seek(Duration position) async {
    if (_error != null) return;

    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      LoggerService.error('Audio seek error', e);
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    if (_error != null) {
      return _buildErrorWidget(theme);
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and duration
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.title,
                  style: theme.textTheme.large.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (_duration > Duration.zero)
                Text(
                  _formatDuration(_duration),
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),

          // Progress bar
          if (_duration > Duration.zero) ...[
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                trackHeight: 4,
              ),
              child: Slider(
                value: _position.inMilliseconds.toDouble(),
                max: _duration.inMilliseconds.toDouble(),
                onChanged: (value) {
                  _seek(Duration(milliseconds: value.toInt()));
                },
                activeColor: theme.colorScheme.primary,
                inactiveColor: theme.colorScheme.muted,
              ),
            ),
            const SizedBox(height: 8),
          ],

          // Controls
          Row(
            children: [
              // Play/Pause button
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                child: _isLoading
                    ? const Center(
                        child: SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        ),
                      )
                    : IconButton(
                        onPressed: _togglePlayPause,
                        icon: Icon(
                          _isPlaying ? Icons.pause : Icons.play_arrow,
                          color: theme.colorScheme.primaryForeground,
                          size: 24,
                        ),
                      ),
              ),
              const SizedBox(width: 16),

              // Position and duration
              if (_duration > Duration.zero)
                Text(
                  '${_formatDuration(_position)} / ${_formatDuration(_duration)}',
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),

              const Spacer(),

              // Download/Open button (web-specific)
              if (widget.showDownloadButton && kIsWeb)
                ShadButton.outline(
                  size: ShadButtonSize.sm,
                  onPressed: () => _openAudioInNewTab(),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.open_in_new, size: 16),
                      const SizedBox(width: 4),
                      const Text('Open'),
                    ],
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.destructive.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.destructive.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: theme.colorScheme.destructive,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Audio Player Error',
                  style: theme.textTheme.small.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.destructive,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _error!,
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.destructive,
                  ),
                ),
              ],
            ),
          ),
          if (kIsWeb)
            ShadButton.outline(
              size: ShadButtonSize.sm,
              onPressed: () => _openAudioInNewTab(),
              child: const Text('Open File'),
            ),
        ],
      ),
    );
  }

  void _openAudioInNewTab() {
    if (kIsWeb) {
      // This will be implemented in the web-specific file
      PlatformAudioPlayer.openInNewTab(widget.audioUrl);
    }
  }
}
