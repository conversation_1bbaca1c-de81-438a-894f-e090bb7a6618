import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class InlineErrorTextWidget extends StatelessWidget {
  const InlineErrorTextWidget({
    super.key,
    required this.message,
    this.style,
  });

  final String message;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Text(
      message,
      style: style ?? theme.textTheme.p.copyWith(color: theme.colorScheme.destructive),
      textAlign: TextAlign.start,
    );
  }
}