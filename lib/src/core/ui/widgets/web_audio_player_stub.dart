import 'package:flutter/foundation.dart';

/// Stub implementation for unsupported platforms
class PlatformAudioPlayer {
  Future<void> initialize({
    required String audioUrl,
    required Function(Duration) onPositionChanged,
    required Function(Duration) onDurationChanged,
    required Function(bool) onPlayingChanged,
    required Function(String) onError,
  }) async {
    onError('Audio playback not supported on this platform');
  }

  Future<void> play() async {
    throw UnsupportedError('Audio playback not supported on this platform');
  }

  Future<void> pause() async {
    throw UnsupportedError('Audio playback not supported on this platform');
  }

  Future<void> seek(Duration position) async {
    throw UnsupportedError('Audio playback not supported on this platform');
  }

  void dispose() {
    // No-op for stub
  }

  static void openInNewTab(String url) {
    if (kDebugMode) {
      print('Opening audio URL in new tab not supported: $url');
    }
  }
}
