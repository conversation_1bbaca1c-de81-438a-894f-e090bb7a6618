import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class EmptyStateWidget extends StatelessWidget {
  const EmptyStateWidget({
    super.key,
    this.icon,
    this.iconSize = 48,
    required this.message,
    this.actionButtonText,
    this.onActionButtonPressed,
    this.messageStyle,
    this.iconColor,
  });

  final IconData? icon;
  final double iconSize;
  final String message;
  final String? actionButtonText;
  final VoidCallback? onActionButtonPressed;
  final TextStyle? messageStyle;
  final Color? iconColor;

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            if (icon != null)
              Icon(
                icon,
                size: iconSize,
                color: iconColor ?? theme.colorScheme.mutedForeground,
              ),
            if (icon != null) const SizedBox(height: 24),
            Text(
              message,
              textAlign: TextAlign.center,
              style: messageStyle ?? theme.textTheme.h4.copyWith(color: theme.colorScheme.mutedForeground),
            ),
            if (actionButtonText != null && onActionButtonPressed != null)
              const SizedBox(height: 24),
            if (actionButtonText != null && onActionButtonPressed != null)
              ShadButton(
                onPressed: onActionButtonPressed,
                child: Text(actionButtonText!),
              ),
          ],
        ),
      ),
    );
  }
}