import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart'; // Keep for theme access

// TODO: Replace this with the actual skeleton widget from shadcn_ui once identified.
// This is a placeholder to allow progress on UI structure for loading states.

class CustomSkeletonWidget extends StatelessWidget {
  const CustomSkeletonWidget({
    super.key,
    this.height,
    this.width,
    this.widthFactor,
    this.cornerRadius = const BorderRadius.all(Radius.circular(4)), // Default from ShadSkeleton
    this.color,
  });

  final double? height;
  final double? width;
  final double? widthFactor; // Percentage of available width
  final BorderRadius cornerRadius;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final skeletonColor = color ?? theme.colorScheme.muted; // A typical skeleton color

    Widget skeleton = Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        color: skeletonColor,
        borderRadius: cornerRadius,
      ),
    );

    if (widthFactor != null) {
      return FractionallySizedBox(
        widthFactor: widthFactor,
        child: skeleton,
      );
    }
    return skeleton;
  }
}