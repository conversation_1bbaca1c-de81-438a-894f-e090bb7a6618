import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';

/// A utility class for handling registration errors consistently across all registration pages
class RegistrationErrorHandler {
  /// Shows a user-friendly error toast for registration failures
  /// 
  /// This method uses the centralized error mapping from PocketBaseService
  /// to convert technical errors into user-friendly messages
  static void showErrorToast(BuildContext context, dynamic error) {
    final friendlyError = PocketBaseService.mapPocketBaseError(error);
    
    ShadToaster.of(context).show(
      ShadToast.destructive(
        title: Text(friendlyError.title),
        description: Text(friendlyError.message),
      ),
    );
  }

  /// Shows a validation error toast for form validation failures
  static void showValidationErrorToast(BuildContext context) {
    ShadToaster.of(context).show(
      const ShadToast.destructive(
        title: Text('Validation Error'),
        description: Text('Please correct the errors in the form.'),
      ),
    );
  }

  /// Shows a success toast for successful registration
  static void showSuccessToast(BuildContext context) {
    ShadToaster.of(context).show(
      const ShadToast(
        title: Text('Registration Successful'),
        description: Text('Please sign in with your new account.'),
      ),
    );
  }

  /// Handles registration errors and updates the error message state
  /// 
  /// Returns the user-friendly error message for display in the UI
  static String handleRegistrationError(
    BuildContext context,
    dynamic error,
    void Function(String?) setErrorMessage,
  ) {
    final friendlyError = PocketBaseService.mapPocketBaseError(error);
    
    // Update the error message state
    setErrorMessage(friendlyError.message);
    
    // Show the error toast
    showErrorToast(context, error);
    
    return friendlyError.message;
  }
}

/// Widget for displaying inline error messages in registration forms
class RegistrationErrorDisplay extends StatelessWidget {
  const RegistrationErrorDisplay({
    super.key,
    required this.errorMessage,
    this.padding = const EdgeInsets.only(top: 16),
  });

  final String? errorMessage;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    if (errorMessage == null) return const SizedBox.shrink();
    
    final shadTheme = ShadTheme.of(context);
    
    return Padding(
      padding: padding,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: shadTheme.colorScheme.destructive.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: shadTheme.colorScheme.destructive.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              LucideIcons.circleAlert,
              size: 16,
              color: shadTheme.colorScheme.destructive,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                errorMessage!,
                style: shadTheme.textTheme.small.copyWith(
                  color: shadTheme.colorScheme.destructive,
                  height: 1.4,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget for displaying loading states in registration buttons
class RegistrationButton extends StatelessWidget {
  const RegistrationButton({
    super.key,
    required this.isLoading,
    required this.onPressed,
    required this.text,
    this.width = double.infinity,
  });

  final bool isLoading;
  final VoidCallback? onPressed;
  final String text;
  final double width;

  @override
  Widget build(BuildContext context) {
    return ShadButton(
      width: width,
      onPressed: isLoading ? null : onPressed,
      child: isLoading
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator.adaptive(strokeWidth: 2),
            )
          : Text(text),
    );
  }
}

/// Mixin for consistent registration error handling across all registration pages
mixin RegistrationErrorMixin<T extends StatefulWidget> on State<T> {
  String? _errorMessage;
  bool _isLoading = false;

  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;

  void setErrorMessage(String? message) {
    if (mounted) {
      setState(() {
        _errorMessage = message;
      });
    }
  }

  void setLoading(bool loading) {
    if (mounted) {
      setState(() {
        _isLoading = loading;
      });
    }
  }

  void clearError() {
    setErrorMessage(null);
  }

  void handleRegistrationError(dynamic error) {
    RegistrationErrorHandler.handleRegistrationError(
      context,
      error,
      setErrorMessage,
    );
  }

  void showValidationError() {
    RegistrationErrorHandler.showValidationErrorToast(context);
  }

  void showRegistrationSuccess() {
    RegistrationErrorHandler.showSuccessToast(context);
  }
}
