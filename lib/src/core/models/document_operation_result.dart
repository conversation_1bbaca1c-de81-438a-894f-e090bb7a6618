import 'storage_type.dart';

/// Result of a document operation (upload, download, delete, etc.)
class DocumentOperationResult<T> {
  /// Whether the operation was successful
  final bool success;
  
  /// The result data (if successful)
  final T? data;
  
  /// Error message (if failed)
  final String? error;
  
  /// Exception that caused the failure (if any)
  final Exception? exception;
  
  /// Storage type used for the operation
  final StorageType storageType;
  
  /// Operation type performed
  final DocumentOperationType operationType;
  
  /// Duration of the operation
  final Duration? duration;
  
  /// Additional metadata about the operation
  final Map<String, dynamic>? metadata;
  
  /// Whether a fallback storage was used
  final bool usedFallback;
  
  /// Number of retry attempts made
  final int retryAttempts;

  const DocumentOperationResult({
    required this.success,
    this.data,
    this.error,
    this.exception,
    required this.storageType,
    required this.operationType,
    this.duration,
    this.metadata,
    this.usedFallback = false,
    this.retryAttempts = 0,
  });

  /// Create a successful result
  factory DocumentOperationResult.success({
    required T data,
    required StorageType storageType,
    required DocumentOperationType operationType,
    Duration? duration,
    Map<String, dynamic>? metadata,
    bool usedFallback = false,
    int retryAttempts = 0,
  }) {
    return DocumentOperationResult(
      success: true,
      data: data,
      storageType: storageType,
      operationType: operationType,
      duration: duration,
      metadata: metadata,
      usedFallback: usedFallback,
      retryAttempts: retryAttempts,
    );
  }

  /// Create a failed result
  factory DocumentOperationResult.failure({
    required String error,
    Exception? exception,
    required StorageType storageType,
    required DocumentOperationType operationType,
    Duration? duration,
    Map<String, dynamic>? metadata,
    bool usedFallback = false,
    int retryAttempts = 0,
  }) {
    return DocumentOperationResult(
      success: false,
      error: error,
      exception: exception,
      storageType: storageType,
      operationType: operationType,
      duration: duration,
      metadata: metadata,
      usedFallback: usedFallback,
      retryAttempts: retryAttempts,
    );
  }

  /// Create a result from an exception
  factory DocumentOperationResult.fromException({
    required Exception exception,
    required StorageType storageType,
    required DocumentOperationType operationType,
    Duration? duration,
    Map<String, dynamic>? metadata,
    bool usedFallback = false,
    int retryAttempts = 0,
  }) {
    return DocumentOperationResult(
      success: false,
      error: exception.toString(),
      exception: exception,
      storageType: storageType,
      operationType: operationType,
      duration: duration,
      metadata: metadata,
      usedFallback: usedFallback,
      retryAttempts: retryAttempts,
    );
  }

  /// Get the result data or throw an exception if failed
  T get dataOrThrow {
    if (success && data != null) {
      return data!;
    } else if (exception != null) {
      throw exception!;
    } else {
      throw Exception(error ?? 'Operation failed');
    }
  }

  /// Get a user-friendly error message
  String get userFriendlyError {
    if (success) return '';
    
    switch (operationType) {
      case DocumentOperationType.upload:
        return 'Failed to upload document. Please try again.';
      case DocumentOperationType.download:
        return 'Failed to download document. Please check your connection.';
      case DocumentOperationType.delete:
        return 'Failed to delete document. Please try again.';
      case DocumentOperationType.getUrl:
        return 'Failed to get document URL. Please try again.';
      case DocumentOperationType.getMetadata:
        return 'Failed to get document information. Please try again.';
      case DocumentOperationType.list:
        return 'Failed to list documents. Please try again.';
      case DocumentOperationType.search:
        return 'Failed to search documents. Please try again.';
      case DocumentOperationType.copy:
        return 'Failed to copy document. Please try again.';
      case DocumentOperationType.move:
        return 'Failed to move document. Please try again.';
    }
  }

  /// Check if the operation should be retried
  bool get shouldRetry {
    if (success) return false;
    if (retryAttempts >= 3) return false; // Max retries reached
    
    // Don't retry certain types of errors
    if (error?.contains('not found') == true) return false;
    if (error?.contains('unauthorized') == true) return false;
    if (error?.contains('forbidden') == true) return false;
    
    return true;
  }

  /// Get performance grade based on duration
  String get performanceGrade {
    if (duration == null) return 'N/A';
    
    final ms = duration!.inMilliseconds;
    if (ms < 500) return 'A'; // Excellent
    if (ms < 1000) return 'B'; // Good
    if (ms < 2000) return 'C'; // Average
    if (ms < 5000) return 'D'; // Poor
    return 'F'; // Very poor
  }

  /// Copy result with modified values
  DocumentOperationResult<T> copyWith({
    bool? success,
    T? data,
    String? error,
    Exception? exception,
    StorageType? storageType,
    DocumentOperationType? operationType,
    Duration? duration,
    Map<String, dynamic>? metadata,
    bool? usedFallback,
    int? retryAttempts,
  }) {
    return DocumentOperationResult(
      success: success ?? this.success,
      data: data ?? this.data,
      error: error ?? this.error,
      exception: exception ?? this.exception,
      storageType: storageType ?? this.storageType,
      operationType: operationType ?? this.operationType,
      duration: duration ?? this.duration,
      metadata: metadata ?? this.metadata,
      usedFallback: usedFallback ?? this.usedFallback,
      retryAttempts: retryAttempts ?? this.retryAttempts,
    );
  }

  @override
  String toString() {
    return 'DocumentOperationResult(success: $success, operation: $operationType, '
        'storage: $storageType, duration: ${duration?.inMilliseconds}ms, '
        'retries: $retryAttempts, fallback: $usedFallback)';
  }
}

/// Types of document operations
enum DocumentOperationType {
  /// Upload a new document
  upload,
  
  /// Download an existing document
  download,
  
  /// Delete a document
  delete,
  
  /// Get a document URL
  getUrl,
  
  /// Get document metadata
  getMetadata,
  
  /// List documents in a folder
  list,
  
  /// Search for documents
  search,
  
  /// Copy a document
  copy,
  
  /// Move a document
  move,
}

/// Extension for DocumentOperationType
extension DocumentOperationTypeExtension on DocumentOperationType {
  /// Get the string representation
  String get value {
    switch (this) {
      case DocumentOperationType.upload:
        return 'upload';
      case DocumentOperationType.download:
        return 'download';
      case DocumentOperationType.delete:
        return 'delete';
      case DocumentOperationType.getUrl:
        return 'get_url';
      case DocumentOperationType.getMetadata:
        return 'get_metadata';
      case DocumentOperationType.list:
        return 'list';
      case DocumentOperationType.search:
        return 'search';
      case DocumentOperationType.copy:
        return 'copy';
      case DocumentOperationType.move:
        return 'move';
    }
  }

  /// Get a human-readable display name
  String get displayName {
    switch (this) {
      case DocumentOperationType.upload:
        return 'Upload Document';
      case DocumentOperationType.download:
        return 'Download Document';
      case DocumentOperationType.delete:
        return 'Delete Document';
      case DocumentOperationType.getUrl:
        return 'Get Document URL';
      case DocumentOperationType.getMetadata:
        return 'Get Document Metadata';
      case DocumentOperationType.list:
        return 'List Documents';
      case DocumentOperationType.search:
        return 'Search Documents';
      case DocumentOperationType.copy:
        return 'Copy Document';
      case DocumentOperationType.move:
        return 'Move Document';
    }
  }

  /// Check if this operation modifies data
  bool get isModifying {
    switch (this) {
      case DocumentOperationType.upload:
      case DocumentOperationType.delete:
      case DocumentOperationType.copy:
      case DocumentOperationType.move:
        return true;
      case DocumentOperationType.download:
      case DocumentOperationType.getUrl:
      case DocumentOperationType.getMetadata:
      case DocumentOperationType.list:
      case DocumentOperationType.search:
        return false;
    }
  }

  /// Check if this operation should be cached
  bool get shouldCache {
    switch (this) {
      case DocumentOperationType.getUrl:
      case DocumentOperationType.getMetadata:
      case DocumentOperationType.list:
        return true;
      case DocumentOperationType.upload:
      case DocumentOperationType.download:
      case DocumentOperationType.delete:
      case DocumentOperationType.search:
      case DocumentOperationType.copy:
      case DocumentOperationType.move:
        return false;
    }
  }
}
