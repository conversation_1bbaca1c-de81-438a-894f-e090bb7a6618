
/// Model representing a Google Drive file or folder
class GoogleDriveFile {
  final String id;
  final String name;
  final String? mimeType;
  final int? size;
  final DateTime? createdTime;
  final DateTime? modifiedTime;
  final String? webViewLink;
  final String? webContentLink;
  final List<String>? parents;
  final Map<String, String>? properties;
  final String? md5Checksum;
  final String? sha1Checksum;
  final bool? trashed;
  final String? description;
  final Map<String, dynamic>? capabilities;

  const GoogleDriveFile({
    required this.id,
    required this.name,
    this.mimeType,
    this.size,
    this.createdTime,
    this.modifiedTime,
    this.webViewLink,
    this.webContentLink,
    this.parents,
    this.properties,
    this.md5Checksum,
    this.sha1Checksum,
    this.trashed,
    this.description,
    this.capabilities,
  });

  /// Create from Google Drive API response
  factory GoogleDriveFile.fromGoogleDriveApi(Map<String, dynamic> json) {
    return GoogleDriveFile(
      id: json['id'] as String,
      name: json['name'] as String,
      mimeType: json['mimeType'] as String?,
      size: json['size'] != null ? int.tryParse(json['size'].toString()) : null,
      createdTime: json['createdTime'] != null 
          ? DateTime.tryParse(json['createdTime'] as String) 
          : null,
      modifiedTime: json['modifiedTime'] != null 
          ? DateTime.tryParse(json['modifiedTime'] as String) 
          : null,
      webViewLink: json['webViewLink'] as String?,
      webContentLink: json['webContentLink'] as String?,
      parents: json['parents'] != null 
          ? List<String>.from(json['parents'] as List) 
          : null,
      properties: json['properties'] != null 
          ? Map<String, String>.from(json['properties'] as Map) 
          : null,
      md5Checksum: json['md5Checksum'] as String?,
      sha1Checksum: json['sha1Checksum'] as String?,
      trashed: json['trashed'] as bool?,
      description: json['description'] as String?,
      capabilities: json['capabilities'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'mimeType': mimeType,
      'size': size?.toString(),
      'createdTime': createdTime?.toIso8601String(),
      'modifiedTime': modifiedTime?.toIso8601String(),
      'webViewLink': webViewLink,
      'webContentLink': webContentLink,
      'parents': parents,
      'properties': properties,
      'md5Checksum': md5Checksum,
      'sha1Checksum': sha1Checksum,
      'trashed': trashed,
      'description': description,
      'capabilities': capabilities,
    };
  }

  /// Create a copy with updated fields
  GoogleDriveFile copyWith({
    String? id,
    String? name,
    String? mimeType,
    int? size,
    DateTime? createdTime,
    DateTime? modifiedTime,
    String? webViewLink,
    String? webContentLink,
    List<String>? parents,
    Map<String, String>? properties,
    String? md5Checksum,
    String? sha1Checksum,
    bool? trashed,
    String? description,
    Map<String, dynamic>? capabilities,
  }) {
    return GoogleDriveFile(
      id: id ?? this.id,
      name: name ?? this.name,
      mimeType: mimeType ?? this.mimeType,
      size: size ?? this.size,
      createdTime: createdTime ?? this.createdTime,
      modifiedTime: modifiedTime ?? this.modifiedTime,
      webViewLink: webViewLink ?? this.webViewLink,
      webContentLink: webContentLink ?? this.webContentLink,
      parents: parents ?? this.parents,
      properties: properties ?? this.properties,
      md5Checksum: md5Checksum ?? this.md5Checksum,
      sha1Checksum: sha1Checksum ?? this.sha1Checksum,
      trashed: trashed ?? this.trashed,
      description: description ?? this.description,
      capabilities: capabilities ?? this.capabilities,
    );
  }

  /// Check if this is a folder
  bool get isFolder => mimeType == 'application/vnd.google-apps.folder';

  /// Check if this is a Google Workspace document
  bool get isGoogleWorkspaceDocument => 
      mimeType?.startsWith('application/vnd.google-apps.') == true;

  /// Get file extension from name
  String get extension {
    final lastDot = name.lastIndexOf('.');
    return lastDot != -1 ? name.substring(lastDot + 1).toLowerCase() : '';
  }

  /// Get formatted file size
  String get formattedSize {
    if (size == null) return 'Unknown';
    
    if (size! <= 0) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    double fileSize = size!.toDouble();
    int unitIndex = 0;
    
    while (fileSize >= 1024 && unitIndex < units.length - 1) {
      fileSize /= 1024;
      unitIndex++;
    }
    
    return '${fileSize.toStringAsFixed(fileSize < 10 ? 1 : 0)} ${units[unitIndex]}';
  }

  /// Check if file can be downloaded
  bool get canDownload => 
      capabilities?['canDownload'] == true || webContentLink != null;

  /// Check if file can be edited
  bool get canEdit => capabilities?['canEdit'] == true;

  /// Check if file can be shared
  bool get canShare => capabilities?['canShare'] == true;

  /// Check if file can be deleted
  bool get canDelete => capabilities?['canDelete'] == true;

  @override
  String toString() => 'GoogleDriveFile(id: $id, name: $name, isFolder: $isFolder)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GoogleDriveFile &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Upload progress tracking
class GoogleDriveUploadProgress {
  final String fileId;
  final String fileName;
  final int bytesUploaded;
  final int totalBytes;
  final double progress;
  final UploadStatus status;
  final String? error;

  const GoogleDriveUploadProgress({
    required this.fileId,
    required this.fileName,
    required this.bytesUploaded,
    required this.totalBytes,
    required this.progress,
    required this.status,
    this.error,
  });

  GoogleDriveUploadProgress copyWith({
    String? fileId,
    String? fileName,
    int? bytesUploaded,
    int? totalBytes,
    double? progress,
    UploadStatus? status,
    String? error,
  }) {
    return GoogleDriveUploadProgress(
      fileId: fileId ?? this.fileId,
      fileName: fileName ?? this.fileName,
      bytesUploaded: bytesUploaded ?? this.bytesUploaded,
      totalBytes: totalBytes ?? this.totalBytes,
      progress: progress ?? this.progress,
      status: status ?? this.status,
      error: error ?? this.error,
    );
  }

  @override
  String toString() => 
      'GoogleDriveUploadProgress(fileName: $fileName, progress: ${(progress * 100).toStringAsFixed(1)}%, status: $status)';
}

/// Upload status enumeration
enum UploadStatus {
  pending,
  uploading,
  completed,
  failed,
  cancelled,
}

/// Batch operation result
class GoogleDriveBatchResult<T> {
  final List<T> successful;
  final List<GoogleDriveError> failed;
  final int totalCount;

  const GoogleDriveBatchResult({
    required this.successful,
    required this.failed,
    required this.totalCount,
  });

  int get successCount => successful.length;
  int get failureCount => failed.length;
  double get successRate => totalCount > 0 ? successCount / totalCount : 0.0;
  bool get hasFailures => failed.isNotEmpty;
  bool get allSuccessful => failureCount == 0;

  @override
  String toString() => 
      'GoogleDriveBatchResult(total: $totalCount, successful: $successCount, failed: $failureCount)';
}

/// Error information for failed operations
class GoogleDriveError {
  final String operation;
  final String message;
  final String? fileId;
  final String? fileName;
  final DateTime timestamp;
  final Map<String, dynamic>? details;

  const GoogleDriveError({
    required this.operation,
    required this.message,
    this.fileId,
    this.fileName,
    required this.timestamp,
    this.details,
  });

  factory GoogleDriveError.fromException(
    String operation,
    Exception exception, {
    String? fileId,
    String? fileName,
    Map<String, dynamic>? details,
  }) {
    return GoogleDriveError(
      operation: operation,
      message: exception.toString(),
      fileId: fileId,
      fileName: fileName,
      timestamp: DateTime.now(),
      details: details,
    );
  }

  @override
  String toString() => 
      'GoogleDriveError(operation: $operation, message: $message, file: ${fileName ?? fileId})';
}
