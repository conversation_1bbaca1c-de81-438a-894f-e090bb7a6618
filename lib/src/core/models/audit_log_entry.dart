/// Enhanced audit log entry for comprehensive tracking
class AuditLogEntry {
  /// Unique identifier for the audit log entry
  final String id;

  /// Timestamp when the action occurred
  final DateTime timestamp;

  /// User ID who performed the action
  final String? userId;

  /// User email for additional context
  final String? userEmail;

  /// User role at the time of action
  final String? userRole;

  /// Action that was performed
  final String action;

  /// Resource that was affected
  final String? resource;

  /// Resource type (e.g., 'document', 'folder', 'user')
  final String? resourceType;

  /// Resource ID for tracking
  final String? resourceId;

  /// Whether the action was successful
  final bool success;

  /// Error message if action failed
  final String? errorMessage;

  /// IP address from which the action originated
  final String? ipAddress;

  /// User agent string
  final String? userAgent;

  /// Session ID for tracking user sessions
  final String? sessionId;

  /// Additional metadata about the action
  final Map<String, dynamic>? metadata;

  /// Changes made (before/after values)
  final Map<String, dynamic>? changes;

  /// Risk level of the action
  final AuditRiskLevel riskLevel;

  /// Compliance tags for regulatory requirements
  final List<String> complianceTags;

  /// Data classification level
  final DataClassification dataClassification;

  /// Retention period for this log entry (in days)
  final int retentionDays;

  /// Whether this entry is immutable
  final bool isImmutable;

  /// Digital signature for tamper detection
  final String? digitalSignature;

  AuditLogEntry({
    required this.id,
    required this.timestamp,
    this.userId,
    this.userEmail,
    this.userRole,
    required this.action,
    this.resource,
    this.resourceType,
    this.resourceId,
    required this.success,
    this.errorMessage,
    this.ipAddress,
    this.userAgent,
    this.sessionId,
    this.metadata,
    this.changes,
    this.riskLevel = AuditRiskLevel.low,
    this.complianceTags = const [],
    this.dataClassification = DataClassification.internal,
    this.retentionDays = 2555, // 7 years default
    this.isImmutable = true,
    this.digitalSignature,
  });

  factory AuditLogEntry.fromJson(Map<String, dynamic> json) {
    return AuditLogEntry(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      userId: json['userId'] as String?,
      userEmail: json['userEmail'] as String?,
      userRole: json['userRole'] as String?,
      action: json['action'] as String,
      resource: json['resource'] as String?,
      resourceType: json['resourceType'] as String?,
      resourceId: json['resourceId'] as String?,
      success: json['success'] as bool,
      errorMessage: json['errorMessage'] as String?,
      ipAddress: json['ipAddress'] as String?,
      userAgent: json['userAgent'] as String?,
      sessionId: json['sessionId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      changes: json['changes'] as Map<String, dynamic>?,
      riskLevel: AuditRiskLevel.values.firstWhere(
        (e) => e.value == json['riskLevel'],
        orElse: () => AuditRiskLevel.low,
      ),
      complianceTags:
          (json['complianceTags'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      dataClassification: DataClassification.values.firstWhere(
        (e) => e.value == json['dataClassification'],
        orElse: () => DataClassification.internal,
      ),
      retentionDays: json['retentionDays'] as int? ?? 2555,
      isImmutable: json['isImmutable'] as bool? ?? true,
      digitalSignature: json['digitalSignature'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'userId': userId,
      'userEmail': userEmail,
      'userRole': userRole,
      'action': action,
      'resource': resource,
      'resourceType': resourceType,
      'resourceId': resourceId,
      'success': success,
      'errorMessage': errorMessage,
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'sessionId': sessionId,
      'metadata': metadata,
      'changes': changes,
      'riskLevel': riskLevel.value,
      'complianceTags': complianceTags,
      'dataClassification': dataClassification.value,
      'retentionDays': retentionDays,
      'isImmutable': isImmutable,
      'digitalSignature': digitalSignature,
    };
  }

  /// Create audit log for document access
  factory AuditLogEntry.documentAccess({
    required String id,
    required String userId,
    required String action,
    required String documentId,
    required bool success,
    String? userEmail,
    String? userRole,
    String? errorMessage,
    String? ipAddress,
    Map<String, dynamic>? metadata,
  }) {
    return AuditLogEntry(
      id: id,
      timestamp: DateTime.now(),
      userId: userId,
      userEmail: userEmail,
      userRole: userRole,
      action: action,
      resource: 'document',
      resourceType: 'document',
      resourceId: documentId,
      success: success,
      errorMessage: errorMessage,
      ipAddress: ipAddress,
      metadata: metadata,
      riskLevel: _determineRiskLevel(action, success),
      complianceTags: ['GDPR', 'document_access'],
      dataClassification: DataClassification.confidential,
    );
  }

  /// Create audit log for user authentication
  factory AuditLogEntry.authentication({
    required String id,
    required String userId,
    required bool success,
    String? userEmail,
    String? errorMessage,
    String? ipAddress,
    String? userAgent,
    String? sessionId,
    Map<String, dynamic>? metadata,
  }) {
    return AuditLogEntry(
      id: id,
      timestamp: DateTime.now(),
      userId: userId,
      userEmail: userEmail,
      action: 'authentication',
      resource: 'user_session',
      resourceType: 'session',
      resourceId: sessionId,
      success: success,
      errorMessage: errorMessage,
      ipAddress: ipAddress,
      userAgent: userAgent,
      sessionId: sessionId,
      metadata: metadata,
      riskLevel: success ? AuditRiskLevel.low : AuditRiskLevel.medium,
      complianceTags: ['authentication', 'security'],
      dataClassification: DataClassification.internal,
    );
  }

  /// Create audit log for permission changes
  factory AuditLogEntry.permissionChange({
    required String id,
    required String userId,
    required String resourceId,
    required Map<String, dynamic> changes,
    required bool success,
    String? userEmail,
    String? userRole,
    String? errorMessage,
    String? ipAddress,
    Map<String, dynamic>? metadata,
  }) {
    return AuditLogEntry(
      id: id,
      timestamp: DateTime.now(),
      userId: userId,
      userEmail: userEmail,
      userRole: userRole,
      action: 'permission_change',
      resource: 'permissions',
      resourceType: 'permission',
      resourceId: resourceId,
      success: success,
      errorMessage: errorMessage,
      ipAddress: ipAddress,
      metadata: metadata,
      changes: changes,
      riskLevel: AuditRiskLevel.high,
      complianceTags: ['GDPR', 'permission_management', 'security'],
      dataClassification: DataClassification.confidential,
    );
  }

  /// Create audit log for data processing
  factory AuditLogEntry.dataProcessing({
    required String id,
    required String userId,
    required String action,
    required String dataType,
    required bool success,
    String? userEmail,
    String? userRole,
    String? errorMessage,
    String? ipAddress,
    Map<String, dynamic>? metadata,
  }) {
    return AuditLogEntry(
      id: id,
      timestamp: DateTime.now(),
      userId: userId,
      userEmail: userEmail,
      userRole: userRole,
      action: action,
      resource: dataType,
      resourceType: 'data',
      success: success,
      errorMessage: errorMessage,
      ipAddress: ipAddress,
      metadata: metadata,
      riskLevel: _determineDataProcessingRisk(action),
      complianceTags: ['GDPR', 'data_processing'],
      dataClassification: DataClassification.confidential,
    );
  }

  /// Determine risk level based on action and success
  static AuditRiskLevel _determineRiskLevel(String action, bool success) {
    if (!success) return AuditRiskLevel.medium;

    switch (action.toLowerCase()) {
      case 'delete':
      case 'modify':
      case 'share':
        return AuditRiskLevel.medium;
      case 'download':
      case 'view':
        return AuditRiskLevel.low;
      default:
        return AuditRiskLevel.low;
    }
  }

  /// Determine risk level for data processing actions
  static AuditRiskLevel _determineDataProcessingRisk(String action) {
    switch (action.toLowerCase()) {
      case 'delete':
      case 'anonymize':
      case 'export':
        return AuditRiskLevel.high;
      case 'modify':
      case 'share':
        return AuditRiskLevel.medium;
      default:
        return AuditRiskLevel.low;
    }
  }

  @override
  String toString() {
    return 'AuditLogEntry{id: $id, action: $action, success: $success, riskLevel: $riskLevel}';
  }
}

/// Risk levels for audit entries
enum AuditRiskLevel { low, medium, high, critical }

/// Data classification levels
enum DataClassification { public, internal, confidential, restricted }

/// Extensions for enums
extension AuditRiskLevelExtension on AuditRiskLevel {
  String get value {
    switch (this) {
      case AuditRiskLevel.low:
        return 'low';
      case AuditRiskLevel.medium:
        return 'medium';
      case AuditRiskLevel.high:
        return 'high';
      case AuditRiskLevel.critical:
        return 'critical';
    }
  }
}

extension DataClassificationExtension on DataClassification {
  String get value {
    switch (this) {
      case DataClassification.public:
        return 'public';
      case DataClassification.internal:
        return 'internal';
      case DataClassification.confidential:
        return 'confidential';
      case DataClassification.restricted:
        return 'restricted';
    }
  }
}
