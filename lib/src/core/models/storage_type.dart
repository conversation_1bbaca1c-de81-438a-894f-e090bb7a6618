/// Enumeration of available storage types for document management
enum StorageType {
  /// PocketBase file storage (legacy)
  pocketbase,

  /// Google Drive storage (primary)
  googleDrive,

  /// Hybrid storage (both PocketBase and Google Drive)
  hybrid,
}

/// Extension for StorageType to provide utility methods
extension StorageTypeExtension on StorageType {
  /// Get the string representation of the storage type
  String get value {
    switch (this) {
      case StorageType.pocketbase:
        return 'pocketbase';
      case StorageType.googleDrive:
        return 'google_drive';
      case StorageType.hybrid:
        return 'hybrid';
    }
  }

  /// Get a human-readable display name
  String get displayName {
    switch (this) {
      case StorageType.pocketbase:
        return 'PocketBase Storage';
      case StorageType.googleDrive:
        return 'Google Drive Storage';
      case StorageType.hybrid:
        return 'Hybrid Storage';
    }
  }

  /// Check if this storage type supports caching
  bool get supportsCaching {
    switch (this) {
      case StorageType.pocketbase:
        return false; // PocketBase URLs are direct
      case StorageType.googleDrive:
        return true; // Google Drive URLs expire and benefit from caching
      case StorageType.hybrid:
        return true; // Hybrid mode uses caching for Google Drive
    }
  }

  /// Check if this storage type requires authentication
  bool get requiresAuth {
    switch (this) {
      case StorageType.pocketbase:
        return true; // PocketBase requires user authentication
      case StorageType.googleDrive:
        return true; // Google Drive requires service account auth
      case StorageType.hybrid:
        return true; // Both require authentication
    }
  }

  /// Get the priority order for storage operations (lower = higher priority)
  int get priority {
    switch (this) {
      case StorageType.googleDrive:
        return 1; // Highest priority - primary storage
      case StorageType.hybrid:
        return 2; // Medium priority
      case StorageType.pocketbase:
        return 3; // Lowest priority - legacy fallback
    }
  }

  /// Check if this storage type supports versioning
  bool get supportsVersioning {
    switch (this) {
      case StorageType.pocketbase:
        return true; // PocketBase supports multiple files per record
      case StorageType.googleDrive:
        return true; // Google Drive has native versioning
      case StorageType.hybrid:
        return true; // Both support versioning
    }
  }

  /// Check if this storage type supports metadata
  bool get supportsMetadata {
    switch (this) {
      case StorageType.pocketbase:
        return true; // PocketBase stores metadata in database
      case StorageType.googleDrive:
        return true; // Google Drive supports custom properties
      case StorageType.hybrid:
        return true; // Both support metadata
    }
  }

  /// Get maximum file size supported (in bytes)
  int get maxFileSize {
    switch (this) {
      case StorageType.pocketbase:
        return 100 * 1024 * 1024; // 100MB typical PocketBase limit
      case StorageType.googleDrive:
        return 5 * 1024 * 1024 * 1024; // 5GB Google Drive limit
      case StorageType.hybrid:
        return 5 * 1024 * 1024 * 1024; // Use Google Drive limit for hybrid
    }
  }
}

/// Parse StorageType from string value
StorageType? parseStorageType(String? value) {
  if (value == null) return null;

  for (final type in StorageType.values) {
    if (type.value == value.toLowerCase()) {
      return type;
    }
  }
  return null;
}

/// Configuration for storage behavior
class StorageConfiguration {
  /// Primary storage type to use
  final StorageType primaryStorage;

  /// Fallback storage type if primary fails
  final StorageType? fallbackStorage;

  /// Whether to enable caching for supported storage types
  final bool enableCaching;

  /// Whether to enable automatic retry on failures
  final bool enableRetry;

  /// Maximum number of retry attempts
  final int maxRetryAttempts;

  /// Whether to enable parallel uploads for hybrid mode
  final bool enableParallelUploads;

  /// Whether to verify uploads after completion
  final bool verifyUploads;

  const StorageConfiguration({
    this.primaryStorage = StorageType.googleDrive,
    this.fallbackStorage = StorageType.pocketbase,
    this.enableCaching = true,
    this.enableRetry = true,
    this.maxRetryAttempts = 3,
    this.enableParallelUploads = false,
    this.verifyUploads = true,
  });

  /// Create configuration for development environment
  factory StorageConfiguration.development() {
    return const StorageConfiguration(
      primaryStorage: StorageType.googleDrive,
      fallbackStorage: StorageType.pocketbase,
      enableCaching: true,
      enableRetry: true,
      maxRetryAttempts: 2,
      enableParallelUploads: false,
      verifyUploads: false, // Skip verification in development
    );
  }

  /// Create configuration for production environment
  factory StorageConfiguration.production() {
    return const StorageConfiguration(
      primaryStorage: StorageType.googleDrive,
      fallbackStorage: null, // No fallback in production
      enableCaching: true,
      enableRetry: true,
      maxRetryAttempts: 5,
      enableParallelUploads: true,
      verifyUploads: true,
    );
  }

  /// Create configuration for testing environment
  factory StorageConfiguration.testing() {
    return const StorageConfiguration(
      primaryStorage: StorageType.pocketbase, // Use PocketBase for testing
      fallbackStorage: null,
      enableCaching: false,
      enableRetry: false,
      maxRetryAttempts: 1,
      enableParallelUploads: false,
      verifyUploads: false,
    );
  }

  /// Copy configuration with modified values
  StorageConfiguration copyWith({
    StorageType? primaryStorage,
    StorageType? fallbackStorage,
    bool? enableCaching,
    bool? enableRetry,
    int? maxRetryAttempts,
    bool? enableParallelUploads,
    bool? verifyUploads,
  }) {
    return StorageConfiguration(
      primaryStorage: primaryStorage ?? this.primaryStorage,
      fallbackStorage: fallbackStorage ?? this.fallbackStorage,
      enableCaching: enableCaching ?? this.enableCaching,
      enableRetry: enableRetry ?? this.enableRetry,
      maxRetryAttempts: maxRetryAttempts ?? this.maxRetryAttempts,
      enableParallelUploads:
          enableParallelUploads ?? this.enableParallelUploads,
      verifyUploads: verifyUploads ?? this.verifyUploads,
    );
  }

  @override
  String toString() {
    return 'StorageConfiguration(primary: $primaryStorage, fallback: $fallbackStorage, '
        'caching: $enableCaching, retry: $enableRetry, maxRetries: $maxRetryAttempts)';
  }
}
