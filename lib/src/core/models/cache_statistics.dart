import 'cache_entry.dart';

/// Statistics for cache performance monitoring
class CacheStatistics {
  /// Total number of cache hits
  final int totalHits;

  /// Total number of cache misses
  final int totalMisses;

  /// Total number of cache entries
  final int totalEntries;

  /// Total size of cached data in bytes
  final int totalSizeBytes;

  /// Statistics by cache type
  final Map<CacheType, CacheTypeStatistics> typeStatistics;

  /// When these statistics were last updated
  final DateTime lastUpdated;

  /// Average access time in milliseconds
  final double averageAccessTimeMs;

  /// Memory usage in bytes
  final int memoryUsageBytes;

  /// Disk usage in bytes
  final int diskUsageBytes;

  /// Number of cache evictions
  final int totalEvictions;

  /// Number of cache invalidations
  final int totalInvalidations;

  const CacheStatistics({
    required this.totalHits,
    required this.totalMisses,
    required this.totalEntries,
    required this.totalSizeBytes,
    required this.typeStatistics,
    required this.lastUpdated,
    required this.averageAccessTimeMs,
    required this.memoryUsageBytes,
    required this.diskUsageBytes,
    required this.totalEvictions,
    required this.totalInvalidations,
  });

  /// Create empty statistics
  factory CacheStatistics.empty() {
    return CacheStatistics(
      totalHits: 0,
      totalMisses: 0,
      totalEntries: 0,
      totalSizeBytes: 0,
      typeStatistics: {},
      lastUpdated: DateTime.now(),
      averageAccessTimeMs: 0.0,
      memoryUsageBytes: 0,
      diskUsageBytes: 0,
      totalEvictions: 0,
      totalInvalidations: 0,
    );
  }

  /// Calculate hit ratio (0.0 to 1.0)
  double get hitRatio {
    final total = totalHits + totalMisses;
    if (total == 0) return 0.0;
    return totalHits / total;
  }

  /// Calculate miss ratio (0.0 to 1.0)
  double get missRatio {
    final total = totalHits + totalMisses;
    if (total == 0) return 0.0;
    return totalMisses / total;
  }

  /// Get hit ratio as percentage
  double get hitRatioPercentage => hitRatio * 100;

  /// Get miss ratio as percentage
  double get missRatioPercentage => missRatio * 100;

  /// Get total cache size in MB
  double get totalSizeMB => totalSizeBytes / (1024 * 1024);

  /// Get memory usage in MB
  double get memoryUsageMB => memoryUsageBytes / (1024 * 1024);

  /// Get disk usage in MB
  double get diskUsageMB => diskUsageBytes / (1024 * 1024);

  /// Check if cache performance is healthy
  bool get isHealthy {
    // Consider cache healthy if hit ratio > 70% and average access time < 100ms
    return hitRatio > 0.7 && averageAccessTimeMs < 100;
  }

  /// Get performance grade (A-F)
  String get performanceGrade {
    if (hitRatio >= 0.9 && averageAccessTimeMs < 50) return 'A';
    if (hitRatio >= 0.8 && averageAccessTimeMs < 75) return 'B';
    if (hitRatio >= 0.7 && averageAccessTimeMs < 100) return 'C';
    if (hitRatio >= 0.6 && averageAccessTimeMs < 150) return 'D';
    return 'F';
  }

  /// Create a copy with updated values
  CacheStatistics copyWith({
    int? totalHits,
    int? totalMisses,
    int? totalEntries,
    int? totalSizeBytes,
    Map<CacheType, CacheTypeStatistics>? typeStatistics,
    DateTime? lastUpdated,
    double? averageAccessTimeMs,
    int? memoryUsageBytes,
    int? diskUsageBytes,
    int? totalEvictions,
    int? totalInvalidations,
  }) {
    return CacheStatistics(
      totalHits: totalHits ?? this.totalHits,
      totalMisses: totalMisses ?? this.totalMisses,
      totalEntries: totalEntries ?? this.totalEntries,
      totalSizeBytes: totalSizeBytes ?? this.totalSizeBytes,
      typeStatistics: typeStatistics ?? this.typeStatistics,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      averageAccessTimeMs: averageAccessTimeMs ?? this.averageAccessTimeMs,
      memoryUsageBytes: memoryUsageBytes ?? this.memoryUsageBytes,
      diskUsageBytes: diskUsageBytes ?? this.diskUsageBytes,
      totalEvictions: totalEvictions ?? this.totalEvictions,
      totalInvalidations: totalInvalidations ?? this.totalInvalidations,
    );
  }

  @override
  String toString() {
    return 'CacheStatistics(entries: $totalEntries, '
        'hitRatio: ${hitRatioPercentage.toStringAsFixed(1)}%, '
        'size: ${totalSizeMB.toStringAsFixed(1)}MB, '
        'avgAccess: ${averageAccessTimeMs.toStringAsFixed(1)}ms, '
        'grade: $performanceGrade)';
  }
}

/// Statistics for a specific cache type
class CacheTypeStatistics {
  /// Number of hits for this cache type
  final int hits;

  /// Number of misses for this cache type
  final int misses;

  /// Number of entries for this cache type
  final int entries;

  /// Total size in bytes for this cache type
  final int sizeBytes;

  /// Number of evictions for this cache type
  final int evictions;

  /// Average access time for this cache type
  final double averageAccessTimeMs;

  /// Cache type
  final CacheType type;

  const CacheTypeStatistics({
    required this.hits,
    required this.misses,
    required this.entries,
    required this.sizeBytes,
    required this.evictions,
    required this.averageAccessTimeMs,
    required this.type,
  });

  /// Create empty statistics for a cache type
  factory CacheTypeStatistics.empty(CacheType type) {
    return CacheTypeStatistics(
      hits: 0,
      misses: 0,
      entries: 0,
      sizeBytes: 0,
      evictions: 0,
      averageAccessTimeMs: 0.0,
      type: type,
    );
  }

  /// Calculate hit ratio for this cache type
  double get hitRatio {
    final total = hits + misses;
    if (total == 0) return 0.0;
    return hits / total;
  }

  /// Get hit ratio as percentage
  double get hitRatioPercentage => hitRatio * 100;

  /// Get size in MB
  double get sizeMB => sizeBytes / (1024 * 1024);

  /// Create a copy with updated values
  CacheTypeStatistics copyWith({
    int? hits,
    int? misses,
    int? entries,
    int? sizeBytes,
    int? evictions,
    double? averageAccessTimeMs,
  }) {
    return CacheTypeStatistics(
      hits: hits ?? this.hits,
      misses: misses ?? this.misses,
      entries: entries ?? this.entries,
      sizeBytes: sizeBytes ?? this.sizeBytes,
      evictions: evictions ?? this.evictions,
      averageAccessTimeMs: averageAccessTimeMs ?? this.averageAccessTimeMs,
      type: type,
    );
  }

  @override
  String toString() {
    return 'CacheTypeStatistics(type: $type, entries: $entries, '
        'hitRatio: ${hitRatioPercentage.toStringAsFixed(1)}%, '
        'size: ${sizeMB.toStringAsFixed(1)}MB)';
  }
}
