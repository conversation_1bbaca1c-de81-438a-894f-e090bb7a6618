/// Model representing Google Drive file permissions
class GoogleDrivePermission {
  final String id;
  final String type;
  final String role;
  final String? emailAddress;
  final String? domain;
  final String? displayName;
  final String? photoLink;
  final bool? allowFileDiscovery;
  final DateTime? expirationTime;
  final bool? deleted;

  const GoogleDrivePermission({
    required this.id,
    required this.type,
    required this.role,
    this.emailAddress,
    this.domain,
    this.displayName,
    this.photoLink,
    this.allowFileDiscovery,
    this.expirationTime,
    this.deleted,
  });

  /// Create from Google Drive API response
  factory GoogleDrivePermission.fromGoogleDriveApi(Map<String, dynamic> json) {
    return GoogleDrivePermission(
      id: json['id'] as String,
      type: json['type'] as String,
      role: json['role'] as String,
      emailAddress: json['emailAddress'] as String?,
      domain: json['domain'] as String?,
      displayName: json['displayName'] as String?,
      photoLink: json['photoLink'] as String?,
      allowFileDiscovery: json['allowFileDiscovery'] as bool?,
      expirationTime: json['expirationTime'] != null 
          ? DateTime.tryParse(json['expirationTime'] as String) 
          : null,
      deleted: json['deleted'] as bool?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'role': role,
      'emailAddress': emailAddress,
      'domain': domain,
      'displayName': displayName,
      'photoLink': photoLink,
      'allowFileDiscovery': allowFileDiscovery,
      'expirationTime': expirationTime?.toIso8601String(),
      'deleted': deleted,
    };
  }

  /// Check if permission is expired
  bool get isExpired => 
      expirationTime != null && DateTime.now().isAfter(expirationTime!);

  /// Check if permission is active
  bool get isActive => !isExpired && deleted != true;

  @override
  String toString() => 
      'GoogleDrivePermission(type: $type, role: $role, email: $emailAddress)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GoogleDrivePermission &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Permission type enumeration
enum GoogleDrivePermissionType {
  user('user'),
  group('group'),
  domain('domain'),
  anyone('anyone');

  const GoogleDrivePermissionType(this.value);
  final String value;
}

/// Permission role enumeration
enum GoogleDrivePermissionRole {
  owner('owner'),
  organizer('organizer'),
  fileOrganizer('fileOrganizer'),
  writer('writer'),
  commenter('commenter'),
  reader('reader');

  const GoogleDrivePermissionRole(this.value);
  final String value;
}

/// Builder for creating permission data
class GoogleDrivePermissionBuilder {
  GoogleDrivePermissionType? _type;
  GoogleDrivePermissionRole? _role;
  String? _emailAddress;
  String? _domain;
  bool? _allowFileDiscovery;
  DateTime? _expirationTime;

  GoogleDrivePermissionBuilder type(GoogleDrivePermissionType type) {
    _type = type;
    return this;
  }

  GoogleDrivePermissionBuilder role(GoogleDrivePermissionRole role) {
    _role = role;
    return this;
  }

  GoogleDrivePermissionBuilder emailAddress(String emailAddress) {
    _emailAddress = emailAddress;
    return this;
  }

  GoogleDrivePermissionBuilder domain(String domain) {
    _domain = domain;
    return this;
  }

  GoogleDrivePermissionBuilder allowFileDiscovery(bool allow) {
    _allowFileDiscovery = allow;
    return this;
  }

  GoogleDrivePermissionBuilder expirationTime(DateTime? expiration) {
    _expirationTime = expiration;
    return this;
  }

  Map<String, dynamic> build() {
    if (_type == null || _role == null) {
      throw ArgumentError('Type and role are required');
    }

    final permission = <String, dynamic>{
      'type': _type!.value,
      'role': _role!.value,
    };

    if (_emailAddress != null) {
      permission['emailAddress'] = _emailAddress;
    }

    if (_domain != null) {
      permission['domain'] = _domain;
    }

    if (_allowFileDiscovery != null) {
      permission['allowFileDiscovery'] = _allowFileDiscovery;
    }

    if (_expirationTime != null) {
      permission['expirationTime'] = _expirationTime!.toIso8601String();
    }

    return permission;
  }
}

/// Pre-defined permission templates
class GoogleDrivePermissionTemplates {
  /// Public reader permission (anyone with link can view)
  static Map<String, dynamic> publicReader() {
    return GoogleDrivePermissionBuilder()
        .type(GoogleDrivePermissionType.anyone)
        .role(GoogleDrivePermissionRole.reader)
        .allowFileDiscovery(false)
        .build();
  }

  /// User reader permission
  static Map<String, dynamic> userReader(String emailAddress) {
    return GoogleDrivePermissionBuilder()
        .type(GoogleDrivePermissionType.user)
        .role(GoogleDrivePermissionRole.reader)
        .emailAddress(emailAddress)
        .build();
  }

  /// User writer permission
  static Map<String, dynamic> userWriter(String emailAddress) {
    return GoogleDrivePermissionBuilder()
        .type(GoogleDrivePermissionType.user)
        .role(GoogleDrivePermissionRole.writer)
        .emailAddress(emailAddress)
        .build();
  }

  /// User commenter permission
  static Map<String, dynamic> userCommenter(String emailAddress) {
    return GoogleDrivePermissionBuilder()
        .type(GoogleDrivePermissionType.user)
        .role(GoogleDrivePermissionRole.commenter)
        .emailAddress(emailAddress)
        .build();
  }

  /// Domain reader permission
  static Map<String, dynamic> domainReader(String domain) {
    return GoogleDrivePermissionBuilder()
        .type(GoogleDrivePermissionType.domain)
        .role(GoogleDrivePermissionRole.reader)
        .domain(domain)
        .build();
  }

  /// Domain writer permission
  static Map<String, dynamic> domainWriter(String domain) {
    return GoogleDrivePermissionBuilder()
        .type(GoogleDrivePermissionType.domain)
        .role(GoogleDrivePermissionRole.writer)
        .domain(domain)
        .build();
  }

  /// Temporary reader permission (expires after specified duration)
  static Map<String, dynamic> temporaryReader(
    String emailAddress,
    Duration duration,
  ) {
    return GoogleDrivePermissionBuilder()
        .type(GoogleDrivePermissionType.user)
        .role(GoogleDrivePermissionRole.reader)
        .emailAddress(emailAddress)
        .expirationTime(DateTime.now().add(duration))
        .build();
  }

  /// Group reader permission
  static Map<String, dynamic> groupReader(String groupEmail) {
    return GoogleDrivePermissionBuilder()
        .type(GoogleDrivePermissionType.group)
        .role(GoogleDrivePermissionRole.reader)
        .emailAddress(groupEmail)
        .build();
  }

  /// Group writer permission
  static Map<String, dynamic> groupWriter(String groupEmail) {
    return GoogleDrivePermissionBuilder()
        .type(GoogleDrivePermissionType.group)
        .role(GoogleDrivePermissionRole.writer)
        .emailAddress(groupEmail)
        .build();
  }

  /// Get permission template based on user role in 3Pay system
  static Map<String, dynamic> forUserRole(
    String emailAddress,
    String userRole,
  ) {
    switch (userRole.toLowerCase()) {
      case 'admin':
      case 'solicitor':
        return userWriter(emailAddress);
      case 'claimant':
      case 'co_funder':
        return userReader(emailAddress);
      default:
        return userReader(emailAddress);
    }
  }
}

/// Permission management utilities
class GoogleDrivePermissionUtils {
  /// Check if role allows editing
  static bool canEdit(String role) {
    return ['owner', 'organizer', 'fileOrganizer', 'writer'].contains(role);
  }

  /// Check if role allows commenting
  static bool canComment(String role) {
    return ['owner', 'organizer', 'fileOrganizer', 'writer', 'commenter']
        .contains(role);
  }

  /// Check if role allows viewing
  static bool canView(String role) {
    return ['owner', 'organizer', 'fileOrganizer', 'writer', 'commenter', 'reader']
        .contains(role);
  }

  /// Get role hierarchy level (higher number = more permissions)
  static int getRoleLevel(String role) {
    switch (role) {
      case 'owner':
        return 6;
      case 'organizer':
        return 5;
      case 'fileOrganizer':
        return 4;
      case 'writer':
        return 3;
      case 'commenter':
        return 2;
      case 'reader':
        return 1;
      default:
        return 0;
    }
  }

  /// Compare two roles (returns positive if role1 > role2)
  static int compareRoles(String role1, String role2) {
    return getRoleLevel(role1) - getRoleLevel(role2);
  }

  /// Get user-friendly role name
  static String getRoleDisplayName(String role) {
    switch (role) {
      case 'owner':
        return 'Owner';
      case 'organizer':
        return 'Organizer';
      case 'fileOrganizer':
        return 'File Organizer';
      case 'writer':
        return 'Editor';
      case 'commenter':
        return 'Commenter';
      case 'reader':
        return 'Viewer';
      default:
        return role;
    }
  }
}
