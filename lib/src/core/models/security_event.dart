/// Represents a security event in the system
class SecurityEvent {
  /// Unique identifier for the security event
  final String id;

  /// Type of security event (e.g., 'authentication', 'file_access', 'permission_change')
  final String type;

  /// Timestamp when the event occurred
  final DateTime timestamp;

  /// Whether the security operation was successful
  final bool success;

  /// User ID associated with the event (if applicable)
  final String? userId;

  /// IP address from which the event originated
  final String? ipAddress;

  /// User agent string (for web requests)
  final String? userAgent;

  /// Resource that was accessed or modified
  final String? resource;

  /// Action that was performed
  final String? action;

  /// Additional details about the event
  final Map<String, dynamic>? details;

  /// Error message if the operation failed
  final String? errorMessage;

  /// Severity level of the security event
  final SecurityEventSeverity severity;

  /// Risk score associated with the event (0-100)
  final int riskScore;

  /// Whether this event requires immediate attention
  final bool requiresAttention;

  /// Tags for categorizing the event
  final List<String> tags;

  SecurityEvent({
    required this.id,
    required this.type,
    required this.timestamp,
    required this.success,
    this.userId,
    this.ipAddress,
    this.userAgent,
    this.resource,
    this.action,
    this.details,
    this.errorMessage,
    this.severity = SecurityEventSeverity.low,
    this.riskScore = 0,
    this.requiresAttention = false,
    this.tags = const [],
  });

  factory SecurityEvent.fromJson(Map<String, dynamic> json) {
    return SecurityEvent(
      id: json['id'] as String,
      type: json['type'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      success: json['success'] as bool,
      userId: json['userId'] as String?,
      ipAddress: json['ipAddress'] as String?,
      userAgent: json['userAgent'] as String?,
      resource: json['resource'] as String?,
      action: json['action'] as String?,
      details: json['details'] as Map<String, dynamic>?,
      errorMessage: json['errorMessage'] as String?,
      severity: SecurityEventSeverityExtension.fromString(
        json['severity'] as String? ?? 'low',
      ),
      riskScore: json['riskScore'] as int? ?? 0,
      requiresAttention: json['requiresAttention'] as bool? ?? false,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'timestamp': timestamp.toIso8601String(),
      'success': success,
      'userId': userId,
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'resource': resource,
      'action': action,
      'details': details,
      'errorMessage': errorMessage,
      'severity': severity.value,
      'riskScore': riskScore,
      'requiresAttention': requiresAttention,
      'tags': tags,
    };
  }

  /// Create a security event for authentication
  factory SecurityEvent.authentication({
    required String id,
    required bool success,
    required String userId,
    String? ipAddress,
    String? userAgent,
    String? errorMessage,
    Map<String, dynamic>? details,
  }) {
    return SecurityEvent(
      id: id,
      type: 'authentication',
      timestamp: DateTime.now(),
      success: success,
      userId: userId,
      ipAddress: ipAddress,
      userAgent: userAgent,
      action: 'login',
      errorMessage: errorMessage,
      details: details,
      severity:
          success ? SecurityEventSeverity.low : SecurityEventSeverity.medium,
      riskScore: success ? 10 : 50,
      requiresAttention: !success,
      tags: success ? ['auth', 'success'] : ['auth', 'failure'],
    );
  }

  /// Create a security event for file access
  factory SecurityEvent.fileAccess({
    required String id,
    required bool success,
    required String userId,
    required String resource,
    required String action,
    String? ipAddress,
    String? errorMessage,
    Map<String, dynamic>? details,
  }) {
    return SecurityEvent(
      id: id,
      type: 'file_access',
      timestamp: DateTime.now(),
      success: success,
      userId: userId,
      ipAddress: ipAddress,
      resource: resource,
      action: action,
      errorMessage: errorMessage,
      details: details,
      severity:
          success ? SecurityEventSeverity.low : SecurityEventSeverity.medium,
      riskScore: success ? 5 : 30,
      requiresAttention: !success,
      tags:
          success
              ? ['file', 'access', 'success']
              : ['file', 'access', 'failure'],
    );
  }

  /// Create a security event for permission changes
  factory SecurityEvent.permissionChange({
    required String id,
    required bool success,
    required String userId,
    required String resource,
    String? ipAddress,
    String? errorMessage,
    Map<String, dynamic>? details,
  }) {
    return SecurityEvent(
      id: id,
      type: 'permission_change',
      timestamp: DateTime.now(),
      success: success,
      userId: userId,
      ipAddress: ipAddress,
      resource: resource,
      action: 'permission_change',
      errorMessage: errorMessage,
      details: details,
      severity: SecurityEventSeverity.high,
      riskScore: 70,
      requiresAttention: true,
      tags: ['permission', 'change', success ? 'success' : 'failure'],
    );
  }

  /// Create a security event for suspicious activity
  factory SecurityEvent.suspiciousActivity({
    required String id,
    required String type,
    required String userId,
    required String description,
    String? ipAddress,
    String? resource,
    Map<String, dynamic>? details,
  }) {
    return SecurityEvent(
      id: id,
      type: type,
      timestamp: DateTime.now(),
      success: false,
      userId: userId,
      ipAddress: ipAddress,
      resource: resource,
      action: 'suspicious_activity',
      errorMessage: description,
      details: details,
      severity: SecurityEventSeverity.critical,
      riskScore: 90,
      requiresAttention: true,
      tags: ['suspicious', 'security', 'alert'],
    );
  }

  @override
  String toString() {
    return 'SecurityEvent{id: $id, type: $type, success: $success, severity: $severity, riskScore: $riskScore}';
  }
}

/// Severity levels for security events
enum SecurityEventSeverity { low, medium, high, critical }

/// Extension for SecurityEventSeverity
extension SecurityEventSeverityExtension on SecurityEventSeverity {
  String get value {
    switch (this) {
      case SecurityEventSeverity.low:
        return 'low';
      case SecurityEventSeverity.medium:
        return 'medium';
      case SecurityEventSeverity.high:
        return 'high';
      case SecurityEventSeverity.critical:
        return 'critical';
    }
  }

  static SecurityEventSeverity fromString(String value) {
    switch (value.toLowerCase()) {
      case 'low':
        return SecurityEventSeverity.low;
      case 'medium':
        return SecurityEventSeverity.medium;
      case 'high':
        return SecurityEventSeverity.high;
      case 'critical':
        return SecurityEventSeverity.critical;
      default:
        return SecurityEventSeverity.low;
    }
  }
}
