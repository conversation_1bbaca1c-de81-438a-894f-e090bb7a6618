/// Represents a cached entry with metadata
class CacheEntry<T> {
  /// The cached data
  final T data;

  /// When this entry was created
  final DateTime createdAt;

  /// When this entry was last accessed
  final DateTime lastAccessedAt;

  /// When this entry expires (null means no expiration)
  final DateTime? expiresAt;

  /// Size of the cached data in bytes
  final int sizeBytes;

  /// Number of times this entry has been accessed
  final int accessCount;

  /// Cache key for this entry
  final String key;

  /// Cache type (url, metadata, content, permission)
  final CacheType type;

  /// Additional metadata for this cache entry
  final Map<String, dynamic>? metadata;

  const CacheEntry({
    required this.data,
    required this.createdAt,
    required this.lastAccessedAt,
    required this.sizeBytes,
    required this.accessCount,
    required this.key,
    required this.type,
    this.expiresAt,
    this.metadata,
  });

  /// Create a new cache entry
  factory CacheEntry.create({
    required T data,
    required String key,
    required CacheType type,
    required int sizeBytes,
    Duration? ttl,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    return CacheEntry(
      data: data,
      createdAt: now,
      lastAccessedAt: now,
      expiresAt: ttl != null ? now.add(ttl) : null,
      sizeBytes: sizeBytes,
      accessCount: 1,
      key: key,
      type: type,
      metadata: metadata,
    );
  }

  /// Check if this cache entry is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if this cache entry is still valid
  bool get isValid => !isExpired;

  /// Get the age of this cache entry
  Duration get age => DateTime.now().difference(createdAt);

  /// Get time since last access
  Duration get timeSinceLastAccess => DateTime.now().difference(lastAccessedAt);

  /// Create a copy with updated access information
  CacheEntry<T> copyWithAccess() {
    return CacheEntry(
      data: data,
      createdAt: createdAt,
      lastAccessedAt: DateTime.now(),
      expiresAt: expiresAt,
      sizeBytes: sizeBytes,
      accessCount: accessCount + 1,
      key: key,
      type: type,
      metadata: metadata,
    );
  }

  /// Create a copy with updated data
  CacheEntry<T> copyWithData({
    required T newData,
    required int newSizeBytes,
    Duration? newTtl,
    Map<String, dynamic>? newMetadata,
  }) {
    final now = DateTime.now();
    return CacheEntry(
      data: newData,
      createdAt: now,
      lastAccessedAt: now,
      expiresAt: newTtl != null ? now.add(newTtl) : expiresAt,
      sizeBytes: newSizeBytes,
      accessCount: 1,
      key: key,
      type: type,
      metadata: newMetadata ?? metadata,
    );
  }

  @override
  String toString() {
    return 'CacheEntry(key: $key, type: $type, size: ${sizeBytes}B, '
        'age: ${age.inMinutes}min, accessCount: $accessCount, '
        'expired: $isExpired)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CacheEntry &&
        other.key == key &&
        other.type == type &&
        other.data == data;
  }

  @override
  int get hashCode => Object.hash(key, type, data);
}

/// Types of cache entries
enum CacheType {
  /// URL cache for Google Drive file URLs
  url,

  /// Metadata cache for file information
  metadata,

  /// Content cache for small file contents
  content,

  /// Permission cache for access control
  permission,

  /// Folder structure cache
  folder,

  /// Search results cache
  search,
}

/// Extension for CacheType
extension CacheTypeExtension on CacheType {
  /// Get the string representation
  String get value {
    switch (this) {
      case CacheType.url:
        return 'url';
      case CacheType.metadata:
        return 'metadata';
      case CacheType.content:
        return 'content';
      case CacheType.permission:
        return 'permission';
      case CacheType.folder:
        return 'folder';
      case CacheType.search:
        return 'search';
    }
  }

  /// Get default TTL for this cache type
  Duration get defaultTtl {
    switch (this) {
      case CacheType.url:
        return const Duration(hours: 1); // URLs expire quickly
      case CacheType.metadata:
        return const Duration(hours: 6); // Metadata is relatively stable
      case CacheType.content:
        return const Duration(hours: 24); // Content rarely changes
      case CacheType.permission:
        return const Duration(hours: 2); // Permissions can change
      case CacheType.folder:
        return const Duration(hours: 12); // Folder structure is stable
      case CacheType.search:
        return const Duration(minutes: 30); // Search results change frequently
    }
  }

  /// Get maximum cache size for this type (in bytes)
  int get maxCacheSize {
    switch (this) {
      case CacheType.url:
        return 1024 * 1024; // 1MB for URLs
      case CacheType.metadata:
        return 5 * 1024 * 1024; // 5MB for metadata
      case CacheType.content:
        return 50 * 1024 * 1024; // 50MB for content
      case CacheType.permission:
        return 1024 * 1024; // 1MB for permissions
      case CacheType.folder:
        return 2 * 1024 * 1024; // 2MB for folder structure
      case CacheType.search:
        return 2 * 1024 * 1024; // 2MB for search results
    }
  }
}
