
/// Performance metrics for various operations
class PerformanceMetrics {
  final String operationType;
  final String operationId;
  final DateTime startTime;
  final DateTime endTime;
  final bool success;
  final String? errorMessage;
  final Map<String, dynamic> metadata;
  final int? fileSizeBytes;
  final double? networkSpeedBytesPerSecond;
  final String? userId;
  final String? sessionId;

  const PerformanceMetrics({
    required this.operationType,
    required this.operationId,
    required this.startTime,
    required this.endTime,
    required this.success,
    this.errorMessage,
    this.metadata = const {},
    this.fileSizeBytes,
    this.networkSpeedBytesPerSecond,
    this.userId,
    this.sessionId,
  });

  /// Duration of the operation in milliseconds
  int get durationMs => endTime.difference(startTime).inMilliseconds;

  /// Duration of the operation as a Duration object
  Duration get duration => endTime.difference(startTime);

  /// Whether the operation was fast (under 2 seconds)
  bool get isFast => durationMs < 2000;

  /// Whether the operation was slow (over 10 seconds)
  bool get isSlow => durationMs > 10000;

  /// Performance score (0-100, higher is better)
  double get performanceScore {
    if (!success) return 0.0;

    // Base score starts at 100
    double score = 100.0;

    // Deduct points for duration
    if (durationMs > 1000) score -= (durationMs - 1000) / 100;
    if (durationMs > 5000) score -= (durationMs - 5000) / 50;
    if (durationMs > 10000) score -= (durationMs - 10000) / 25;

    // Ensure score doesn't go below 0
    return score.clamp(0.0, 100.0);
  }

  /// Create a copy with updated fields
  PerformanceMetrics copyWith({
    String? operationType,
    String? operationId,
    DateTime? startTime,
    DateTime? endTime,
    bool? success,
    String? errorMessage,
    Map<String, dynamic>? metadata,
    int? fileSizeBytes,
    double? networkSpeedBytesPerSecond,
    String? userId,
    String? sessionId,
  }) {
    return PerformanceMetrics(
      operationType: operationType ?? this.operationType,
      operationId: operationId ?? this.operationId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      success: success ?? this.success,
      errorMessage: errorMessage ?? this.errorMessage,
      metadata: metadata ?? this.metadata,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      networkSpeedBytesPerSecond:
          networkSpeedBytesPerSecond ?? this.networkSpeedBytesPerSecond,
      userId: userId ?? this.userId,
      sessionId: sessionId ?? this.sessionId,
    );
  }

  /// Convert to JSON for database storage
  Map<String, dynamic> toJson() {
    return {
      'operation_type': operationType,
      'operation_name': operationId,
      'duration_ms': durationMs,
      'success': success,
      'error_message': errorMessage,
      'metadata': metadata,
      'user_id': userId,
      'timestamp': endTime.toIso8601String(), // Use endTime as the timestamp
      'performance_score': performanceScore,
      'file_size_bytes': fileSizeBytes,
      'throughput_mbps':
          networkSpeedBytesPerSecond != null
              ? (networkSpeedBytesPerSecond! /
                  1024 /
                  1024) // Convert bytes/sec to MB/sec
              : null,
    };
  }

  /// Create from JSON (database format)
  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) {
    // Handle both old and new field names for backward compatibility
    final operationType =
        json['operation_type'] as String? ?? json['operationType'] as String;
    final operationId =
        json['operation_name'] as String? ?? json['operationId'] as String;
    final timestamp = json['timestamp'] as String? ?? json['endTime'] as String;
    final errorMessage =
        json['error_message'] as String? ?? json['errorMessage'] as String?;
    final fileSizeBytes =
        json['file_size_bytes'] as int? ?? json['fileSizeBytes'] as int?;
    final userId = json['user_id'] as String? ?? json['userId'] as String?;
    final throughputMbps = json['throughput_mbps'] as double?;

    return PerformanceMetrics(
      operationType: operationType,
      operationId: operationId,
      startTime: DateTime.parse(
        timestamp,
      ), // Use timestamp as both start and end for simplicity
      endTime: DateTime.parse(timestamp),
      success: json['success'] as bool,
      errorMessage: errorMessage,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      fileSizeBytes: fileSizeBytes,
      networkSpeedBytesPerSecond:
          throughputMbps != null
              ? (throughputMbps *
                  1024 *
                  1024) // Convert MB/sec back to bytes/sec
              : null,
      userId: userId,
      sessionId: null, // Not stored in database
    );
  }

  @override
  String toString() {
    return 'PerformanceMetrics($operationType: ${durationMs}ms, success: $success)';
  }
}

/// Aggregated performance statistics
class PerformanceStatistics {
  final String operationType;
  final int totalOperations;
  final int successfulOperations;
  final int failedOperations;
  final double averageDurationMs;
  final double medianDurationMs;
  final double p95DurationMs;
  final double p99DurationMs;
  final double minDurationMs;
  final double maxDurationMs;
  final double successRate;
  final double averagePerformanceScore;
  final DateTime periodStart;
  final DateTime periodEnd;
  final Map<String, int> errorCounts;

  const PerformanceStatistics({
    required this.operationType,
    required this.totalOperations,
    required this.successfulOperations,
    required this.failedOperations,
    required this.averageDurationMs,
    required this.medianDurationMs,
    required this.p95DurationMs,
    required this.p99DurationMs,
    required this.minDurationMs,
    required this.maxDurationMs,
    required this.successRate,
    required this.averagePerformanceScore,
    required this.periodStart,
    required this.periodEnd,
    required this.errorCounts,
  });

  /// Whether performance is meeting targets
  bool get isPerformingWell {
    return successRate >= 0.95 && // 95% success rate
        averageDurationMs <= 5000 && // Average under 5 seconds
        p95DurationMs <= 10000; // 95th percentile under 10 seconds
  }

  /// Performance health status
  PerformanceHealthStatus get healthStatus {
    if (successRate < 0.90) return PerformanceHealthStatus.critical;
    if (successRate < 0.95 || averageDurationMs > 10000){
      return PerformanceHealthStatus.warning;
    }
    if (averageDurationMs > 5000 || p95DurationMs > 15000){
      return PerformanceHealthStatus.degraded;
    }
    return PerformanceHealthStatus.healthy;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'operationType': operationType,
      'totalOperations': totalOperations,
      'successfulOperations': successfulOperations,
      'failedOperations': failedOperations,
      'averageDurationMs': averageDurationMs,
      'medianDurationMs': medianDurationMs,
      'p95DurationMs': p95DurationMs,
      'p99DurationMs': p99DurationMs,
      'minDurationMs': minDurationMs,
      'maxDurationMs': maxDurationMs,
      'successRate': successRate,
      'averagePerformanceScore': averagePerformanceScore,
      'periodStart': periodStart.toIso8601String(),
      'periodEnd': periodEnd.toIso8601String(),
      'errorCounts': errorCounts,
      'healthStatus': healthStatus.name,
    };
  }

  /// Create from JSON
  factory PerformanceStatistics.fromJson(Map<String, dynamic> json) {
    return PerformanceStatistics(
      operationType: json['operationType'] as String,
      totalOperations: json['totalOperations'] as int,
      successfulOperations: json['successfulOperations'] as int,
      failedOperations: json['failedOperations'] as int,
      averageDurationMs: (json['averageDurationMs'] as num).toDouble(),
      medianDurationMs: (json['medianDurationMs'] as num).toDouble(),
      p95DurationMs: (json['p95DurationMs'] as num).toDouble(),
      p99DurationMs: (json['p99DurationMs'] as num).toDouble(),
      minDurationMs: (json['minDurationMs'] as num).toDouble(),
      maxDurationMs: (json['maxDurationMs'] as num).toDouble(),
      successRate: (json['successRate'] as num).toDouble(),
      averagePerformanceScore:
          (json['averagePerformanceScore'] as num).toDouble(),
      periodStart: DateTime.parse(json['periodStart'] as String),
      periodEnd: DateTime.parse(json['periodEnd'] as String),
      errorCounts: Map<String, int>.from(json['errorCounts'] as Map),
    );
  }
}

/// Performance health status enumeration
enum PerformanceHealthStatus { healthy, degraded, warning, critical }

/// Real-time performance snapshot
class PerformanceSnapshot {
  final DateTime timestamp;
  final Map<String, PerformanceStatistics> operationStats;
  final SystemResourceMetrics systemMetrics;
  final NetworkMetrics networkMetrics;
  final CacheMetrics cacheMetrics;

  const PerformanceSnapshot({
    required this.timestamp,
    required this.operationStats,
    required this.systemMetrics,
    required this.networkMetrics,
    required this.cacheMetrics,
  });

  /// Overall system health status
  PerformanceHealthStatus get overallHealth {
    final statuses = operationStats.values.map((s) => s.healthStatus).toList();

    if (statuses.any((s) => s == PerformanceHealthStatus.critical)) {
      return PerformanceHealthStatus.critical;
    }
    if (statuses.any((s) => s == PerformanceHealthStatus.warning)) {
      return PerformanceHealthStatus.warning;
    }
    if (statuses.any((s) => s == PerformanceHealthStatus.degraded)) {
      return PerformanceHealthStatus.degraded;
    }
    return PerformanceHealthStatus.healthy;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'operationStats': operationStats.map((k, v) => MapEntry(k, v.toJson())),
      'systemMetrics': systemMetrics.toJson(),
      'networkMetrics': networkMetrics.toJson(),
      'cacheMetrics': cacheMetrics.toJson(),
      'overallHealth': overallHealth.name,
    };
  }
}

/// System resource metrics
class SystemResourceMetrics {
  final double cpuUsagePercent;
  final double memoryUsagePercent;
  final double diskUsagePercent;
  final int activeConnections;
  final double batteryLevel;
  final bool isLowPowerMode;

  const SystemResourceMetrics({
    required this.cpuUsagePercent,
    required this.memoryUsagePercent,
    required this.diskUsagePercent,
    required this.activeConnections,
    required this.batteryLevel,
    required this.isLowPowerMode,
  });

  Map<String, dynamic> toJson() {
    return {
      'cpuUsagePercent': cpuUsagePercent,
      'memoryUsagePercent': memoryUsagePercent,
      'diskUsagePercent': diskUsagePercent,
      'activeConnections': activeConnections,
      'batteryLevel': batteryLevel,
      'isLowPowerMode': isLowPowerMode,
    };
  }
}

/// Network performance metrics
class NetworkMetrics {
  final String connectionType;
  final double bandwidthMbps;
  final double latencyMs;
  final double packetLossPercent;
  final bool isOnline;
  final bool isMetered;

  const NetworkMetrics({
    required this.connectionType,
    required this.bandwidthMbps,
    required this.latencyMs,
    required this.packetLossPercent,
    required this.isOnline,
    required this.isMetered,
  });

  Map<String, dynamic> toJson() {
    return {
      'connectionType': connectionType,
      'bandwidthMbps': bandwidthMbps,
      'latencyMs': latencyMs,
      'packetLossPercent': packetLossPercent,
      'isOnline': isOnline,
      'isMetered': isMetered,
    };
  }
}

/// Cache performance metrics
class CacheMetrics {
  final double hitRatePercent;
  final int totalRequests;
  final int cacheHits;
  final int cacheMisses;
  final double averageAccessTimeMs;
  final int totalSizeBytes;
  final int entryCount;

  const CacheMetrics({
    required this.hitRatePercent,
    required this.totalRequests,
    required this.cacheHits,
    required this.cacheMisses,
    required this.averageAccessTimeMs,
    required this.totalSizeBytes,
    required this.entryCount,
  });

  Map<String, dynamic> toJson() {
    return {
      'hitRatePercent': hitRatePercent,
      'totalRequests': totalRequests,
      'cacheHits': cacheHits,
      'cacheMisses': cacheMisses,
      'averageAccessTimeMs': averageAccessTimeMs,
      'totalSizeBytes': totalSizeBytes,
      'entryCount': entryCount,
    };
  }
}
