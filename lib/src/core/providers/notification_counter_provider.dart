import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Provider for the PocketBase service instance
final pocketBaseServiceProvider = Provider<PocketBaseService>((ref) {
  return PocketBaseService();
});

/// Provider for the notification service instance
final notificationServiceProvider = Provider<NotificationService>((ref) {
  final pocketBaseService = ref.watch(pocketBaseServiceProvider);
  return NotificationService(pocketBaseService);
});

/// Provider for the unread notification count as a stream
final unreadNotificationCountProvider = StreamProvider<int>((ref) async* {
  final notificationService = ref.watch(notificationServiceProvider);

  // Initialize the notification service if not already done
  await notificationService.initialize();

  // Create a stream controller to convert ValueNotifier to Stream
  final controller = StreamController<int>();

  void listener() {
    final notifications = notificationService.notifications.value;
    final unreadCount = notifications.where((n) => !n.isRead).length;
    LoggerService.info('Unread notification count updated: $unreadCount');
    controller.add(unreadCount);
  }

  // Add listener to the ValueNotifier
  notificationService.notifications.addListener(listener);

  // Emit initial value
  final initialNotifications = notificationService.notifications.value;
  final initialUnreadCount =
      initialNotifications.where((n) => !n.isRead).length;
  yield initialUnreadCount;

  // Listen to the stream
  await for (final count in controller.stream) {
    yield count;
  }
});

/// Provider for the current unread count as a simple value (for immediate access)
final currentUnreadCountProvider = Provider<int>((ref) {
  final asyncCount = ref.watch(unreadNotificationCountProvider);
  return asyncCount.when(
    data: (count) => count,
    loading: () => 0,
    error: (_, __) => 0,
  );
});

/// Notification counter state notifier for manual state management
class NotificationCounterNotifier extends StateNotifier<int> {
  NotificationCounterNotifier(this._notificationService) : super(0) {
    _initialize();
  }

  final NotificationService _notificationService;

  void _notificationListener() {
    final notifications = _notificationService.notifications.value;
    final unreadCount = notifications.where((n) => !n.isRead).length;
    LoggerService.info(
      'NotificationCounterNotifier: Updating count to $unreadCount',
    );
    state = unreadCount;
  }

  Future<void> _initialize() async {
    try {
      await _notificationService.initialize();

      // Subscribe to notification changes using addListener
      _notificationService.notifications.addListener(_notificationListener);

      // Get initial count
      final initialCount = await _notificationService.getUnreadCount();
      state = initialCount;
      LoggerService.info(
        'NotificationCounterNotifier: Initial count set to $initialCount',
      );
    } catch (e) {
      LoggerService.error('Error initializing notification counter', e);
      state = 0;
    }
  }

  /// Manually refresh the unread count
  Future<void> refresh() async {
    try {
      await _notificationService.initialize();
      final count = await _notificationService.getUnreadCount();
      state = count;
      LoggerService.info(
        'NotificationCounterNotifier: Refreshed count to $count',
      );
    } catch (e) {
      LoggerService.error('Error refreshing notification counter', e);
    }
  }

  /// Mark a notification as read and update the counter
  Future<void> markAsRead(String notificationId) async {
    try {
      await _notificationService.markAsRead(notificationId);
      // The counter will be updated automatically via the stream subscription
      LoggerService.info(
        'NotificationCounterNotifier: Marked notification $notificationId as read',
      );
    } catch (e) {
      LoggerService.error('Error marking notification as read', e);
    }
  }

  @override
  void dispose() {
    _notificationService.notifications.removeListener(_notificationListener);
    _notificationService.dispose();
    super.dispose();
  }
}

/// Provider for the notification counter state notifier
final notificationCounterProvider =
    StateNotifierProvider<NotificationCounterNotifier, int>((ref) {
      final notificationService = ref.watch(notificationServiceProvider);
      return NotificationCounterNotifier(notificationService);
    });

/// Helper provider to get the notification service for manual operations
final notificationServiceHelperProvider = Provider<NotificationService>((ref) {
  return ref.watch(notificationServiceProvider);
});

/// Provider for checking if notifications are loading
final notificationsLoadingProvider = Provider<bool>((ref) {
  final asyncCount = ref.watch(unreadNotificationCountProvider);
  return asyncCount.isLoading;
});

/// Provider for notification error state
final notificationErrorProvider = Provider<String?>((ref) {
  final asyncCount = ref.watch(unreadNotificationCountProvider);
  return asyncCount.when(
    data: (_) => null,
    loading: () => null,
    error: (error, _) => error.toString(),
  );
});
