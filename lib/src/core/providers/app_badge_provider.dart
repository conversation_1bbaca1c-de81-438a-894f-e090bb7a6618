import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/app_badge_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/providers/notification_counter_provider.dart';

/// Provider for app badge state management
final appBadgeProvider = StateNotifierProvider<AppBadgeNotifier, AppBadgeState>((ref) {
  return AppBadgeNotifier(ref);
});

/// State class for app badge
class AppBadgeState {
  final bool isSupported;
  final bool isInitialized;
  final int currentCount;
  final bool isUpdating;
  final String? error;

  const AppBadgeState({
    this.isSupported = false,
    this.isInitialized = false,
    this.currentCount = 0,
    this.isUpdating = false,
    this.error,
  });

  AppBadgeState copyWith({
    bool? isSupported,
    bool? isInitialized,
    int? currentCount,
    bool? isUpdating,
    String? error,
  }) {
    return AppBadgeState(
      isSupported: isSupported ?? this.isSupported,
      isInitialized: isInitialized ?? this.isInitialized,
      currentCount: currentCount ?? this.currentCount,
      isUpdating: isUpdating ?? this.isUpdating,
      error: error,
    );
  }

  @override
  String toString() {
    return 'AppBadgeState(isSupported: $isSupported, isInitialized: $isInitialized, '
           'currentCount: $currentCount, isUpdating: $isUpdating, error: $error)';
  }
}

/// State notifier for managing app badge functionality
class AppBadgeNotifier extends StateNotifier<AppBadgeState> {
  final Ref _ref;
  
  AppBadgeNotifier(this._ref) : super(const AppBadgeState()) {
    _initialize();
  }

  /// Initialize the badge service and start listening to notification changes
  Future<void> _initialize() async {
    try {
      LoggerService.info('Initializing AppBadgeNotifier...');
      
      // Initialize the badge service
      await AppBadgeService.initialize();
      
      state = state.copyWith(
        isSupported: AppBadgeService.isSupported,
        isInitialized: AppBadgeService.isInitialized,
        error: null,
      );

      if (AppBadgeService.isSupported) {
        // Listen to notification count changes
        _ref.listen<int>(
          currentUnreadCountProvider,
          (previous, current) {
            if (previous != current) {
              _updateBadgeCount(current);
            }
          },
        );

        // Set initial badge count
        final initialCount = _ref.read(currentUnreadCountProvider);
        await _updateBadgeCount(initialCount);
        
        LoggerService.info(
          'AppBadgeNotifier initialized successfully. Initial count: $initialCount',
        );
      } else {
        LoggerService.info(
          'App badge not supported on this platform',
        );
      }
    } catch (e) {
      LoggerService.error('Error initializing AppBadgeNotifier', e);
      state = state.copyWith(
        error: e.toString(),
        isInitialized: true,
      );
    }
  }

  /// Update the badge count
  Future<void> _updateBadgeCount(int count) async {
    if (!state.isSupported || !state.isInitialized) {
      return;
    }

    try {
      state = state.copyWith(isUpdating: true, error: null);
      
      await AppBadgeService.updateBadge(count);
      
      state = state.copyWith(
        currentCount: count,
        isUpdating: false,
      );
      
      LoggerService.debug('Badge count updated to: $count');
    } catch (e) {
      LoggerService.error('Error updating badge count', e);
      state = state.copyWith(
        isUpdating: false,
        error: e.toString(),
      );
    }
  }

  /// Manually refresh the badge count from the notification provider
  Future<void> refreshBadge() async {
    if (!state.isSupported || !state.isInitialized) {
      LoggerService.warning('Cannot refresh badge: not supported or not initialized');
      return;
    }

    try {
      final currentCount = _ref.read(currentUnreadCountProvider);
      await _updateBadgeCount(currentCount);
      LoggerService.info('Badge manually refreshed to: $currentCount');
    } catch (e) {
      LoggerService.error('Error manually refreshing badge', e);
      state = state.copyWith(error: e.toString());
    }
  }

  /// Clear the badge
  Future<void> clearBadge() async {
    if (!state.isSupported || !state.isInitialized) {
      return;
    }

    try {
      state = state.copyWith(isUpdating: true, error: null);
      
      await AppBadgeService.clearBadge();
      
      state = state.copyWith(
        currentCount: 0,
        isUpdating: false,
      );
      
      LoggerService.info('Badge cleared');
    } catch (e) {
      LoggerService.error('Error clearing badge', e);
      state = state.copyWith(
        isUpdating: false,
        error: e.toString(),
      );
    }
  }

  /// Get platform information
  Map<String, dynamic> getPlatformInfo() {
    return AppBadgeService.getPlatformInfo();
  }

  @override
  void dispose() {
    AppBadgeService.dispose();
    super.dispose();
  }
}

/// Provider for checking if app badge is supported
final appBadgeSupportedProvider = Provider<bool>((ref) {
  final badgeState = ref.watch(appBadgeProvider);
  return badgeState.isSupported;
});

/// Provider for getting current badge count
final currentBadgeCountProvider = Provider<int>((ref) {
  final badgeState = ref.watch(appBadgeProvider);
  return badgeState.currentCount;
});

/// Provider for checking if badge is currently updating
final badgeUpdatingProvider = Provider<bool>((ref) {
  final badgeState = ref.watch(appBadgeProvider);
  return badgeState.isUpdating;
});

/// Provider for badge error state
final badgeErrorProvider = Provider<String?>((ref) {
  final badgeState = ref.watch(appBadgeProvider);
  return badgeState.error;
});
