import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/theme/app_theme.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_profile_level2_form_page.dart';

import 'package:three_pay_group_litigation_platform/src/features/home/<USER>/pages/landing_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/onboarding/presentation/pages/onboarding_flow_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/role_selection_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/solicitor_registration_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/cofunder_registration_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/sign_in_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/account_deactivated_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/claim_detail_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/my_claims_list_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/solicitor_dashboard_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/pu_application_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/funding_application_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/funding_application_documents_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/edit_funding_application_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/application_chat_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_dashboard_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/educational_content_list_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/educational_content_detail_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/podcast_detail_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/content_item_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_profile_level3_form_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/knowledge_test_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/view_investment_cases_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/investment_case_detail_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/my_investments_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_investment_form_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/claimant_dashboard_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/claims_list_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/claim_detail_page.dart'
    as claimant;
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/claimant_notifications_list_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/funding_dashboard_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/chat_conversations_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/home/<USER>/pages/generic_content_detail_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/claimant_registration_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/claimant_profile_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/profile_edit_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_profile_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_profile_edit_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/solicitor_profile_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/solicitor_profile_edit_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/forgot_password_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/change_password_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/reset_password_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/presentation/pages/notifications_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/presentation/pages/notification_detail_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/sign_nda_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/solicitor_login_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/firm_documents_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/reports_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/search/presentation/pages/search_results_page.dart';

import 'package:three_pay_group_litigation_platform/src/core/services/service_locator.dart'; // Import ServiceLocator
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/mini_player_widget.dart';
import 'package:three_pay_group_litigation_platform/src/core/providers/app_badge_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/debug/presentation/pages/badge_test_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/admin_login_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/admin_dashboard_page.dart';
// Firebase service import - currently disabled due to Xcode 16/iOS 18.5 compatibility issues
// import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';

// Global navigation key for notification navigation
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class AppWidget extends ConsumerStatefulWidget {
  // Restore class definition
  const AppWidget({super.key});

  @override
  ConsumerState<AppWidget> createState() => _AppWidgetState();
}

class _AppWidgetState extends ConsumerState<AppWidget> {
  final ThemeMode _themeMode = ThemeMode.system;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Set navigator key for Firebase service (currently disabled)
        // FirebaseApiService.setNavigatorKey(navigatorKey);

        // Initialize NotificationService if user is authenticated
        if (ServiceLocator.pocketBaseService.pb.authStore.isValid) {
          ServiceLocator.notificationService.initialize().catchError((e) {
            // Log error if initialization fails, but don't block UI
            debugPrint('Error initializing NotificationService: $e');
          });
        }

        // Initialize app badge provider
        ref.read(appBadgeProvider.notifier);
      }
    });
  }

  @override
  void dispose() {
    // Dispose notification service
    ServiceLocator.notificationService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ShadApp(
      navigatorKey:
          navigatorKey, // Add global navigator key for notification navigation
      debugShowCheckedModeBanner: false,
      themeMode: _themeMode,
      theme: AppTheme.shadLightTheme,
      darkTheme: AppTheme.shadDarkTheme,
      materialThemeBuilder: (context, materialTheme) {
        return materialTheme;
      },
      builder: (context, child) {
        // Wrap the entire app with mini player functionality and edge-to-edge support
        return SafeArea(
          // Allow the app to extend into system UI areas
          top: false,
          bottom: false,
          child: MiniPlayerWrapper(child: child ?? const SizedBox.shrink()),
        );
      },
      initialRoute: '/',
      routes: {
        '/': (context) => const LandingPage(),
        OnboardingFlowPage.routeName: (context) => const OnboardingFlowPage(),
        RoleSelectionPage.routeName: (context) => const RoleSelectionPage(),
        SolicitorRegistrationPage.routeName:
            (context) => const SolicitorRegistrationPage(),
        CoFunderRegistrationPage.routeName:
            (context) => const CoFunderRegistrationPage(),
        SignInPage.routeName: (context) => const SignInPage(),
        AccountDeactivatedPage.routeName:
            (context) => const AccountDeactivatedPage(),
        SolicitorDashboardPage.routeName:
            (context) => const SolicitorDashboardPage(),
        PUApplicationPage.routeName: (context) => const PUApplicationPage(),
        FundingApplicationPage.routeName:
            (context) => const FundingApplicationPage(),
        // FundingApplicationDocumentsPage is now handled in onGenerateRoute
        // because it requires an applicationId parameter
        MyClaimsListPage.routeName: (context) => const MyClaimsListPage(),
        CoFunderDashboardPage.routeName: (context) => CoFunderDashboardPage(),
        EducationalContentListPage.routeName:
            (context) => EducationalContentListPage(),
        CoFunderProfileLevel2FormPage.routeName:
            (context) => CoFunderProfileLevel2FormPage(),
        CoFunderProfileLevel3FormPage.routeName:
            (context) => CoFunderProfileLevel3FormPage(),
        KnowledgeTestPage.routeName: (context) => const KnowledgeTestPage(),
        MyInvestmentsPage.routeName: (context) => const MyInvestmentsPage(),
        ClaimantDashboardPage.routeName:
            (context) => const ClaimantDashboardPage(),
        ClaimsListPage.routeName: (context) => const ClaimsListPage(),
        FundingDashboardPage.routeName:
            (context) => const FundingDashboardPage(),
        ClaimantNotificationsListPage.routeName:
            (context) => const ClaimantNotificationsListPage(),
        ClaimantRegistrationPage.routeName:
            (context) => const ClaimantRegistrationPage(),
        ClaimantProfilePage.routeName: (context) => const ClaimantProfilePage(),
        ProfileEditPage.routeName: (context) => const ProfileEditPage(),
        ChatConversationsPage.routeName:
            (context) => const ChatConversationsPage(),
        CoFunderProfilePage.routeName: (context) => const CoFunderProfilePage(),
        CoFunderProfileEditPage.routeName:
            (context) => const CoFunderProfileEditPage(),
        SolicitorProfilePage.routeName:
            (context) => const SolicitorProfilePage(),
        SolicitorProfileEditPage.routeName:
            (context) => const SolicitorProfileEditPage(),
        ForgotPasswordPage.routeName: (context) => const ForgotPasswordPage(),
        ChangePasswordPage.routeName: (context) => const ChangePasswordPage(),
        NotificationsPage.routeName: (context) => const NotificationsPage(),
        SignNDAPage.routeName: (context) => const SignNDAPage(),
        SolicitorLoginPage.routeName: (context) => const SolicitorLoginPage(),
        FirmDocumentsPage.routeName: (context) => const FirmDocumentsPage(),
        ReportsPage.routeName: (context) => const ReportsPage(),
        SearchResultsPage.routeName: (context) => const SearchResultsPage(),
        BadgeTestPage.routeName: (context) => const BadgeTestPage(),
        AdminLoginPage.routeName: (context) => const AdminLoginPage(),
        AdminDashboardPage.routeName: (context) => const AdminDashboardPage(),
      },
      onGenerateRoute: (settings) {
        if (settings.name == claimant.ClaimDetailPage.routeName) {
          final caseId = settings.arguments as String;
          return MaterialPageRoute(
            builder: (context) {
              return claimant.ClaimDetailPage(claimId: caseId);
            },
          );
        }

        if (settings.name == FundingApplicationDocumentsPage.routeName) {
          final applicationId = settings.arguments as String;
          return MaterialPageRoute(
            builder: (context) {
              return FundingApplicationDocumentsPage(
                applicationId: applicationId,
              );
            },
          );
        }

        if (settings.name == '/solicitor/edit-funding-application') {
          final fundingApplicationId = settings.arguments as String;
          return MaterialPageRoute(
            builder: (context) {
              return EditFundingApplicationPage(
                fundingApplicationId: fundingApplicationId,
              );
            },
          );
        }
        if (settings.name == EducationalContentDetailPage.routeName) {
          final contentItem = settings.arguments as ContentItemModel;
          return MaterialPageRoute(
            builder: (context) {
              return EducationalContentDetailPage(contentItem: contentItem);
            },
          );
        }
        if (settings.name == PodcastDetailPage.routeName) {
          final contentItem = settings.arguments as ContentItemModel;
          return MaterialPageRoute(
            builder: (context) {
              return PodcastDetailPage(contentItem: contentItem);
            },
          );
        }
        if (settings.name == InvestmentCaseDetailPage.routeName) {
          final args = settings.arguments as Map<String, dynamic>?;
          final caseId = args?['caseId'] as String?;
          if (caseId != null) {
            return MaterialPageRoute(
              builder: (context) {
                return InvestmentCaseDetailPage(caseId: caseId);
              },
            );
          }
        }
        if (settings.name == GenericContentDetailPage.routeName) {
          final contentItem = settings.arguments as ContentItemModel;
          return MaterialPageRoute(
            builder: (context) {
              return GenericContentDetailPage(contentItem: contentItem);
            },
          );
        }
        if (settings.name == ViewInvestmentCasesPage.routeName) {
          bool selectionMode = false;
          if (settings.arguments is Map<String, dynamic>) {
            selectionMode =
                (settings.arguments as Map<String, dynamic>)['selectionMode'] ??
                false;
          }
          return MaterialPageRoute(
            builder: (context) {
              return ViewInvestmentCasesPage(selectionMode: selectionMode);
            },
          );
        }
        if (settings.name == CofunderInvestmentFormPage.routeName) {
          final caseId = settings.arguments as String?;
          return MaterialPageRoute(
            builder: (context) {
              return CofunderInvestmentFormPage(caseId: caseId);
            },
          );
        }
        if (settings.name == '/solicitor-case-detail') {
          final caseId = settings.arguments as String;
          return MaterialPageRoute(
            builder: (context) {
              return ClaimDetailPage(claimId: caseId);
            },
          );
        }

        if (settings.name == ClaimDetailPage.routeName) {
          final caseId = settings.arguments as String;
          return MaterialPageRoute(
            builder: (context) {
              return ClaimDetailPage(claimId: caseId);
            },
          );
        }

        if (settings.name == ApplicationChatPage.routeName) {
          final applicationId = settings.arguments as String;
          return MaterialPageRoute(
            builder: (context) {
              return ApplicationChatPage(applicationId: applicationId);
            },
          );
        }

        if (settings.name == NotificationDetailPage.routeName) {
          final notificationId = settings.arguments as String;
          return MaterialPageRoute(
            builder: (context) {
              return NotificationDetailPage(notificationId: notificationId);
            },
          );
        }

        if (settings.name == ResetPasswordPage.routeName) {
          final token = settings.arguments as String;
          return MaterialPageRoute(
            builder: (context) {
              return ResetPasswordPage(token: token);
            },
          );
        }

        assert(false, 'Need to implement ${settings.name}');
        return null;
      },
    );
  }
}
