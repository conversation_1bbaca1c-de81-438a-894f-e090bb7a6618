import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import '../models/cache_entry.dart';

/// Utility functions for cache operations
class CacheUtils {
  CacheUtils._();

  /// Generate a cache key for a file ID and cache type
  static String generateKey(String fileId, CacheType type, {String? suffix}) {
    final parts = [type.value, fileId];
    if (suffix != null) {
      parts.add(suffix);
    }
    return parts.join(':');
  }

  /// Generate a cache key for folder listing
  static String generateFolderKey(String? folderId, String? query) {
    final parts = ['folder', folderId ?? 'root'];
    if (query != null && query.isNotEmpty) {
      parts.add(_sanitizeForKey(query));
    }
    return parts.join(':');
  }

  /// Generate a cache key for search results
  static String generateSearchKey(String query, int? maxResults) {
    final parts = ['search', _sanitizeForKey(query)];
    if (maxResults != null) {
      parts.add(maxResults.toString());
    }
    return parts.join(':');
  }

  /// Generate a hash-based cache key for complex data
  static String generateHashKey(String prefix, Map<String, dynamic> data) {
    final jsonString = jsonEncode(data);
    final bytes = utf8.encode(jsonString);
    final digest = sha256.convert(bytes);
    return '$prefix:${digest.toString().substring(0, 16)}';
  }

  /// Sanitize a string for use in cache keys
  static String _sanitizeForKey(String input) {
    return input
        .replaceAll(RegExp(r'[^a-zA-Z0-9_-]'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .toLowerCase();
  }

  /// Validate cache key format
  static bool isValidKey(String key) {
    if (key.isEmpty || key.length > 255) return false;
    return RegExp(r'^[a-zA-Z0-9:_-]+$').hasMatch(key);
  }

  /// Calculate size of data in bytes
  static int calculateSize(dynamic data) {
    if (data == null) return 0;

    if (data is String) {
      return utf8.encode(data).length;
    } else if (data is Uint8List) {
      return data.length;
    } else if (data is List<int>) {
      return data.length;
    } else if (data is Map || data is List) {
      try {
        final jsonString = jsonEncode(data);
        return utf8.encode(jsonString).length;
      } catch (e) {
        // Fallback to rough estimation
        return data.toString().length * 2;
      }
    } else {
      // Fallback to string representation
      return data.toString().length * 2;
    }
  }

  /// Check if data should be cached based on size
  static bool shouldCache(dynamic data, CacheType type) {
    final size = calculateSize(data);
    final maxSize = type.maxCacheSize;

    // Don't cache if data is too large
    if (size > maxSize) return false;

    // Don't cache empty data
    if (size == 0) return false;

    return true;
  }

  /// Compress data for storage (simple JSON compression)
  static String compressData(dynamic data) {
    if (data is String) return data;
    return jsonEncode(data);
  }

  /// Decompress data from storage
  static dynamic decompressData(String compressedData, Type expectedType) {
    try {
      if (expectedType == String) {
        return compressedData;
      }
      return jsonDecode(compressedData);
    } catch (e) {
      // If decompression fails, return as string
      return compressedData;
    }
  }

  /// Create a namespace for cache keys
  static String createNamespace(String environment, String userId) {
    return '${environment}_$userId';
  }

  /// Extract file ID from cache key
  static String? extractFileId(String key) {
    final parts = key.split(':');
    if (parts.length >= 2) {
      return parts[1];
    }
    return null;
  }

  /// Extract cache type from cache key
  static CacheType? extractCacheType(String key) {
    final parts = key.split(':');
    if (parts.isEmpty) return null;

    final typeString = parts[0];
    for (final type in CacheType.values) {
      if (type.value == typeString) {
        return type;
      }
    }
    return null;
  }

  /// Check if cache entry should be evicted based on LRU policy
  static bool shouldEvict(
    CacheEntry entry,
    Duration maxAge,
    int maxAccessCount,
  ) {
    // Evict if expired
    if (entry.isExpired) return true;

    // Evict if too old
    if (entry.age > maxAge) return true;

    // Evict if not accessed frequently enough
    if (entry.accessCount < maxAccessCount &&
        entry.timeSinceLastAccess > maxAge) {
      return true;
    }

    return false;
  }

  /// Calculate cache efficiency score (0.0 to 1.0)
  static double calculateEfficiency(
    int hits,
    int misses,
    double avgAccessTime,
  ) {
    final totalRequests = hits + misses;
    if (totalRequests == 0) return 0.0;

    final hitRatio = hits / totalRequests;
    final timeScore = avgAccessTime < 50 ? 1.0 : (100 - avgAccessTime) / 100;

    return (hitRatio * 0.7) + (timeScore.clamp(0.0, 1.0) * 0.3);
  }

  /// Format cache size for display
  static String formatSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// Format duration for display
  static String formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  /// Get cache priority based on access patterns
  static int getCachePriority(CacheEntry entry) {
    // Higher priority = more important to keep
    int priority = 0;

    // Recent access increases priority
    final hoursSinceAccess = entry.timeSinceLastAccess.inHours;
    if (hoursSinceAccess < 1) {
      priority += 100;
    } else if (hoursSinceAccess < 6) {
      priority += 50;
    } else if (hoursSinceAccess < 24) {
      priority += 25;
    }

    // Frequent access increases priority
    if (entry.accessCount > 10) {
      priority += 50;
    } else if (entry.accessCount > 5) {
      priority += 25;
    } else if (entry.accessCount > 1) {
      priority += 10;
    }

    // Cache type priority
    switch (entry.type) {
      case CacheType.url:
        priority += 30; // URLs are frequently needed
        break;
      case CacheType.metadata:
        priority += 20; // Metadata is important
        break;
      case CacheType.permission:
        priority += 25; // Permissions are critical
        break;
      case CacheType.content:
        priority += 10; // Content is large but less critical
        break;
      case CacheType.folder:
        priority += 15; // Folder structure is moderately important
        break;
      case CacheType.search:
        priority += 5; // Search results are least critical
        break;
    }

    return priority;
  }

  /// Check if cache is under memory pressure
  static bool isMemoryPressure(int currentSize, int maxSize) {
    return currentSize > (maxSize * 0.8); // 80% threshold
  }

  /// Calculate optimal cache size based on available memory
  static int calculateOptimalCacheSize(int availableMemory) {
    // Use up to 10% of available memory for cache
    return (availableMemory * 0.1).round();
  }
}
