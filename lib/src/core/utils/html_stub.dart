// lib/src/core/utils/html_stub.dart

// This is a stub implementation for dart:html elements for non-web platforms.
// It allows the code to compile.
// Ensure that actual usage of these is guarded by kIsWeb checks to prevent runtime UnimplementedError.

class Blob {
  Blob(List<dynamic> blobParts, [String? type, String? endings]) {
    // On non-web, this constructor might be called if not guarded by kIsWeb.
    // Depending on the strategy, it could throw here or allow instantiation.
    // For now, let it instantiate, errors will occur if methods on it are called without a kIsWeb check.
  }
}

class Url {
  static String createObjectUrlFromBlob(Blob blob) {
    throw UnimplementedError('Url.createObjectUrlFromBlob is only available on web.');
  }

  static void revokeObjectUrl(String url) {
    throw UnimplementedError('Url.revokeObjectUrl is only available on web.');
  }
}

class AnchorElement {
  String? href;
  String? download; // To match the usage of setAttribute("download", ...)

  AnchorElement({this.href});

  void setAttribute(String name, String value) {
    // The code uses .setAttribute("download", ...), so handle that specifically for the stub.
    if (name.toLowerCase() == 'download') {
      this.download = value;
      return;
    }
    // For other attributes, it's safer to throw if not expected.
    throw UnimplementedError('AnchorElement.setAttribute for "$name" is only available on web or not stubbed.');
  }

  void click() {
    throw UnimplementedError('AnchorElement.click is only available on web.');
  }
}