import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Security utilities for the application
class SecurityUtils {
  static final Random _random = Random.secure();

  /// Generate a secure random ID
  static String generateSecureId({int length = 32}) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return List.generate(
      length,
      (index) => chars[_random.nextInt(chars.length)],
    ).join();
  }

  /// Generate a cryptographic hash of data
  static String generateHash(String data, {String algorithm = 'sha256'}) {
    try {
      Digest digest;
      switch (algorithm.toLowerCase()) {
        case 'sha256':
          digest = sha256.convert(utf8.encode(data));
          break;
        case 'sha512':
          digest = sha512.convert(utf8.encode(data));
          break;
        case 'md5':
          digest = md5.convert(utf8.encode(data));
          break;
        default:
          digest = sha256.convert(utf8.encode(data));
      }
      return digest.toString();
    } catch (e) {
      LoggerService.error('Failed to generate hash', e);
      rethrow;
    }
  }

  /// Generate a digital signature for data integrity
  static String generateDigitalSignature(
    Map<String, dynamic> data,
    String secretKey,
  ) {
    try {
      // Sort keys for consistent signature generation
      final sortedKeys = data.keys.toList()..sort();
      final sortedData = <String, dynamic>{};
      for (final key in sortedKeys) {
        sortedData[key] = data[key];
      }

      final dataString = jsonEncode(sortedData);
      final combinedData = '$dataString$secretKey';
      return generateHash(combinedData, algorithm: 'sha256');
    } catch (e) {
      LoggerService.error('Failed to generate digital signature', e);
      rethrow;
    }
  }

  /// Verify a digital signature
  static bool verifyDigitalSignature(
    Map<String, dynamic> data,
    String signature,
    String secretKey,
  ) {
    try {
      final expectedSignature = generateDigitalSignature(data, secretKey);
      return expectedSignature == signature;
    } catch (e) {
      LoggerService.error('Failed to verify digital signature', e);
      return false;
    }
  }

  /// Validate IP address format
  static bool isValidIpAddress(String? ipAddress) {
    if (ipAddress == null || ipAddress.isEmpty) return false;

    // IPv4 validation
    final ipv4Regex = RegExp(r'^(\d{1,3}\.){3}\d{1,3}$');
    if (ipv4Regex.hasMatch(ipAddress)) {
      final parts = ipAddress.split('.');
      return parts.every((part) {
        final num = int.tryParse(part);
        return num != null && num >= 0 && num <= 255;
      });
    }

    // IPv6 validation (basic)
    final ipv6Regex = RegExp(r'^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$');
    return ipv6Regex.hasMatch(ipAddress);
  }

  /// Sanitize user input to prevent injection attacks
  static String sanitizeInput(String input) {
    return input
        .replaceAll(RegExp(r'[<>"]'), '')
        .replaceAll("'", '')
        .replaceAll(RegExp(r'[;&|`]'), '')
        .trim();
  }

  /// Validate file extension against allowed types
  static bool isAllowedFileType(
    String filename,
    List<String> allowedExtensions,
  ) {
    if (filename.isEmpty) return false;

    final extension = filename.split('.').last.toLowerCase();
    return allowedExtensions.map((e) => e.toLowerCase()).contains(extension);
  }

  /// Check if file size is within limits
  static bool isValidFileSize(int fileSizeBytes, int maxSizeBytes) {
    return fileSizeBytes > 0 && fileSizeBytes <= maxSizeBytes;
  }

  /// Generate a secure session token
  static String generateSessionToken() {
    return generateSecureId(length: 64);
  }

  /// Validate session token format
  static bool isValidSessionToken(String? token) {
    if (token == null || token.isEmpty) return false;
    return RegExp(r'^[a-zA-Z0-9]{64}$').hasMatch(token);
  }

  /// Calculate risk score based on various factors
  static int calculateRiskScore({
    required bool success,
    required String action,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic>? context,
  }) {
    int score = 0;

    // Base score for failed operations
    if (!success) score += 30;

    // Action-based scoring
    switch (action.toLowerCase()) {
      case 'delete':
        score += 40;
        break;
      case 'modify':
      case 'update':
        score += 20;
        break;
      case 'share':
      case 'permission_change':
        score += 30;
        break;
      case 'download':
        score += 10;
        break;
      case 'view':
      case 'read':
        score += 5;
        break;
    }

    // IP address validation
    if (!isValidIpAddress(ipAddress)) {
      score += 20;
    }

    // Suspicious user agent patterns
    if (userAgent != null && _isSuspiciousUserAgent(userAgent)) {
      score += 25;
    }

    // Context-based scoring
    if (context != null) {
      // Multiple failed attempts
      final failedAttempts = context['failed_attempts'] as int? ?? 0;
      if (failedAttempts > 3) score += 30;

      // Off-hours access
      final isOffHours = context['off_hours'] as bool? ?? false;
      if (isOffHours) score += 15;

      // Unusual location
      final unusualLocation = context['unusual_location'] as bool? ?? false;
      if (unusualLocation) score += 20;
    }

    return score.clamp(0, 100);
  }

  /// Check if user agent string is suspicious
  static bool _isSuspiciousUserAgent(String userAgent) {
    final suspiciousPatterns = [
      'bot',
      'crawler',
      'spider',
      'scraper',
      'curl',
      'wget',
      'python',
      'java',
    ];

    final lowerUserAgent = userAgent.toLowerCase();
    return suspiciousPatterns.any(
      (pattern) => lowerUserAgent.contains(pattern),
    );
  }

  /// Mask sensitive data for logging
  static String maskSensitiveData(String data, {int visibleChars = 4}) {
    if (data.length <= visibleChars * 2) {
      return '*' * data.length;
    }

    final start = data.substring(0, visibleChars);
    final end = data.substring(data.length - visibleChars);
    final masked = '*' * (data.length - visibleChars * 2);

    return '$start$masked$end';
  }

  /// Validate email format
  static bool isValidEmail(String email) {
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }

  /// Generate a secure password
  static String generateSecurePassword({int length = 16}) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*';
    return List.generate(
      length,
      (index) => chars[_random.nextInt(chars.length)],
    ).join();
  }

  /// Validate password strength
  static PasswordStrength validatePasswordStrength(String password) {
    if (password.length < 8) return PasswordStrength.weak;

    bool hasLower = password.contains(RegExp(r'[a-z]'));
    bool hasUpper = password.contains(RegExp(r'[A-Z]'));
    bool hasDigit = password.contains(RegExp(r'[0-9]'));
    bool hasSpecial = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    int score = 0;
    if (hasLower) score++;
    if (hasUpper) score++;
    if (hasDigit) score++;
    if (hasSpecial) score++;
    if (password.length >= 12) score++;

    switch (score) {
      case 0:
      case 1:
      case 2:
        return PasswordStrength.weak;
      case 3:
        return PasswordStrength.medium;
      case 4:
        return PasswordStrength.strong;
      case 5:
        return PasswordStrength.veryStrong;
      default:
        return PasswordStrength.weak;
    }
  }

  /// Check if timestamp is within acceptable time window
  static bool isTimestampValid(
    DateTime timestamp, {
    Duration maxAge = const Duration(minutes: 5),
  }) {
    final now = DateTime.now();
    final difference = now.difference(timestamp).abs();
    return difference <= maxAge;
  }

  /// Generate a time-based one-time password (TOTP) secret
  static String generateTotpSecret() {
    return base32Encode(List.generate(20, (_) => _random.nextInt(256)));
  }

  /// Base32 encoding for TOTP secrets
  static String base32Encode(List<int> bytes) {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    String result = '';
    int buffer = 0;
    int bitsLeft = 0;

    for (int byte in bytes) {
      buffer = (buffer << 8) | byte;
      bitsLeft += 8;
      while (bitsLeft >= 5) {
        result += alphabet[(buffer >> (bitsLeft - 5)) & 31];
        bitsLeft -= 5;
      }
    }

    if (bitsLeft > 0) {
      result += alphabet[(buffer << (5 - bitsLeft)) & 31];
    }

    return result;
  }
}

/// Password strength levels
enum PasswordStrength { weak, medium, strong, veryStrong }

/// Extension for PasswordStrength
extension PasswordStrengthExtension on PasswordStrength {
  String get value {
    switch (this) {
      case PasswordStrength.weak:
        return 'weak';
      case PasswordStrength.medium:
        return 'medium';
      case PasswordStrength.strong:
        return 'strong';
      case PasswordStrength.veryStrong:
        return 'very_strong';
    }
  }

  String get description {
    switch (this) {
      case PasswordStrength.weak:
        return 'Password is too weak';
      case PasswordStrength.medium:
        return 'Password strength is medium';
      case PasswordStrength.strong:
        return 'Password is strong';
      case PasswordStrength.veryStrong:
        return 'Password is very strong';
    }
  }
}
