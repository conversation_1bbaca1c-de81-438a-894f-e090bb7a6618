import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:mime/mime.dart';
import 'dart:math' as math;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Utility class for Google Drive operations
class GoogleDriveUtils {
  /// MIME type constants
  static const String folderMimeType = 'application/vnd.google-apps.folder';
  static const String documentMimeType = 'application/vnd.google-apps.document';
  static const String spreadsheetMimeType =
      'application/vnd.google-apps.spreadsheet';
  static const String presentationMimeType =
      'application/vnd.google-apps.presentation';

  /// File size constants
  static const int maxFileSize = 5 * 1024 * 1024 * 1024; // 5GB
  static const int chunkUploadThreshold = 5 * 1024 * 1024; // 5MB
  static const int defaultChunkSize = 1024 * 1024; // 1MB

  /// Detect MIME type from file
  static String? detectMimeType(String filePath) {
    try {
      return lookupMimeType(filePath);
    } catch (e) {
      LoggerService.warning('Failed to detect MIME type for $filePath: $e');
      return null;
    }
  }

  /// Detect MIME type from file content
  static String? detectMimeTypeFromBytes(Uint8List bytes, {String? fileName}) {
    try {
      // Try to detect from file name first
      if (fileName != null) {
        final mimeType = lookupMimeType(fileName);
        if (mimeType != null) return mimeType;
      }

      // Try to detect from content
      return lookupMimeType('', headerBytes: bytes);
    } catch (e) {
      LoggerService.warning('Failed to detect MIME type from bytes: $e');
      return 'application/octet-stream'; // Default binary type
    }
  }

  /// Calculate file checksum (MD5)
  static Future<String> calculateMD5(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final digest = md5.convert(bytes);
      return digest.toString();
    } catch (e) {
      LoggerService.error('Failed to calculate MD5 for ${file.path}', e);
      rethrow;
    }
  }

  /// Calculate checksum from bytes
  static String calculateMD5FromBytes(Uint8List bytes) {
    try {
      final digest = md5.convert(bytes);
      return digest.toString();
    } catch (e) {
      LoggerService.error('Failed to calculate MD5 from bytes', e);
      rethrow;
    }
  }

  /// Calculate SHA-256 checksum
  static Future<String> calculateSHA256(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final digest = sha256.convert(bytes);
      return digest.toString();
    } catch (e) {
      LoggerService.error('Failed to calculate SHA-256 for ${file.path}', e);
      rethrow;
    }
  }

  /// Validate file name for Google Drive
  static bool isValidFileName(String fileName) {
    if (fileName.isEmpty || fileName.length > 255) return false;

    // Check for invalid characters
    final invalidChars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|'];
    for (final char in invalidChars) {
      if (fileName.contains(char)) return false;
    }

    // Check for reserved names
    final reservedNames = [
      'CON',
      'PRN',
      'AUX',
      'NUL',
      'COM1',
      'COM2',
      'COM3',
      'COM4',
      'COM5',
      'COM6',
      'COM7',
      'COM8',
      'COM9',
      'LPT1',
      'LPT2',
      'LPT3',
      'LPT4',
      'LPT5',
      'LPT6',
      'LPT7',
      'LPT8',
      'LPT9',
    ];
    if (reservedNames.contains(fileName.toUpperCase())) return false;

    return true;
  }

  /// Sanitize file name for Google Drive
  static String sanitizeFileName(String fileName) {
    if (isValidFileName(fileName)) return fileName;

    // Replace invalid characters
    String sanitized = fileName;
    final invalidChars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|'];
    for (final char in invalidChars) {
      sanitized = sanitized.replaceAll(char, '_');
    }

    // Trim length if necessary
    if (sanitized.length > 255) {
      final extension = getFileExtension(sanitized);
      final nameWithoutExt = sanitized.substring(0, sanitized.lastIndexOf('.'));
      final maxNameLength = 255 - extension.length - 1; // -1 for the dot
      sanitized = '${nameWithoutExt.substring(0, maxNameLength)}.$extension';
    }

    return sanitized;
  }

  /// Get file extension
  static String getFileExtension(String fileName) {
    final lastDot = fileName.lastIndexOf('.');
    return lastDot != -1 ? fileName.substring(lastDot + 1).toLowerCase() : '';
  }

  /// Format file size to human-readable string
  static String formatFileSize(int bytes) {
    if (bytes <= 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    double size = bytes.toDouble();
    int unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(size < 10 ? 1 : 0)} ${units[unitIndex]}';
  }

  /// Check if file size is valid for upload
  static bool isValidFileSize(int bytes) {
    return bytes > 0 && bytes <= maxFileSize;
  }

  /// Check if file should use chunked upload
  static bool shouldUseChunkedUpload(int bytes) {
    return bytes > chunkUploadThreshold;
  }

  /// Generate folder path from components
  static String generateFolderPath(List<String> components) {
    return components.where((c) => c.isNotEmpty).join('/');
  }

  /// Parse folder path into components
  static List<String> parseFolderPath(String path) {
    return path.split('/').where((c) => c.isNotEmpty).toList();
  }

  /// Generate unique file name with timestamp
  static String generateUniqueFileName(String originalName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = getFileExtension(originalName);
    final nameWithoutExt = originalName.substring(
      0,
      originalName.lastIndexOf('.'),
    );

    return extension.isNotEmpty
        ? '${nameWithoutExt}_$timestamp.$extension'
        : '${originalName}_$timestamp';
  }

  /// Validate Google Drive file ID format
  static bool isValidFileId(String fileId) {
    // Google Drive file IDs are typically 28-44 characters long
    // and contain alphanumeric characters, hyphens, and underscores
    final regex = RegExp(r'^[a-zA-Z0-9_-]{10,50}$');
    return regex.hasMatch(fileId);
  }

  /// Extract file ID from Google Drive URL
  static String? extractFileIdFromUrl(String url) {
    try {
      // Handle different Google Drive URL formats
      final patterns = [
        RegExp(r'/file/d/([a-zA-Z0-9_-]+)'),
        RegExp(r'id=([a-zA-Z0-9_-]+)'),
        RegExp(r'/folders/([a-zA-Z0-9_-]+)'),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(url);
        if (match != null) {
          return match.group(1);
        }
      }

      return null;
    } catch (e) {
      LoggerService.warning('Failed to extract file ID from URL: $url - $e');
      return null;
    }
  }

  /// Generate Google Drive file URL
  static String generateFileUrl(String fileId, {bool isFolder = false}) {
    if (isFolder) {
      return 'https://drive.google.com/drive/folders/$fileId';
    } else {
      return 'https://drive.google.com/file/d/$fileId/view';
    }
  }

  /// Generate Google Drive download URL
  static String generateDownloadUrl(String fileId) {
    return 'https://drive.google.com/uc?id=$fileId&export=download';
  }

  /// Convert bytes to base64
  static String bytesToBase64(Uint8List bytes) {
    return base64Encode(bytes);
  }

  /// Convert base64 to bytes
  static Uint8List base64ToBytes(String base64String) {
    return base64Decode(base64String);
  }

  /// Check if file is an image
  static bool isImageFile(String fileName) {
    final extension = getFileExtension(fileName);
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
    return imageExtensions.contains(extension);
  }

  /// Check if file is a document (only PDF files are supported for claim documents)
  static bool isDocumentFile(String fileName) {
    final extension = getFileExtension(fileName);
    const documentExtensions = ['pdf'];
    return documentExtensions.contains(extension);
  }

  /// Check if file is a spreadsheet
  static bool isSpreadsheetFile(String fileName) {
    final extension = getFileExtension(fileName);
    const spreadsheetExtensions = ['xls', 'xlsx', 'csv', 'ods'];
    return spreadsheetExtensions.contains(extension);
  }

  /// Check if file is a presentation
  static bool isPresentationFile(String fileName) {
    final extension = getFileExtension(fileName);
    const presentationExtensions = ['ppt', 'pptx', 'odp'];
    return presentationExtensions.contains(extension);
  }

  /// Get file category based on extension
  static String getFileCategory(String fileName) {
    if (isImageFile(fileName)) return 'image';
    if (isDocumentFile(fileName)) return 'document';
    if (isSpreadsheetFile(fileName)) return 'spreadsheet';
    if (isPresentationFile(fileName)) return 'presentation';
    return 'other';
  }

  /// Validate folder name
  static bool isValidFolderName(String folderName) {
    return isValidFileName(folderName) &&
        folderName != '.' &&
        folderName != '..';
  }

  /// Generate folder name for claim documents
  static String generateClaimFolderName(String claimId, String claimTitle) {
    final sanitizedTitle = sanitizeFileName(claimTitle);
    return '${claimId}_${sanitizedTitle}';
  }

  /// Generate document category folder name
  static String generateCategoryFolderName(String logicalName) {
    return sanitizeFileName(logicalName.replaceAll(' ', '_'));
  }

  /// Create retry delay with exponential backoff
  static Duration calculateRetryDelay(
    int attemptNumber, {
    Duration baseDelay = const Duration(seconds: 1),
  }) {
    final multiplier = math.pow(2, attemptNumber - 1).toInt();
    final delay = Duration(milliseconds: baseDelay.inMilliseconds * multiplier);

    // Cap at 30 seconds
    return delay.inSeconds > 30 ? const Duration(seconds: 30) : delay;
  }

  /// Check if error is retryable
  static bool isRetryableError(dynamic error) {
    if (error is SocketException) return true;
    if (error is HttpException) return true;

    final errorString = error.toString().toLowerCase();

    // Check for common retryable error patterns
    const retryablePatterns = [
      'timeout',
      'connection',
      'network',
      'temporary',
      'rate limit',
      'quota',
      'service unavailable',
      '429', // Too Many Requests
      '500', // Internal Server Error
      '502', // Bad Gateway
      '503', // Service Unavailable
      '504', // Gateway Timeout
    ];

    return retryablePatterns.any((pattern) => errorString.contains(pattern));
  }
}
