import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/services/claim_documents_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/utils/storage_utils.dart';

/// Utility class for debugging document-related issues
class DocumentDebugUtils {
  static final ClaimDocumentsService _claimDocumentsService =
      ClaimDocumentsService();

  /// Debug the specific file that's causing 404 errors
  /// Based on the error log: z1x11mm20u08b98_v1 in collection pbc_1421764558
  static Future<void> debugProblematicFile() async {
    try {
      LoggerService.info('=== STARTING DOCUMENT DEBUG SESSION ===');

      // The problematic file ID from the error logs
      const problematicFileId = 'z1x11mm20u08b98_v1';
      const recordId = 'z1x11mm20u08b98';

      LoggerService.info('Debugging file ID: $problematicFileId');
      LoggerService.info('Record ID: $recordId');

      // Step 1: Inspect the record
      LoggerService.info('Step 1: Inspecting record...');
      await _claimDocumentsService.inspectRecord(recordId);

      // Step 2: Try to debug and fix the URL
      LoggerService.info('Step 2: Attempting to debug and fix URL...');
      final fixedUrl = await _claimDocumentsService.debugAndFixFileUrl(
        problematicFileId,
      );

      if (fixedUrl != null) {
        LoggerService.info('Successfully fixed URL: $fixedUrl');
      } else {
        LoggerService.error('Failed to fix URL for file: $problematicFileId');
      }

      LoggerService.info('=== DOCUMENT DEBUG SESSION COMPLETE ===');
    } catch (e) {
      LoggerService.error('Error during document debug session', e);
    }
  }

  /// Debug all documents for a specific funding application
  static Future<void> debugFundingApplicationDocuments(
    String fundingApplicationId,
  ) async {
    try {
      LoggerService.info('=== DEBUGGING FUNDING APPLICATION DOCUMENTS ===');
      LoggerService.info('Funding Application ID: $fundingApplicationId');

      // Get all documents for the funding application
      final documents = await _claimDocumentsService
          .getDocumentsForFundingApplication(fundingApplicationId);
      LoggerService.info('Found ${documents.length} document categories');

      for (final category in documents) {
        LoggerService.info('--- Category: ${category.logicalName} ---');
        LoggerService.info('Current version: ${category.currentVersionFileId}');
        LoggerService.info('Total versions: ${category.versions.length}');

        for (final version in category.versions) {
          LoggerService.info('  Version: ${version.fileId}');
          LoggerService.info('  Filename: ${version.filename}');
          LoggerService.info('  Uploaded by: ${version.uploadedBy}');
          LoggerService.info('  Uploaded at: ${version.uploadedAt}');

          // Try to get URL for each version
          try {
            final url = await _claimDocumentsService.getFileUrl(version.fileId);
            LoggerService.info('  URL: $url');
          } catch (e) {
            LoggerService.error('  Failed to get URL: $e');

            // Try to fix the URL
            try {
              final fixedUrl = await _claimDocumentsService.debugAndFixFileUrl(
                version.fileId,
              );
              if (fixedUrl != null) {
                LoggerService.info('  Fixed URL: $fixedUrl');
              } else {
                LoggerService.error(
                  '  Could not fix URL for: ${version.fileId}',
                );
              }
            } catch (fixError) {
              LoggerService.error('  Error fixing URL: $fixError');
            }
          }
        }
      }

      LoggerService.info(
        '=== FUNDING APPLICATION DOCUMENTS DEBUG COMPLETE ===',
      );
    } catch (e) {
      LoggerService.error('Error debugging funding application documents', e);
    }
  }

  /// Check and fix all document records in the claim_documents collection
  static Future<void> checkAndFixAllDocuments() async {
    try {
      LoggerService.info('=== CHECKING AND FIXING ALL DOCUMENTS ===');

      // Test metadata generation first
      await testMetadataGeneration();

      // This would require admin access to get all records
      // For now, we'll focus on specific problematic records

      await debugProblematicFile();

      LoggerService.info('=== ALL DOCUMENTS CHECK COMPLETE ===');
    } catch (e) {
      LoggerService.error('Error checking and fixing all documents', e);
    }
  }

  /// Test metadata generation to ensure it respects Google Drive limits
  static Future<void> testMetadataGeneration() async {
    try {
      LoggerService.info('=== TESTING METADATA GENERATION ===');

      // Run the metadata test
      StorageUtils.testMetadataGeneration();

      LoggerService.info('=== METADATA GENERATION TEST COMPLETE ===');
    } catch (e) {
      LoggerService.error('Error testing metadata generation', e);
    }
  }

  /// Test Google Drive URL conversion and file download
  static Future<void> testGoogleDriveUrlConversion() async {
    try {
      LoggerService.info('=== TESTING GOOGLE DRIVE URL CONVERSION ===');

      // Test URL conversion
      const testViewUrl =
          'https://drive.google.com/file/d/1Zhdbyfso-0TIFzpOPlyg8b6MhKQY4eG4/view';
      LoggerService.info('Original view URL: $testViewUrl');

      // Convert to download URL
      final fileIdMatch = RegExp(
        r'/file/d/([a-zA-Z0-9_-]+)',
      ).firstMatch(testViewUrl);
      if (fileIdMatch != null) {
        final fileId = fileIdMatch.group(1);
        final downloadUrl =
            'https://drive.google.com/uc?export=download&id=$fileId';
        LoggerService.info('Converted download URL: $downloadUrl');

        // Test if the URL is accessible
        LoggerService.info('Testing URL accessibility...');
        // This would require HTTP client to test
        LoggerService.info('URL conversion test complete');
      } else {
        LoggerService.warning('Could not extract file ID from URL');
      }

      LoggerService.info('=== GOOGLE DRIVE URL CONVERSION TEST COMPLETE ===');
    } catch (e) {
      LoggerService.error('Error testing Google Drive URL conversion', e);
    }
  }

  /// Test URL accessibility for a specific file
  static Future<bool> testUrlAccessibility(String url) async {
    try {
      LoggerService.info('Testing URL accessibility: $url');

      // This would use the same verification method as the service
      // For now, we'll just log the attempt
      LoggerService.info('URL test would be performed here');

      return true;
    } catch (e) {
      LoggerService.error('URL accessibility test failed', e);
      return false;
    }
  }
}
