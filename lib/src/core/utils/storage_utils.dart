import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import '../models/storage_type.dart';
import '../models/document_operation_result.dart';
import '../services/logger_service.dart';

/// Utility functions for storage operations
class StorageUtils {
  StorageUtils._();

  /// Generate a unique file ID for Google Drive storage
  static String generateGoogleDriveFileId(
    String fundingApplicationId,
    String logicalName,
    int versionNumber,
  ) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'gd_${fundingApplicationId}_${_sanitizeForId(logicalName)}_v${versionNumber}_$timestamp';
  }

  /// Generate a folder path for Google Drive based on funding application
  static String generateGoogleDriveFolderPath(String fundingApplicationId) {
    return '3PayGlobal/Claims/$fundingApplicationId/Documents';
  }

  /// Generate a file name for Google Drive storage
  static String generateGoogleDriveFileName(
    String logicalName,
    String originalFilename,
    int versionNumber,
  ) {
    final extension = path.extension(originalFilename);
    final baseName = path.basenameWithoutExtension(originalFilename);
    final sanitizedLogicalName = _sanitizeForFilename(logicalName);
    return '${sanitizedLogicalName}_v${versionNumber}_${_sanitizeForFilename(baseName)}$extension';
  }

  /// Sanitize a string for use in file IDs
  static String _sanitizeForId(String input) {
    return input
        .replaceAll(RegExp(r'[^a-zA-Z0-9_-]'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .toLowerCase();
  }

  /// Sanitize a string for use in filenames
  static String _sanitizeForFilename(String input) {
    return input
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .trim();
  }

  /// Validate file for upload
  static DocumentOperationResult<bool> validateFile(
    File file,
    StorageType storageType,
  ) {
    try {
      // Check if file exists
      if (!file.existsSync()) {
        return DocumentOperationResult.failure(
          error: 'File does not exist',
          storageType: storageType,
          operationType: DocumentOperationType.upload,
        );
      }

      // Check file size
      final fileSize = file.lengthSync();
      final maxSize = storageType.maxFileSize;
      if (fileSize > maxSize) {
        return DocumentOperationResult.failure(
          error:
              'File size (${formatFileSize(fileSize)}) exceeds maximum allowed size (${formatFileSize(maxSize)})',
          storageType: storageType,
          operationType: DocumentOperationType.upload,
        );
      }

      // Check file extension
      final extension = path.extension(file.path).toLowerCase();
      if (!_isAllowedFileType(extension)) {
        return DocumentOperationResult.failure(
          error: 'File type $extension is not allowed',
          storageType: storageType,
          operationType: DocumentOperationType.upload,
        );
      }

      return DocumentOperationResult.success(
        data: true,
        storageType: storageType,
        operationType: DocumentOperationType.upload,
      );
    } catch (e) {
      return DocumentOperationResult.fromException(
        exception: e is Exception ? e : Exception(e.toString()),
        storageType: storageType,
        operationType: DocumentOperationType.upload,
      );
    }
  }

  /// Validate MultipartFile for upload
  static DocumentOperationResult<bool> validateMultipartFile(
    http.MultipartFile file,
    StorageType storageType,
  ) {
    try {
      // Check file size
      final fileSize = file.length;
      final maxSize = storageType.maxFileSize;
      if (fileSize > maxSize) {
        return DocumentOperationResult.failure(
          error:
              'File size (${formatFileSize(fileSize)}) exceeds maximum allowed size (${formatFileSize(maxSize)})',
          storageType: storageType,
          operationType: DocumentOperationType.upload,
        );
      }

      // Check file extension
      final filename = file.filename ?? '';
      final extension = path.extension(filename).toLowerCase();
      if (!_isAllowedFileType(extension)) {
        return DocumentOperationResult.failure(
          error: 'File type $extension is not allowed',
          storageType: storageType,
          operationType: DocumentOperationType.upload,
        );
      }

      return DocumentOperationResult.success(
        data: true,
        storageType: storageType,
        operationType: DocumentOperationType.upload,
      );
    } catch (e) {
      return DocumentOperationResult.fromException(
        exception: e is Exception ? e : Exception(e.toString()),
        storageType: storageType,
        operationType: DocumentOperationType.upload,
      );
    }
  }

  /// Check if file type is allowed for claim documents
  /// Only PDF and image files are supported for optimal document preview experience
  static bool _isAllowedFileType(String extension) {
    const allowedExtensions = {
      // Documents
      '.pdf',
      // Images
      '.jpg', '.jpeg', '.png', '.gif',
    };

    return allowedExtensions.contains(extension.toLowerCase());
  }

  /// Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// Convert File to MultipartFile
  static Future<http.MultipartFile> fileToMultipartFile(
    File file,
    String fieldName,
  ) async {
    return http.MultipartFile.fromBytes(
      fieldName,
      await file.readAsBytes(),
      filename: path.basename(file.path),
    );
  }

  /// Convert MultipartFile to bytes
  static Future<Uint8List> multipartFileToBytes(http.MultipartFile file) async {
    return Uint8List.fromList(await file.finalize().toBytes());
  }

  /// Extract file extension from filename
  static String getFileExtension(String filename) {
    return path.extension(filename).toLowerCase();
  }

  /// Extract base filename without extension
  static String getBaseFilename(String filename) {
    return path.basenameWithoutExtension(filename);
  }

  /// Generate metadata for document storage
  /// Respects Google Drive's 124-byte limit per property (key + value)
  static Map<String, String> generateDocumentMetadata({
    required String fundingApplicationId,
    required String logicalName,
    required String uploadedBy,
    required int versionNumber,
    String? notes,
    String? originalFilename,
  }) {
    final metadata = <String, String>{};

    // Add essential metadata with size limits
    _addMetadataProperty(
      metadata,
      'app_id',
      fundingApplicationId,
      maxLength: 50,
    );
    _addMetadataProperty(metadata, 'category', logicalName, maxLength: 40);
    _addMetadataProperty(metadata, 'uploader', uploadedBy, maxLength: 30);
    _addMetadataProperty(
      metadata,
      'version',
      versionNumber.toString(),
      maxLength: 10,
    );
    _addMetadataProperty(metadata, 'source', '3PayGlobal', maxLength: 20);

    // Add optional metadata if space allows
    if (notes != null && notes.isNotEmpty) {
      _addMetadataProperty(metadata, 'notes', notes, maxLength: 60);
    }

    if (originalFilename != null && originalFilename.isNotEmpty) {
      // Truncate filename to fit within limits
      final truncatedFilename = _truncateFilename(originalFilename, 50);
      _addMetadataProperty(
        metadata,
        'orig_name',
        truncatedFilename,
        maxLength: 60,
      );
    }

    // Log the generated metadata for debugging
    LoggerService.debug('Generated metadata for Google Drive:');
    for (final entry in metadata.entries) {
      final keyBytes = utf8.encode(entry.key).length;
      final valueBytes = utf8.encode(entry.value).length;
      final totalBytes = keyBytes + valueBytes;
      LoggerService.debug('  ${entry.key}: ${entry.value} ($totalBytes bytes)');
    }

    return metadata;
  }

  /// Add a metadata property while respecting Google Drive's 124-byte limit
  static void _addMetadataProperty(
    Map<String, String> metadata,
    String key,
    String value, {
    int maxLength = 100,
  }) {
    // Calculate the total bytes for key + value
    final truncatedValue =
        value.length > maxLength ? value.substring(0, maxLength) : value;
    final totalBytes =
        utf8.encode(key).length + utf8.encode(truncatedValue).length;

    // Google Drive limit is 124 bytes for key + value
    if (totalBytes <= 120) {
      // Leave some buffer
      metadata[key] = truncatedValue;
    } else {
      // Further truncate the value to fit
      final availableForValue = 120 - utf8.encode(key).length;
      if (availableForValue > 0) {
        String finalValue = truncatedValue;
        while (utf8.encode(finalValue).length > availableForValue &&
            finalValue.isNotEmpty) {
          finalValue = finalValue.substring(0, finalValue.length - 1);
        }
        if (finalValue.isNotEmpty) {
          metadata[key] = finalValue;
        }
      }
    }
  }

  /// Truncate filename while preserving extension
  static String _truncateFilename(String filename, int maxLength) {
    if (filename.length <= maxLength) return filename;

    final extension = getFileExtension(filename);
    final nameWithoutExt = getBaseFilename(filename);

    if (extension.isNotEmpty) {
      final maxNameLength = maxLength - extension.length - 1; // -1 for the dot
      if (maxNameLength > 0) {
        final truncatedName =
            nameWithoutExt.length > maxNameLength
                ? nameWithoutExt.substring(0, maxNameLength)
                : nameWithoutExt;
        return '$truncatedName$extension';
      }
    }

    return filename.substring(0, maxLength);
  }

  /// Parse version file ID to extract components
  static Map<String, String>? parseVersionFileId(String versionFileId) {
    try {
      // Handle Google Drive file IDs: gd_fundingAppId_logicalName_v1_timestamp
      if (versionFileId.startsWith('gd_')) {
        final parts = versionFileId.split('_');
        if (parts.length >= 5) {
          return {
            'storage_type': 'google_drive',
            'funding_application_id': parts[1],
            'logical_name': parts[2],
            'version': parts[3].substring(1), // Remove 'v' prefix
            'timestamp': parts[4],
          };
        }
      }

      // Handle PocketBase file IDs: recordId_v1, recordId_v2, etc.
      if (versionFileId.contains('_v')) {
        final parts = versionFileId.split('_v');
        if (parts.length == 2) {
          return {
            'storage_type': 'pocketbase',
            'record_id': parts[0],
            'version': parts[1],
          };
        }
      }

      // Fallback for legacy file IDs
      return {
        'storage_type': 'pocketbase',
        'record_id': versionFileId,
        'version': '1',
      };
    } catch (e) {
      LoggerService.error('Failed to parse version file ID: $versionFileId', e);
      return null;
    }
  }

  /// Determine storage type from version file ID
  static StorageType getStorageTypeFromFileId(String versionFileId) {
    final parsed = parseVersionFileId(versionFileId);
    if (parsed == null) return StorageType.pocketbase;

    final storageType = parsed['storage_type'];
    switch (storageType) {
      case 'google_drive':
        return StorageType.googleDrive;
      case 'pocketbase':
        return StorageType.pocketbase;
      default:
        return StorageType.pocketbase;
    }
  }

  /// Create a retry strategy for failed operations
  static Future<T> retryOperation<T>(
    Future<T> Function() operation,
    StorageConfiguration config,
    DocumentOperationType operationType,
  ) async {
    int attempts = 0;
    Exception? lastException;

    while (attempts < config.maxRetryAttempts) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        lastException = e is Exception ? e : Exception(e.toString());

        LoggerService.warning(
          'Operation $operationType failed (attempt $attempts/${config.maxRetryAttempts}): $e',
        );

        if (attempts >= config.maxRetryAttempts) {
          break;
        }

        // Wait before retrying (exponential backoff)
        final delay = Duration(milliseconds: 1000 * attempts);
        await Future.delayed(delay);
      }
    }

    throw lastException ??
        Exception('Operation failed after $attempts attempts');
  }

  /// Check if a URL is a Google Drive URL
  static bool isGoogleDriveUrl(String url) {
    return url.contains('drive.google.com') || url.contains('googleapis.com');
  }

  /// Check if a URL is a PocketBase URL
  static bool isPocketBaseUrl(String url) {
    return url.contains('/api/files/');
  }

  /// Extract Google Drive file ID from URL
  static String? extractGoogleDriveFileId(String url) {
    final regex = RegExp(r'/file/d/([a-zA-Z0-9-_]+)');
    final match = regex.firstMatch(url);
    return match?.group(1);
  }

  /// Generate a cache key for document operations
  static String generateCacheKey(String operation, String identifier) {
    return '${operation}_$identifier';
  }

  /// Check if operation should use cache
  static bool shouldUseCache(
    DocumentOperationType operationType,
    StorageConfiguration config,
  ) {
    return config.enableCaching && operationType.shouldCache;
  }

  /// Get MIME type from file extension
  static String getMimeType(String filename) {
    final extension = getFileExtension(filename);

    const mimeTypes = {
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx':
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx':
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.ppt': 'application/vnd.ms-powerpoint',
      '.pptx':
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      '.txt': 'text/plain',
      '.csv': 'text/csv',
      '.json': 'application/json',
      '.xml': 'application/xml',
      '.html': 'text/html',
      '.htm': 'text/html',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.tiff': 'image/tiff',
      '.webp': 'image/webp',
      '.zip': 'application/zip',
      '.rar': 'application/x-rar-compressed',
      '.7z': 'application/x-7z-compressed',
    };

    return mimeTypes[extension] ?? 'application/octet-stream';
  }

  /// Validate that metadata respects Google Drive's property limits
  static bool validateMetadataForGoogleDrive(Map<String, String> metadata) {
    for (final entry in metadata.entries) {
      final keyBytes = utf8.encode(entry.key).length;
      final valueBytes = utf8.encode(entry.value).length;
      final totalBytes = keyBytes + valueBytes;

      if (totalBytes > 124) {
        LoggerService.warning(
          'Metadata property exceeds Google Drive limit: ${entry.key} = ${entry.value} ($totalBytes bytes)',
        );
        return false;
      }
    }
    return true;
  }

  /// Test metadata generation with a problematic filename
  static void testMetadataGeneration() {
    LoggerService.info('=== Testing Metadata Generation ===');

    // Test with the problematic filename from the error
    const problematicFilename =
        'Expert Wit_v1_Hacking APIs Breaking Web Application Programming Interfaces (Final Release) (Corey J. Ball) (z-lib.org).pdf';

    final metadata = generateDocumentMetadata(
      fundingApplicationId: 'test_app_123',
      logicalName: 'Expert Witness Report',
      uploadedBy: 'user_12345',
      versionNumber: 1,
      notes: 'This is a test note for the document',
      originalFilename: problematicFilename,
    );

    LoggerService.info('Generated metadata:');
    for (final entry in metadata.entries) {
      final keyBytes = utf8.encode(entry.key).length;
      final valueBytes = utf8.encode(entry.value).length;
      final totalBytes = keyBytes + valueBytes;
      LoggerService.info('  ${entry.key}: ${entry.value} ($totalBytes bytes)');
    }

    final isValid = validateMetadataForGoogleDrive(metadata);
    LoggerService.info(
      'Metadata validation result: ${isValid ? 'PASS' : 'FAIL'}',
    );

    LoggerService.info('=== End Metadata Test ===');
  }
}
