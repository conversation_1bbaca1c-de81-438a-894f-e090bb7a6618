import 'dart:math';
import '../models/performance_metrics.dart';
import '../services/logger_service.dart';

/// Utility functions for performance monitoring and optimization
class PerformanceUtils {
  /// Calculate percentiles from a list of durations
  static Map<String, double> calculatePercentiles(List<int> durations) {
    if (durations.isEmpty) {
      return {
        'p50': 0.0,
        'p95': 0.0,
        'p99': 0.0,
        'min': 0.0,
        'max': 0.0,
        'avg': 0.0,
      };
    }

    final sorted = List<int>.from(durations)..sort();
    final length = sorted.length;

    return {
      'p50': _getPercentile(sorted, 0.5).toDouble(),
      'p95': _getPercentile(sorted, 0.95).toDouble(),
      'p99': _getPercentile(sorted, 0.99).toDouble(),
      'min': sorted.first.toDouble(),
      'max': sorted.last.toDouble(),
      'avg': sorted.reduce((a, b) => a + b) / length,
    };
  }

  /// Get percentile value from sorted list
  static int _getPercentile(List<int> sorted, double percentile) {
    final index = (sorted.length * percentile).floor();
    return sorted[index.clamp(0, sorted.length - 1)];
  }

  /// Calculate network speed from file size and duration
  static double calculateNetworkSpeed(int fileSizeBytes, int durationMs) {
    if (durationMs <= 0) return 0.0;
    return (fileSizeBytes * 1000) / durationMs; // bytes per second
  }

  /// Format file size in human-readable format
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Format duration in human-readable format
  static String formatDuration(int milliseconds) {
    if (milliseconds < 1000) return '${milliseconds}ms';
    if (milliseconds < 60000) {
      return '${(milliseconds / 1000).toStringAsFixed(1)}s';
    }
    return '${(milliseconds / 60000).toStringAsFixed(1)}m';
  }

  /// Format network speed in human-readable format
  static String formatNetworkSpeed(double bytesPerSecond) {
    if (bytesPerSecond < 1024) {
      return '${bytesPerSecond.toStringAsFixed(1)} B/s';
    }
    if (bytesPerSecond < 1024 * 1024) {
      return '${(bytesPerSecond / 1024).toStringAsFixed(1)} KB/s';
    }
    return '${(bytesPerSecond / (1024 * 1024)).toStringAsFixed(1)} MB/s';
  }

  /// Check if operation meets performance targets
  static bool meetsPerformanceTargets(PerformanceMetrics metrics) {
    switch (metrics.operationType) {
      case 'upload':
        // Target: <30 seconds for files up to 10MB
        if (metrics.fileSizeBytes != null &&
            metrics.fileSizeBytes! <= 10 * 1024 * 1024) {
          return metrics.durationMs <= 30000;
        }
        return metrics.durationMs <= 60000; // 1 minute for larger files

      case 'download':
        // Target: <10 seconds for files up to 10MB
        if (metrics.fileSizeBytes != null &&
            metrics.fileSizeBytes! <= 10 * 1024 * 1024) {
          return metrics.durationMs <= 10000;
        }
        return metrics.durationMs <= 30000; // 30 seconds for larger files

      case 'api_call':
        // Target: <2 seconds for 95% of requests
        return metrics.durationMs <= 2000;

      case 'cache_access':
        // Target: <100ms for cache operations
        return metrics.durationMs <= 100;

      default:
        // Default target: <5 seconds
        return metrics.durationMs <= 5000;
    }
  }

  /// Generate performance score based on operation type and metrics
  static double calculatePerformanceScore(PerformanceMetrics metrics) {
    if (!metrics.success) return 0.0;

    double baseScore = 100.0;
    final duration = metrics.durationMs;

    switch (metrics.operationType) {
      case 'upload':
        // Deduct points based on upload speed expectations
        if (duration > 10000) baseScore -= (duration - 10000) / 500;
        if (duration > 30000) baseScore -= (duration - 30000) / 200;
        break;

      case 'download':
        // Deduct points based on download speed expectations
        if (duration > 5000) baseScore -= (duration - 5000) / 200;
        if (duration > 10000) baseScore -= (duration - 10000) / 100;
        break;

      case 'api_call':
        // Deduct points based on API response time expectations
        if (duration > 1000) baseScore -= (duration - 1000) / 50;
        if (duration > 2000) baseScore -= (duration - 2000) / 25;
        break;

      case 'cache_access':
        // Deduct points based on cache access time expectations
        if (duration > 50) baseScore -= (duration - 50) / 5;
        if (duration > 100) baseScore -= (duration - 100) / 2;
        break;

      default:
        // General performance scoring
        if (duration > 2000) baseScore -= (duration - 2000) / 100;
        if (duration > 5000) baseScore -= (duration - 5000) / 50;
        break;
    }

    return baseScore.clamp(0.0, 100.0);
  }

  /// Detect if device is on a slow network
  static bool isSlowNetwork(NetworkMetrics? networkMetrics) {
    if (networkMetrics == null) return false;

    return networkMetrics.bandwidthMbps < 1.0 || // Less than 1 Mbps
        networkMetrics.latencyMs > 500 || // High latency
        networkMetrics.packetLossPercent > 5.0; // High packet loss
  }

  /// Detect if device is in low power mode
  static bool isLowPowerMode(SystemResourceMetrics? systemMetrics) {
    if (systemMetrics == null) return false;

    return systemMetrics.isLowPowerMode ||
        systemMetrics.batteryLevel < 0.2 || // Less than 20% battery
        systemMetrics.cpuUsagePercent > 80.0; // High CPU usage
  }

  /// Calculate optimal chunk size for file uploads based on network conditions
  static int calculateOptimalChunkSize(
    NetworkMetrics? networkMetrics,
    int fileSizeBytes,
  ) {
    const int minChunkSize = 256 * 1024; // 256KB
    const int maxChunkSize = 8 * 1024 * 1024; // 8MB
    const int defaultChunkSize = 1024 * 1024; // 1MB

    if (networkMetrics == null) return defaultChunkSize;

    // Adjust chunk size based on bandwidth
    int chunkSize = defaultChunkSize;

    if (networkMetrics.bandwidthMbps < 1.0) {
      // Slow network: use smaller chunks
      chunkSize = minChunkSize;
    } else if (networkMetrics.bandwidthMbps > 10.0) {
      // Fast network: use larger chunks
      chunkSize = maxChunkSize;
    } else {
      // Medium network: scale chunk size with bandwidth
      chunkSize = (networkMetrics.bandwidthMbps * 1024 * 1024 / 8).round();
    }

    // Ensure chunk size is reasonable for file size
    if (fileSizeBytes < chunkSize * 2) {
      chunkSize = max(minChunkSize, fileSizeBytes ~/ 4);
    }

    return chunkSize.clamp(minChunkSize, maxChunkSize);
  }

  /// Calculate optimal concurrency level based on system resources
  static int calculateOptimalConcurrency(SystemResourceMetrics? systemMetrics) {
    const int minConcurrency = 1;
    const int maxConcurrency = 8;
    const int defaultConcurrency = 3;

    if (systemMetrics == null) return defaultConcurrency;

    // Reduce concurrency if system is under stress
    if (systemMetrics.cpuUsagePercent > 80.0 ||
        systemMetrics.memoryUsagePercent > 80.0 ||
        systemMetrics.isLowPowerMode) {
      return minConcurrency;
    }

    // Increase concurrency if system has resources available
    if (systemMetrics.cpuUsagePercent < 50.0 &&
        systemMetrics.memoryUsagePercent < 50.0) {
      return maxConcurrency;
    }

    return defaultConcurrency;
  }

  /// Generate unique operation ID
  static String generateOperationId(String operationType) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(10000);
    return '${operationType}_${timestamp}_$random';
  }

  /// Check if operation should be retried based on error and attempt count
  static bool shouldRetry(String? errorMessage, int attemptCount) {
    const int maxRetries = 3;

    if (attemptCount >= maxRetries) return false;
    if (errorMessage == null) return false;

    // Retry on network errors
    if (errorMessage.toLowerCase().contains('network') ||
        errorMessage.toLowerCase().contains('timeout') ||
        errorMessage.toLowerCase().contains('connection')) {
      return true;
    }

    // Retry on temporary server errors
    if (errorMessage.contains('503') || // Service Unavailable
        errorMessage.contains('502') || // Bad Gateway
        errorMessage.contains('504')) {
      // Gateway Timeout
      return true;
    }

    // Don't retry on client errors
    if (errorMessage.contains('400') || // Bad Request
        errorMessage.contains('401') || // Unauthorized
        errorMessage.contains('403') || // Forbidden
        errorMessage.contains('404')) {
      // Not Found
      return false;
    }

    return false;
  }

  /// Calculate exponential backoff delay
  static Duration calculateBackoffDelay(int attemptCount) {
    const Duration baseDelay = Duration(seconds: 1);
    const Duration maxDelay = Duration(seconds: 30);

    final delayMs =
        (baseDelay.inMilliseconds * pow(2, attemptCount - 1)).round();
    final jitter = Random().nextInt(
      1000,
    ); // Add jitter to prevent thundering herd

    return Duration(
      milliseconds: (delayMs + jitter).clamp(0, maxDelay.inMilliseconds),
    );
  }

  /// Log performance metrics for debugging
  static void logPerformanceMetrics(PerformanceMetrics metrics) {
    final message =
        'Performance: ${metrics.operationType} '
        '(${formatDuration(metrics.durationMs)}, '
        'success: ${metrics.success}, '
        'score: ${metrics.performanceScore.toStringAsFixed(1)})';

    if (metrics.success && metrics.isFast) {
      LoggerService.debug(message);
    } else if (metrics.success && !metrics.isSlow) {
      LoggerService.info(message);
    } else if (metrics.success && metrics.isSlow) {
      LoggerService.warning('Slow operation: $message');
    } else {
      LoggerService.error('Failed operation: $message', metrics.errorMessage);
    }
  }

  /// Get system resource metrics (platform-specific implementation needed)
  static Future<SystemResourceMetrics> getSystemResourceMetrics() async {
    // This is a simplified implementation
    // In a real app, you would use platform-specific APIs

    return const SystemResourceMetrics(
      cpuUsagePercent: 45.0,
      memoryUsagePercent: 60.0,
      diskUsagePercent: 70.0,
      activeConnections: 5,
      batteryLevel: 0.8,
      isLowPowerMode: false,
    );
  }

  /// Get network metrics (platform-specific implementation needed)
  static Future<NetworkMetrics> getNetworkMetrics() async {
    // This is a simplified implementation
    // In a real app, you would use connectivity plugins

    return const NetworkMetrics(
      connectionType: 'wifi',
      bandwidthMbps: 50.0,
      latencyMs: 20.0,
      packetLossPercent: 0.1,
      isOnline: true,
      isMetered: false,
    );
  }

  /// Validate performance metrics
  static bool isValidMetrics(PerformanceMetrics metrics) {
    return metrics.startTime.isBefore(metrics.endTime) &&
        metrics.durationMs >= 0 &&
        (metrics.fileSizeBytes == null || metrics.fileSizeBytes! >= 0) &&
        (metrics.networkSpeedBytesPerSecond == null ||
            metrics.networkSpeedBytesPerSecond! >= 0);
  }
}
