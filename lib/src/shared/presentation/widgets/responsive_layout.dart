import 'package:flutter/material.dart';

/// Responsive layout widget that displays different layouts based on screen size
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final double mobileBreakpoint;
  final double tabletBreakpoint;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.mobileBreakpoint = 600,
    this.tabletBreakpoint = 1200,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;

        if (width >= tabletBreakpoint && desktop != null) {
          return desktop!;
        } else if (width >= mobileBreakpoint && tablet != null) {
          return tablet!;
        } else {
          return mobile;
        }
      },
    );
  }
}

/// Responsive breakpoint utilities
class ResponsiveBreakpoints {
  static const double mobile = 600;
  static const double tablet = 1200;
  static const double desktop = 1600;

  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < tablet;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tablet;
  }

  static bool isLargeDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }
}

/// Responsive value utility
class ResponsiveValue<T> {
  final T mobile;
  final T? tablet;
  final T? desktop;

  const ResponsiveValue({
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  T getValue(BuildContext context) {
    if (ResponsiveBreakpoints.isDesktop(context) && desktop != null) {
      return desktop!;
    } else if (ResponsiveBreakpoints.isTablet(context) && tablet != null) {
      return tablet!;
    } else {
      return mobile;
    }
  }
}

/// Responsive padding utility
class ResponsivePadding extends EdgeInsets {
  const ResponsivePadding.all(double value) : super.all(value);

  factory ResponsivePadding.responsive(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    final value = ResponsiveValue(
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    ).getValue(context);
    
    return ResponsivePadding.all(value);
  }

  factory ResponsivePadding.symmetric(
    BuildContext context, {
    double? horizontal,
    double? vertical,
    double? mobileHorizontal,
    double? mobileVertical,
    double? tabletHorizontal,
    double? tabletVertical,
    double? desktopHorizontal,
    double? desktopVertical,
  }) {
    final h = ResponsiveValue(
      mobile: mobileHorizontal ?? horizontal ?? 0,
      tablet: tabletHorizontal ?? horizontal,
      desktop: desktopHorizontal ?? horizontal,
    ).getValue(context);

    final v = ResponsiveValue(
      mobile: mobileVertical ?? vertical ?? 0,
      tablet: tabletVertical ?? vertical,
      desktop: desktopVertical ?? vertical,
    ).getValue(context);

    return EdgeInsets.symmetric(horizontal: h, vertical: v) as ResponsivePadding;
  }
}

/// Responsive grid utility
class ResponsiveGrid {
  static int getColumnCount(BuildContext context, {
    int mobile = 1,
    int tablet = 2,
    int desktop = 3,
    int largeDesktop = 4,
  }) {
    final width = MediaQuery.of(context).size.width;
    
    if (width >= ResponsiveBreakpoints.desktop) {
      return width >= 1600 ? largeDesktop : desktop;
    } else if (width >= ResponsiveBreakpoints.mobile) {
      return tablet;
    } else {
      return mobile;
    }
  }

  static double getChildAspectRatio(BuildContext context, {
    double mobile = 1.0,
    double tablet = 1.2,
    double desktop = 1.3,
  }) {
    return ResponsiveValue(
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    ).getValue(context);
  }
}

/// Responsive text size utility
class ResponsiveTextSize {
  static double getSize(BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return ResponsiveValue(
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    ).getValue(context);
  }
}

/// Responsive spacing utility
class ResponsiveSpacing {
  static double getSpacing(BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return ResponsiveValue(
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    ).getValue(context);
  }

  static SizedBox verticalSpace(BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return SizedBox(
      height: getSpacing(
        context,
        mobile: mobile,
        tablet: tablet,
        desktop: desktop,
      ),
    );
  }

  static SizedBox horizontalSpace(BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return SizedBox(
      width: getSpacing(
        context,
        mobile: mobile,
        tablet: tablet,
        desktop: desktop,
      ),
    );
  }
}
