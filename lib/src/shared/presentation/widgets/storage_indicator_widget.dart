import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as li;
import 'package:three_pay_group_litigation_platform/src/core/models/storage_type.dart';

/// A widget that displays storage type indicators and status for documents
class StorageIndicatorWidget extends StatelessWidget {
  final StorageType storageType;
  final bool isUploading;
  final bool isDownloading;
  final double? uploadProgress;
  final double? downloadProgress;
  final String? errorMessage;
  final bool showLabel;
  final double iconSize;

  const StorageIndicatorWidget({
    super.key,
    required this.storageType,
    this.isUploading = false,
    this.isDownloading = false,
    this.uploadProgress,
    this.downloadProgress,
    this.errorMessage,
    this.showLabel = true,
    this.iconSize = 16,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildStorageIcon(theme),
        if (showLabel) ...[const SizedBox(width: 4), _buildStorageLabel(theme)],
        if (isUploading || isDownloading) ...[
          const SizedBox(width: 4),
          _buildProgressIndicator(theme),
        ],
        if (errorMessage != null) ...[
          const SizedBox(width: 4),
          _buildErrorIndicator(theme),
        ],
      ],
    );
  }

  Widget _buildStorageIcon(ShadThemeData theme) {
    IconData iconData;
    Color iconColor;

    switch (storageType) {
      case StorageType.googleDrive:
        iconData = li.LucideIcons.cloud;
        iconColor = _getGoogleDriveColor();
        break;
      case StorageType.pocketbase:
        iconData = li.LucideIcons.database;
        iconColor = theme.colorScheme.primary;
        break;
      case StorageType.hybrid:
        iconData = li.LucideIcons.layers;
        iconColor = theme.colorScheme.secondary;
        break;
    }

    return Icon(iconData, size: iconSize, color: iconColor);
  }

  Widget _buildStorageLabel(ShadThemeData theme) {
    String label;
    switch (storageType) {
      case StorageType.googleDrive:
        label = 'Google Drive';
        break;
      case StorageType.pocketbase:
        label = 'PocketBase';
        break;
      case StorageType.hybrid:
        label = 'Hybrid';
        break;
    }

    return Text(
      label,
      style: theme.textTheme.small.copyWith(
        color: theme.colorScheme.mutedForeground,
        fontSize: 12,
      ),
    );
  }

  Widget _buildProgressIndicator(ShadThemeData theme) {
    final progress = uploadProgress ?? downloadProgress ?? 0.0;

    return SizedBox(
      width: 12,
      height: 12,
      child: CircularProgressIndicator(
        value: progress > 0 ? progress : null,
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(_getProgressColor(theme)),
        backgroundColor: theme.colorScheme.muted,
      ),
    );
  }

  Widget _buildErrorIndicator(ShadThemeData theme) {
    return ShadTooltip(
      builder: (context) => Text(errorMessage!),
      child: Icon(
        li.LucideIcons.alertCircle,
        size: 12,
        color: theme.colorScheme.destructive,
      ),
    );
  }

  Color _getGoogleDriveColor() {
    if (errorMessage != null) {
      return Colors.red;
    }
    if (isUploading || isDownloading) {
      return Colors.orange;
    }
    return Colors.blue;
  }

  Color _getProgressColor(ShadThemeData theme) {
    if (isUploading) {
      return Colors.blue;
    }
    if (isDownloading) {
      return Colors.green;
    }
    return theme.colorScheme.primary;
  }
}

/// A compact version of the storage indicator for use in lists
class CompactStorageIndicatorWidget extends StatelessWidget {
  final StorageType storageType;
  final bool hasError;
  final bool isProcessing;

  const CompactStorageIndicatorWidget({
    super.key,
    required this.storageType,
    this.hasError = false,
    this.isProcessing = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getBackgroundColor(theme),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: _getBorderColor(theme), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getIcon(), size: 10, color: _getIconColor(theme)),
          if (isProcessing) ...[
            const SizedBox(width: 4),
            SizedBox(
              width: 8,
              height: 8,
              child: CircularProgressIndicator(
                strokeWidth: 1,
                valueColor: AlwaysStoppedAnimation<Color>(_getIconColor(theme)),
              ),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getIcon() {
    switch (storageType) {
      case StorageType.googleDrive:
        return li.LucideIcons.cloud;
      case StorageType.pocketbase:
        return li.LucideIcons.database;
      case StorageType.hybrid:
        return li.LucideIcons.layers;
    }
  }

  Color _getBackgroundColor(ShadThemeData theme) {
    if (hasError) {
      return theme.colorScheme.destructive.withValues(alpha: 0.1);
    }
    return theme.colorScheme.muted.withValues(alpha: 0.5);
  }

  Color _getBorderColor(ShadThemeData theme) {
    if (hasError) {
      return theme.colorScheme.destructive.withValues(alpha: 0.3);
    }
    return theme.colorScheme.border;
  }

  Color _getIconColor(ShadThemeData theme) {
    if (hasError) {
      return theme.colorScheme.destructive;
    }
    switch (storageType) {
      case StorageType.googleDrive:
        return Colors.blue;
      case StorageType.pocketbase:
        return theme.colorScheme.primary;
      case StorageType.hybrid:
        return theme.colorScheme.secondary;
    }
  }
}
