import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/chat_message_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/application_message_model.dart';

/// Adapter to convert between different message model types
class MessageAdapter {
  /// Convert ApplicationMessageModel to ChatMessageModel for shared components
  static ChatMessageModel fromApplicationMessage(
    ApplicationMessageModel appMessage,
  ) {
    // Determine message type based on attachment
    MessageType messageType = MessageType.text;
    if (appMessage.attachmentUrl != null &&
        appMessage.attachmentUrl!.isNotEmpty) {
      messageType = MessageType.file;
    }

    return ChatMessageModel(
      id: appMessage.id,
      conversationId: appMessage.applicationId,
      senderId: appMessage.senderId,
      recipientId: appMessage.recipientId,
      recipientGroup: appMessage.recipientGroup,
      messageContent: appMessage.messageContent,
      messageType: messageType,
      status: MessageStatus.sent, // Default status for existing messages
      attachmentUrl: appMessage.attachmentUrl,
      timestamp: appMessage.created,
      senderName: appMessage.senderName,
      senderUserType: appMessage.senderUserType,
      // Note: senderAvatar is not available in ApplicationMessageModel, so it will be null
    );
  }

  /// Convert ChatMessageModel to ApplicationMessageModel if needed
  static ApplicationMessageModel toApplicationMessage(
    ChatMessageModel chatMessage,
  ) {
    return ApplicationMessageModel(
      id: chatMessage.id,
      applicationId: chatMessage.conversationId,
      senderId: chatMessage.senderId,
      recipientId: chatMessage.recipientId,
      recipientGroup: chatMessage.recipientGroup,
      messageContent: chatMessage.messageContent,
      attachmentUrl: chatMessage.attachmentUrl,
      created: chatMessage.timestamp,
      senderName: chatMessage.senderName,
      senderUserType: chatMessage.senderUserType,
    );
  }

  /// Convert list of ApplicationMessageModel to ChatMessageModel
  static List<ChatMessageModel> fromApplicationMessages(
    List<ApplicationMessageModel> appMessages,
  ) {
    return appMessages.map(fromApplicationMessage).toList();
  }

  /// Convert list of ChatMessageModel to ApplicationMessageModel
  static List<ApplicationMessageModel> toApplicationMessages(
    List<ChatMessageModel> chatMessages,
  ) {
    return chatMessages.map(toApplicationMessage).toList();
  }
}
