import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/notification_permission_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';

/// Widget for managing notification settings and preferences
class NotificationSettingsWidget extends StatefulWidget {
  final Map<String, dynamic> currentPreferences;
  final Function(Map<String, dynamic>) onPreferencesChanged;
  final String userType; // 'claimant', 'solicitor', 'co_funder'

  const NotificationSettingsWidget({
    super.key,
    required this.currentPreferences,
    required this.onPreferencesChanged,
    required this.userType,
  });

  @override
  State<NotificationSettingsWidget> createState() =>
      _NotificationSettingsWidgetState();
}

class _NotificationSettingsWidgetState
    extends State<NotificationSettingsWidget> {
  late Map<String, dynamic> _preferences;
  NotificationPermissionStatus _permissionStatus =
      NotificationPermissionStatus.unknown;
  bool _isLoading = false;
  String? _fcmToken;

  @override
  void initState() {
    super.initState();
    _preferences = Map<String, dynamic>.from(widget.currentPreferences);
    _checkPermissionStatus();
    _getFCMToken();
  }

  Future<void> _checkPermissionStatus() async {
    try {
      final status =
          await NotificationPermissionService.checkPermissionStatus();
      if (mounted) {
        setState(() {
          _permissionStatus = status;
        });
      }
    } catch (e) {
      LoggerService.error('Error checking notification permission status', e);
    }
  }

  Future<void> _getFCMToken() async {
    try {
      final token = FirebaseApiService.getCurrentToken();
      if (mounted) {
        setState(() {
          _fcmToken = token;
        });
      }
    } catch (e) {
      LoggerService.error('Error getting FCM token', e);
    }
  }

  Future<void> _requestPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result =
          await NotificationPermissionService.requestPermissionsWithFlow();

      if (mounted) {
        setState(() {
          _permissionStatus = result.status;
          _isLoading = false;
        });

        // Show result to user
        ShadToaster.of(context).show(
          ShadToast(
            title: Text(result.isGranted ? 'Success' : 'Permission Required'),
            description: Text(result.message),
          ),
        );

        // If permission granted, refresh FCM token
        if (result.isGranted) {
          await FirebaseApiService.refreshToken();
          _getFCMToken();
        }
      }
    } catch (e) {
      LoggerService.error('Error requesting notification permissions', e);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Error'),
            description: const Text(
              'Failed to request notification permissions',
            ),
          ),
        );
      }
    }
  }

  void _updatePreference(String key, bool value) {
    setState(() {
      _preferences[key] = value;
    });
    widget.onPreferencesChanged(_preferences);
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      title: Text('Notification Settings', style: theme.textTheme.h4),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Permission Status Section
            _buildPermissionStatusSection(theme),
            const SizedBox(height: 24),

            // Notification Preferences Section
            _buildNotificationPreferencesSection(theme),

            // Debug Information (only in debug mode)
            if (_fcmToken != null) ...[
              const SizedBox(height: 24),
              _buildDebugSection(theme),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionStatusSection(ShadThemeData theme) {
    final isEnabled =
        _permissionStatus == NotificationPermissionStatus.granted ||
        _permissionStatus == NotificationPermissionStatus.provisional;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Permission Status', style: theme.textTheme.h4),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(
              isEnabled ? LucideIcons.circleCheck : LucideIcons.circleAlert,
              color: isEnabled ? Colors.green : Colors.orange,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                NotificationPermissionService.getPermissionStatusMessage(
                  _permissionStatus,
                ),
                style: theme.textTheme.p,
              ),
            ),
          ],
        ),
        if (!isEnabled) ...[
          const SizedBox(height: 12),
          ShadButton(
            onPressed: _isLoading ? null : _requestPermissions,
            child:
                _isLoading
                    ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator.adaptive(strokeWidth: 2),
                    )
                    : const Text('Enable Notifications'),
          ),
        ],
      ],
    );
  }

  Widget _buildNotificationPreferencesSection(ShadThemeData theme) {
    final isEnabled =
        _permissionStatus == NotificationPermissionStatus.granted ||
        _permissionStatus == NotificationPermissionStatus.provisional;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Notification Types', style: theme.textTheme.h4),
        const SizedBox(height: 8),

        // Common notification types for all users
        _buildPreferenceSwitch(
          'Push Notifications',
          'push_notifications',
          'Receive push notifications on your device',
          isEnabled,
        ),
        _buildPreferenceSwitch(
          'Email Notifications',
          'email_notifications',
          'Receive notifications via email',
          true, // Email doesn't require push permission
        ),

        // User-type specific preferences
        ..._buildUserTypeSpecificPreferences(isEnabled),
      ],
    );
  }

  List<Widget> _buildUserTypeSpecificPreferences(bool isEnabled) {
    switch (widget.userType) {
      case 'claimant':
        return [
          _buildPreferenceSwitch(
            'Claim Updates',
            'claim_updates',
            'Get notified about updates to your claims',
            isEnabled,
          ),
          _buildPreferenceSwitch(
            'Document Requests',
            'document_requests',
            'Get notified when documents are requested',
            isEnabled,
          ),
        ];
      case 'solicitor':
        return [
          _buildPreferenceSwitch(
            'New Applications',
            'new_applications',
            'Get notified about new funding applications',
            isEnabled,
          ),
          _buildPreferenceSwitch(
            'Client Updates',
            'client_updates',
            'Get notified about client activity',
            isEnabled,
          ),
        ];
      case 'co_funder':
        return [
          _buildPreferenceSwitch(
            'Investment Opportunities',
            'investment_opportunities',
            'Get notified about new investment opportunities',
            isEnabled,
          ),
          _buildPreferenceSwitch(
            'Newsletter',
            'news_letter',
            'Receive our newsletter with market updates',
            true, // Newsletter doesn't require push permission
          ),
        ];
      default:
        return [];
    }
  }

  Widget _buildPreferenceSwitch(
    String title,
    String key,
    String description,
    bool enabled,
  ) {
    final value = _preferences[key] as bool? ?? false;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: ShadTheme.of(context).textTheme.p),
                Text(
                  description,
                  style: ShadTheme.of(context).textTheme.small.copyWith(
                    color: ShadTheme.of(context).colorScheme.mutedForeground,
                  ),
                ),
              ],
            ),
          ),
          ShadSwitch(
            value: enabled ? value : false,
            onChanged:
                enabled ? (newValue) => _updatePreference(key, newValue) : null,
          ),
        ],
      ),
    );
  }

  Widget _buildDebugSection(ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Debug Information', style: theme.textTheme.h4),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.muted,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'FCM Token Status: ${_fcmToken != null ? 'Active' : 'Not Set'}',
              ),
              if (_fcmToken != null) ...[
                const SizedBox(height: 4),
                Text(
                  'Token: ${_fcmToken!.substring(0, 20)}...',
                  style: theme.textTheme.small,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
