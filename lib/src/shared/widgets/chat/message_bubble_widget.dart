import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/chat_message_model.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/theme/app_theme.dart';

/// Reusable message bubble widget for chat interfaces
class MessageBubbleWidget extends StatelessWidget {
  final ChatMessageModel message;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool showAvatar;
  final bool showSenderName;

  const MessageBubbleWidget({
    super.key,
    required this.message,
    this.onTap,
    this.onLongPress,
    this.showAvatar = true,
    this.showSenderName = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final currentUserId = PocketBaseService().currentUser?.id;
    final isCurrentUser = message.senderId == currentUserId;
    final isSystemMessage = message.messageType == MessageType.system;

    if (isSystemMessage) {
      return _buildSystemMessage(context, theme);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
      child: Row(
        mainAxisAlignment:
            isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isCurrentUser && showAvatar) ...[
            _buildAvatar(theme, isCurrentUser),
            const SizedBox(width: 12),
          ],
          Flexible(
            child: GestureDetector(
              onTap: onTap,
              onLongPress: onLongPress,
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.75,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: _getBubbleColor(theme, isCurrentUser),
                  borderRadius: _getBorderRadius(isCurrentUser),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (!isCurrentUser &&
                        showSenderName &&
                        message.senderName != null)
                      _buildSenderName(theme),
                    _buildMessageContent(theme, isCurrentUser),
                    const SizedBox(height: 4),
                    _buildMessageFooter(theme, isCurrentUser),
                  ],
                ),
              ),
            ),
          ),
          if (isCurrentUser && showAvatar) ...[
            const SizedBox(width: 12),
            _buildAvatar(theme, isCurrentUser),
          ],
        ],
      ),
    );
  }

  Widget _buildSystemMessage(BuildContext context, ShadThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: theme.colorScheme.muted.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            message.messageContent,
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(ShadThemeData theme, bool isCurrentUser) {
    final avatarColor =
        isCurrentUser
            ? theme.colorScheme.secondary
            : AppTheme.primaryColor; // 3Pay brand primary
    final avatarTextColor = Colors.white;

    final displayName =
        isCurrentUser ? 'You' : (message.senderName ?? '3Pay Agent');

    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: avatarColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child:
          message.senderAvatar != null && message.senderAvatar!.isNotEmpty
              ? ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Image.network(
                  message.senderAvatar!,
                  fit: BoxFit.cover,
                  errorBuilder:
                      (context, error, stackTrace) => _buildAvatarIcon(
                        displayName,
                        avatarTextColor,
                        isCurrentUser,
                      ),
                ),
              )
              : _buildAvatarIcon(displayName, avatarTextColor, isCurrentUser),
    );
  }

  Widget _buildAvatarIcon(
    String displayName,
    Color textColor,
    bool isCurrentUser,
  ) {
    return Center(
      child:
          isCurrentUser
              ? Icon(Icons.person, color: textColor, size: 18)
              : Icon(Icons.lightbulb_outline, color: textColor, size: 18),
    );
  }

  Widget _buildSenderName(ShadThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        message.senderName!,
        style: theme.textTheme.small.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.mutedForeground,
        ),
      ),
    );
  }

  Widget _buildMessageContent(ShadThemeData theme, bool isCurrentUser) {
    final textColor =
        isCurrentUser
            ? theme.colorScheme.primaryForeground
            : theme.colorScheme.foreground;

    if (message.messageType == MessageType.file &&
        message.attachmentUrl != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (message.messageContent.isNotEmpty) ...[
            Text(message.messageContent, style: TextStyle(color: textColor)),
            const SizedBox(height: 8),
          ],
          _buildFileAttachment(theme, isCurrentUser),
        ],
      );
    }

    return Text(message.messageContent, style: TextStyle(color: textColor));
  }

  Widget _buildFileAttachment(ShadThemeData theme, bool isCurrentUser) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: (isCurrentUser
                ? theme.colorScheme.primaryForeground
                : theme.colorScheme.muted)
            .withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.attach_file,
            size: 16,
            color:
                isCurrentUser
                    ? theme.colorScheme.primaryForeground
                    : theme.colorScheme.foreground,
          ),
          const SizedBox(width: 4),
          Text(
            'File attachment',
            style: theme.textTheme.small.copyWith(
              color:
                  isCurrentUser
                      ? theme.colorScheme.primaryForeground
                      : theme.colorScheme.foreground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageFooter(ShadThemeData theme, bool isCurrentUser) {
    final footerColor =
        isCurrentUser
            ? theme.colorScheme.primaryForeground.withValues(alpha: 0.7)
            : theme.colorScheme.mutedForeground;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          _formatMessageTime(message.timestamp),
          style: theme.textTheme.small.copyWith(color: footerColor),
        ),
        if (isCurrentUser && message.status != MessageStatus.sent) ...[
          const SizedBox(width: 4),
          _buildStatusIcon(theme, footerColor),
        ],
      ],
    );
  }

  Widget _buildStatusIcon(ShadThemeData theme, Color color) {
    IconData icon;
    switch (message.status) {
      case MessageStatus.sending:
        icon = Icons.schedule;
        break;
      case MessageStatus.sent:
        icon = Icons.check;
        break;
      case MessageStatus.delivered:
        icon = Icons.done_all;
        break;
      case MessageStatus.read:
        icon = Icons.done_all;
        color = theme.colorScheme.primary;
        break;
      case MessageStatus.failed:
        icon = Icons.error_outline;
        color = theme.colorScheme.destructive;
        break;
    }

    return Icon(icon, size: 12, color: color);
  }

  Color _getBubbleColor(ShadThemeData theme, bool isCurrentUser) {
    return isCurrentUser
        ? AppTheme
            .primaryColor // 3Pay brand primary
        : theme.colorScheme.muted.withValues(alpha: 0.5);
  }

  BorderRadius _getBorderRadius(bool isCurrentUser) {
    return BorderRadius.only(
      topLeft: const Radius.circular(18),
      topRight: const Radius.circular(18),
      bottomLeft: Radius.circular(isCurrentUser ? 18 : 4),
      bottomRight: Radius.circular(isCurrentUser ? 4 : 18),
    );
  }

  String _formatMessageTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
