import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/theme/app_theme.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/loading_spinner_widget.dart';

/// Callback for sending text messages
typedef OnSendMessage = Future<bool> Function(String content);

/// Callback for sending messages with file attachments
typedef OnSendMessageWithFile =
    Future<bool> Function(String content, File file);

/// Callback for file attachment selection
typedef OnFileSelected = void Function(File file);

/// Reusable message input widget for chat interfaces
class MessageInputWidget extends StatefulWidget {
  final OnSendMessage onSendMessage;
  final OnSendMessageWithFile? onSendMessageWithFile;
  final OnFileSelected? onFileSelected;
  final bool isSending;
  final bool showFileButton;
  final String placeholder;
  final int? maxLines;
  final int? maxLength;
  final VoidCallback? onTypingStart;
  final VoidCallback? onTypingStop;
  final TextEditingController? controller;

  const MessageInputWidget({
    super.key,
    required this.onSendMessage,
    this.onSendMessageWithFile,
    this.onFileSelected,
    this.isSending = false,
    this.showFileButton = true,
    this.placeholder = 'Type your message...',
    this.maxLines = 5,
    this.maxLength = 1000,
    this.onTypingStart,
    this.onTypingStop,
    this.controller,
  });

  @override
  State<MessageInputWidget> createState() => _MessageInputWidgetState();
}

class _MessageInputWidgetState extends State<MessageInputWidget> {
  late final TextEditingController _controller;
  final FocusNode _focusNode = FocusNode();
  File? _selectedFile;
  bool _isTyping = false;
  late final bool _isExternalController;

  @override
  void initState() {
    super.initState();

    // Use external controller if provided, otherwise create internal one
    if (widget.controller != null) {
      _controller = widget.controller!;
      _isExternalController = true;
    } else {
      _controller = TextEditingController();
      _isExternalController = false;
    }

    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _focusNode.removeListener(_onFocusChanged);

    // Only dispose controller if it's internal (not external)
    if (!_isExternalController) {
      _controller.dispose();
    }

    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.trim().isNotEmpty;
    if (hasText && !_isTyping) {
      _isTyping = true;
      widget.onTypingStart?.call();
    } else if (!hasText && _isTyping) {
      _isTyping = false;
      widget.onTypingStop?.call();
    }

    // Trigger rebuild to update send button state
    if (mounted) {
      setState(() {});
    }
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus && _isTyping) {
      _isTyping = false;
      widget.onTypingStop?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.mutedForeground,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.border.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          if (_selectedFile != null) _buildFilePreview(theme),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
            decoration: BoxDecoration(
              color: theme.colorScheme.muted.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (widget.showFileButton) _buildFileButton(theme),
                _buildTextInput(theme),
                _buildSendButton(theme),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilePreview(ShadThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.attach_file,
            size: 20,
            color: theme.colorScheme.mutedForeground,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _selectedFile!.path.split('/').last,
              style: theme.textTheme.small,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          ShadButton.ghost(
            size: ShadButtonSize.sm,
            onPressed: () {
              setState(() {
                _selectedFile = null;
              });
            },
            child: const Icon(Icons.close, size: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildFileButton(ShadThemeData theme) {
    return IconButton(
      onPressed: widget.isSending ? null : _selectFile,
      padding: const EdgeInsets.all(8),
      icon: Icon(
        Icons.attach_file,
        size: 20,
        color: theme.colorScheme.mutedForeground,
      ),
    );
  }

  Widget _buildTextInput(ShadThemeData theme) {
    return Expanded(
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        maxLines: 1, // Single line for better design
        maxLength: widget.maxLength,
        keyboardType: TextInputType.text,
        textInputAction: TextInputAction.send,
        enabled: !widget.isSending,
        onSubmitted: (_) async {
          await _sendMessage();
          // Ensure focus is maintained after sending via Enter
          if (mounted && !_focusNode.hasFocus) {
            _focusNode.requestFocus();
          }
        },
        decoration: InputDecoration(
          hintText: widget.placeholder,
          hintStyle: TextStyle(
            color: theme.colorScheme.background,
            fontSize: 14,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 10,
          ),
          counterText: '', // Hide character counter
        ),
        style: TextStyle(color: theme.colorScheme.background, fontSize: 14),
      ),
    );
  }

  Widget _buildSendButton(ShadThemeData theme) {
    final hasContent =
        _controller.text.trim().isNotEmpty || _selectedFile != null;

    return GestureDetector(
      onTap: (widget.isSending || !hasContent) ? null : _sendMessage,
      child: Container(
        width: 36,
        height: 36,
        margin: const EdgeInsets.only(left: 8),
        decoration: BoxDecoration(
          color:
              hasContent && !widget.isSending
                  ? AppTheme
                      .primaryColor // 3Pay brand primary
                  : theme.colorScheme.muted,
          borderRadius: BorderRadius.circular(18),
        ),
        child: Center(
          child:
              widget.isSending
                  ? const LoadingSpinnerWidget(size: 16)
                  : Icon(
                    Icons.send,
                    size: 18,
                    color:
                        hasContent && !widget.isSending
                            ? Colors.white
                            : theme.colorScheme.mutedForeground,
                  ),
        ),
      ),
    );
  }

  Future<void> _selectFile() async {
    try {
      // TODO: Implement file picker
      // For now, just show a message that file selection is coming soon
      // Don't call the callback with an empty File as it causes URI errors

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('File selection coming soon')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error selecting file: $e'),
          backgroundColor: ShadTheme.of(context).colorScheme.destructive,
        ),
      );
    }
  }

  Future<void> _sendMessage() async {
    final content = _controller.text.trim();

    if (content.isEmpty && _selectedFile == null) {
      return;
    }

    bool success = false;

    try {
      if (_selectedFile != null && widget.onSendMessageWithFile != null) {
        success = await widget.onSendMessageWithFile!(content, _selectedFile!);
      } else {
        success = await widget.onSendMessage(content);
      }

      if (success) {
        // Store focus state before clearing
        final hadFocus = _focusNode.hasFocus;

        // Clear text and update state
        _controller.clear();
        setState(() {
          _selectedFile = null;
        });
        _isTyping = false;
        widget.onTypingStop?.call();

        // Restore focus only if it was focused before, and do it immediately
        if (hadFocus && mounted) {
          _focusNode.requestFocus();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending message: $e'),
            backgroundColor: ShadTheme.of(context).colorScheme.destructive,
          ),
        );
      }
    }
  }
}
