import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

/// Reusable chat header widget for chat interfaces
class ChatHeaderWidget extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String? subtitle;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final bool showBackButton;
  final bool isOnline;
  final bool isTyping;

  const ChatHeaderWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.onBackPressed,
    this.actions,
    this.showBackButton = true,
    this.isOnline = false,
    this.isTyping = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return AppBar(
      leading: showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
            )
          : null,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.h4,
            overflow: TextOverflow.ellipsis,
          ),
          if (subtitle != null || isTyping || isOnline)
            _buildSubtitle(theme),
        ],
      ),
      actions: actions,
      backgroundColor: theme.colorScheme.background,
      foregroundColor: theme.colorScheme.foreground,
      elevation: 1,
      shadowColor: theme.colorScheme.border,
    );
  }

  Widget _buildSubtitle(ShadThemeData theme) {
    String subtitleText;
    Color subtitleColor = theme.colorScheme.mutedForeground;

    if (isTyping) {
      subtitleText = 'Typing...';
      subtitleColor = theme.colorScheme.primary;
    } else if (isOnline) {
      subtitleText = 'Online';
      subtitleColor = theme.colorScheme.primary;
    } else if (subtitle != null) {
      subtitleText = subtitle!;
    } else {
      subtitleText = 'Offline';
    }

    return Row(
      children: [
        if (isOnline || isTyping) ...[
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: isTyping 
                  ? theme.colorScheme.primary 
                  : Colors.green,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
        ],
        Expanded(
          child: Text(
            subtitleText,
            style: theme.textTheme.small.copyWith(
              color: subtitleColor,
              fontStyle: isTyping ? FontStyle.italic : FontStyle.normal,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Chat header with agent information
class AgentChatHeaderWidget extends StatelessWidget implements PreferredSizeWidget {
  final String claimId;
  final String? claimTitle;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final bool isTyping;

  const AgentChatHeaderWidget({
    super.key,
    required this.claimId,
    this.claimTitle,
    this.onBackPressed,
    this.actions,
    this.isTyping = false,
  });

  @override
  Widget build(BuildContext context) {
    return ChatHeaderWidget(
      title: claimTitle ?? 'Claim Query',
      subtitle: 'Claim ID: $claimId',
      onBackPressed: onBackPressed,
      actions: actions,
      isOnline: true, // 3Pay agents are always considered online
      isTyping: isTyping,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
