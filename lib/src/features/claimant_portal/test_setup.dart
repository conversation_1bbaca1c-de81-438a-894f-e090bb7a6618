// Test script to verify claimant portal setup
// This file can be removed after testing is complete

import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/claimant_base_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

void testClaimantPortalSetup() {
  LoggerService.info('Testing Claimant Portal Setup...');

  // Test ClaimantProfile model
  final testProfile = ClaimantProfile(
    id: 'test123',
    userId: 'user123',
    userExpanded: {
      'name': '<PERSON>',
      'email': '<EMAIL>',
      'first_name': '<PERSON>',
      'last_name': '<PERSON><PERSON>',
      'mobile': '+1234567890',
      'address_line1': '123 Test Street',
      'city': 'Test City',
      'postcode': '12345',
    },
    associatedClaimIds: ['claim1', 'claim2'],
    created: DateTime.now(),
    updated: DateTime.now(),
  );

  // Test validation methods
  LoggerService.info('Profile is valid: ${testProfile.isValid}');
  LoggerService.info('Profile has valid email: ${testProfile.hasValidEmail}');
  LoggerService.info('Profile is complete: ${testProfile.isProfileComplete}');
  LoggerService.info('Missing fields: ${testProfile.missingFields}');
  LoggerService.info('Display name: ${testProfile.displayName}');
  LoggerService.info('Display email: ${testProfile.displayEmail}');
  LoggerService.info(
    'Has associated claims: ${testProfile.hasAssociatedClaims}',
  );

  // Test JSON serialization
  final json = testProfile.toJson();
  final fromJson = ClaimantProfile.fromJson(json);
  LoggerService.info(
    'JSON serialization works: ${fromJson.id == testProfile.id}',
  );

  // Test service initialization
  try {
    final service = ClaimantBaseService();
    LoggerService.info('Service initialized: ${service.runtimeType}');
    LoggerService.info('Is authenticated: ${service.isAuthenticated}');
    LoggerService.info('Is claimant: ${service.isClaimant}');
  } catch (e) {
    LoggerService.error('Service initialization error', e);
  }

  LoggerService.info('Claimant Portal Setup Test Complete!');
}

// Test incomplete profile
void testIncompleteProfile() {
  final incompleteProfile = ClaimantProfile(
    id: 'test456',
    userId: 'user456',
    userExpanded: null, // No user data expanded
    associatedClaimIds: [],
    created: DateTime.now(),
    updated: DateTime.now(),
  );

  LoggerService.info('Incomplete profile validation:');
  LoggerService.info('Is valid: ${incompleteProfile.isValid}');
  LoggerService.info('Is complete: ${incompleteProfile.isProfileComplete}');
  LoggerService.info('Missing fields: ${incompleteProfile.missingFields}');
}
