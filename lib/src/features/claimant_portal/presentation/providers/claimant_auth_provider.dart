import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/claimant_base_service.dart';

/// Authentication state for claimants
class ClaimantAuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final RecordModel? user;
  final ClaimantProfile? profile;
  final String? error;

  const ClaimantAuthState({
    this.isAuthenticated = false,
    this.isLoading = false,
    this.user,
    this.profile,
    this.error,
  });

  ClaimantAuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    RecordModel? user,
    ClaimantProfile? profile,
    String? error,
  }) {
    return ClaimantAuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      profile: profile ?? this.profile,
      error: error ?? this.error,
    );
  }

  bool get isClaimant => user?.data['user_type'] == 'claimant';
  bool get hasProfile => profile != null;
  bool get isProfileComplete => profile?.isProfileComplete ?? false;
}

/// Claimant authentication provider
class ClaimantAuthNotifier extends StateNotifier<ClaimantAuthState> {
  final ClaimantBaseService _service;
  final PocketBase _pb;

  ClaimantAuthNotifier(this._service, this._pb)
    : super(const ClaimantAuthState()) {
    _initializeAuth();
  }

  /// Initialize authentication state
  void _initializeAuth() {
    if (_pb.authStore.isValid) {
      final user = _pb.authStore.record;
      if (user != null && user.data['user_type'] == 'claimant') {
        state = state.copyWith(isAuthenticated: true, user: user);
        _loadClaimantProfile();
      }
    }
  }

  /// Load claimant profile
  Future<void> _loadClaimantProfile() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final profile = await _service.getCurrentClaimantProfile();

      state = state.copyWith(isLoading: false, profile: profile);
    } catch (e) {
      LoggerService.error('Error loading claimant profile', e);
      state = state.copyWith(
        isLoading: false,
        error: _service.getErrorMessage(e),
      );
    }
  }

  /// Sign in claimant
  Future<bool> signIn(String email, String password) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final authData = await _pb
          .collection('users')
          .authWithPassword(email, password);

      if (authData.record?.data['user_type'] != 'claimant') {
        _pb.authStore.clear();
        state = state.copyWith(
          isLoading: false,
          error: 'Invalid user type. This portal is for claimants only.',
        );
        return false;
      }

      state = state.copyWith(
        isAuthenticated: true,
        isLoading: false,
        user: authData.record,
      );

      // Refresh FCM token after successful claimant sign in
      try {
        await FirebaseApiService.refreshToken();
        LoggerService.info('FCM token refreshed for claimant');
      } catch (e) {
        LoggerService.warning('Failed to refresh FCM token for claimant: $e');
        // Don't fail the sign in process if FCM token refresh fails
      }

      await _loadClaimantProfile();
      return true;
    } catch (e) {
      LoggerService.error('Claimant sign in error', e);
      state = state.copyWith(
        isLoading: false,
        error: _service.getErrorMessage(e),
      );
      return false;
    }
  }

  /// Sign out claimant
  Future<void> signOut() async {
    try {
      // Clear FCM token before signing out
      try {
        await FirebaseApiService.clearToken();
        LoggerService.info('FCM token cleared for claimant sign out');
      } catch (e) {
        LoggerService.warning('Failed to clear FCM token for claimant: $e');
        // Continue with sign out even if FCM token clearing fails
      }

      _pb.authStore.clear();
      state = const ClaimantAuthState();
      LoggerService.info('Claimant signed out successfully');
    } catch (e) {
      LoggerService.error('Error signing out claimant', e);
    }
  }

  /// Create or update claimant profile
  Future<bool> createOrUpdateProfile({
    Map<String, dynamic>? notificationPreferences,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      ClaimantProfile profile;

      if (state.profile == null) {
        // Create new profile
        profile = await _service.createClaimantProfile(
          notificationPreferences: notificationPreferences,
        );
      } else {
        // Update existing profile
        final updates = <String, dynamic>{};
        if (notificationPreferences != null) {
          updates['notification_preferences'] = notificationPreferences;
        }

        profile = await _service.updateClaimantProfile(
          state.profile!.id,
          updates,
        );
      }

      state = state.copyWith(isLoading: false, profile: profile);

      return true;
    } catch (e) {
      LoggerService.error('Error creating/updating claimant profile', e);
      state = state.copyWith(
        isLoading: false,
        error: _service.getErrorMessage(e),
      );
      return false;
    }
  }

  /// Refresh authentication state
  Future<void> refresh() async {
    if (_pb.authStore.isValid) {
      await _loadClaimantProfile();
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for claimant authentication
final claimantAuthProvider =
    StateNotifierProvider<ClaimantAuthNotifier, ClaimantAuthState>((ref) {
      final service = ClaimantBaseService();
      final pocketBase = PocketBaseService().pb;
      return ClaimantAuthNotifier(service, pocketBase);
    });

/// Provider for current claimant profile
final currentClaimantProfileProvider = Provider<ClaimantProfile?>((ref) {
  final authState = ref.watch(claimantAuthProvider);
  return authState.profile;
});

/// Provider for claimant authentication status
final isClaimantAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(claimantAuthProvider);
  return authState.isAuthenticated && authState.isClaimant;
});
