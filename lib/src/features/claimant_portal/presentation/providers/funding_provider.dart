import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:logger/logger.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/funding_status_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/funding_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/claimant_auth_provider.dart';

part 'funding_provider.freezed.dart';

/// Funding state for the claimant portal
@freezed
class FundingState with _$FundingState {
  const factory FundingState({
    @Default([]) List<FundingStatus> commitments,
    @Default([]) List<FundingTimelineEvent> timeline,
    FundingAggregation? aggregation,
    @Default(false) bool isLoading,
    @Default(false) bool isRefreshing,
    String? error,
    DateTime? lastUpdated,
  }) = _FundingState;
}

/// Funding provider for managing funding dashboard state
class FundingNotifier extends StateNotifier<FundingState> {
  final FundingService _fundingService;
  final Ref _ref;
  static final Logger _logger = Logger();

  FundingNotifier(this._fundingService, this._ref) : super(const FundingState());

  /// Initialize funding data
  Future<void> initialize() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final authState = _ref.read(claimantAuthProvider);
      if (!authState.isAuthenticated || authState.user == null) {
        throw Exception('User not authenticated');
      }

      final userId = authState.user!.id;
      _logger.i('Initializing funding data for user: $userId');

      // Fetch all funding data concurrently
      final results = await Future.wait([
        _fundingService.getFundingCommitments(userId),
        _fundingService.calculateFundingAggregation(userId),
      ]);

      final commitments = results[0] as List<FundingStatus>;
      final aggregation = results[1] as FundingAggregation;

      // Generate timeline from commitments
      final timeline = await _generateTimelineFromCommitments(commitments);

      state = state.copyWith(
        commitments: commitments,
        aggregation: aggregation,
        timeline: timeline,
        isLoading: false,
        lastUpdated: DateTime.now(),
      );

      _logger.i('Funding data initialized successfully');
    } catch (e) {
      _logger.e('Error initializing funding data: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Refresh funding data
  Future<void> refresh() async {
    if (state.isRefreshing) return;

    state = state.copyWith(isRefreshing: true, error: null);

    try {
      final authState = _ref.read(claimantAuthProvider);
      if (!authState.isAuthenticated || authState.user == null) {
        throw Exception('User not authenticated');
      }

      final userId = authState.user!.id;
      _logger.i('Refreshing funding data for user: $userId');

      // Fetch updated funding data
      final results = await Future.wait([
        _fundingService.getFundingCommitments(userId),
        _fundingService.calculateFundingAggregation(userId),
      ]);

      final commitments = results[0] as List<FundingStatus>;
      final aggregation = results[1] as FundingAggregation;

      // Generate updated timeline
      final timeline = await _generateTimelineFromCommitments(commitments);

      state = state.copyWith(
        commitments: commitments,
        aggregation: aggregation,
        timeline: timeline,
        isRefreshing: false,
        lastUpdated: DateTime.now(),
      );

      _logger.i('Funding data refreshed successfully');
    } catch (e) {
      _logger.e('Error refreshing funding data: $e');
      state = state.copyWith(
        isRefreshing: false,
        error: e.toString(),
      );
    }
  }

  /// Get funding status for a specific claim
  Future<List<FundingStatus>> getFundingForClaim(String claimId) async {
    try {
      _logger.i('Fetching funding for claim: $claimId');
      return await _fundingService.getFundingStatusForClaim(claimId);
    } catch (e) {
      _logger.e('Error fetching funding for claim $claimId: $e');
      rethrow;
    }
  }

  /// Generate timeline events from commitments
  Future<List<FundingTimelineEvent>> _generateTimelineFromCommitments(
    List<FundingStatus> commitments,
  ) async {
    final events = <FundingTimelineEvent>[];

    for (final commitment in commitments) {
      // Add commitment event
      events.add(FundingTimelineEvent(
        id: '${commitment.id}_commitment',
        stage: FundingStage.commitment,
        date: commitment.commitmentDate,
        title: 'Funding Commitment Received',
        description: 'Commitment of ${commitment.formattedAmount} from ${commitment.funderInfo?.name ?? 'Anonymous Funder'}',
        amount: commitment.amount,
        status: commitment.status,
      ));

      // Add approval event if approved
      if (commitment.approvalDate != null) {
        events.add(FundingTimelineEvent(
          id: '${commitment.id}_approval',
          stage: FundingStage.approval,
          date: commitment.approvalDate!,
          title: 'Funding Approved',
          description: 'Funding commitment of ${commitment.formattedAmount} approved by 3Pay Global',
          amount: commitment.amount,
          status: commitment.status,
        ));
      }

      // Add deployment event if active
      if (commitment.status == FundingCommitmentStatus.active) {
        events.add(FundingTimelineEvent(
          id: '${commitment.id}_deployment',
          stage: FundingStage.deployment,
          date: commitment.approvalDate ?? commitment.commitmentDate,
          title: 'Funds Deployed',
          description: 'Funding of ${commitment.formattedAmount} deployed to claim',
          amount: commitment.amount,
          status: commitment.status,
        ));
      }

      // Add completion event if completed
      if (commitment.status == FundingCommitmentStatus.completed) {
        events.add(FundingTimelineEvent(
          id: '${commitment.id}_completion',
          stage: FundingStage.completion,
          date: commitment.approvalDate ?? commitment.commitmentDate,
          title: 'Funding Completed',
          description: 'Funding cycle completed for ${commitment.formattedAmount}',
          amount: commitment.amount,
          status: commitment.status,
        ));
      }
    }

    // Sort events by date (newest first)
    events.sort((a, b) => b.date.compareTo(a.date));
    return events;
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Check if funding data needs refresh (older than 5 minutes)
  bool get needsRefresh {
    if (state.lastUpdated == null) return true;
    final now = DateTime.now();
    final difference = now.difference(state.lastUpdated!);
    return difference.inMinutes >= 5;
  }

  /// Get funding statistics summary
  Map<String, dynamic> get fundingStatistics {
    final aggregation = state.aggregation;
    if (aggregation == null) return {};

    return {
      'totalSecured': aggregation.formattedTotalSecured,
      'totalPending': aggregation.formattedTotalPending,
      'fundingPercentage': '${aggregation.fundingPercentage.toStringAsFixed(1)}%',
      'commitmentCount': aggregation.commitmentCount,
      'securedCount': aggregation.securedCount,
      'pendingCount': aggregation.pendingCount,
      'statusDescription': aggregation.fundingStatusDescription,
    };
  }
}

/// Provider for funding service
final fundingServiceProvider = Provider<FundingService>((ref) {
  return FundingService();
});

/// Provider for funding state
final fundingProvider = StateNotifierProvider<FundingNotifier, FundingState>((ref) {
  final fundingService = ref.watch(fundingServiceProvider);
  return FundingNotifier(fundingService, ref);
});

/// Provider for funding statistics
final fundingStatisticsProvider = Provider<Map<String, dynamic>>((ref) {
  final fundingState = ref.watch(fundingProvider);
  final aggregation = fundingState.aggregation;
  
  if (aggregation == null) return {};

  return {
    'totalSecured': aggregation.formattedTotalSecured,
    'totalPending': aggregation.formattedTotalPending,
    'fundingPercentage': '${aggregation.fundingPercentage.toStringAsFixed(1)}%',
    'commitmentCount': aggregation.commitmentCount,
    'securedCount': aggregation.securedCount,
    'pendingCount': aggregation.pendingCount,
    'statusDescription': aggregation.fundingStatusDescription,
  };
});

/// Provider for checking if funding data is available
final hasFundingDataProvider = Provider<bool>((ref) {
  final fundingState = ref.watch(fundingProvider);
  return fundingState.commitments.isNotEmpty || fundingState.aggregation != null;
});
