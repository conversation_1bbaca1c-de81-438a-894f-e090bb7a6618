// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_conversations_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$ChatConversationsState {
  List<ChatConversationModel> get conversations =>
      throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isRefreshing => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  String? get searchQuery => throw _privateConstructorUsedError;

  /// Create a copy of ChatConversationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatConversationsStateCopyWith<ChatConversationsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatConversationsStateCopyWith<$Res> {
  factory $ChatConversationsStateCopyWith(
    ChatConversationsState value,
    $Res Function(ChatConversationsState) then,
  ) = _$ChatConversationsStateCopyWithImpl<$Res, ChatConversationsState>;
  @useResult
  $Res call({
    List<ChatConversationModel> conversations,
    bool isLoading,
    bool isRefreshing,
    String? error,
    String? searchQuery,
  });
}

/// @nodoc
class _$ChatConversationsStateCopyWithImpl<
  $Res,
  $Val extends ChatConversationsState
>
    implements $ChatConversationsStateCopyWith<$Res> {
  _$ChatConversationsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatConversationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? conversations = null,
    Object? isLoading = null,
    Object? isRefreshing = null,
    Object? error = freezed,
    Object? searchQuery = freezed,
  }) {
    return _then(
      _value.copyWith(
            conversations:
                null == conversations
                    ? _value.conversations
                    : conversations // ignore: cast_nullable_to_non_nullable
                        as List<ChatConversationModel>,
            isLoading:
                null == isLoading
                    ? _value.isLoading
                    : isLoading // ignore: cast_nullable_to_non_nullable
                        as bool,
            isRefreshing:
                null == isRefreshing
                    ? _value.isRefreshing
                    : isRefreshing // ignore: cast_nullable_to_non_nullable
                        as bool,
            error:
                freezed == error
                    ? _value.error
                    : error // ignore: cast_nullable_to_non_nullable
                        as String?,
            searchQuery:
                freezed == searchQuery
                    ? _value.searchQuery
                    : searchQuery // ignore: cast_nullable_to_non_nullable
                        as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ChatConversationsStateImplCopyWith<$Res>
    implements $ChatConversationsStateCopyWith<$Res> {
  factory _$$ChatConversationsStateImplCopyWith(
    _$ChatConversationsStateImpl value,
    $Res Function(_$ChatConversationsStateImpl) then,
  ) = __$$ChatConversationsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<ChatConversationModel> conversations,
    bool isLoading,
    bool isRefreshing,
    String? error,
    String? searchQuery,
  });
}

/// @nodoc
class __$$ChatConversationsStateImplCopyWithImpl<$Res>
    extends
        _$ChatConversationsStateCopyWithImpl<$Res, _$ChatConversationsStateImpl>
    implements _$$ChatConversationsStateImplCopyWith<$Res> {
  __$$ChatConversationsStateImplCopyWithImpl(
    _$ChatConversationsStateImpl _value,
    $Res Function(_$ChatConversationsStateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ChatConversationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? conversations = null,
    Object? isLoading = null,
    Object? isRefreshing = null,
    Object? error = freezed,
    Object? searchQuery = freezed,
  }) {
    return _then(
      _$ChatConversationsStateImpl(
        conversations:
            null == conversations
                ? _value._conversations
                : conversations // ignore: cast_nullable_to_non_nullable
                    as List<ChatConversationModel>,
        isLoading:
            null == isLoading
                ? _value.isLoading
                : isLoading // ignore: cast_nullable_to_non_nullable
                    as bool,
        isRefreshing:
            null == isRefreshing
                ? _value.isRefreshing
                : isRefreshing // ignore: cast_nullable_to_non_nullable
                    as bool,
        error:
            freezed == error
                ? _value.error
                : error // ignore: cast_nullable_to_non_nullable
                    as String?,
        searchQuery:
            freezed == searchQuery
                ? _value.searchQuery
                : searchQuery // ignore: cast_nullable_to_non_nullable
                    as String?,
      ),
    );
  }
}

/// @nodoc

class _$ChatConversationsStateImpl implements _ChatConversationsState {
  const _$ChatConversationsStateImpl({
    final List<ChatConversationModel> conversations = const [],
    this.isLoading = false,
    this.isRefreshing = false,
    this.error,
    this.searchQuery,
  }) : _conversations = conversations;

  final List<ChatConversationModel> _conversations;
  @override
  @JsonKey()
  List<ChatConversationModel> get conversations {
    if (_conversations is EqualUnmodifiableListView) return _conversations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_conversations);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isRefreshing;
  @override
  final String? error;
  @override
  final String? searchQuery;

  @override
  String toString() {
    return 'ChatConversationsState(conversations: $conversations, isLoading: $isLoading, isRefreshing: $isRefreshing, error: $error, searchQuery: $searchQuery)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatConversationsStateImpl &&
            const DeepCollectionEquality().equals(
              other._conversations,
              _conversations,
            ) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isRefreshing, isRefreshing) ||
                other.isRefreshing == isRefreshing) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_conversations),
    isLoading,
    isRefreshing,
    error,
    searchQuery,
  );

  /// Create a copy of ChatConversationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatConversationsStateImplCopyWith<_$ChatConversationsStateImpl>
  get copyWith =>
      __$$ChatConversationsStateImplCopyWithImpl<_$ChatConversationsStateImpl>(
        this,
        _$identity,
      );
}

abstract class _ChatConversationsState implements ChatConversationsState {
  const factory _ChatConversationsState({
    final List<ChatConversationModel> conversations,
    final bool isLoading,
    final bool isRefreshing,
    final String? error,
    final String? searchQuery,
  }) = _$ChatConversationsStateImpl;

  @override
  List<ChatConversationModel> get conversations;
  @override
  bool get isLoading;
  @override
  bool get isRefreshing;
  @override
  String? get error;
  @override
  String? get searchQuery;

  /// Create a copy of ChatConversationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatConversationsStateImplCopyWith<_$ChatConversationsStateImpl>
  get copyWith => throw _privateConstructorUsedError;
}
