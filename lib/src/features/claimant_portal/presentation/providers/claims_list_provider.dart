import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import '../../data/models/claimant_claim_model.dart';
import '../../data/repositories/claims_repository.dart';
import '../widgets/claims_filter_widget.dart';
import 'claims_provider.dart';

/// State for claims list with filtering and search
class ClaimsListState {
  final List<ClaimantClaim> allClaims;
  final List<ClaimantClaim> filteredClaims;
  final String searchQuery;
  final ClaimsFilterOptions filterOptions;
  final bool isLoading;
  final bool isRefreshing;
  final String? error;
  final DateTime? lastUpdated;

  const ClaimsListState({
    this.allClaims = const [],
    this.filteredClaims = const [],
    this.searchQuery = '',
    this.filterOptions = const ClaimsFilterOptions(),
    this.isLoading = false,
    this.isRefreshing = false,
    this.error,
    this.lastUpdated,
  });

  ClaimsListState copyWith({
    List<ClaimantClaim>? allClaims,
    List<ClaimantClaim>? filteredClaims,
    String? searchQuery,
    ClaimsFilterOptions? filterOptions,
    bool? isLoading,
    bool? isRefreshing,
    String? error,
    DateTime? lastUpdated,
  }) {
    return ClaimsListState(
      allClaims: allClaims ?? this.allClaims,
      filteredClaims: filteredClaims ?? this.filteredClaims,
      searchQuery: searchQuery ?? this.searchQuery,
      filterOptions: filterOptions ?? this.filterOptions,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  bool get hasActiveFilters =>
      filterOptions.hasActiveFilters || searchQuery.isNotEmpty;
  int get totalClaims => allClaims.length;
  int get filteredCount => filteredClaims.length;
}

/// Provider for claims list with filtering and search functionality
class ClaimsListNotifier extends StateNotifier<ClaimsListState> {
  final ClaimsRepository _repository;
  Timer? _searchDebounceTimer;

  ClaimsListNotifier(this._repository) : super(const ClaimsListState());

  /// Initialize claims list
  Future<void> initialize() async {
    if (state.isLoading) return;

    try {
      state = state.copyWith(isLoading: true, error: null);

      // Subscribe to real-time updates
      await _repository.subscribeToUpdates();

      // Load initial data
      await loadClaims();

      state = state.copyWith(isLoading: false);
    } catch (e) {
      LoggerService.error('Error initializing claims list', e);
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Load claims data
  Future<void> loadClaims({bool forceRefresh = false}) async {
    try {
      if (!forceRefresh && state.isRefreshing) return;

      if (forceRefresh) {
        state = state.copyWith(isRefreshing: true, error: null);
      }

      final claims = await _repository.getClaims(forceRefresh: forceRefresh);

      state = state.copyWith(
        allClaims: claims,
        isRefreshing: false,
        lastUpdated: DateTime.now(),
        error: null,
      );

      // Apply current filters
      _applyFiltersAndSearch();
    } catch (e) {
      LoggerService.error('Error loading claims', e);
      state = state.copyWith(isRefreshing: false, error: e.toString());
    }
  }

  /// Refresh claims data
  Future<void> refresh() async {
    return loadClaims(forceRefresh: true);
  }

  /// Update search query with debouncing
  void updateSearchQuery(String query) {
    _searchDebounceTimer?.cancel();

    state = state.copyWith(searchQuery: query);

    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      _applyFiltersAndSearch();
    });
  }

  /// Update filter options
  void updateFilterOptions(ClaimsFilterOptions options) {
    state = state.copyWith(filterOptions: options);
    _applyFiltersAndSearch();
  }

  /// Clear all filters and search
  void clearFilters() {
    state = state.copyWith(
      searchQuery: '',
      filterOptions: const ClaimsFilterOptions(),
    );
    _applyFiltersAndSearch();
  }

  /// Apply filters and search to claims list
  void _applyFiltersAndSearch() {
    List<ClaimantClaim> filtered = List.from(state.allClaims);

    // Apply search filter
    if (state.searchQuery.isNotEmpty) {
      final query = state.searchQuery.toLowerCase();
      filtered =
          filtered.where((claim) {
            return claim.title.toLowerCase().contains(query) ||
                claim.id.toLowerCase().contains(query) ||
                claim.currentStage.toLowerCase().contains(query) ||
                (claim.description?.toLowerCase().contains(query) ?? false) ||
                (claim.claimType?.toLowerCase().contains(query) ?? false);
          }).toList();
    }

    // Apply status filters
    if (state.filterOptions.statusFilters.isNotEmpty) {
      filtered =
          filtered.where((claim) {
            return state.filterOptions.statusFilters.contains(claim.status);
          }).toList();
    }

    // Apply claim type filter
    if (state.filterOptions.claimType != null) {
      filtered =
          filtered.where((claim) {
            return claim.claimType == state.filterOptions.claimType;
          }).toList();
    }

    // Apply date range filter
    if (state.filterOptions.dateRange != null) {
      final range = state.filterOptions.dateRange!;
      filtered =
          filtered.where((claim) {
            final submissionDate = claim.submissionDate;
            return submissionDate.isAfter(
                  range.start.subtract(const Duration(days: 1)),
                ) &&
                submissionDate.isBefore(range.end.add(const Duration(days: 1)));
          }).toList();
    }

    // Apply sorting
    _sortClaims(filtered);

    state = state.copyWith(filteredClaims: filtered);
  }

  /// Sort claims based on current sort options
  void _sortClaims(List<ClaimantClaim> claims) {
    final sortOption = state.filterOptions.sortOption;
    final ascending = state.filterOptions.sortAscending;

    claims.sort((a, b) {
      int comparison;

      switch (sortOption) {
        case ClaimsSortOption.submissionDate:
          comparison = a.submissionDate.compareTo(b.submissionDate);
          break;
        case ClaimsSortOption.title:
          comparison = a.title.compareTo(b.title);
          break;
        case ClaimsSortOption.status:
          comparison = a.status.index.compareTo(b.status.index);
          break;
        case ClaimsSortOption.stage:
          comparison = a.currentStage.compareTo(b.currentStage);
          break;
        case ClaimsSortOption.fundingAmount:
          final aAmount = a.requiredFundingAmount ?? 0;
          final bAmount = b.requiredFundingAmount ?? 0;
          comparison = aAmount.compareTo(bAmount);
          break;
      }

      return ascending ? comparison : -comparison;
    });
  }

  /// Get claims by status
  List<ClaimantClaim> getClaimsByStatus(Set<ClaimStatus> statuses) {
    return state.allClaims
        .where((claim) => statuses.contains(claim.status))
        .toList();
  }

  /// Get active claims (excluding completed, cancelled, rejected)
  List<ClaimantClaim> getActiveClaims() {
    const inactiveStatuses = {
      ClaimStatus.completed,
      ClaimStatus.cancelled,
      ClaimStatus.rejected,
    };
    return state.allClaims
        .where((claim) => !inactiveStatuses.contains(claim.status))
        .toList();
  }

  /// Get claims statistics
  Map<String, int> getClaimsStatistics() {
    final stats = <String, int>{};

    for (final status in ClaimStatus.values) {
      stats[status.toString()] =
          state.allClaims.where((claim) => claim.status == status).length;
    }

    stats['total'] = state.allClaims.length;
    stats['active'] = getActiveClaims().length;

    return stats;
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    _repository.dispose();
    super.dispose();
  }
}

/// Provider for claims list state
final claimsListProvider =
    StateNotifierProvider<ClaimsListNotifier, ClaimsListState>((ref) {
      final repository = ref.watch(claimsRepositoryProvider);
      return ClaimsListNotifier(repository);
    });

/// Provider for filtered claims only
final filteredClaimsProvider = Provider<List<ClaimantClaim>>((ref) {
  final claimsListState = ref.watch(claimsListProvider);
  return claimsListState.filteredClaims;
});

/// Provider for claims statistics
final claimsStatisticsProvider = Provider<Map<String, int>>((ref) {
  final notifier = ref.watch(claimsListProvider.notifier);
  return notifier.getClaimsStatistics();
});

/// Provider for active claims count
final activeClaimsCountProvider = Provider<int>((ref) {
  final notifier = ref.watch(claimsListProvider.notifier);
  return notifier.getActiveClaims().length;
});
