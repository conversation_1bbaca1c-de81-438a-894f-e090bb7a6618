import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/chat_message_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/repositories/chat_repository.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/chat_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/claimant_auth_provider.dart';

part 'chat_interface_provider.freezed.dart';

/// State for chat interface
@freezed
class ChatInterfaceState with _$ChatInterfaceState {
  const factory ChatInterfaceState({
    @Default([]) List<ChatMessageModel> messages,
    @Default(false) bool isLoading,
    @Default(false) bool isSending,
    @Default(false) bool isTyping,
    String? error,
    String? claimId,
    String? claimTitle,
  }) = _ChatInterfaceState;
}

/// Provider for managing chat interface
class ChatInterfaceNotifier extends StateNotifier<ChatInterfaceState> {
  final ChatRepository _chatRepository;
  final String claimId;

  ChatInterfaceNotifier(this._chatRepository, this.claimId)
    : super(ChatInterfaceState(claimId: claimId, isLoading: true)) {
    _init();
  }

  /// Initialize chat interface
  Future<void> _init() async {
    try {
      LoggerService.info('Initializing chat interface for claim: $claimId');

      // Load messages first
      await loadMessages();

      // Then set up real-time subscription
      await _subscribeToUpdates();

      LoggerService.info('Chat interface initialized successfully');
    } catch (e) {
      LoggerService.error('Error initializing chat interface', e);
      if (mounted) {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to initialize chat: ${e.toString()}',
        );
      }
    }
  }

  /// Load messages for the conversation
  Future<void> loadMessages({bool forceRefresh = false}) async {
    try {
      if (mounted) {
        state = state.copyWith(isLoading: state.messages.isEmpty, error: null);
      }

      LoggerService.info(
        'Loading messages for claim: $claimId (forceRefresh: $forceRefresh)',
      );

      final messages = await _chatRepository.getMessages(
        claimId: claimId,
        forceRefresh: forceRefresh,
      );

      if (mounted) {
        state = state.copyWith(messages: messages, isLoading: false);
      }

      LoggerService.info(
        'Loaded ${messages.length} messages for claim: $claimId',
      );
    } catch (e) {
      LoggerService.error('Error loading messages for claim $claimId', e);
      if (mounted) {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to load messages: ${e.toString()}',
        );
      }
    }
  }

  /// Send a text message
  Future<bool> sendMessage(String content) async {
    if (content.trim().isEmpty) return false;

    try {
      if (mounted) {
        state = state.copyWith(isSending: true, error: null);
      }

      LoggerService.info('Sending message to claim: $claimId');

      final message = await _chatRepository.sendMessage(
        claimId: claimId,
        content: content.trim(),
        recipientGroup: 'agent',
      );

      LoggerService.info('Message sent successfully: ${message.id}');

      if (mounted) {
        state = state.copyWith(isSending: false);
      }
      return true;
    } catch (e) {
      LoggerService.error('Error sending message to claim $claimId', e);
      if (mounted) {
        state = state.copyWith(
          isSending: false,
          error: 'Failed to send message: ${e.toString()}',
        );
      }
      return false;
    }
  }

  /// Send a message with file attachment
  Future<bool> sendMessageWithFile({
    required String content,
    required File file,
  }) async {
    try {
      state = state.copyWith(isSending: true, error: null);

      final message = await _chatRepository.sendMessageWithFile(
        claimId: claimId,
        content: content.trim(),
        file: file,
        recipientGroup: 'agent',
      );

      LoggerService.info('Message with file sent successfully: ${message.id}');
      state = state.copyWith(isSending: false);
      return true;
    } catch (e) {
      LoggerService.error('Error sending message with file', e);
      state = state.copyWith(isSending: false, error: e.toString());
      return false;
    }
  }

  /// Set typing indicator
  void setTyping(bool isTyping) {
    if (state.isTyping != isTyping) {
      state = state.copyWith(isTyping: isTyping);
    }
  }

  /// Subscribe to real-time message updates
  Future<void> _subscribeToUpdates() async {
    try {
      await _chatRepository.subscribeToConversationUpdates(claimId);

      _chatRepository.messagesStream.listen(
        (messages) {
          if (mounted) {
            state = state.copyWith(messages: messages);
          }
        },
        onError: (error) {
          LoggerService.error('Error in messages stream', error);
          if (mounted) {
            state = state.copyWith(error: error.toString());
          }
        },
      );

      LoggerService.info('Subscribed to real-time updates for claim: $claimId');
    } catch (e) {
      LoggerService.error('Error subscribing to updates', e);
      state = state.copyWith(error: e.toString());
    }
  }

  /// Refresh messages
  Future<void> refreshMessages() async {
    await loadMessages(forceRefresh: true);
  }

  /// Force re-initialization (useful for debugging)
  Future<void> forceReinit() async {
    LoggerService.info(
      'Force re-initializing chat interface for claim: $claimId',
    );
    await _init();
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Set claim title
  void setClaimTitle(String title) {
    state = state.copyWith(claimTitle: title);
  }

  @override
  void dispose() {
    _chatRepository.unsubscribeFromUpdates();
    super.dispose();
  }
}

/// Provider instance with authentication dependency
final chatInterfaceProvider = StateNotifierProvider.family<
  ChatInterfaceNotifier,
  ChatInterfaceState,
  String
>((ref, claimId) {
  final chatRepository = ref.watch(chatRepositoryProvider);

  // Watch the auth provider to ensure authentication is ready
  // This creates a dependency that will rebuild the provider when auth changes
  ref.watch(claimantAuthProvider);

  return ChatInterfaceNotifier(chatRepository, claimId);
});

/// Chat service provider
final chatServiceProvider = Provider<ChatService>((ref) {
  return ChatService();
});

/// Chat repository provider
final chatRepositoryProvider = Provider<ChatRepository>((ref) {
  final chatService = ref.watch(chatServiceProvider);
  return ChatRepository(chatService);
});
