// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'funding_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$FundingState {
  List<FundingStatus> get commitments => throw _privateConstructorUsedError;
  List<FundingTimelineEvent> get timeline => throw _privateConstructorUsedError;
  FundingAggregation? get aggregation => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isRefreshing => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Create a copy of FundingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FundingStateCopyWith<FundingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FundingStateCopyWith<$Res> {
  factory $FundingStateCopyWith(
    FundingState value,
    $Res Function(FundingState) then,
  ) = _$FundingStateCopyWithImpl<$Res, FundingState>;
  @useResult
  $Res call({
    List<FundingStatus> commitments,
    List<FundingTimelineEvent> timeline,
    FundingAggregation? aggregation,
    bool isLoading,
    bool isRefreshing,
    String? error,
    DateTime? lastUpdated,
  });
}

/// @nodoc
class _$FundingStateCopyWithImpl<$Res, $Val extends FundingState>
    implements $FundingStateCopyWith<$Res> {
  _$FundingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FundingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? commitments = null,
    Object? timeline = null,
    Object? aggregation = freezed,
    Object? isLoading = null,
    Object? isRefreshing = null,
    Object? error = freezed,
    Object? lastUpdated = freezed,
  }) {
    return _then(
      _value.copyWith(
            commitments:
                null == commitments
                    ? _value.commitments
                    : commitments // ignore: cast_nullable_to_non_nullable
                        as List<FundingStatus>,
            timeline:
                null == timeline
                    ? _value.timeline
                    : timeline // ignore: cast_nullable_to_non_nullable
                        as List<FundingTimelineEvent>,
            aggregation:
                freezed == aggregation
                    ? _value.aggregation
                    : aggregation // ignore: cast_nullable_to_non_nullable
                        as FundingAggregation?,
            isLoading:
                null == isLoading
                    ? _value.isLoading
                    : isLoading // ignore: cast_nullable_to_non_nullable
                        as bool,
            isRefreshing:
                null == isRefreshing
                    ? _value.isRefreshing
                    : isRefreshing // ignore: cast_nullable_to_non_nullable
                        as bool,
            error:
                freezed == error
                    ? _value.error
                    : error // ignore: cast_nullable_to_non_nullable
                        as String?,
            lastUpdated:
                freezed == lastUpdated
                    ? _value.lastUpdated
                    : lastUpdated // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$FundingStateImplCopyWith<$Res>
    implements $FundingStateCopyWith<$Res> {
  factory _$$FundingStateImplCopyWith(
    _$FundingStateImpl value,
    $Res Function(_$FundingStateImpl) then,
  ) = __$$FundingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<FundingStatus> commitments,
    List<FundingTimelineEvent> timeline,
    FundingAggregation? aggregation,
    bool isLoading,
    bool isRefreshing,
    String? error,
    DateTime? lastUpdated,
  });
}

/// @nodoc
class __$$FundingStateImplCopyWithImpl<$Res>
    extends _$FundingStateCopyWithImpl<$Res, _$FundingStateImpl>
    implements _$$FundingStateImplCopyWith<$Res> {
  __$$FundingStateImplCopyWithImpl(
    _$FundingStateImpl _value,
    $Res Function(_$FundingStateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of FundingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? commitments = null,
    Object? timeline = null,
    Object? aggregation = freezed,
    Object? isLoading = null,
    Object? isRefreshing = null,
    Object? error = freezed,
    Object? lastUpdated = freezed,
  }) {
    return _then(
      _$FundingStateImpl(
        commitments:
            null == commitments
                ? _value._commitments
                : commitments // ignore: cast_nullable_to_non_nullable
                    as List<FundingStatus>,
        timeline:
            null == timeline
                ? _value._timeline
                : timeline // ignore: cast_nullable_to_non_nullable
                    as List<FundingTimelineEvent>,
        aggregation:
            freezed == aggregation
                ? _value.aggregation
                : aggregation // ignore: cast_nullable_to_non_nullable
                    as FundingAggregation?,
        isLoading:
            null == isLoading
                ? _value.isLoading
                : isLoading // ignore: cast_nullable_to_non_nullable
                    as bool,
        isRefreshing:
            null == isRefreshing
                ? _value.isRefreshing
                : isRefreshing // ignore: cast_nullable_to_non_nullable
                    as bool,
        error:
            freezed == error
                ? _value.error
                : error // ignore: cast_nullable_to_non_nullable
                    as String?,
        lastUpdated:
            freezed == lastUpdated
                ? _value.lastUpdated
                : lastUpdated // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
      ),
    );
  }
}

/// @nodoc

class _$FundingStateImpl implements _FundingState {
  const _$FundingStateImpl({
    final List<FundingStatus> commitments = const [],
    final List<FundingTimelineEvent> timeline = const [],
    this.aggregation,
    this.isLoading = false,
    this.isRefreshing = false,
    this.error,
    this.lastUpdated,
  }) : _commitments = commitments,
       _timeline = timeline;

  final List<FundingStatus> _commitments;
  @override
  @JsonKey()
  List<FundingStatus> get commitments {
    if (_commitments is EqualUnmodifiableListView) return _commitments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_commitments);
  }

  final List<FundingTimelineEvent> _timeline;
  @override
  @JsonKey()
  List<FundingTimelineEvent> get timeline {
    if (_timeline is EqualUnmodifiableListView) return _timeline;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_timeline);
  }

  @override
  final FundingAggregation? aggregation;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isRefreshing;
  @override
  final String? error;
  @override
  final DateTime? lastUpdated;

  @override
  String toString() {
    return 'FundingState(commitments: $commitments, timeline: $timeline, aggregation: $aggregation, isLoading: $isLoading, isRefreshing: $isRefreshing, error: $error, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FundingStateImpl &&
            const DeepCollectionEquality().equals(
              other._commitments,
              _commitments,
            ) &&
            const DeepCollectionEquality().equals(other._timeline, _timeline) &&
            (identical(other.aggregation, aggregation) ||
                other.aggregation == aggregation) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isRefreshing, isRefreshing) ||
                other.isRefreshing == isRefreshing) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_commitments),
    const DeepCollectionEquality().hash(_timeline),
    aggregation,
    isLoading,
    isRefreshing,
    error,
    lastUpdated,
  );

  /// Create a copy of FundingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FundingStateImplCopyWith<_$FundingStateImpl> get copyWith =>
      __$$FundingStateImplCopyWithImpl<_$FundingStateImpl>(this, _$identity);
}

abstract class _FundingState implements FundingState {
  const factory _FundingState({
    final List<FundingStatus> commitments,
    final List<FundingTimelineEvent> timeline,
    final FundingAggregation? aggregation,
    final bool isLoading,
    final bool isRefreshing,
    final String? error,
    final DateTime? lastUpdated,
  }) = _$FundingStateImpl;

  @override
  List<FundingStatus> get commitments;
  @override
  List<FundingTimelineEvent> get timeline;
  @override
  FundingAggregation? get aggregation;
  @override
  bool get isLoading;
  @override
  bool get isRefreshing;
  @override
  String? get error;
  @override
  DateTime? get lastUpdated;

  /// Create a copy of FundingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FundingStateImplCopyWith<_$FundingStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
