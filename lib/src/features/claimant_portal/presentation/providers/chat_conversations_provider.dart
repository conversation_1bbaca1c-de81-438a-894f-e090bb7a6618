import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/chat_conversation_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/repositories/chat_repository.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/chat_service.dart';

part 'chat_conversations_provider.freezed.dart';

/// State for chat conversations
@freezed
class ChatConversationsState with _$ChatConversationsState {
  const factory ChatConversationsState({
    @Default([]) List<ChatConversationModel> conversations,
    @Default(false) bool isLoading,
    @Default(false) bool isRefreshing,
    String? error,
    String? searchQuery,
  }) = _ChatConversationsState;
}

/// Provider for managing chat conversations
class ChatConversationsNotifier extends StateNotifier<ChatConversationsState> {
  final ChatRepository _chatRepository;

  ChatConversationsNotifier(this._chatRepository)
    : super(const ChatConversationsState()) {
    _init();
  }

  /// Initialize conversations
  Future<void> _init() async {
    await loadConversations();
    _subscribeToUpdates();
  }

  /// Load conversations
  Future<void> loadConversations({bool forceRefresh = false}) async {
    try {
      if (!forceRefresh && state.conversations.isNotEmpty) {
        return;
      }

      state = state.copyWith(
        isLoading: state.conversations.isEmpty,
        isRefreshing: state.conversations.isNotEmpty,
        error: null,
      );

      final conversations = await _chatRepository.getConversations(
        forceRefresh: forceRefresh,
      );

      state = state.copyWith(
        conversations: conversations,
        isLoading: false,
        isRefreshing: false,
      );

      LoggerService.info('Loaded ${conversations.length} conversations');
    } catch (e) {
      LoggerService.error('Error loading conversations', e);
      state = state.copyWith(
        isLoading: false,
        isRefreshing: false,
        error: e.toString(),
      );
    }
  }

  /// Refresh conversations
  Future<void> refreshConversations() async {
    await loadConversations(forceRefresh: true);
  }

  /// Search conversations
  void searchConversations(String query) {
    state = state.copyWith(searchQuery: query);
  }

  /// Get filtered conversations based on search query
  List<ChatConversationModel> get filteredConversations {
    final query = state.searchQuery?.toLowerCase().trim();
    if (query == null || query.isEmpty) {
      return state.conversations;
    }

    return state.conversations.where((conversation) {
      final title = conversation.title?.toLowerCase() ?? '';
      final lastMessage =
          conversation.lastMessage?.messageContent.toLowerCase() ?? '';
      final claimId = conversation.claimId.toLowerCase();

      return title.contains(query) ||
          lastMessage.contains(query) ||
          claimId.contains(query);
    }).toList();
  }

  /// Subscribe to real-time updates
  void _subscribeToUpdates() {
    _chatRepository.conversationsStream.listen(
      (conversations) {
        if (mounted) {
          state = state.copyWith(conversations: conversations);
        }
      },
      onError: (error) {
        LoggerService.error('Error in conversations stream', error);
        if (mounted) {
          state = state.copyWith(error: error.toString());
        }
      },
    );
  }

  /// Create a new conversation for a claim
  Future<ChatConversationModel?> createConversation({
    required String claimId,
    String? title,
  }) async {
    try {
      LoggerService.info('Creating conversation for claim: $claimId');

      final conversation = await _chatRepository.createConversation(
        claimId: claimId,
        title: title,
      );

      LoggerService.info(
        'Conversation created successfully: ${conversation.id}',
      );
      return conversation;
    } catch (e) {
      LoggerService.error('Error creating conversation', e);
      state = state.copyWith(error: e.toString());
      return null;
    }
  }

  @override
  void dispose() {
    _chatRepository.dispose();
    super.dispose();
  }
}

/// Provider instance
final chatConversationsProvider =
    StateNotifierProvider<ChatConversationsNotifier, ChatConversationsState>((
      ref,
    ) {
      final chatRepository = ref.watch(chatRepositoryProvider);
      return ChatConversationsNotifier(chatRepository);
    });

/// Chat service provider
final chatServiceProvider = Provider<ChatService>((ref) {
  return ChatService();
});

/// Chat repository provider
final chatRepositoryProvider = Provider<ChatRepository>((ref) {
  final chatService = ref.watch(chatServiceProvider);
  return ChatRepository(chatService);
});
