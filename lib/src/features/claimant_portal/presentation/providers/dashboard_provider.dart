import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/claimant_base_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/data/models/enhanced_notification_model.dart';

/// Dashboard statistics for claimants
class ClaimantDashboardStats {
  final int totalClaims;
  final int activeClaims;
  final int unreadNotifications;
  final String primaryClaimStatus;
  final String fundingStatus;
  final List<EnhancedNotificationModel> recentNotifications;
  final List<RecordModel> associatedClaims;

  const ClaimantDashboardStats({
    this.totalClaims = 0,
    this.activeClaims = 0,
    this.unreadNotifications = 0,
    this.primaryClaimStatus = 'No Claims',
    this.fundingStatus = 'Not Applicable',
    this.recentNotifications = const [],
    this.associatedClaims = const [],
  });

  ClaimantDashboardStats copyWith({
    int? totalClaims,
    int? activeClaims,
    int? unreadNotifications,
    String? primaryClaimStatus,
    String? fundingStatus,
    List<EnhancedNotificationModel>? recentNotifications,
    List<RecordModel>? associatedClaims,
  }) {
    return ClaimantDashboardStats(
      totalClaims: totalClaims ?? this.totalClaims,
      activeClaims: activeClaims ?? this.activeClaims,
      unreadNotifications: unreadNotifications ?? this.unreadNotifications,
      primaryClaimStatus: primaryClaimStatus ?? this.primaryClaimStatus,
      fundingStatus: fundingStatus ?? this.fundingStatus,
      recentNotifications: recentNotifications ?? this.recentNotifications,
      associatedClaims: associatedClaims ?? this.associatedClaims,
    );
  }
}

/// Dashboard state for claimants
class ClaimantDashboardState {
  final bool isLoading;
  final bool isRefreshing;
  final ClaimantDashboardStats stats;
  final ClaimantProfile? profile;
  final String? error;
  final DateTime? lastUpdated;

  const ClaimantDashboardState({
    this.isLoading = false,
    this.isRefreshing = false,
    this.stats = const ClaimantDashboardStats(),
    this.profile,
    this.error,
    this.lastUpdated,
  });

  ClaimantDashboardState copyWith({
    bool? isLoading,
    bool? isRefreshing,
    ClaimantDashboardStats? stats,
    ClaimantProfile? profile,
    String? error,
    DateTime? lastUpdated,
  }) {
    return ClaimantDashboardState(
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      stats: stats ?? this.stats,
      profile: profile ?? this.profile,
      error: error ?? this.error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  bool get hasData => profile != null;
  bool get hasError => error != null;
  bool get hasClaims => stats.totalClaims > 0;
}

/// Claimant dashboard provider
class ClaimantDashboardNotifier extends StateNotifier<ClaimantDashboardState> {
  final ClaimantBaseService _service;

  ClaimantDashboardNotifier(this._service)
    : super(const ClaimantDashboardState());

  /// Initialize dashboard data
  Future<void> initialize() async {
    if (state.isLoading) return;

    try {
      state = state.copyWith(isLoading: true, error: null);

      await _loadDashboardData();

      state = state.copyWith(isLoading: false, lastUpdated: DateTime.now());
    } catch (e) {
      LoggerService.error('Error initializing claimant dashboard', e);
      state = state.copyWith(
        isLoading: false,
        error: _service.getErrorMessage(e),
      );
    }
  }

  /// Refresh dashboard data
  Future<void> refresh() async {
    if (state.isRefreshing) return;

    try {
      state = state.copyWith(isRefreshing: true, error: null);

      await _loadDashboardData();

      state = state.copyWith(isRefreshing: false, lastUpdated: DateTime.now());
    } catch (e) {
      LoggerService.error('Error refreshing claimant dashboard', e);
      state = state.copyWith(
        isRefreshing: false,
        error: _service.getErrorMessage(e),
      );
    }
  }

  /// Load all dashboard data
  Future<void> _loadDashboardData() async {
    try {
      // Load profile
      LoggerService.info('Loading claimant profile...');
      final profile = await _service.getCurrentClaimantProfile();

      if (profile == null) {
        LoggerService.warning('No claimant profile found for current user');
        state = state.copyWith(
          profile: null,
          stats: const ClaimantDashboardStats(),
        );
        return;
      }

      LoggerService.info(
        'Claimant profile loaded: ${profile.id}, associated claims: ${profile.associatedClaimIds.length}',
      );

      // Load associated claims
      LoggerService.info('Loading associated funding applications...');
      final claims = await _service.getAssociatedFundingApplications();
      LoggerService.info('Loaded ${claims.length} funding applications');

      // Load notifications
      LoggerService.info('Loading notifications...');
      final notifications = await _service.getClaimantNotifications(perPage: 5);
      final unreadNotifications = await _service.getClaimantNotifications(
        perPage: 50,
        unreadOnly: true,
      );
      LoggerService.info(
        'Loaded ${notifications.length} recent notifications, ${unreadNotifications.length} unread',
      );

      // Calculate stats
      final stats = _calculateStats(claims, notifications, unreadNotifications);
      LoggerService.info(
        'Dashboard stats calculated - Total: ${stats.totalClaims}, Active: ${stats.activeClaims}, Unread: ${stats.unreadNotifications}',
      );

      state = state.copyWith(profile: profile, stats: stats);
    } catch (e) {
      LoggerService.error('Error in _loadDashboardData', e);
      rethrow;
    }
  }

  /// Calculate dashboard statistics
  ClaimantDashboardStats _calculateStats(
    List<RecordModel> claims,
    List<EnhancedNotificationModel> notifications,
    List<EnhancedNotificationModel> unreadNotifications,
  ) {
    final totalClaims = claims.length;

    // Calculate active claims based on application_status field
    // Active claims are those NOT in 'completed', 'cancelled', or 'rejected' status
    final activeClaims =
        claims.where((claim) {
          final status = claim.data['application_status'] as String?;
          return status != null &&
              status.toLowerCase() != 'completed' &&
              status.toLowerCase() != 'cancelled' &&
              status.toLowerCase() != 'rejected';
        }).length;

    // Get primary claim status (most recent active claim or first claim)
    String primaryClaimStatus = 'No Claims';
    String fundingStatus = 'Not Applicable';

    if (claims.isNotEmpty) {
      // Find the most recent active claim, or fall back to the first claim
      RecordModel? primaryClaim;

      // Try to find an active claim first
      final activeClaim =
          claims.where((claim) {
            final status = claim.data['application_status'] as String?;
            return status != null &&
                status.toLowerCase() != 'completed' &&
                status.toLowerCase() != 'cancelled' &&
                status.toLowerCase() != 'rejected';
          }).toList();

      if (activeClaim.isNotEmpty) {
        // Sort by updated date and get the most recent
        activeClaim.sort((a, b) {
          final aUpdated =
              DateTime.tryParse(a.data['updated'] as String? ?? '') ??
              DateTime(1970);
          final bUpdated =
              DateTime.tryParse(b.data['updated'] as String? ?? '') ??
              DateTime(1970);
          return bUpdated.compareTo(aUpdated);
        });
        primaryClaim = activeClaim.first;
      } else {
        // No active claims, use the most recent claim
        final sortedClaims = List<RecordModel>.from(claims);
        sortedClaims.sort((a, b) {
          final aUpdated =
              DateTime.tryParse(a.data['updated'] as String? ?? '') ??
              DateTime(1970);
          final bUpdated =
              DateTime.tryParse(b.data['updated'] as String? ?? '') ??
              DateTime(1970);
          return bUpdated.compareTo(aUpdated);
        });
        primaryClaim = sortedClaims.first;
      }

      primaryClaimStatus = _formatStage(primaryClaim.data['stage'] as String?);
      fundingStatus = _formatApplicationStatus(
        primaryClaim.data['application_status'] as String?,
      );
    }

    return ClaimantDashboardStats(
      totalClaims: totalClaims,
      activeClaims: activeClaims,
      unreadNotifications: unreadNotifications.length,
      primaryClaimStatus: primaryClaimStatus,
      fundingStatus: fundingStatus,
      recentNotifications: notifications.take(3).toList(),
      associatedClaims: claims,
    );
  }

  /// Format stage for display
  String _formatStage(String? stage) {
    if (stage == null) return 'Unknown';

    switch (stage) {
      case 'STAGE 1: PRE ACTION':
        return 'Pre-Action';
      case 'STAGE 2: PROCEEDINGS ISSUED':
        return 'Proceedings Issued';
      case 'STAGE 3: DISCLOSURE':
        return 'Disclosure';
      case 'STAGE 4: TRIAL':
        return 'Trial';
      case 'STAGE 5: SETTLEMENT':
        return 'Settlement';
      default:
        return stage.replaceAll('STAGE ', '').replaceAll(':', '');
    }
  }

  /// Format application status for display (used for funding status)
  String _formatApplicationStatus(String? status) {
    if (status == null) return 'Not Applicable';

    switch (status.toLowerCase()) {
      case 'draft':
        return 'Draft Application';
      case 'submitted':
        return 'Under Review';
      case 'approved_for_funding':
        return 'Funding Approved';
      case 'pending_review':
        return 'Pending Review';
      case 'requires_more_info':
        return 'More Info Required';
      case 'rejected':
        return 'Application Rejected';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status
            .replaceAll('_', ' ')
            .split(' ')
            .map(
              (word) =>
                  word.isNotEmpty
                      ? word[0].toUpperCase() + word.substring(1)
                      : word,
            )
            .join(' ');
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Mark notification as read
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _service.markNotificationAsRead(notificationId);
      // Refresh to update unread count
      await refresh();
    } catch (e) {
      LoggerService.error('Error marking notification as read', e);
    }
  }
}

/// Provider for claimant dashboard
final claimantDashboardProvider =
    StateNotifierProvider<ClaimantDashboardNotifier, ClaimantDashboardState>((
      ref,
    ) {
      final service = ClaimantBaseService();
      return ClaimantDashboardNotifier(service);
    });

/// Provider for dashboard stats only
final claimantDashboardStatsProvider = Provider<ClaimantDashboardStats>((ref) {
  final dashboardState = ref.watch(claimantDashboardProvider);
  return dashboardState.stats;
});

/// Provider for checking if dashboard has data
final claimantDashboardHasDataProvider = Provider<bool>((ref) {
  final dashboardState = ref.watch(claimantDashboardProvider);
  return dashboardState.hasData;
});
