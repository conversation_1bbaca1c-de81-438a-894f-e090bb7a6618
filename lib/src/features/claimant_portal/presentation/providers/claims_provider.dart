import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/claims_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/repositories/claims_repository.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_claim_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claim_status_model.dart';

/// Provider for ClaimsService
final claimsServiceProvider = Provider<ClaimsService>((ref) {
  return ClaimsService();
});

/// Provider for ClaimsRepository
final claimsRepositoryProvider = Provider<ClaimsRepository>((ref) {
  final claimsService = ref.watch(claimsServiceProvider);
  return ClaimsRepository(claimsService);
});

/// State for claims management
class ClaimsState {
  final List<ClaimantClaim> claims;
  final bool isLoading;
  final bool isRefreshing;
  final String? error;
  final DateTime? lastUpdated;
  final Map<String, int> statistics;

  const ClaimsState({
    this.claims = const [],
    this.isLoading = false,
    this.isRefreshing = false,
    this.error,
    this.lastUpdated,
    this.statistics = const {},
  });

  ClaimsState copyWith({
    List<ClaimantClaim>? claims,
    bool? isLoading,
    bool? isRefreshing,
    String? error,
    DateTime? lastUpdated,
    Map<String, int>? statistics,
  }) {
    return ClaimsState(
      claims: claims ?? this.claims,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      error: error ?? this.error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      statistics: statistics ?? this.statistics,
    );
  }

  bool get hasClaims => claims.isNotEmpty;
  bool get hasError => error != null;
  List<ClaimantClaim> get activeClaims => claims.where((claim) => claim.isActive).toList();
  List<ClaimantClaim> get fundedClaims => claims.where((claim) => claim.isFunded).toList();
}

/// Claims state notifier
class ClaimsNotifier extends StateNotifier<ClaimsState> {
  final ClaimsRepository _repository;

  ClaimsNotifier(this._repository) : super(const ClaimsState()) {
    _setupStreams();
  }

  /// Setup streams from repository
  void _setupStreams() {
    // Listen to claims stream
    _repository.claimsStream.listen(
      (claims) {
        LoggerService.info('Received ${claims.length} claims from stream');
        state = state.copyWith(
          claims: claims,
          lastUpdated: DateTime.now(),
          error: null,
        );
        _updateStatistics();
      },
      onError: (error) {
        LoggerService.error('Error in claims stream', error);
        state = state.copyWith(error: error.toString());
      },
    );

    // Listen to individual claim updates
    _repository.claimUpdatesStream.listen(
      (updatedClaim) {
        LoggerService.info('Received claim update: ${updatedClaim.title}');
        final updatedClaims = state.claims.map((claim) {
          return claim.id == updatedClaim.id ? updatedClaim : claim;
        }).toList();
        
        state = state.copyWith(
          claims: updatedClaims,
          lastUpdated: DateTime.now(),
        );
        _updateStatistics();
      },
      onError: (error) {
        LoggerService.error('Error in claim updates stream', error);
      },
    );
  }

  /// Initialize claims data
  Future<void> initialize() async {
    if (state.isLoading) return;

    try {
      state = state.copyWith(isLoading: true, error: null);
      
      // Subscribe to real-time updates
      await _repository.subscribeToUpdates();
      
      // Load initial data
      await loadClaims();
      
      state = state.copyWith(isLoading: false);
    } catch (e) {
      LoggerService.error('Error initializing claims', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Load claims data
  Future<void> loadClaims({bool forceRefresh = false}) async {
    try {
      if (!forceRefresh && state.isRefreshing) return;
      
      if (forceRefresh) {
        state = state.copyWith(isRefreshing: true, error: null);
      }

      final claims = await _repository.getClaims(forceRefresh: forceRefresh);
      
      state = state.copyWith(
        claims: claims,
        isRefreshing: false,
        lastUpdated: DateTime.now(),
        error: null,
      );
      
      await _updateStatistics();
    } catch (e) {
      LoggerService.error('Error loading claims', e);
      state = state.copyWith(
        isRefreshing: false,
        error: e.toString(),
      );
    }
  }

  /// Refresh claims data
  Future<void> refresh() async {
    return loadClaims(forceRefresh: true);
  }

  /// Get claim by ID
  Future<ClaimantClaim?> getClaimById(String claimId) async {
    try {
      return await _repository.getClaimById(claimId);
    } catch (e) {
      LoggerService.error('Error getting claim by ID: $claimId', e);
      state = state.copyWith(error: e.toString());
      return null;
    }
  }

  /// Filter claims by status
  List<ClaimantClaim> getClaimsByStatus(ClaimStatus status) {
    return state.claims.where((claim) => claim.status == status).toList();
  }

  /// Search claims
  Future<List<ClaimantClaim>> searchClaims(String query) async {
    try {
      return await _repository.searchClaims(query);
    } catch (e) {
      LoggerService.error('Error searching claims', e);
      state = state.copyWith(error: e.toString());
      return [];
    }
  }

  /// Get claim status history
  Future<ClaimStatusHistory?> getClaimStatusHistory(String claimId) async {
    try {
      return await _repository.getClaimStatusHistory(claimId);
    } catch (e) {
      LoggerService.error('Error getting claim status history', e);
      return null;
    }
  }

  /// Update statistics
  Future<void> _updateStatistics() async {
    try {
      final stats = await _repository.getClaimsStatistics();
      state = state.copyWith(statistics: stats);
    } catch (e) {
      LoggerService.error('Error updating statistics', e);
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clear cache
  Future<void> clearCache() async {
    try {
      await _repository.clearCache();
      LoggerService.info('Claims cache cleared');
    } catch (e) {
      LoggerService.error('Error clearing cache', e);
    }
  }

  @override
  void dispose() {
    _repository.dispose();
    super.dispose();
  }
}

/// Provider for claims state
final claimsProvider = StateNotifierProvider<ClaimsNotifier, ClaimsState>((ref) {
  final repository = ref.watch(claimsRepositoryProvider);
  return ClaimsNotifier(repository);
});

/// Provider for claims list only
final claimsListProvider = Provider<List<ClaimantClaim>>((ref) {
  final claimsState = ref.watch(claimsProvider);
  return claimsState.claims;
});

/// Provider for active claims only
final activeClaimsProvider = Provider<List<ClaimantClaim>>((ref) {
  final claimsState = ref.watch(claimsProvider);
  return claimsState.activeClaims;
});

/// Provider for funded claims only
final fundedClaimsProvider = Provider<List<ClaimantClaim>>((ref) {
  final claimsState = ref.watch(claimsProvider);
  return claimsState.fundedClaims;
});

/// Provider for claims statistics
final claimsStatisticsProvider = Provider<Map<String, int>>((ref) {
  final claimsState = ref.watch(claimsProvider);
  return claimsState.statistics;
});

/// Provider for checking if claims are loading
final claimsLoadingProvider = Provider<bool>((ref) {
  final claimsState = ref.watch(claimsProvider);
  return claimsState.isLoading;
});

/// Provider for checking if claims are refreshing
final claimsRefreshingProvider = Provider<bool>((ref) {
  final claimsState = ref.watch(claimsProvider);
  return claimsState.isRefreshing;
});

/// Provider for claims error state
final claimsErrorProvider = Provider<String?>((ref) {
  final claimsState = ref.watch(claimsProvider);
  return claimsState.error;
});

/// Provider for checking if there are claims
final hasClaimsProvider = Provider<bool>((ref) {
  final claimsState = ref.watch(claimsProvider);
  return claimsState.hasClaims;
});

/// Provider for specific claim by ID
final claimByIdProvider = Provider.family<ClaimantClaim?, String>((ref, claimId) {
  final claims = ref.watch(claimsListProvider);
  final matchingClaims = claims.where((claim) => claim.id == claimId).toList();
  return matchingClaims.isNotEmpty ? matchingClaims.first : null;
});

/// Provider for claims by status
final claimsByStatusProvider = Provider.family<List<ClaimantClaim>, ClaimStatus>((ref, status) {
  final claims = ref.watch(claimsListProvider);
  return claims.where((claim) => claim.status == status).toList();
});

/// Provider for claims count by status
final claimsCountByStatusProvider = Provider.family<int, ClaimStatus>((ref, status) {
  final claimsByStatus = ref.watch(claimsByStatusProvider(status));
  return claimsByStatus.length;
});
