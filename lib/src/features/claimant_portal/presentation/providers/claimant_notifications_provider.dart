import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/claimant_notifications_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_notification_model.dart';

/// State for claimant notifications
class ClaimantNotificationsState {
  final List<ClaimantNotificationModel> notifications;
  final bool isLoading;
  final bool isInitialized;
  final String? error;
  final int unreadCount;
  final Map<String, int> stats;
  final String selectedFilter; // 'all', 'unread', 'read'
  final ClaimantNotificationType? selectedType;

  const ClaimantNotificationsState({
    this.notifications = const [],
    this.isLoading = false,
    this.isInitialized = false,
    this.error,
    this.unreadCount = 0,
    this.stats = const {},
    this.selectedFilter = 'all',
    this.selectedType,
  });

  ClaimantNotificationsState copyWith({
    List<ClaimantNotificationModel>? notifications,
    bool? isLoading,
    bool? isInitialized,
    String? error,
    int? unreadCount,
    Map<String, int>? stats,
    String? selectedFilter,
    ClaimantNotificationType? selectedType,
    bool clearError = false,
    bool clearSelectedType = false,
  }) {
    return ClaimantNotificationsState(
      notifications: notifications ?? this.notifications,
      isLoading: isLoading ?? this.isLoading,
      isInitialized: isInitialized ?? this.isInitialized,
      error: clearError ? null : (error ?? this.error),
      unreadCount: unreadCount ?? this.unreadCount,
      stats: stats ?? this.stats,
      selectedFilter: selectedFilter ?? this.selectedFilter,
      selectedType:
          clearSelectedType ? null : (selectedType ?? this.selectedType),
    );
  }

  /// Get filtered notifications based on current filters
  List<ClaimantNotificationModel> get filteredNotifications {
    var filtered = notifications;

    // Filter by type first (if selected)
    if (selectedType != null) {
      filtered =
          filtered.where((n) => n.notificationType == selectedType).toList();
    }

    // Then filter by read status
    switch (selectedFilter) {
      case 'unread':
        filtered = filtered.where((n) => !n.isRead).toList();
        break;
      case 'read':
        filtered = filtered.where((n) => n.isRead).toList();
        break;
      case 'all':
      default:
        // No filtering by read status
        break;
    }

    return filtered;
  }

  /// Get notifications grouped by date
  Map<String, List<ClaimantNotificationModel>> get notificationsGroupedByDate {
    final filtered = filteredNotifications;
    final grouped = <String, List<ClaimantNotificationModel>>{};

    for (final notification in filtered) {
      final group = notification.dateGroup;
      grouped.putIfAbsent(group, () => []).add(notification);
    }

    // Sort groups by priority (Today, Yesterday, This Week, etc.)
    final sortedGroups = <String, List<ClaimantNotificationModel>>{};
    const groupOrder = [
      'Today',
      'Yesterday',
      'This Week',
      'This Month',
      'Older',
      'Unknown', // Include Unknown group in the standard order
    ];

    for (final group in groupOrder) {
      if (grouped.containsKey(group)) {
        sortedGroups[group] = grouped[group]!;
      }
    }

    // Add any groups not in the standard order
    for (final entry in grouped.entries) {
      if (!groupOrder.contains(entry.key)) {
        sortedGroups[entry.key] = entry.value;
      }
    }

    return sortedGroups;
  }

  bool get hasError => error != null;
  bool get hasNotifications => notifications.isNotEmpty;
  bool get hasUnreadNotifications => unreadCount > 0;
}

/// Provider for claimant notifications
final claimantNotificationsProvider = StateNotifierProvider<
  ClaimantNotificationsNotifier,
  ClaimantNotificationsState
>((ref) => ClaimantNotificationsNotifier());

/// Notifier for managing claimant notifications state
class ClaimantNotificationsNotifier
    extends StateNotifier<ClaimantNotificationsState> {
  late final ClaimantNotificationsService _notificationsService;
  bool _isDisposed = false;

  ClaimantNotificationsNotifier() : super(const ClaimantNotificationsState()) {
    _notificationsService = ClaimantNotificationsService(PocketBaseService());
    _initialize();
  }

  /// Initialize the notifications service
  Future<void> _initialize() async {
    if (_isDisposed) return;

    try {
      state = state.copyWith(isLoading: true, clearError: true);

      await _notificationsService.initialize();

      // Listen to notifications changes
      _notificationsService.notifications.addListener(_onNotificationsChanged);

      // Initial state update
      _onNotificationsChanged();

      if (!_isDisposed) {
        state = state.copyWith(isLoading: false, isInitialized: true);
      }

      LoggerService.info(
        'Claimant notifications provider initialized successfully',
      );
    } catch (e) {
      LoggerService.error(
        'Error initializing claimant notifications provider',
        e,
      );

      if (!_isDisposed) {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to initialize notifications: ${e.toString()}',
        );
      }
    }
  }

  /// Handle notifications changes from the service
  void _onNotificationsChanged() {
    if (_isDisposed) return;

    try {
      final notifications = _notificationsService.notifications.value;
      final unreadCount = _notificationsService.unreadCount;
      final stats = _notificationsService.getNotificationStats();

      state = state.copyWith(
        notifications: notifications,
        unreadCount: unreadCount,
        stats: stats,
        clearError: true,
      );

      LoggerService.info(
        'Updated notifications state: ${notifications.length} total, $unreadCount unread',
      );
    } catch (e) {
      LoggerService.error('Error updating notifications state', e);

      if (!_isDisposed) {
        state = state.copyWith(
          error: 'Failed to update notifications: ${e.toString()}',
        );
      }
    }
  }

  /// Mark a notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _notificationsService.markNotificationAsRead(notificationId);
      LoggerService.info('Notification $notificationId marked as read');
    } catch (e) {
      LoggerService.error('Error marking notification as read', e);

      if (!_isDisposed) {
        state = state.copyWith(
          error: 'Failed to mark notification as read: ${e.toString()}',
        );
      }
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      state = state.copyWith(isLoading: true);

      await _notificationsService.markAllAsRead();

      if (!_isDisposed) {
        state = state.copyWith(isLoading: false);
      }

      LoggerService.info('All notifications marked as read');
    } catch (e) {
      LoggerService.error('Error marking all notifications as read', e);

      if (!_isDisposed) {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to mark all notifications as read: ${e.toString()}',
        );
      }
    }
  }

  /// Set filter for notifications
  void setFilter(String filter) {
    if (!_isDisposed) {
      state = state.copyWith(selectedFilter: filter);
      LoggerService.info('Notifications filter set to: $filter');
    }
  }

  /// Set type filter for notifications
  void setTypeFilter(ClaimantNotificationType? type) {
    if (!_isDisposed) {
      state = state.copyWith(
        selectedType: type,
        clearSelectedType: type == null,
      );
      LoggerService.info(
        'Notifications type filter set to: ${type?.value ?? 'none'}',
      );
    }
  }

  /// Clear error state
  void clearError() {
    if (!_isDisposed) {
      state = state.copyWith(clearError: true);
    }
  }

  /// Refresh notifications
  Future<void> refresh() async {
    if (!_isDisposed && state.isInitialized) {
      // The service automatically updates via real-time subscriptions
      // This method is here for manual refresh if needed
      LoggerService.info(
        'Refreshing notifications (handled by real-time updates)',
      );
    }
  }

  /// Get notifications for a specific claim
  List<ClaimantNotificationModel> getNotificationsForClaim(String claimId) {
    return state.notifications.where((n) => n.claimId == claimId).toList();
  }

  /// Get unread count for a specific claim
  int getUnreadCountForClaim(String claimId) {
    return state.notifications
        .where((n) => !n.isRead && n.claimId == claimId)
        .length;
  }

  @override
  void dispose() {
    _isDisposed = true;
    try {
      _notificationsService.notifications.removeListener(
        _onNotificationsChanged,
      );
      _notificationsService.dispose();
    } catch (e) {
      LoggerService.error('Error disposing claimant notifications provider', e);
    }
    super.dispose();
  }
}
