import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import '../../data/models/claimant_claim_model.dart';
import '../../data/models/claim_status_model.dart';
import '../../data/repositories/claims_repository.dart';
import 'claims_provider.dart';

/// State for claim detail view
class ClaimDetailState {
  final ClaimantClaim? claim;
  final ClaimStatusHistory? statusHistory;
  final List<ClaimDocument> documents;
  final bool isLoading;
  final bool isRefreshing;
  final String? error;
  final DateTime? lastUpdated;
  final bool hasRealTimeUpdates;

  const ClaimDetailState({
    this.claim,
    this.statusHistory,
    this.documents = const [],
    this.isLoading = false,
    this.isRefreshing = false,
    this.error,
    this.lastUpdated,
    this.hasRealTimeUpdates = false,
  });

  ClaimDetailState copyWith({
    ClaimantClaim? claim,
    ClaimStatusHistory? statusHistory,
    List<ClaimDocument>? documents,
    bool? isLoading,
    bool? isRefreshing,
    String? error,
    DateTime? lastUpdated,
    bool? hasRealTimeUpdates,
  }) {
    return ClaimDetailState(
      claim: claim ?? this.claim,
      statusHistory: statusHistory ?? this.statusHistory,
      documents: documents ?? this.documents,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      hasRealTimeUpdates: hasRealTimeUpdates ?? this.hasRealTimeUpdates,
    );
  }

  bool get hasData => claim != null;
  bool get isInitialized => hasData || error != null;
}

/// Document model for claim documents
class ClaimDocument {
  final String id;
  final String name;
  final String type;
  final String url;
  final DateTime uploadDate;
  final String uploadedBy;
  final int size;
  final bool isAccessible;

  const ClaimDocument({
    required this.id,
    required this.name,
    required this.type,
    required this.url,
    required this.uploadDate,
    required this.uploadedBy,
    required this.size,
    this.isAccessible = true,
  });

  factory ClaimDocument.fromJson(Map<String, dynamic> json) {
    return ClaimDocument(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      url: json['url'] ?? '',
      uploadDate: DateTime.tryParse(json['upload_date'] ?? '') ?? DateTime.now(),
      uploadedBy: json['uploaded_by'] ?? '',
      size: json['size'] ?? 0,
      isAccessible: json['is_accessible'] ?? true,
    );
  }

  String get formattedSize {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  String get fileExtension {
    final parts = name.split('.');
    return parts.length > 1 ? parts.last.toUpperCase() : 'FILE';
  }
}

/// Provider for claim detail state management
class ClaimDetailNotifier extends StateNotifier<ClaimDetailState> {
  final ClaimsRepository _repository;
  final String _claimId;
  StreamSubscription? _claimUpdateSubscription;

  ClaimDetailNotifier(this._repository, this._claimId) : super(const ClaimDetailState());

  /// Initialize claim detail view
  Future<void> initialize() async {
    if (state.isLoading) return;

    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await _loadClaimDetails();
      await _loadStatusHistory();
      await _loadDocuments();
      await _setupRealTimeUpdates();
      
      state = state.copyWith(
        isLoading: false,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      LoggerService.error('Error initializing claim detail for $_claimId', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Load claim details
  Future<void> _loadClaimDetails() async {
    try {
      final claim = await _repository.getClaimById(_claimId);
      if (claim == null) {
        throw Exception('Claim not found or access denied');
      }
      
      state = state.copyWith(claim: claim);
      LoggerService.info('Loaded claim details for: ${claim.title}');
    } catch (e) {
      LoggerService.error('Error loading claim details', e);
      rethrow;
    }
  }

  /// Load status history
  Future<void> _loadStatusHistory() async {
    try {
      // For now, create a simple history from current status
      // In the future, this could fetch from a dedicated status_history collection
      if (state.claim != null) {
        final currentStatusModel = ClaimStatusModel.fromStatus(
          id: '${_claimId}_current',
          claimId: _claimId,
          status: state.claim!.status.value,
          timestamp: state.claim!.lastUpdated ?? state.claim!.created,
        );

        final history = ClaimStatusHistory(
          claimId: _claimId,
          statusHistory: [currentStatusModel],
          currentStatus: currentStatusModel,
        );

        state = state.copyWith(statusHistory: history);
        LoggerService.info('Loaded status history for claim $_claimId');
      }
    } catch (e) {
      LoggerService.error('Error loading status history', e);
      // Don't rethrow - status history is not critical
    }
  }

  /// Load claim documents
  Future<void> _loadDocuments() async {
    try {
      // TODO: Implement document loading from claim_documents collection
      // For now, return empty list
      final documents = <ClaimDocument>[];
      
      state = state.copyWith(documents: documents);
      LoggerService.info('Loaded ${documents.length} documents for claim $_claimId');
    } catch (e) {
      LoggerService.error('Error loading documents', e);
      // Don't rethrow - documents are not critical for initial load
    }
  }

  /// Setup real-time updates
  Future<void> _setupRealTimeUpdates() async {
    try {
      // Subscribe to claim updates through the claims service
      // This will be handled by the existing claims provider
      state = state.copyWith(hasRealTimeUpdates: true);
      LoggerService.info('Set up real-time updates for claim $_claimId');
    } catch (e) {
      LoggerService.error('Error setting up real-time updates', e);
      // Don't rethrow - real-time updates are not critical
    }
  }

  /// Refresh all claim data
  Future<void> refresh() async {
    if (state.isRefreshing) return;

    try {
      state = state.copyWith(isRefreshing: true, error: null);
      
      await _loadClaimDetails();
      await _loadStatusHistory();
      await _loadDocuments();
      
      state = state.copyWith(
        isRefreshing: false,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      LoggerService.error('Error refreshing claim detail', e);
      state = state.copyWith(
        isRefreshing: false,
        error: e.toString(),
      );
    }
  }

  /// Download document
  Future<void> downloadDocument(ClaimDocument document) async {
    try {
      LoggerService.info('Downloading document: ${document.name}');
      // TODO: Implement document download functionality
      // This would integrate with the document service
    } catch (e) {
      LoggerService.error('Error downloading document: ${document.name}', e);
      rethrow;
    }
  }

  @override
  void dispose() {
    _claimUpdateSubscription?.cancel();
    super.dispose();
  }
}

/// Provider for claim detail state
final claimDetailProvider = StateNotifierProvider.family<ClaimDetailNotifier, ClaimDetailState, String>((ref, claimId) {
  final repository = ref.watch(claimsRepositoryProvider);
  return ClaimDetailNotifier(repository, claimId);
});

/// Provider for claim detail data only
final claimDetailDataProvider = Provider.family<ClaimantClaim?, String>((ref, claimId) {
  final detailState = ref.watch(claimDetailProvider(claimId));
  return detailState.claim;
});

/// Provider for claim documents
final claimDocumentsProvider = Provider.family<List<ClaimDocument>, String>((ref, claimId) {
  final detailState = ref.watch(claimDetailProvider(claimId));
  return detailState.documents;
});

/// Provider for claim status history
final claimStatusHistoryProvider = Provider.family<ClaimStatusHistory?, String>((ref, claimId) {
  final detailState = ref.watch(claimDetailProvider(claimId));
  return detailState.statusHistory;
});
