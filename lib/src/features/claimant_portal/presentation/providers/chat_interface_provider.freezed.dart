// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_interface_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$ChatInterfaceState {
  List<ChatMessageModel> get messages => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isSending => throw _privateConstructorUsedError;
  bool get isTyping => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  String? get claimId => throw _privateConstructorUsedError;
  String? get claimTitle => throw _privateConstructorUsedError;

  /// Create a copy of ChatInterfaceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatInterfaceStateCopyWith<ChatInterfaceState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatInterfaceStateCopyWith<$Res> {
  factory $ChatInterfaceStateCopyWith(
    ChatInterfaceState value,
    $Res Function(ChatInterfaceState) then,
  ) = _$ChatInterfaceStateCopyWithImpl<$Res, ChatInterfaceState>;
  @useResult
  $Res call({
    List<ChatMessageModel> messages,
    bool isLoading,
    bool isSending,
    bool isTyping,
    String? error,
    String? claimId,
    String? claimTitle,
  });
}

/// @nodoc
class _$ChatInterfaceStateCopyWithImpl<$Res, $Val extends ChatInterfaceState>
    implements $ChatInterfaceStateCopyWith<$Res> {
  _$ChatInterfaceStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatInterfaceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messages = null,
    Object? isLoading = null,
    Object? isSending = null,
    Object? isTyping = null,
    Object? error = freezed,
    Object? claimId = freezed,
    Object? claimTitle = freezed,
  }) {
    return _then(
      _value.copyWith(
            messages:
                null == messages
                    ? _value.messages
                    : messages // ignore: cast_nullable_to_non_nullable
                        as List<ChatMessageModel>,
            isLoading:
                null == isLoading
                    ? _value.isLoading
                    : isLoading // ignore: cast_nullable_to_non_nullable
                        as bool,
            isSending:
                null == isSending
                    ? _value.isSending
                    : isSending // ignore: cast_nullable_to_non_nullable
                        as bool,
            isTyping:
                null == isTyping
                    ? _value.isTyping
                    : isTyping // ignore: cast_nullable_to_non_nullable
                        as bool,
            error:
                freezed == error
                    ? _value.error
                    : error // ignore: cast_nullable_to_non_nullable
                        as String?,
            claimId:
                freezed == claimId
                    ? _value.claimId
                    : claimId // ignore: cast_nullable_to_non_nullable
                        as String?,
            claimTitle:
                freezed == claimTitle
                    ? _value.claimTitle
                    : claimTitle // ignore: cast_nullable_to_non_nullable
                        as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ChatInterfaceStateImplCopyWith<$Res>
    implements $ChatInterfaceStateCopyWith<$Res> {
  factory _$$ChatInterfaceStateImplCopyWith(
    _$ChatInterfaceStateImpl value,
    $Res Function(_$ChatInterfaceStateImpl) then,
  ) = __$$ChatInterfaceStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<ChatMessageModel> messages,
    bool isLoading,
    bool isSending,
    bool isTyping,
    String? error,
    String? claimId,
    String? claimTitle,
  });
}

/// @nodoc
class __$$ChatInterfaceStateImplCopyWithImpl<$Res>
    extends _$ChatInterfaceStateCopyWithImpl<$Res, _$ChatInterfaceStateImpl>
    implements _$$ChatInterfaceStateImplCopyWith<$Res> {
  __$$ChatInterfaceStateImplCopyWithImpl(
    _$ChatInterfaceStateImpl _value,
    $Res Function(_$ChatInterfaceStateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ChatInterfaceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messages = null,
    Object? isLoading = null,
    Object? isSending = null,
    Object? isTyping = null,
    Object? error = freezed,
    Object? claimId = freezed,
    Object? claimTitle = freezed,
  }) {
    return _then(
      _$ChatInterfaceStateImpl(
        messages:
            null == messages
                ? _value._messages
                : messages // ignore: cast_nullable_to_non_nullable
                    as List<ChatMessageModel>,
        isLoading:
            null == isLoading
                ? _value.isLoading
                : isLoading // ignore: cast_nullable_to_non_nullable
                    as bool,
        isSending:
            null == isSending
                ? _value.isSending
                : isSending // ignore: cast_nullable_to_non_nullable
                    as bool,
        isTyping:
            null == isTyping
                ? _value.isTyping
                : isTyping // ignore: cast_nullable_to_non_nullable
                    as bool,
        error:
            freezed == error
                ? _value.error
                : error // ignore: cast_nullable_to_non_nullable
                    as String?,
        claimId:
            freezed == claimId
                ? _value.claimId
                : claimId // ignore: cast_nullable_to_non_nullable
                    as String?,
        claimTitle:
            freezed == claimTitle
                ? _value.claimTitle
                : claimTitle // ignore: cast_nullable_to_non_nullable
                    as String?,
      ),
    );
  }
}

/// @nodoc

class _$ChatInterfaceStateImpl implements _ChatInterfaceState {
  const _$ChatInterfaceStateImpl({
    final List<ChatMessageModel> messages = const [],
    this.isLoading = false,
    this.isSending = false,
    this.isTyping = false,
    this.error,
    this.claimId,
    this.claimTitle,
  }) : _messages = messages;

  final List<ChatMessageModel> _messages;
  @override
  @JsonKey()
  List<ChatMessageModel> get messages {
    if (_messages is EqualUnmodifiableListView) return _messages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_messages);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isSending;
  @override
  @JsonKey()
  final bool isTyping;
  @override
  final String? error;
  @override
  final String? claimId;
  @override
  final String? claimTitle;

  @override
  String toString() {
    return 'ChatInterfaceState(messages: $messages, isLoading: $isLoading, isSending: $isSending, isTyping: $isTyping, error: $error, claimId: $claimId, claimTitle: $claimTitle)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatInterfaceStateImpl &&
            const DeepCollectionEquality().equals(other._messages, _messages) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isSending, isSending) ||
                other.isSending == isSending) &&
            (identical(other.isTyping, isTyping) ||
                other.isTyping == isTyping) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.claimId, claimId) || other.claimId == claimId) &&
            (identical(other.claimTitle, claimTitle) ||
                other.claimTitle == claimTitle));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_messages),
    isLoading,
    isSending,
    isTyping,
    error,
    claimId,
    claimTitle,
  );

  /// Create a copy of ChatInterfaceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatInterfaceStateImplCopyWith<_$ChatInterfaceStateImpl> get copyWith =>
      __$$ChatInterfaceStateImplCopyWithImpl<_$ChatInterfaceStateImpl>(
        this,
        _$identity,
      );
}

abstract class _ChatInterfaceState implements ChatInterfaceState {
  const factory _ChatInterfaceState({
    final List<ChatMessageModel> messages,
    final bool isLoading,
    final bool isSending,
    final bool isTyping,
    final String? error,
    final String? claimId,
    final String? claimTitle,
  }) = _$ChatInterfaceStateImpl;

  @override
  List<ChatMessageModel> get messages;
  @override
  bool get isLoading;
  @override
  bool get isSending;
  @override
  bool get isTyping;
  @override
  String? get error;
  @override
  String? get claimId;
  @override
  String? get claimTitle;

  /// Create a copy of ChatInterfaceState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatInterfaceStateImplCopyWith<_$ChatInterfaceStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
