import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/claimant_base_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/utils/profile_validation.dart';

/// State class for profile management
class ProfileState {
  final ClaimantProfile? profile;
  final bool isLoading;
  final bool isUpdating;
  final bool isUploadingImage;
  final String? error;
  final String? successMessage;
  final String? profilePictureUrl;

  const ProfileState({
    this.profile,
    this.isLoading = false,
    this.isUpdating = false,
    this.isUploadingImage = false,
    this.error,
    this.successMessage,
    this.profilePictureUrl,
  });

  ProfileState copyWith({
    ClaimantProfile? profile,
    bool? isLoading,
    bool? isUpdating,
    bool? isUploadingImage,
    String? error,
    String? successMessage,
    String? profilePictureUrl,
  }) {
    return ProfileState(
      profile: profile ?? this.profile,
      isLoading: isLoading ?? this.isLoading,
      isUpdating: isUpdating ?? this.isUpdating,
      isUploadingImage: isUploadingImage ?? this.isUploadingImage,
      error: error,
      successMessage: successMessage,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
    );
  }
}

/// Profile provider for managing claimant profile state and operations
class ProfileNotifier extends StateNotifier<ProfileState> {
  final ClaimantBaseService _service;

  ProfileNotifier(this._service) : super(const ProfileState());

  /// Load current profile
  Future<void> loadProfile() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final profile = await _service.getCurrentClaimantProfile();
      final profilePictureUrl = _service.getProfilePictureUrl();

      state = state.copyWith(
        isLoading: false,
        profile: profile,
        profilePictureUrl: profilePictureUrl,
      );

      LoggerService.info('Profile loaded successfully');
    } catch (e) {
      LoggerService.error('Error loading profile', e);
      state = state.copyWith(
        isLoading: false,
        error: _service.getErrorMessage(e),
      );
    }
  }

  /// Update user profile information
  Future<bool> updateProfile({
    String? name,
    String? firstName,
    String? lastName,
    String? mobile,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? postcode,
  }) async {
    state = state.copyWith(isUpdating: true, error: null, successMessage: null);

    try {
      // Validate inputs
      final nameError =
          name != null ? ProfileValidation.validateName(name) : null;
      final firstNameError =
          firstName != null
              ? ProfileValidation.validateName(
                firstName,
                fieldName: 'First name',
              )
              : null;
      final lastNameError =
          lastName != null
              ? ProfileValidation.validateName(lastName, fieldName: 'Last name')
              : null;
      final mobileError =
          mobile != null ? ProfileValidation.validatePhoneNumber(mobile) : null;
      final addressLine1Error =
          addressLine1 != null
              ? ProfileValidation.validateAddressLine(
                addressLine1,
                fieldName: 'Address line 1',
              )
              : null;
      final addressLine2Error =
          addressLine2 != null
              ? ProfileValidation.validateAddressLine(
                addressLine2,
                required: false,
                fieldName: 'Address line 2',
              )
              : null;
      final cityError =
          city != null ? ProfileValidation.validateCity(city) : null;
      final postcodeError =
          postcode != null
              ? ProfileValidation.validatePostcode(postcode)
              : null;

      final errors =
          [
            nameError,
            firstNameError,
            lastNameError,
            mobileError,
            addressLine1Error,
            addressLine2Error,
            cityError,
            postcodeError,
          ].where((error) => error != null).toList();

      if (errors.isNotEmpty) {
        state = state.copyWith(isUpdating: false, error: errors.first);
        return false;
      }

      // Update user profile
      await _service.updateUserProfile(
        name: name,
        firstName: firstName,
        lastName: lastName,
        mobile: mobile,
        addressLine1: addressLine1,
        addressLine2: addressLine2,
        city: city,
        postcode: postcode,
      );

      // Reload profile to get updated data
      await loadProfile();

      state = state.copyWith(
        isUpdating: false,
        successMessage: 'Profile updated successfully',
      );

      LoggerService.info('Profile updated successfully');
      return true;
    } catch (e) {
      LoggerService.error('Error updating profile', e);
      state = state.copyWith(
        isUpdating: false,
        error: _service.getErrorMessage(e),
      );
      return false;
    }
  }

  /// Upload profile picture
  Future<bool> uploadProfilePicture() async {
    state = state.copyWith(
      isUploadingImage: true,
      error: null,
      successMessage: null,
    );

    try {
      // Pick image file with specific allowed extensions to avoid iOS issues
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png'],
        allowMultiple: false,
        withData: true,
      );

      if (result == null || result.files.isEmpty) {
        state = state.copyWith(isUploadingImage: false);
        return false;
      }

      final file = result.files.first;

      // Validate image
      final validationError = ProfileValidation.validateImageFile(
        file.name,
        file.size,
      );
      if (validationError != null) {
        state = state.copyWith(isUploadingImage: false, error: validationError);
        return false;
      }

      // Check if file data is available
      if (file.bytes == null) {
        // Try to read from path if bytes are not available
        if (file.path != null) {
          try {
            final fileBytes = await _readFileFromPath(file.path!);
            await _service.uploadProfilePicture(fileBytes, file.name);
          } catch (pathError) {
            state = state.copyWith(
              isUploadingImage: false,
              error:
                  'Failed to read image file. Please try selecting a different image.',
            );
            return false;
          }
        } else {
          state = state.copyWith(
            isUploadingImage: false,
            error: 'Failed to access image file. Please try again.',
          );
          return false;
        }
      } else {
        // Upload image using bytes
        final bytes = file.bytes!;
        final fileName = file.name;
        await _service.uploadProfilePicture(bytes, fileName);
      }

      // Reload profile to get updated picture URL
      await loadProfile();

      state = state.copyWith(
        isUploadingImage: false,
        successMessage: 'Profile picture updated successfully',
      );

      LoggerService.info('Profile picture uploaded successfully');
      return true;
    } catch (e) {
      LoggerService.error('Error uploading profile picture', e);

      // Provide more specific error messages for common file picker issues
      String errorMessage;
      if (e.toString().contains('file_picker_error')) {
        errorMessage =
            'Unable to process the selected image. Please try selecting a different image file (JPG or PNG).';
      } else if (e.toString().contains('Failed to process any images')) {
        errorMessage =
            'The selected image format is not supported. Please choose a JPG or PNG file.';
      } else if (e.toString().contains('Cannot load representation')) {
        errorMessage =
            'Unable to access the selected image. Please try selecting a different image.';
      } else {
        errorMessage = _service.getErrorMessage(e);
      }

      state = state.copyWith(isUploadingImage: false, error: errorMessage);
      return false;
    }
  }

  /// Update notification preferences
  Future<bool> updateNotificationPreferences(
    Map<String, dynamic> preferences,
  ) async {
    state = state.copyWith(isUpdating: true, error: null, successMessage: null);

    try {
      final updatedProfile = await _service.updateNotificationPreferences(
        preferences,
      );

      state = state.copyWith(
        isUpdating: false,
        profile: updatedProfile,
        successMessage: 'Notification preferences updated successfully',
      );

      LoggerService.info('Notification preferences updated successfully');
      return true;
    } catch (e) {
      LoggerService.error('Error updating notification preferences', e);
      state = state.copyWith(
        isUpdating: false,
        error: _service.getErrorMessage(e),
      );
      return false;
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clear success message
  void clearSuccessMessage() {
    state = state.copyWith(successMessage: null);
  }

  /// Clear all messages
  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }

  /// Read file from path as fallback when bytes are not available
  Future<List<int>> _readFileFromPath(String path) async {
    try {
      final file = File(path);
      if (!await file.exists()) {
        throw Exception('File does not exist at path: $path');
      }
      return await file.readAsBytes();
    } catch (e) {
      LoggerService.error('Error reading file from path: $path', e);
      rethrow;
    }
  }
}

/// Provider for profile management
final profileProvider = StateNotifierProvider<ProfileNotifier, ProfileState>((
  ref,
) {
  final service = ClaimantBaseService();
  return ProfileNotifier(service);
});
