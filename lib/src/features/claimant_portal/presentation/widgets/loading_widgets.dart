import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/custom_skeleton_widget.dart';
import '../../utils/responsive_layout.dart';

/// Loading widgets for the claimant portal with responsive design
class ClaimantLoadingWidgets {
  /// Skeleton loading for dashboard stats cards
  static Widget dashboardStatsLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final crossAxisCount = ResponsiveLayout.getGridCrossAxisCount(
      context,
      maxColumns: 4,
    );
    final spacing = ResponsiveLayout.getCardSpacing(context);

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: ResponsiveLayout.getResponsivePadding(context),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: ResponsiveLayout.getGridChildAspectRatio(context),
      ),
      itemCount: 4,
      itemBuilder: (context, index) => _buildStatCardSkeleton(theme),
    );
  }

  /// Skeleton loading for claims list
  static Widget claimsListLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final padding = ResponsiveLayout.getResponsivePadding(context);

    return ListView.separated(
      padding: padding,
      itemCount: 5,
      separatorBuilder:
          (context, index) =>
              SizedBox(height: ResponsiveLayout.getCardSpacing(context)),
      itemBuilder: (context, index) => _buildClaimCardSkeleton(context, theme),
    );
  }

  /// Skeleton loading for notifications list
  static Widget notificationsListLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final padding = ResponsiveLayout.getResponsivePadding(context);

    return ListView.separated(
      padding: padding,
      itemCount: 8,
      separatorBuilder:
          (context, index) =>
              SizedBox(height: ResponsiveLayout.getCardSpacing(context)),
      itemBuilder:
          (context, index) => _buildNotificationItemSkeleton(context, theme),
    );
  }

  /// Skeleton loading for profile page
  static Widget profileLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final padding = ResponsiveLayout.getResponsivePadding(context);

    return SingleChildScrollView(
      padding: padding,
      child: ResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile header skeleton
          _buildProfileHeaderSkeleton(context, theme),

          // Profile sections skeleton
          _buildProfileSectionSkeleton(context, theme, 'Personal Information'),
          _buildProfileSectionSkeleton(context, theme, 'Contact Details'),
          _buildProfileSectionSkeleton(context, theme, 'Preferences'),
        ],
      ),
    );
  }

  /// Skeleton loading for claim detail page
  static Widget claimDetailLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final padding = ResponsiveLayout.getResponsivePadding(context);

    return SingleChildScrollView(
      padding: padding,
      child: ResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Claim header skeleton
          _buildClaimHeaderSkeleton(context, theme),

          // Claim content skeleton
          _buildClaimContentSkeleton(context, theme),
        ],
      ),
    );
  }

  /// Shimmer loading effect for content
  static Widget shimmerLoading({required Widget child, bool isLoading = true}) {
    if (!isLoading) return child;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 1000),
      child: child,
    );
  }

  /// Progress indicator for long operations
  static Widget progressIndicator(
    BuildContext context, {
    String? message,
    double? progress,
  }) {
    final theme = ShadTheme.of(context);

    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: theme.colorScheme.card,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: theme.colorScheme.border),
        ),
        child: ResponsiveColumn(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (progress != null)
              SizedBox(
                width: 200,
                child: LinearProgressIndicator(
                  value: progress,
                  backgroundColor: theme.colorScheme.muted,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              )
            else
              SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              ),

            if (message != null) ...[
              const SizedBox(height: 16),
              Text(
                message,
                style: theme.textTheme.small,
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Private helper methods for building skeleton components

  static Widget _buildStatCardSkeleton(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomSkeletonWidget(height: 24, widthFactor: 0.6),
          const SizedBox(height: 8),
          const CustomSkeletonWidget(height: 32, widthFactor: 0.4),
          const SizedBox(height: 8),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.8),
        ],
      ),
    );
  }

  static Widget _buildClaimCardSkeleton(
    BuildContext context,
    ShadThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: ResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8,
        children: [
          const ResponsiveRow(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomSkeletonWidget(height: 20, widthFactor: 0.6),
              CustomSkeletonWidget(height: 20, width: 80),
            ],
          ),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.8),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.4),
          const ResponsiveRow(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomSkeletonWidget(height: 16, width: 100),
              CustomSkeletonWidget(height: 16, width: 60),
            ],
          ),
        ],
      ),
    );
  }

  static Widget _buildNotificationItemSkeleton(
    BuildContext context,
    ShadThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: ResponsiveRow(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomSkeletonWidget(
            height: 40,
            width: 40,
            cornerRadius: BorderRadius.all(Radius.circular(20)),
          ),
          Expanded(
            child: ResponsiveColumn(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 4,
              children: [
                const CustomSkeletonWidget(height: 16, widthFactor: 0.7),
                const CustomSkeletonWidget(height: 14, widthFactor: 0.9),
                const CustomSkeletonWidget(height: 12, widthFactor: 0.4),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildProfileHeaderSkeleton(
    BuildContext context,
    ShadThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: ResponsiveRow(
        children: [
          const CustomSkeletonWidget(
            height: 80,
            width: 80,
            cornerRadius: BorderRadius.all(Radius.circular(40)),
          ),
          Expanded(
            child: ResponsiveColumn(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 8,
              children: [
                const CustomSkeletonWidget(height: 24, widthFactor: 0.6),
                const CustomSkeletonWidget(height: 16, widthFactor: 0.8),
                const CustomSkeletonWidget(height: 14, widthFactor: 0.4),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildProfileSectionSkeleton(
    BuildContext context,
    ShadThemeData theme,
    String title,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: ResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12,
        children: [
          CustomSkeletonWidget(height: 20, width: title.length * 8.0),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.9),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.7),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.8),
        ],
      ),
    );
  }

  static Widget _buildClaimHeaderSkeleton(
    BuildContext context,
    ShadThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: ResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12,
        children: [
          const CustomSkeletonWidget(height: 28, widthFactor: 0.8),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.6),
          const ResponsiveRow(
            children: [
              CustomSkeletonWidget(height: 24, width: 80),
              CustomSkeletonWidget(height: 16, width: 120),
            ],
          ),
        ],
      ),
    );
  }

  static Widget _buildClaimContentSkeleton(
    BuildContext context,
    ShadThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: ResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 16,
        children: [
          const CustomSkeletonWidget(height: 20, widthFactor: 0.4),
          const CustomSkeletonWidget(height: 16, widthFactor: 1.0),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.9),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.7),
        ],
      ),
    );
  }
}
