import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/dashboard_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/widgets/dashboard_overview_card.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/funding_dashboard_page.dart';
import '../../utils/responsive_layout.dart';

/// Quick stats widget for claimant dashboard
class QuickStatsWidget extends ConsumerWidget {
  const QuickStatsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final dashboardState = ref.watch(claimantDashboardProvider);
    final stats = dashboardState.stats;
    final isDesktop = ResponsiveLayout.isDesktop(context);
    final isTablet = ResponsiveLayout.isTablet(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Overview',
          style: theme.textTheme.h3.copyWith(fontWeight: FontWeight.bold),
        ),
        SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),
        _buildStatsGrid(
          context,
          theme,
          stats,
          dashboardState.isLoading,
          isDesktop,
          isTablet,
        ),
      ],
    );
  }

  Widget _buildStatsGrid(
    BuildContext context,
    ShadThemeData theme,
    ClaimantDashboardStats stats,
    bool isLoading,
    bool isDesktop,
    bool isTablet,
  ) {
    final statsItems = [
      _StatsItem(
        label: 'Total Claims',
        value: stats.totalClaims.toString(),
        icon: LucideIcons.fileText,
        color: theme.colorScheme.primary,
        onTap: () => _navigateToMyClaims(context),
      ),
      _StatsItem(
        label: 'Active Claims',
        value: stats.activeClaims.toString(),
        icon: LucideIcons.activity,
        color: Colors.green,
        onTap: () => _navigateToMyClaims(context),
      ),
      _StatsItem(
        label: 'Notifications',
        value: stats.unreadNotifications.toString(),
        icon: LucideIcons.bell,
        color:
            stats.unreadNotifications > 0
                ? Colors.orange
                : theme.colorScheme.mutedForeground,
        onTap: () => _navigateToNotifications(context),
      ),
      _StatsItem(
        label: 'Funding',
        value: stats.fundingStatus == 'Not Applicable' ? '0' : '1',
        icon: LucideIcons.piggyBank,
        color:
            stats.fundingStatus.contains('Approved')
                ? Colors.green
                : stats.fundingStatus.contains('Pending')
                ? Colors.orange
                : theme.colorScheme.secondary,
        onTap: () => _navigateToFundingDetails(context),
      ),
    ];

    if (isDesktop) {
      // Desktop: 4 columns
      return Row(
        children:
            statsItems
                .map(
                  (item) => Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(right: 16.0),
                      child: QuickStatsCard(
                        label: item.label,
                        value: item.value,
                        icon: item.icon,
                        color: item.color,
                        onTap: item.onTap,
                        isLoading: isLoading,
                      ),
                    ),
                  ),
                )
                .toList(),
      );
    } else if (isTablet) {
      // Tablet: 2x2 grid
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: QuickStatsCard(
                    label: statsItems[0].label,
                    value: statsItems[0].value,
                    icon: statsItems[0].icon,
                    color: statsItems[0].color,
                    onTap: statsItems[0].onTap,
                    isLoading: isLoading,
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: QuickStatsCard(
                    label: statsItems[1].label,
                    value: statsItems[1].value,
                    icon: statsItems[1].icon,
                    color: statsItems[1].color,
                    onTap: statsItems[1].onTap,
                    isLoading: isLoading,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: QuickStatsCard(
                    label: statsItems[2].label,
                    value: statsItems[2].value,
                    icon: statsItems[2].icon,
                    color: statsItems[2].color,
                    onTap: statsItems[2].onTap,
                    isLoading: isLoading,
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: QuickStatsCard(
                    label: statsItems[3].label,
                    value: statsItems[3].value,
                    icon: statsItems[3].icon,
                    color: statsItems[3].color,
                    onTap: statsItems[3].onTap,
                    isLoading: isLoading,
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    } else {
      // Mobile: 2x2 grid with smaller spacing
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 4.0),
                  child: QuickStatsCard(
                    label: statsItems[0].label,
                    value: statsItems[0].value,
                    icon: statsItems[0].icon,
                    color: statsItems[0].color,
                    onTap: statsItems[0].onTap,
                    isLoading: isLoading,
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 4.0),
                  child: QuickStatsCard(
                    label: statsItems[1].label,
                    value: statsItems[1].value,
                    icon: statsItems[1].icon,
                    color: statsItems[1].color,
                    onTap: statsItems[1].onTap,
                    isLoading: isLoading,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 4.0),
                  child: QuickStatsCard(
                    label: statsItems[2].label,
                    value: statsItems[2].value,
                    icon: statsItems[2].icon,
                    color: statsItems[2].color,
                    onTap: statsItems[2].onTap,
                    isLoading: isLoading,
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 4.0),
                  child: QuickStatsCard(
                    label: statsItems[3].label,
                    value: statsItems[3].value,
                    icon: statsItems[3].icon,
                    color: statsItems[3].color,
                    onTap: statsItems[3].onTap,
                    isLoading: isLoading,
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    }
  }

  void _navigateToMyClaims(BuildContext context) {
    Navigator.of(context).pushNamed('/claimant/claims');
  }

  void _navigateToNotifications(BuildContext context) {
    Navigator.of(context).pushNamed('/notifications');
  }

  void _navigateToMessages(BuildContext context) {
    // TODO: Navigate to messages page when implemented
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Messages feature coming soon')),
    );
  }

  void _navigateToFundingDetails(BuildContext context) {
    Navigator.of(context).pushNamed(FundingDashboardPage.routeName);
  }
}

/// Data class for stats items
class _StatsItem {
  final String label;
  final String value;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;

  const _StatsItem({
    required this.label,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
  });
}

/// Status overview widget showing claim and funding status
class StatusOverviewWidget extends ConsumerWidget {
  const StatusOverviewWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final dashboardState = ref.watch(claimantDashboardProvider);
    final stats = dashboardState.stats;
    final isDesktop = ResponsiveLayout.isDesktop(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(
          'Funding Overview',
          style: theme.textTheme.h3.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        if (isDesktop)
          Row(
            children: [
              const SizedBox(width: 16),
              Expanded(
                child: _buildFundingStatusCard(
                  context,
                  theme,
                  stats,
                  dashboardState.isLoading,
                ),
              ),
            ],
          )
        else
          Column(
            children: [
              _buildFundingStatusCard(
                context,
                theme,
                stats,
                dashboardState.isLoading,
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildFundingStatusCard(
    BuildContext context,
    ShadThemeData theme,
    ClaimantDashboardStats stats,
    bool isLoading,
  ) {
    Color statusColor = theme.colorScheme.secondary;
    if (stats.fundingStatus.contains('Approved')) {
      statusColor = Colors.green;
    } else if (stats.fundingStatus.contains('Rejected')) {
      statusColor = Colors.red;
    } else if (stats.fundingStatus.contains('Pending')) {
      statusColor = Colors.orange;
    }

    return DashboardOverviewCard(
      title: 'Funding Status',
      subtitle: 'Current funding arrangement',
      icon: LucideIcons.creditCard,
      value: isLoading ? null : stats.fundingStatus,
      description:
          stats.fundingStatus != 'Not Applicable'
              ? 'Funding status for your claim'
              : 'No funding applications at this time',
      isLoading: isLoading,
      onTap: () => _navigateToFundingDetails(context),
      iconColor: statusColor,
    );
  }

  void _navigateToClaimDetails(BuildContext context) {
    // TODO: Navigate to claim details when implemented
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Claim details page coming soon')),
    );
  }

  void _navigateToFundingDetails(BuildContext context) {
    Navigator.of(context).pushNamed(FundingDashboardPage.routeName);
  }
}
