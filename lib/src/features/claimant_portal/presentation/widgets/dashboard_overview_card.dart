import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

/// Overview card widget for claimant dashboard
class DashboardOverviewCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final String? value;
  final String? description;
  final VoidCallback? onTap;
  final Color? iconColor;
  final Color? backgroundColor;
  final Widget? trailing;
  final bool isLoading;
  final String? buttonText;
  final VoidCallback? onButtonPressed;

  const DashboardOverviewCard({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    this.value,
    this.description,
    this.onTap,
    this.iconColor,
    this.backgroundColor,
    this.trailing,
    this.isLoading = false,
    this.buttonText,
    this.onButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return ShadCard(
      width: double.infinity,
      title: _buildTitle(context, theme),
      description: _buildDescription(context, theme),
      child: _buildContent(context, theme, isDesktop),
      footer: _buildFooter(context, theme),
    );
  }

  Widget _buildTitle(BuildContext context, ShadThemeData theme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color:
                backgroundColor ??
                iconColor?.withValues(alpha: 0.1) ??
                theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: iconColor ?? theme.colorScheme.primary,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.h4.copyWith(fontSize: 16),
                overflow: TextOverflow.ellipsis,
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 2),
                Text(
                  subtitle!,
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
        if (trailing != null) trailing!,
      ],
    );
  }

  Widget? _buildDescription(BuildContext context, ShadThemeData theme) {
    if (description == null) return null;

    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Text(
        description!,
        style: theme.textTheme.p.copyWith(
          color: theme.colorScheme.mutedForeground,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget? _buildContent(
    BuildContext context,
    ShadThemeData theme,
    bool isDesktop,
  ) {
    if (value == null && !isLoading) return null;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child:
          isLoading
              ? _buildLoadingContent(theme)
              : _buildValueContent(theme, isDesktop),
    );
  }

  Widget _buildLoadingContent(ShadThemeData theme) {
    return Row(
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.primary,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'Loading...',
          style: theme.textTheme.p.copyWith(
            color: theme.colorScheme.mutedForeground,
          ),
        ),
      ],
    );
  }

  Widget _buildValueContent(ShadThemeData theme, bool isDesktop) {
    if (value == null) return const SizedBox.shrink();

    return Text(
      value!,
      style:
          isDesktop
              ? theme.textTheme.h2.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.foreground,
              )
              : theme.textTheme.h3.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.foreground,
              ),
    );
  }

  Widget? _buildFooter(BuildContext context, ShadThemeData theme) {
    if (buttonText == null && onTap == null) return null;

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (buttonText != null && onButtonPressed != null)
          ShadButton(
            onPressed: onButtonPressed,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(buttonText!),
                const SizedBox(width: 4),
                Icon(LucideIcons.arrowRight, size: 16),
              ],
            ),
          )
        else if (onTap != null)
          ShadButton.outline(
            onPressed: onTap,
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('View Details'),
                SizedBox(width: 4),
                Icon(LucideIcons.externalLink, size: 16),
              ],
            ),
          ),
      ],
    );
  }
}

/// Quick stats card for displaying numerical data
class QuickStatsCard extends StatelessWidget {
  final String label;
  final String value;
  final IconData icon;
  final Color? color;
  final VoidCallback? onTap;
  final bool isLoading;

  const QuickStatsCard({
    super.key,
    required this.label,
    required this.value,
    required this.icon,
    this.color,
    this.onTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final cardColor = color ?? theme.colorScheme.primary;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.card,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: theme.colorScheme.border, width: 1),
          boxShadow: [
            BoxShadow(
              color: theme.colorScheme.foreground.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 20, color: cardColor),
                const Spacer(),
                if (onTap != null)
                  Icon(
                    LucideIcons.chevronRight,
                    size: 16,
                    color: theme.colorScheme.mutedForeground,
                  ),
              ],
            ),
            const SizedBox(height: 12),
            if (isLoading)
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(cardColor),
                ),
              )
            else
              Text(
                value,
                style: theme.textTheme.h2.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.foreground,
                ),
              ),
            const SizedBox(height: 4),
            Text(
              label,
              style: theme.textTheme.small.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Action card for navigation to different sections
class ActionCard extends StatelessWidget {
  final String title;
  final String description;
  final IconData icon;
  final VoidCallback onTap;
  final Color? color;
  final bool isEnabled;

  const ActionCard({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.onTap,
    this.color,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final cardColor = color ?? theme.colorScheme.primary;

    return ShadCard(
      width: double.infinity,
      title: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: isEnabled ? cardColor : theme.colorScheme.mutedForeground,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.h4.copyWith(
                fontSize: 16,
                color:
                    isEnabled
                        ? theme.colorScheme.foreground
                        : theme.colorScheme.mutedForeground,
              ),
            ),
          ),
          Icon(
            LucideIcons.chevronRight,
            size: 16,
            color: theme.colorScheme.mutedForeground,
          ),
        ],
      ),
      description: Text(
        description,
        style: theme.textTheme.p.copyWith(
          color: theme.colorScheme.mutedForeground,
          fontSize: 14,
        ),
      ),
      footer: ShadButton(
        onPressed: isEnabled ? onTap : null,
        width: double.infinity,
        child: Text(isEnabled ? 'Open' : 'Coming Soon'),
      ),
    );
  }
}
