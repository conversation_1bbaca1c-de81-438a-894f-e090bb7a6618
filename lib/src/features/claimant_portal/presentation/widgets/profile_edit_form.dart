import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/utils/profile_validation.dart';
import '../providers/profile_provider.dart';
import 'profile_picture_widget.dart';

/// Form widget for editing profile information
class ProfileEditForm extends ConsumerStatefulWidget {
  final VoidCallback? onSaved;
  final VoidCallback? onCancelled;

  const ProfileEditForm({super.key, this.onSaved, this.onCancelled});

  @override
  ConsumerState<ProfileEditForm> createState() => _ProfileEditFormState();
}

class _ProfileEditFormState extends ConsumerState<ProfileEditForm> {
  final _formKey = GlobalKey<ShadFormState>();

  // Controllers
  late final TextEditingController _nameController;
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _mobileController;
  late final TextEditingController _addressLine1Controller;
  late final TextEditingController _addressLine2Controller;
  late final TextEditingController _cityController;
  late final TextEditingController _postcodeController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    final profile = ref.read(profileProvider).profile;

    _nameController = TextEditingController(text: profile?.name ?? '');
    _firstNameController = TextEditingController(
      text: profile?.firstName ?? '',
    );
    _lastNameController = TextEditingController(text: profile?.lastName ?? '');
    _mobileController = TextEditingController(text: profile?.phoneNumber ?? '');
    _addressLine1Controller = TextEditingController(
      text: profile?.userExpanded?['address_line1'] ?? '',
    );
    _addressLine2Controller = TextEditingController(
      text: profile?.userExpanded?['address_line2'] ?? '',
    );
    _cityController = TextEditingController(
      text: profile?.userExpanded?['city'] ?? '',
    );
    _postcodeController = TextEditingController(
      text: profile?.userExpanded?['postcode'] ?? '',
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _mobileController.dispose();
    _addressLine1Controller.dispose();
    _addressLine2Controller.dispose();
    _cityController.dispose();
    _postcodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(profileProvider);

    return ShadCard(
      title: Text('Edit Profile', style: theme.textTheme.h4),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: ShadForm(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Picture Section
              Center(
                child: ProfilePictureWidget(
                  size: 120,
                  showEditButton: true,
                  // Don't override onEditPressed - let the widget handle it internally
                ),
              ),

              const SizedBox(height: 32),

              // Personal Information Section
              Text('Personal Information', style: theme.textTheme.h4),
              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'name',
                controller: _nameController,
                label: const Text('Full Name'),
                placeholder: const Text('Enter your full name'),
                validator: (value) => ProfileValidation.validateName(value),
              ),

              const SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    child: ShadInputFormField(
                      id: 'firstName',
                      controller: _firstNameController,
                      label: const Text('First Name'),
                      placeholder: const Text('Enter your first name'),
                      validator:
                          (value) => ProfileValidation.validateName(
                            value,
                            fieldName: 'First name',
                          ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ShadInputFormField(
                      id: 'lastName',
                      controller: _lastNameController,
                      label: const Text('Last Name'),
                      placeholder: const Text('Enter your last name'),
                      validator:
                          (value) => ProfileValidation.validateName(
                            value,
                            fieldName: 'Last name',
                          ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'mobile',
                controller: _mobileController,
                label: const Text('Phone Number'),
                placeholder: const Text('Enter your phone number'),
                keyboardType: TextInputType.phone,
                validator: ProfileValidation.validatePhoneNumber,
              ),

              const SizedBox(height: 24),

              // Address Information Section
              Text('Address Information', style: theme.textTheme.h4),
              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'addressLine1',
                controller: _addressLine1Controller,
                label: const Text('Address Line 1'),
                placeholder: const Text('Enter your address'),
                validator:
                    (value) => ProfileValidation.validateAddressLine(
                      value,
                      fieldName: 'Address line 1',
                    ),
              ),

              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'addressLine2',
                controller: _addressLine2Controller,
                label: const Text('Address Line 2 (Optional)'),
                placeholder: const Text('Enter additional address information'),
                validator:
                    (value) => ProfileValidation.validateAddressLine(
                      value,
                      required: false,
                      fieldName: 'Address line 2',
                    ),
              ),

              const SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: ShadInputFormField(
                      id: 'city',
                      controller: _cityController,
                      label: const Text('City'),
                      placeholder: const Text('Enter your city'),
                      validator: ProfileValidation.validateCity,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ShadInputFormField(
                      id: 'postcode',
                      controller: _postcodeController,
                      label: const Text('Postcode/ZIP Code'),
                      placeholder: const Text('Enter postcode or ZIP code'),
                      validator: ProfileValidation.validatePostcode,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: ShadButton.outline(
                      onPressed: profileState.isUpdating ? null : _handleCancel,
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ShadButton(
                      onPressed: profileState.isUpdating ? null : _handleSave,
                      child:
                          profileState.isUpdating
                              ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text('Saving...'),
                                ],
                              )
                              : const Text('Save Changes'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleCancel() {
    if (widget.onCancelled != null) {
      widget.onCancelled!();
    } else {
      Navigator.of(context).pop();
    }
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final success = await ref
        .read(profileProvider.notifier)
        .updateProfile(
          name: _nameController.text.trim(),
          firstName: _firstNameController.text.trim(),
          lastName: _lastNameController.text.trim(),
          mobile: _mobileController.text.trim(),
          addressLine1: _addressLine1Controller.text.trim(),
          addressLine2: _addressLine2Controller.text.trim(),
          city: _cityController.text.trim(),
          postcode: _postcodeController.text.trim(),
        );

    if (mounted && success) {
      ShadToaster.of(context).show(
        const ShadToast(
          title: Text('Success'),
          description: Text('Profile updated successfully'),
        ),
      );

      if (widget.onSaved != null) {
        widget.onSaved!();
      } else {
        Navigator.of(context).pop();
      }
    }
  }
}
