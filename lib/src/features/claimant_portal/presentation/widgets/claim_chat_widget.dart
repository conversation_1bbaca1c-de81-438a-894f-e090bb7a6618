import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../pages/chat_interface_page.dart';
import '../pages/chat_conversations_page.dart';

/// Widget for claim-specific chat integration
class ClaimChatWidget extends StatelessWidget {
  final String claimId;
  final String claimTitle;

  const ClaimChatWidget({
    super.key,
    required this.claimId,
    required this.claimTitle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Chat Header
          _buildChatHeader(context, theme),

          const SizedBox(height: 24),

          // Recent Messages Preview
          _buildRecentMessages(context, theme),

          const SizedBox(height: 24),

          // Quick Actions
          _buildQuickActions(context, theme),

          const SizedBox(height: 24),

          // Chat Guidelines
          _buildChatGuidelines(context, theme),
        ],
      ),
    );
  }

  Widget _buildChatHeader(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.support_agent,
                size: 24,
                color: theme.colorScheme.primary,
              ),
            ),

            const SizedBox(width: 16),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Claim Support Chat',
                    style: theme.textTheme.h4.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Get help with your claim from 3Pay Global agents',
                    style: theme.textTheme.p.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),

            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.green.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'Online',
                    style: theme.textTheme.small.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentMessages(BuildContext context, ShadThemeData theme) {
    // Mock recent messages - in real implementation, this would come from a provider
    final recentMessages = _getMockRecentMessages();

    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.chat_bubble_outline,
                  size: 20,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Recent Messages',
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            if (recentMessages.isEmpty)
              _buildNoMessagesState(context, theme)
            else
              Column(
                children:
                    recentMessages
                        .map(
                          (message) =>
                              _buildMessagePreview(context, theme, message),
                        )
                        .toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoMessagesState(BuildContext context, ShadThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border, width: 1),
      ),
      child: Column(
        children: [
          Icon(Icons.chat, size: 48, color: theme.colorScheme.mutedForeground),
          const SizedBox(height: 12),
          Text(
            'No Messages Yet',
            style: theme.textTheme.h4.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start a conversation with our support team about this claim.',
            textAlign: TextAlign.center,
            style: theme.textTheme.p.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessagePreview(
    BuildContext context,
    ShadThemeData theme,
    ChatMessage message,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            message.isFromAgent
                ? theme.colorScheme.muted.withValues(alpha: 0.3)
                : theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color:
                      message.isFromAgent
                          ? Colors.blue
                          : theme.colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  message.isFromAgent ? Icons.support_agent : Icons.person,
                  size: 14,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                message.isFromAgent ? '3Pay Support' : 'You',
                style: theme.textTheme.small.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                DateFormat('MMM dd, HH:mm').format(message.timestamp),
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          Text(
            message.content,
            style: theme.textTheme.p,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.w600),
            ),

            const SizedBox(height: 16),

            Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: ShadButton(
                    onPressed: () => _startNewChat(context),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.chat, size: 18),
                        SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            'Start New Chat',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 12),

                Row(
                  children: [
                    Expanded(
                      child: ShadButton.outline(
                        onPressed: () => _viewChatHistory(context),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.history, size: 16),
                            SizedBox(width: 4),
                            Flexible(
                              child: Text(
                                'History',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    Expanded(
                      child: ShadButton.outline(
                        onPressed: () => _requestCallback(context),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.phone_callback, size: 16),
                            SizedBox(width: 4),
                            Flexible(
                              child: Text(
                                'Call',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatGuidelines(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 20,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Chat Guidelines',
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildGuidelineItem(
                  context,
                  theme,
                  'Response time: Usually within 2-4 hours during business hours',
                ),
                _buildGuidelineItem(
                  context,
                  theme,
                  'Business hours: Monday-Friday, 9:00 AM - 6:00 PM GMT',
                ),
                _buildGuidelineItem(
                  context,
                  theme,
                  'For urgent matters, please call our support line',
                ),
                _buildGuidelineItem(
                  context,
                  theme,
                  'All conversations are secure and confidential',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuidelineItem(
    BuildContext context,
    ShadThemeData theme,
    String text,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 4,
            height: 4,
            margin: const EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: theme.textTheme.p.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<ChatMessage> _getMockRecentMessages() {
    // In real implementation, this would come from a provider/service
    return [
      ChatMessage(
        content:
            'Thank you for submitting your claim. We have received all required documents and will begin review shortly.',
        isFromAgent: true,
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      ChatMessage(
        content: 'I have a question about the timeline for my claim review.',
        isFromAgent: false,
        timestamp: DateTime.now().subtract(const Duration(hours: 3)),
      ),
    ];
  }

  void _startNewChat(BuildContext context) {
    // Navigate to chat interface for this claim
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) =>
                ChatInterfacePage(claimId: claimId, claimTitle: claimTitle),
      ),
    );
  }

  void _viewChatHistory(BuildContext context) {
    // Navigate to chat conversations list
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const ChatConversationsPage()),
    );
  }

  void _requestCallback(BuildContext context) {
    // TODO: Implement callback request
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Callback request coming soon')),
    );
  }
}

/// Chat message model
class ChatMessage {
  final String content;
  final bool isFromAgent;
  final DateTime timestamp;

  const ChatMessage({
    required this.content,
    required this.isFromAgent,
    required this.timestamp,
  });
}
