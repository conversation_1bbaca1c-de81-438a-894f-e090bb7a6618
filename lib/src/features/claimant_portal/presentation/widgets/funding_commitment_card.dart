import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/funding_status_model.dart';

class FundingCommitmentCard extends StatelessWidget {
  final FundingStatus commitment;

  const FundingCommitmentCard({super.key, required this.commitment});

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return ShadCard(
      child: Padding(
        padding: EdgeInsets.all(isDesktop ? 20.0 : 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        commitment.formattedAmount,
                        style: theme.textTheme.h3.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        commitment.fundingType.displayName,
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusBadge(theme, commitment.status),
              ],
            ),

            const SizedBox(height: 16),

            // Funder information
            if (commitment.funderInfo != null) ...[
              Row(
                children: [
                  Icon(
                    LucideIcons.user,
                    size: 16,
                    color: theme.colorScheme.mutedForeground,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      commitment.funderInfo!.name,
                      style: theme.textTheme.p.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.muted,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      commitment.funderInfo!.levelDisplayName,
                      style: theme.textTheme.small.copyWith(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
            ],

            // Commitment details
            Row(
              children: [
                Icon(
                  LucideIcons.calendar,
                  size: 16,
                  color: theme.colorScheme.mutedForeground,
                ),
                const SizedBox(width: 8),
                Text(
                  'Committed: ${_formatDate(commitment.commitmentDate)}',
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
              ],
            ),

            // Approval date if available
            if (commitment.approvalDate != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(LucideIcons.check, size: 16, color: Colors.green),
                  const SizedBox(width: 8),
                  Text(
                    'Approved: ${_formatDate(commitment.approvalDate!)}',
                    style: theme.textTheme.small.copyWith(color: Colors.green),
                  ),
                ],
              ),
            ],

            // FRFR earned if available
            if (commitment.frfrEarned != null &&
                commitment.frfrEarned! > 0) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withOpacity(0.2)),
                ),
                child: Row(
                  children: [
                    Icon(LucideIcons.trendingUp, size: 16, color: Colors.green),
                    const SizedBox(width: 8),
                    Text(
                      'FRFR Earned: ',
                      style: theme.textTheme.small.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '£${commitment.frfrEarned!.toStringAsFixed(2)}',
                      style: theme.textTheme.small.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Investor notes if available
            if (commitment.investorNotes != null &&
                commitment.investorNotes!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.muted,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          LucideIcons.messageSquare,
                          size: 16,
                          color: theme.colorScheme.mutedForeground,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Investor Notes',
                          style: theme.textTheme.small.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      commitment.investorNotes!,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Action buttons for certain statuses
            if (commitment.status ==
                FundingCommitmentStatus.pendingApproval) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Icon(LucideIcons.clock, size: 16, color: Colors.orange),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Awaiting approval from 3Pay Global',
                      style: theme.textTheme.small.copyWith(
                        color: Colors.orange,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(
    ShadThemeData theme,
    FundingCommitmentStatus status,
  ) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (status) {
      case FundingCommitmentStatus.approved:
      case FundingCommitmentStatus.active:
        backgroundColor = Colors.green.withOpacity(0.1);
        textColor = Colors.green;
        icon = LucideIcons.check;
        break;
      case FundingCommitmentStatus.pendingApproval:
        backgroundColor = Colors.orange.withOpacity(0.1);
        textColor = Colors.orange;
        icon = LucideIcons.clock;
        break;
      case FundingCommitmentStatus.rejected:
        backgroundColor = Colors.red.withOpacity(0.1);
        textColor = Colors.red;
        icon = LucideIcons.x;
        break;
      case FundingCommitmentStatus.completed:
        backgroundColor = theme.colorScheme.primary.withOpacity(0.1);
        textColor = theme.colorScheme.primary;
        icon = LucideIcons.checkCheck;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: textColor.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: textColor),
          const SizedBox(width: 6),
          Text(
            status.displayName,
            style: theme.textTheme.small.copyWith(
              color: textColor,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks != 1 ? 's' : ''} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months != 1 ? 's' : ''} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years year${years != 1 ? 's' : ''} ago';
    }
  }
}
