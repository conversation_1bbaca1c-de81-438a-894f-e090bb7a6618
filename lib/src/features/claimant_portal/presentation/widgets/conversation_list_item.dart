import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/chat_conversation_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/chat_message_model.dart';

/// Widget for displaying a conversation item in the conversations list
class ConversationListItem extends StatelessWidget {
  final ChatConversationModel conversation;
  final VoidCallback onTap;
  final bool isSelected;

  const ConversationListItem({
    super.key,
    required this.conversation,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final hasUnreadMessages = conversation.unreadCount > 0;

    return ShadCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border:
                isSelected
                    ? Border.all(color: theme.colorScheme.primary, width: 2)
                    : null,
            color:
                isSelected
                    ? theme.colorScheme.primary.withValues(alpha: 0.05)
                    : null,
          ),
          child: Row(
            children: [
              _buildAvatar(theme),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(theme, hasUnreadMessages),
                    const SizedBox(height: 4),
                    _buildLastMessage(theme, hasUnreadMessages),
                    const SizedBox(height: 4),
                    _buildFooter(theme),
                  ],
                ),
              ),
              if (hasUnreadMessages) _buildUnreadBadge(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(ShadThemeData theme) {
    return Stack(
      children: [
        CircleAvatar(
          radius: 24,
          backgroundColor: theme.colorScheme.primary,
          child: Text(
            '3P',
            style: TextStyle(
              color: theme.colorScheme.primaryForeground,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        // Online indicator
        Positioned(
          bottom: 0,
          right: 0,
          child: Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
              border: Border.all(color: theme.colorScheme.background, width: 2),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(ShadThemeData theme, bool hasUnreadMessages) {
    return Row(
      children: [
        Expanded(
          child: Text(
            conversation.title ?? 'Chat with 3Pay Agent',
            style: theme.textTheme.h4.copyWith(
              fontWeight: hasUnreadMessages ? FontWeight.bold : FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Text(
          _formatTime(
            conversation.lastActivityDate ?? conversation.createdDate,
          ),
          style: theme.textTheme.small.copyWith(
            color: theme.colorScheme.mutedForeground,
            fontWeight: hasUnreadMessages ? FontWeight.w500 : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  Widget _buildLastMessage(ShadThemeData theme, bool hasUnreadMessages) {
    final lastMessage = conversation.lastMessage;
    String messageText;
    IconData? messageIcon;

    if (lastMessage == null) {
      messageText = 'No messages yet';
    } else {
      switch (lastMessage.messageType) {
        case MessageType.file:
          messageIcon = Icons.attach_file;
          messageText =
              lastMessage.messageContent.isNotEmpty
                  ? lastMessage.messageContent
                  : 'File attachment';
          break;
        case MessageType.system:
          messageIcon = Icons.info_outline;
          messageText = lastMessage.messageContent;
          break;
        default:
          messageText = lastMessage.messageContent;
      }
    }

    return Row(
      children: [
        if (messageIcon != null) ...[
          Icon(messageIcon, size: 14, color: theme.colorScheme.mutedForeground),
          const SizedBox(width: 4),
        ],
        Expanded(
          child: Text(
            messageText,
            style: theme.textTheme.small.copyWith(
              color:
                  hasUnreadMessages
                      ? theme.colorScheme.foreground
                      : theme.colorScheme.mutedForeground,
              fontWeight:
                  hasUnreadMessages ? FontWeight.w500 : FontWeight.normal,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ),
      ],
    );
  }

  Widget _buildFooter(ShadThemeData theme) {
    return Row(
      children: [
        Icon(
          Icons.chat_bubble_outline,
          size: 12,
          color: theme.colorScheme.mutedForeground,
        ),
        const SizedBox(width: 4),
        Text(
          'Claim ID: ${conversation.claimId}',
          style: theme.textTheme.small.copyWith(
            color: theme.colorScheme.mutedForeground,
            fontSize: 11,
          ),
        ),
        const Spacer(),
        _buildStatusIndicator(theme),
      ],
    );
  }

  Widget _buildStatusIndicator(ShadThemeData theme) {
    Color statusColor;
    String statusText;

    switch (conversation.status) {
      case ConversationStatus.active:
        statusColor = Colors.green;
        statusText = 'Active';
        break;
      case ConversationStatus.closed:
        statusColor = theme.colorScheme.mutedForeground;
        statusText = 'Closed';
        break;
      case ConversationStatus.archived:
        statusColor = theme.colorScheme.mutedForeground;
        statusText = 'Archived';
        break;
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(color: statusColor, shape: BoxShape.circle),
        ),
        const SizedBox(width: 4),
        Text(
          statusText,
          style: theme.textTheme.small.copyWith(
            color: statusColor,
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildUnreadBadge(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        conversation.unreadCount > 99
            ? '99+'
            : conversation.unreadCount.toString(),
        style: TextStyle(
          color: theme.colorScheme.primaryForeground,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}d ago';
      } else {
        return '${dateTime.day}/${dateTime.month}';
      }
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
