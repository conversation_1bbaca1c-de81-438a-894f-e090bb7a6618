import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/providers/notification_counter_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/claimant_notifications_list_page.dart';

/// Notification bell widget for the claimant dashboard header
/// Shows unread count and navigates to notifications list
class NotificationBellWidget extends ConsumerStatefulWidget {
  const NotificationBellWidget({super.key});

  @override
  ConsumerState<NotificationBellWidget> createState() =>
      _NotificationBellWidgetState();
}

class _NotificationBellWidgetState extends ConsumerState<NotificationBellWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 0.1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _animateBell() {
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final unreadCount = ref.watch(currentUnreadCountProvider);

    // Animate when new unread notifications arrive
    ref.listen<int>(currentUnreadCountProvider, (previous, current) {
      if (previous != null && current > previous) {
        _animateBell();
      }
    });

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: _buildBellButton(theme, unreadCount),
          ),
        );
      },
    );
  }

  Widget _buildBellButton(ShadThemeData theme, int unreadCount) {
    final hasUnread = unreadCount > 0;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Bell icon button
        IconButton(
          onPressed: () => _navigateToNotifications(context),
          icon: Icon(
            hasUnread ? LucideIcons.bell : LucideIcons.bellRing,
            color:
                hasUnread
                    ? theme.colorScheme.primary
                    : theme.colorScheme.mutedForeground,
            size: 20,
          ),
          tooltip:
              hasUnread
                  ? '$unreadCount unread notification${unreadCount == 1 ? '' : 's'}'
                  : 'Notifications',
          padding: const EdgeInsets.all(8),
          constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
        ),

        // Unread count badge
        if (hasUnread)
          Positioned(
            right: 6,
            top: 6,
            child: _buildUnreadBadge(theme, unreadCount),
          ),
      ],
    );
  }

  Widget _buildUnreadBadge(ShadThemeData theme, int count) {
    final displayCount = count > 99 ? '99+' : count.toString();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: theme.colorScheme.destructive,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: theme.colorScheme.background, width: 1),
      ),
      constraints: const BoxConstraints(minWidth: 18, minHeight: 18),
      child: Text(
        displayCount,
        style: TextStyle(
          color: theme.colorScheme.destructiveForeground,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  void _navigateToNotifications(BuildContext context) {
    Navigator.of(context).pushNamed(ClaimantNotificationsListPage.routeName);
  }
}

/// Compact notification bell for smaller spaces
class CompactNotificationBellWidget extends ConsumerWidget {
  const CompactNotificationBellWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final unreadCount = ref.watch(currentUnreadCountProvider);
    final hasUnread = unreadCount > 0;

    return GestureDetector(
      onTap:
          () => Navigator.of(
            context,
          ).pushNamed(ClaimantNotificationsListPage.routeName),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color:
              hasUnread
                  ? theme.colorScheme.primary.withOpacity(0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              hasUnread ? LucideIcons.bellRing : LucideIcons.bell,
              size: 16,
              color:
                  hasUnread
                      ? theme.colorScheme.primary
                      : theme.colorScheme.mutedForeground,
            ),
            if (hasUnread) ...[
              const SizedBox(width: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                decoration: BoxDecoration(
                  color: theme.colorScheme.destructive,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  unreadCount > 9 ? '9+' : unreadCount.toString(),
                  style: TextStyle(
                    color: theme.colorScheme.destructiveForeground,
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Notification bell with text label
class LabeledNotificationBellWidget extends ConsumerWidget {
  final String label;
  final bool showLabel;

  const LabeledNotificationBellWidget({
    super.key,
    this.label = 'Notifications',
    this.showLabel = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final unreadCount = ref.watch(currentUnreadCountProvider);
    final hasUnread = unreadCount > 0;

    return InkWell(
      onTap:
          () => Navigator.of(
            context,
          ).pushNamed(ClaimantNotificationsListPage.routeName),
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                Icon(
                  hasUnread ? LucideIcons.bellRing : LucideIcons.bell,
                  size: 18,
                  color:
                      hasUnread
                          ? theme.colorScheme.primary
                          : theme.colorScheme.mutedForeground,
                ),
                if (hasUnread)
                  Positioned(
                    right: -2,
                    top: -2,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.destructive,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: theme.colorScheme.background,
                          width: 1,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            if (showLabel) ...[
              const SizedBox(width: 8),
              Text(
                label,
                style: theme.textTheme.small.copyWith(
                  color:
                      hasUnread
                          ? theme.colorScheme.primary
                          : theme.colorScheme.mutedForeground,
                  fontWeight: hasUnread ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
              if (hasUnread) ...[
                const SizedBox(width: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 4,
                    vertical: 1,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.destructive,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    unreadCount.toString(),
                    style: TextStyle(
                      color: theme.colorScheme.destructiveForeground,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }
}
