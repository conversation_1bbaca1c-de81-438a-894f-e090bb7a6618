import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:intl/intl.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/document_preview_widget.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/empty_state_widget.dart';
import '../providers/claim_detail_provider.dart';

/// Widget to display and manage claim documents
class ClaimDocumentsWidget extends ConsumerWidget {
  final String claimId;

  const ClaimDocumentsWidget({super.key, required this.claimId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final documents = ref.watch(claimDocumentsProvider(claimId));
    final detailState = ref.watch(claimDetailProvider(claimId));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Documents Header
          _buildDocumentsHeader(context, theme, documents.length),

          const SizedBox(height: 24),

          // Documents List
          if (detailState.isLoading)
            _buildLoadingState(context, theme)
          else if (documents.isEmpty)
            _buildEmptyState(context, theme)
          else
            _buildDocumentsList(context, theme, documents, ref),
        ],
      ),
    );
  }

  Widget _buildDocumentsHeader(
    BuildContext context,
    ShadThemeData theme,
    int documentCount,
  ) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.folder_open,
                size: 24,
                color: theme.colorScheme.primary,
              ),
            ),

            const SizedBox(width: 16),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Claim Documents',
                    style: theme.textTheme.h4.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '$documentCount ${documentCount == 1 ? 'document' : 'documents'} available',
                    style: theme.textTheme.p.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),

            ShadButton.outline(
              onPressed: () => _showDocumentInfo(context),
              size: ShadButtonSize.sm,
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.info_outline, size: 16),
                  SizedBox(width: 6),
                  Text('Info'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Center(
          child: Column(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'Loading documents...',
                style: theme.textTheme.p.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: EmptyStateWidget(
          icon: Icons.description,
          message:
              'No documents have been uploaded for this claim yet. Documents will appear here as they become available.',
        ),
      ),
    );
  }

  Widget _buildDocumentsList(
    BuildContext context,
    ShadThemeData theme,
    List<ClaimDocument> documents,
    WidgetRef ref,
  ) {
    // Group documents by type
    final groupedDocs = _groupDocumentsByType(documents);

    return Column(
      children:
          groupedDocs.entries.map((entry) {
            final type = entry.key;
            final docs = entry.value;

            return Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: _buildDocumentGroup(context, theme, type, docs, ref),
            );
          }).toList(),
    );
  }

  Widget _buildDocumentGroup(
    BuildContext context,
    ShadThemeData theme,
    String type,
    List<ClaimDocument> documents,
    WidgetRef ref,
  ) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Group Header
            Row(
              children: [
                Icon(
                  _getDocumentTypeIcon(type),
                  size: 20,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  type,
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.muted.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${documents.length}',
                    style: theme.textTheme.small.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Documents in this group
            Column(
              children:
                  documents
                      .map(
                        (doc) => _buildDocumentItem(context, theme, doc, ref),
                      )
                      .toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentItem(
    BuildContext context,
    ShadThemeData theme,
    ClaimDocument document,
    WidgetRef ref,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border, width: 1),
      ),
      child: Row(
        children: [
          // File type indicator
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getFileTypeColor(
                document.fileExtension,
              ).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                document.fileExtension,
                style: theme.textTheme.small.copyWith(
                  color: _getFileTypeColor(document.fileExtension),
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Document info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  document.name,
                  style: theme.textTheme.p.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      document.formattedSize,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '•',
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        DateFormat('MMM dd, yyyy').format(document.uploadDate),
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
                // if (document.uploadedBy.isNotEmpty) ...[
                //   const SizedBox(height: 2),
                //   Text(
                //     'Uploaded by ${document.uploadedBy}',
                //     style: theme.textTheme.small.copyWith(
                //       color: theme.colorScheme.mutedForeground,
                //       fontSize: 11,
                //     ),
                //     overflow: TextOverflow.ellipsis,
                //     maxLines: 1,
                //   ),
                // ],
              ],
            ),
          ),

          const SizedBox(width: 12),

          // Actions
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (document.isAccessible) ...[
                ShadButton.ghost(
                  onPressed: () => _downloadDocument(context, ref, document),
                  size: ShadButtonSize.sm,
                  child: const Icon(Icons.download, size: 16),
                ),
                const SizedBox(width: 4),
                ShadButton.ghost(
                  onPressed: () => _previewDocument(context, document),
                  size: ShadButtonSize.sm,
                  child: const Icon(Icons.visibility, size: 16),
                ),
              ] else
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Restricted',
                    style: theme.textTheme.small.copyWith(
                      color: Colors.orange,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Map<String, List<ClaimDocument>> _groupDocumentsByType(
    List<ClaimDocument> documents,
  ) {
    final grouped = <String, List<ClaimDocument>>{};

    for (final doc in documents) {
      final type = doc.type.isEmpty ? 'General Documents' : doc.type;
      grouped.putIfAbsent(type, () => []).add(doc);
    }

    return grouped;
  }

  IconData _getDocumentTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'legal':
      case 'legal documents':
        return Icons.gavel;
      case 'evidence':
      case 'evidence documents':
        return Icons.fact_check;
      case 'financial':
      case 'financial documents':
        return Icons.account_balance;
      case 'correspondence':
        return Icons.mail;
      case 'medical':
      case 'medical documents':
        return Icons.local_hospital;
      default:
        return Icons.description;
    }
  }

  Color _getFileTypeColor(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Colors.red;
      case 'doc':
      case 'docx':
        return Colors.blue;
      case 'xls':
      case 'xlsx':
        return Colors.green;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  void _downloadDocument(
    BuildContext context,
    WidgetRef ref,
    ClaimDocument document,
  ) async {
    try {
      await ref
          .read(claimDetailProvider(claimId).notifier)
          .downloadDocument(document);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Downloaded ${document.name}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to download ${document.name}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _previewDocument(BuildContext context, ClaimDocument document) async {
    try {
      // Instead of launching URL in browser, navigate to our preview widget
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => DocumentPreviewWidget(
            url: document.url,
            fileName: document.name,
          ),
        ),
      );
    } catch (e) {
      if (context.mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Preview Error'),
            description: Text('Failed to preview document: ${e.toString()}'),
          ),
        );
      }
    }
  }

  void _showDocumentInfo(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Document Information'),
            content: const Text(
              'This section contains all documents related to your claim. You can download accessible documents and view their details. Some documents may be restricted based on your access level.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }
}
