import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../providers/profile_provider.dart';

/// Widget for displaying and managing profile picture
class ProfilePictureWidget extends ConsumerWidget {
  final double size;
  final bool showEditButton;
  final VoidCallback? onEditPressed;

  const ProfilePictureWidget({
    super.key,
    this.size = 120,
    this.showEditButton = true,
    this.onEditPressed,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(profileProvider);
    final profileNotifier = ref.read(profileProvider.notifier);

    return Column(
      children: [
        Stack(
          children: [
            // Profile picture or avatar
            Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: theme.colorScheme.border, width: 2),
              ),
              child: ClipOval(
                child:
                    profileState.profilePictureUrl != null
                        ? Image.network(
                          profileState.profilePictureUrl!,
                          width: size,
                          height: size,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildDefaultAvatar(theme);
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return _buildLoadingAvatar(theme);
                          },
                        )
                        : _buildDefaultAvatar(theme),
              ),
            ),

            // Edit button
            if (showEditButton)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: theme.colorScheme.background,
                      width: 2,
                    ),
                  ),
                  child: ShadButton.ghost(
                    size: ShadButtonSize.sm,
                    width: 32,
                    height: 32,
                    padding: EdgeInsets.zero,
                    onPressed:
                        profileState.isUploadingImage
                            ? null
                            : () async {
                              if (onEditPressed != null) {
                                onEditPressed!();
                              } else {
                                await _handleImageUpload(
                                  context,
                                  profileNotifier,
                                );
                              }
                            },
                    child:
                        profileState.isUploadingImage
                            ? SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  theme.colorScheme.primaryForeground,
                                ),
                              ),
                            )
                            : Icon(
                              LucideIcons.camera,
                              size: 16,
                              color: theme.colorScheme.primaryForeground,
                            ),
                  ),
                ),
              ),
          ],
        ),

        // Upload status and instructions
        if (profileState.isUploadingImage) ...[
          const SizedBox(height: 8),
          Text(
            'Uploading...',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ] else ...[
          const SizedBox(height: 8),
          Text(
            'Click the edit button to change the profile picture',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            'Supported: JPG, PNG (max 5MB)',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
              fontSize: 11,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  Widget _buildDefaultAvatar(ShadThemeData theme) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        shape: BoxShape.circle,
      ),
      child: Icon(
        LucideIcons.user,
        size: size * 0.4,
        color: theme.colorScheme.mutedForeground,
      ),
    );
  }

  Widget _buildLoadingAvatar(ShadThemeData theme) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
        ),
      ),
    );
  }

  Future<void> _handleImageUpload(
    BuildContext context,
    ProfileNotifier notifier,
  ) async {
    final success = await notifier.uploadProfilePicture();

    if (context.mounted) {
      if (success) {
        ShadToaster.of(context).show(
          const ShadToast(
            title: Text('Success'),
            description: Text('Profile picture updated successfully'),
          ),
        );
      } else {
        // Error is handled by the provider state
        // Show help dialog for any upload failure to assist users
        Future.delayed(const Duration(milliseconds: 500), () {
          if (context.mounted) {
            _showImageUploadHelp(context);
          }
        });
      }
    }
  }

  void _showImageUploadHelp(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Image Upload Tips'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'If you\'re having trouble uploading your profile picture, try:',
                ),
                SizedBox(height: 12),
                Text('• Use JPG or PNG format only'),
                Text('• Ensure the image is smaller than 5MB'),
                Text('• Try taking a new photo with your camera'),
                Text('• Save the image to your device first, then select it'),
                Text('• Restart the app and try again'),
                SizedBox(height: 12),
                Text('If the problem persists, please contact support.'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }
}

/// Compact version of profile picture for smaller spaces
class CompactProfilePictureWidget extends ConsumerWidget {
  final double size;

  const CompactProfilePictureWidget({super.key, this.size = 40});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(profileProvider);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: theme.colorScheme.border, width: 1),
      ),
      child: ClipOval(
        child:
            profileState.profilePictureUrl != null
                ? Image.network(
                  profileState.profilePictureUrl!,
                  width: size,
                  height: size,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildDefaultAvatar(theme);
                  },
                )
                : _buildDefaultAvatar(theme),
      ),
    );
  }

  Widget _buildDefaultAvatar(ShadThemeData theme) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        shape: BoxShape.circle,
      ),
      child: Icon(
        LucideIcons.user,
        size: size * 0.5,
        color: theme.colorScheme.mutedForeground,
      ),
    );
  }
}
