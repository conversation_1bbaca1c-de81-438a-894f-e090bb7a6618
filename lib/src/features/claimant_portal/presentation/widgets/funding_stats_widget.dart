import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/funding_provider.dart';

class FundingStatsWidget extends ConsumerWidget {
  const FundingStatsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final fundingState = ref.watch(fundingProvider);
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600;

    final aggregation = fundingState.aggregation;
    if (aggregation == null) {
      return const SizedBox.shrink();
    }

    return ShadCard(
      child: Padding(
        padding: EdgeInsets.all(isDesktop ? 24.0 : 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  LucideIcons.trendingUp,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Funding Statistics',
                  style: theme.textTheme.h3.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Statistics grid
            if (isDesktop)
              Row(
                children: [
                  Expanded(child: _buildVelocityCard(theme, aggregation)),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildCommitmentCountCard(theme, aggregation),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildAverageCommitmentCard(theme, aggregation),
                  ),
                  const SizedBox(width: 16),
                  Expanded(child: _buildFundingRatioCard(theme, aggregation)),
                ],
              )
            else if (isTablet)
              Column(
                children: [
                  Row(
                    children: [
                      Expanded(child: _buildVelocityCard(theme, aggregation)),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildCommitmentCountCard(theme, aggregation),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildAverageCommitmentCard(theme, aggregation),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildFundingRatioCard(theme, aggregation),
                      ),
                    ],
                  ),
                ],
              )
            else
              Column(
                children: [
                  _buildVelocityCard(theme, aggregation),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildCommitmentCountCard(theme, aggregation),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildAverageCommitmentCard(theme, aggregation),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildFundingRatioCard(theme, aggregation),
                ],
              ),

            const SizedBox(height: 20),

            // Additional insights
            _buildInsightsSection(theme, aggregation),
          ],
        ),
      ),
    );
  }

  Widget _buildVelocityCard(ShadThemeData theme, dynamic aggregation) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(LucideIcons.zap, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Velocity',
                  style: theme.textTheme.small.copyWith(
                    color: Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            aggregation.formattedFundingVelocity,
            style: theme.textTheme.h3.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          Text(
            'Average per month',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommitmentCountCard(ShadThemeData theme, dynamic aggregation) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.purple.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(LucideIcons.users, color: Colors.purple, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Investors',
                  style: theme.textTheme.small.copyWith(
                    color: Colors.purple,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${aggregation.commitmentCount}',
            style: theme.textTheme.h3.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.purple,
            ),
          ),
          Text(
            'Total investors',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAverageCommitmentCard(ShadThemeData theme, dynamic aggregation) {
    final averageCommitment =
        aggregation.commitmentCount > 0
            ? aggregation.totalCommitted / aggregation.commitmentCount
            : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.teal.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.teal.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(LucideIcons.calculator, color: Colors.teal, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Average',
                  style: theme.textTheme.small.copyWith(
                    color: Colors.teal,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '£${averageCommitment.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}',
            style: theme.textTheme.h3.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.teal,
            ),
          ),
          Text(
            'Per commitment',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFundingRatioCard(ShadThemeData theme, dynamic aggregation) {
    final securedRatio =
        aggregation.commitmentCount > 0
            ? (aggregation.securedCount / aggregation.commitmentCount * 100)
            : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(LucideIcons.percent, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Success',
                  style: theme.textTheme.small.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${securedRatio.toStringAsFixed(1)}%',
            style: theme.textTheme.h3.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          Text(
            'Secured funding',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsightsSection(ShadThemeData theme, dynamic aggregation) {
    final insights = _generateInsights(aggregation);

    if (insights.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Insights',
          style: theme.textTheme.p.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        ...insights.map(
          (insight) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  LucideIcons.lightbulb,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    insight,
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  List<String> _generateInsights(dynamic aggregation) {
    final insights = <String>[];

    if (aggregation.fundingPercentage >= 100) {
      insights.add('Congratulations! Your claims are fully funded.');
    } else if (aggregation.fundingPercentage >= 75) {
      insights.add('You\'re close to full funding. Great progress!');
    } else if (aggregation.pendingCount > 0) {
      insights.add(
        'You have ${aggregation.pendingCount} pending commitment${aggregation.pendingCount != 1 ? 's' : ''} awaiting approval.',
      );
    }

    if (aggregation.fundingVelocity > 0) {
      final monthsToTarget =
          aggregation.targetAmount > aggregation.totalSecured
              ? (aggregation.targetAmount - aggregation.totalSecured) /
                  aggregation.fundingVelocity
              : 0;

      if (monthsToTarget > 0 && monthsToTarget < 12) {
        insights.add(
          'At current velocity, you could reach your target in ${monthsToTarget.toStringAsFixed(1)} months.',
        );
      }
    }

    if (aggregation.commitmentCount > 1) {
      insights.add(
        'You have diversified funding from ${aggregation.commitmentCount} different investors.',
      );
    }

    return insights.take(3).toList(); // Limit to 3 insights
  }
}
