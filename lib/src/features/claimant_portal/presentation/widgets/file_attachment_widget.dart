import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

/// Widget for handling file attachments in chat
class FileAttachmentWidget extends StatelessWidget {
  final File file;
  final VoidCallback? onRemove;
  final VoidCallback? onTap;
  final bool showRemoveButton;
  final bool isUploading;
  final double? uploadProgress;

  const FileAttachmentWidget({
    super.key,
    required this.file,
    this.onRemove,
    this.onTap,
    this.showRemoveButton = true,
    this.isUploading = false,
    this.uploadProgress,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final fileName = file.path.split('/').last;
    final fileExtension = fileName.split('.').last.toLowerCase();

    return ShadCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              _buildFileIcon(theme, fileExtension),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fileName,
                      style: theme.textTheme.small.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _formatFileSize(_getFileSize()),
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                        fontSize: 11,
                      ),
                    ),
                    if (isUploading && uploadProgress != null) ...[
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: uploadProgress,
                        backgroundColor: theme.colorScheme.muted,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (isUploading) ...[
                const SizedBox(width: 8),
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.primary,
                    ),
                  ),
                ),
              ] else if (showRemoveButton && onRemove != null) ...[
                const SizedBox(width: 8),
                ShadButton.ghost(
                  size: ShadButtonSize.sm,
                  onPressed: onRemove,
                  child: Icon(
                    Icons.close,
                    size: 16,
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFileIcon(ShadThemeData theme, String extension) {
    IconData iconData;
    Color iconColor = theme.colorScheme.mutedForeground;

    switch (extension) {
      case 'pdf':
        iconData = Icons.picture_as_pdf;
        iconColor = Colors.red;
        break;
      case 'doc':
      case 'docx':
        iconData = Icons.description;
        iconColor = Colors.blue;
        break;
      case 'xls':
      case 'xlsx':
        iconData = Icons.table_chart;
        iconColor = Colors.green;
        break;
      case 'ppt':
      case 'pptx':
        iconData = Icons.slideshow;
        iconColor = Colors.orange;
        break;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        iconData = Icons.image;
        iconColor = Colors.purple;
        break;
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        iconData = Icons.video_file;
        iconColor = Colors.indigo;
        break;
      case 'mp3':
      case 'wav':
      case 'aac':
        iconData = Icons.audio_file;
        iconColor = Colors.teal;
        break;
      case 'zip':
      case 'rar':
      case '7z':
        iconData = Icons.archive;
        iconColor = Colors.brown;
        break;
      case 'txt':
        iconData = Icons.text_snippet;
        break;
      default:
        iconData = Icons.insert_drive_file;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(iconData, color: iconColor, size: 20),
    );
  }

  int _getFileSize() {
    try {
      // Check if file path is valid before trying to get size
      if (file.path.isEmpty) {
        return 0;
      }
      return file.lengthSync();
    } catch (e) {
      // Return 0 if there's any error getting file size (e.g., invalid path)
      return 0;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}

/// Widget for displaying a list of file attachments
class FileAttachmentListWidget extends StatelessWidget {
  final List<File> files;
  final Function(File)? onRemoveFile;
  final Function(File)? onTapFile;
  final bool showRemoveButtons;
  final bool isUploading;
  final Map<String, double>? uploadProgress;

  const FileAttachmentListWidget({
    super.key,
    required this.files,
    this.onRemoveFile,
    this.onTapFile,
    this.showRemoveButtons = true,
    this.isUploading = false,
    this.uploadProgress,
  });

  @override
  Widget build(BuildContext context) {
    if (files.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children:
          files.map((file) {
            final fileName = file.path.split('/').last;
            final progress = uploadProgress?[fileName];

            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: FileAttachmentWidget(
                file: file,
                onRemove:
                    showRemoveButtons ? () => onRemoveFile?.call(file) : null,
                onTap: () => onTapFile?.call(file),
                showRemoveButton: showRemoveButtons,
                isUploading: isUploading && progress != null,
                uploadProgress: progress,
              ),
            );
          }).toList(),
    );
  }
}
