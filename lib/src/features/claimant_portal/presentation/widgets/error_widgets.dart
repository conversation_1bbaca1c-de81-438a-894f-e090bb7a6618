import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../utils/responsive_layout.dart';

/// Error handling widgets for the claimant portal with responsive design
class ClaimantErrorWidgets {
  /// Generic error state widget with retry functionality
  static Widget errorState({
    required BuildContext context,
    required String message,
    String? title,
    IconData? icon,
    VoidCallback? onRetry,
    String? retryButtonText,
    bool showDetails = false,
    String? details,
  }) {
    final theme = ShadTheme.of(context);
    final isDesktop = ResponsiveLayout.isDesktop(context);

    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isDesktop ? 500 : double.infinity,
        ),
        padding: ResponsiveLayout.getResponsivePadding(context),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: theme.colorScheme.card,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: theme.colorScheme.border),
          ),
          child: ResponsiveColumn(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Error icon
              Icon(
                icon ?? LucideIcons.triangle,
                size: ResponsiveLayout.getIconSize(context, baseSize: 48),
                color: theme.colorScheme.destructive,
              ),

              // Error title
              if (title != null)
                Text(
                  title,
                  style: theme.textTheme.h4?.copyWith(
                    color: theme.colorScheme.destructive,
                  ),
                  textAlign: TextAlign.center,
                ),

              // Error message
              Text(
                message,
                style: theme.textTheme.p,
                textAlign: TextAlign.center,
              ),

              // Error details (expandable)
              if (showDetails && details != null)
                _buildErrorDetails(context, theme, details),

              // Retry button
              if (onRetry != null)
                SizedBox(
                  width: double.infinity,
                  height: ResponsiveLayout.getButtonHeight(context),
                  child: ShadButton(
                    onPressed: onRetry,
                    child: Text(retryButtonText ?? 'Try Again'),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Network error state with specific messaging
  static Widget networkError({
    required BuildContext context,
    VoidCallback? onRetry,
  }) {
    return errorState(
      context: context,
      title: 'Connection Problem',
      message: 'Please check your internet connection and try again.',
      icon: LucideIcons.wifiOff,
      onRetry: onRetry,
      retryButtonText: 'Retry',
    );
  }

  /// Server error state
  static Widget serverError({
    required BuildContext context,
    VoidCallback? onRetry,
  }) {
    return errorState(
      context: context,
      title: 'Server Error',
      message: 'Something went wrong on our end. Please try again later.',
      icon: LucideIcons.server,
      onRetry: onRetry,
      retryButtonText: 'Retry',
    );
  }

  /// Authentication error state
  static Widget authError({
    required BuildContext context,
    VoidCallback? onLogin,
  }) {
    return errorState(
      context: context,
      title: 'Authentication Required',
      message: 'Please log in to access this content.',
      icon: LucideIcons.lock,
      onRetry: onLogin,
      retryButtonText: 'Log In',
    );
  }

  /// Permission denied error state
  static Widget permissionError({
    required BuildContext context,
    VoidCallback? onGoBack,
  }) {
    return errorState(
      context: context,
      title: 'Access Denied',
      message: 'You don\'t have permission to access this content.',
      icon: LucideIcons.shield,
      onRetry: onGoBack,
      retryButtonText: 'Go Back',
    );
  }

  /// Not found error state
  static Widget notFoundError({
    required BuildContext context,
    String? resourceName,
    VoidCallback? onGoBack,
  }) {
    return errorState(
      context: context,
      title: 'Not Found',
      message:
          resourceName != null
              ? 'The $resourceName you\'re looking for doesn\'t exist.'
              : 'The content you\'re looking for doesn\'t exist.',
      icon: LucideIcons.search,
      onRetry: onGoBack,
      retryButtonText: 'Go Back',
    );
  }

  /// Offline state widget
  static Widget offlineState({
    required BuildContext context,
    VoidCallback? onRetry,
  }) {
    final theme = ShadTheme.of(context);
    final isDesktop = ResponsiveLayout.isDesktop(context);

    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isDesktop ? 400 : double.infinity,
        ),
        padding: ResponsiveLayout.getResponsivePadding(context),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: theme.colorScheme.card,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: theme.colorScheme.border),
          ),
          child: ResponsiveColumn(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                LucideIcons.cloudOff,
                size: ResponsiveLayout.getIconSize(context, baseSize: 48),
                color: theme.colorScheme.muted.withValues(alpha: 0.7),
              ),

              Text(
                'You\'re Offline',
                style: theme.textTheme.h4,
                textAlign: TextAlign.center,
              ),

              Text(
                'Some features may not be available while offline.',
                style: theme.textTheme.p,
                textAlign: TextAlign.center,
              ),

              if (onRetry != null)
                SizedBox(
                  width: double.infinity,
                  height: ResponsiveLayout.getButtonHeight(context),
                  child: ShadButton.outline(
                    onPressed: onRetry,
                    child: const Text('Check Connection'),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Loading failed state for specific components
  static Widget loadingFailed({
    required BuildContext context,
    required String componentName,
    VoidCallback? onRetry,
  }) {
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: ResponsiveColumn(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            LucideIcons.refreshCw,
            size: ResponsiveLayout.getIconSize(context, baseSize: 24),
            color: theme.colorScheme.muted,
          ),

          Text(
            'Failed to load $componentName',
            style: theme.textTheme.small,
            textAlign: TextAlign.center,
          ),

          if (onRetry != null)
            ShadButton.ghost(
              onPressed: onRetry,
              size: ShadButtonSize.sm,
              child: const Text('Retry'),
            ),
        ],
      ),
    );
  }

  /// Form validation error widget
  static Widget formValidationError({
    required BuildContext context,
    required List<String> errors,
  }) {
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.destructive.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.destructive.withValues(alpha: 0.3),
        ),
      ),
      child: ResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 4,
        children: [
          ResponsiveRow(
            children: [
              Icon(
                LucideIcons.triangle,
                size: ResponsiveLayout.getIconSize(context, baseSize: 16),
                color: theme.colorScheme.destructive,
              ),
              Text(
                'Please fix the following errors:',
                style: theme.textTheme.small?.copyWith(
                  color: theme.colorScheme.destructive,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          ...errors.map(
            (error) => Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                '• $error',
                style: theme.textTheme.small?.copyWith(
                  color: theme.colorScheme.destructive,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Success state widget
  static Widget successState({
    required BuildContext context,
    required String message,
    String? title,
    VoidCallback? onContinue,
    String? continueButtonText,
  }) {
    final theme = ShadTheme.of(context);
    final isDesktop = ResponsiveLayout.isDesktop(context);

    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isDesktop ? 400 : double.infinity,
        ),
        padding: ResponsiveLayout.getResponsivePadding(context),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: theme.colorScheme.card,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: theme.colorScheme.border),
          ),
          child: ResponsiveColumn(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                LucideIcons.checkCheck,
                size: ResponsiveLayout.getIconSize(context, baseSize: 48),
                color: Colors.green,
              ),

              if (title != null)
                Text(
                  title,
                  style: theme.textTheme.h4?.copyWith(color: Colors.green),
                  textAlign: TextAlign.center,
                ),

              Text(
                message,
                style: theme.textTheme.p,
                textAlign: TextAlign.center,
              ),

              if (onContinue != null)
                SizedBox(
                  width: double.infinity,
                  height: ResponsiveLayout.getButtonHeight(context),
                  child: ShadButton(
                    onPressed: onContinue,
                    child: Text(continueButtonText ?? 'Continue'),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Private helper methods

  static Widget _buildErrorDetails(
    BuildContext context,
    ShadThemeData theme,
    String details,
  ) {
    return ExpansionTile(
      title: Text(
        'Error Details',
        style: theme.textTheme.small?.copyWith(fontWeight: FontWeight.w600),
      ),
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.muted.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            details,
            style: theme.textTheme.small?.copyWith(fontFamily: 'monospace'),
          ),
        ),
      ],
    );
  }
}
