import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:intl/intl.dart';
import '../../data/models/claim_status_model.dart';
import '../../data/models/claimant_claim_model.dart';
import 'claim_status_indicator.dart';

/// Widget to display claim status timeline with visual progression
class StatusTimelineWidget extends StatelessWidget {
  final ClaimStatusHistory? statusHistory;
  final ClaimantClaim claim;

  const StatusTimelineWidget({
    super.key,
    this.statusHistory,
    required this.claim,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current Status Card
          _buildCurrentStatusCard(context, theme),

          const SizedBox(height: 24),

          // Timeline Section
          _buildTimelineSection(context, theme),

          const SizedBox(height: 24),

          // Next Steps Section
          _buildNextStepsSection(context, theme),
        ],
      ),
    );
  }

  Widget _buildCurrentStatusCard(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flag, size: 20, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Current Status',
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    theme.colorScheme.primary.withValues(alpha: 0.1),
                    theme.colorScheme.primary.withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.primary.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DetailedClaimStatusIndicator(status: claim.status),
                  const SizedBox(height: 12),
                  Text(
                    'Current Stage: ${claim.currentStage}',
                    style: theme.textTheme.p.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Last updated: ${_formatDateTime(claim.lastUpdated ?? claim.created)}',
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineSection(BuildContext context, ShadThemeData theme) {
    final timelineItems = _buildTimelineItems();

    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.timeline,
                  size: 20,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Status Timeline',
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            if (timelineItems.isEmpty)
              _buildEmptyTimeline(context, theme)
            else
              _buildTimeline(context, theme, timelineItems),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyTimeline(BuildContext context, ShadThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border, width: 1),
      ),
      child: Column(
        children: [
          Icon(
            Icons.history,
            size: 48,
            color: theme.colorScheme.mutedForeground,
          ),
          const SizedBox(height: 12),
          Text(
            'Status History',
            style: theme.textTheme.h4.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Detailed status history will be available as your claim progresses through different stages.',
            textAlign: TextAlign.center,
            style: theme.textTheme.p.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeline(
    BuildContext context,
    ShadThemeData theme,
    List<TimelineItem> items,
  ) {
    return Column(
      children:
          items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isLast = index == items.length - 1;

            return _buildTimelineItem(context, theme, item, isLast);
          }).toList(),
    );
  }

  Widget _buildTimelineItem(
    BuildContext context,
    ShadThemeData theme,
    TimelineItem item,
    bool isLast,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color:
                    item.isCompleted
                        ? theme.colorScheme.primary
                        : theme.colorScheme.muted,
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      item.isCompleted
                          ? theme.colorScheme.primary
                          : theme.colorScheme.border,
                  width: 2,
                ),
              ),
            ),
            if (!isLast)
              Container(width: 2, height: 40, color: theme.colorScheme.border),
          ],
        ),

        const SizedBox(width: 16),

        // Timeline content
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        item.title,
                        style: theme.textTheme.p.copyWith(
                          fontWeight: FontWeight.w600,
                          color:
                              item.isCompleted
                                  ? theme.colorScheme.foreground
                                  : theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ),
                    if (item.date != null)
                      Text(
                        DateFormat('MMM dd').format(item.date!),
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                  ],
                ),

                if (item.description != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    item.description!,
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ],

                if (item.status != null) ...[
                  const SizedBox(height: 8),
                  CompactClaimStatusIndicator(status: item.status!),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNextStepsSection(BuildContext context, ShadThemeData theme) {
    final nextSteps = _getNextSteps();

    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.next_plan,
                  size: 20,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'What\'s Next?',
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            if (nextSteps.isEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.muted.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: theme.colorScheme.border, width: 1),
                ),
                child: Text(
                  'Next steps will be communicated as your claim progresses.',
                  style: theme.textTheme.p.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
              )
            else
              Column(
                children:
                    nextSteps
                        .map((step) => _buildNextStepItem(context, theme, step))
                        .toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNextStepItem(
    BuildContext context,
    ShadThemeData theme,
    String step,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(child: Text(step, style: theme.textTheme.p)),
        ],
      ),
    );
  }

  List<TimelineItem> _buildTimelineItems() {
    if (statusHistory == null || statusHistory!.statusHistory.isEmpty) {
      return [];
    }

    return statusHistory!.statusHistory.map((status) {
      return TimelineItem(
        title: _getStatusTitle(status.status),
        description: _getStatusDescription(status.status),
        date: status.timestamp,
        status: ClaimStatus.values.firstWhere(
          (s) => s.value == status.status,
          orElse: () => ClaimStatus.draft,
        ),
        isCompleted: true,
      );
    }).toList();
  }

  List<String> _getNextSteps() {
    switch (claim.status) {
      case ClaimStatus.draft:
        return ['Complete and submit your claim application'];
      case ClaimStatus.submitted:
        return [
          'Your claim is being reviewed by our team',
          'You may be contacted for additional information',
        ];
      case ClaimStatus.underReview:
        return [
          'Review is in progress',
          'Estimated completion: 5-10 business days',
        ];
      case ClaimStatus.moreInfoRequired:
        return [
          'Please provide the requested additional information',
          'Check your messages for specific requirements',
        ];
      case ClaimStatus.approved:
        return [
          'Your claim has been approved',
          'Funding arrangements are being processed',
        ];
      case ClaimStatus.funded:
        return ['Your claim is fully funded', 'Legal proceedings can commence'];
      case ClaimStatus.completed:
        return ['Your claim has been completed successfully'];
      case ClaimStatus.rejected:
        return [
          'Review the rejection reasons',
          'Consider appealing if applicable',
        ];
      case ClaimStatus.cancelled:
        return ['Claim has been cancelled'];
    }
  }

  String _getStatusTitle(String status) {
    switch (status) {
      case 'draft':
        return 'Claim Created';
      case 'submitted':
        return 'Claim Submitted';
      case 'under_review':
        return 'Under Review';
      case 'more_info_required':
        return 'Additional Information Required';
      case 'approved':
        return 'Claim Approved';
      case 'funded':
        return 'Claim Funded';
      case 'completed':
        return 'Claim Completed';
      case 'rejected':
        return 'Claim Rejected';
      case 'cancelled':
        return 'Claim Cancelled';
      default:
        return 'Status Update';
    }
  }

  String? _getStatusDescription(String status) {
    switch (status) {
      case 'draft':
        return 'Initial claim created and saved';
      case 'submitted':
        return 'Claim submitted for review';
      case 'under_review':
        return 'Being reviewed by our legal team';
      case 'more_info_required':
        return 'Additional information requested';
      case 'approved':
        return 'Claim approved for funding';
      case 'funded':
        return 'Funding has been secured';
      case 'completed':
        return 'Claim process completed successfully';
      case 'rejected':
        return 'Claim was not approved';
      case 'cancelled':
        return 'Claim was cancelled';
      default:
        return null;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('MMM dd, yyyy \'at\' HH:mm').format(dateTime);
  }
}

/// Timeline item model
class TimelineItem {
  final String title;
  final String? description;
  final DateTime? date;
  final ClaimStatus? status;
  final bool isCompleted;

  const TimelineItem({
    required this.title,
    this.description,
    this.date,
    this.status,
    this.isCompleted = false,
  });
}
