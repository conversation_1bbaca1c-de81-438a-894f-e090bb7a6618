import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../data/models/claimant_claim_model.dart';

/// Widget to display claim status with color-coded indicators
class ClaimStatusIndicator extends StatelessWidget {
  final ClaimStatus status;
  final bool showIcon;
  final bool showProgress;
  final double? size;

  const ClaimStatusIndicator({
    super.key,
    required this.status,
    this.showIcon = true,
    this.showProgress = false,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final statusInfo = _getStatusInfo(status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusInfo.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: statusInfo.color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) ...[
            Icon(statusInfo.icon, size: size ?? 14, color: statusInfo.color),
            const SizedBox(width: 4),
          ],
          Text(
            statusInfo.label,
            style: theme.textTheme.small.copyWith(
              color: statusInfo.color,
              fontWeight: FontWeight.w500,
              fontSize: size != null ? size! * 0.8 : 12,
            ),
          ),
          if (showProgress && statusInfo.progress != null) ...[
            const SizedBox(width: 8),
            SizedBox(
              width: 40,
              height: 4,
              child: LinearProgressIndicator(
                value: statusInfo.progress,
                backgroundColor: statusInfo.color.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation<Color>(statusInfo.color),
              ),
            ),
          ],
        ],
      ),
    );
  }

  _StatusInfo _getStatusInfo(ClaimStatus status) {
    switch (status) {
      case ClaimStatus.draft:
        return _StatusInfo(
          label: 'Draft',
          color: Colors.grey,
          icon: Icons.edit,
          progress: 0.1,
        );
      case ClaimStatus.submitted:
        return _StatusInfo(
          label: 'Submitted',
          color: Colors.blue,
          icon: Icons.send,
          progress: 0.2,
        );
      case ClaimStatus.underReview:
        return _StatusInfo(
          label: 'Under Review',
          color: Colors.orange,
          icon: Icons.schedule,
          progress: 0.4,
        );
      case ClaimStatus.moreInfoRequired:
        return _StatusInfo(
          label: 'More Info Required',
          color: Colors.red,
          icon: Icons.info,
          progress: 0.3,
        );
      case ClaimStatus.approved:
        return _StatusInfo(
          label: 'Approved',
          color: Colors.green,
          icon: Icons.check_circle,
          progress: 0.6,
        );
      case ClaimStatus.rejected:
        return _StatusInfo(
          label: 'Rejected',
          color: Colors.red,
          icon: Icons.cancel,
          progress: 1.0,
        );
      case ClaimStatus.completed:
        return _StatusInfo(
          label: 'Completed',
          color: Colors.green.shade700,
          icon: Icons.check_circle_outline,
          progress: 1.0,
        );
      case ClaimStatus.cancelled:
        return _StatusInfo(
          label: 'Cancelled',
          color: Colors.grey.shade600,
          icon: Icons.block,
          progress: 1.0,
        );
      case ClaimStatus.funded:
        return _StatusInfo(
          label: 'Funded',
          color: Colors.green.shade800,
          icon: Icons.account_balance,
          progress: 0.9,
        );
    }
  }
}

/// Helper class to hold status information
class _StatusInfo {
  final String label;
  final Color color;
  final IconData icon;
  final double? progress;

  const _StatusInfo({
    required this.label,
    required this.color,
    required this.icon,
    this.progress,
  });
}

/// Compact version of status indicator for use in lists
class CompactClaimStatusIndicator extends StatelessWidget {
  final ClaimStatus status;

  const CompactClaimStatusIndicator({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    return ClaimStatusIndicator(
      status: status,
      showIcon: true,
      showProgress: false,
      size: 12,
    );
  }
}

/// Status indicator with progress bar for detailed views
class DetailedClaimStatusIndicator extends StatelessWidget {
  final ClaimStatus status;

  const DetailedClaimStatusIndicator({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    return ClaimStatusIndicator(
      status: status,
      showIcon: true,
      showProgress: true,
      size: 16,
    );
  }
}
