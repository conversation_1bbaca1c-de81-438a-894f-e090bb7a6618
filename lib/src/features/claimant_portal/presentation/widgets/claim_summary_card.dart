import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../data/models/claimant_claim_model.dart';
import 'claim_status_indicator.dart';

/// Card widget to display claim summary information
class ClaimSummaryCard extends StatelessWidget {
  final ClaimantClaim claim;
  final VoidCallback? onTap;
  final bool showActions;

  const ClaimSummaryCard({
    super.key,
    required this.claim,
    this.onTap,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final dateFormatter = DateFormat('MMM dd, yyyy');

    return ShadCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and status
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          claim.title,
                          style: theme.textTheme.h4.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'ID: ${claim.id.substring(0, 8)}...',
                          style: theme.textTheme.small.copyWith(
                            color: theme.colorScheme.mutedForeground,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  CompactClaimStatusIndicator(status: claim.status),
                ],
              ),

              const SizedBox(height: 16),

              // Claim details
              _buildDetailRow(
                context,
                'Current Stage',
                claim.currentStage,
                LucideIcons.layers,
              ),

              const SizedBox(height: 8),

              _buildDetailRow(
                context,
                'Submitted',
                dateFormatter.format(claim.submissionDate),
                LucideIcons.calendar,
              ),

              if (claim.claimType != null) ...[
                const SizedBox(height: 8),
                _buildDetailRow(
                  context,
                  'Type',
                  claim.claimType!,
                  LucideIcons.tag,
                ),
              ],

              if (claim.requiredFundingAmount != null) ...[
                const SizedBox(height: 8),
                _buildDetailRow(
                  context,
                  'Funding Required',
                  '£${NumberFormat('#,##0').format(claim.requiredFundingAmount)}',
                  LucideIcons.dollarSign,
                ),
              ],

              if (claim.description != null) ...[
                const SizedBox(height: 12),
                Text(
                  claim.description!,
                  style: theme.textTheme.p.copyWith(
                    color: theme.colorScheme.mutedForeground,
                    fontSize: 14,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              if (showActions) ...[
                const SizedBox(height: 16),
                _buildActionButtons(context, theme),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = ShadTheme.of(context);

    return Row(
      children: [
        Icon(icon, size: 14, color: theme.colorScheme.mutedForeground),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: theme.textTheme.small.copyWith(
            color: theme.colorScheme.mutedForeground,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.small.copyWith(fontWeight: FontWeight.w600),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, ShadThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: ShadButton.outline(
            onPressed: onTap,
            size: ShadButtonSize.sm,
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(LucideIcons.eye, size: 14),
                SizedBox(width: 6),
                Text('View Details'),
              ],
            ),
          ),
        ),
        const SizedBox(width: 8),
        ShadButton.ghost(
          onPressed: () => _showQuickActions(context),
          size: ShadButtonSize.sm,
          child: Icon(LucideIcons.ellipsisVertical, size: 16),
        ),
      ],
    );
  }

  void _showQuickActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _QuickActionsSheet(claim: claim),
    );
  }
}

/// Quick actions bottom sheet for claim cards
class _QuickActionsSheet extends StatelessWidget {
  final ClaimantClaim claim;

  const _QuickActionsSheet({required this.claim});

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Quick Actions', style: theme.textTheme.h4),
          const SizedBox(height: 16),

          ListTile(
            leading: const Icon(LucideIcons.eye),
            title: const Text('View Details'),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(
                context,
                '/claimant/claim-detail',
                arguments: claim.id,
              );
            },
          ),

          ListTile(
            leading: const Icon(LucideIcons.messageSquare),
            title: const Text('Contact Support'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Open support chat
            },
          ),

          ListTile(
            leading: const Icon(LucideIcons.download),
            title: const Text('Download Documents'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Download claim documents
            },
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

/// Compact version of claim card for dense lists
class CompactClaimSummaryCard extends StatelessWidget {
  final ClaimantClaim claim;
  final VoidCallback? onTap;

  const CompactClaimSummaryCard({super.key, required this.claim, this.onTap});

  @override
  Widget build(BuildContext context) {
    return ClaimSummaryCard(claim: claim, onTap: onTap, showActions: false);
  }
}
