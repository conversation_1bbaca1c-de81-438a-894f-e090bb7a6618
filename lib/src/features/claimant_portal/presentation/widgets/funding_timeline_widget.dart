import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/funding_status_model.dart';

class FundingTimelineWidget extends StatelessWidget {
  final List<FundingTimelineEvent> events;

  const FundingTimelineWidget({super.key, required this.events});

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    if (events.isEmpty) {
      return _buildEmptyTimeline(theme);
    }

    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  LucideIcons.clock,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Recent Activity',
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Timeline events
            ...events
                .take(10)
                .map((event) => _buildTimelineEvent(theme, event)),

            // Show more indicator if there are more events
            if (events.length > 10) ...[
              const SizedBox(height: 16),
              Center(
                child: Text(
                  '${events.length - 10} more events...',
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineEvent(ShadThemeData theme, FundingTimelineEvent event) {
    final isLast =
        events.indexOf(event) == events.length - 1 ||
        events.indexOf(event) == 9; // Last visible event

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator
          Column(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: _getStageColor(theme, event.stage),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: _getStageColor(theme, event.stage).withOpacity(0.3),
                    width: 2,
                  ),
                ),
                child: Icon(
                  _getStageIcon(event.stage),
                  size: 16,
                  color: Colors.white,
                ),
              ),
              if (!isLast)
                Container(
                  width: 2,
                  height: 40,
                  color: theme.colorScheme.border,
                ),
            ],
          ),
          const SizedBox(width: 16),

          // Event content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event.title,
                  style: theme.textTheme.p.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  event.description,
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      LucideIcons.calendar,
                      size: 12,
                      color: theme.colorScheme.mutedForeground,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatEventDate(event.date),
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                        fontSize: 11,
                      ),
                    ),
                    if (event.formattedAmount != null) ...[
                      const SizedBox(width: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          event.formattedAmount!,
                          style: theme.textTheme.small.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w500,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyTimeline(ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              LucideIcons.clock,
              size: 48,
              color: theme.colorScheme.mutedForeground,
            ),
            const SizedBox(height: 16),
            Text('No Timeline Events', style: theme.textTheme.h4),
            const SizedBox(height: 8),
            Text(
              'Funding timeline events will appear here as your funding progresses.',
              style: theme.textTheme.p.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getStageColor(ShadThemeData theme, FundingStage stage) {
    switch (stage) {
      case FundingStage.application:
        return Colors.blue;
      case FundingStage.review:
        return Colors.orange;
      case FundingStage.commitment:
        return theme.colorScheme.primary;
      case FundingStage.approval:
        return Colors.green;
      case FundingStage.deployment:
        return Colors.purple;
      case FundingStage.completion:
        return Colors.teal;
    }
  }

  IconData _getStageIcon(FundingStage stage) {
    switch (stage) {
      case FundingStage.application:
        return LucideIcons.fileText;
      case FundingStage.review:
        return LucideIcons.search;
      case FundingStage.commitment:
        return LucideIcons.handshake;
      case FundingStage.approval:
        return LucideIcons.check;
      case FundingStage.deployment:
        return LucideIcons.trendingUp;
      case FundingStage.completion:
        return LucideIcons.checkCheck;
    }
  }

  String _formatEventDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks != 1 ? 's' : ''} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months != 1 ? 's' : ''} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years year${years != 1 ? 's' : ''} ago';
    }
  }
}
