import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../providers/profile_provider.dart';

/// Widget for managing user preferences
class PreferencesWidget extends ConsumerStatefulWidget {
  const PreferencesWidget({super.key});

  @override
  ConsumerState<PreferencesWidget> createState() => _PreferencesWidgetState();
}

class _PreferencesWidgetState extends ConsumerState<PreferencesWidget> {
  late Map<String, dynamic> _preferences;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _initializePreferences();
  }

  void _initializePreferences() {
    final profile = ref.read(profileProvider).profile;
    _preferences = Map<String, dynamic>.from(
      profile?.notificationPreferences ?? _getDefaultPreferences(),
    );
  }

  Map<String, dynamic> _getDefaultPreferences() {
    return {
      'email_notifications': true,
      'push_notifications': true,
      'sms_notifications': false,
      'claim_updates': true,
      'funding_updates': true,
      'document_updates': true,
      'system_announcements': true,
      'marketing_emails': false,
      'weekly_digest': true,
      'language': 'en',
      'timezone': 'Europe/London',
      'privacy_public_profile': false,
      'privacy_share_data': false,
    };
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(profileProvider);

    return ShadCard(
      title: Text('Preferences', style: theme.textTheme.h4),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Notification Preferences
            _buildSection('Notification Preferences', [
              _buildSwitchTile(
                'Email Notifications',
                'Receive notifications via email',
                'email_notifications',
                LucideIcons.mail,
              ),
              _buildSwitchTile(
                'Push Notifications',
                'Receive push notifications on your device',
                'push_notifications',
                LucideIcons.bell,
              ),
              _buildSwitchTile(
                'SMS Notifications',
                'Receive important updates via SMS',
                'sms_notifications',
                LucideIcons.messageSquare,
              ),
            ], theme),

            const SizedBox(height: 24),

            // Content Preferences
            _buildSection('Content Preferences', [
              _buildSwitchTile(
                'Claim Updates',
                'Notifications about your claims',
                'claim_updates',
                LucideIcons.fileText,
              ),
              _buildSwitchTile(
                'Funding Updates',
                'Updates about funding status',
                'funding_updates',
                LucideIcons.creditCard,
              ),
              _buildSwitchTile(
                'Document Updates',
                'Notifications when documents are added or updated',
                'document_updates',
                LucideIcons.file,
              ),
              _buildSwitchTile(
                'System Announcements',
                'Important system updates and announcements',
                'system_announcements',
                LucideIcons.megaphone,
              ),
              _buildSwitchTile(
                'Marketing Emails',
                'Promotional emails and newsletters',
                'marketing_emails',
                LucideIcons.tag,
              ),
              _buildSwitchTile(
                'Weekly Digest',
                'Weekly summary of your account activity',
                'weekly_digest',
                LucideIcons.calendar,
              ),
            ], theme),

            const SizedBox(height: 24),

            // Privacy Preferences
            _buildSection('Privacy Preferences', [
              _buildSwitchTile(
                'Public Profile',
                'Make your profile visible to other users',
                'privacy_public_profile',
                LucideIcons.eye,
              ),
              _buildSwitchTile(
                'Share Data for Analytics',
                'Help improve our services with anonymous usage data',
                'privacy_share_data',
                LucideIcons.activity,
              ),
            ], theme),

            const SizedBox(height: 32),

            // Save Button
            if (_hasChanges)
              ShadButton(
                onPressed: profileState.isUpdating ? null : _savePreferences,
                width: double.infinity,
                child:
                    profileState.isUpdating
                        ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                            SizedBox(width: 8),
                            Text('Saving...'),
                          ],
                        )
                        : const Text('Save Preferences'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    String title,
    List<Widget> children,
    ShadThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: theme.textTheme.h4),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    String key,
    IconData icon,
  ) {
    return Builder(
      builder: (context) {
        final theme = ShadTheme.of(context);
        final value = _preferences[key] as bool? ?? false;

        return Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Row(
            children: [
              Icon(icon, size: 20, color: theme.colorScheme.mutedForeground),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.p.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              ShadSwitch(
                value: value,
                onChanged: (newValue) {
                  setState(() {
                    _preferences[key] = newValue;
                    _hasChanges = true;
                  });
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _savePreferences() async {
    final success = await ref
        .read(profileProvider.notifier)
        .updateNotificationPreferences(_preferences);

    if (mounted) {
      if (success) {
        setState(() {
          _hasChanges = false;
        });
        ShadToaster.of(context).show(
          const ShadToast(
            title: Text('Success'),
            description: Text('Preferences saved successfully'),
          ),
        );
      } else {
        // Error is handled by the provider state
      }
    }
  }
}
