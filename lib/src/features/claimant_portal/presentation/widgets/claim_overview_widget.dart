import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:intl/intl.dart';
import '../../data/models/claimant_claim_model.dart';
import 'claim_status_indicator.dart';

/// Widget to display comprehensive claim overview information
class ClaimOverviewWidget extends StatelessWidget {
  final ClaimantClaim claim;
  final VoidCallback? onEditProfile;

  const ClaimOverviewWidget({
    super.key,
    required this.claim,
    this.onEditProfile,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Claim Header
          _buildClaimHeader(context, theme),

          const SizedBox(height: 24),

          // Status Section
          _buildStatusSection(context, theme),

          const SizedBox(height: 24),

          // Details Grid
          _buildDetailsGrid(context, theme),

          const SizedBox(height: 24),

          // Description Section
          if (claim.description != null) ...[
            _buildDescriptionSection(context, theme),
            const SizedBox(height: 24),
          ],

          // Funding Information
          _buildFundingSection(context, theme),

          const SizedBox(height: 24),

          // Quick Actions
          _buildQuickActions(context, theme),
        ],
      ),
    );
  }

  Widget _buildClaimHeader(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        claim.title,
                        style: theme.textTheme.h2.copyWith(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Claim ID: ${claim.id.substring(0, 8)}...',
                        style: theme.textTheme.p.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                ),
                DetailedClaimStatusIndicator(status: claim.status),
              ],
            ),

            const SizedBox(height: 16),

            // Key Information Row
            Wrap(
              spacing: 24,
              runSpacing: 12,
              children: [
                _buildInfoChip(
                  context,
                  'Submitted',
                  DateFormat('MMM dd, yyyy').format(claim.submissionDate),
                  Icons.calendar_today,
                ),
                _buildInfoChip(
                  context,
                  'Stage',
                  claim.currentStage,
                  Icons.timeline,
                ),
                if (claim.claimType != null)
                  _buildInfoChip(
                    context,
                    'Type',
                    claim.claimType!,
                    Icons.category,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.track_changes,
                  size: 20,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Current Status',
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.muted.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.colorScheme.border, width: 1),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DetailedClaimStatusIndicator(status: claim.status),
                  const SizedBox(height: 12),
                  Text(
                    'Stage: ${claim.currentStage}',
                    style: theme.textTheme.p.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (claim.lastUpdated != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Last updated: ${DateFormat('MMM dd, yyyy \'at\' HH:mm').format(claim.lastUpdated!)}',
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsGrid(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Claim Details',
              style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.w600),
            ),

            const SizedBox(height: 16),

            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 3,
              crossAxisSpacing: 16,
              mainAxisSpacing: 12,
              children: [
                _buildDetailItem(
                  context,
                  'Submission Date',
                  DateFormat('MMM dd, yyyy').format(claim.submissionDate),
                ),
                _buildDetailItem(context, 'Current Stage', claim.currentStage),
                if (claim.claimType != null)
                  _buildDetailItem(context, 'Claim Type', claim.claimType!),
                if (claim.claimIndustry != null)
                  _buildDetailItem(context, 'Industry', claim.claimIndustry!),
                _buildDetailItem(
                  context,
                  'Created',
                  DateFormat('MMM dd, yyyy').format(claim.created),
                ),
                if (claim.lastUpdated != null)
                  _buildDetailItem(
                    context,
                    'Last Updated',
                    DateFormat('MMM dd, yyyy').format(claim.lastUpdated!),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionSection(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Description',
              style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.w600),
            ),

            const SizedBox(height: 12),

            Text(claim.description!, style: theme.textTheme.p),
          ],
        ),
      ),
    );
  }

  Widget _buildFundingSection(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_balance,
                  size: 20,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Funding Information',
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            if (claim.requiredFundingAmount != null) ...[
              _buildFundingItem(
                context,
                'Required Funding',
                '£${NumberFormat('#,##0').format(claim.requiredFundingAmount)}',
                Icons.attach_money,
              ),
              const SizedBox(height: 12),
            ],

            _buildFundingItem(
              context,
              'Funding Status',
              claim.isFunded ? 'Funded' : 'Pending',
              claim.isFunded ? Icons.check_circle : Icons.pending,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.w600),
            ),

            const SizedBox(height: 16),

            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                ShadButton.outline(
                  onPressed: () {
                    // TODO: Navigate to documents
                  },
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.description, size: 16),
                      SizedBox(width: 8),
                      Text('View Documents'),
                    ],
                  ),
                ),
                ShadButton.outline(
                  onPressed: () {
                    // TODO: Open chat
                  },
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.chat, size: 16),
                      SizedBox(width: 8),
                      Text('Contact Support'),
                    ],
                  ),
                ),
                if (onEditProfile != null)
                  ShadButton.outline(
                    onPressed: onEditProfile,
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.edit, size: 16),
                        SizedBox(width: 8),
                        Text('Edit Profile'),
                      ],
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: theme.colorScheme.border, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: theme.colorScheme.mutedForeground),
          const SizedBox(width: 6),
          Flexible(
            child: Text(
              '$label: $value',
              style: theme.textTheme.small.copyWith(
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(BuildContext context, String label, String value) {
    final theme = ShadTheme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.small.copyWith(
            color: theme.colorScheme.mutedForeground,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.p.copyWith(fontWeight: FontWeight.w600),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildFundingItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = ShadTheme.of(context);

    return Row(
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.mutedForeground),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: theme.textTheme.p.copyWith(
            color: theme.colorScheme.mutedForeground,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.p.copyWith(fontWeight: FontWeight.w600),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }
}
