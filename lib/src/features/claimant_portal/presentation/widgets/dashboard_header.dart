import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/claimant_auth_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/dashboard_provider.dart';

/// Dashboard header widget for claimant portal
class DashboardHeader extends ConsumerWidget {
  const DashboardHeader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final authState = ref.watch(claimantAuthProvider);
    final dashboardState = ref.watch(claimantDashboardProvider);
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return Container(
      padding: EdgeInsets.all(isDesktop ? 24.0 : 16.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.background,
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child:
          isDesktop
              ? _buildDesktopHeader(context, theme, authState, dashboardState)
              : _buildMobileHeader(context, theme, authState, dashboardState),
    );
  }

  Widget _buildDesktopHeader(
    BuildContext context,
    ShadThemeData theme,
    ClaimantAuthState authState,
    ClaimantDashboardState dashboardState,
  ) {
    return Row(
      children: [
        // Welcome section
        Expanded(child: _buildWelcomeSection(context, theme, authState)),

        // Actions section
        Row(
          children: [
            _buildRefreshButton(context, theme, dashboardState),
            const SizedBox(width: 16),
            _buildUserMenu(context, theme, authState),
          ],
        ),
      ],
    );
  }

  Widget _buildMobileHeader(
    BuildContext context,
    ShadThemeData theme,
    ClaimantAuthState authState,
    ClaimantDashboardState dashboardState,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Top row with welcome and actions
        Row(
          children: [
            Expanded(child: _buildWelcomeSection(context, theme, authState)),
            _buildUserMenu(context, theme, authState),
          ],
        ),

        // Bottom row with refresh and last updated
        if (dashboardState.lastUpdated != null) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              _buildRefreshButton(context, theme, dashboardState),
              const Spacer(),
              _buildLastUpdated(context, theme, dashboardState),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildWelcomeSection(
    BuildContext context,
    ShadThemeData theme,
    ClaimantAuthState authState,
  ) {
    final displayName = authState.profile?.displayName ?? 'Claimant';
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Welcome back, $displayName',
          style:
              isDesktop
                  ? theme.textTheme.h2.copyWith(fontWeight: FontWeight.bold)
                  : theme.textTheme.h3.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 4),
        Text(
          'Stay updated with your claims',
          style: theme.textTheme.p.copyWith(
            color: theme.colorScheme.mutedForeground,
          ),
        ),
      ],
    );
  }

  Widget _buildRefreshButton(
    BuildContext context,
    ShadThemeData theme,
    ClaimantDashboardState dashboardState,
  ) {
    return Consumer(
      builder: (context, ref, child) {
        return ShadButton.ghost(
          onPressed:
              dashboardState.isRefreshing
                  ? null
                  : () {
                    ref.read(claimantDashboardProvider.notifier).refresh();
                  },
          child:
              dashboardState.isRefreshing
                  ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.foreground,
                      ),
                    ),
                  )
                  : Icon(
                    LucideIcons.refreshCw,
                    size: 16,
                    color: theme.colorScheme.foreground,
                  ),
        );
      },
    );
  }

  Widget _buildUserMenu(
    BuildContext context,
    ShadThemeData theme,
    ClaimantAuthState authState,
  ) {
    return CircleAvatar(
      radius: 16,
      backgroundColor: theme.colorScheme.primary,
      child: Text(
        authState.profile?.displayName.isNotEmpty == true
            ? authState.profile!.displayName[0].toUpperCase()
            : 'C',
        style: TextStyle(
          color: theme.colorScheme.primaryForeground,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildLastUpdated(
    BuildContext context,
    ShadThemeData theme,
    ClaimantDashboardState dashboardState,
  ) {
    if (dashboardState.lastUpdated == null) return const SizedBox.shrink();

    final now = DateTime.now();
    final lastUpdated = dashboardState.lastUpdated!;
    final difference = now.difference(lastUpdated);

    String timeAgo;
    if (difference.inMinutes < 1) {
      timeAgo = 'Just now';
    } else if (difference.inHours < 1) {
      timeAgo = '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      timeAgo = '${difference.inHours}h ago';
    } else {
      timeAgo = '${difference.inDays}d ago';
    }

    return Text(
      'Updated $timeAgo',
      style: theme.textTheme.small.copyWith(
        color: theme.colorScheme.mutedForeground,
      ),
    );
  }
}
