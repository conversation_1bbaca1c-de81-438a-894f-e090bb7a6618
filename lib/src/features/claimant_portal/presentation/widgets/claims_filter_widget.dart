import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../data/models/claimant_claim_model.dart';

/// Filter options for claims list
class ClaimsFilterOptions {
  final Set<ClaimStatus> statusFilters;
  final String? claimType;
  final DateTimeRange? dateRange;
  final ClaimsSortOption sortOption;
  final bool sortAscending;

  const ClaimsFilterOptions({
    this.statusFilters = const {},
    this.claimType,
    this.dateRange,
    this.sortOption = ClaimsSortOption.submissionDate,
    this.sortAscending = false,
  });

  ClaimsFilterOptions copyWith({
    Set<ClaimStatus>? statusFilters,
    String? claimType,
    DateTimeRange? dateRange,
    ClaimsSortOption? sortOption,
    bool? sortAscending,
  }) {
    return ClaimsFilterOptions(
      statusFilters: statusFilters ?? this.statusFilters,
      claimType: claimType ?? this.claimType,
      dateRange: dateRange ?? this.dateRange,
      sortOption: sortOption ?? this.sortOption,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  bool get hasActiveFilters {
    return statusFilters.isNotEmpty || claimType != null || dateRange != null;
  }

  ClaimsFilterOptions clearFilters() {
    return const ClaimsFilterOptions();
  }
}

/// Sort options for claims
enum ClaimsSortOption {
  submissionDate('Submission Date'),
  title('Title'),
  status('Status'),
  stage('Current Stage'),
  fundingAmount('Funding Amount');

  const ClaimsSortOption(this.label);
  final String label;
}

/// Widget for filtering and sorting claims
class ClaimsFilterWidget extends StatefulWidget {
  final ClaimsFilterOptions initialOptions;
  final Function(ClaimsFilterOptions) onFiltersChanged;

  const ClaimsFilterWidget({
    super.key,
    required this.initialOptions,
    required this.onFiltersChanged,
  });

  @override
  State<ClaimsFilterWidget> createState() => _ClaimsFilterWidgetState();
}

class _ClaimsFilterWidgetState extends State<ClaimsFilterWidget> {
  late ClaimsFilterOptions _currentOptions;

  @override
  void initState() {
    super.initState();
    _currentOptions = widget.initialOptions;
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              Text('Filter & Sort Claims', style: theme.textTheme.h4),
              const Spacer(),
              if (_currentOptions.hasActiveFilters)
                ShadButton.ghost(
                  onPressed: _clearAllFilters,
                  size: ShadButtonSize.sm,
                  child: const Text('Clear All'),
                ),
              ShadButton.ghost(
                onPressed: () => Navigator.pop(context),
                size: ShadButtonSize.sm,
                child: const Icon(LucideIcons.x),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Scrollable content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status Filters
                  _buildStatusFilters(theme),

                  const SizedBox(height: 24),

                  // Claim Type Filter
                  _buildClaimTypeFilter(theme),

                  const SizedBox(height: 24),

                  // Date Range Filter
                  _buildDateRangeFilter(theme),

                  const SizedBox(height: 24),

                  // Sort Options
                  _buildSortOptions(theme),

                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),

          // Apply Button (fixed at bottom)
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: SizedBox(
              width: double.infinity,
              child: ShadButton(
                onPressed: _applyFilters,
                child: const Text('Apply Filters'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilters(ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Status', style: theme.textTheme.h4.copyWith(fontSize: 16)),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              ClaimStatus.values.map((status) {
                final isSelected = _currentOptions.statusFilters.contains(
                  status,
                );
                return FilterChip(
                  label: Text(_getStatusLabel(status)),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      final newFilters = Set<ClaimStatus>.from(
                        _currentOptions.statusFilters,
                      );
                      if (selected) {
                        newFilters.add(status);
                      } else {
                        newFilters.remove(status);
                      }
                      _currentOptions = _currentOptions.copyWith(
                        statusFilters: newFilters,
                      );
                    });
                  },
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildClaimTypeFilter(ShadThemeData theme) {
    final claimTypes = [
      'Personal Injury',
      'Employment',
      'Commercial',
      'Property',
      'Other',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Claim Type', style: theme.textTheme.h4.copyWith(fontSize: 16)),
        const SizedBox(height: 12),
        ShadSelect<String>(
          placeholder: const Text('Select claim type'),
          options: [
            const ShadOption(value: '', child: Text('All Types')),
            ...claimTypes.map(
              (type) => ShadOption(value: type, child: Text(type)),
            ),
          ],
          selectedOptionBuilder:
              (context, value) => Text(value.isEmpty ? 'All Types' : value),
          onChanged: (value) {
            setState(() {
              _currentOptions = _currentOptions.copyWith(
                claimType: value?.isEmpty == true ? null : value,
              );
            });
          },
        ),
      ],
    );
  }

  Widget _buildDateRangeFilter(ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Submission Date Range',
          style: theme.textTheme.h4.copyWith(fontSize: 16),
        ),
        const SizedBox(height: 12),
        ShadButton.outline(
          onPressed: _selectDateRange,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(_getDateRangeText()),
              const Icon(LucideIcons.calendar, size: 16),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSortOptions(ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Sort By', style: theme.textTheme.h4.copyWith(fontSize: 16)),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ShadSelect<ClaimsSortOption>(
                placeholder: const Text('Sort by'),
                options:
                    ClaimsSortOption.values
                        .map(
                          (option) => ShadOption(
                            value: option,
                            child: Text(option.label),
                          ),
                        )
                        .toList(),
                selectedOptionBuilder: (context, value) => Text(value.label),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _currentOptions = _currentOptions.copyWith(
                        sortOption: value,
                      );
                    });
                  }
                },
              ),
            ),
            const SizedBox(width: 12),
            ShadButton.outline(
              onPressed: _toggleSortOrder,
              child: Icon(
                _currentOptions.sortAscending
                    ? LucideIcons.arrowUp
                    : LucideIcons.arrowDown,
                size: 16,
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getStatusLabel(ClaimStatus status) {
    switch (status) {
      case ClaimStatus.draft:
        return 'Draft';
      case ClaimStatus.submitted:
        return 'Submitted';
      case ClaimStatus.underReview:
        return 'Under Review';
      case ClaimStatus.moreInfoRequired:
        return 'More Info Required';
      case ClaimStatus.approved:
        return 'Approved';
      case ClaimStatus.rejected:
        return 'Rejected';
      case ClaimStatus.completed:
        return 'Completed';
      case ClaimStatus.cancelled:
        return 'Cancelled';
      case ClaimStatus.funded:
        return 'Funded';
    }
  }

  String _getDateRangeText() {
    if (_currentOptions.dateRange == null) {
      return 'Select date range';
    }
    final start = _currentOptions.dateRange!.start;
    final end = _currentOptions.dateRange!.end;
    return '${start.day}/${start.month}/${start.year} - ${end.day}/${end.month}/${end.year}';
  }

  void _selectDateRange() async {
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _currentOptions.dateRange,
    );

    if (dateRange != null) {
      setState(() {
        _currentOptions = _currentOptions.copyWith(dateRange: dateRange);
      });
    }
  }

  void _toggleSortOrder() {
    setState(() {
      _currentOptions = _currentOptions.copyWith(
        sortAscending: !_currentOptions.sortAscending,
      );
    });
  }

  void _clearAllFilters() {
    setState(() {
      _currentOptions = _currentOptions.clearFilters();
    });
  }

  void _applyFilters() {
    widget.onFiltersChanged(_currentOptions);
    Navigator.pop(context);
  }
}
