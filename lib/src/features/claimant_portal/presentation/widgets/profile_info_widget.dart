import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/utils/profile_validation.dart';
import '../providers/profile_provider.dart';
import 'profile_picture_widget.dart';

/// Widget for displaying profile information
class ProfileInfoWidget extends ConsumerWidget {
  final VoidCallback? onEditPressed;

  const ProfileInfoWidget({super.key, this.onEditPressed});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(profileProvider);

    if (profileState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (profileState.profile == null) {
      return ShadCard(
        title: Text('Profile Information', style: theme.textTheme.h4),
        child: const Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('No profile information available'),
        ),
      );
    }

    final profile = profileState.profile!;

    return ShadCard(
      title: Text('Profile Information', style: theme.textTheme.h4),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile picture and basic info
            LayoutBuilder(
              builder: (context, constraints) {
                // Use column layout for narrow screens
                if (constraints.maxWidth < 400) {
                  return Column(
                    children: [
                      // Profile picture centered
                      const ProfilePictureWidget(
                        size: 80,
                        showEditButton: false,
                      ),
                      const SizedBox(height: 16),
                      // Info and edit button
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            profile.displayName,
                            style: theme.textTheme.h3,
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            profile.displayEmail,
                            style: theme.textTheme.p.copyWith(
                              color: theme.colorScheme.mutedForeground,
                            ),
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          ),
                          const SizedBox(height: 8),
                          _buildStatusBadge(profile, theme),
                          if (onEditPressed != null) ...[
                            const SizedBox(height: 16),
                            ShadButton.outline(
                              onPressed: onEditPressed,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(LucideIcons.pencil, size: 16),
                                  SizedBox(width: 8),
                                  Text('Edit'),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  );
                } else {
                  // Use row layout for wider screens
                  return Row(
                    children: [
                      const ProfilePictureWidget(
                        size: 80,
                        showEditButton: false,
                      ),
                      const SizedBox(width: 24),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              profile.displayName,
                              style: theme.textTheme.h3,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              profile.displayEmail,
                              style: theme.textTheme.p.copyWith(
                                color: theme.colorScheme.mutedForeground,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                            ),
                            const SizedBox(height: 8),
                            _buildStatusBadge(profile, theme),
                          ],
                        ),
                      ),
                      if (onEditPressed != null) ...[
                        const SizedBox(width: 16),
                        ShadButton.outline(
                          onPressed: onEditPressed,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(LucideIcons.pencil, size: 16),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                      ],
                    ],
                  );
                }
              },
            ),

            const SizedBox(height: 32),

            // Personal Information Section
            _buildSection('Personal Information', [
              _buildInfoRow(
                'Full Name',
                profile.name ?? 'Not provided',
                LucideIcons.user,
              ),
              _buildInfoRow(
                'First Name',
                profile.firstName ?? 'Not provided',
                LucideIcons.user,
              ),
              _buildInfoRow(
                'Last Name',
                profile.lastName ?? 'Not provided',
                LucideIcons.user,
              ),
              _buildInfoRow(
                'Email',
                profile.email ?? 'Not provided',
                LucideIcons.mail,
              ),
              _buildInfoRow(
                'Phone Number',
                profile.phoneNumber != null
                    ? ProfileValidation.formatPhoneNumber(profile.phoneNumber!)
                    : 'Not provided',
                LucideIcons.phone,
              ),
            ], theme),

            const SizedBox(height: 24),

            // Address Information Section
            _buildSection('Address Information', [
              _buildInfoRow(
                'Address',
                profile.address ?? 'Not provided',
                LucideIcons.mapPin,
              ),
            ], theme),

            const SizedBox(height: 24),

            // Account Information Section
            _buildSection('Account Information', [
              _buildInfoRow(
                'Associated Claims',
                '${profile.associatedClaimIds.length} claim${profile.associatedClaimIds.length != 1 ? 's' : ''}',
                LucideIcons.fileText,
              ),
              _buildInfoRow(
                'Member Since',
                _formatDate(profile.created),
                LucideIcons.calendar,
              ),
              _buildInfoRow(
                'Last Updated',
                _formatDate(profile.updated),
                LucideIcons.clock,
              ),
            ], theme),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    String title,
    List<Widget> children,
    ShadThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: theme.textTheme.h4),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Builder(
      builder: (context) {
        final theme = ShadTheme.of(context);
        return Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(icon, size: 16, color: theme.colorScheme.mutedForeground),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      value,
                      style: theme.textTheme.p,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusBadge(ClaimantProfile profile, ShadThemeData theme) {
    final isComplete = profile.isProfileComplete;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color:
            isComplete
                ? theme.colorScheme.primary.withValues(alpha: 0.1)
                : theme.colorScheme.destructive.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isComplete
                  ? theme.colorScheme.primary.withValues(alpha: 0.2)
                  : theme.colorScheme.destructive.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isComplete ? LucideIcons.check : LucideIcons.info,
            size: 12,
            color:
                isComplete
                    ? theme.colorScheme.primary
                    : theme.colorScheme.destructive,
          ),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              isComplete ? 'Complete' : 'Incomplete',
              style: theme.textTheme.small.copyWith(
                color:
                    isComplete
                        ? theme.colorScheme.primary
                        : theme.colorScheme.destructive,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
