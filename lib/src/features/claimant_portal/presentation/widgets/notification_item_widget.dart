import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_notification_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/claimant_notifications_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/presentation/pages/notification_detail_page.dart';

/// Widget for displaying individual notification items in lists
class NotificationItemWidget extends ConsumerWidget {
  final ClaimantNotificationModel notification;
  final VoidCallback? onTap;
  final bool showClaimInfo;
  final bool compact;

  const NotificationItemWidget({
    super.key,
    required this.notification,
    this.onTap,
    this.showClaimInfo = true,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      padding: EdgeInsets.zero,
      child: InkWell(
        onTap: () => _handleTap(context, ref),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: EdgeInsets.all(compact ? 12 : 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color:
                notification.isRead
                    ? Colors.transparent
                    : theme.colorScheme.primary.withOpacity(0.02),
            border:
                notification.isRead
                    ? null
                    : Border.all(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      width: 1,
                    ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Notification icon and priority indicator
              _buildIconSection(theme),
              const SizedBox(width: 12),

              // Content section
              Expanded(child: _buildContentSection(theme)),

              // Actions and metadata
              _buildActionsSection(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIconSection(ShadThemeData theme) {
    final iconData = _getIconForType(notification.notificationType);
    final iconColor = _getColorForType(notification.notificationType, theme);
    final priorityColor = _getColorForPriority(
      notification.priorityLevel,
      theme,
    );

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          width: compact ? 36 : 40,
          height: compact ? 36 : 40,
          decoration: BoxDecoration(
            color: iconColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(iconData, size: compact ? 18 : 20, color: iconColor),
        ),

        // Priority indicator
        if (notification.isHighPriority)
          Positioned(
            right: -2,
            top: -2,
            child: Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: priorityColor,
                shape: BoxShape.circle,
                border: Border.all(
                  color: theme.colorScheme.background,
                  width: 1,
                ),
              ),
              child:
                  notification.isUrgent
                      ? Icon(
                        LucideIcons.triangle,
                        size: 6,
                        color: theme.colorScheme.background,
                      )
                      : null,
            ),
          ),

        // Unread indicator
        if (!notification.isRead)
          Positioned(
            left: -2,
            top: -2,
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                shape: BoxShape.circle,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildContentSection(ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and type
        Row(
          children: [
            Expanded(
              child: Text(
                notification.title,
                style: (compact ? theme.textTheme.small : theme.textTheme.p)
                    .copyWith(
                      fontWeight:
                          notification.isRead
                              ? FontWeight.normal
                              : FontWeight.w600,
                      color:
                          notification.isRead
                              ? theme.colorScheme.foreground
                              : theme.colorScheme.primary,
                    ),
                maxLines: compact ? 1 : 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getColorForType(
                  notification.notificationType,
                  theme,
                ).withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                notification.typeDisplayName,
                style: theme.textTheme.small.copyWith(
                  fontSize: 10,
                  color: _getColorForType(notification.notificationType, theme),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 4),

        // Message preview
        Text(
          notification.message,
          style: theme.textTheme.small.copyWith(
            color: theme.colorScheme.mutedForeground,
            height: 1.3,
          ),
          maxLines: compact ? 1 : 2,
          overflow: TextOverflow.ellipsis,
        ),

        // Claim info and metadata
        if (!compact) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              // Claim info
              if (showClaimInfo && notification.claimId != null) ...[
                Icon(
                  LucideIcons.fileText,
                  size: 12,
                  color: theme.colorScheme.mutedForeground,
                ),
                const SizedBox(width: 4),
                Text(
                  'Claim ${notification.claimId!.substring(0, 8)}...',
                  style: theme.textTheme.small.copyWith(
                    fontSize: 11,
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
                const SizedBox(width: 12),
              ],

              // Priority
              if (notification.isHighPriority) ...[
                Icon(
                  notification.isUrgent
                      ? LucideIcons.triangle
                      : LucideIcons.circle,
                  size: 12,
                  color: _getColorForPriority(
                    notification.priorityLevel,
                    theme,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  notification.priorityDisplayName,
                  style: theme.textTheme.small.copyWith(
                    fontSize: 11,
                    color: _getColorForPriority(
                      notification.priorityLevel,
                      theme,
                    ),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildActionsSection(ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Time ago
        Text(
          notification.timeAgo,
          style: theme.textTheme.small.copyWith(
            fontSize: compact ? 10 : 11,
            color: theme.colorScheme.mutedForeground,
          ),
        ),

        if (!compact) ...[
          const SizedBox(height: 8),

          // Quick actions
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Mark as read/unread button
              if (!notification.isRead)
                Builder(
                  builder:
                      (context) => IconButton(
                        onPressed: () => _markAsRead(context),
                        icon: Icon(
                          LucideIcons.check,
                          size: 14,
                          color: theme.colorScheme.mutedForeground,
                        ),
                        tooltip: 'Mark as read',
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(
                          minWidth: 24,
                          minHeight: 24,
                        ),
                      ),
                ),

              // More actions
              Builder(
                builder:
                    (context) => PopupMenuButton<String>(
                      onSelected: (value) => _handleAction(context, value),
                      itemBuilder:
                          (context) => [
                            PopupMenuItem(
                              value: 'view',
                              child: Row(
                                children: [
                                  Icon(LucideIcons.eye, size: 14),
                                  const SizedBox(width: 8),
                                  const Text('View Details'),
                                ],
                              ),
                            ),
                            if (notification.claimId != null)
                              PopupMenuItem(
                                value: 'view_claim',
                                child: Row(
                                  children: [
                                    Icon(LucideIcons.fileText, size: 14),
                                    const SizedBox(width: 8),
                                    const Text('View Claim'),
                                  ],
                                ),
                              ),
                          ],
                      child: Icon(
                        Icons.more_horiz,
                        size: 14,
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  IconData _getIconForType(ClaimantNotificationType type) {
    switch (type) {
      case ClaimantNotificationType.claimUpdate:
        return LucideIcons.fileText;
      case ClaimantNotificationType.fundingUpdate:
        return LucideIcons.dollarSign;
      case ClaimantNotificationType.systemNotification:
        return LucideIcons.info;
      case ClaimantNotificationType.chatMessage:
        return LucideIcons.messageCircle;
      case ClaimantNotificationType.documentUpdate:
        return LucideIcons.paperclip;
      case ClaimantNotificationType.statusChange:
        return LucideIcons.activity;
    }
  }

  Color _getColorForType(ClaimantNotificationType type, ShadThemeData theme) {
    switch (type) {
      case ClaimantNotificationType.claimUpdate:
        return theme.colorScheme.primary;
      case ClaimantNotificationType.fundingUpdate:
        return Colors.green;
      case ClaimantNotificationType.systemNotification:
        return theme.colorScheme.mutedForeground;
      case ClaimantNotificationType.chatMessage:
        return Colors.blue;
      case ClaimantNotificationType.documentUpdate:
        return Colors.orange;
      case ClaimantNotificationType.statusChange:
        return Colors.purple;
    }
  }

  Color _getColorForPriority(
    NotificationPriority priority,
    ShadThemeData theme,
  ) {
    switch (priority) {
      case NotificationPriority.low:
        return theme.colorScheme.mutedForeground;
      case NotificationPriority.normal:
        return theme.colorScheme.primary;
      case NotificationPriority.high:
        return Colors.orange;
      case NotificationPriority.urgent:
        return theme.colorScheme.destructive;
    }
  }

  void _handleTap(BuildContext context, WidgetRef ref) {
    if (onTap != null) {
      onTap!();
    } else {
      // Navigate to notification detail page
      Navigator.of(
        context,
      ).pushNamed(NotificationDetailPage.routeName, arguments: notification.id);
    }

    // Mark as read if not already read
    if (!notification.isRead) {
      _markAsRead(context, ref);
    }
  }

  void _markAsRead(BuildContext context, [WidgetRef? ref]) {
    if (ref != null) {
      ref
          .read(claimantNotificationsProvider.notifier)
          .markAsRead(notification.id);
    } else {
      // Use Consumer to get the ref
      final container = ProviderScope.containerOf(context);
      container
          .read(claimantNotificationsProvider.notifier)
          .markAsRead(notification.id);
    }
  }

  void _handleAction(BuildContext context, String action) {
    switch (action) {
      case 'view':
        Navigator.of(context).pushNamed(
          NotificationDetailPage.routeName,
          arguments: notification.id,
        );
        break;
      case 'view_claim':
        if (notification.claimId != null) {
          Navigator.of(context).pushNamed(
            '/claimant/claim-detail',
            arguments: notification.claimId,
          );
        }
        break;
    }
  }
}
