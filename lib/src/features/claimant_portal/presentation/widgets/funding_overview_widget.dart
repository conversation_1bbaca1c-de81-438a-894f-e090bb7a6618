import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/funding_provider.dart';

class FundingOverviewWidget extends ConsumerWidget {
  const FundingOverviewWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final fundingState = ref.watch(fundingProvider);
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600;

    final aggregation = fundingState.aggregation;
    if (aggregation == null) {
      return _buildLoadingOverview(theme);
    }

    return ShadCard(
      child: Padding(
        padding: EdgeInsets.all(isDesktop ? 24.0 : 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  LucideIcons.trendingUp,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Funding Overview',
                  style: theme.textTheme.h3.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Main metrics
            if (isDesktop)
              Row(
                children: [
                  Expanded(child: _buildTotalSecuredCard(theme, aggregation)),
                  const SizedBox(width: 16),
                  Expanded(child: _buildTotalPendingCard(theme, aggregation)),
                  const SizedBox(width: 16),
                  Expanded(child: _buildTargetAmountCard(theme, aggregation)),
                ],
              )
            else
              Column(
                children: [
                  _buildTotalSecuredCard(theme, aggregation),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildTotalPendingCard(theme, aggregation),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTargetAmountCard(theme, aggregation),
                      ),
                    ],
                  ),
                ],
              ),

            const SizedBox(height: 24),

            // Progress indicator
            _buildFundingProgress(theme, aggregation),

            const SizedBox(height: 16),

            // Status description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.muted,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    _getStatusIcon(aggregation.fundingPercentage),
                    color: _getStatusColor(
                      theme,
                      aggregation.fundingPercentage,
                    ),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      aggregation.fundingStatusDescription,
                      style: theme.textTheme.p.copyWith(
                        fontWeight: FontWeight.w500,
                        color: _getStatusColor(
                          theme,
                          aggregation.fundingPercentage,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalSecuredCard(ShadThemeData theme, dynamic aggregation) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.primary.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                LucideIcons.check,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Secured',
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            aggregation.formattedTotalSecured,
            style: theme.textTheme.h2.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          Text(
            '${aggregation.securedCount} commitment${aggregation.securedCount != 1 ? 's' : ''}',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalPendingCard(ShadThemeData theme, dynamic aggregation) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(LucideIcons.clock, color: Colors.orange, size: 20),
              const SizedBox(width: 8),
              Text(
                'Pending',
                style: theme.textTheme.small.copyWith(
                  color: Colors.orange,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            aggregation.formattedTotalPending,
            style: theme.textTheme.h3.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          Text(
            '${aggregation.pendingCount} pending',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTargetAmountCard(ShadThemeData theme, dynamic aggregation) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                LucideIcons.target,
                color: theme.colorScheme.mutedForeground,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Target',
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            aggregation.formattedTargetAmount,
            style: theme.textTheme.h3.copyWith(fontWeight: FontWeight.bold),
          ),
          Text(
            'Required funding',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFundingProgress(ShadThemeData theme, dynamic aggregation) {
    final percentage = aggregation.fundingPercentage.clamp(0.0, 100.0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Funding Progress',
              style: theme.textTheme.p.copyWith(fontWeight: FontWeight.w500),
            ),
            Text(
              '${percentage.toStringAsFixed(1)}%',
              style: theme.textTheme.p.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: theme.colorScheme.muted,
            borderRadius: BorderRadius.circular(4),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: percentage / 100,
            child: Container(
              decoration: BoxDecoration(
                color: _getProgressColor(theme, percentage),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingOverview(ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            CircularProgressIndicator(color: theme.colorScheme.primary),
            const SizedBox(height: 16),
            Text(
              'Loading funding overview...',
              style: theme.textTheme.p.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getStatusIcon(double percentage) {
    if (percentage >= 100) {
      return LucideIcons.checkCheck;
    } else if (percentage >= 75) {
      return LucideIcons.trendingUp;
    } else if (percentage >= 25) {
      return LucideIcons.activity;
    } else {
      return LucideIcons.clock;
    }
  }

  Color _getStatusColor(ShadThemeData theme, double percentage) {
    if (percentage >= 100) {
      return Colors.green;
    } else if (percentage >= 75) {
      return theme.colorScheme.primary;
    } else if (percentage >= 25) {
      return Colors.orange;
    } else {
      return theme.colorScheme.mutedForeground;
    }
  }

  Color _getProgressColor(ShadThemeData theme, double percentage) {
    if (percentage >= 100) {
      return Colors.green;
    } else if (percentage >= 75) {
      return theme.colorScheme.primary;
    } else if (percentage >= 25) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
