import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/custom_skeleton_widget.dart';
import '../providers/claim_detail_provider.dart';
import '../widgets/claim_overview_widget.dart';
import '../widgets/status_timeline_widget.dart';
import '../widgets/claim_documents_widget.dart';
import '../widgets/claim_chat_widget.dart';
import 'chat_interface_page.dart';

/// Comprehensive claim detail view page
class ClaimDetailPage extends ConsumerStatefulWidget {
  static const String routeName = '/claimant/claim-detail';

  final String claimId;

  const ClaimDetailPage({super.key, required this.claimId});

  @override
  ConsumerState<ClaimDetailPage> createState() => _ClaimDetailPageState();
}

class _ClaimDetailPageState extends ConsumerState<ClaimDetailPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Initialize claim detail data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(claimDetailProvider(widget.claimId).notifier).initialize();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final detailState = ref.watch(claimDetailProvider(widget.claimId));

    return Scaffold(
      body:
          detailState.isLoading && !detailState.hasData
              ? _buildLoadingState(context, theme)
              : detailState.error != null && !detailState.hasData
              ? _buildErrorState(context, theme, detailState.error!)
              : detailState.hasData && detailState.claim != null
              ? _buildDetailView(context, theme, detailState)
              : _buildLoadingState(context, theme), // Fallback to loading state
    );
  }

  Widget _buildLoadingState(BuildContext context, ShadThemeData theme) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Loading Claim...'),
        backgroundColor: theme.colorScheme.background,
        foregroundColor: theme.colorScheme.foreground,
        elevation: 0,
      ),
      body: const Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            CustomSkeletonWidget(height: 120, width: double.infinity),
            SizedBox(height: 16),
            CustomSkeletonWidget(height: 200, width: double.infinity),
            SizedBox(height: 16),
            CustomSkeletonWidget(height: 150, width: double.infinity),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(
    BuildContext context,
    ShadThemeData theme,
    String error,
  ) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
        backgroundColor: theme.colorScheme.background,
        foregroundColor: theme.colorScheme.foreground,
        elevation: 0,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error Loading Claim', style: theme.textTheme.h3),
              const SizedBox(height: 8),
              Text(
                error,
                textAlign: TextAlign.center,
                style: theme.textTheme.p.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ShadButton.outline(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Go Back'),
                  ),
                  const SizedBox(width: 12),
                  ShadButton(
                    onPressed:
                        () =>
                            ref
                                .read(
                                  claimDetailProvider(widget.claimId).notifier,
                                )
                                .refresh(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailView(
    BuildContext context,
    ShadThemeData theme,
    ClaimDetailState state,
  ) {
    final claim = state.claim;

    // Additional safety check - should not happen due to build logic, but prevents crashes
    if (claim == null) {
      return _buildLoadingState(context, theme);
    }

    return Scaffold(
      appBar: AppBar(
        title: LayoutBuilder(
          builder: (context, constraints) {
            return SizedBox(
              width: constraints.maxWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    claim.title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  Text(
                    'ID: ${claim.id.substring(0, 8)}...',
                    style: TextStyle(
                      fontSize: 12,
                      color: theme.colorScheme.mutedForeground,
                      fontWeight: FontWeight.normal,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ],
              ),
            );
          },
        ),
        backgroundColor: theme.colorScheme.background,
        foregroundColor: theme.colorScheme.foreground,
        elevation: 0,
        actions: [
          if (state.isRefreshing)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            IconButton(
              onPressed:
                  () =>
                      ref
                          .read(claimDetailProvider(widget.claimId).notifier)
                          .refresh(),
              icon: const Icon(Icons.refresh),
              tooltip: 'Refresh',
            ),
          IconButton(
            onPressed: () => _showClaimActions(context),
            icon: const Icon(Icons.more_vert),
            tooltip: 'More actions',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.info_outline, size: 18), text: 'Overview'),
            Tab(icon: Icon(Icons.timeline, size: 18), text: 'Timeline'),
            Tab(icon: Icon(Icons.description, size: 18), text: 'Documents'),
            Tab(icon: Icon(Icons.chat, size: 18), text: 'Chat'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Overview Tab
          ClaimOverviewWidget(
            claim: claim,
            onEditProfile: () => _navigateToProfile(context),
          ),

          // Timeline Tab
          StatusTimelineWidget(
            statusHistory: state.statusHistory,
            claim: claim,
          ),

          // Documents Tab
          ClaimDocumentsWidget(claimId: widget.claimId),

          // Chat Tab
          ClaimChatWidget(claimId: widget.claimId, claimTitle: claim.title),
        ],
      ),

      // Floating Action Button for quick actions
      floatingActionButton: _buildFloatingActionButton(context, theme, claim),
    );
  }

  Widget? _buildFloatingActionButton(
    BuildContext context,
    ShadThemeData theme,
    claim,
  ) {
    return FloatingActionButton.extended(
      onPressed: () => _startQuickChat(context),
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.support_agent, size: 20),
      label: const Text('Quick Help'),
    );
  }

  void _showClaimActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _ClaimActionsSheet(claimId: widget.claimId),
    );
  }

  void _navigateToProfile(BuildContext context) {
    // TODO: Navigate to profile editing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Profile editing coming soon')),
    );
  }

  void _startQuickChat(BuildContext context) {
    // Navigate directly to chat interface for this claim
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => ChatInterfacePage(
              claimId: widget.claimId,
              claimTitle: 'Quick Help Chat',
            ),
      ),
    );
  }
}

/// Bottom sheet for claim actions
class _ClaimActionsSheet extends ConsumerWidget {
  final String claimId;

  const _ClaimActionsSheet({required this.claimId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Claim Actions', style: theme.textTheme.h4),
          const SizedBox(height: 16),

          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Share Claim Details'),
            onTap: () {
              Navigator.pop(context);
              _shareClaimDetails(context);
            },
          ),

          ListTile(
            leading: const Icon(Icons.download),
            title: const Text('Download Summary'),
            onTap: () {
              Navigator.pop(context);
              _downloadSummary(context);
            },
          ),

          ListTile(
            leading: const Icon(Icons.print),
            title: const Text('Print Details'),
            onTap: () {
              Navigator.pop(context);
              _printDetails(context);
            },
          ),

          ListTile(
            leading: const Icon(Icons.help_outline),
            title: const Text('Get Help'),
            onTap: () {
              Navigator.pop(context);
              _getHelp(context);
            },
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  void _shareClaimDetails(BuildContext context) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon')),
    );
  }

  void _downloadSummary(BuildContext context) {
    // TODO: Implement download functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Download functionality coming soon')),
    );
  }

  void _printDetails(BuildContext context) {
    // TODO: Implement print functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Print functionality coming soon')),
    );
  }

  void _getHelp(BuildContext context) {
    // Navigate to chat interface for help
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => ChatInterfacePage(
              claimId: claimId,
              claimTitle: 'Help & Support',
            ),
      ),
    );
  }
}
