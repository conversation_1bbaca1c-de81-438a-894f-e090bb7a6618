import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/claimant_auth_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/dashboard_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/widgets/quick_stats_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/widgets/notification_bell_widget.dart';
import '../../utils/responsive_layout.dart';
import '../../utils/animations.dart';
import '../widgets/error_widgets.dart';
import '../widgets/loading_widgets.dart';

class ClaimantDashboardPage extends ConsumerStatefulWidget {
  const ClaimantDashboardPage({super.key});

  static const String routeName = '/claimant-dashboard';

  @override
  ConsumerState<ClaimantDashboardPage> createState() =>
      _ClaimantDashboardPageState();
}

class _ClaimantDashboardPageState extends ConsumerState<ClaimantDashboardPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 700),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();

    // Initialize dashboard data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(claimantDashboardProvider.notifier).initialize();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final authState = ref.watch(claimantAuthProvider);
    final dashboardState = ref.watch(claimantDashboardProvider);
    final isDesktop = ResponsiveLayout.isDesktop(context);
    final isTablet = ResponsiveLayout.isTablet(context);

    // Check authentication
    if (!authState.isAuthenticated || !authState.isClaimant) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                LucideIcons.userX,
                size: 64,
                color: theme.colorScheme.mutedForeground,
              ),
              const SizedBox(height: 16),
              Text('Access Denied', style: theme.textTheme.h2),
              const SizedBox(height: 8),
              Text(
                'You must be signed in as a claimant to access this page.',
                style: theme.textTheme.p.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: _buildAppBar(context, theme, authState, dashboardState),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: _buildMainContent(
            context,
            theme,
            dashboardState,
            isDesktop,
            isTablet,
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
    BuildContext context,
    ShadThemeData theme,
    ClaimantAuthState authState,
    ClaimantDashboardState dashboardState,
  ) {
    final user = authState.user;
    final profile = dashboardState.profile;
    final isDesktop = ResponsiveLayout.isDesktop(context);

    return AppBar(
      backgroundColor: theme.colorScheme.background,
      elevation: 0,
      scrolledUnderElevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: Icon(LucideIcons.arrowLeft, color: theme.colorScheme.foreground),
        tooltip: 'Back',
      ),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome, ${profile?.displayName ?? user?.data['name'] ?? 'Claimant'}',
            style: theme.textTheme.h3.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          Text(
            'Stay updated with your claims',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
      actions: [
        // Notification bell
        const NotificationBellWidget(),

        // Refresh button
        IconButton(
          onPressed:
              dashboardState.isRefreshing
                  ? null
                  : () =>
                      ref.read(claimantDashboardProvider.notifier).refresh(),
          icon:
              dashboardState.isRefreshing
                  ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: theme.colorScheme.primary,
                    ),
                  )
                  : Icon(
                    LucideIcons.refreshCw,
                    color: theme.colorScheme.mutedForeground,
                  ),
          tooltip: 'Refresh',
        ),

        // Last updated indicator
        if (dashboardState.lastUpdated != null && isDesktop)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Center(
              child: Text(
                'Updated ${_formatLastUpdated(dashboardState.lastUpdated!)}',
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
            ),
          ),

        // User avatar - direct navigation to profile
        Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: GestureDetector(
            onTap: () {
              Navigator.of(context).pushNamed('/claimant-profile');
            },
            child: CircleAvatar(
              radius: 16,
              backgroundColor: theme.colorScheme.primary,
              child: Text(
                (profile?.displayName ?? user?.data['name'] ?? 'C')
                    .substring(0, 1)
                    .toUpperCase(),
                style: TextStyle(
                  color: theme.colorScheme.primaryForeground,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _formatLastUpdated(DateTime lastUpdated) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Widget _buildMainContent(
    BuildContext context,
    ShadThemeData theme,
    ClaimantDashboardState dashboardState,
    bool isDesktop,
    bool isTablet,
  ) {
    if (dashboardState.hasError) {
      return ClaimantErrorWidgets.errorState(
        context: context,
        message: dashboardState.error!,
        title: 'Dashboard Error',
        onRetry: () => ref.read(claimantDashboardProvider.notifier).refresh(),
      );
    }

    if (dashboardState.isLoading) {
      return ClaimantLoadingWidgets.dashboardStatsLoading(context);
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(claimantDashboardProvider.notifier).refresh();
      },
      child: SingleChildScrollView(
        padding: ResponsiveLayout.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Quick Stats
            ClaimantAnimations.fadeIn(child: const QuickStatsWidget()),
            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

            // Status Overview
            ClaimantAnimations.slideIn(
              direction: SlideDirection.bottom,
              child: const StatusOverviewWidget(),
            ),
          ],
        ),
      ),
    );
  }
}
