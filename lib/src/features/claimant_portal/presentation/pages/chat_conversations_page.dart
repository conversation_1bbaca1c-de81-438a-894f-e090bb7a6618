import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/loading_spinner_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/chat_interface_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/chat_conversations_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/widgets/conversation_list_item.dart';

class ChatConversationsPage extends ConsumerStatefulWidget {
  static const String routeName = '/claimant/chat-conversations';

  const ChatConversationsPage({super.key});

  @override
  ConsumerState<ChatConversationsPage> createState() =>
      _ChatConversationsPageState();
}

class _ChatConversationsPageState extends ConsumerState<ChatConversationsPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final conversationsState = ref.watch(chatConversationsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Messages'),
        backgroundColor: theme.colorScheme.background,
        foregroundColor: theme.colorScheme.foreground,
        elevation: 1,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref
                  .read(chatConversationsProvider.notifier)
                  .refreshConversations();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(theme),
          Expanded(child: _buildConversationsList(context, conversationsState)),
        ],
      ),
    );
  }

  Widget _buildSearchBar(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.background,
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child: ShadInput(
        controller: _searchController,
        placeholder: const Text('Search conversations...'),
        leading: const Icon(Icons.search, size: 20),
        onChanged: (value) {
          ref
              .read(chatConversationsProvider.notifier)
              .searchConversations(value);
        },
      ),
    );
  }

  Widget _buildConversationsList(
    BuildContext context,
    ChatConversationsState state,
  ) {
    final theme = ShadTheme.of(context);

    if (state.isLoading && state.conversations.isEmpty) {
      return const Center(child: LoadingSpinnerWidget());
    }

    if (state.error != null && state.conversations.isEmpty) {
      return _buildErrorState(theme, state.error!);
    }

    final filteredConversations =
        ref.read(chatConversationsProvider.notifier).filteredConversations;

    if (filteredConversations.isEmpty) {
      return _buildEmptyState(theme, state.searchQuery);
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref
            .read(chatConversationsProvider.notifier)
            .refreshConversations();
      },
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: filteredConversations.length,
        separatorBuilder: (context, index) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final conversation = filteredConversations[index];
          return ConversationListItem(
            conversation: conversation,
            onTap:
                () => _openChat(
                  context,
                  conversation.claimId,
                  conversation.title,
                ),
          );
        },
      ),
    );
  }

  Widget _buildErrorState(ShadThemeData theme, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.destructive,
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading Conversations',
              style: theme.textTheme.h3,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: theme.textTheme.muted,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ShadButton.outline(
              onPressed: () {
                ref
                    .read(chatConversationsProvider.notifier)
                    .refreshConversations();
              },
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(ShadThemeData theme, String? searchQuery) {
    final isSearching = searchQuery != null && searchQuery.isNotEmpty;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isSearching ? Icons.search_off : Icons.chat_bubble_outline,
              size: 64,
              color: theme.colorScheme.mutedForeground,
            ),
            const SizedBox(height: 16),
            Text(
              isSearching ? 'No conversations found' : 'No conversations yet',
              style: theme.textTheme.h3,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              isSearching
                  ? 'Try adjusting your search terms'
                  : 'Start a conversation by visiting a claim and clicking the chat button',
              style: theme.textTheme.muted,
              textAlign: TextAlign.center,
            ),
            if (isSearching) ...[
              const SizedBox(height: 24),
              ShadButton.outline(
                onPressed: () {
                  _searchController.clear();
                  ref
                      .read(chatConversationsProvider.notifier)
                      .searchConversations('');
                },
                child: const Text('Clear Search'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _openChat(BuildContext context, String claimId, String? claimTitle) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) =>
                ChatInterfacePage(claimId: claimId, claimTitle: claimTitle),
      ),
    );
  }
}
