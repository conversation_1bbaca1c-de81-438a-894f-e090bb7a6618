import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/form_submission_error_alert_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/widgets/account_settings_widget.dart';
import '../providers/profile_provider.dart';
import '../widgets/profile_info_widget.dart';
import '../widgets/preferences_widget.dart';
import 'profile_edit_page.dart';

/// Legacy profile page - redirects to new profile view
class ClaimantProfilePage extends ConsumerStatefulWidget {
  static const String routeName = '/claimant-profile';

  const ClaimantProfilePage({super.key});

  @override
  ConsumerState<ClaimantProfilePage> createState() =>
      _ClaimantProfilePageState();
}

class _ClaimantProfilePageState extends ConsumerState<ClaimantProfilePage>
    with SingleTickerProviderStateMixin {
  final PocketBaseService _pbService = PocketBaseService();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Load profile data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(profileProvider.notifier).loadProfile();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final currentUser = _pbService.currentUser;
    final profileState = ref.watch(profileProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('My Profile', style: theme.textTheme.h4),
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(LucideIcons.user), text: 'Profile'),
            Tab(icon: Icon(LucideIcons.settings), text: 'Preferences'),
            Tab(icon: Icon(LucideIcons.logOut), text: 'Account'),
          ],
        ),
      ),
      body:
          currentUser == null
              ? LayoutBuilder(
                builder:
                    (context, constraints) => SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Container(
                        height: constraints.maxHeight,
                        alignment: Alignment.center,
                        child: const Text(
                          'Not logged in or user data unavailable.',
                        ),
                      ),
                    ),
              )
              : Column(
                children: [
                  // Error/Success Messages
                  if (profileState.error != null)
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: FormSubmissionErrorAlertWidget(
                        title: 'Error',
                        description: profileState.error!,
                      ),
                    ),

                  if (profileState.successMessage != null)
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: ShadAlert(
                        icon: Icon(LucideIcons.check, size: 16),
                        title: const Text('Success'),
                        description: Text(profileState.successMessage!),
                      ),
                    ),

                  // Tab Content
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        // Profile Tab
                        RefreshIndicator.adaptive(
                          onRefresh: _refreshProfile,
                          child: SingleChildScrollView(
                            physics: const AlwaysScrollableScrollPhysics(),
                            padding: const EdgeInsets.all(16.0),
                            child: ProfileInfoWidget(
                              onEditPressed: _navigateToEditProfile,
                            ),
                          ),
                        ),

                        // Preferences Tab
                        RefreshIndicator.adaptive(
                          onRefresh: _refreshProfile,
                          child: const SingleChildScrollView(
                            physics: AlwaysScrollableScrollPhysics(),
                            padding: EdgeInsets.all(16.0),
                            child: PreferencesWidget(),
                          ),
                        ),

                        // Account Tab
                        RefreshIndicator.adaptive(
                          onRefresh: _refreshProfile,
                          child: const SingleChildScrollView(
                            physics: AlwaysScrollableScrollPhysics(),
                            padding: EdgeInsets.all(16.0),
                            child: AccountSettingsWidget(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }

  Future<void> _refreshProfile() async {
    await ref.read(profileProvider.notifier).loadProfile();
  }

  void _navigateToEditProfile() {
    Navigator.of(context).pushNamed(ProfileEditPage.routeName).then((_) {
      // Refresh profile when returning from edit page
      ref.read(profileProvider.notifier).loadProfile();
    });
  }
}
