import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/claimant_auth_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/funding_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/widgets/funding_overview_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/widgets/funding_commitment_card.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/widgets/funding_timeline_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/widgets/funding_stats_widget.dart';

class FundingDashboardPage extends ConsumerStatefulWidget {
  const FundingDashboardPage({super.key});

  static const String routeName = '/claimant-funding-dashboard';

  @override
  ConsumerState<FundingDashboardPage> createState() => _FundingDashboardPageState();
}

class _FundingDashboardPageState extends ConsumerState<FundingDashboardPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 700),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();

    // Initialize funding data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(fundingProvider.notifier).initialize();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final authState = ref.watch(claimantAuthProvider);
    final fundingState = ref.watch(fundingProvider);
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    final isTablet = MediaQuery.of(context).size.width >= 600;

    // Check authentication
    if (!authState.isAuthenticated || !authState.isClaimant) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                LucideIcons.userX,
                size: 64,
                color: theme.colorScheme.mutedForeground,
              ),
              const SizedBox(height: 16),
              Text('Access Denied', style: theme.textTheme.h2),
              const SizedBox(height: 8),
              Text(
                'You must be signed in as a claimant to access this page.',
                style: theme.textTheme.p.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: _buildAppBar(context, theme, fundingState),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: _buildMainContent(
            context,
            theme,
            fundingState,
            isDesktop,
            isTablet,
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
    BuildContext context,
    ShadThemeData theme,
    FundingState fundingState,
  ) {
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return AppBar(
      backgroundColor: theme.colorScheme.background,
      elevation: 0,
      scrolledUnderElevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: Icon(
          LucideIcons.arrowLeft,
          color: theme.colorScheme.foreground,
        ),
      ),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Funding Dashboard',
            style: theme.textTheme.h3.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          Text(
            'Track your funding status and commitments',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
      actions: [
        // Refresh button
        IconButton(
          onPressed: fundingState.isRefreshing
              ? null
              : () => ref.read(fundingProvider.notifier).refresh(),
          icon: fundingState.isRefreshing
              ? SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: theme.colorScheme.primary,
                  ),
                )
              : Icon(
                  LucideIcons.refreshCw,
                  color: theme.colorScheme.mutedForeground,
                ),
          tooltip: 'Refresh',
        ),

        // Last updated indicator
        if (fundingState.lastUpdated != null && isDesktop)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Center(
              child: Text(
                'Updated ${_formatLastUpdated(fundingState.lastUpdated!)}',
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
            ),
          ),

        const SizedBox(width: 16),
      ],
    );
  }

  String _formatLastUpdated(DateTime lastUpdated) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Widget _buildMainContent(
    BuildContext context,
    ShadThemeData theme,
    FundingState fundingState,
    bool isDesktop,
    bool isTablet,
  ) {
    if (fundingState.error != null) {
      return _buildErrorState(context, theme, fundingState.error!);
    }

    if (fundingState.isLoading) {
      return _buildLoadingState(context, theme);
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(fundingProvider.notifier).refresh();
      },
      child: SingleChildScrollView(
        padding: EdgeInsets.all(isDesktop ? 24.0 : 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Funding Overview
            const FundingOverviewWidget(),
            SizedBox(height: isDesktop ? 32 : 24),

            // Funding Statistics
            const FundingStatsWidget(),
            SizedBox(height: isDesktop ? 32 : 24),

            // Content Grid
            if (isDesktop)
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left Column - Commitments
                  Expanded(
                    flex: 2,
                    child: _buildCommitmentsSection(theme, fundingState),
                  ),
                  const SizedBox(width: 24),
                  // Right Column - Timeline
                  Expanded(
                    flex: 1,
                    child: _buildTimelineSection(theme, fundingState),
                  ),
                ],
              )
            else
              Column(
                children: [
                  _buildCommitmentsSection(theme, fundingState),
                  const SizedBox(height: 24),
                  _buildTimelineSection(theme, fundingState),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommitmentsSection(ShadThemeData theme, FundingState fundingState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Funding Commitments',
          style: theme.textTheme.h4.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (fundingState.commitments.isEmpty)
          _buildEmptyCommitments(theme)
        else
          ...fundingState.commitments.map(
            (commitment) => Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: FundingCommitmentCard(commitment: commitment),
            ),
          ),
      ],
    );
  }

  Widget _buildTimelineSection(ShadThemeData theme, FundingState fundingState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Funding Timeline',
          style: theme.textTheme.h4.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        FundingTimelineWidget(events: fundingState.timeline),
      ],
    );
  }

  Widget _buildEmptyCommitments(ShadThemeData theme) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              LucideIcons.piggyBank,
              size: 48,
              color: theme.colorScheme.mutedForeground,
            ),
            const SizedBox(height: 16),
            Text(
              'No Funding Commitments',
              style: theme.textTheme.h4,
            ),
            const SizedBox(height: 8),
            Text(
              'Your funding commitments will appear here once investors commit to your claims.',
              style: theme.textTheme.p.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context, ShadThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'Loading funding data...',
            style: theme.textTheme.p.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, ShadThemeData theme, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.destructive,
            ),
            const SizedBox(height: 16),
            Text('Something went wrong', style: theme.textTheme.h2),
            const SizedBox(height: 8),
            Text(
              error,
              style: theme.textTheme.p.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ShadButton(
              onPressed: () {
                ref.read(fundingProvider.notifier).refresh();
              },
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }
}
