import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/form_submission_error_alert_widget.dart';
import '../providers/profile_provider.dart';
import '../widgets/profile_edit_form.dart';

/// Page for editing profile information
class ProfileEditPage extends ConsumerStatefulWidget {
  static const String routeName = '/claimant/profile/edit';

  const ProfileEditPage({super.key});

  @override
  ConsumerState<ProfileEditPage> createState() => _ProfileEditPageState();
}

class _ProfileEditPageState extends ConsumerState<ProfileEditPage> {
  @override
  void initState() {
    super.initState();

    // Load profile data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final profileState = ref.read(profileProvider);
      if (profileState.profile == null) {
        ref.read(profileProvider.notifier).loadProfile();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(profileProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Profile', style: theme.textTheme.h4),
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Error Messages
            if (profileState.error != null) ...[
              FormSubmissionErrorAlertWidget(
                title: 'Error',
                description: profileState.error!,
              ),
              const SizedBox(height: 16),
            ],

            // Success Messages
            if (profileState.successMessage != null) ...[
              ShadAlert(
                icon: Icon(LucideIcons.check, size: 16),
                title: const Text('Success'),
                description: Text(profileState.successMessage!),
              ),
              const SizedBox(height: 16),
            ],

            // Loading State
            if (profileState.isLoading)
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Loading profile...'),
                  ],
                ),
              )
            else if (profileState.profile == null)
              ShadCard(
                title: Text('Profile Not Found', style: theme.textTheme.h4),
                child: const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(LucideIcons.info, size: 48),
                      SizedBox(height: 16),
                      Text(
                        'Unable to load profile information. Please try again.',
                      ),
                    ],
                  ),
                ),
              )
            else
              // Edit Form
              ProfileEditForm(
                onSaved: _handleSaved,
                onCancelled: _handleCancelled,
              ),
          ],
        ),
      ),
    );
  }

  void _handleSaved() {
    // Clear any messages
    ref.read(profileProvider.notifier).clearMessages();

    // Navigate back
    Navigator.of(context).pop();
  }

  void _handleCancelled() {
    // Clear any messages
    ref.read(profileProvider.notifier).clearMessages();

    // Navigate back
    Navigator.of(context).pop();
  }
}

/// Responsive edit page for larger screens
class ResponsiveProfileEditPage extends ConsumerStatefulWidget {
  static const String routeName = '/claimant/profile/edit-responsive';

  const ResponsiveProfileEditPage({super.key});

  @override
  ConsumerState<ResponsiveProfileEditPage> createState() =>
      _ResponsiveProfileEditPageState();
}

class _ResponsiveProfileEditPageState
    extends ConsumerState<ResponsiveProfileEditPage> {
  @override
  void initState() {
    super.initState();

    // Load profile data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final profileState = ref.read(profileProvider);
      if (profileState.profile == null) {
        ref.read(profileProvider.notifier).loadProfile();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(profileProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Profile', style: theme.textTheme.h4),
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
      ),
      body: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Error Messages
                if (profileState.error != null) ...[
                  FormSubmissionErrorAlertWidget(
                    title: 'Error',
                    description: profileState.error!,
                  ),
                  const SizedBox(height: 16),
                ],

                // Success Messages
                if (profileState.successMessage != null) ...[
                  ShadAlert(
                    icon: Icon(LucideIcons.check, size: 16),
                    title: const Text('Success'),
                    description: Text(profileState.successMessage!),
                  ),
                  const SizedBox(height: 16),
                ],

                // Loading State
                if (profileState.isLoading)
                  const Center(
                    child: Column(
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading profile...'),
                      ],
                    ),
                  )
                else if (profileState.profile == null)
                  ShadCard(
                    title: Text('Profile Not Found', style: theme.textTheme.h4),
                    child: const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Icon(LucideIcons.info, size: 48),
                          SizedBox(height: 16),
                          Text(
                            'Unable to load profile information. Please try again.',
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  // Edit Form
                  ProfileEditForm(
                    onSaved: _handleSaved,
                    onCancelled: _handleCancelled,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleSaved() {
    // Clear any messages
    ref.read(profileProvider.notifier).clearMessages();

    // Navigate back
    Navigator.of(context).pop();
  }

  void _handleCancelled() {
    // Clear any messages
    ref.read(profileProvider.notifier).clearMessages();

    // Navigate back
    Navigator.of(context).pop();
  }
}
