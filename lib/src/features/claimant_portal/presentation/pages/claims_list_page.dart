import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/empty_state_widget.dart';

import '../providers/claims_list_provider.dart';
import '../widgets/claim_summary_card.dart';
import '../widgets/claims_filter_widget.dart';
import '../../utils/responsive_layout.dart';
import '../widgets/error_widgets.dart';
import '../widgets/loading_widgets.dart';

/// Page displaying list of claims for the claimant
class ClaimsListPage extends ConsumerStatefulWidget {
  static const String routeName = '/claimant/claims';

  const ClaimsListPage({super.key});

  @override
  ConsumerState<ClaimsListPage> createState() => _ClaimsListPageState();
}

class _ClaimsListPageState extends ConsumerState<ClaimsListPage> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Initialize claims list when page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(claimsListProvider.notifier).initialize();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final claimsListState = ref.watch(claimsListProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Claims'),
        elevation: 0,
        backgroundColor: theme.colorScheme.background,
        foregroundColor: theme.colorScheme.foreground,
        actions: [
          // Filter button
          IconButton(
            onPressed: _showFilterSheet,
            icon: Stack(
              children: [
                const Icon(LucideIcons.settings),
                if (claimsListState.hasActiveFilters)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.destructive,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          _buildSearchBar(theme, claimsListState),

          // Filter summary
          if (claimsListState.hasActiveFilters)
            _buildFilterSummary(theme, claimsListState),

          // Claims list
          Expanded(child: _buildClaimsList(theme, claimsListState)),
        ],
      ),
    );
  }

  Widget _buildSearchBar(ShadThemeData theme, ClaimsListState state) {
    return Container(
      padding: ResponsiveLayout.getResponsivePadding(context),
      decoration: BoxDecoration(
        color: theme.colorScheme.background,
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child: ShadInput(
        controller: _searchController,
        placeholder: const Text('Search claims by title, ID, or stage...'),
        leading: const Padding(
          padding: EdgeInsets.only(left: 12, right: 8),
          child: Icon(LucideIcons.search, size: 16),
        ),
        trailing:
            state.searchQuery.isNotEmpty
                ? Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ShadButton.ghost(
                    onPressed: _clearSearch,
                    size: ShadButtonSize.sm,
                    child: const Icon(LucideIcons.x, size: 14),
                  ),
                )
                : null,
        onChanged: (value) {
          ref.read(claimsListProvider.notifier).updateSearchQuery(value);
        },
      ),
    );
  }

  Widget _buildFilterSummary(ShadThemeData theme, ClaimsListState state) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveLayout.getHorizontalPadding(context),
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.3),
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child: Row(
        children: [
          Icon(
            LucideIcons.settings,
            size: 14,
            color: theme.colorScheme.mutedForeground,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Showing ${state.filteredCount} of ${state.totalClaims} claims',
              style: theme.textTheme.small.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
            ),
          ),
          ShadButton.ghost(
            onPressed: _clearAllFilters,
            size: ShadButtonSize.sm,
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  Widget _buildClaimsList(ShadThemeData theme, ClaimsListState state) {
    if (state.isLoading) {
      return ClaimantLoadingWidgets.claimsListLoading(context);
    }

    if (state.error != null) {
      return ClaimantErrorWidgets.errorState(
        context: context,
        message: state.error!,
        title: 'Error Loading Claims',
        onRetry: () => ref.read(claimsListProvider.notifier).refresh(),
      );
    }

    if (state.filteredClaims.isEmpty) {
      return _buildEmptyState(state);
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(claimsListProvider.notifier).refresh(),
      child: ListView.builder(
        controller: _scrollController,
        padding: ResponsiveLayout.getResponsivePadding(context),
        itemCount: state.filteredClaims.length,
        itemBuilder: (context, index) {
          final claim = state.filteredClaims[index];
          return Padding(
            padding: EdgeInsets.only(
              bottom: ResponsiveLayout.getCardSpacing(context),
            ),
            child: ClaimSummaryCard(
              claim: claim,
              onTap: () => _navigateToClaimDetails(claim.id),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(ClaimsListState state) {
    if (state.hasActiveFilters) {
      return EmptyStateWidget(
        icon: LucideIcons.search,
        message: 'No claims match your current search and filter criteria.',
        actionButtonText: 'Clear Filters',
        onActionButtonPressed: _clearAllFilters,
      );
    }

    return const EmptyStateWidget(
      icon: LucideIcons.fileText,
      message:
          'You don\'t have any claims yet. Claims will appear here once they are submitted.',
    );
  }

  void _showFilterSheet() {
    final currentOptions = ref.read(claimsListProvider).filterOptions;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.75,
            maxChildSize: 0.95,
            minChildSize: 0.5,
            expand: false,
            builder:
                (context, scrollController) => ClaimsFilterWidget(
                  initialOptions: currentOptions,
                  onFiltersChanged: (options) {
                    ref
                        .read(claimsListProvider.notifier)
                        .updateFilterOptions(options);
                  },
                ),
          ),
    );
  }

  void _clearSearch() {
    _searchController.clear();
    ref.read(claimsListProvider.notifier).updateSearchQuery('');
  }

  void _clearAllFilters() {
    _searchController.clear();
    ref.read(claimsListProvider.notifier).clearFilters();
  }

  void _navigateToClaimDetails(String claimId) {
    Navigator.pushNamed(context, '/claimant/claim-detail', arguments: claimId);
  }
}
