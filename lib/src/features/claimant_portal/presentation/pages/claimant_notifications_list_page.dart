import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_notification_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/claimant_notifications_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/widgets/notification_item_widget.dart';
import '../../utils/responsive_layout.dart';
import '../widgets/error_widgets.dart';
import '../widgets/loading_widgets.dart';

/// Page displaying list of notifications for claimants
class ClaimantNotificationsListPage extends ConsumerStatefulWidget {
  static const String routeName = '/claimant/notifications';

  const ClaimantNotificationsListPage({super.key});

  @override
  ConsumerState<ClaimantNotificationsListPage> createState() =>
      _ClaimantNotificationsListPageState();
}

class _ClaimantNotificationsListPageState
    extends ConsumerState<ClaimantNotificationsListPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final notificationsState = ref.watch(claimantNotificationsProvider);
    final isDesktop = ResponsiveLayout.isDesktop(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: _buildAppBar(theme, notificationsState),
      body: Column(
        children: [
          // Filters section
          _buildFiltersSection(theme, notificationsState),

          // Content section
          Expanded(child: _buildContent(theme, notificationsState, isDesktop)),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
    ShadThemeData theme,
    ClaimantNotificationsState state,
  ) {
    return AppBar(
      backgroundColor: theme.colorScheme.background,
      elevation: 0,
      scrolledUnderElevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: Icon(LucideIcons.arrowLeft, color: theme.colorScheme.foreground),
      ),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notifications',
            style: theme.textTheme.h3.copyWith(fontWeight: FontWeight.bold),
          ),
          if (state.hasNotifications)
            Text(
              '${state.filteredNotifications.length} notification${state.filteredNotifications.length == 1 ? '' : 's'}',
              style: theme.textTheme.small.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
            ),
        ],
      ),
      actions: [
        // Mark all as read button
        if (state.hasUnreadNotifications)
          TextButton.icon(
            onPressed: () => _showMarkAllAsReadDialog(),
            icon: Icon(LucideIcons.checkCheck, size: 16),
            label: const Text('Mark All Read'),
            style: TextButton.styleFrom(
              foregroundColor: theme.colorScheme.primary,
            ),
          ),

        // Refresh button
        IconButton(
          onPressed:
              () => ref.read(claimantNotificationsProvider.notifier).refresh(),
          icon: Icon(
            LucideIcons.refreshCw,
            color: theme.colorScheme.mutedForeground,
          ),
          tooltip: 'Refresh',
        ),

        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildFiltersSection(
    ShadThemeData theme,
    ClaimantNotificationsState state,
  ) {
    return Container(
      padding: ResponsiveLayout.getResponsivePadding(context),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child: Column(
        children: [
          // Filter chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  'All',
                  state.selectedFilter == 'all',
                  () => ref
                      .read(claimantNotificationsProvider.notifier)
                      .setFilter('all'),
                  theme,
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'Unread (${_getUnreadCountForCurrentFilters(state)})',
                  state.selectedFilter == 'unread',
                  () => ref
                      .read(claimantNotificationsProvider.notifier)
                      .setFilter('unread'),
                  theme,
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'Read',
                  state.selectedFilter == 'read',
                  () => ref
                      .read(claimantNotificationsProvider.notifier)
                      .setFilter('read'),
                  theme,
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Type filters
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildTypeFilterChip(
                  'All Types',
                  state.selectedType == null,
                  () => ref
                      .read(claimantNotificationsProvider.notifier)
                      .setTypeFilter(null),
                  theme,
                ),
                const SizedBox(width: 8),
                ...ClaimantNotificationType.values.map(
                  (type) => Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: _buildTypeFilterChip(
                      type.value.replaceAll('_', ' ').toUpperCase(),
                      state.selectedType == type,
                      () => ref
                          .read(claimantNotificationsProvider.notifier)
                          .setTypeFilter(type),
                      theme,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    bool isSelected,
    VoidCallback onTap,
    ShadThemeData theme,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.secondary,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color:
                isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.border,
          ),
        ),
        child: Text(
          label,
          style: theme.textTheme.small.copyWith(
            color:
                isSelected
                    ? theme.colorScheme.primaryForeground
                    : theme.colorScheme.foreground,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildTypeFilterChip(
    String label,
    bool isSelected,
    VoidCallback onTap,
    ShadThemeData theme,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? theme.colorScheme.primary.withValues(alpha: 0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.border,
          ),
        ),
        child: Text(
          label,
          style: theme.textTheme.small.copyWith(
            fontSize: 11,
            color:
                isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.mutedForeground,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildContent(
    ShadThemeData theme,
    ClaimantNotificationsState state,
    bool isDesktop,
  ) {
    if (state.isLoading && !state.isInitialized) {
      return ClaimantLoadingWidgets.notificationsListLoading(context);
    }

    if (state.hasError) {
      return ClaimantErrorWidgets.errorState(
        context: context,
        message: state.error!,
        title: 'Error Loading Notifications',
        onRetry:
            () => ref.read(claimantNotificationsProvider.notifier).refresh(),
      );
    }

    if (!state.hasNotifications) {
      return _buildEmptyState(theme, 'No notifications yet');
    }

    if (state.filteredNotifications.isEmpty) {
      return _buildEmptyState(theme, 'No notifications match your filters');
    }

    return _buildNotificationsList(theme, state, isDesktop);
  }

  Widget _buildEmptyState(ShadThemeData theme, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              LucideIcons.bell,
              size: 48,
              color: theme.colorScheme.mutedForeground,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: theme.textTheme.h4.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Notifications will appear here when available',
              style: theme.textTheme.p.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationsList(
    ShadThemeData theme,
    ClaimantNotificationsState state,
    bool isDesktop,
  ) {
    final groupedNotifications = state.notificationsGroupedByDate;

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(claimantNotificationsProvider.notifier).refresh();
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: ResponsiveLayout.getResponsivePadding(context),
        itemCount: _calculateItemCount(groupedNotifications),
        itemBuilder: (context, index) {
          return _buildListItem(groupedNotifications, index, theme);
        },
      ),
    );
  }

  int _calculateItemCount(
    Map<String, List<ClaimantNotificationModel>> grouped,
  ) {
    int count = 0;
    for (final group in grouped.values) {
      count += 1 + group.length; // 1 for header + notifications count
    }
    return count;
  }

  Widget _buildListItem(
    Map<String, List<ClaimantNotificationModel>> grouped,
    int index,
    ShadThemeData theme,
  ) {
    int currentIndex = 0;

    for (final entry in grouped.entries) {
      final groupName = entry.key;
      final notifications = entry.value;

      // Check if this is the group header
      if (currentIndex == index) {
        return _buildGroupHeader(groupName, theme);
      }
      currentIndex++;

      // Check if this is one of the notifications in this group
      for (int i = 0; i < notifications.length; i++) {
        if (currentIndex == index) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: NotificationItemWidget(
              notification: notifications[i],
              showClaimInfo: true,
            ),
          );
        }
        currentIndex++;
      }
    }

    return const SizedBox.shrink();
  }

  Widget _buildGroupHeader(String groupName, ShadThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 8),
      child: Text(
        groupName,
        style: theme.textTheme.h4.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.primary,
        ),
      ),
    );
  }

  /// Get unread count considering current type filter
  int _getUnreadCountForCurrentFilters(ClaimantNotificationsState state) {
    var notifications = state.notifications;

    // Apply type filter if selected
    if (state.selectedType != null) {
      notifications =
          notifications
              .where((n) => n.notificationType == state.selectedType)
              .toList();
    }

    // Count unread notifications
    return notifications.where((n) => !n.isRead).length;
  }

  void _showMarkAllAsReadDialog() {
    showDialog(
      context: context,
      builder:
          (context) => ShadDialog(
            title: const Text('Mark All as Read'),
            description: const Text(
              'Are you sure you want to mark all notifications as read? This action cannot be undone.',
            ),
            actions: [
              ShadButton.outline(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ShadButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ref
                      .read(claimantNotificationsProvider.notifier)
                      .markAllAsRead();
                },
                child: const Text('Mark All Read'),
              ),
            ],
          ),
    );
  }
}
