import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/loading_spinner_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/providers/chat_interface_provider.dart';
import 'package:three_pay_group_litigation_platform/src/shared/widgets/chat/chat_header_widget.dart';
import 'package:three_pay_group_litigation_platform/src/shared/widgets/chat/message_bubble_widget.dart';
import 'package:three_pay_group_litigation_platform/src/shared/widgets/chat/message_input_widget.dart';
import 'package:three_pay_group_litigation_platform/src/shared/widgets/chat/typing_indicator_widget.dart';
import 'package:three_pay_group_litigation_platform/src/debug/chat_debug_helper.dart';
import 'package:three_pay_group_litigation_platform/src/debug/chat_quick_test.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

class ChatInterfacePage extends ConsumerStatefulWidget {
  static const String routeName = '/claimant/chat-interface';

  final String claimId;
  final String? claimTitle;

  const ChatInterfacePage({super.key, required this.claimId, this.claimTitle});

  @override
  ConsumerState<ChatInterfacePage> createState() => _ChatInterfacePageState();
}

class _ChatInterfacePageState extends ConsumerState<ChatInterfacePage> {
  final ScrollController _scrollController = ScrollController();
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    // Ensure initialization happens after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ensureInitialization();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Ensure the chat interface is properly initialized
  Future<void> _ensureInitialization() async {
    if (_hasInitialized) return;

    try {
      LoggerService.info(
        'Ensuring chat interface initialization for claim: ${widget.claimId}',
      );

      // Wait a bit for auth to be ready if needed
      await Future.delayed(const Duration(milliseconds: 200));

      // Force re-initialization if needed
      await ref
          .read(chatInterfaceProvider(widget.claimId).notifier)
          .forceReinit();

      _hasInitialized = true;
      LoggerService.info('Chat interface initialization completed');
    } catch (e) {
      LoggerService.error('Error during chat interface initialization', e);
    }
  }

  @override
  Widget build(BuildContext context) {
    final chatState = ref.watch(chatInterfaceProvider(widget.claimId));

    // Set claim title if provided
    if (widget.claimTitle != null &&
        chatState.claimTitle != widget.claimTitle) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref
            .read(chatInterfaceProvider(widget.claimId).notifier)
            .setClaimTitle(widget.claimTitle!);
      });
    }

    return Scaffold(
      appBar: AgentChatHeaderWidget(
        claimId: widget.claimId,
        claimTitle: chatState.claimTitle ?? widget.claimTitle,
        isTyping: chatState.isTyping,
        actions: [
          // Debug button (remove in production)
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: () => _runDebug(),
            tooltip: 'Debug Chat',
          ),
        ],
      ),
      body: Column(
        children: [
          // Show error banner if there's an error
          if (chatState.error != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              color: ShadTheme.of(context).colorScheme.destructive,
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color:
                        ShadTheme.of(context).colorScheme.destructiveForeground,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Error: ${chatState.error}',
                      style: TextStyle(
                        color:
                            ShadTheme.of(
                              context,
                            ).colorScheme.destructiveForeground,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      color:
                          ShadTheme.of(
                            context,
                          ).colorScheme.destructiveForeground,
                      size: 16,
                    ),
                    onPressed: () {
                      ref
                          .read(chatInterfaceProvider(widget.claimId).notifier)
                          .clearError();
                    },
                  ),
                ],
              ),
            ),
          Expanded(child: _buildMessagesList(context, chatState)),
          _buildMessageInput(context, chatState),
        ],
      ),
    );
  }

  Widget _buildMessagesList(BuildContext context, ChatInterfaceState state) {
    final theme = ShadTheme.of(context);

    if (state.isLoading && state.messages.isEmpty) {
      return const Center(child: LoadingSpinnerWidget());
    }

    if (state.error != null && state.messages.isEmpty) {
      return _buildErrorState(theme, state.error!);
    }

    if (state.messages.isEmpty) {
      return _buildEmptyState(theme);
    }

    // Auto-scroll to bottom when new messages arrive
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: state.messages.length + (state.isTyping ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == state.messages.length) {
          // Show typing indicator at the end
          return TypingIndicatorWidget(
            isVisible: state.isTyping,
            senderName: '3Pay Agent',
          );
        }

        final message = state.messages[index];
        return MessageBubbleWidget(
          message: message,
          onTap: () => _onMessageTap(message),
        );
      },
    );
  }

  Widget _buildErrorState(ShadThemeData theme, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.destructive,
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading Messages',
              style: theme.textTheme.h3,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: theme.textTheme.muted,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ShadButton.outline(
              onPressed: () {
                ref
                    .read(chatInterfaceProvider(widget.claimId).notifier)
                    .refreshMessages();
              },
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(ShadThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: theme.colorScheme.mutedForeground,
            ),
            const SizedBox(height: 16),
            Text(
              'Start the conversation',
              style: theme.textTheme.h3,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Send a message to start chatting with the 3Pay Global team about your claim',
              style: theme.textTheme.muted,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageInput(BuildContext context, ChatInterfaceState state) {
    return MessageInputWidget(
      onSendMessage: (content) => _sendMessage(content),
      onSendMessageWithFile:
          (content, file) => _sendMessageWithFile(content, file),
      isSending: state.isSending,
      showFileButton: true,
      placeholder: 'Type your message to 3Pay Global...',
    );
  }

  Future<bool> _sendMessage(String content) async {
    final success = await ref
        .read(chatInterfaceProvider(widget.claimId).notifier)
        .sendMessage(content);

    if (success) {
      _scrollToBottom();
    } else {
      // Get the actual error from the state
      final error = ref.read(chatInterfaceProvider(widget.claimId)).error;
      _showErrorToast('Failed to send message: ${error ?? 'Unknown error'}');
    }

    return success;
  }

  Future<bool> _sendMessageWithFile(String content, File file) async {
    final success = await ref
        .read(chatInterfaceProvider(widget.claimId).notifier)
        .sendMessageWithFile(content: content, file: file);

    if (success) {
      _scrollToBottom();
    } else {
      _showErrorToast('Failed to send message with file');
    }

    return success;
  }

  void _onMessageTap(dynamic message) {
    // Handle message tap (e.g., show details, copy text, etc.)
    // For now, just clear any error state
    ref.read(chatInterfaceProvider(widget.claimId).notifier).clearError();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showErrorToast(String message) {
    if (mounted) {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Error'),
          description: Text(message),
        ),
      );
    }
  }

  /// Debug method to troubleshoot chat issues
  Future<void> _runDebug() async {
    // Run quick test first
    final results = await ChatQuickTest.runQuickTest();
    ChatQuickTest.printResults(results);

    // Run full debug if needed
    await ChatDebugHelper.runFullDebug(widget.claimId, 'Debug test message');

    if (mounted) {
      // Show specific error if found
      String message = 'Check console logs for debug information';
      if (results['connection'] != 'OK') {
        message = 'PocketBase connection failed. Check server.';
      } else if (results['authenticated'] != true) {
        message = 'User not authenticated. Please sign in.';
      } else if (results['collection_access'] != 'OK') {
        message = 'Collection access denied. Check permissions.';
      } else if (results['message_creation'] != 'OK') {
        message = 'Cannot create messages. Check collection rules.';
      }

      ShadToaster.of(context).show(
        ShadToast(
          title: const Text('Debug Complete'),
          description: Text(message),
        ),
      );
    }
  }
}
