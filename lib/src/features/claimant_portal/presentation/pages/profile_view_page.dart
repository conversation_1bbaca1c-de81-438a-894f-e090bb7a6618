import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/form_submission_error_alert_widget.dart';
import '../providers/profile_provider.dart';
import '../widgets/profile_info_widget.dart';
import '../widgets/preferences_widget.dart';
import 'profile_edit_page.dart';

/// Page for viewing profile information and preferences
class ProfileViewPage extends ConsumerStatefulWidget {
  static const String routeName = '/claimant/profile';

  const ProfileViewPage({super.key});

  @override
  ConsumerState<ProfileViewPage> createState() => _ProfileViewPageState();
}

class _ProfileViewPageState extends ConsumerState<ProfileViewPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Load profile data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(profileProvider.notifier).loadProfile();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(profileProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('My Profile', style: theme.textTheme.h4),
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(LucideIcons.user), text: 'Profile'),
            Tab(icon: Icon(LucideIcons.settings), text: 'Preferences'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Error/Success Messages
          if (profileState.error != null)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: FormSubmissionErrorAlertWidget(
                title: 'Error',
                description: profileState.error!,
              ),
            ),

          if (profileState.successMessage != null)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ShadAlert(
                icon: Icon(LucideIcons.check, size: 16),
                title: const Text('Success'),
                description: Text(profileState.successMessage!),
              ),
            ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Profile Tab
                RefreshIndicator.adaptive(
                  onRefresh: _refreshProfile,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(16.0),
                    child: ProfileInfoWidget(
                      onEditPressed: _navigateToEditProfile,
                    ),
                  ),
                ),

                // Preferences Tab
                RefreshIndicator.adaptive(
                  onRefresh: _refreshProfile,
                  child: const SingleChildScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    padding: EdgeInsets.all(16.0),
                    child: PreferencesWidget(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshProfile() async {
    await ref.read(profileProvider.notifier).loadProfile();
  }

  void _navigateToEditProfile() {
    Navigator.of(context).pushNamed(ProfileEditPage.routeName).then((_) {
      // Refresh profile when returning from edit page
      ref.read(profileProvider.notifier).loadProfile();
    });
  }
}

/// Responsive layout for larger screens
class ResponsiveProfileViewPage extends ConsumerStatefulWidget {
  static const String routeName = '/claimant/profile-responsive';

  const ResponsiveProfileViewPage({super.key});

  @override
  ConsumerState<ResponsiveProfileViewPage> createState() =>
      _ResponsiveProfileViewPageState();
}

class _ResponsiveProfileViewPageState
    extends ConsumerState<ResponsiveProfileViewPage> {
  @override
  void initState() {
    super.initState();

    // Load profile data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(profileProvider.notifier).loadProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(profileProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('My Profile', style: theme.textTheme.h4),
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
      ),
      body: RefreshIndicator.adaptive(
        onRefresh: _refreshProfile,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Error/Success Messages
              if (profileState.error != null) ...[
                FormSubmissionErrorAlertWidget(
                  title: 'Error',
                  description: profileState.error!,
                ),
                const SizedBox(height: 16),
              ],

              if (profileState.successMessage != null) ...[
                ShadAlert(
                  icon: Icon(LucideIcons.check, size: 16),
                  title: const Text('Success'),
                  description: Text(profileState.successMessage!),
                ),
                const SizedBox(height: 16),
              ],

              // Desktop Layout - Side by side
              LayoutBuilder(
                builder: (context, constraints) {
                  if (constraints.maxWidth > 1024) {
                    // Desktop layout
                    return Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 2,
                          child: ProfileInfoWidget(
                            onEditPressed: _navigateToEditProfile,
                          ),
                        ),
                        const SizedBox(width: 24),
                        const Expanded(flex: 1, child: PreferencesWidget()),
                      ],
                    );
                  } else {
                    // Mobile/Tablet layout
                    return Column(
                      children: [
                        ProfileInfoWidget(
                          onEditPressed: _navigateToEditProfile,
                        ),
                        const SizedBox(height: 24),
                        const PreferencesWidget(),
                      ],
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _refreshProfile() async {
    await ref.read(profileProvider.notifier).loadProfile();
  }

  void _navigateToEditProfile() {
    Navigator.of(context).pushNamed(ProfileEditPage.routeName).then((_) {
      // Refresh profile when returning from edit page
      ref.read(profileProvider.notifier).loadProfile();
    });
  }
}
