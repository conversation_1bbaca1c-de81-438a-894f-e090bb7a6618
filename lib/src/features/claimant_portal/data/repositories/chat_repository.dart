import 'dart:async';
import 'dart:io';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/chat_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/chat_message_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/chat_conversation_model.dart';

/// Repository for chat data access and management
/// Provides abstraction layer over chat service with caching and offline support
class ChatRepository {
  final ChatService _chatService;
  
  // In-memory cache for conversations and messages
  final Map<String, List<ChatMessageModel>> _messagesCache = {};
  final Map<String, ChatConversationModel> _conversationsCache = {};
  final List<ChatMessageModel> _pendingMessages = [];
  
  // Stream controllers for reactive data
  final StreamController<List<ChatConversationModel>> _conversationsController =
      StreamController<List<ChatConversationModel>>.broadcast();
  final StreamController<List<ChatMessageModel>> _messagesController =
      StreamController<List<ChatMessageModel>>.broadcast();
  final StreamController<ChatMessageModel> _newMessageController =
      StreamController<ChatMessageModel>.broadcast();

  ChatRepository(this._chatService) {
    _initializeStreams();
  }

  /// Stream of conversations
  Stream<List<ChatConversationModel>> get conversationsStream => 
      _conversationsController.stream;

  /// Stream of messages for current conversation
  Stream<List<ChatMessageModel>> get messagesStream => 
      _messagesController.stream;

  /// Stream of new messages
  Stream<ChatMessageModel> get newMessageStream => 
      _newMessageController.stream;

  /// Initialize stream listeners
  void _initializeStreams() {
    // Listen to new messages from service
    _chatService.newMessageStream.listen((message) {
      _handleNewMessage(message);
    });
  }

  /// Get conversations for current claimant with caching
  Future<List<ChatConversationModel>> getConversations({
    bool forceRefresh = false,
  }) async {
    try {
      LoggerService.info('Getting conversations (forceRefresh: $forceRefresh)');

      // Return cached data if available and not forcing refresh
      if (!forceRefresh && _conversationsCache.isNotEmpty) {
        final conversations = _conversationsCache.values.toList();
        _conversationsController.add(conversations);
        return conversations;
      }

      // Fetch from service
      final conversations = await _chatService.getConversationsForClaimant();
      
      // Update cache
      _conversationsCache.clear();
      for (final conversation in conversations) {
        _conversationsCache[conversation.claimId] = conversation;
      }

      // Emit to stream
      _conversationsController.add(conversations);
      
      LoggerService.info('Retrieved ${conversations.length} conversations');
      return conversations;
    } catch (e) {
      LoggerService.error('Error getting conversations', e);
      
      // Return cached data if available
      if (_conversationsCache.isNotEmpty) {
        final conversations = _conversationsCache.values.toList();
        _conversationsController.add(conversations);
        return conversations;
      }
      
      rethrow;
    }
  }

  /// Get messages for a conversation with caching
  Future<List<ChatMessageModel>> getMessages({
    required String claimId,
    bool forceRefresh = false,
  }) async {
    try {
      LoggerService.info('Getting messages for claim: $claimId (forceRefresh: $forceRefresh)');

      // Return cached data if available and not forcing refresh
      if (!forceRefresh && _messagesCache.containsKey(claimId)) {
        final messages = _messagesCache[claimId]!;
        _messagesController.add(messages);
        return messages;
      }

      // Fetch from service
      final messages = await _chatService.getMessagesForConversation(claimId);
      
      // Update cache
      _messagesCache[claimId] = messages;

      // Emit to stream
      _messagesController.add(messages);
      
      LoggerService.info('Retrieved ${messages.length} messages for claim $claimId');
      return messages;
    } catch (e) {
      LoggerService.error('Error getting messages for claim $claimId', e);
      
      // Return cached data if available
      if (_messagesCache.containsKey(claimId)) {
        final messages = _messagesCache[claimId]!;
        _messagesController.add(messages);
        return messages;
      }
      
      rethrow;
    }
  }

  /// Send a text message with offline support
  Future<ChatMessageModel> sendMessage({
    required String claimId,
    required String content,
    String? recipientId,
    String? recipientGroup = 'agent',
    Map<String, dynamic>? metadata,
  }) async {
    try {
      LoggerService.info('Sending message to claim: $claimId');

      // Create optimistic message for immediate UI update
      final optimisticMessage = ChatMessageModel(
        id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
        conversationId: claimId,
        senderId: _chatService.currentUserId ?? '',
        recipientGroup: recipientGroup,
        messageContent: content,
        messageType: MessageType.text,
        status: MessageStatus.sending,
        timestamp: DateTime.now(),
      );

      // Add to pending messages
      _pendingMessages.add(optimisticMessage);
      
      // Update cache and emit immediately
      _addMessageToCache(claimId, optimisticMessage);

      try {
        // Send via service
        final sentMessage = await _chatService.sendMessage(
          claimId: claimId,
          content: content,
          recipientId: recipientId,
          recipientGroup: recipientGroup,
          metadata: metadata,
        );

        // Remove from pending and update cache with real message
        _pendingMessages.removeWhere((msg) => msg.id == optimisticMessage.id);
        _replaceMessageInCache(claimId, optimisticMessage.id, sentMessage);

        LoggerService.info('Message sent successfully: ${sentMessage.id}');
        return sentMessage;
      } catch (e) {
        // Mark message as failed
        final failedMessage = optimisticMessage.copyWith(status: MessageStatus.failed);
        _replaceMessageInCache(claimId, optimisticMessage.id, failedMessage);
        
        LoggerService.error('Failed to send message', e);
        rethrow;
      }
    } catch (e) {
      LoggerService.error('Error in sendMessage', e);
      rethrow;
    }
  }

  /// Send a message with file attachment
  Future<ChatMessageModel> sendMessageWithFile({
    required String claimId,
    required String content,
    required File file,
    String? recipientId,
    String? recipientGroup = 'agent',
    Map<String, dynamic>? metadata,
  }) async {
    try {
      LoggerService.info('Sending message with file to claim: $claimId');

      // Send via service (no optimistic update for file messages due to complexity)
      final sentMessage = await _chatService.sendMessageWithFile(
        claimId: claimId,
        content: content,
        file: file,
        recipientId: recipientId,
        recipientGroup: recipientGroup,
        metadata: metadata,
      );

      // Update cache
      _addMessageToCache(claimId, sentMessage);

      LoggerService.info('Message with file sent successfully: ${sentMessage.id}');
      return sentMessage;
    } catch (e) {
      LoggerService.error('Error sending message with file', e);
      rethrow;
    }
  }

  /// Create a new conversation
  Future<ChatConversationModel> createConversation({
    required String claimId,
    String? title,
  }) async {
    try {
      LoggerService.info('Creating conversation for claim: $claimId');

      final conversation = await _chatService.createConversation(
        claimId: claimId,
        title: title,
      );

      // Update cache
      _conversationsCache[claimId] = conversation;
      
      // Emit updated conversations
      final conversations = _conversationsCache.values.toList();
      _conversationsController.add(conversations);

      LoggerService.info('Conversation created successfully: ${conversation.id}');
      return conversation;
    } catch (e) {
      LoggerService.error('Error creating conversation', e);
      rethrow;
    }
  }

  /// Subscribe to real-time updates for a conversation
  Future<void> subscribeToConversationUpdates(String claimId) async {
    try {
      await _chatService.subscribeToConversationUpdates(claimId);
      LoggerService.info('Subscribed to updates for claim: $claimId');
    } catch (e) {
      LoggerService.error('Error subscribing to conversation updates', e);
      rethrow;
    }
  }

  /// Unsubscribe from real-time updates
  Future<void> unsubscribeFromUpdates() async {
    try {
      await _chatService.unsubscribeFromUpdates();
      LoggerService.info('Unsubscribed from conversation updates');
    } catch (e) {
      LoggerService.error('Error unsubscribing from updates', e);
    }
  }

  /// Mark messages as read
  Future<void> markMessagesAsRead({
    required String claimId,
    required List<String> messageIds,
  }) async {
    try {
      await _chatService.markMessagesAsRead(
        claimId: claimId,
        messageIds: messageIds,
      );

      // Update cache to mark messages as read
      if (_messagesCache.containsKey(claimId)) {
        final messages = _messagesCache[claimId]!;
        final updatedMessages = messages.map((msg) {
          if (messageIds.contains(msg.id)) {
            return msg.copyWith(isRead: true, readAt: DateTime.now());
          }
          return msg;
        }).toList();
        
        _messagesCache[claimId] = updatedMessages;
        _messagesController.add(updatedMessages);
      }

      LoggerService.info('Marked ${messageIds.length} messages as read');
    } catch (e) {
      LoggerService.error('Error marking messages as read', e);
      rethrow;
    }
  }

  /// Retry failed messages
  Future<void> retryFailedMessages() async {
    final failedMessages = _pendingMessages
        .where((msg) => msg.status == MessageStatus.failed)
        .toList();

    for (final message in failedMessages) {
      try {
        await sendMessage(
          claimId: message.conversationId,
          content: message.messageContent,
          recipientGroup: message.recipientGroup,
          metadata: message.metadata,
        );
      } catch (e) {
        LoggerService.error('Failed to retry message: ${message.id}', e);
      }
    }
  }

  /// Clear cache
  void clearCache() {
    _messagesCache.clear();
    _conversationsCache.clear();
    _pendingMessages.clear();
    LoggerService.info('Chat cache cleared');
  }

  /// Handle new message from service
  void _handleNewMessage(ChatMessageModel message) {
    _addMessageToCache(message.conversationId, message);
    _newMessageController.add(message);
  }

  /// Add message to cache and emit update
  void _addMessageToCache(String claimId, ChatMessageModel message) {
    if (!_messagesCache.containsKey(claimId)) {
      _messagesCache[claimId] = [];
    }
    
    _messagesCache[claimId]!.add(message);
    _messagesCache[claimId]!.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    _messagesController.add(_messagesCache[claimId]!);
  }

  /// Replace message in cache
  void _replaceMessageInCache(String claimId, String oldMessageId, ChatMessageModel newMessage) {
    if (_messagesCache.containsKey(claimId)) {
      final messages = _messagesCache[claimId]!;
      final index = messages.indexWhere((msg) => msg.id == oldMessageId);
      if (index != -1) {
        messages[index] = newMessage;
        _messagesController.add(messages);
      }
    }
  }

  /// Dispose resources
  void dispose() {
    _conversationsController.close();
    _messagesController.close();
    _newMessageController.close();
    _chatService.dispose();
  }
}
