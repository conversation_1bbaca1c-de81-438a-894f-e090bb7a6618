import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/claims_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_claim_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claim_status_model.dart';

/// Repository for claims data with caching and offline support
/// Provides an abstraction layer between the UI and data services
class ClaimsRepository {
  final ClaimsService _claimsService;
  final String _cacheKeyPrefix = 'claims_cache_';
  final String _lastUpdateKey = 'claims_last_update';
  final Duration _cacheExpiry = const Duration(minutes: 15);

  // In-memory cache
  List<ClaimantClaim>? _cachedClaims;
  DateTime? _lastCacheUpdate;

  // Stream controllers for reactive data
  final StreamController<List<ClaimantClaim>> _claimsController =
      StreamController<List<ClaimantClaim>>.broadcast();
  final StreamController<ClaimantClaim> _claimUpdatesController =
      StreamController<ClaimantClaim>.broadcast();

  ClaimsRepository(this._claimsService) {
    _setupServiceStreams();
  }

  /// Stream of claims data
  Stream<List<ClaimantClaim>> get claimsStream => _claimsController.stream;

  /// Stream of individual claim updates
  Stream<ClaimantClaim> get claimUpdatesStream =>
      _claimUpdatesController.stream;

  /// Setup streams from the service
  void _setupServiceStreams() {
    _claimsService.claimsStream.listen((claims) {
      _updateCache(claims);
      _claimsController.add(claims);
    });

    _claimsService.claimUpdatesStream.listen((claim) {
      _claimUpdatesController.add(claim);
    });
  }

  /// Get claims for the current claimant with caching
  Future<List<ClaimantClaim>> getClaims({bool forceRefresh = false}) async {
    try {
      LoggerService.info('Getting claims (forceRefresh: $forceRefresh)');

      // Check if we have valid cached data and don't need to force refresh
      if (!forceRefresh && _hasValidCache()) {
        LoggerService.info('Returning cached claims data');
        return _cachedClaims!;
      }

      // Try to get from persistent cache first if no in-memory cache
      if (_cachedClaims == null && !forceRefresh) {
        final cachedClaims = await _loadFromPersistentCache();
        if (cachedClaims != null) {
          LoggerService.info('Loaded claims from persistent cache');
          _cachedClaims = cachedClaims;
          _lastCacheUpdate = DateTime.now();
          _claimsController.add(cachedClaims);
          return cachedClaims;
        }
      }

      // Fetch fresh data from service
      LoggerService.info('Fetching fresh claims data from service');
      final claims = await _claimsService.getClaimsForClaimant();

      // Update cache
      await _updateCache(claims);

      return claims;
    } catch (e) {
      LoggerService.error('Error getting claims from repository', e);

      // Return cached data if available, even if expired
      if (_cachedClaims != null) {
        LoggerService.info('Returning stale cached data due to error');
        return _cachedClaims!;
      }

      rethrow;
    }
  }

  /// Get specific claim by ID
  Future<ClaimantClaim?> getClaimById(String claimId) async {
    try {
      LoggerService.info('Getting claim by ID: $claimId');

      // First check in-memory cache
      if (_cachedClaims != null) {
        final matchingClaims =
            _cachedClaims!.where((claim) => claim.id == claimId).toList();
        if (matchingClaims.isNotEmpty) {
          final cachedClaim = matchingClaims.first;
          LoggerService.info('Found claim in cache: ${cachedClaim.title}');
          return cachedClaim;
        }
      }

      // Fetch from service
      return await _claimsService.getClaimDetails(claimId);
    } catch (e) {
      LoggerService.error('Error getting claim by ID: $claimId', e);
      rethrow;
    }
  }

  /// Get claims by status with caching
  Future<List<ClaimantClaim>> getClaimsByStatus(ClaimStatus status) async {
    try {
      final allClaims = await getClaims();
      return allClaims.where((claim) => claim.status == status).toList();
    } catch (e) {
      LoggerService.error('Error getting claims by status: ${status.value}', e);
      rethrow;
    }
  }

  /// Get active claims
  Future<List<ClaimantClaim>> getActiveClaims() async {
    try {
      final allClaims = await getClaims();
      return allClaims.where((claim) => claim.isActive).toList();
    } catch (e) {
      LoggerService.error('Error getting active claims', e);
      rethrow;
    }
  }

  /// Get funded claims
  Future<List<ClaimantClaim>> getFundedClaims() async {
    try {
      final allClaims = await getClaims();
      return allClaims.where((claim) => claim.isFunded).toList();
    } catch (e) {
      LoggerService.error('Error getting funded claims', e);
      rethrow;
    }
  }

  /// Search claims
  Future<List<ClaimantClaim>> searchClaims(String query) async {
    try {
      if (query.trim().isEmpty) {
        return getClaims();
      }

      final allClaims = await getClaims();
      final lowercaseQuery = query.toLowerCase();

      return allClaims.where((claim) {
        return claim.title.toLowerCase().contains(lowercaseQuery) ||
            (claim.description?.toLowerCase().contains(lowercaseQuery) ??
                false) ||
            (claim.claimType?.toLowerCase().contains(lowercaseQuery) ??
                false) ||
            (claim.claimIndustry?.toLowerCase().contains(lowercaseQuery) ??
                false);
      }).toList();
    } catch (e) {
      LoggerService.error('Error searching claims', e);
      rethrow;
    }
  }

  /// Get claims statistics
  Future<Map<String, int>> getClaimsStatistics() async {
    try {
      final allClaims = await getClaims();

      return {
        'total': allClaims.length,
        'active': allClaims.where((claim) => claim.isActive).length,
        'funded': allClaims.where((claim) => claim.isFunded).length,
        'completed':
            allClaims
                .where((claim) => claim.status == ClaimStatus.completed)
                .length,
        'draft':
            allClaims
                .where((claim) => claim.status == ClaimStatus.draft)
                .length,
        'submitted':
            allClaims
                .where((claim) => claim.status == ClaimStatus.submitted)
                .length,
        'under_review':
            allClaims
                .where((claim) => claim.status == ClaimStatus.underReview)
                .length,
        'approved':
            allClaims
                .where((claim) => claim.status == ClaimStatus.approved)
                .length,
        'rejected':
            allClaims
                .where((claim) => claim.status == ClaimStatus.rejected)
                .length,
      };
    } catch (e) {
      LoggerService.error('Error getting claims statistics', e);
      rethrow;
    }
  }

  /// Get claim status history
  Future<ClaimStatusHistory?> getClaimStatusHistory(String claimId) async {
    try {
      return await _claimsService.getClaimStatusHistory(claimId);
    } catch (e) {
      LoggerService.error('Error getting claim status history', e);
      rethrow;
    }
  }

  /// Subscribe to real-time updates
  Future<void> subscribeToUpdates() async {
    try {
      await _claimsService.subscribeToClaimUpdates();
    } catch (e) {
      LoggerService.error('Error subscribing to claim updates', e);
      rethrow;
    }
  }

  /// Refresh claims data
  Future<List<ClaimantClaim>> refresh() async {
    return getClaims(forceRefresh: true);
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    try {
      LoggerService.info('Clearing claims cache');

      _cachedClaims = null;
      _lastCacheUpdate = null;

      final prefs = await SharedPreferences.getInstance();
      final keys =
          prefs
              .getKeys()
              .where(
                (key) =>
                    key.startsWith(_cacheKeyPrefix) || key == _lastUpdateKey,
              )
              .toList();

      for (final key in keys) {
        await prefs.remove(key);
      }

      LoggerService.info('Claims cache cleared successfully');
    } catch (e) {
      LoggerService.error('Error clearing claims cache', e);
    }
  }

  /// Check if we have valid cached data
  bool _hasValidCache() {
    if (_cachedClaims == null || _lastCacheUpdate == null) {
      return false;
    }

    final now = DateTime.now();
    final cacheAge = now.difference(_lastCacheUpdate!);
    return cacheAge < _cacheExpiry;
  }

  /// Update in-memory and persistent cache
  Future<void> _updateCache(List<ClaimantClaim> claims) async {
    try {
      _cachedClaims = claims;
      _lastCacheUpdate = DateTime.now();

      // Save to persistent cache
      await _saveToPersistentCache(claims);

      LoggerService.info('Updated claims cache with ${claims.length} claims');
    } catch (e) {
      LoggerService.error('Error updating claims cache', e);
    }
  }

  /// Save claims to persistent cache
  Future<void> _saveToPersistentCache(List<ClaimantClaim> claims) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final claimsJson = claims.map((claim) => claim.toJson()).toList();
      final cacheData = {
        'claims': claimsJson,
        'timestamp': DateTime.now().toIso8601String(),
      };

      await prefs.setString('${_cacheKeyPrefix}data', jsonEncode(cacheData));
      await prefs.setString(_lastUpdateKey, DateTime.now().toIso8601String());

      LoggerService.info('Saved ${claims.length} claims to persistent cache');
    } catch (e) {
      LoggerService.error('Error saving claims to persistent cache', e);
    }
  }

  /// Load claims from persistent cache
  Future<List<ClaimantClaim>?> _loadFromPersistentCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheDataString = prefs.getString('${_cacheKeyPrefix}data');

      if (cacheDataString == null) return null;

      final cacheData = jsonDecode(cacheDataString) as Map<String, dynamic>;
      final timestamp = DateTime.parse(cacheData['timestamp'] as String);

      // Check if cache is still valid
      final cacheAge = DateTime.now().difference(timestamp);
      if (cacheAge > _cacheExpiry) {
        LoggerService.info(
          'Persistent cache expired, age: ${cacheAge.inMinutes} minutes',
        );
        return null;
      }

      final claimsJson = cacheData['claims'] as List;
      final claims =
          claimsJson
              .map(
                (json) => ClaimantClaim.fromJson(json as Map<String, dynamic>),
              )
              .toList();

      LoggerService.info(
        'Loaded ${claims.length} claims from persistent cache',
      );
      return claims;
    } catch (e) {
      LoggerService.error('Error loading claims from persistent cache', e);
      return null;
    }
  }

  /// Dispose of resources
  void dispose() {
    LoggerService.info('Disposing ClaimsRepository resources');

    _claimsController.close();
    _claimUpdatesController.close();
    _claimsService.dispose();

    LoggerService.info('ClaimsRepository disposed successfully');
  }
}
