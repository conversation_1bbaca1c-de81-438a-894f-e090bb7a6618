import 'package:flutter/foundation.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/enhanced_notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_notification_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/data/models/enhanced_notification_model.dart';

/// Service for managing claimant-specific notifications
/// Wraps the enhanced notification service with claimant-specific logic
class ClaimantNotificationsService {
  final EnhancedNotificationService _enhancedService;
  final PocketBaseService _pocketBaseService;
  final ValueNotifier<List<ClaimantNotificationModel>> _notifications =
      ValueNotifier<List<ClaimantNotificationModel>>([]);

  ClaimantNotificationsService(this._pocketBaseService)
    : _enhancedService = EnhancedNotificationService(_pocketBaseService);

  /// Get the notifications stream
  ValueNotifier<List<ClaimantNotificationModel>> get notifications =>
      _notifications;

  /// Initialize the service
  Future<void> initialize() async {
    try {
      LoggerService.info('Initializing claimant notifications service');

      // Initialize the enhanced service
      await _enhancedService.initialize();

      // Listen to enhanced notifications and convert to claimant notifications
      _enhancedService.notifications.addListener(
        _onEnhancedNotificationsChanged,
      );

      // Initial conversion
      _onEnhancedNotificationsChanged();

      LoggerService.info(
        'Claimant notifications service initialized successfully',
      );
    } catch (e) {
      LoggerService.error(
        'Error initializing claimant notifications service',
        e,
      );
      rethrow;
    }
  }

  /// Handle changes in enhanced notifications
  void _onEnhancedNotificationsChanged() {
    try {
      final enhancedNotifications = _enhancedService.notifications.value;
      final claimantNotifications =
          enhancedNotifications
              .map((enhanced) => _convertToClaimantNotification(enhanced))
              .toList();

      _notifications.value = claimantNotifications;

      LoggerService.info(
        'Converted ${enhancedNotifications.length} enhanced notifications to claimant notifications',
      );
    } catch (e) {
      LoggerService.error('Error converting enhanced notifications', e);
    }
  }

  /// Convert enhanced notification to claimant notification
  ClaimantNotificationModel _convertToClaimantNotification(
    EnhancedNotificationModel enhanced,
  ) {
    // Extract claim ID from metadata or link if available
    String? claimId;
    String priority = 'normal';
    Map<String, dynamic>? metadata;

    // Try to extract claim ID from the link or related_item_id
    if (enhanced.link != null && enhanced.link!.contains('/claim/')) {
      final parts = enhanced.link!.split('/claim/');
      if (parts.length > 1) {
        claimId = parts[1].split('/').first;
      }
    }

    // Determine priority based on notification type
    switch (enhanced.type.toLowerCase()) {
      case 'urgent':
      case 'action_required':
        priority = 'urgent';
        break;
      case 'alert':
      case 'warning':
        priority = 'high';
        break;
      case 'info':
      case 'success':
        priority = 'normal';
        break;
      default:
        priority = 'normal';
    }

    return ClaimantNotificationModel.fromEnhanced(
      enhanced,
      claimId: claimId,
      priority: priority,
      metadata: metadata,
    );
  }

  /// Get notifications for a specific claimant
  Future<List<ClaimantNotificationModel>> getNotificationsForClaimant(
    String claimantId, {
    int page = 1,
    int perPage = 20,
    bool unreadOnly = false,
    String? claimId,
    ClaimantNotificationType? type,
  }) async {
    try {
      LoggerService.info(
        'Getting notifications for claimant $claimantId (page: $page, perPage: $perPage, unreadOnly: $unreadOnly)',
      );

      var notifications = _notifications.value;

      // Filter by read status
      if (unreadOnly) {
        notifications = notifications.where((n) => !n.isRead).toList();
      }

      // Filter by claim ID
      if (claimId != null) {
        notifications =
            notifications.where((n) => n.claimId == claimId).toList();
      }

      // Filter by type
      if (type != null) {
        notifications =
            notifications.where((n) => n.type == type.value).toList();
      }

      // Sort by created date (newest first)
      notifications.sort((a, b) {
        if (a.created == null && b.created == null) return 0;
        if (a.created == null) return 1;
        if (b.created == null) return -1;
        return b.created!.compareTo(a.created!);
      });

      // Apply pagination
      final startIndex = (page - 1) * perPage;
      final endIndex = startIndex + perPage;

      if (startIndex >= notifications.length) {
        return [];
      }

      final paginatedNotifications = notifications.sublist(
        startIndex,
        endIndex > notifications.length ? notifications.length : endIndex,
      );

      LoggerService.info(
        'Returning ${paginatedNotifications.length} notifications for claimant $claimantId',
      );

      return paginatedNotifications;
    } catch (e) {
      LoggerService.error('Error getting notifications for claimant', e);
      rethrow;
    }
  }

  /// Mark a notification as read
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      LoggerService.info('Marking notification $notificationId as read');

      await _enhancedService.markAsRead(notificationId);

      LoggerService.info(
        'Notification $notificationId marked as read successfully',
      );
    } catch (e) {
      LoggerService.error('Error marking notification as read', e);
      rethrow;
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      LoggerService.info('Marking all notifications as read');

      final unreadNotifications = _notifications.value.where((n) => !n.isRead);

      for (final notification in unreadNotifications) {
        await markNotificationAsRead(notification.id);
      }

      LoggerService.info('All notifications marked as read successfully');
    } catch (e) {
      LoggerService.error('Error marking all notifications as read', e);
      rethrow;
    }
  }

  /// Get unread count
  int get unreadCount {
    return _notifications.value.where((n) => !n.isRead).length;
  }

  /// Get unread count for a specific claim
  int getUnreadCountForClaim(String claimId) {
    return _notifications.value
        .where((n) => !n.isRead && n.claimId == claimId)
        .length;
  }

  /// Subscribe to real-time notifications (already handled by enhanced service)
  Future<void> subscribeToNotifications() async {
    // The enhanced service already handles real-time subscriptions
    // This method is here for API compatibility
    LoggerService.info(
      'Real-time notifications already subscribed via enhanced service',
    );
  }

  /// Dispose resources
  void dispose() {
    try {
      _enhancedService.notifications.removeListener(
        _onEnhancedNotificationsChanged,
      );
      _notifications.dispose();
      LoggerService.info('Claimant notifications service disposed');
    } catch (e) {
      LoggerService.error('Error disposing claimant notifications service', e);
    }
  }

  /// Get notifications grouped by date
  Map<String, List<ClaimantNotificationModel>> getNotificationsGroupedByDate() {
    final notifications = _notifications.value;
    final grouped = <String, List<ClaimantNotificationModel>>{};

    for (final notification in notifications) {
      final group = notification.dateGroup;
      grouped.putIfAbsent(group, () => []).add(notification);
    }

    return grouped;
  }

  /// Get notification statistics
  Map<String, int> getNotificationStats() {
    final notifications = _notifications.value;

    return {
      'total': notifications.length,
      'unread': notifications.where((n) => !n.isRead).length,
      'urgent': notifications.where((n) => n.isUrgent).length,
      'high_priority': notifications.where((n) => n.isHighPriority).length,
      'claim_updates':
          notifications
              .where(
                (n) =>
                    n.notificationType == ClaimantNotificationType.claimUpdate,
              )
              .length,
      'funding_updates':
          notifications
              .where(
                (n) =>
                    n.notificationType ==
                    ClaimantNotificationType.fundingUpdate,
              )
              .length,
      'chat_messages':
          notifications
              .where(
                (n) =>
                    n.notificationType == ClaimantNotificationType.chatMessage,
              )
              .length,
    };
  }
}
