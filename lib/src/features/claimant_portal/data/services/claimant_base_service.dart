import 'package:pocketbase/pocketbase.dart';
import 'package:http/http.dart' as http;
import 'package:three_pay_group_litigation_platform/src/core/services/enhanced_notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/data/models/enhanced_notification_model.dart';

/// Base service class for claimant portal operations
/// Provides common functionality and follows existing codebase patterns
class ClaimantBaseService {
  final PocketBase _pb;
  final PocketBaseService _pocketBaseService;
  late final EnhancedNotificationService _notificationService;

  ClaimantBaseService()
    : _pb = PocketBaseService().pb,
      _pocketBaseService = PocketBaseService() {
    _notificationService = EnhancedNotificationService(_pocketBaseService);
  }

  /// Get the current authenticated user
  RecordModel? get currentUser => _pb.authStore.record;

  /// Check if user is authenticated
  bool get isAuthenticated => _pb.authStore.isValid;

  /// Check if current user is a claimant
  bool get isClaimant {
    final user = currentUser;
    return user != null && user.data['user_type'] == 'claimant';
  }

  /// Get current user ID
  String? get currentUserId => currentUser?.id;

  /// Get PocketBase client for subclasses
  PocketBase get pb => _pb;

  /// Verify authentication and claimant role
  void _verifyClaimantAuth() {
    if (!isAuthenticated) {
      throw Exception('User not authenticated');
    }
    if (!isClaimant) {
      throw Exception('User is not a claimant');
    }
  }

  /// Get claimant profile for current user
  Future<ClaimantProfile?> getCurrentClaimantProfile() async {
    try {
      _verifyClaimantAuth();

      final userId = currentUserId!;
      final records = await _pb
          .collection('claimant_profiles')
          .getList(filter: 'user_id = "$userId"', expand: 'user_id');

      if (records.items.isEmpty) {
        LoggerService.info('No claimant profile found for user: $userId');
        return null;
      }

      return ClaimantProfile.fromJson(records.items.first.toJson());
    } catch (e) {
      LoggerService.error('Error getting claimant profile', e);
      rethrow;
    }
  }

  /// Create claimant profile for current user
  Future<ClaimantProfile> createClaimantProfile({
    Map<String, dynamic>? notificationPreferences,
  }) async {
    try {
      _verifyClaimantAuth();

      final userId = currentUserId!;

      final profileData = {
        'user_id': userId,
        'associated_claim_ids': <String>[],
        'notification_preferences': notificationPreferences ?? {},
      };

      final record = await _pb
          .collection('claimant_profiles')
          .create(body: profileData, expand: 'user_id');

      LoggerService.info('Created claimant profile for user: $userId');
      return ClaimantProfile.fromJson(record.toJson());
    } catch (e) {
      LoggerService.error('Error creating claimant profile', e);
      rethrow;
    }
  }

  /// Update claimant profile
  Future<ClaimantProfile> updateClaimantProfile(
    String profileId,
    Map<String, dynamic> updates,
  ) async {
    try {
      _verifyClaimantAuth();

      final record = await _pb
          .collection('claimant_profiles')
          .update(profileId, body: updates, expand: 'user_id');

      LoggerService.info('Updated claimant profile: $profileId');
      return ClaimantProfile.fromJson(record.toJson());
    } catch (e) {
      LoggerService.error('Error updating claimant profile', e);
      rethrow;
    }
  }

  /// Get funding applications associated with claimant
  Future<List<RecordModel>> getAssociatedFundingApplications() async {
    try {
      _verifyClaimantAuth();

      final profile = await getCurrentClaimantProfile();
      if (profile == null) {
        LoggerService.warning(
          'No claimant profile found when getting funding applications',
        );
        return [];
      }

      if (profile.associatedClaimIds.isEmpty) {
        LoggerService.info(
          'Claimant ${profile.id} has no associated claim IDs',
        );
        return [];
      }

      LoggerService.info(
        'Fetching funding applications for claim IDs: ${profile.associatedClaimIds}',
      );

      // Build filter for multiple IDs using OR conditions
      final filterConditions = profile.associatedClaimIds
          .map((id) => 'id = "$id"')
          .join(' || ');

      final filter =
          filterConditions.isNotEmpty ? '($filterConditions)' : 'id = ""';
      LoggerService.info('Using filter: $filter');

      final records = await _pb
          .collection('funding_applications')
          .getList(filter: filter);

      LoggerService.info(
        'Found ${records.items.length} funding applications for claimant ${profile.id}',
      );

      // Log details about each funding application for debugging
      for (final record in records.items) {
        final status = record.data['application_status'] as String?;
        final stage = record.data['stage'] as String?;
        final title = record.data['claim_title'] as String?;
        LoggerService.info(
          'Funding Application ${record.id}: status=$status, stage=$stage, title=$title',
        );
      }

      return records.items;
    } catch (e) {
      LoggerService.error('Error getting associated funding applications', e);
      rethrow;
    }
  }

  /// Get notifications for current claimant using enhanced notification service
  Future<List<EnhancedNotificationModel>> getClaimantNotifications({
    int page = 1,
    int perPage = 20,
    bool unreadOnly = false,
  }) async {
    try {
      _verifyClaimantAuth();

      // Initialize the notification service if not already done
      await _notificationService.initialize();

      // Get all notifications from the enhanced service
      final allNotifications = _notificationService.notifications.value;

      LoggerService.info(
        'Enhanced notification service has ${allNotifications.length} total notifications',
      );

      // Filter by read status if needed
      List<EnhancedNotificationModel> filteredNotifications = allNotifications;
      if (unreadOnly) {
        filteredNotifications =
            allNotifications.where((n) => !n.isRead).toList();
        LoggerService.info(
          'Filtered to ${filteredNotifications.length} unread notifications',
        );
      }

      // Apply pagination
      final startIndex = (page - 1) * perPage;
      final endIndex = startIndex + perPage;

      if (startIndex >= filteredNotifications.length) {
        return [];
      }

      final paginatedNotifications = filteredNotifications.sublist(
        startIndex,
        endIndex > filteredNotifications.length
            ? filteredNotifications.length
            : endIndex,
      );

      LoggerService.info(
        'Returning ${paginatedNotifications.length} notifications (page $page, perPage $perPage, unreadOnly: $unreadOnly)',
      );

      // Log notification details for debugging
      if (unreadOnly) {
        for (final notification in paginatedNotifications) {
          LoggerService.info(
            'Unread notification ${notification.id}: title=${notification.title}, isRead=${notification.isRead}',
          );
        }
      }

      return paginatedNotifications;
    } catch (e) {
      LoggerService.error('Error getting claimant notifications', e);
      rethrow;
    }
  }

  /// Get notifications for current claimant (legacy method returning RecordModel for compatibility)
  Future<List<RecordModel>> getClaimantNotificationsLegacy({
    int page = 1,
    int perPage = 20,
    bool unreadOnly = false,
  }) async {
    try {
      _verifyClaimantAuth();

      final userId = currentUserId!;
      String filter = '(recipientId ~ "$userId" || recipientId:length < 1)';

      if (unreadOnly) {
        filter += ' && isRead = false';
      }

      LoggerService.info(
        'Fetching notifications for user $userId with filter: $filter (unreadOnly: $unreadOnly)',
      );

      final records = await _pb
          .collection('notifications')
          .getList(
            page: page,
            perPage: perPage,
            filter: filter,
            sort: '-created',
          );

      LoggerService.info(
        'Found ${records.items.length} notifications for user $userId (unreadOnly: $unreadOnly)',
      );

      return records.items;
    } catch (e) {
      LoggerService.error('Error getting claimant notifications (legacy)', e);
      rethrow;
    }
  }

  /// Mark notification as read using enhanced notification service
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      _verifyClaimantAuth();

      // Use the enhanced notification service to mark as read
      await _notificationService.markAsRead(notificationId);

      LoggerService.info(
        'Marked notification as read using enhanced service: $notificationId',
      );
    } catch (e) {
      LoggerService.error('Error marking notification as read', e);
      rethrow;
    }
  }

  /// Update user profile information (name, email, phone, address)
  Future<RecordModel> updateUserProfile({
    String? name,
    String? firstName,
    String? lastName,
    String? mobile,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? postcode,
  }) async {
    try {
      _verifyClaimantAuth();

      final userId = currentUserId!;
      final updates = <String, dynamic>{};

      if (name != null) updates['name'] = name;
      if (firstName != null) updates['first_name'] = firstName;
      if (lastName != null) updates['last_name'] = lastName;
      if (mobile != null) updates['mobile'] = mobile;
      if (addressLine1 != null) updates['address_line1'] = addressLine1;
      if (addressLine2 != null) updates['address_line2'] = addressLine2;
      if (city != null) updates['city'] = city;
      if (postcode != null) updates['postcode'] = postcode;

      final record = await _pb
          .collection('users')
          .update(userId, body: updates);

      LoggerService.info('Updated user profile for user: $userId');
      return record;
    } catch (e) {
      LoggerService.error('Error updating user profile', e);
      rethrow;
    }
  }

  /// Upload profile picture
  Future<RecordModel> uploadProfilePicture(
    List<int> imageBytes,
    String fileName,
  ) async {
    try {
      _verifyClaimantAuth();

      final userId = currentUserId!;

      // Create multipart file
      final multipartFile = http.MultipartFile.fromBytes(
        'avatar',
        imageBytes,
        filename: fileName,
      );

      final record = await _pb
          .collection('users')
          .update(userId, files: [multipartFile]);

      LoggerService.info('Uploaded profile picture for user: $userId');
      return record;
    } catch (e) {
      LoggerService.error('Error uploading profile picture', e);
      rethrow;
    }
  }

  /// Update notification preferences
  Future<ClaimantProfile> updateNotificationPreferences(
    Map<String, dynamic> preferences,
  ) async {
    try {
      _verifyClaimantAuth();

      final profile = await getCurrentClaimantProfile();
      if (profile == null) {
        throw Exception('Claimant profile not found');
      }

      final updates = {'notification_preferences': preferences};

      final record = await _pb
          .collection('claimant_profiles')
          .update(profile.id, body: updates, expand: 'user_id');

      LoggerService.info(
        'Updated notification preferences for profile: ${profile.id}',
      );
      return ClaimantProfile.fromJson(record.toJson());
    } catch (e) {
      LoggerService.error('Error updating notification preferences', e);
      rethrow;
    }
  }

  /// Get profile picture URL
  String? getProfilePictureUrl() {
    try {
      final user = _pb.authStore.record;
      if (user == null) return null;

      final avatarField = user.getStringValue('avatar');
      if (avatarField.isEmpty) return null;

      return _pb.files.getURL(user, avatarField).toString();
    } catch (e) {
      LoggerService.error('Error getting profile picture URL', e);
      return null;
    }
  }

  /// Handle errors with user-friendly messages
  String getErrorMessage(dynamic error) {
    if (error is ClientException) {
      switch (error.statusCode) {
        case 400:
          return 'Invalid request. Please check your input.';
        case 401:
          return 'Authentication required. Please sign in.';
        case 403:
          return 'Access denied. You do not have permission for this action.';
        case 404:
          return 'Resource not found.';
        case 500:
          return 'Server error. Please try again later.';
        default:
          return 'An unexpected error occurred.';
      }
    }
    return error.toString();
  }
}
