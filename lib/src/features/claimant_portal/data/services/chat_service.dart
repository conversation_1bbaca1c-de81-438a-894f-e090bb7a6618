import 'dart:async';
import 'dart:io';
import 'package:pocketbase/pocketbase.dart';
import 'package:http/http.dart' as http;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/claimant_base_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/chat_message_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/chat_conversation_model.dart';

/// Service for managing chat functionality in the claimant portal
/// Handles messaging between claimants and 3Pay agents
class ChatService extends ClaimantBaseService {
  /// Access to PocketBase instance through service
  PocketBase get pb => PocketBaseService().pb;

  UnsubscribeFunc? _messagesSubscription;
  final StreamController<List<ChatMessageModel>> _messagesController =
      StreamController<List<ChatMessageModel>>.broadcast();
  final StreamController<ChatMessageModel> _newMessageController =
      StreamController<ChatMessageModel>.broadcast();

  /// Stream of messages for a conversation
  Stream<List<ChatMessageModel>> get messagesStream =>
      _messagesController.stream;

  /// Stream of new messages
  Stream<ChatMessageModel> get newMessageStream => _newMessageController.stream;

  /// Verify claimant authentication with retry logic
  Future<void> _verifyAuth() async {
    // First check - immediate verification
    if (isAuthenticated && isClaimant) {
      LoggerService.info('Authentication verified immediately');
      return;
    }

    // If not authenticated, try multiple times with increasing delays
    for (int attempt = 1; attempt <= 3; attempt++) {
      if (!isAuthenticated) {
        LoggerService.info(
          'Authentication not ready (attempt $attempt/3), waiting for initialization...',
        );

        // Exponential backoff: 100ms, 200ms, 400ms
        await Future.delayed(Duration(milliseconds: 100 * attempt));

        // Check again after delay
        if (isAuthenticated && isClaimant) {
          LoggerService.info('Authentication verified after $attempt attempts');
          return;
        }
      }
    }

    // Final checks with detailed error messages
    if (!isAuthenticated) {
      LoggerService.error('User not authenticated after 3 attempts');
      throw Exception('User not authenticated - please sign in again');
    }

    if (!isClaimant) {
      LoggerService.error(
        'User is not a claimant: ${currentUser?.data['user_type']}',
      );
      throw Exception('User is not a claimant');
    }

    LoggerService.info('Authentication verified successfully');
  }

  /// Get conversations for current claimant
  Future<List<ChatConversationModel>> getConversationsForClaimant() async {
    try {
      await _verifyAuth();

      final profile = await getCurrentClaimantProfile();
      if (profile == null || profile.associatedClaimIds.isEmpty) {
        LoggerService.info('No claims found for current claimant');
        return [];
      }

      final conversations = <ChatConversationModel>[];

      // Get conversations for each associated claim
      for (final claimId in profile.associatedClaimIds) {
        try {
          final messages = await getMessagesForConversation(claimId);
          if (messages.isNotEmpty) {
            final conversation = ChatConversationHelper.fromMessages(
              claimId: claimId,
              messages: messages,
              currentUserId: currentUserId!,
            );
            conversations.add(conversation);
          }
        } catch (e) {
          LoggerService.error('Error getting messages for claim $claimId', e);
          // Continue with other claims
        }
      }

      LoggerService.info('Retrieved ${conversations.length} conversations');
      return conversations;
    } catch (e) {
      LoggerService.error('Error getting conversations for claimant', e);
      rethrow;
    }
  }

  /// Get messages for a specific conversation (claim)
  Future<List<ChatMessageModel>> getMessagesForConversation(
    String claimId,
  ) async {
    try {
      await _verifyAuth();

      LoggerService.info('Fetching messages for claim: $claimId');

      final records = await pb
          .collection('application_communications')
          .getFullList(
            filter: 'application_id = "$claimId"',
            sort: 'created',
            expand: 'sender_id',
          );

      final messages =
          records
              .map((record) => ChatMessageHelper.fromRecord(record))
              .toList();

      LoggerService.info(
        'Retrieved ${messages.length} messages for claim $claimId',
      );
      return messages;
    } catch (e) {
      LoggerService.error(
        'Error getting messages for conversation $claimId',
        e,
      );
      rethrow;
    }
  }

  /// Send a text message
  Future<ChatMessageModel> sendMessage({
    required String claimId,
    required String content,
    String? recipientId,
    String? recipientGroup = 'agent',
    Map<String, dynamic>? metadata,
  }) async {
    try {
      await _verifyAuth();

      if (content.trim().isEmpty) {
        throw ArgumentError('Message content cannot be empty');
      }

      LoggerService.info('Sending message to claim: $claimId');

      final messageData = {
        'application_id': claimId,
        'sender_id': currentUserId!,
        'message_content': content.trim(),
        if (recipientId != null) 'recipient_id': recipientId,
        if (recipientGroup != null) 'recipient_group': recipientGroup,
        // Note: metadata and message_type fields removed as they don't exist in the collection schema
      };

      final record = await pb
          .collection('application_communications')
          .create(body: messageData, expand: 'sender_id');

      final message = ChatMessageHelper.fromRecord(record);
      LoggerService.info('Message sent successfully: ${message.id}');

      // Emit new message to stream
      _newMessageController.add(message);

      return message;
    } catch (e) {
      LoggerService.error('Error sending message', e);
      rethrow;
    }
  }

  /// Send a message with file attachment
  Future<ChatMessageModel> sendMessageWithFile({
    required String claimId,
    required String content,
    required File file,
    String? recipientId,
    String? recipientGroup = 'agent',
    Map<String, dynamic>? metadata,
  }) async {
    try {
      await _verifyAuth();

      if (content.trim().isEmpty && file.path.isEmpty) {
        throw ArgumentError('Message must have content or file attachment');
      }

      LoggerService.info('Sending message with file to claim: $claimId');

      // Create multipart file
      final multipartFile = await http.MultipartFile.fromPath(
        'attachment', // Use the correct field name from the collection schema
        file.path,
      );

      final messageData = {
        'application_id': claimId,
        'sender_id': currentUserId!,
        'message_content': content.trim(),
        if (recipientId != null) 'recipient_id': recipientId,
        if (recipientGroup != null) 'recipient_group': recipientGroup,
        // Note: metadata and message_type fields removed as they don't exist in the collection schema
      };

      final record = await pb
          .collection('application_communications')
          .create(
            body: messageData,
            files: [multipartFile],
            expand: 'sender_id',
          );

      final message = ChatMessageHelper.fromRecord(record);
      LoggerService.info('Message with file sent successfully: ${message.id}');

      // Emit new message to stream
      _newMessageController.add(message);

      return message;
    } catch (e) {
      LoggerService.error('Error sending message with file', e);
      rethrow;
    }
  }

  /// Create a new conversation for a claim
  Future<ChatConversationModel> createConversation({
    required String claimId,
    String? title,
  }) async {
    try {
      await _verifyAuth();

      LoggerService.info('Creating conversation for claim: $claimId');

      // Get claimant profile for participant info
      final profile = await getCurrentClaimantProfile();
      if (profile == null) {
        throw StateError('Claimant profile not found');
      }

      // Create initial system message
      await sendMessage(
        claimId: claimId,
        content:
            'Conversation started. A 3Pay Global agent will respond shortly.',
        recipientGroup: 'system',
        metadata: {'type': 'system', 'action': 'conversation_created'},
      );

      // Create conversation model
      final conversation = ChatConversationHelper.fromClaimData(
        claimId: claimId,
        claimTitle: title ?? 'Claim Discussion',
        claimantId: currentUserId!,
        claimantName: profile.displayName,
        claimantEmail: profile.displayEmail,
      );

      LoggerService.info(
        'Conversation created successfully: ${conversation.id}',
      );
      return conversation;
    } catch (e) {
      LoggerService.error('Error creating conversation', e);
      rethrow;
    }
  }

  /// Subscribe to real-time updates for a conversation
  Future<void> subscribeToConversationUpdates(String claimId) async {
    try {
      await _verifyAuth();

      LoggerService.info(
        'Setting up real-time subscription for claim: $claimId',
      );

      // Unsubscribe from previous subscription
      await unsubscribeFromUpdates();

      // Subscribe to new messages for this claim
      _messagesSubscription = await pb
          .collection('application_communications')
          .subscribe(
            '*',
            _handleMessageUpdate,
            filter: 'application_id = "$claimId"',
          );

      LoggerService.info('Real-time subscription set up successfully');
    } catch (e) {
      LoggerService.error('Error setting up conversation subscription', e);
      rethrow;
    }
  }

  /// Handle real-time message updates
  void _handleMessageUpdate(RecordSubscriptionEvent event) {
    try {
      LoggerService.info('Received real-time message update: ${event.action}');

      if (event.action == 'create' || event.action == 'update') {
        final message = ChatMessageHelper.fromRecord(event.record!);
        _newMessageController.add(message);
      }
    } catch (e) {
      LoggerService.error('Error handling message update', e);
    }
  }

  /// Unsubscribe from real-time updates
  Future<void> unsubscribeFromUpdates() async {
    if (_messagesSubscription != null) {
      await _messagesSubscription!();
      _messagesSubscription = null;
      LoggerService.info('Unsubscribed from message updates');
    }
  }

  /// Mark messages as read
  Future<void> markMessagesAsRead({
    required String claimId,
    required List<String> messageIds,
  }) async {
    try {
      await _verifyAuth();

      LoggerService.info('Marking ${messageIds.length} messages as read');

      for (final messageId in messageIds) {
        try {
          // Note: The application_communications collection doesn't have is_read/read_at fields
          // This functionality would need to be implemented differently, perhaps with a separate collection
          // For now, we'll just log that the message was marked as read
          LoggerService.info('Message $messageId marked as read (local only)');
        } catch (e) {
          LoggerService.error('Error marking message $messageId as read', e);
          // Continue with other messages
        }
      }

      LoggerService.info('Messages marked as read successfully');
    } catch (e) {
      LoggerService.error('Error marking messages as read', e);
      rethrow;
    }
  }

  /// Dispose resources
  void dispose() {
    unsubscribeFromUpdates();
    _messagesController.close();
    _newMessageController.close();
  }
}
