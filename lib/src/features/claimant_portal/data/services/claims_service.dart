import 'dart:async';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/claimant_base_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_claim_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claim_status_model.dart';

/// Service for managing claims data for claimants
/// Extends ClaimantBaseService for consistency with existing patterns
class ClaimsService extends ClaimantBaseService {
  /// Access to PocketBase instance through service
  PocketBase get pb => PocketBaseService().pb;
  UnsubscribeFunc? _claimsUnsubscribe;
  final StreamController<List<ClaimantClaim>> _claimsController =
      StreamController<List<ClaimantClaim>>.broadcast();
  final StreamController<ClaimantClaim> _claimUpdatesController =
      StreamController<ClaimantClaim>.broadcast();

  /// Stream of claims for the current claimant
  Stream<List<ClaimantClaim>> get claimsStream => _claimsController.stream;

  /// Stream of individual claim updates
  Stream<ClaimantClaim> get claimUpdatesStream =>
      _claimUpdatesController.stream;

  /// Get claims for the current claimant
  Future<List<ClaimantClaim>> getClaimsForClaimant() async {
    try {
      LoggerService.info('Fetching claims for current claimant');

      // Get associated funding applications from base service
      final fundingApplications = await getAssociatedFundingApplications();

      // Transform to ClaimantClaim models
      final claims =
          fundingApplications
              .map((record) => ClaimantClaim.fromRecord(record))
              .toList();

      // Sort by submission date (newest first)
      claims.sort((a, b) => b.submissionDate.compareTo(a.submissionDate));

      LoggerService.info(
        'Successfully fetched ${claims.length} claims for claimant',
      );

      // Emit to stream
      _claimsController.add(claims);

      return claims;
    } catch (e) {
      LoggerService.error('Error fetching claims for claimant', e);
      rethrow;
    }
  }

  /// Get specific claim details by ID
  Future<ClaimantClaim?> getClaimDetails(String claimId) async {
    try {
      LoggerService.info('Fetching claim details for ID: $claimId');

      // Verify the claim belongs to the current claimant
      final profile = await getCurrentClaimantProfile();
      if (profile == null) {
        LoggerService.warning(
          'No claimant profile found when fetching claim details',
        );
        return null;
      }

      if (!profile.associatedClaimIds.contains(claimId)) {
        LoggerService.warning(
          'Claim $claimId not associated with current claimant',
        );
        return null;
      }

      // Fetch the funding application record
      final record = await pb
          .collection('funding_applications')
          .getOne(claimId);
      final claim = ClaimantClaim.fromRecord(record);

      LoggerService.info(
        'Successfully fetched claim details for: ${claim.title}',
      );
      return claim;
    } catch (e) {
      LoggerService.error('Error fetching claim details for ID: $claimId', e);
      rethrow;
    }
  }

  /// Subscribe to real-time updates for claims
  Future<void> subscribeToClaimUpdates() async {
    try {
      LoggerService.info('Setting up real-time subscription for claims');

      // Get current claimant profile to know which claims to watch
      final profile = await getCurrentClaimantProfile();
      if (profile == null || profile.associatedClaimIds.isEmpty) {
        LoggerService.info('No claims to subscribe to for current claimant');
        return;
      }

      // Build filter for associated claims
      final filterConditions = profile.associatedClaimIds
          .map((id) => 'id = "$id"')
          .join(' || ');
      final filter = '($filterConditions)';

      LoggerService.info(
        'Subscribing to funding_applications with filter: $filter',
      );

      // Subscribe to funding_applications collection for associated claims
      _claimsUnsubscribe = await pb
          .collection('funding_applications')
          .subscribe('*', _handleClaimUpdate, filter: filter);

      LoggerService.info(
        'Successfully set up real-time subscription for claims',
      );
    } catch (e) {
      LoggerService.error('Error setting up claims subscription', e);
      rethrow;
    }
  }

  /// Handle real-time claim updates
  void _handleClaimUpdate(RecordSubscriptionEvent event) {
    try {
      LoggerService.info(
        'Received claim update: ${event.action} for record ${event.record?.id}',
      );

      if (event.record == null) return;

      final updatedClaim = ClaimantClaim.fromRecord(event.record!);

      switch (event.action) {
        case 'create':
          LoggerService.info('New claim created: ${updatedClaim.title}');
          break;
        case 'update':
          LoggerService.info(
            'Claim updated: ${updatedClaim.title} - Status: ${updatedClaim.status.displayName}',
          );
          break;
        case 'delete':
          LoggerService.info('Claim deleted: ${updatedClaim.id}');
          break;
      }

      // Emit individual claim update
      _claimUpdatesController.add(updatedClaim);

      // Refresh full claims list
      getClaimsForClaimant().catchError((error) {
        LoggerService.error('Error refreshing claims after update', error);
        return <ClaimantClaim>[]; // Return empty list on error
      });
    } catch (e) {
      LoggerService.error('Error handling claim update', e);
    }
  }

  /// Get claim status history (if available in the future)
  Future<ClaimStatusHistory?> getClaimStatusHistory(String claimId) async {
    try {
      LoggerService.info('Fetching status history for claim: $claimId');

      // For now, create a simple history from the current status
      // In the future, this could fetch from a dedicated status_history collection
      final claim = await getClaimDetails(claimId);
      if (claim == null) return null;

      final currentStatusModel = ClaimStatusModel.fromStatus(
        id: '${claimId}_current',
        claimId: claimId,
        status: claim.status.value,
        timestamp: claim.lastUpdated ?? claim.created,
      );

      return ClaimStatusHistory(
        claimId: claimId,
        statusHistory: [currentStatusModel],
        currentStatus: currentStatusModel,
      );
    } catch (e) {
      LoggerService.error('Error fetching claim status history', e);
      rethrow;
    }
  }

  /// Filter claims by status
  Future<List<ClaimantClaim>> getClaimsByStatus(ClaimStatus status) async {
    try {
      final allClaims = await getClaimsForClaimant();
      return allClaims.where((claim) => claim.status == status).toList();
    } catch (e) {
      LoggerService.error(
        'Error filtering claims by status: ${status.value}',
        e,
      );
      rethrow;
    }
  }

  /// Get active claims (not completed, cancelled, or rejected)
  Future<List<ClaimantClaim>> getActiveClaims() async {
    try {
      final allClaims = await getClaimsForClaimant();
      return allClaims.where((claim) => claim.isActive).toList();
    } catch (e) {
      LoggerService.error('Error fetching active claims', e);
      rethrow;
    }
  }

  /// Get funded claims
  Future<List<ClaimantClaim>> getFundedClaims() async {
    try {
      final allClaims = await getClaimsForClaimant();
      return allClaims.where((claim) => claim.isFunded).toList();
    } catch (e) {
      LoggerService.error('Error fetching funded claims', e);
      rethrow;
    }
  }

  /// Search claims by title or description
  Future<List<ClaimantClaim>> searchClaims(String query) async {
    try {
      if (query.trim().isEmpty) {
        return getClaimsForClaimant();
      }

      final allClaims = await getClaimsForClaimant();
      final lowercaseQuery = query.toLowerCase();

      return allClaims.where((claim) {
        return claim.title.toLowerCase().contains(lowercaseQuery) ||
            (claim.description?.toLowerCase().contains(lowercaseQuery) ??
                false) ||
            (claim.claimType?.toLowerCase().contains(lowercaseQuery) ??
                false) ||
            (claim.claimIndustry?.toLowerCase().contains(lowercaseQuery) ??
                false);
      }).toList();
    } catch (e) {
      LoggerService.error('Error searching claims with query: $query', e);
      rethrow;
    }
  }

  /// Get claims statistics
  Future<Map<String, int>> getClaimsStatistics() async {
    try {
      final allClaims = await getClaimsForClaimant();

      final stats = <String, int>{
        'total': allClaims.length,
        'active': allClaims.where((claim) => claim.isActive).length,
        'funded': allClaims.where((claim) => claim.isFunded).length,
        'completed':
            allClaims
                .where((claim) => claim.status == ClaimStatus.completed)
                .length,
        'draft':
            allClaims
                .where((claim) => claim.status == ClaimStatus.draft)
                .length,
        'submitted':
            allClaims
                .where((claim) => claim.status == ClaimStatus.submitted)
                .length,
        'under_review':
            allClaims
                .where((claim) => claim.status == ClaimStatus.underReview)
                .length,
        'approved':
            allClaims
                .where((claim) => claim.status == ClaimStatus.approved)
                .length,
        'rejected':
            allClaims
                .where((claim) => claim.status == ClaimStatus.rejected)
                .length,
      };

      LoggerService.info('Claims statistics calculated: $stats');
      return stats;
    } catch (e) {
      LoggerService.error('Error calculating claims statistics', e);
      rethrow;
    }
  }

  /// Dispose of resources and subscriptions
  void dispose() {
    LoggerService.info('Disposing ClaimsService resources');

    _claimsUnsubscribe?.call();
    _claimsUnsubscribe = null;

    _claimsController.close();
    _claimUpdatesController.close();

    LoggerService.info('ClaimsService disposed successfully');
  }
}
