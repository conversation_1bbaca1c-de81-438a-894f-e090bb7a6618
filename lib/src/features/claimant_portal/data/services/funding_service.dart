import 'package:logger/logger.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/funding_status_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/claimant_base_service.dart';

/// Service for managing funding data for claimants
class FundingService extends ClaimantBaseService {
  static final Logger _logger = Logger();

  FundingService() : super();

  /// Get funding status for a specific claim
  Future<List<FundingStatus>> getFundingStatusForClaim(String claimId) async {
    try {
      _logger.i('Fetching funding status for claim: $claimId');

      final result = await pb
          .collection('funding_commitments')
          .getList(
            filter: 'case_id="$claimId"',
            sort: '-created',
            expand: 'co_funder_profile_id,co_funder_profile_id.user_id',
          );

      final fundingStatuses =
          result.items
              .map((record) => FundingStatus.fromRecord(record))
              .toList();

      _logger.i(
        'Found ${fundingStatuses.length} funding commitments for claim $claimId',
      );
      return fundingStatuses;
    } catch (e) {
      _logger.e('Error fetching funding status for claim $claimId: $e');
      throw Exception('Failed to fetch funding status: ${e.toString()}');
    }
  }

  /// Get all funding commitments for a claimant's claims
  Future<List<FundingStatus>> getFundingCommitments(String userId) async {
    try {
      _logger.i('Fetching funding commitments for user: $userId');

      // First get the claimant profile to find associated claims
      final claimantProfile = await pb
          .collection('claimant_profiles')
          .getFirstListItem('user_id="$userId"');

      final associatedClaimIds =
          claimantProfile.data['associated_claim_ids'] as List<dynamic>?;

      if (associatedClaimIds == null || associatedClaimIds.isEmpty) {
        _logger.i('No associated claims found for user $userId');
        return [];
      }

      // Convert to string list and create filter
      final claimIds = associatedClaimIds.cast<String>();
      final claimFilter = claimIds.map((id) => 'case_id="$id"').join(' || ');

      final result = await pb
          .collection('funding_commitments')
          .getList(
            filter: claimFilter,
            sort: '-created',
            expand: 'co_funder_profile_id,co_funder_profile_id.user_id,case_id',
          );

      final fundingStatuses =
          result.items
              .map((record) => FundingStatus.fromRecord(record))
              .toList();

      _logger.i(
        'Found ${fundingStatuses.length} total funding commitments for user $userId',
      );
      return fundingStatuses;
    } catch (e) {
      _logger.e('Error fetching funding commitments for user $userId: $e');
      throw Exception('Failed to fetch funding commitments: ${e.toString()}');
    }
  }

  /// Get funding timeline for a claim
  Future<List<FundingTimelineEvent>> getFundingTimeline(String claimId) async {
    try {
      _logger.i('Fetching funding timeline for claim: $claimId');

      // Get funding commitments for the claim
      final commitments = await getFundingStatusForClaim(claimId);

      // Convert commitments to timeline events
      final events = <FundingTimelineEvent>[];

      for (final commitment in commitments) {
        // Add commitment event
        events.add(
          FundingTimelineEvent(
            id: '${commitment.id}_commitment',
            stage: FundingStage.commitment,
            date: commitment.commitmentDate,
            title: 'Funding Commitment Received',
            description:
                'Commitment of ${commitment.formattedAmount} from ${commitment.funderInfo?.name ?? 'Anonymous Funder'}',
            amount: commitment.amount,
            status: commitment.status,
          ),
        );

        // Add approval event if approved
        if (commitment.approvalDate != null) {
          events.add(
            FundingTimelineEvent(
              id: '${commitment.id}_approval',
              stage: FundingStage.approval,
              date: commitment.approvalDate!,
              title: 'Funding Approved',
              description:
                  'Funding commitment of ${commitment.formattedAmount} approved by 3Pay Global',
              amount: commitment.amount,
              status: commitment.status,
            ),
          );
        }

        // Add deployment event if active
        if (commitment.status == FundingCommitmentStatus.active) {
          events.add(
            FundingTimelineEvent(
              id: '${commitment.id}_deployment',
              stage: FundingStage.deployment,
              date: commitment.approvalDate ?? commitment.commitmentDate,
              title: 'Funds Deployed',
              description:
                  'Funding of ${commitment.formattedAmount} deployed to claim',
              amount: commitment.amount,
              status: commitment.status,
            ),
          );
        }

        // Add completion event if completed
        if (commitment.status == FundingCommitmentStatus.completed) {
          events.add(
            FundingTimelineEvent(
              id: '${commitment.id}_completion',
              stage: FundingStage.completion,
              date: commitment.approvalDate ?? commitment.commitmentDate,
              title: 'Funding Completed',
              description:
                  'Funding cycle completed for ${commitment.formattedAmount}',
              amount: commitment.amount,
              status: commitment.status,
            ),
          );
        }
      }

      // Sort events by date (newest first)
      events.sort((a, b) => b.date.compareTo(a.date));

      _logger.i(
        'Generated ${events.length} timeline events for claim $claimId',
      );
      return events;
    } catch (e) {
      _logger.e('Error fetching funding timeline for claim $claimId: $e');
      throw Exception('Failed to fetch funding timeline: ${e.toString()}');
    }
  }

  /// Calculate funding aggregations for a claimant
  Future<FundingAggregation> calculateFundingAggregation(String userId) async {
    try {
      _logger.i('Calculating funding aggregation for user: $userId');

      final commitments = await getFundingCommitments(userId);

      final totalSecured = FundingCalculations.calculateTotalSecured(
        commitments,
      );
      final totalPending = FundingCalculations.calculateTotalPending(
        commitments,
      );
      final totalCommitted = commitments.fold(0.0, (sum, c) => sum + c.amount);

      // Get target amount from claims
      double targetAmount = 0.0;
      try {
        final claimantProfile = await pb
            .collection('claimant_profiles')
            .getFirstListItem(
              'user_id="$userId"',
              expand: 'associated_claim_ids',
            );

        final associatedClaimIds =
            claimantProfile.data['associated_claim_ids'] as List<dynamic>?;
        if (associatedClaimIds != null && associatedClaimIds.isNotEmpty) {
          final claimIds = associatedClaimIds.cast<String>();
          final claimFilter = claimIds.map((id) => 'id="$id"').join(' || ');

          final claimsResult = await pb
              .collection('funding_applications')
              .getList(filter: claimFilter);

          targetAmount = claimsResult.items.fold(0.0, (sum, claim) {
            final required = claim.data['required_funding_amount'] as num?;
            return sum + (required?.toDouble() ?? 0.0);
          });
        }
      } catch (e) {
        _logger.w('Could not calculate target amount: $e');
      }

      final fundingPercentage = FundingCalculations.calculateFundingPercentage(
        commitments,
        targetAmount,
      );

      final fundingVelocity = FundingCalculations.calculateFundingVelocity(
        commitments,
      );

      final aggregation = FundingAggregation(
        totalSecured: totalSecured,
        totalPending: totalPending,
        totalCommitted: totalCommitted,
        targetAmount: targetAmount,
        fundingPercentage: fundingPercentage,
        fundingVelocity: fundingVelocity,
        commitmentCount: commitments.length,
        securedCount: commitments.where((c) => c.isSecured).length,
        pendingCount:
            commitments
                .where(
                  (c) => c.status == FundingCommitmentStatus.pendingApproval,
                )
                .length,
        lastUpdated: DateTime.now(),
      );

      _logger.i(
        'Calculated funding aggregation for user $userId: ${aggregation.toJson()}',
      );
      return aggregation;
    } catch (e) {
      _logger.e('Error calculating funding aggregation for user $userId: $e');
      throw Exception(
        'Failed to calculate funding aggregation: ${e.toString()}',
      );
    }
  }

  /// Get real-time funding updates (placeholder for future implementation)
  Stream<List<FundingStatus>> getFundingUpdatesStream(String userId) async* {
    // This would implement real-time updates using PocketBase realtime subscriptions
    // For now, we'll yield the current data
    try {
      final commitments = await getFundingCommitments(userId);
      yield commitments;
    } catch (e) {
      _logger.e('Error in funding updates stream: $e');
      yield [];
    }
  }
}
