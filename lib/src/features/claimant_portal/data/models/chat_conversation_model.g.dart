// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_conversation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ConversationParticipantImpl _$$ConversationParticipantImplFromJson(
  Map<String, dynamic> json,
) => _$ConversationParticipantImpl(
  id: json['id'] as String,
  name: json['name'] as String,
  role: json['role'] as String,
  avatar: json['avatar'] as String?,
  email: json['email'] as String?,
  isActive: json['isActive'] as bool? ?? true,
);

Map<String, dynamic> _$$ConversationParticipantImplToJson(
  _$ConversationParticipantImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'role': instance.role,
  'avatar': instance.avatar,
  'email': instance.email,
  'isActive': instance.isActive,
};

_$ChatConversationModelImpl _$$ChatConversationModelImplFromJson(
  Map<String, dynamic> json,
) => _$ChatConversationModelImpl(
  id: json['id'] as String,
  claimId: json['claimId'] as String,
  participants:
      (json['participants'] as List<dynamic>)
          .map(
            (e) => ConversationParticipant.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  lastMessage:
      json['lastMessage'] == null
          ? null
          : ChatMessageModel.fromJson(
            json['lastMessage'] as Map<String, dynamic>,
          ),
  unreadCount: (json['unreadCount'] as num?)?.toInt() ?? 0,
  status:
      $enumDecodeNullable(_$ConversationStatusEnumMap, json['status']) ??
      ConversationStatus.active,
  createdDate: DateTime.parse(json['createdDate'] as String),
  lastActivityDate:
      json['lastActivityDate'] == null
          ? null
          : DateTime.parse(json['lastActivityDate'] as String),
  title: json['title'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$ChatConversationModelImplToJson(
  _$ChatConversationModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'claimId': instance.claimId,
  'participants': instance.participants,
  'lastMessage': instance.lastMessage,
  'unreadCount': instance.unreadCount,
  'status': _$ConversationStatusEnumMap[instance.status]!,
  'createdDate': instance.createdDate.toIso8601String(),
  'lastActivityDate': instance.lastActivityDate?.toIso8601String(),
  'title': instance.title,
  'metadata': instance.metadata,
};

const _$ConversationStatusEnumMap = {
  ConversationStatus.active: 'active',
  ConversationStatus.closed: 'closed',
  ConversationStatus.archived: 'archived',
};
