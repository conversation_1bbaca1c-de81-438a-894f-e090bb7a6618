import 'package:three_pay_group_litigation_platform/src/features/notifications/data/models/enhanced_notification_model.dart';

/// Notification types specific to claimants
enum ClaimantNotificationType {
  claimUpdate('claim_update'),
  fundingUpdate('funding_update'),
  systemNotification('system'),
  chatMessage('chat'),
  documentUpdate('document_update'),
  statusChange('status_change');

  const ClaimantNotificationType(this.value);
  final String value;

  static ClaimantNotificationType fromString(String value) {
    return ClaimantNotificationType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ClaimantNotificationType.systemNotification,
    );
  }
}

/// Priority levels for notifications
enum NotificationPriority {
  low('low'),
  normal('normal'),
  high('high'),
  urgent('urgent');

  const NotificationPriority(this.value);
  final String value;

  static NotificationPriority fromString(String value) {
    return NotificationPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => NotificationPriority.normal,
    );
  }
}

class ClaimantNotificationModel {
  final String id;
  final String title;
  final String message;
  final String type;
  final bool isRead;
  final List<String>? recipientId;
  final DateTime? created;
  final DateTime? updated;
  final DateTime? readAt;
  final bool isGlobal;
  final String? link;
  final String? icon;
  final String? claimId;
  final String priority;
  final Map<String, dynamic>? metadata;

  const ClaimantNotificationModel({
    required this.id,
    required this.title,
    required this.message,
    this.type = 'system',
    this.isRead = false,
    this.recipientId,
    this.created,
    this.updated,
    this.readAt,
    this.isGlobal = false,
    this.link,
    this.icon,
    this.claimId,
    this.priority = 'normal',
    this.metadata,
  });

  factory ClaimantNotificationModel.fromJson(Map<String, dynamic> json) {
    return ClaimantNotificationModel(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      type: json['type'] as String? ?? 'system',
      isRead: json['isRead'] as bool? ?? false,
      recipientId: (json['recipientId'] as List<dynamic>?)?.cast<String>(),
      created:
          json['created'] != null
              ? DateTime.tryParse(json['created'] as String)
              : null,
      updated:
          json['updated'] != null
              ? DateTime.tryParse(json['updated'] as String)
              : null,
      readAt:
          json['readAt'] != null
              ? DateTime.tryParse(json['readAt'] as String)
              : null,
      isGlobal: json['isGlobal'] as bool? ?? false,
      link: json['link'] as String?,
      icon: json['icon'] as String?,
      claimId: json['claimId'] as String?,
      priority: json['priority'] as String? ?? 'normal',
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'isRead': isRead,
      'recipientId': recipientId,
      'created': created?.toIso8601String(),
      'updated': updated?.toIso8601String(),
      'readAt': readAt?.toIso8601String(),
      'isGlobal': isGlobal,
      'link': link,
      'icon': icon,
      'claimId': claimId,
      'priority': priority,
      'metadata': metadata,
    };
  }

  ClaimantNotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    bool? isRead,
    List<String>? recipientId,
    DateTime? created,
    DateTime? updated,
    DateTime? readAt,
    bool? isGlobal,
    String? link,
    String? icon,
    String? claimId,
    String? priority,
    Map<String, dynamic>? metadata,
  }) {
    return ClaimantNotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      recipientId: recipientId ?? this.recipientId,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      readAt: readAt ?? this.readAt,
      isGlobal: isGlobal ?? this.isGlobal,
      link: link ?? this.link,
      icon: icon ?? this.icon,
      claimId: claimId ?? this.claimId,
      priority: priority ?? this.priority,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Create from enhanced notification model
  factory ClaimantNotificationModel.fromEnhanced(
    EnhancedNotificationModel enhanced, {
    String? claimId,
    String? priority,
    Map<String, dynamic>? metadata,
  }) {
    return ClaimantNotificationModel(
      id: enhanced.id,
      title: enhanced.title,
      message: enhanced.message,
      type: enhanced.type,
      isRead: enhanced.isRead,
      recipientId: enhanced.recipientId,
      created: enhanced.created,
      updated: enhanced.updated,
      readAt: enhanced.readAt,
      isGlobal: enhanced.isGlobal,
      link: enhanced.link,
      icon: enhanced.icon,
      claimId: claimId,
      priority: priority ?? 'normal',
      metadata: metadata,
    );
  }
}

/// Extension methods for claimant notifications
extension ClaimantNotificationModelExtension on ClaimantNotificationModel {
  /// Get the notification type as enum
  ClaimantNotificationType get notificationType =>
      ClaimantNotificationType.fromString(type);

  /// Get the priority as enum
  NotificationPriority get priorityLevel =>
      NotificationPriority.fromString(priority);

  /// Check if notification is related to a specific claim
  bool isRelatedToClaim(String claimId) => this.claimId == claimId;

  /// Get a user-friendly type display name
  String get typeDisplayName {
    switch (notificationType) {
      case ClaimantNotificationType.claimUpdate:
        return 'Claim Update';
      case ClaimantNotificationType.fundingUpdate:
        return 'Funding Update';
      case ClaimantNotificationType.systemNotification:
        return 'System Notification';
      case ClaimantNotificationType.chatMessage:
        return 'New Message';
      case ClaimantNotificationType.documentUpdate:
        return 'Document Update';
      case ClaimantNotificationType.statusChange:
        return 'Status Change';
    }
  }

  /// Get priority display name
  String get priorityDisplayName {
    switch (priorityLevel) {
      case NotificationPriority.low:
        return 'Low';
      case NotificationPriority.normal:
        return 'Normal';
      case NotificationPriority.high:
        return 'High';
      case NotificationPriority.urgent:
        return 'Urgent';
    }
  }

  /// Check if notification requires immediate attention
  bool get isUrgent => priorityLevel == NotificationPriority.urgent;

  /// Check if notification is high priority
  bool get isHighPriority =>
      priorityLevel == NotificationPriority.high ||
      priorityLevel == NotificationPriority.urgent;

  /// Get formatted time ago string
  String get timeAgo {
    if (created == null) return 'Unknown time';

    final now = DateTime.now();
    final difference = now.difference(created!);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${(difference.inDays / 7).floor()}w ago';
    }
  }

  /// Get date grouping for list display
  String get dateGroup {
    if (created == null) return 'Unknown';

    final now = DateTime.now();
    final notificationDate = created!;
    final difference = now.difference(notificationDate);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return 'This Week';
    } else if (difference.inDays < 30) {
      return 'This Month';
    } else {
      return 'Older';
    }
  }
}
