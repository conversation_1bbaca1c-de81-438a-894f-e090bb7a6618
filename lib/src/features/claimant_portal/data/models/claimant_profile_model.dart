class ClaimantProfile {
  final String id;
  final String? userId; // Links to users collection
  final Map<String, dynamic>? userExpanded; // Expanded user data
  final List<String> associatedClaimIds; // Updated to match PocketBase field
  final Map<String, dynamic>? notificationPreferences;
  final DateTime created;
  final DateTime updated;

  ClaimantProfile({
    required this.id,
    this.userId,
    this.userExpanded,
    required this.associatedClaimIds,
    this.notificationPreferences,
    required this.created,
    required this.updated,
  });

  factory ClaimantProfile.fromJson(Map<String, dynamic> json) {
    return ClaimantProfile(
      id: json['id'] as String,
      userId:
          json['user_id'] is String
              ? json['user_id'] as String
              : (json['user_id'] is Map
                  ? json['user_id']['id'] as String?
                  : null),
      userExpanded:
          json['expand']?['user_id'] as Map<String, dynamic>? ??
          (json['user_id'] is Map
              ? json['user_id'] as Map<String, dynamic>
              : null),
      associatedClaimIds: _parseRelationField(json['associated_claim_ids']),
      notificationPreferences:
          json['notification_preferences'] as Map<String, dynamic>?,
      created:
          DateTime.tryParse(json['created'] as String? ?? '') ?? DateTime.now(),
      updated:
          DateTime.tryParse(json['updated'] as String? ?? '') ?? DateTime.now(),
    );
  }

  // Helper method to parse relation fields (can be string, list, or null)
  static List<String> _parseRelationField(dynamic field) {
    if (field == null) return [];
    if (field is String) return [field];
    if (field is List) return field.map((e) => e.toString()).toList();
    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'associated_claim_ids': associatedClaimIds,
      'notification_preferences': notificationPreferences,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  // Getter methods to extract user data from expanded user field
  String? get name => userExpanded?['name'] as String?;
  String? get email => userExpanded?['email'] as String?;
  String? get firstName => userExpanded?['first_name'] as String?;
  String? get lastName => userExpanded?['last_name'] as String?;
  String? get phoneNumber => userExpanded?['mobile'] as String?;
  String? get address {
    final addressParts =
        [
          userExpanded?['address_line1'],
          userExpanded?['address_line2'],
          userExpanded?['city'],
          userExpanded?['postcode'],
        ].where((part) => part != null && part.toString().isNotEmpty).toList();

    return addressParts.isEmpty ? null : addressParts.join(' ');
  }

  // Helper method to get display name
  String get displayName => name ?? firstName ?? email ?? 'Unknown';

  // Helper method to get display email
  String get displayEmail => email ?? '';

  // Validation methods
  bool get isValid => id.isNotEmpty && (name != null || email != null);

  bool get hasValidEmail {
    final email = displayEmail;
    return email.isNotEmpty && email.contains('@');
  }

  bool get hasAssociatedClaims => associatedClaimIds.isNotEmpty;

  // Helper method to check if profile is complete
  bool get isProfileComplete {
    return isValid &&
        hasValidEmail &&
        name != null &&
        name!.isNotEmpty &&
        phoneNumber != null &&
        phoneNumber!.isNotEmpty;
  }

  // Helper method to get missing profile fields
  List<String> get missingFields {
    final missing = <String>[];
    if (name == null || name!.isEmpty) missing.add('name');
    if (!hasValidEmail) missing.add('email');
    if (phoneNumber == null || phoneNumber!.isEmpty) {
      missing.add('phone_number');
    }
    if (address == null || address!.isEmpty) missing.add('address');
    return missing;
  }
}
