import 'package:pocketbase/pocketbase.dart';

/// Funding status model for claimant portal
/// Maps from funding_commitments collection
class FundingStatus {
  final String id;
  final String commitmentId;
  final double amount;
  final FundingCommitmentStatus status;
  final DateTime commitmentDate;
  final FunderInfo? funderInfo;
  final FundingType fundingType;
  final DateTime? approvalDate;
  final double? frfrEarned;
  final String? investorNotes;
  final String? caseId;

  const FundingStatus({
    required this.id,
    required this.commitmentId,
    required this.amount,
    required this.status,
    required this.commitmentDate,
    this.funderInfo,
    required this.fundingType,
    this.approvalDate,
    this.frfrEarned,
    this.investorNotes,
    this.caseId,
  });

  /// Create FundingStatus from PocketBase RecordModel
  factory FundingStatus.fromRecord(RecordModel record) {
    final data = record.data;

    return FundingStatus(
      id: record.id,
      commitmentId: record.id,
      amount: (data['amount_committed'] as num?)?.toDouble() ?? 0.0,
      status: FundingCommitmentStatus.fromValue(
        data['status'] as String? ?? 'pending_approval',
      ),
      commitmentDate: DateTime.parse(data['created'] as String),
      funderInfo: _extractFunderInfo(record),
      fundingType: FundingType.fromValue(
        data['funding_type'] as String? ?? 'discretionary',
      ),
      approvalDate:
          data['approval_date'] != null
              ? DateTime.parse(data['approval_date'] as String)
              : null,
      frfrEarned: (data['frfr_earned'] as num?)?.toDouble(),
      investorNotes: data['investor_notes'] as String?,
      caseId: data['case_id'] as String?,
    );
  }

  /// Extract funder information from expanded co_funder_profile_id
  static FunderInfo? _extractFunderInfo(RecordModel record) {
    try {
      // Use the new get method for expanded records
      final coFunderProfile = record.get<RecordModel>(
        'expand.co_funder_profile_id',
      );
      final profileData = coFunderProfile.data;

      // Try to get user information
      String? funderName;
      try {
        final user = coFunderProfile.get<RecordModel>('expand.user_id');
        funderName = user.data['name'] as String?;
      } catch (e) {
        // User expansion might not be available
      }

      return FunderInfo(
        id: coFunderProfile.id,
        name: funderName ?? 'Anonymous Funder',
        level: profileData['current_level'] as int? ?? 1,
      );
    } catch (e) {
      // Return null if extraction fails
    }
    return null;
  }

  /// Get formatted amount string
  String get formattedAmount {
    return '£${amount.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}';
  }

  /// Get status color for UI
  String get statusColor {
    switch (status) {
      case FundingCommitmentStatus.approved:
      case FundingCommitmentStatus.active:
        return 'success';
      case FundingCommitmentStatus.pendingApproval:
        return 'warning';
      case FundingCommitmentStatus.rejected:
        return 'destructive';
      case FundingCommitmentStatus.completed:
        return 'secondary';
    }
  }

  /// Check if funding is active/secured
  bool get isSecured {
    return status == FundingCommitmentStatus.approved ||
        status == FundingCommitmentStatus.active ||
        status == FundingCommitmentStatus.completed;
  }

  /// Copy with method
  FundingStatus copyWith({
    String? id,
    String? commitmentId,
    double? amount,
    FundingCommitmentStatus? status,
    DateTime? commitmentDate,
    FunderInfo? funderInfo,
    FundingType? fundingType,
    DateTime? approvalDate,
    double? frfrEarned,
    String? investorNotes,
    String? caseId,
  }) {
    return FundingStatus(
      id: id ?? this.id,
      commitmentId: commitmentId ?? this.commitmentId,
      amount: amount ?? this.amount,
      status: status ?? this.status,
      commitmentDate: commitmentDate ?? this.commitmentDate,
      funderInfo: funderInfo ?? this.funderInfo,
      fundingType: fundingType ?? this.fundingType,
      approvalDate: approvalDate ?? this.approvalDate,
      frfrEarned: frfrEarned ?? this.frfrEarned,
      investorNotes: investorNotes ?? this.investorNotes,
      caseId: caseId ?? this.caseId,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'commitmentId': commitmentId,
      'amount': amount,
      'status': status.value,
      'commitmentDate': commitmentDate.toIso8601String(),
      'funderInfo': funderInfo?.toJson(),
      'fundingType': fundingType.value,
      'approvalDate': approvalDate?.toIso8601String(),
      'frfrEarned': frfrEarned,
      'investorNotes': investorNotes,
      'caseId': caseId,
    };
  }

  /// Create from JSON
  factory FundingStatus.fromJson(Map<String, dynamic> json) {
    return FundingStatus(
      id: json['id'] as String,
      commitmentId: json['commitmentId'] as String,
      amount: (json['amount'] as num).toDouble(),
      status: FundingCommitmentStatus.fromValue(json['status'] as String),
      commitmentDate: DateTime.parse(json['commitmentDate'] as String),
      funderInfo:
          json['funderInfo'] != null
              ? FunderInfo.fromJson(json['funderInfo'] as Map<String, dynamic>)
              : null,
      fundingType: FundingType.fromValue(json['fundingType'] as String),
      approvalDate:
          json['approvalDate'] != null
              ? DateTime.parse(json['approvalDate'] as String)
              : null,
      frfrEarned: (json['frfrEarned'] as num?)?.toDouble(),
      investorNotes: json['investorNotes'] as String?,
      caseId: json['caseId'] as String?,
    );
  }
}

/// Funding commitment status enum
enum FundingCommitmentStatus {
  pendingApproval(
    'pending_approval',
    'Pending Approval',
    'Awaiting approval from 3Pay Global',
  ),
  approved('approved', 'Approved', 'Funding commitment approved'),
  rejected('rejected', 'Rejected', 'Funding commitment rejected'),
  active('active', 'Active', 'Funding is active and deployed'),
  completed('completed', 'Completed', 'Funding commitment completed');

  const FundingCommitmentStatus(this.value, this.displayName, this.description);

  final String value;
  final String displayName;
  final String description;

  /// Create FundingCommitmentStatus from string value
  static FundingCommitmentStatus fromValue(String value) {
    for (final status in FundingCommitmentStatus.values) {
      if (status.value == value.toLowerCase()) {
        return status;
      }
    }
    return FundingCommitmentStatus.pendingApproval;
  }

  /// Get icon for status
  String get iconName {
    switch (this) {
      case FundingCommitmentStatus.pendingApproval:
        return 'clock';
      case FundingCommitmentStatus.approved:
        return 'check-circle';
      case FundingCommitmentStatus.rejected:
        return 'x-circle';
      case FundingCommitmentStatus.active:
        return 'trending-up';
      case FundingCommitmentStatus.completed:
        return 'check-circle-2';
    }
  }
}

/// Funding type enum
enum FundingType {
  discretionary('discretionary', 'Discretionary', 'Discretionary funding'),
  nonDiscretionary(
    'non_discretionary',
    'Non-Discretionary',
    'Non-discretionary funding',
  );

  const FundingType(this.value, this.displayName, this.description);

  final String value;
  final String displayName;
  final String description;

  /// Create FundingType from string value
  static FundingType fromValue(String value) {
    for (final type in FundingType.values) {
      if (type.value == value.toLowerCase()) {
        return type;
      }
    }
    return FundingType.discretionary;
  }
}

/// Funder information class
class FunderInfo {
  final String id;
  final String name;
  final int level;

  const FunderInfo({required this.id, required this.name, required this.level});

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name, 'level': level};
  }

  /// Create from JSON
  factory FunderInfo.fromJson(Map<String, dynamic> json) {
    return FunderInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      level: json['level'] as int,
    );
  }

  /// Get level display name
  String get levelDisplayName {
    switch (level) {
      case 1:
        return 'Level 1 Investor';
      case 2:
        return 'Level 2 Investor';
      case 3:
        return 'Level 3 Investor';
      case 4:
        return 'Level 4 Investor';
      default:
        return 'Investor';
    }
  }
}

/// Funding stage enum for timeline
enum FundingStage {
  application(
    'application',
    'Application Submitted',
    'Funding application submitted',
  ),
  review('review', 'Under Review', 'Application under review'),
  commitment('commitment', 'Commitment Made', 'Funding commitment received'),
  approval('approval', 'Approved', 'Funding approved by 3Pay Global'),
  deployment('deployment', 'Funds Deployed', 'Funding deployed to claim'),
  completion('completion', 'Completed', 'Funding cycle completed');

  const FundingStage(this.value, this.displayName, this.description);

  final String value;
  final String displayName;
  final String description;

  /// Get icon for stage
  String get iconName {
    switch (this) {
      case FundingStage.application:
        return 'file-text';
      case FundingStage.review:
        return 'search';
      case FundingStage.commitment:
        return 'handshake';
      case FundingStage.approval:
        return 'check-circle';
      case FundingStage.deployment:
        return 'trending-up';
      case FundingStage.completion:
        return 'check-circle-2';
    }
  }
}

/// Funding calculation helpers
class FundingCalculations {
  /// Calculate total funding secured
  static double calculateTotalSecured(List<FundingStatus> commitments) {
    return commitments
        .where((c) => c.isSecured)
        .fold(0.0, (sum, c) => sum + c.amount);
  }

  /// Calculate total funding pending
  static double calculateTotalPending(List<FundingStatus> commitments) {
    return commitments
        .where((c) => c.status == FundingCommitmentStatus.pendingApproval)
        .fold(0.0, (sum, c) => sum + c.amount);
  }

  /// Calculate funding percentage
  static double calculateFundingPercentage(
    List<FundingStatus> commitments,
    double targetAmount,
  ) {
    if (targetAmount <= 0) return 0.0;
    final secured = calculateTotalSecured(commitments);
    return (secured / targetAmount * 100).clamp(0.0, 100.0);
  }

  /// Get funding velocity (average per month)
  static double calculateFundingVelocity(List<FundingStatus> commitments) {
    if (commitments.isEmpty) return 0.0;

    final securedCommitments = commitments.where((c) => c.isSecured).toList();
    if (securedCommitments.isEmpty) return 0.0;

    final earliest = securedCommitments
        .map((c) => c.commitmentDate)
        .reduce((a, b) => a.isBefore(b) ? a : b);

    final latest = securedCommitments
        .map((c) => c.commitmentDate)
        .reduce((a, b) => a.isAfter(b) ? a : b);

    final monthsDiff = latest.difference(earliest).inDays / 30.0;
    if (monthsDiff <= 0) return 0.0;

    final totalSecured = calculateTotalSecured(commitments);
    return totalSecured / monthsDiff;
  }
}

/// Funding timeline event model
class FundingTimelineEvent {
  final String id;
  final FundingStage stage;
  final DateTime date;
  final String title;
  final String description;
  final double? amount;
  final FundingCommitmentStatus? status;

  const FundingTimelineEvent({
    required this.id,
    required this.stage,
    required this.date,
    required this.title,
    required this.description,
    this.amount,
    this.status,
  });

  /// Get formatted amount string
  String? get formattedAmount {
    if (amount == null) return null;
    return '£${amount!.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}';
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'stage': stage.value,
      'date': date.toIso8601String(),
      'title': title,
      'description': description,
      'amount': amount,
      'status': status?.value,
    };
  }

  /// Create from JSON
  factory FundingTimelineEvent.fromJson(Map<String, dynamic> json) {
    return FundingTimelineEvent(
      id: json['id'] as String,
      stage: FundingStage.values.firstWhere(
        (s) => s.value == json['stage'],
        orElse: () => FundingStage.application,
      ),
      date: DateTime.parse(json['date'] as String),
      title: json['title'] as String,
      description: json['description'] as String,
      amount: (json['amount'] as num?)?.toDouble(),
      status:
          json['status'] != null
              ? FundingCommitmentStatus.fromValue(json['status'] as String)
              : null,
    );
  }
}

/// Funding aggregation model for dashboard statistics
class FundingAggregation {
  final double totalSecured;
  final double totalPending;
  final double totalCommitted;
  final double targetAmount;
  final double fundingPercentage;
  final double fundingVelocity;
  final int commitmentCount;
  final int securedCount;
  final int pendingCount;
  final DateTime lastUpdated;

  const FundingAggregation({
    required this.totalSecured,
    required this.totalPending,
    required this.totalCommitted,
    required this.targetAmount,
    required this.fundingPercentage,
    required this.fundingVelocity,
    required this.commitmentCount,
    required this.securedCount,
    required this.pendingCount,
    required this.lastUpdated,
  });

  /// Get formatted total secured string
  String get formattedTotalSecured {
    return '£${totalSecured.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}';
  }

  /// Get formatted total pending string
  String get formattedTotalPending {
    return '£${totalPending.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}';
  }

  /// Get formatted target amount string
  String get formattedTargetAmount {
    return '£${targetAmount.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}';
  }

  /// Get formatted funding velocity string
  String get formattedFundingVelocity {
    return '£${fundingVelocity.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}/month';
  }

  /// Get funding status description
  String get fundingStatusDescription {
    if (fundingPercentage >= 100) {
      return 'Fully funded';
    } else if (fundingPercentage >= 75) {
      return 'Nearly funded';
    } else if (fundingPercentage >= 50) {
      return 'Partially funded';
    } else if (fundingPercentage > 0) {
      return 'Initial funding secured';
    } else {
      return 'Seeking funding';
    }
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'totalSecured': totalSecured,
      'totalPending': totalPending,
      'totalCommitted': totalCommitted,
      'targetAmount': targetAmount,
      'fundingPercentage': fundingPercentage,
      'fundingVelocity': fundingVelocity,
      'commitmentCount': commitmentCount,
      'securedCount': securedCount,
      'pendingCount': pendingCount,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Create from JSON
  factory FundingAggregation.fromJson(Map<String, dynamic> json) {
    return FundingAggregation(
      totalSecured: (json['totalSecured'] as num).toDouble(),
      totalPending: (json['totalPending'] as num).toDouble(),
      totalCommitted: (json['totalCommitted'] as num).toDouble(),
      targetAmount: (json['targetAmount'] as num).toDouble(),
      fundingPercentage: (json['fundingPercentage'] as num).toDouble(),
      fundingVelocity: (json['fundingVelocity'] as num).toDouble(),
      commitmentCount: json['commitmentCount'] as int,
      securedCount: json['securedCount'] as int,
      pendingCount: json['pendingCount'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}
