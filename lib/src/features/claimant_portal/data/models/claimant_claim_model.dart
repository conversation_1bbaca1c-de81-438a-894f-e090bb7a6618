import 'package:pocketbase/pocketbase.dart';

/// Claimant-specific claim model mapped from funding_applications collection
/// Provides a simplified view of claims for claimant portal
class ClaimantClaim {
  final String id;
  final String title;
  final ClaimStatus status;
  final DateTime submissionDate;
  final String currentStage;
  final String? description;
  final double? minimumValueClaim;
  final double? requiredFundingAmount;
  final String? claimType;
  final String? claimIndustry;
  final String? claimantType;
  final DateTime? lastUpdated;
  final DateTime created;

  const ClaimantClaim({
    required this.id,
    required this.title,
    required this.status,
    required this.submissionDate,
    required this.currentStage,
    this.description,
    this.minimumValueClaim,
    this.requiredFundingAmount,
    this.claimType,
    this.claimIndustry,
    this.claimantType,
    this.lastUpdated,
    required this.created,
  });

  /// Create ClaimantClaim from PocketBase RecordModel
  factory ClaimantClaim.fromRecord(RecordModel record) {
    final data = record.data;
    
    return ClaimantClaim(
      id: record.id,
      title: data['claim_title'] as String? ?? 'Untitled Claim',
      status: ClaimStatus.fromApplicationStatus(
        data['application_status'] as String?,
      ),
      submissionDate: DateTime.tryParse(data['submission_date'] as String? ?? '') ??
          DateTime.tryParse(data['created'] as String? ?? '') ??
          DateTime.now(),
      currentStage: data['stage'] as String? ?? 'Unknown',
      description: _buildDescription(data),
      minimumValueClaim: (data['minimum_value_claim'] as num?)?.toDouble(),
      requiredFundingAmount: (data['required_funding_amount'] as num?)?.toDouble(),
      claimType: data['claim_type'] as String?,
      claimIndustry: data['claim_industry'] as String?,
      claimantType: data['claimant_type'] as String?,
      lastUpdated: DateTime.tryParse(data['updated'] as String? ?? ''),
      created: DateTime.tryParse(data['created'] as String? ?? '') ?? DateTime.now(),
    );
  }

  /// Build description from available data
  static String? _buildDescription(Map<String, dynamic> data) {
    final parts = <String>[];
    
    if (data['claim_type'] != null) {
      parts.add('Type: ${data['claim_type']}');
    }
    if (data['claim_industry'] != null) {
      parts.add('Industry: ${data['claim_industry']}');
    }
    if (data['claimant_type'] != null) {
      parts.add('Claimant: ${data['claimant_type']}');
    }
    
    return parts.isEmpty ? null : parts.join(' • ');
  }

  /// Get formatted stage display name
  String get formattedStage {
    switch (currentStage) {
      case 'STAGE 1: PRE ACTION':
        return 'Pre-Action';
      case 'STAGE 2: PROCEEDINGS ISSUED':
        return 'Proceedings Issued';
      case 'STAGE 3: DISCLOSURE':
        return 'Disclosure';
      case 'STAGE 4: TRIAL':
        return 'Trial';
      case 'STAGE 5: SETTLEMENT':
        return 'Settlement';
      default:
        return currentStage.replaceAll('STAGE ', '').replaceAll(':', '');
    }
  }

  /// Get formatted funding amount
  String get formattedFundingAmount {
    if (requiredFundingAmount == null) return 'Not specified';
    return '£${requiredFundingAmount!.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}';
  }

  /// Get formatted minimum value
  String get formattedMinimumValue {
    if (minimumValueClaim == null) return 'Not specified';
    return '£${minimumValueClaim!.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}';
  }

  /// Check if claim is active (not completed, cancelled, or rejected)
  bool get isActive {
    return status != ClaimStatus.completed &&
           status != ClaimStatus.cancelled &&
           status != ClaimStatus.rejected;
  }

  /// Check if claim is in funding stage
  bool get isFunded {
    return status == ClaimStatus.approved || status == ClaimStatus.funded;
  }

  /// Get days since submission
  int get daysSinceSubmission {
    return DateTime.now().difference(submissionDate).inDays;
  }

  /// Get days since last update
  int? get daysSinceLastUpdate {
    if (lastUpdated == null) return null;
    return DateTime.now().difference(lastUpdated!).inDays;
  }

  /// Copy with method for immutable updates
  ClaimantClaim copyWith({
    String? id,
    String? title,
    ClaimStatus? status,
    DateTime? submissionDate,
    String? currentStage,
    String? description,
    double? minimumValueClaim,
    double? requiredFundingAmount,
    String? claimType,
    String? claimIndustry,
    String? claimantType,
    DateTime? lastUpdated,
    DateTime? created,
  }) {
    return ClaimantClaim(
      id: id ?? this.id,
      title: title ?? this.title,
      status: status ?? this.status,
      submissionDate: submissionDate ?? this.submissionDate,
      currentStage: currentStage ?? this.currentStage,
      description: description ?? this.description,
      minimumValueClaim: minimumValueClaim ?? this.minimumValueClaim,
      requiredFundingAmount: requiredFundingAmount ?? this.requiredFundingAmount,
      claimType: claimType ?? this.claimType,
      claimIndustry: claimIndustry ?? this.claimIndustry,
      claimantType: claimantType ?? this.claimantType,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      created: created ?? this.created,
    );
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'status': status.value,
      'submissionDate': submissionDate.toIso8601String(),
      'currentStage': currentStage,
      'description': description,
      'minimumValueClaim': minimumValueClaim,
      'requiredFundingAmount': requiredFundingAmount,
      'claimType': claimType,
      'claimIndustry': claimIndustry,
      'claimantType': claimantType,
      'lastUpdated': lastUpdated?.toIso8601String(),
      'created': created.toIso8601String(),
    };
  }

  /// Create from JSON
  factory ClaimantClaim.fromJson(Map<String, dynamic> json) {
    return ClaimantClaim(
      id: json['id'] as String,
      title: json['title'] as String,
      status: ClaimStatus.fromValue(json['status'] as String),
      submissionDate: DateTime.parse(json['submissionDate'] as String),
      currentStage: json['currentStage'] as String,
      description: json['description'] as String?,
      minimumValueClaim: (json['minimumValueClaim'] as num?)?.toDouble(),
      requiredFundingAmount: (json['requiredFundingAmount'] as num?)?.toDouble(),
      claimType: json['claimType'] as String?,
      claimIndustry: json['claimIndustry'] as String?,
      claimantType: json['claimantType'] as String?,
      lastUpdated: json['lastUpdated'] != null 
          ? DateTime.parse(json['lastUpdated'] as String)
          : null,
      created: DateTime.parse(json['created'] as String),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ClaimantClaim && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ClaimantClaim(id: $id, title: $title, status: ${status.displayName})';
  }
}

/// Claim status enum with mapping to funding application statuses
enum ClaimStatus {
  draft('draft', 'Draft', 'Application being prepared'),
  submitted('submitted', 'Submitted', 'Application submitted for review'),
  underReview('pending_review', 'Under Review', 'Application being reviewed'),
  moreInfoRequired('requires_more_info', 'More Info Required', 'Additional information needed'),
  approved('approved_for_funding', 'Approved', 'Application approved for funding'),
  funded('funded', 'Funded', 'Funding has been provided'),
  rejected('rejected', 'Rejected', 'Application was rejected'),
  completed('completed', 'Completed', 'Claim has been completed'),
  cancelled('cancelled', 'Cancelled', 'Claim was cancelled');

  const ClaimStatus(this.value, this.displayName, this.description);

  final String value;
  final String displayName;
  final String description;

  /// Create ClaimStatus from funding application status
  static ClaimStatus fromApplicationStatus(String? status) {
    if (status == null) return ClaimStatus.draft;
    
    for (final claimStatus in ClaimStatus.values) {
      if (claimStatus.value == status.toLowerCase()) {
        return claimStatus;
      }
    }
    
    // Fallback mapping for common variations
    switch (status.toLowerCase()) {
      case 'pending':
      case 'in_review':
        return ClaimStatus.underReview;
      case 'approved':
        return ClaimStatus.approved;
      case 'declined':
      case 'denied':
        return ClaimStatus.rejected;
      default:
        return ClaimStatus.draft;
    }
  }

  /// Create ClaimStatus from string value
  static ClaimStatus fromValue(String value) {
    for (final status in ClaimStatus.values) {
      if (status.value == value) return status;
    }
    return ClaimStatus.draft;
  }

  /// Get status color for UI
  String get colorHex {
    switch (this) {
      case ClaimStatus.draft:
        return '#6B7280'; // Gray
      case ClaimStatus.submitted:
        return '#3B82F6'; // Blue
      case ClaimStatus.underReview:
        return '#F59E0B'; // Amber
      case ClaimStatus.moreInfoRequired:
        return '#EF4444'; // Red
      case ClaimStatus.approved:
        return '#10B981'; // Green
      case ClaimStatus.funded:
        return '#059669'; // Emerald
      case ClaimStatus.rejected:
        return '#DC2626'; // Red
      case ClaimStatus.completed:
        return '#6366F1'; // Indigo
      case ClaimStatus.cancelled:
        return '#6B7280'; // Gray
    }
  }

  /// Check if status indicates an active claim
  bool get isActive {
    return this != ClaimStatus.completed &&
           this != ClaimStatus.cancelled &&
           this != ClaimStatus.rejected;
  }

  /// Check if status indicates funding is available
  bool get isFunded {
    return this == ClaimStatus.approved || this == ClaimStatus.funded;
  }

  /// Get next possible statuses for workflow
  List<ClaimStatus> get nextPossibleStatuses {
    switch (this) {
      case ClaimStatus.draft:
        return [ClaimStatus.submitted];
      case ClaimStatus.submitted:
        return [ClaimStatus.underReview, ClaimStatus.cancelled];
      case ClaimStatus.underReview:
        return [
          ClaimStatus.moreInfoRequired,
          ClaimStatus.approved,
          ClaimStatus.rejected,
        ];
      case ClaimStatus.moreInfoRequired:
        return [ClaimStatus.submitted, ClaimStatus.cancelled];
      case ClaimStatus.approved:
        return [ClaimStatus.funded, ClaimStatus.cancelled];
      case ClaimStatus.funded:
        return [ClaimStatus.completed];
      case ClaimStatus.rejected:
      case ClaimStatus.completed:
      case ClaimStatus.cancelled:
        return [];
    }
  }
}
