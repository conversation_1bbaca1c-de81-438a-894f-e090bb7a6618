// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ChatMessageModelImpl _$$ChatMessageModelImplFromJson(
  Map<String, dynamic> json,
) => _$ChatMessageModelImpl(
  id: json['id'] as String,
  conversationId: json['conversationId'] as String,
  senderId: json['senderId'] as String,
  recipientId: json['recipientId'] as String?,
  recipientGroup: json['recipientGroup'] as String?,
  messageContent: json['messageContent'] as String,
  messageType:
      $enumDecodeNullable(_$MessageTypeEnumMap, json['messageType']) ??
      MessageType.text,
  status:
      $enumDecodeNullable(_$MessageStatusEnumMap, json['status']) ??
      MessageStatus.sent,
  attachmentUrl: json['attachmentUrl'] as String?,
  attachmentUrls:
      (json['attachmentUrls'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
  timestamp: DateTime.parse(json['timestamp'] as String),
  readAt:
      json['readAt'] == null ? null : DateTime.parse(json['readAt'] as String),
  isRead: json['isRead'] as bool?,
  senderName: json['senderName'] as String?,
  senderUserType: json['senderUserType'] as String?,
  senderAvatar: json['senderAvatar'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$ChatMessageModelImplToJson(
  _$ChatMessageModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'conversationId': instance.conversationId,
  'senderId': instance.senderId,
  'recipientId': instance.recipientId,
  'recipientGroup': instance.recipientGroup,
  'messageContent': instance.messageContent,
  'messageType': _$MessageTypeEnumMap[instance.messageType]!,
  'status': _$MessageStatusEnumMap[instance.status]!,
  'attachmentUrl': instance.attachmentUrl,
  'attachmentUrls': instance.attachmentUrls,
  'timestamp': instance.timestamp.toIso8601String(),
  'readAt': instance.readAt?.toIso8601String(),
  'isRead': instance.isRead,
  'senderName': instance.senderName,
  'senderUserType': instance.senderUserType,
  'senderAvatar': instance.senderAvatar,
  'metadata': instance.metadata,
};

const _$MessageTypeEnumMap = {
  MessageType.text: 'text',
  MessageType.file: 'file',
  MessageType.system: 'system',
  MessageType.image: 'image',
  MessageType.document: 'document',
};

const _$MessageStatusEnumMap = {
  MessageStatus.sending: 'sending',
  MessageStatus.sent: 'sent',
  MessageStatus.delivered: 'delivered',
  MessageStatus.read: 'read',
  MessageStatus.failed: 'failed',
};
