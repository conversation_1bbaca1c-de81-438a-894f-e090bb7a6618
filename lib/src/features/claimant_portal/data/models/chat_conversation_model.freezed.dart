// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_conversation_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ConversationParticipant _$ConversationParticipantFromJson(
  Map<String, dynamic> json,
) {
  return _ConversationParticipant.fromJson(json);
}

/// @nodoc
mixin _$ConversationParticipant {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get role =>
      throw _privateConstructorUsedError; // 'claimant', 'agent', 'admin'
  String? get avatar => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;

  /// Serializes this ConversationParticipant to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ConversationParticipant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConversationParticipantCopyWith<ConversationParticipant> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConversationParticipantCopyWith<$Res> {
  factory $ConversationParticipantCopyWith(
    ConversationParticipant value,
    $Res Function(ConversationParticipant) then,
  ) = _$ConversationParticipantCopyWithImpl<$Res, ConversationParticipant>;
  @useResult
  $Res call({
    String id,
    String name,
    String role,
    String? avatar,
    String? email,
    bool isActive,
  });
}

/// @nodoc
class _$ConversationParticipantCopyWithImpl<
  $Res,
  $Val extends ConversationParticipant
>
    implements $ConversationParticipantCopyWith<$Res> {
  _$ConversationParticipantCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConversationParticipant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? role = null,
    Object? avatar = freezed,
    Object? email = freezed,
    Object? isActive = null,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as String,
            name:
                null == name
                    ? _value.name
                    : name // ignore: cast_nullable_to_non_nullable
                        as String,
            role:
                null == role
                    ? _value.role
                    : role // ignore: cast_nullable_to_non_nullable
                        as String,
            avatar:
                freezed == avatar
                    ? _value.avatar
                    : avatar // ignore: cast_nullable_to_non_nullable
                        as String?,
            email:
                freezed == email
                    ? _value.email
                    : email // ignore: cast_nullable_to_non_nullable
                        as String?,
            isActive:
                null == isActive
                    ? _value.isActive
                    : isActive // ignore: cast_nullable_to_non_nullable
                        as bool,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ConversationParticipantImplCopyWith<$Res>
    implements $ConversationParticipantCopyWith<$Res> {
  factory _$$ConversationParticipantImplCopyWith(
    _$ConversationParticipantImpl value,
    $Res Function(_$ConversationParticipantImpl) then,
  ) = __$$ConversationParticipantImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String role,
    String? avatar,
    String? email,
    bool isActive,
  });
}

/// @nodoc
class __$$ConversationParticipantImplCopyWithImpl<$Res>
    extends
        _$ConversationParticipantCopyWithImpl<
          $Res,
          _$ConversationParticipantImpl
        >
    implements _$$ConversationParticipantImplCopyWith<$Res> {
  __$$ConversationParticipantImplCopyWithImpl(
    _$ConversationParticipantImpl _value,
    $Res Function(_$ConversationParticipantImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ConversationParticipant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? role = null,
    Object? avatar = freezed,
    Object? email = freezed,
    Object? isActive = null,
  }) {
    return _then(
      _$ConversationParticipantImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as String,
        name:
            null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                    as String,
        role:
            null == role
                ? _value.role
                : role // ignore: cast_nullable_to_non_nullable
                    as String,
        avatar:
            freezed == avatar
                ? _value.avatar
                : avatar // ignore: cast_nullable_to_non_nullable
                    as String?,
        email:
            freezed == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                    as String?,
        isActive:
            null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                    as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ConversationParticipantImpl implements _ConversationParticipant {
  const _$ConversationParticipantImpl({
    required this.id,
    required this.name,
    required this.role,
    this.avatar,
    this.email,
    this.isActive = true,
  });

  factory _$ConversationParticipantImpl.fromJson(Map<String, dynamic> json) =>
      _$$ConversationParticipantImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String role;
  // 'claimant', 'agent', 'admin'
  @override
  final String? avatar;
  @override
  final String? email;
  @override
  @JsonKey()
  final bool isActive;

  @override
  String toString() {
    return 'ConversationParticipant(id: $id, name: $name, role: $role, avatar: $avatar, email: $email, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConversationParticipantImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, role, avatar, email, isActive);

  /// Create a copy of ConversationParticipant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConversationParticipantImplCopyWith<_$ConversationParticipantImpl>
  get copyWith => __$$ConversationParticipantImplCopyWithImpl<
    _$ConversationParticipantImpl
  >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ConversationParticipantImplToJson(this);
  }
}

abstract class _ConversationParticipant implements ConversationParticipant {
  const factory _ConversationParticipant({
    required final String id,
    required final String name,
    required final String role,
    final String? avatar,
    final String? email,
    final bool isActive,
  }) = _$ConversationParticipantImpl;

  factory _ConversationParticipant.fromJson(Map<String, dynamic> json) =
      _$ConversationParticipantImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get role; // 'claimant', 'agent', 'admin'
  @override
  String? get avatar;
  @override
  String? get email;
  @override
  bool get isActive;

  /// Create a copy of ConversationParticipant
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConversationParticipantImplCopyWith<_$ConversationParticipantImpl>
  get copyWith => throw _privateConstructorUsedError;
}

ChatConversationModel _$ChatConversationModelFromJson(
  Map<String, dynamic> json,
) {
  return _ChatConversationModel.fromJson(json);
}

/// @nodoc
mixin _$ChatConversationModel {
  String get id => throw _privateConstructorUsedError;
  String get claimId =>
      throw _privateConstructorUsedError; // Links to funding_applications
  List<ConversationParticipant> get participants =>
      throw _privateConstructorUsedError;
  ChatMessageModel? get lastMessage => throw _privateConstructorUsedError;
  int get unreadCount => throw _privateConstructorUsedError;
  ConversationStatus get status => throw _privateConstructorUsedError;
  DateTime get createdDate => throw _privateConstructorUsedError;
  DateTime? get lastActivityDate => throw _privateConstructorUsedError;
  String? get title =>
      throw _privateConstructorUsedError; // Optional conversation title
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this ChatConversationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatConversationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatConversationModelCopyWith<ChatConversationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatConversationModelCopyWith<$Res> {
  factory $ChatConversationModelCopyWith(
    ChatConversationModel value,
    $Res Function(ChatConversationModel) then,
  ) = _$ChatConversationModelCopyWithImpl<$Res, ChatConversationModel>;
  @useResult
  $Res call({
    String id,
    String claimId,
    List<ConversationParticipant> participants,
    ChatMessageModel? lastMessage,
    int unreadCount,
    ConversationStatus status,
    DateTime createdDate,
    DateTime? lastActivityDate,
    String? title,
    Map<String, dynamic>? metadata,
  });

  $ChatMessageModelCopyWith<$Res>? get lastMessage;
}

/// @nodoc
class _$ChatConversationModelCopyWithImpl<
  $Res,
  $Val extends ChatConversationModel
>
    implements $ChatConversationModelCopyWith<$Res> {
  _$ChatConversationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatConversationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? claimId = null,
    Object? participants = null,
    Object? lastMessage = freezed,
    Object? unreadCount = null,
    Object? status = null,
    Object? createdDate = null,
    Object? lastActivityDate = freezed,
    Object? title = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as String,
            claimId:
                null == claimId
                    ? _value.claimId
                    : claimId // ignore: cast_nullable_to_non_nullable
                        as String,
            participants:
                null == participants
                    ? _value.participants
                    : participants // ignore: cast_nullable_to_non_nullable
                        as List<ConversationParticipant>,
            lastMessage:
                freezed == lastMessage
                    ? _value.lastMessage
                    : lastMessage // ignore: cast_nullable_to_non_nullable
                        as ChatMessageModel?,
            unreadCount:
                null == unreadCount
                    ? _value.unreadCount
                    : unreadCount // ignore: cast_nullable_to_non_nullable
                        as int,
            status:
                null == status
                    ? _value.status
                    : status // ignore: cast_nullable_to_non_nullable
                        as ConversationStatus,
            createdDate:
                null == createdDate
                    ? _value.createdDate
                    : createdDate // ignore: cast_nullable_to_non_nullable
                        as DateTime,
            lastActivityDate:
                freezed == lastActivityDate
                    ? _value.lastActivityDate
                    : lastActivityDate // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
            title:
                freezed == title
                    ? _value.title
                    : title // ignore: cast_nullable_to_non_nullable
                        as String?,
            metadata:
                freezed == metadata
                    ? _value.metadata
                    : metadata // ignore: cast_nullable_to_non_nullable
                        as Map<String, dynamic>?,
          )
          as $Val,
    );
  }

  /// Create a copy of ChatConversationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ChatMessageModelCopyWith<$Res>? get lastMessage {
    if (_value.lastMessage == null) {
      return null;
    }

    return $ChatMessageModelCopyWith<$Res>(_value.lastMessage!, (value) {
      return _then(_value.copyWith(lastMessage: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ChatConversationModelImplCopyWith<$Res>
    implements $ChatConversationModelCopyWith<$Res> {
  factory _$$ChatConversationModelImplCopyWith(
    _$ChatConversationModelImpl value,
    $Res Function(_$ChatConversationModelImpl) then,
  ) = __$$ChatConversationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String claimId,
    List<ConversationParticipant> participants,
    ChatMessageModel? lastMessage,
    int unreadCount,
    ConversationStatus status,
    DateTime createdDate,
    DateTime? lastActivityDate,
    String? title,
    Map<String, dynamic>? metadata,
  });

  @override
  $ChatMessageModelCopyWith<$Res>? get lastMessage;
}

/// @nodoc
class __$$ChatConversationModelImplCopyWithImpl<$Res>
    extends
        _$ChatConversationModelCopyWithImpl<$Res, _$ChatConversationModelImpl>
    implements _$$ChatConversationModelImplCopyWith<$Res> {
  __$$ChatConversationModelImplCopyWithImpl(
    _$ChatConversationModelImpl _value,
    $Res Function(_$ChatConversationModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ChatConversationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? claimId = null,
    Object? participants = null,
    Object? lastMessage = freezed,
    Object? unreadCount = null,
    Object? status = null,
    Object? createdDate = null,
    Object? lastActivityDate = freezed,
    Object? title = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$ChatConversationModelImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as String,
        claimId:
            null == claimId
                ? _value.claimId
                : claimId // ignore: cast_nullable_to_non_nullable
                    as String,
        participants:
            null == participants
                ? _value._participants
                : participants // ignore: cast_nullable_to_non_nullable
                    as List<ConversationParticipant>,
        lastMessage:
            freezed == lastMessage
                ? _value.lastMessage
                : lastMessage // ignore: cast_nullable_to_non_nullable
                    as ChatMessageModel?,
        unreadCount:
            null == unreadCount
                ? _value.unreadCount
                : unreadCount // ignore: cast_nullable_to_non_nullable
                    as int,
        status:
            null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                    as ConversationStatus,
        createdDate:
            null == createdDate
                ? _value.createdDate
                : createdDate // ignore: cast_nullable_to_non_nullable
                    as DateTime,
        lastActivityDate:
            freezed == lastActivityDate
                ? _value.lastActivityDate
                : lastActivityDate // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
        title:
            freezed == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                    as String?,
        metadata:
            freezed == metadata
                ? _value._metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                    as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatConversationModelImpl implements _ChatConversationModel {
  const _$ChatConversationModelImpl({
    required this.id,
    required this.claimId,
    required final List<ConversationParticipant> participants,
    this.lastMessage,
    this.unreadCount = 0,
    this.status = ConversationStatus.active,
    required this.createdDate,
    this.lastActivityDate,
    this.title,
    final Map<String, dynamic>? metadata,
  }) : _participants = participants,
       _metadata = metadata;

  factory _$ChatConversationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatConversationModelImplFromJson(json);

  @override
  final String id;
  @override
  final String claimId;
  // Links to funding_applications
  final List<ConversationParticipant> _participants;
  // Links to funding_applications
  @override
  List<ConversationParticipant> get participants {
    if (_participants is EqualUnmodifiableListView) return _participants;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_participants);
  }

  @override
  final ChatMessageModel? lastMessage;
  @override
  @JsonKey()
  final int unreadCount;
  @override
  @JsonKey()
  final ConversationStatus status;
  @override
  final DateTime createdDate;
  @override
  final DateTime? lastActivityDate;
  @override
  final String? title;
  // Optional conversation title
  final Map<String, dynamic>? _metadata;
  // Optional conversation title
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'ChatConversationModel(id: $id, claimId: $claimId, participants: $participants, lastMessage: $lastMessage, unreadCount: $unreadCount, status: $status, createdDate: $createdDate, lastActivityDate: $lastActivityDate, title: $title, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatConversationModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.claimId, claimId) || other.claimId == claimId) &&
            const DeepCollectionEquality().equals(
              other._participants,
              _participants,
            ) &&
            (identical(other.lastMessage, lastMessage) ||
                other.lastMessage == lastMessage) &&
            (identical(other.unreadCount, unreadCount) ||
                other.unreadCount == unreadCount) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.lastActivityDate, lastActivityDate) ||
                other.lastActivityDate == lastActivityDate) &&
            (identical(other.title, title) || other.title == title) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    claimId,
    const DeepCollectionEquality().hash(_participants),
    lastMessage,
    unreadCount,
    status,
    createdDate,
    lastActivityDate,
    title,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of ChatConversationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatConversationModelImplCopyWith<_$ChatConversationModelImpl>
  get copyWith =>
      __$$ChatConversationModelImplCopyWithImpl<_$ChatConversationModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatConversationModelImplToJson(this);
  }
}

abstract class _ChatConversationModel implements ChatConversationModel {
  const factory _ChatConversationModel({
    required final String id,
    required final String claimId,
    required final List<ConversationParticipant> participants,
    final ChatMessageModel? lastMessage,
    final int unreadCount,
    final ConversationStatus status,
    required final DateTime createdDate,
    final DateTime? lastActivityDate,
    final String? title,
    final Map<String, dynamic>? metadata,
  }) = _$ChatConversationModelImpl;

  factory _ChatConversationModel.fromJson(Map<String, dynamic> json) =
      _$ChatConversationModelImpl.fromJson;

  @override
  String get id;
  @override
  String get claimId; // Links to funding_applications
  @override
  List<ConversationParticipant> get participants;
  @override
  ChatMessageModel? get lastMessage;
  @override
  int get unreadCount;
  @override
  ConversationStatus get status;
  @override
  DateTime get createdDate;
  @override
  DateTime? get lastActivityDate;
  @override
  String? get title; // Optional conversation title
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of ChatConversationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatConversationModelImplCopyWith<_$ChatConversationModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}
