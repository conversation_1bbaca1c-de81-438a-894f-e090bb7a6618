// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_message_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ChatMessageModel _$ChatMessageModelFromJson(Map<String, dynamic> json) {
  return _ChatMessageModel.fromJson(json);
}

/// @nodoc
mixin _$ChatMessageModel {
  String get id => throw _privateConstructorUsedError;
  String get conversationId =>
      throw _privateConstructorUsedError; // Maps to application_id or claim_id
  String get senderId => throw _privateConstructorUsedError;
  String? get recipientId => throw _privateConstructorUsedError;
  String? get recipientGroup => throw _privateConstructorUsedError;
  String get messageContent => throw _privateConstructorUsedError;
  MessageType get messageType => throw _privateConstructorUsedError;
  MessageStatus get status => throw _privateConstructorUsedError;
  String? get attachmentUrl => throw _privateConstructorUsedError;
  List<String>? get attachmentUrls =>
      throw _privateConstructorUsedError; // For multiple file attachments
  DateTime get timestamp => throw _privateConstructorUsedError;
  DateTime? get readAt => throw _privateConstructorUsedError;
  bool? get isRead =>
      throw _privateConstructorUsedError; // Expanded sender information
  String? get senderName => throw _privateConstructorUsedError;
  String? get senderUserType => throw _privateConstructorUsedError;
  String? get senderAvatar =>
      throw _privateConstructorUsedError; // Additional metadata
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this ChatMessageModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChatMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChatMessageModelCopyWith<ChatMessageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatMessageModelCopyWith<$Res> {
  factory $ChatMessageModelCopyWith(
    ChatMessageModel value,
    $Res Function(ChatMessageModel) then,
  ) = _$ChatMessageModelCopyWithImpl<$Res, ChatMessageModel>;
  @useResult
  $Res call({
    String id,
    String conversationId,
    String senderId,
    String? recipientId,
    String? recipientGroup,
    String messageContent,
    MessageType messageType,
    MessageStatus status,
    String? attachmentUrl,
    List<String>? attachmentUrls,
    DateTime timestamp,
    DateTime? readAt,
    bool? isRead,
    String? senderName,
    String? senderUserType,
    String? senderAvatar,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$ChatMessageModelCopyWithImpl<$Res, $Val extends ChatMessageModel>
    implements $ChatMessageModelCopyWith<$Res> {
  _$ChatMessageModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChatMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? conversationId = null,
    Object? senderId = null,
    Object? recipientId = freezed,
    Object? recipientGroup = freezed,
    Object? messageContent = null,
    Object? messageType = null,
    Object? status = null,
    Object? attachmentUrl = freezed,
    Object? attachmentUrls = freezed,
    Object? timestamp = null,
    Object? readAt = freezed,
    Object? isRead = freezed,
    Object? senderName = freezed,
    Object? senderUserType = freezed,
    Object? senderAvatar = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as String,
            conversationId:
                null == conversationId
                    ? _value.conversationId
                    : conversationId // ignore: cast_nullable_to_non_nullable
                        as String,
            senderId:
                null == senderId
                    ? _value.senderId
                    : senderId // ignore: cast_nullable_to_non_nullable
                        as String,
            recipientId:
                freezed == recipientId
                    ? _value.recipientId
                    : recipientId // ignore: cast_nullable_to_non_nullable
                        as String?,
            recipientGroup:
                freezed == recipientGroup
                    ? _value.recipientGroup
                    : recipientGroup // ignore: cast_nullable_to_non_nullable
                        as String?,
            messageContent:
                null == messageContent
                    ? _value.messageContent
                    : messageContent // ignore: cast_nullable_to_non_nullable
                        as String,
            messageType:
                null == messageType
                    ? _value.messageType
                    : messageType // ignore: cast_nullable_to_non_nullable
                        as MessageType,
            status:
                null == status
                    ? _value.status
                    : status // ignore: cast_nullable_to_non_nullable
                        as MessageStatus,
            attachmentUrl:
                freezed == attachmentUrl
                    ? _value.attachmentUrl
                    : attachmentUrl // ignore: cast_nullable_to_non_nullable
                        as String?,
            attachmentUrls:
                freezed == attachmentUrls
                    ? _value.attachmentUrls
                    : attachmentUrls // ignore: cast_nullable_to_non_nullable
                        as List<String>?,
            timestamp:
                null == timestamp
                    ? _value.timestamp
                    : timestamp // ignore: cast_nullable_to_non_nullable
                        as DateTime,
            readAt:
                freezed == readAt
                    ? _value.readAt
                    : readAt // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
            isRead:
                freezed == isRead
                    ? _value.isRead
                    : isRead // ignore: cast_nullable_to_non_nullable
                        as bool?,
            senderName:
                freezed == senderName
                    ? _value.senderName
                    : senderName // ignore: cast_nullable_to_non_nullable
                        as String?,
            senderUserType:
                freezed == senderUserType
                    ? _value.senderUserType
                    : senderUserType // ignore: cast_nullable_to_non_nullable
                        as String?,
            senderAvatar:
                freezed == senderAvatar
                    ? _value.senderAvatar
                    : senderAvatar // ignore: cast_nullable_to_non_nullable
                        as String?,
            metadata:
                freezed == metadata
                    ? _value.metadata
                    : metadata // ignore: cast_nullable_to_non_nullable
                        as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ChatMessageModelImplCopyWith<$Res>
    implements $ChatMessageModelCopyWith<$Res> {
  factory _$$ChatMessageModelImplCopyWith(
    _$ChatMessageModelImpl value,
    $Res Function(_$ChatMessageModelImpl) then,
  ) = __$$ChatMessageModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String conversationId,
    String senderId,
    String? recipientId,
    String? recipientGroup,
    String messageContent,
    MessageType messageType,
    MessageStatus status,
    String? attachmentUrl,
    List<String>? attachmentUrls,
    DateTime timestamp,
    DateTime? readAt,
    bool? isRead,
    String? senderName,
    String? senderUserType,
    String? senderAvatar,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$ChatMessageModelImplCopyWithImpl<$Res>
    extends _$ChatMessageModelCopyWithImpl<$Res, _$ChatMessageModelImpl>
    implements _$$ChatMessageModelImplCopyWith<$Res> {
  __$$ChatMessageModelImplCopyWithImpl(
    _$ChatMessageModelImpl _value,
    $Res Function(_$ChatMessageModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ChatMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? conversationId = null,
    Object? senderId = null,
    Object? recipientId = freezed,
    Object? recipientGroup = freezed,
    Object? messageContent = null,
    Object? messageType = null,
    Object? status = null,
    Object? attachmentUrl = freezed,
    Object? attachmentUrls = freezed,
    Object? timestamp = null,
    Object? readAt = freezed,
    Object? isRead = freezed,
    Object? senderName = freezed,
    Object? senderUserType = freezed,
    Object? senderAvatar = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$ChatMessageModelImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as String,
        conversationId:
            null == conversationId
                ? _value.conversationId
                : conversationId // ignore: cast_nullable_to_non_nullable
                    as String,
        senderId:
            null == senderId
                ? _value.senderId
                : senderId // ignore: cast_nullable_to_non_nullable
                    as String,
        recipientId:
            freezed == recipientId
                ? _value.recipientId
                : recipientId // ignore: cast_nullable_to_non_nullable
                    as String?,
        recipientGroup:
            freezed == recipientGroup
                ? _value.recipientGroup
                : recipientGroup // ignore: cast_nullable_to_non_nullable
                    as String?,
        messageContent:
            null == messageContent
                ? _value.messageContent
                : messageContent // ignore: cast_nullable_to_non_nullable
                    as String,
        messageType:
            null == messageType
                ? _value.messageType
                : messageType // ignore: cast_nullable_to_non_nullable
                    as MessageType,
        status:
            null == status
                ? _value.status
                : status // ignore: cast_nullable_to_non_nullable
                    as MessageStatus,
        attachmentUrl:
            freezed == attachmentUrl
                ? _value.attachmentUrl
                : attachmentUrl // ignore: cast_nullable_to_non_nullable
                    as String?,
        attachmentUrls:
            freezed == attachmentUrls
                ? _value._attachmentUrls
                : attachmentUrls // ignore: cast_nullable_to_non_nullable
                    as List<String>?,
        timestamp:
            null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                    as DateTime,
        readAt:
            freezed == readAt
                ? _value.readAt
                : readAt // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
        isRead:
            freezed == isRead
                ? _value.isRead
                : isRead // ignore: cast_nullable_to_non_nullable
                    as bool?,
        senderName:
            freezed == senderName
                ? _value.senderName
                : senderName // ignore: cast_nullable_to_non_nullable
                    as String?,
        senderUserType:
            freezed == senderUserType
                ? _value.senderUserType
                : senderUserType // ignore: cast_nullable_to_non_nullable
                    as String?,
        senderAvatar:
            freezed == senderAvatar
                ? _value.senderAvatar
                : senderAvatar // ignore: cast_nullable_to_non_nullable
                    as String?,
        metadata:
            freezed == metadata
                ? _value._metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                    as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatMessageModelImpl implements _ChatMessageModel {
  const _$ChatMessageModelImpl({
    required this.id,
    required this.conversationId,
    required this.senderId,
    this.recipientId,
    this.recipientGroup,
    required this.messageContent,
    this.messageType = MessageType.text,
    this.status = MessageStatus.sent,
    this.attachmentUrl,
    final List<String>? attachmentUrls,
    required this.timestamp,
    this.readAt,
    this.isRead,
    this.senderName,
    this.senderUserType,
    this.senderAvatar,
    final Map<String, dynamic>? metadata,
  }) : _attachmentUrls = attachmentUrls,
       _metadata = metadata;

  factory _$ChatMessageModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatMessageModelImplFromJson(json);

  @override
  final String id;
  @override
  final String conversationId;
  // Maps to application_id or claim_id
  @override
  final String senderId;
  @override
  final String? recipientId;
  @override
  final String? recipientGroup;
  @override
  final String messageContent;
  @override
  @JsonKey()
  final MessageType messageType;
  @override
  @JsonKey()
  final MessageStatus status;
  @override
  final String? attachmentUrl;
  final List<String>? _attachmentUrls;
  @override
  List<String>? get attachmentUrls {
    final value = _attachmentUrls;
    if (value == null) return null;
    if (_attachmentUrls is EqualUnmodifiableListView) return _attachmentUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  // For multiple file attachments
  @override
  final DateTime timestamp;
  @override
  final DateTime? readAt;
  @override
  final bool? isRead;
  // Expanded sender information
  @override
  final String? senderName;
  @override
  final String? senderUserType;
  @override
  final String? senderAvatar;
  // Additional metadata
  final Map<String, dynamic>? _metadata;
  // Additional metadata
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'ChatMessageModel(id: $id, conversationId: $conversationId, senderId: $senderId, recipientId: $recipientId, recipientGroup: $recipientGroup, messageContent: $messageContent, messageType: $messageType, status: $status, attachmentUrl: $attachmentUrl, attachmentUrls: $attachmentUrls, timestamp: $timestamp, readAt: $readAt, isRead: $isRead, senderName: $senderName, senderUserType: $senderUserType, senderAvatar: $senderAvatar, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatMessageModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.senderId, senderId) ||
                other.senderId == senderId) &&
            (identical(other.recipientId, recipientId) ||
                other.recipientId == recipientId) &&
            (identical(other.recipientGroup, recipientGroup) ||
                other.recipientGroup == recipientGroup) &&
            (identical(other.messageContent, messageContent) ||
                other.messageContent == messageContent) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.attachmentUrl, attachmentUrl) ||
                other.attachmentUrl == attachmentUrl) &&
            const DeepCollectionEquality().equals(
              other._attachmentUrls,
              _attachmentUrls,
            ) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.readAt, readAt) || other.readAt == readAt) &&
            (identical(other.isRead, isRead) || other.isRead == isRead) &&
            (identical(other.senderName, senderName) ||
                other.senderName == senderName) &&
            (identical(other.senderUserType, senderUserType) ||
                other.senderUserType == senderUserType) &&
            (identical(other.senderAvatar, senderAvatar) ||
                other.senderAvatar == senderAvatar) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    conversationId,
    senderId,
    recipientId,
    recipientGroup,
    messageContent,
    messageType,
    status,
    attachmentUrl,
    const DeepCollectionEquality().hash(_attachmentUrls),
    timestamp,
    readAt,
    isRead,
    senderName,
    senderUserType,
    senderAvatar,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of ChatMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatMessageModelImplCopyWith<_$ChatMessageModelImpl> get copyWith =>
      __$$ChatMessageModelImplCopyWithImpl<_$ChatMessageModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatMessageModelImplToJson(this);
  }
}

abstract class _ChatMessageModel implements ChatMessageModel {
  const factory _ChatMessageModel({
    required final String id,
    required final String conversationId,
    required final String senderId,
    final String? recipientId,
    final String? recipientGroup,
    required final String messageContent,
    final MessageType messageType,
    final MessageStatus status,
    final String? attachmentUrl,
    final List<String>? attachmentUrls,
    required final DateTime timestamp,
    final DateTime? readAt,
    final bool? isRead,
    final String? senderName,
    final String? senderUserType,
    final String? senderAvatar,
    final Map<String, dynamic>? metadata,
  }) = _$ChatMessageModelImpl;

  factory _ChatMessageModel.fromJson(Map<String, dynamic> json) =
      _$ChatMessageModelImpl.fromJson;

  @override
  String get id;
  @override
  String get conversationId; // Maps to application_id or claim_id
  @override
  String get senderId;
  @override
  String? get recipientId;
  @override
  String? get recipientGroup;
  @override
  String get messageContent;
  @override
  MessageType get messageType;
  @override
  MessageStatus get status;
  @override
  String? get attachmentUrl;
  @override
  List<String>? get attachmentUrls; // For multiple file attachments
  @override
  DateTime get timestamp;
  @override
  DateTime? get readAt;
  @override
  bool? get isRead; // Expanded sender information
  @override
  String? get senderName;
  @override
  String? get senderUserType;
  @override
  String? get senderAvatar; // Additional metadata
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of ChatMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChatMessageModelImplCopyWith<_$ChatMessageModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
