import 'package:freezed_annotation/freezed_annotation.dart';
import 'chat_message_model.dart';

part 'chat_conversation_model.freezed.dart';
part 'chat_conversation_model.g.dart';

/// Enumeration for conversation status
enum ConversationStatus {
  @JsonValue('active')
  active,
  @JsonValue('closed')
  closed,
  @JsonValue('archived')
  archived,
}

/// Participant information in a conversation
@freezed
class ConversationParticipant with _$ConversationParticipant {
  const factory ConversationParticipant({
    required String id,
    required String name,
    required String role, // 'claimant', 'agent', 'admin'
    String? avatar,
    String? email,
    @Default(true) bool isActive,
  }) = _ConversationParticipant;

  factory ConversationParticipant.fromJson(Map<String, dynamic> json) =>
      _$ConversationParticipantFromJson(json);
}

/// Chat conversation model for claimant portal
/// Represents a conversation thread between claimant and 3Pay agents
@freezed
class ChatConversationModel with _$ChatConversationModel {
  const factory ChatConversationModel({
    required String id,
    required String claimId, // Links to funding_applications
    required List<ConversationParticipant> participants,
    ChatMessageModel? lastMessage,
    @Default(0) int unreadCount,
    @Default(ConversationStatus.active) ConversationStatus status,
    required DateTime createdDate,
    DateTime? lastActivityDate,
    String? title, // Optional conversation title
    Map<String, dynamic>? metadata,
  }) = _ChatConversationModel;

  factory ChatConversationModel.fromJson(Map<String, dynamic> json) =>
      _$ChatConversationModelFromJson(json);
}

/// Helper class for ChatConversationModel operations
class ChatConversationHelper {
  /// Create conversation from claim data
  static ChatConversationModel fromClaimData({
    required String claimId,
    required String claimTitle,
    required String claimantId,
    required String claimantName,
    String? claimantEmail,
  }) {
    final participants = [
      ConversationParticipant(
        id: claimantId,
        name: claimantName,
        role: 'claimant',
        email: claimantEmail,
      ),
      const ConversationParticipant(
        id: 'agent',
        name: '3Pay Global Agent',
        role: 'agent',
      ),
    ];

    return ChatConversationModel(
      id: 'conv_$claimId', // Generate conversation ID from claim ID
      claimId: claimId,
      participants: participants,
      createdDate: DateTime.now(),
      title: 'Chat for $claimTitle',
    );
  }

  /// Create from messages list (derive conversation from messages)
  static ChatConversationModel fromMessages({
    required String claimId,
    required List<ChatMessageModel> messages,
    required String currentUserId,
  }) {
    if (messages.isEmpty) {
      throw ArgumentError(
        'Cannot create conversation from empty messages list',
      );
    }

    // Get unique participants from messages
    final participantIds = <String>{};
    final participantData = <String, Map<String, String>>{};

    for (final message in messages) {
      participantIds.add(message.senderId);
      if (message.recipientId != null) {
        participantIds.add(message.recipientId!);
      }

      // Store participant data
      if (message.senderName != null) {
        participantData[message.senderId] = {
          'name': message.senderName!,
          'role': message.senderUserType ?? 'unknown',
        };
      }
    }

    // Create participants list
    final participants =
        participantIds.map((id) {
          final data = participantData[id];
          return ConversationParticipant(
            id: id,
            name: data?['name'] ?? (id == currentUserId ? 'You' : 'Unknown'),
            role: data?['role'] ?? (id == currentUserId ? 'claimant' : 'agent'),
          );
        }).toList();

    // Get last message
    final sortedMessages = List<ChatMessageModel>.from(messages)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    final lastMessage = sortedMessages.first;

    // Count unread messages (messages not from current user that are unread)
    final unreadCount =
        messages
            .where(
              (msg) =>
                  msg.senderId != currentUserId &&
                  (msg.isRead == null || !msg.isRead!),
            )
            .length;

    return ChatConversationModel(
      id: 'conv_$claimId',
      claimId: claimId,
      participants: participants,
      lastMessage: lastMessage,
      unreadCount: unreadCount,
      createdDate: messages
          .map((m) => m.timestamp)
          .reduce((a, b) => a.isBefore(b) ? a : b),
      lastActivityDate: lastMessage.timestamp,
    );
  }

  /// Check if conversation has unread messages
  static bool hasUnreadMessages(ChatConversationModel conversation) {
    return conversation.unreadCount > 0;
  }

  /// Get conversation display title
  static String getDisplayTitle(ChatConversationModel conversation) {
    if (conversation.title != null && conversation.title!.isNotEmpty) {
      return conversation.title!;
    }
    return 'Claim Discussion';
  }

  /// Get other participants (excluding current user)
  static List<ConversationParticipant> getOtherParticipants(
    ChatConversationModel conversation,
    String currentUserId,
  ) {
    return conversation.participants
        .where((p) => p.id != currentUserId)
        .toList();
  }

  /// Get current user participant
  static ConversationParticipant? getCurrentUserParticipant(
    ChatConversationModel conversation,
    String currentUserId,
  ) {
    try {
      return conversation.participants.firstWhere((p) => p.id == currentUserId);
    } catch (e) {
      return null;
    }
  }

  /// Check if conversation is active
  static bool isActive(ChatConversationModel conversation) {
    return conversation.status == ConversationStatus.active;
  }

  /// Get formatted last activity time
  static String getFormattedLastActivity(ChatConversationModel conversation) {
    final lastActivity =
        conversation.lastActivityDate ?? conversation.createdDate;
    final now = DateTime.now();
    final difference = now.difference(lastActivity);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Get conversation summary for display
  static String getLastMessageSummary(ChatConversationModel conversation) {
    if (conversation.lastMessage == null) {
      return 'No messages yet';
    }

    final message = conversation.lastMessage!;
    final senderName = ChatMessageHelper.getDisplaySenderName(message);
    final content = message.messageContent;

    if (content.length > 50) {
      return '$senderName: ${content.substring(0, 50)}...';
    }
    return '$senderName: $content';
  }
}
