/// Detailed claim status model with history and metadata
/// Provides comprehensive status tracking for claims
class ClaimStatusModel {
  final String id;
  final String claimId;
  final String status;
  final String displayName;
  final String description;
  final String colorHex;
  final DateTime timestamp;
  final String? changedBy;
  final String? notes;
  final Map<String, dynamic>? metadata;
  final bool isActive;
  final bool isFunded;
  final List<String> nextPossibleStatuses;

  const ClaimStatusModel({
    required this.id,
    required this.claimId,
    required this.status,
    required this.displayName,
    required this.description,
    required this.colorHex,
    required this.timestamp,
    this.changedBy,
    this.notes,
    this.metadata,
    required this.isActive,
    required this.isFunded,
    required this.nextPossibleStatuses,
  });

  /// Create ClaimStatusModel from status string
  factory ClaimStatusModel.fromStatus({
    required String id,
    required String claimId,
    required String status,
    DateTime? timestamp,
    String? changedBy,
    String? notes,
    Map<String, dynamic>? metadata,
  }) {
    final statusInfo = _getStatusInfo(status);
    
    return ClaimStatusModel(
      id: id,
      claimId: claimId,
      status: status,
      displayName: statusInfo['displayName'] as String,
      description: statusInfo['description'] as String,
      colorHex: statusInfo['colorHex'] as String,
      timestamp: timestamp ?? DateTime.now(),
      changedBy: changedBy,
      notes: notes,
      metadata: metadata,
      isActive: statusInfo['isActive'] as bool,
      isFunded: statusInfo['isFunded'] as bool,
      nextPossibleStatuses: statusInfo['nextPossibleStatuses'] as List<String>,
    );
  }

  /// Get status information for a given status
  static Map<String, dynamic> _getStatusInfo(String status) {
    switch (status.toLowerCase()) {
      case 'draft':
        return {
          'displayName': 'Draft',
          'description': 'Application being prepared',
          'colorHex': '#6B7280',
          'isActive': true,
          'isFunded': false,
          'nextPossibleStatuses': ['submitted'],
        };
      case 'submitted':
        return {
          'displayName': 'Submitted',
          'description': 'Application submitted for review',
          'colorHex': '#3B82F6',
          'isActive': true,
          'isFunded': false,
          'nextPossibleStatuses': ['pending_review', 'cancelled'],
        };
      case 'pending_review':
        return {
          'displayName': 'Under Review',
          'description': 'Application being reviewed',
          'colorHex': '#F59E0B',
          'isActive': true,
          'isFunded': false,
          'nextPossibleStatuses': ['requires_more_info', 'approved_for_funding', 'rejected'],
        };
      case 'requires_more_info':
        return {
          'displayName': 'More Info Required',
          'description': 'Additional information needed',
          'colorHex': '#EF4444',
          'isActive': true,
          'isFunded': false,
          'nextPossibleStatuses': ['submitted', 'cancelled'],
        };
      case 'approved_for_funding':
        return {
          'displayName': 'Approved',
          'description': 'Application approved for funding',
          'colorHex': '#10B981',
          'isActive': true,
          'isFunded': true,
          'nextPossibleStatuses': ['funded', 'cancelled'],
        };
      case 'funded':
        return {
          'displayName': 'Funded',
          'description': 'Funding has been provided',
          'colorHex': '#059669',
          'isActive': true,
          'isFunded': true,
          'nextPossibleStatuses': ['completed'],
        };
      case 'rejected':
        return {
          'displayName': 'Rejected',
          'description': 'Application was rejected',
          'colorHex': '#DC2626',
          'isActive': false,
          'isFunded': false,
          'nextPossibleStatuses': <String>[],
        };
      case 'completed':
        return {
          'displayName': 'Completed',
          'description': 'Claim has been completed',
          'colorHex': '#6366F1',
          'isActive': false,
          'isFunded': false,
          'nextPossibleStatuses': <String>[],
        };
      case 'cancelled':
        return {
          'displayName': 'Cancelled',
          'description': 'Claim was cancelled',
          'colorHex': '#6B7280',
          'isActive': false,
          'isFunded': false,
          'nextPossibleStatuses': <String>[],
        };
      default:
        return {
          'displayName': 'Unknown',
          'description': 'Status not recognized',
          'colorHex': '#6B7280',
          'isActive': false,
          'isFunded': false,
          'nextPossibleStatuses': <String>[],
        };
    }
  }

  /// Get formatted timestamp
  String get formattedTimestamp {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }

  /// Get formatted date
  String get formattedDate {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
  }

  /// Get formatted date and time
  String get formattedDateTime {
    return '${formattedDate} at ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  /// Check if status can transition to another status
  bool canTransitionTo(String targetStatus) {
    return nextPossibleStatuses.contains(targetStatus);
  }

  /// Get status priority for sorting (lower number = higher priority)
  int get priority {
    switch (status.toLowerCase()) {
      case 'requires_more_info':
        return 1;
      case 'pending_review':
        return 2;
      case 'submitted':
        return 3;
      case 'approved_for_funding':
        return 4;
      case 'funded':
        return 5;
      case 'draft':
        return 6;
      case 'completed':
        return 7;
      case 'rejected':
        return 8;
      case 'cancelled':
        return 9;
      default:
        return 10;
    }
  }

  /// Copy with method for immutable updates
  ClaimStatusModel copyWith({
    String? id,
    String? claimId,
    String? status,
    String? displayName,
    String? description,
    String? colorHex,
    DateTime? timestamp,
    String? changedBy,
    String? notes,
    Map<String, dynamic>? metadata,
    bool? isActive,
    bool? isFunded,
    List<String>? nextPossibleStatuses,
  }) {
    return ClaimStatusModel(
      id: id ?? this.id,
      claimId: claimId ?? this.claimId,
      status: status ?? this.status,
      displayName: displayName ?? this.displayName,
      description: description ?? this.description,
      colorHex: colorHex ?? this.colorHex,
      timestamp: timestamp ?? this.timestamp,
      changedBy: changedBy ?? this.changedBy,
      notes: notes ?? this.notes,
      metadata: metadata ?? this.metadata,
      isActive: isActive ?? this.isActive,
      isFunded: isFunded ?? this.isFunded,
      nextPossibleStatuses: nextPossibleStatuses ?? this.nextPossibleStatuses,
    );
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'claimId': claimId,
      'status': status,
      'displayName': displayName,
      'description': description,
      'colorHex': colorHex,
      'timestamp': timestamp.toIso8601String(),
      'changedBy': changedBy,
      'notes': notes,
      'metadata': metadata,
      'isActive': isActive,
      'isFunded': isFunded,
      'nextPossibleStatuses': nextPossibleStatuses,
    };
  }

  /// Create from JSON
  factory ClaimStatusModel.fromJson(Map<String, dynamic> json) {
    return ClaimStatusModel(
      id: json['id'] as String,
      claimId: json['claimId'] as String,
      status: json['status'] as String,
      displayName: json['displayName'] as String,
      description: json['description'] as String,
      colorHex: json['colorHex'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      changedBy: json['changedBy'] as String?,
      notes: json['notes'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      isActive: json['isActive'] as bool,
      isFunded: json['isFunded'] as bool,
      nextPossibleStatuses: List<String>.from(json['nextPossibleStatuses'] as List),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ClaimStatusModel && 
           other.id == id && 
           other.claimId == claimId &&
           other.status == status;
  }

  @override
  int get hashCode => Object.hash(id, claimId, status);

  @override
  String toString() {
    return 'ClaimStatusModel(id: $id, claimId: $claimId, status: $status, displayName: $displayName)';
  }
}

/// Status history model for tracking claim status changes
class ClaimStatusHistory {
  final String claimId;
  final List<ClaimStatusModel> statusHistory;
  final ClaimStatusModel currentStatus;

  const ClaimStatusHistory({
    required this.claimId,
    required this.statusHistory,
    required this.currentStatus,
  });

  /// Get status changes in chronological order (oldest first)
  List<ClaimStatusModel> get chronologicalHistory {
    final sorted = List<ClaimStatusModel>.from(statusHistory);
    sorted.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    return sorted;
  }

  /// Get status changes in reverse chronological order (newest first)
  List<ClaimStatusModel> get reverseChronologicalHistory {
    final sorted = List<ClaimStatusModel>.from(statusHistory);
    sorted.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sorted;
  }

  /// Get the previous status
  ClaimStatusModel? get previousStatus {
    if (statusHistory.length < 2) return null;
    final sorted = reverseChronologicalHistory;
    return sorted[1]; // Second most recent
  }

  /// Get duration in current status
  Duration get durationInCurrentStatus {
    return DateTime.now().difference(currentStatus.timestamp);
  }

  /// Get total duration from first status to now
  Duration get totalDuration {
    if (statusHistory.isEmpty) return Duration.zero;
    final firstStatus = chronologicalHistory.first;
    return DateTime.now().difference(firstStatus.timestamp);
  }

  /// Check if status has changed recently (within specified duration)
  bool hasChangedRecently(Duration duration) {
    return durationInCurrentStatus <= duration;
  }

  /// Get status at a specific point in time
  ClaimStatusModel? getStatusAt(DateTime dateTime) {
    final validStatuses = statusHistory
        .where((status) => status.timestamp.isBefore(dateTime) || 
                          status.timestamp.isAtSameMomentAs(dateTime))
        .toList();
    
    if (validStatuses.isEmpty) return null;
    
    validStatuses.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return validStatuses.first;
  }

  /// Add a new status to the history
  ClaimStatusHistory addStatus(ClaimStatusModel newStatus) {
    final updatedHistory = List<ClaimStatusModel>.from(statusHistory);
    updatedHistory.add(newStatus);
    
    return ClaimStatusHistory(
      claimId: claimId,
      statusHistory: updatedHistory,
      currentStatus: newStatus,
    );
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'claimId': claimId,
      'statusHistory': statusHistory.map((status) => status.toJson()).toList(),
      'currentStatus': currentStatus.toJson(),
    };
  }

  /// Create from JSON
  factory ClaimStatusHistory.fromJson(Map<String, dynamic> json) {
    final statusHistoryList = (json['statusHistory'] as List)
        .map((item) => ClaimStatusModel.fromJson(item as Map<String, dynamic>))
        .toList();
    
    return ClaimStatusHistory(
      claimId: json['claimId'] as String,
      statusHistory: statusHistoryList,
      currentStatus: ClaimStatusModel.fromJson(json['currentStatus'] as Map<String, dynamic>),
    );
  }
}
