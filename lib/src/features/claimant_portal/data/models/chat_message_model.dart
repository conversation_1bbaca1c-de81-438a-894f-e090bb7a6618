import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pocketbase/pocketbase.dart';

part 'chat_message_model.freezed.dart';
part 'chat_message_model.g.dart';

/// Enumeration for different message types
enum MessageType {
  @JsonValue('text')
  text,
  @JsonValue('file')
  file,
  @JsonValue('system')
  system,
  @JsonValue('image')
  image,
  @JsonValue('document')
  document,
}

/// Enumeration for message delivery status
enum MessageStatus {
  @JsonValue('sending')
  sending,
  @JsonValue('sent')
  sent,
  @JsonValue('delivered')
  delivered,
  @JsonValue('read')
  read,
  @JsonValue('failed')
  failed,
}

/// Chat message model for claimant portal
/// Maps from application_communications collection
@freezed
class ChatMessageModel with _$ChatMessageModel {
  const factory ChatMessageModel({
    required String id,
    required String conversationId, // Maps to application_id or claim_id
    required String senderId,
    String? recipientId,
    String? recipientGroup,
    required String messageContent,
    @Default(MessageType.text) MessageType messageType,
    @Default(MessageStatus.sent) MessageStatus status,
    String? attachmentUrl,
    List<String>? attachmentUrls, // For multiple file attachments
    required DateTime timestamp,
    DateTime? readAt,
    bool? isRead,
    // Expanded sender information
    String? senderName,
    String? senderUserType,
    String? senderAvatar,
    // Additional metadata
    Map<String, dynamic>? metadata,
  }) = _ChatMessageModel;

  factory ChatMessageModel.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageModelFromJson(json);
}

/// Helper class for ChatMessageModel operations
class ChatMessageHelper {
  /// Create from PocketBase record
  static ChatMessageModel fromRecord(RecordModel record) {
    // Extract sender information from expanded data
    final senderExpanded = record.get<RecordModel>('expand.sender_id');
    String? senderName;
    String? senderUserType;
    String? senderAvatar;

    if (senderExpanded != null) {
      final name = senderExpanded.getStringValue('name');
      final firstName = senderExpanded.getStringValue('first_name');
      final email = senderExpanded.getStringValue('email');

      senderName =
          name.isNotEmpty
              ? name
              : firstName.isNotEmpty
              ? firstName
              : email;
      senderUserType = senderExpanded.getStringValue('user_type');

      // Handle avatar - ensure empty strings are treated as null to prevent NetworkImage errors
      final avatarValue = senderExpanded.getStringValue('avatar');
      senderAvatar = avatarValue.isNotEmpty ? avatarValue : null;
    }

    // Parse message type
    MessageType messageType = MessageType.text;
    final typeString = record.getStringValue('message_type');
    if (typeString.isNotEmpty) {
      messageType = MessageType.values.firstWhere(
        (type) => type.name == typeString,
        orElse: () => MessageType.text,
      );
    }

    // Parse attachment URLs
    List<String>? attachmentUrls;
    final attachmentData = record.data['attachment_urls'];
    if (attachmentData is List) {
      attachmentUrls = attachmentData.map((e) => e.toString()).toList();
    }

    return ChatMessageModel(
      id: record.id,
      conversationId:
          record.getStringValue('application_id').isNotEmpty
              ? record.getStringValue('application_id')
              : record.getStringValue('claim_id'),
      senderId: record.getStringValue('sender_id'),
      recipientId: record.getStringValue('recipient_id'),
      recipientGroup: record.getStringValue('recipient_group'),
      messageContent: record.getStringValue('message_content'),
      messageType: messageType,
      attachmentUrl: record.getStringValue('attachment_url'),
      attachmentUrls: attachmentUrls,
      timestamp:
          DateTime.tryParse(record.get<String>('created')) ?? DateTime.now(),
      readAt:
          record.data['read_at'] != null
              ? DateTime.tryParse(record.data['read_at'].toString())
              : null,
      isRead: record.data['is_read'] as bool?,
      senderName: senderName,
      senderUserType: senderUserType,
      senderAvatar: senderAvatar,
      metadata: record.data['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON for PocketBase creation
  static Map<String, dynamic> toCreateJson(ChatMessageModel message) {
    return {
      'application_id': message.conversationId,
      'sender_id': message.senderId,
      if (message.recipientId != null) 'recipient_id': message.recipientId,
      if (message.recipientGroup != null)
        'recipient_group': message.recipientGroup,
      'message_content': message.messageContent,
      'message_type': message.messageType.name,
      if (message.attachmentUrl != null)
        'attachment_url': message.attachmentUrl,
      if (message.attachmentUrls != null)
        'attachment_urls': message.attachmentUrls,
      if (message.metadata != null) 'metadata': message.metadata,
    };
  }

  /// Check if message is from current user
  static bool isFromCurrentUser(
    ChatMessageModel message,
    String currentUserId,
  ) {
    return message.senderId == currentUserId;
  }

  /// Check if message is from agent
  static bool isFromAgent(ChatMessageModel message) {
    return message.recipientGroup == 'agent' ||
        message.senderUserType == 'agent';
  }

  /// Check if message has attachments
  static bool hasAttachments(ChatMessageModel message) {
    return message.attachmentUrl != null ||
        (message.attachmentUrls != null && message.attachmentUrls!.isNotEmpty);
  }

  /// Get all attachment URLs
  static List<String> getAllAttachmentUrls(ChatMessageModel message) {
    final urls = <String>[];
    if (message.attachmentUrl != null) urls.add(message.attachmentUrl!);
    if (message.attachmentUrls != null) urls.addAll(message.attachmentUrls!);
    return urls;
  }

  /// Check if message is a system message
  static bool isSystemMessage(ChatMessageModel message) {
    return message.messageType == MessageType.system;
  }

  /// Get display name for sender
  static String getDisplaySenderName(ChatMessageModel message) {
    if (message.senderName != null && message.senderName!.isNotEmpty) {
      return message.senderName!;
    }
    if (isFromAgent(message)) {
      return '3Pay Agent';
    }
    return 'You';
  }

  /// Get sender initials for avatar
  static String getSenderInitials(ChatMessageModel message) {
    final name = getDisplaySenderName(message);
    if (name.isEmpty) return '?';

    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    }
    return name[0].toUpperCase();
  }
}
