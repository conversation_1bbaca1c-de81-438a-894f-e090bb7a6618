class ProfileValidation {
  /// Validate email format
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validate phone number format
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }

    // Remove all non-digit characters for validation
    final digitsOnly = value.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.length < 10) {
      return 'Phone number must be at least 10 digits';
    }

    if (digitsOnly.length > 15) {
      return 'Phone number must be no more than 15 digits';
    }

    return null;
  }

  /// Validate name (first name, last name, or full name)
  static String? validateName(String? value, {String fieldName = 'Name'}) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }

    if (value.trim().length < 2) {
      return '$fieldName must be at least 2 characters';
    }

    if (value.trim().length > 50) {
      return '$fieldName must be no more than 50 characters';
    }

    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    final nameRegex = RegExp(r"^[a-zA-Z\s\-']+$");
    if (!nameRegex.hasMatch(value.trim())) {
      return '$fieldName can only contain letters, spaces, hyphens, and apostrophes';
    }

    return null;
  }

  /// Validate address line
  static String? validateAddressLine(
    String? value, {
    bool required = true,
    String fieldName = 'Address',
  }) {
    if (value == null || value.isEmpty) {
      return required ? '$fieldName is required' : null;
    }

    if (value.trim().length > 100) {
      return '$fieldName must be no more than 100 characters';
    }

    return null;
  }

  /// Validate city
  static String? validateCity(String? value) {
    if (value == null || value.isEmpty) {
      return 'City is required';
    }

    if (value.trim().length < 2) {
      return 'City must be at least 2 characters';
    }

    if (value.trim().length > 50) {
      return 'City must be no more than 50 characters';
    }

    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    final cityRegex = RegExp(r"^[a-zA-Z\s\-']+$");
    if (!cityRegex.hasMatch(value.trim())) {
      return 'City can only contain letters, spaces, hyphens, and apostrophes';
    }

    return null;
  }

  /// Validate postcode (international format)
  static String? validatePostcode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Postcode is required';
    }

    final trimmedValue = value.trim();

    // Basic validation - allow alphanumeric characters, spaces, and hyphens
    if (trimmedValue.length < 3) {
      return 'Postcode must be at least 3 characters';
    }

    if (trimmedValue.length > 12) {
      return 'Postcode must be no more than 12 characters';
    }

    // Allow letters, numbers, spaces, and hyphens
    final postcodeRegex = RegExp(r'^[A-Za-z0-9\s\-]+$');
    if (!postcodeRegex.hasMatch(trimmedValue)) {
      return 'Postcode can only contain letters, numbers, spaces, and hyphens';
    }

    return null;
  }

  /// Validate required field
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// Format phone number for display
  static String formatPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final digitsOnly = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.length == 11 && digitsOnly.startsWith('0')) {
      // UK mobile format: 07XXX XXXXXX
      if (digitsOnly.startsWith('07')) {
        return '${digitsOnly.substring(0, 5)} ${digitsOnly.substring(5)}';
      }
      // UK landline format: 0XXX XXX XXXX
      return '${digitsOnly.substring(0, 4)} ${digitsOnly.substring(4, 7)} ${digitsOnly.substring(7)}';
    }

    // International format or other
    return phoneNumber;
  }

  /// Format postcode for display (basic cleanup)
  static String formatPostcode(String postcode) {
    // Basic formatting - trim whitespace and normalize case
    final trimmed = postcode.trim();

    // For most international postcodes, just return trimmed and uppercase
    // This is a simple approach that works for most countries
    return trimmed.toUpperCase();
  }

  /// Validate image file
  static String? validateImageFile(String? fileName, int? fileSizeBytes) {
    if (fileName == null || fileName.isEmpty) {
      return null; // Optional field
    }

    // Check file extension - only allow JPG and PNG for better compatibility
    final allowedExtensions = ['jpg', 'jpeg', 'png'];
    final extension = fileName.toLowerCase().split('.').last;

    if (!allowedExtensions.contains(extension)) {
      return 'Please select a JPG or PNG image file';
    }

    // Check file size (5MB limit)
    if (fileSizeBytes != null && fileSizeBytes > 5 * 1024 * 1024) {
      return 'Image file must be smaller than 5MB';
    }

    return null;
  }
}
