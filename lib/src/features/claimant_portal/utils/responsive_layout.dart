import 'package:flutter/material.dart';

/// Responsive layout utilities for the claimant portal
/// Provides consistent breakpoints and adaptive layout helpers
class ResponsiveLayout {
  // Breakpoint definitions
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;
  static const double largeDesktopBreakpoint = 1600.0;

  /// Check if current screen is mobile
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  /// Check if current screen is tablet
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }

  /// Check if current screen is desktop
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  /// Check if current screen is large desktop
  static bool isLargeDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= largeDesktopBreakpoint;
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isDesktop(context)) {
      return const EdgeInsets.all(24.0);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(20.0);
    } else {
      return const EdgeInsets.all(16.0);
    }
  }

  /// Get responsive horizontal padding
  static double getHorizontalPadding(BuildContext context) {
    if (isLargeDesktop(context)) {
      return 48.0;
    } else if (isDesktop(context)) {
      return 32.0;
    } else if (isTablet(context)) {
      return 24.0;
    } else {
      return 16.0;
    }
  }

  /// Get responsive vertical padding
  static double getVerticalPadding(BuildContext context) {
    if (isDesktop(context)) {
      return 24.0;
    } else if (isTablet(context)) {
      return 20.0;
    } else {
      return 16.0;
    }
  }

  /// Get responsive spacing between sections
  static double getSectionSpacing(BuildContext context) {
    if (isDesktop(context)) {
      return 32.0;
    } else if (isTablet(context)) {
      return 24.0;
    } else {
      return 20.0;
    }
  }

  /// Get responsive card spacing
  static double getCardSpacing(BuildContext context) {
    if (isDesktop(context)) {
      return 16.0;
    } else if (isTablet(context)) {
      return 12.0;
    } else {
      return 8.0;
    }
  }

  /// Get responsive grid cross axis count
  static int getGridCrossAxisCount(BuildContext context, {int? maxColumns}) {
    final width = MediaQuery.of(context).size.width;

    if (width >= largeDesktopBreakpoint) {
      return maxColumns ?? 4;
    } else if (width >= desktopBreakpoint) {
      return maxColumns != null ? (maxColumns > 3 ? 3 : maxColumns) : 3;
    } else if (width >= tabletBreakpoint) {
      return maxColumns != null ? (maxColumns > 2 ? 2 : maxColumns) : 2;
    } else {
      return 1;
    }
  }

  /// Get responsive grid child aspect ratio
  static double getGridChildAspectRatio(BuildContext context) {
    if (isDesktop(context)) {
      return 1.3;
    } else if (isTablet(context)) {
      return 1.2;
    } else {
      return 1.1;
    }
  }

  /// Get responsive font size multiplier
  static double getFontSizeMultiplier(BuildContext context) {
    if (isDesktop(context)) {
      return 1.1;
    } else if (isTablet(context)) {
      return 1.05;
    } else {
      return 1.0;
    }
  }

  /// Get responsive icon size
  static double getIconSize(BuildContext context, {double baseSize = 24.0}) {
    final multiplier = getFontSizeMultiplier(context);
    return baseSize * multiplier;
  }

  /// Get responsive button height
  static double getButtonHeight(BuildContext context) {
    if (isDesktop(context)) {
      return 48.0;
    } else if (isTablet(context)) {
      return 44.0;
    } else {
      return 40.0;
    }
  }

  /// Get responsive app bar height
  static double getAppBarHeight(BuildContext context) {
    if (isDesktop(context)) {
      return 64.0;
    } else {
      return 56.0;
    }
  }
}

/// Responsive grid widget that adapts to screen size
class ResponsiveGrid extends StatelessWidget {
  const ResponsiveGrid({
    super.key,
    required this.children,
    this.maxColumns,
    this.spacing,
    this.runSpacing,
    this.childAspectRatio,
    this.physics,
    this.shrinkWrap = false,
    this.padding,
  });

  final List<Widget> children;
  final int? maxColumns;
  final double? spacing;
  final double? runSpacing;
  final double? childAspectRatio;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    final crossAxisCount = ResponsiveLayout.getGridCrossAxisCount(
      context,
      maxColumns: maxColumns,
    );

    final effectiveSpacing =
        spacing ?? ResponsiveLayout.getCardSpacing(context);
    final effectiveAspectRatio =
        childAspectRatio ?? ResponsiveLayout.getGridChildAspectRatio(context);

    return GridView.builder(
      padding: padding ?? ResponsiveLayout.getResponsivePadding(context),
      physics: physics,
      shrinkWrap: shrinkWrap,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: effectiveSpacing,
        mainAxisSpacing: effectiveSpacing,
        childAspectRatio: effectiveAspectRatio,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}

/// Responsive wrap widget that adapts spacing to screen size
class ResponsiveWrap extends StatelessWidget {
  const ResponsiveWrap({
    super.key,
    required this.children,
    this.direction = Axis.horizontal,
    this.alignment = WrapAlignment.start,
    this.spacing,
    this.runSpacing,
    this.runAlignment = WrapAlignment.start,
    this.crossAxisAlignment = WrapCrossAlignment.start,
    this.textDirection,
    this.verticalDirection = VerticalDirection.down,
    this.clipBehavior = Clip.none,
  });

  final List<Widget> children;
  final Axis direction;
  final WrapAlignment alignment;
  final double? spacing;
  final double? runSpacing;
  final WrapAlignment runAlignment;
  final WrapCrossAlignment crossAxisAlignment;
  final TextDirection? textDirection;
  final VerticalDirection verticalDirection;
  final Clip clipBehavior;

  @override
  Widget build(BuildContext context) {
    final effectiveSpacing =
        spacing ?? ResponsiveLayout.getCardSpacing(context);
    final effectiveRunSpacing =
        runSpacing ?? ResponsiveLayout.getCardSpacing(context);

    return Wrap(
      direction: direction,
      alignment: alignment,
      spacing: effectiveSpacing,
      runSpacing: effectiveRunSpacing,
      runAlignment: runAlignment,
      crossAxisAlignment: crossAxisAlignment,
      textDirection: textDirection,
      verticalDirection: verticalDirection,
      clipBehavior: clipBehavior,
      children: children,
    );
  }
}

/// Responsive column widget that adapts spacing to screen size
class ResponsiveColumn extends StatelessWidget {
  const ResponsiveColumn({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.textDirection,
    this.verticalDirection = VerticalDirection.down,
    this.textBaseline,
    this.spacing,
  });

  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final TextDirection? textDirection;
  final VerticalDirection verticalDirection;
  final TextBaseline? textBaseline;
  final double? spacing;

  @override
  Widget build(BuildContext context) {
    final effectiveSpacing =
        spacing ?? ResponsiveLayout.getSectionSpacing(context);

    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1) {
        spacedChildren.add(SizedBox(height: effectiveSpacing));
      }
    }

    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      textDirection: textDirection,
      verticalDirection: verticalDirection,
      textBaseline: textBaseline,
      children: spacedChildren,
    );
  }
}

/// Responsive row widget that adapts spacing to screen size
class ResponsiveRow extends StatelessWidget {
  const ResponsiveRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.textDirection,
    this.verticalDirection = VerticalDirection.down,
    this.textBaseline,
    this.spacing,
  });

  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final TextDirection? textDirection;
  final VerticalDirection verticalDirection;
  final TextBaseline? textBaseline;
  final double? spacing;

  @override
  Widget build(BuildContext context) {
    final effectiveSpacing =
        spacing ?? ResponsiveLayout.getCardSpacing(context);

    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1) {
        spacedChildren.add(SizedBox(width: effectiveSpacing));
      }
    }

    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      textDirection: textDirection,
      verticalDirection: verticalDirection,
      textBaseline: textBaseline,
      children: spacedChildren,
    );
  }
}
