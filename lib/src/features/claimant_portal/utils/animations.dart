import 'package:flutter/material.dart';

/// Animation utilities for the claimant portal
/// Provides consistent animations and transitions across the app
class ClaimantAnimations {
  // Animation durations
  static const Duration fastDuration = Duration(milliseconds: 200);
  static const Duration normalDuration = Duration(milliseconds: 300);
  static const Duration slowDuration = Duration(milliseconds: 500);

  // Animation curves
  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve bounceCurve = Curves.elasticOut;
  static const Curve slideCurve = Curves.easeOutCubic;

  /// Fade in animation
  static Widget fadeIn({
    required Widget child,
    Duration duration = normalDuration,
    Curve curve = defaultCurve,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      curve: curve,
      tween: Tween(begin: begin, end: end),
      builder: (context, value, child) {
        return Opacity(opacity: value, child: child);
      },
      child: child,
    );
  }

  /// Slide in animation from direction
  static Widget slideIn({
    required Widget child,
    Duration duration = normalDuration,
    Curve curve = slideCurve,
    SlideDirection direction = SlideDirection.bottom,
    double distance = 50.0,
  }) {
    late Offset begin;
    const Offset end = Offset.zero;

    switch (direction) {
      case SlideDirection.top:
        begin = Offset(0, -distance / 100);
        break;
      case SlideDirection.bottom:
        begin = Offset(0, distance / 100);
        break;
      case SlideDirection.left:
        begin = Offset(-distance / 100, 0);
        break;
      case SlideDirection.right:
        begin = Offset(distance / 100, 0);
        break;
    }

    return TweenAnimationBuilder<Offset>(
      duration: duration,
      curve: curve,
      tween: Tween(begin: begin, end: end),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(
            value.dx * MediaQuery.of(context).size.width,
            value.dy * MediaQuery.of(context).size.height,
          ),
          child: child,
        );
      },
      child: child,
    );
  }

  /// Scale animation
  static Widget scaleIn({
    required Widget child,
    Duration duration = normalDuration,
    Curve curve = bounceCurve,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      curve: curve,
      tween: Tween(begin: begin, end: end),
      builder: (context, value, child) {
        return Transform.scale(scale: value, child: child);
      },
      child: child,
    );
  }

  /// Staggered list animation
  static Widget staggeredList({
    required List<Widget> children,
    Duration staggerDelay = const Duration(milliseconds: 100),
    Duration itemDuration = normalDuration,
    Curve curve = defaultCurve,
    SlideDirection direction = SlideDirection.bottom,
  }) {
    return Column(
      children:
          children.asMap().entries.map((entry) {
            final index = entry.key;
            final child = entry.value;

            return AnimatedContainer(
              duration: Duration(
                milliseconds: staggerDelay.inMilliseconds * index,
              ),
              curve: curve,
              child: slideIn(
                duration: itemDuration,
                curve: curve,
                direction: direction,
                child: fadeIn(
                  duration: itemDuration,
                  curve: curve,
                  child: child,
                ),
              ),
            );
          }).toList(),
    );
  }

  /// Page transition animation
  static PageRouteBuilder<T> pageTransition<T>({
    required Widget page,
    PageTransitionType type = PageTransitionType.slideFromRight,
    Duration duration = normalDuration,
    Curve curve = defaultCurve,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        switch (type) {
          case PageTransitionType.fade:
            return FadeTransition(opacity: animation, child: child);

          case PageTransitionType.slideFromRight:
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(parent: animation, curve: curve)),
              child: child,
            );

          case PageTransitionType.slideFromLeft:
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(-1.0, 0.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(parent: animation, curve: curve)),
              child: child,
            );

          case PageTransitionType.slideFromBottom:
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, 1.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(parent: animation, curve: curve)),
              child: child,
            );

          case PageTransitionType.scale:
            return ScaleTransition(scale: animation, child: child);
        }
      },
    );
  }

  /// Shimmer loading animation
  static Widget shimmer({
    required Widget child,
    Color? baseColor,
    Color? highlightColor,
    Duration duration = const Duration(milliseconds: 1500),
  }) {
    return AnimatedBuilder(
      animation: AnimationController(
        duration: duration,
        vsync: Navigator.of(child as BuildContext),
      )..repeat(),
      builder: (context, _) {
        return ShaderMask(
          blendMode: BlendMode.srcATop,
          shaderCallback: (bounds) {
            return LinearGradient(
              colors: [
                baseColor ?? Colors.grey[300]!,
                highlightColor ?? Colors.grey[100]!,
                baseColor ?? Colors.grey[300]!,
              ],
              stops: const [0.1, 0.3, 0.4],
              begin: const Alignment(-1.0, -0.3),
              end: const Alignment(1.0, 0.3),
              tileMode: TileMode.clamp,
            ).createShader(bounds);
          },
          child: child,
        );
      },
    );
  }

  /// Bounce animation for buttons
  static Widget bounceOnTap({
    required Widget child,
    VoidCallback? onTap,
    double scale = 0.95,
    Duration duration = const Duration(milliseconds: 150),
  }) {
    return _BounceWidget(
      onTap: onTap,
      scale: scale,
      duration: duration,
      child: child,
    );
  }

  /// Pulse animation for notifications
  static Widget pulse({
    required Widget child,
    Duration duration = const Duration(milliseconds: 1000),
    double minScale = 0.95,
    double maxScale = 1.05,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween(begin: minScale, end: maxScale),
      builder: (context, value, child) {
        return Transform.scale(scale: value, child: child);
      },
      onEnd: () {
        // Reverse the animation
      },
      child: child,
    );
  }

  /// Loading dots animation
  static Widget loadingDots({
    Color? color,
    double size = 8.0,
    Duration duration = const Duration(milliseconds: 1200),
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: AnimationController(
            duration: duration,
            vsync: Navigator.of(color as BuildContext),
          )..repeat(),
          builder: (context, _) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: size * 0.2),
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: color ?? Colors.grey,
                shape: BoxShape.circle,
              ),
            );
          },
        );
      }),
    );
  }

  /// Card flip animation
  static Widget flipCard({
    required Widget front,
    required Widget back,
    bool isFlipped = false,
    Duration duration = normalDuration,
  }) {
    return AnimatedSwitcher(
      duration: duration,
      transitionBuilder: (child, animation) {
        final rotate = Tween(begin: 0.0, end: 1.0).animate(animation);
        return AnimatedBuilder(
          animation: rotate,
          child: child,
          builder: (context, child) {
            final isShowingFront = rotate.value < 0.5;
            return Transform(
              alignment: Alignment.center,
              transform:
                  Matrix4.identity()
                    ..setEntry(3, 2, 0.001)
                    ..rotateY(rotate.value * 3.14159),
              child: isShowingFront ? front : back,
            );
          },
        );
      },
      child: isFlipped ? back : front,
    );
  }
}

/// Slide direction enum for animations
enum SlideDirection { top, bottom, left, right }

/// Page transition type enum
enum PageTransitionType {
  fade,
  slideFromRight,
  slideFromLeft,
  slideFromBottom,
  scale,
}

/// Private widget for bounce animation
class _BounceWidget extends StatefulWidget {
  const _BounceWidget({
    required this.child,
    this.onTap,
    this.scale = 0.95,
    this.duration = const Duration(milliseconds: 150),
  });

  final Widget child;
  final VoidCallback? onTap;
  final double scale;
  final Duration duration;

  @override
  State<_BounceWidget> createState() => _BounceWidgetState();
}

class _BounceWidgetState extends State<_BounceWidget> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      onTap: widget.onTap,
      child: AnimatedScale(
        scale: _isPressed ? widget.scale : 1.0,
        duration: widget.duration,
        curve: Curves.easeInOut,
        child: widget.child,
      ),
    );
  }
}
