import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:pocketbase/pocketbase.dart'; // For PocketBase types
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';

part 'cofounder_progress_event.dart';
part 'cofounder_progress_state.dart';

class CofounderProgressBloc extends Bloc<CofounderProgressEvent, CofounderProgressState> {
  final PocketBaseService _pbService;

  CofounderProgressBloc(this._pbService) : super(const CofounderProgressInitial()) {
    on<LoadCofounderProgress>(_onLoadCofounderProgress);
  }

  Future<void> _onLoadCofounderProgress(
    LoadCofounderProgress event,
    Emitter<CofounderProgressState> emit,
  ) async {
    emit(const CofounderProgressLoading());
    final userId = _pbService.currentUser?.id;

    if (userId == null) {
      emit(const CofounderProgressError("User not logged in."));
      return;
    }

    try {
      // Fetch the co_funder_profile record associated with the current user
      // Expand 'read_educational_content' to get the related records (or just their count)
      final profileRecord = await _pbService.client
          .collection('co_funder_profiles')
          .getFirstListItem(
            'user_id="$userId"',
            // Expand the relation field to get the actual related records
            // If you only need the count, PocketBase might offer ways to get that directly,
            // but expanding and counting is a common approach.
            expand: 'read_educational_content',
          );

      // Safely access the expanded data and count the read articles
      final List<dynamic> readArticlesData = profileRecord.expand['read_educational_content'] ?? [];
      final int readArticlesCount = readArticlesData.length;

      // Safely access other fields from the profile record
      final int currentLevel = profileRecord.data['current_level'] as int? ?? 0;
      final bool upgradeRequested = profileRecord.data['upgrade_request_submitted'] as bool? ?? false;
      // final bool subscriptionActive = profileRecord.data['subscription_active'] as bool? ?? false; // Old way
      // final bool ndaSigned = profileRecord.data['nda_signed'] as bool? ?? false; // Old way of checking NDA
      final String ndaSignedAt = profileRecord.data['nda_signed_at'] as String? ?? ''; // Fetch nda_signed_at timestamp
      final bool hasSignedNDA = ndaSignedAt.isNotEmpty; // Determine if NDA is signed based on timestamp presence

      final bool level3Approved = profileRecord.data['level_3_approved'] as bool? ?? false; // Fetch level_3_approved status
      // final bool level4Approved = profileRecord.data['level_4_approved'] as bool? ?? false; // No longer directly used for hasActivatedSubscription
      // final String subscriptionStatus = profileRecord.data['subscription_status'] as String? ?? ''; // No longer directly used for hasActivatedSubscription
      final bool level4Subscribed = profileRecord.data['level_4_subscribed'] as bool? ?? false; // Fetch level_4_subscribed status
      // final bool knowledgeTestPassed = profileRecord.data['knowledge_test_passed'] as bool? ?? false; // Old way: Fetch knowledge_test_passed status

      // Check for a passed knowledge test attempt
      bool hasPassedKnowledgeTest = false;
      try {
        await _pbService.client
            .collection('knowledge_test_attempts')
            .getFirstListItem('co_funder_profile_id="${profileRecord.id}" && passed=true');
        hasPassedKnowledgeTest = true;
      } on ClientException catch (e) {
        if (e.statusCode == 404) {
          // No passed test attempt found, which is fine.
          hasPassedKnowledgeTest = false;
        } else {
          // Rethrow other client exceptions
          rethrow;
        }
      }

      // Determine hasActivatedSubscription based on new logic
      // final bool newHasActivatedSubscription = (level4Approved) && (subscriptionStatus == 'Successful'); // Old logic

      // Create the progress object
      final progress = CofounderProgress(
        hasReadAllArticles: readArticlesCount >= 22, // Check against the threshold
        hasSubmittedUpgradeRequest: upgradeRequested,
        hasActivatedSubscription: level4Subscribed, // Use level_4_subscribed directly
        hasSignedNDA: hasSignedNDA, // Use new logic based on nda_signed_at
        isEligibleForClaims: hasPassedKnowledgeTest, // Use status from knowledge_test_attempts
        hasLevel3Approved: level3Approved, // Pass the fetched status
      );

      emit(CofounderProgressLoaded(progress));

    } on ClientException catch (e) {
      // Handle PocketBase specific errors (e.g., record not found, network issues)
      // ignore: avoid_print
      print("PocketBase Error in CofounderProgressBloc: ${e.statusCode} ${e.response}");
      emit(CofounderProgressError("Failed to load progress: ${e.response['message'] ?? e.toString()}"));
    } catch (e) {
      // Handle other potential errors during data processing
      // ignore: avoid_print
      print("Error in CofounderProgressBloc fetching progress: $e");
      emit(CofounderProgressError("An unexpected error occurred: ${e.toString()}"));
    }
  }
}