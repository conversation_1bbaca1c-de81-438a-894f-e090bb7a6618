part of 'cofounder_progress_bloc.dart';

// Data Model for Progress
@immutable
class CofounderProgress {
  final bool hasReadAllArticles;
  final bool hasSubmittedUpgradeRequest;
  final bool hasActivatedSubscription;
  final bool hasSignedNDA;
  final bool isEligibleForClaims; // True if level is 4 or higher
  final bool hasLevel3Approved; // Represents level_3_approved status from DB

  const CofounderProgress({
    required this.hasReadAllArticles,
    required this.hasSubmittedUpgradeRequest,
    required this.hasActivatedSubscription,
    required this.hasSignedNDA,
    required this.isEligibleForClaims,
    required this.hasLevel3Approved,
  });

  // Optional: Add copyWith, equality, and toString for better state management
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CofounderProgress &&
          runtimeType == other.runtimeType &&
          hasReadAllArticles == other.hasReadAllArticles &&
          hasSubmittedUpgradeRequest == other.hasSubmittedUpgradeRequest &&
          hasActivatedSubscription == other.hasActivatedSubscription &&
          hasSignedNDA == other.hasSignedNDA &&
          isEligibleForClaims == other.isEligibleForClaims &&
          hasLevel3Approved == other.hasLevel3Approved;

  @override
  int get hashCode =>
      hasReadAllArticles.hashCode ^
      hasSubmittedUpgradeRequest.hashCode ^
      hasActivatedSubscription.hashCode ^
      hasSignedNDA.hashCode ^
      isEligibleForClaims.hashCode ^
      hasLevel3Approved.hashCode;

  @override
  String toString() {
    return 'CofounderProgress{hasReadAllArticles: $hasReadAllArticles, hasSubmittedUpgradeRequest: $hasSubmittedUpgradeRequest, hasActivatedSubscription: $hasActivatedSubscription, hasSignedNDA: $hasSignedNDA, isEligibleForClaims: $isEligibleForClaims, hasLevel3Approved: $hasLevel3Approved}';
  }
}


// State Definitions
@immutable
abstract class CofounderProgressState {
  const CofounderProgressState();
}

class CofounderProgressInitial extends CofounderProgressState {
  const CofounderProgressInitial();
}

class CofounderProgressLoading extends CofounderProgressState {
  const CofounderProgressLoading();
}

class CofounderProgressLoaded extends CofounderProgressState {
  final CofounderProgress progress;
  const CofounderProgressLoaded(this.progress);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CofounderProgressLoaded &&
          runtimeType == other.runtimeType &&
          progress == other.progress;

  @override
  int get hashCode => progress.hashCode;

   @override
  String toString() => 'CofounderProgressLoaded { progress: $progress }';
}

class CofounderProgressError extends CofounderProgressState {
  final String message;
  const CofounderProgressError(this.message);

   @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CofounderProgressError &&
          runtimeType == other.runtimeType &&
          message == other.message;

  @override
  int get hashCode => message.hashCode;

  @override
  String toString() => 'CofounderProgressError { message: $message }';
}