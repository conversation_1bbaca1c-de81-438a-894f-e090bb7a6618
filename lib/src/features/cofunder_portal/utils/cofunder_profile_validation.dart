/// Validation utilities for co-funder profile fields
class CoFunderProfileValidation {
  /// Validate funding capacity
  static String? validateFundingCapacity(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Funding capacity is optional
    }

    final trimmedValue = value.trim();
    final doubleValue = double.tryParse(trimmedValue);

    if (doubleValue == null) {
      return 'Please enter a valid number';
    }

    if (doubleValue < 0) {
      return 'Funding capacity cannot be negative';
    }

    if (doubleValue > 100000000) {
      return 'Funding capacity cannot exceed £100,000,000';
    }

    return null;
  }

  /// Validate risk tolerance
  static String? validateRiskTolerance(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Risk tolerance is optional
    }

    final validRiskLevels = ['Low', 'Medium', 'High'];
    if (!validRiskLevels.contains(value)) {
      return 'Please select a valid risk tolerance level';
    }

    return null;
  }

  /// Validate preferred sectors
  static String? validatePreferredSectors(List<String>? sectors) {
    if (sectors == null || sectors.isEmpty) {
      return null; // Preferred sectors are optional
    }

    if (sectors.length > 10) {
      return 'Please select no more than 10 preferred sectors';
    }

    return null;
  }

  /// Validate investment preferences
  static String? validateInvestmentPreferences(
    Map<String, dynamic>? preferences,
  ) {
    if (preferences == null || preferences.isEmpty) {
      return null; // Investment preferences are optional
    }

    // Validate minimum investment amount
    if (preferences.containsKey('minimum_investment')) {
      final minInvestment = preferences['minimum_investment'];
      if (minInvestment != null) {
        final doubleValue = double.tryParse(minInvestment.toString());
        if (doubleValue == null || doubleValue < 0) {
          return 'Minimum investment must be a valid positive number';
        }
      }
    }

    // Validate maximum investment amount
    if (preferences.containsKey('maximum_investment')) {
      final maxInvestment = preferences['maximum_investment'];
      if (maxInvestment != null) {
        final doubleValue = double.tryParse(maxInvestment.toString());
        if (doubleValue == null || doubleValue < 0) {
          return 'Maximum investment must be a valid positive number';
        }
      }
    }

    return null;
  }

  /// Validate email format (reused from claimant validation)
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validate phone number
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Phone number is optional
    }

    final trimmedValue = value.trim();

    // Basic phone number validation
    if (trimmedValue.length < 10) {
      return 'Phone number must be at least 10 digits';
    }

    if (trimmedValue.length > 20) {
      return 'Phone number must be no more than 20 characters';
    }

    // Allow digits, spaces, hyphens, parentheses, and plus sign
    final phoneRegex = RegExp(r'^[\d\s\-\(\)\+]+$');
    if (!phoneRegex.hasMatch(trimmedValue)) {
      return 'Phone number can only contain digits, spaces, hyphens, parentheses, and plus sign';
    }

    return null;
  }

  /// Validate name (for user profile fields)
  static String? validateName(String? value, {String fieldName = 'Name'}) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }

    if (value.trim().length < 2) {
      return '$fieldName must be at least 2 characters';
    }

    if (value.trim().length > 50) {
      return '$fieldName must be no more than 50 characters';
    }

    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    final nameRegex = RegExp(r"^[a-zA-Z\s\-']+$");
    if (!nameRegex.hasMatch(value.trim())) {
      return '$fieldName can only contain letters, spaces, hyphens, and apostrophes';
    }

    return null;
  }

  /// Validate address line
  static String? validateAddressLine(
    String? value, {
    bool required = true,
    String fieldName = 'Address',
  }) {
    if (value == null || value.isEmpty) {
      return required ? '$fieldName is required' : null;
    }

    if (value.trim().length > 100) {
      return '$fieldName must be no more than 100 characters';
    }

    return null;
  }

  /// Validate city
  static String? validateCity(String? value) {
    if (value == null || value.isEmpty) {
      return 'City is required';
    }

    if (value.trim().length < 2) {
      return 'City must be at least 2 characters';
    }

    if (value.trim().length > 50) {
      return 'City must be no more than 50 characters';
    }

    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    final cityRegex = RegExp(r"^[a-zA-Z\s\-']+$");
    if (!cityRegex.hasMatch(value.trim())) {
      return 'City can only contain letters, spaces, hyphens, and apostrophes';
    }

    return null;
  }

  /// Validate postcode (international format)
  static String? validatePostcode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Postcode is required';
    }

    final trimmedValue = value.trim();

    // Basic validation - allow alphanumeric characters, spaces, and hyphens
    if (trimmedValue.length < 3) {
      return 'Postcode must be at least 3 characters';
    }

    if (trimmedValue.length > 12) {
      return 'Postcode must be no more than 12 characters';
    }

    // Allow letters, numbers, spaces, and hyphens
    final postcodeRegex = RegExp(r'^[A-Za-z0-9\s\-]+$');
    if (!postcodeRegex.hasMatch(trimmedValue)) {
      return 'Postcode can only contain letters, numbers, spaces, and hyphens';
    }

    return null;
  }

  /// Get available risk tolerance options
  static List<String> getRiskToleranceOptions() {
    return ['Low', 'Medium', 'High'];
  }

  /// Get available sector options
  static List<String> getAvailableSectors() {
    return [
      'Technology',
      'Healthcare',
      'Finance',
      'Real Estate',
      'Energy',
      'Manufacturing',
      'Retail',
      'Transportation',
      'Education',
      'Entertainment',
      'Agriculture',
      'Construction',
      'Telecommunications',
      'Pharmaceuticals',
      'Automotive',
      'Aerospace',
      'Food & Beverage',
      'Textiles',
      'Mining',
      'Other',
    ];
  }

  /// Validate notification preferences
  static String? validateNotificationPreferences(
    Map<String, dynamic>? preferences,
  ) {
    if (preferences == null) {
      return null; // Notification preferences are optional
    }

    // Basic validation - ensure all values are boolean
    for (final entry in preferences.entries) {
      if (entry.value is! bool) {
        return 'Invalid notification preference format';
      }
    }

    return null;
  }

  /// Validate image file
  static String? validateImageFile(String? fileName, int? fileSizeBytes) {
    if (fileName == null || fileName.isEmpty) {
      return null; // Optional field
    }

    // Check file extension - only allow JPG and PNG for better compatibility
    final allowedExtensions = ['jpg', 'jpeg', 'png'];
    final extension = fileName.toLowerCase().split('.').last;

    if (!allowedExtensions.contains(extension)) {
      return 'Please select a JPG or PNG image file';
    }

    // Check file size (5MB limit)
    if (fileSizeBytes != null && fileSizeBytes > 5 * 1024 * 1024) {
      return 'Image file must be smaller than 5MB';
    }

    return null;
  }
}
