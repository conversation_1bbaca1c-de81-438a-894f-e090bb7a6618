import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/document_preview_widget.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/empty_state_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/services/claim_documents_service.dart';
import 'package:url_launcher/url_launcher.dart';

/// Widget to display and manage claim documents for co-funders
class CoFunderDocumentsWidget extends ConsumerWidget {
  final String claimId;
  final List<UploadedDocumentCategory> documents;
  final bool isLoading;
  final String? errorMessage;

  const CoFunderDocumentsWidget({
    super.key,
    required this.claimId,
    required this.documents,
    this.isLoading = false,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Documents Header
          _buildDocumentsHeader(context, theme, documents.length),

          const SizedBox(height: 24),

          // Documents List
          if (isLoading)
            _buildLoadingState(context, theme)
          else if (errorMessage != null)
            _buildErrorState(context, theme)
          else if (documents.isEmpty)
            _buildEmptyState(context, theme)
          else
            _buildDocumentsList(context, theme, documents),
        ],
      ),
    );
  }

  Widget _buildDocumentsHeader(
    BuildContext context,
    ShadThemeData theme,
    int documentCount,
  ) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.folder_open,
                size: 24,
                color: theme.colorScheme.primary,
              ),
            ),

            const SizedBox(width: 16),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Claim Documents',
                    style: theme.textTheme.h4.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '$documentCount ${documentCount == 1 ? 'document' : 'documents'} available',
                    style: theme.textTheme.p.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),

            ShadButton.outline(
              onPressed: () => _showDocumentInfo(context),
              size: ShadButtonSize.sm,
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.info_outline, size: 16),
                  SizedBox(width: 6),
                  Text('Info'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context, ShadThemeData theme) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading documents...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, ShadThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: theme.colorScheme.destructive,
          ),
          const SizedBox(height: 16),
          Text(
            errorMessage ?? 'Failed to load documents',
            style: theme.textTheme.p.copyWith(
              color: theme.colorScheme.destructive,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, ShadThemeData theme) {
    return EmptyStateWidget(
      icon: Icons.folder_open,
      message: 'No documents available for this claim yet.',
    );
  }

  Widget _buildDocumentsList(
    BuildContext context,
    ShadThemeData theme,
    List<UploadedDocumentCategory> documents,
  ) {
    return Column(
      children:
          documents
              .map((doc) => _buildDocumentRow(context, theme, doc))
              .toList(),
    );
  }

  Widget _buildDocumentRow(
    BuildContext context,
    ShadThemeData theme,
    UploadedDocumentCategory doc,
  ) {
    // Get the current version of the document
    final currentVersion =
        doc.versions.isNotEmpty
            ? doc.versions.firstWhere(
              (v) => v.fileId == doc.currentVersionFileId,
              orElse: () => doc.versions.last,
            )
            : null;

    if (currentVersion == null) {
      return Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: theme.colorScheme.muted.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.error_outline,
              color: theme.colorScheme.mutedForeground,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                '${doc.logicalName} - No version available',
                style: theme.textTheme.p.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Get file extension for icon
    final extension = currentVersion.filename.split('.').last.toLowerCase();
    IconData fileIcon = Icons.description;
    Color iconColor = Colors.blue;

    switch (extension) {
      case 'pdf':
        fileIcon = Icons.picture_as_pdf;
        iconColor = Colors.red;
        break;
      case 'doc':
      case 'docx':
        fileIcon = Icons.description;
        iconColor = Colors.blue;
        break;
      case 'xls':
      case 'xlsx':
        fileIcon = Icons.table_chart;
        iconColor = Colors.green;
        break;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        fileIcon = Icons.image;
        iconColor = Colors.purple;
        break;
      default:
        fileIcon = Icons.insert_drive_file;
        iconColor = Colors.grey;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(fileIcon, color: iconColor, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  doc.logicalName,
                  style: theme.textTheme.p.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  currentVersion.filename,
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
                if (currentVersion.notes != null &&
                    currentVersion.notes!.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    currentVersion.notes!,
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ShadButton.outline(
                size: ShadButtonSize.sm,
                child: const Icon(Icons.visibility, size: 16),
                onPressed: () => _previewDocument(context, currentVersion),
              ),
              const SizedBox(width: 8),
              ShadButton.outline(
                size: ShadButtonSize.sm,
                child: const Icon(Icons.download, size: 16),
                onPressed: () => _downloadDocument(context, currentVersion),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _previewDocument(
    BuildContext context,
    DocumentVersion version,
  ) async {
    try {
      // Get the file URL using the ClaimDocumentsService
      final claimDocumentsService = ClaimDocumentsService();
      final fileUrl = await claimDocumentsService.getFileUrl(version.fileId);

      // Navigate to document preview screen
      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => DocumentPreviewWidget(
                  url: fileUrl,
                  fileName: version.filename,
                ),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Preview Error'),
            description: Text('Failed to preview document: ${e.toString()}'),
          ),
        );
      }
    }
  }

  Future<void> _downloadDocument(
    BuildContext context,
    DocumentVersion version,
  ) async {
    try {
      // Get the file URL using the ClaimDocumentsService
      final claimDocumentsService = ClaimDocumentsService();
      final fileUrl = await claimDocumentsService.getFileUrl(version.fileId);

      // Show success toast
      if (context.mounted) {
        ShadToaster.of(context).show(
          ShadToast(
            title: const Text('Download'),
            description: Text('Downloading ${version.filename}...'),
          ),
        );
      }

      // Launch the URL to download the file
      final uri = Uri.parse(fileUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw Exception('Could not launch download URL');
      }
    } catch (e) {
      if (context.mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Download Error'),
            description: Text(
              'Failed to download ${version.filename}: ${e.toString()}',
            ),
          ),
        );
      }
    }
  }

  void _showDocumentInfo(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Document Information'),
            content: const Text(
              'This section contains all documents related to this claim. You can preview supported file types (PDF, images) within the app or download them to your device. Some documents may be restricted based on your access level.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }
}
