import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_podcast_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/utils/responsive_layout.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/background_audio_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/service_locator.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

class EnhancedPodcastPlayerWidget extends ConsumerStatefulWidget {
  final CoFunderPodcastModel podcast;
  final String audioUrl;
  final bool showDownloadButton;
  final bool isCompact;
  final VoidCallback? onPlayCountIncrement;

  const EnhancedPodcastPlayerWidget({
    super.key,
    required this.podcast,
    required this.audioUrl,
    this.showDownloadButton = true,
    this.isCompact = false,
    this.onPlayCountIncrement,
  });

  @override
  ConsumerState<EnhancedPodcastPlayerWidget> createState() =>
      _EnhancedPodcastPlayerWidgetState();
}

class _EnhancedPodcastPlayerWidgetState
    extends ConsumerState<EnhancedPodcastPlayerWidget> {
  bool _isDownloading = false;
  late final BackgroundAudioService _audioService;

  @override
  void initState() {
    super.initState();
    _audioService = ServiceLocator.backgroundAudioService;
  }

  Future<void> _playPause() async {
    if (!mounted) return;

    if (widget.audioUrl.isEmpty) {
      _showErrorToast('Audio file not available');
      return;
    }

    try {
      if (!mounted) return;

      // Safely read providers with mounted check
      late final AsyncValue<CoFunderPodcastModel?> currentPodcastAsync;
      late final AsyncValue<AudioPlaybackState> playbackStateAsync;

      try {
        if (!mounted) return;
        currentPodcastAsync = ref.read(currentPodcastStreamProvider);
        playbackStateAsync = ref.read(audioPlaybackStateProvider);
      } catch (e) {
        // Widget was disposed while reading providers
        return;
      }

      final currentPodcast = currentPodcastAsync.valueOrNull;
      final playbackState =
          playbackStateAsync.valueOrNull ?? const AudioPlaybackState();

      // If this is the currently playing podcast
      if (currentPodcast?.id == widget.podcast.id) {
        if (playbackState.isPlaying) {
          await _audioService.pause();
        } else {
          await _audioService.play();
        }
      } else {
        // Start playing this podcast
        await _audioService.playPodcast(widget.podcast, widget.audioUrl);

        // Update the current podcast in the provider only if still mounted
        if (mounted) {
          try {
            ref.read(currentPlayingPodcastProvider.notifier).state =
                widget.podcast;
          } catch (e) {
            // Widget was disposed while updating provider
            // This is fine, just ignore
          }
        }

        // Call the play count increment callback
        widget.onPlayCountIncrement?.call();
      }
    } catch (e) {
      // Only log error if widget is still mounted
      if (mounted) {
        LoggerService.error('Error in play/pause', e);
        _showErrorToast('Error playing audio: ${e.toString()}');
      }
    }
  }

  Future<void> _stop() async {
    await _audioService.stop();
    if (mounted) {
      ref.read(currentPlayingPodcastProvider.notifier).state = null;
    }
  }

  Future<void> _seek(Duration position) async {
    await _audioService.seek(position);
  }

  Future<void> _setVolume(double volume) async {
    await _audioService.setVolume(volume);
  }

  void _skipForward() {
    _audioService.skipForward();
  }

  void _skipBackward() {
    _audioService.skipBackward();
  }

  void _showErrorToast(String message) {
    if (mounted) {
      ShadToaster.of(context).show(
        ShadToast(
          description: Text(message),
          action: ShadButton.outline(
            onPressed: () => ShadToaster.of(context).hide(),
            child: const Text('Dismiss'),
          ),
        ),
      );
    }
  }

  void _showSuccessToast(String message) {
    if (mounted) {
      ShadToaster.of(context).show(
        ShadToast(
          description: Text(message),
          action: ShadButton.outline(
            onPressed: () => ShadToaster.of(context).hide(),
            child: const Text('Dismiss'),
          ),
        ),
      );
    }
  }

  Future<void> _downloadPodcast() async {
    if (_isDownloading) return;

    setState(() => _isDownloading = true);

    try {
      final response = await http.get(Uri.parse(widget.audioUrl));
      if (response.statusCode == 200) {
        final directory = await getApplicationDocumentsDirectory();
        final fileName =
            '${widget.podcast.contentItemSlug}_episode_${widget.podcast.episodeNumber ?? 'unknown'}.mp3';
        final file = File('${directory.path}/$fileName');

        await file.writeAsBytes(response.bodyBytes);

        _showSuccessToast('Podcast downloaded successfully');
      } else {
        _showErrorToast('Failed to download podcast');
      }
    } catch (e) {
      _showErrorToast('Download error: ${e.toString()}');
    } finally {
      setState(() => _isDownloading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isDesktop = CoFunderResponsiveLayout.isDesktop(context);

    // Watch the current playing podcast and playback state
    final currentPodcastAsync = ref.watch(currentPodcastStreamProvider);
    final playbackStateAsync = ref.watch(audioPlaybackStateProvider);

    // Get current values or defaults
    final currentPodcast = currentPodcastAsync.valueOrNull;
    final playbackState =
        playbackStateAsync.valueOrNull ?? const AudioPlaybackState();

    // Check if this widget's podcast is currently playing
    final isCurrentPodcast = currentPodcast?.id == widget.podcast.id;
    final isPlaying = isCurrentPodcast && playbackState.isPlaying;
    final isLoading = isCurrentPodcast && playbackState.isLoading;
    final position = isCurrentPodcast ? playbackState.position : Duration.zero;

    // Always prioritize podcast metadata duration, fall back to audio player duration only when playing
    final displayDuration =
        widget.podcast.durationSeconds != null
            ? Duration(seconds: widget.podcast.durationSeconds!)
            : (isCurrentPodcast && playbackState.duration.inSeconds > 0
                ? playbackState.duration
                : Duration.zero);

    if (widget.isCompact) {
      return _buildCompactPlayer(
        theme,
        isPlaying,
        isLoading,
        position,
        displayDuration,
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        children: [
          if (!isDesktop) ...[_buildHeader(theme), const SizedBox(height: 16)],
          _buildProgressSection(
            theme,
            position,
            displayDuration,
            isCurrentPodcast,
          ),
          const SizedBox(height: 16),
          _buildPlayerControls(theme, isDesktop, isPlaying, isLoading),
          if (isDesktop) ...[
            const SizedBox(height: 16),
            _buildVolumeControl(theme, playbackState.volume),
          ],
        ],
      ),
    );
  }

  Widget _buildCompactPlayer(
    ShadThemeData theme,
    bool isPlaying,
    bool isLoading,
    Duration position,
    Duration duration,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Row(
        children: [
          _buildPlayPauseButton(
            theme,
            size: 32,
            isPlaying: isPlaying,
            isLoading: isLoading,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildProgressBar(theme, position, duration),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(position),
                      style: theme.textTheme.small,
                    ),
                    Text(
                      _formatDuration(duration),
                      style: theme.textTheme.small,
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (widget.showDownloadButton) ...[
            const SizedBox(width: 8),
            _buildDownloadButton(theme, size: 24),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.border.withValues(alpha: 0.5),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.headphones,
              size: 20,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Episode ${widget.podcast.episodeNumber ?? 'Unknown'}',
                  style: theme.textTheme.h4.copyWith(
                    color: theme.colorScheme.foreground,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '3Pay Global Educational Content',
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
              ],
            ),
          ),
          if (widget.podcast.durationSeconds != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: theme.colorScheme.secondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _formatDuration(
                  Duration(seconds: widget.podcast.durationSeconds!),
                ),
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.secondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProgressSection(
    ShadThemeData theme,
    Duration position,
    Duration duration,
    bool isCurrentPodcast,
  ) {
    return Column(
      children: [
        _buildProgressBar(theme, position, duration),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(_formatDuration(position), style: theme.textTheme.small),
            Text(_formatDuration(duration), style: theme.textTheme.small),
          ],
        ),
      ],
    );
  }

  Widget _buildProgressBar(
    ShadThemeData theme,
    Duration position,
    Duration duration,
  ) {
    final progress =
        duration.inMilliseconds > 0
            ? position.inMilliseconds / duration.inMilliseconds
            : 0.0;

    return GestureDetector(
      onTapDown: (details) {
        if (duration.inMilliseconds > 0) {
          final RenderBox box = context.findRenderObject() as RenderBox;
          final localPosition = box.globalToLocal(details.globalPosition);
          final progress = localPosition.dx / box.size.width;
          final newPosition = Duration(
            milliseconds: (duration.inMilliseconds * progress).round(),
          );
          _seek(newPosition);
        }
      },
      child: Container(
        height: 8,
        decoration: BoxDecoration(
          color: theme.colorScheme.muted,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Stack(
          children: [
            // Background track
            Container(
              width: double.infinity,
              height: 8,
              decoration: BoxDecoration(
                color: theme.colorScheme.muted.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            // Progress track
            FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: progress.clamp(0.0, 1.0),
              child: Container(
                height: 8,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(4),
                  boxShadow: [
                    BoxShadow(
                      color: theme.colorScheme.primary.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
              ),
            ),
            // Thumb indicator (only show when there's progress)
            if (progress > 0.01)
              Positioned.fill(
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: FractionallySizedBox(
                    widthFactor: progress.clamp(0.0, 1.0),
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: theme.colorScheme.background,
                            width: 2,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: theme.colorScheme.primary.withValues(
                                alpha: 0.3,
                              ),
                              blurRadius: 4,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerControls(
    ShadThemeData theme,
    bool isDesktop,
    bool isPlaying,
    bool isLoading,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ShadButton.ghost(
          onPressed: _skipBackward,
          child: const Icon(Icons.replay_10, size: 20),
        ),
        const SizedBox(width: 16),
        _buildPlayPauseButton(
          theme,
          isPlaying: isPlaying,
          isLoading: isLoading,
        ),
        const SizedBox(width: 16),
        ShadButton.ghost(
          onPressed: _stop,
          child: const Icon(Icons.stop, size: 20),
        ),
        const SizedBox(width: 16),
        ShadButton.ghost(
          onPressed: _skipForward,
          child: const Icon(Icons.forward_10, size: 20),
        ),
        if (widget.showDownloadButton) ...[
          const SizedBox(width: 16),
          _buildDownloadButton(theme),
        ],
      ],
    );
  }

  Widget _buildPlayPauseButton(
    ShadThemeData theme, {
    double size = 40,
    required bool isPlaying,
    required bool isLoading,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: theme.colorScheme.primary,
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(size / 2),
          onTap: isLoading ? null : _playPause,
          child: Container(
            width: size,
            height: size,
            decoration: const BoxDecoration(shape: BoxShape.circle),
            child:
                isLoading
                    ? Padding(
                      padding: EdgeInsets.all(size * 0.25),
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          theme.colorScheme.primaryForeground,
                        ),
                      ),
                    )
                    : AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: Icon(
                        isPlaying ? Icons.pause : Icons.play_arrow,
                        key: ValueKey(isPlaying),
                        size: size * 0.5,
                        color: theme.colorScheme.primaryForeground,
                      ),
                    ),
          ),
        ),
      ),
    );
  }

  Widget _buildDownloadButton(ShadThemeData theme, {double size = 20}) {
    return ShadButton.ghost(
      onPressed: _isDownloading ? null : _downloadPodcast,
      child:
          _isDownloading
              ? SizedBox(
                width: size,
                height: size,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              )
              : Icon(Icons.download, size: size),
    );
  }

  Widget _buildVolumeControl(ShadThemeData theme, double volume) {
    return Row(
      children: [
        Icon(
          Icons.volume_up,
          size: 16,
          color: theme.colorScheme.mutedForeground,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Slider(
            value: volume,
            onChanged: _setVolume,
            activeColor: theme.colorScheme.primary,
            inactiveColor: theme.colorScheme.muted,
          ),
        ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
