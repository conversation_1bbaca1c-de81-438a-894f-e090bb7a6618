import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/custom_skeleton_widget.dart';
import '../../utils/responsive_layout.dart';

/// Loading widgets for the co-funder portal with responsive design
class CoFunderLoadingWidgets {
  
  /// Skeleton loading for investment opportunities dashboard
  static Widget investmentOpportunitiesLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final crossAxisCount = CoFunderResponsiveLayout.getInvestmentGridCrossAxisCount(context);
    final spacing = CoFunderResponsiveLayout.getCardSpacing(context);
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: CoFunderResponsiveLayout.getResponsivePadding(context),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: CoFunderResponsiveLayout.getGridChildAspectRatio(context),
      ),
      itemCount: 6,
      itemBuilder: (context, index) => _buildInvestmentCardSkeleton(theme),
    );
  }

  /// Skeleton loading for investment case details
  static Widget investmentCaseDetailLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final padding = CoFunderResponsiveLayout.getResponsivePadding(context);
    
    return SingleChildScrollView(
      padding: padding,
      child: CoFunderResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Case header skeleton
          _buildCaseHeaderSkeleton(context, theme),
          
          // Case details skeleton
          _buildCaseDetailsSkeleton(context, theme),
          
          // Investment terms skeleton
          _buildInvestmentTermsSkeleton(context, theme),
          
          // Documents skeleton
          _buildDocumentsSkeleton(context, theme),
        ],
      ),
    );
  }

  /// Skeleton loading for portfolio overview
  static Widget portfolioOverviewLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final crossAxisCount = CoFunderResponsiveLayout.getPortfolioGridCrossAxisCount(context);
    final spacing = CoFunderResponsiveLayout.getCardSpacing(context);
    
    return Column(
      children: [
        // Portfolio stats skeleton
        _buildPortfolioStatsSkeleton(context, theme),
        
        SizedBox(height: CoFunderResponsiveLayout.getSectionSpacing(context)),
        
        // Portfolio items grid
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: CoFunderResponsiveLayout.getResponsivePadding(context),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            childAspectRatio: CoFunderResponsiveLayout.getGridChildAspectRatio(context),
          ),
          itemCount: 4,
          itemBuilder: (context, index) => _buildPortfolioItemSkeleton(theme),
        ),
      ],
    );
  }

  /// Skeleton loading for funding commitments list
  static Widget fundingCommitmentsLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final padding = CoFunderResponsiveLayout.getResponsivePadding(context);
    
    return ListView.separated(
      padding: padding,
      itemCount: 5,
      separatorBuilder: (context, index) => SizedBox(
        height: CoFunderResponsiveLayout.getCardSpacing(context),
      ),
      itemBuilder: (context, index) => _buildCommitmentItemSkeleton(context, theme),
    );
  }

  /// Skeleton loading for co-funder notifications
  static Widget coFunderNotificationsLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final padding = CoFunderResponsiveLayout.getResponsivePadding(context);
    
    return ListView.separated(
      padding: padding,
      itemCount: 8,
      separatorBuilder: (context, index) => SizedBox(
        height: CoFunderResponsiveLayout.getCardSpacing(context),
      ),
      itemBuilder: (context, index) => _buildNotificationItemSkeleton(context, theme),
    );
  }

  /// Skeleton loading for co-funder profile
  static Widget coFunderProfileLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final padding = CoFunderResponsiveLayout.getResponsivePadding(context);
    
    return SingleChildScrollView(
      padding: padding,
      child: CoFunderResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile header skeleton
          _buildProfileHeaderSkeleton(context, theme),
          
          // Profile sections skeleton
          _buildProfileSectionSkeleton(context, theme, 'Personal Information'),
          _buildProfileSectionSkeleton(context, theme, 'Investment Preferences'),
          _buildProfileSectionSkeleton(context, theme, 'Subscription Level'),
        ],
      ),
    );
  }

  /// Shimmer loading effect for content
  static Widget shimmerLoading({
    required Widget child,
    bool isLoading = true,
  }) {
    if (!isLoading) return child;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 1000),
      child: child,
    );
  }

  /// Progress indicator for investment operations
  static Widget investmentProgressIndicator(BuildContext context, {
    String? message,
    double? progress,
  }) {
    final theme = ShadTheme.of(context);
    
    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: theme.colorScheme.card,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: theme.colorScheme.border),
        ),
        child: CoFunderResponsiveColumn(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (progress != null)
              SizedBox(
                width: 200,
                child: LinearProgressIndicator(
                  value: progress,
                  backgroundColor: theme.colorScheme.muted,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              )
            else
              SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              ),
            
            if (message != null) ...[
              const SizedBox(height: 16),
              Text(
                message,
                style: theme.textTheme.small,
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Private helper methods for building skeleton components
  
  static Widget _buildInvestmentCardSkeleton(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomSkeletonWidget(height: 20, widthFactor: 0.8),
          const SizedBox(height: 8),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.6),
          const SizedBox(height: 12),
          const CustomSkeletonWidget(height: 24, widthFactor: 0.4),
          const SizedBox(height: 8),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.7),
          const Spacer(),
          const CustomSkeletonWidget(height: 32, widthFactor: 1.0),
        ],
      ),
    );
  }

  static Widget _buildCaseHeaderSkeleton(BuildContext context, ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: CoFunderResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12,
        children: [
          const CustomSkeletonWidget(height: 28, widthFactor: 0.9),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.6),
          const CoFunderResponsiveRow(
            children: [
              CustomSkeletonWidget(height: 24, width: 100),
              CustomSkeletonWidget(height: 16, width: 120),
            ],
          ),
        ],
      ),
    );
  }

  static Widget _buildCaseDetailsSkeleton(BuildContext context, ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: CoFunderResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 16,
        children: [
          const CustomSkeletonWidget(height: 20, widthFactor: 0.4),
          const CustomSkeletonWidget(height: 16, widthFactor: 1.0),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.9),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.7),
        ],
      ),
    );
  }

  static Widget _buildInvestmentTermsSkeleton(BuildContext context, ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: CoFunderResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12,
        children: [
          const CustomSkeletonWidget(height: 20, widthFactor: 0.5),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.8),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.6),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.9),
        ],
      ),
    );
  }

  static Widget _buildDocumentsSkeleton(BuildContext context, ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: CoFunderResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12,
        children: [
          const CustomSkeletonWidget(height: 20, widthFactor: 0.3),
          ...List.generate(3, (index) => const CoFunderResponsiveRow(
            children: [
              CustomSkeletonWidget(height: 16, width: 24),
              CustomSkeletonWidget(height: 16, widthFactor: 0.6),
              CustomSkeletonWidget(height: 16, width: 60),
            ],
          )),
        ],
      ),
    );
  }

  static Widget _buildPortfolioStatsSkeleton(BuildContext context, ShadThemeData theme) {
    final crossAxisCount = CoFunderResponsiveLayout.isDesktop(context) ? 4 : 2;
    final spacing = CoFunderResponsiveLayout.getCardSpacing(context);
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: 2.5,
      ),
      itemCount: 4,
      itemBuilder: (context, index) => Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.card,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: theme.colorScheme.border),
        ),
        child: const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomSkeletonWidget(height: 16, widthFactor: 0.6),
            SizedBox(height: 8),
            CustomSkeletonWidget(height: 24, widthFactor: 0.4),
          ],
        ),
      ),
    );
  }

  static Widget _buildPortfolioItemSkeleton(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomSkeletonWidget(height: 18, widthFactor: 0.8),
          SizedBox(height: 8),
          CustomSkeletonWidget(height: 14, widthFactor: 0.6),
          SizedBox(height: 12),
          CustomSkeletonWidget(height: 20, widthFactor: 0.4),
          Spacer(),
          CustomSkeletonWidget(height: 16, widthFactor: 0.5),
        ],
      ),
    );
  }

  static Widget _buildCommitmentItemSkeleton(BuildContext context, ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: CoFunderResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8,
        children: [
          const CoFunderResponsiveRow(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomSkeletonWidget(height: 18, widthFactor: 0.6),
              CustomSkeletonWidget(height: 18, width: 80),
            ],
          ),
          const CustomSkeletonWidget(height: 14, widthFactor: 0.8),
          const CustomSkeletonWidget(height: 14, widthFactor: 0.4),
          const CoFunderResponsiveRow(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomSkeletonWidget(height: 14, width: 100),
              CustomSkeletonWidget(height: 14, width: 60),
            ],
          ),
        ],
      ),
    );
  }

  static Widget _buildNotificationItemSkeleton(BuildContext context, ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: CoFunderResponsiveRow(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomSkeletonWidget(
            height: 40,
            width: 40,
            cornerRadius: BorderRadius.all(Radius.circular(20)),
          ),
          Expanded(
            child: CoFunderResponsiveColumn(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 4,
              children: [
                const CustomSkeletonWidget(height: 16, widthFactor: 0.7),
                const CustomSkeletonWidget(height: 14, widthFactor: 0.9),
                const CustomSkeletonWidget(height: 12, widthFactor: 0.4),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildProfileHeaderSkeleton(BuildContext context, ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: CoFunderResponsiveRow(
        children: [
          const CustomSkeletonWidget(
            height: 80,
            width: 80,
            cornerRadius: BorderRadius.all(Radius.circular(40)),
          ),
          Expanded(
            child: CoFunderResponsiveColumn(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 8,
              children: [
                const CustomSkeletonWidget(height: 24, widthFactor: 0.6),
                const CustomSkeletonWidget(height: 16, widthFactor: 0.8),
                const CustomSkeletonWidget(height: 14, widthFactor: 0.4),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildProfileSectionSkeleton(BuildContext context, ShadThemeData theme, String title) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: CoFunderResponsiveColumn(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12,
        children: [
          CustomSkeletonWidget(height: 20, width: title.length * 8.0),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.9),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.7),
          const CustomSkeletonWidget(height: 16, widthFactor: 0.8),
        ],
      ),
    );
  }
}
