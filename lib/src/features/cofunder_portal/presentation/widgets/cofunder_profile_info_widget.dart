import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/providers/cofunder_profile_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/widgets/cofunder_profile_picture_widget.dart';

/// Widget for displaying co-funder profile information
class CoFunderProfileInfoWidget extends ConsumerWidget {
  final VoidCallback? onEditPressed;

  const CoFunderProfileInfoWidget({super.key, this.onEditPressed});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(coFunderProfileProvider);

    if (profileState.isLoading) {
      return const Center(child: CircularProgressIndicator.adaptive());
    }

    if (profileState.error != null) {
      return ShadCard(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Text(
            'Error loading profile: ${profileState.error}',
            style: theme.textTheme.p.copyWith(
              color: theme.colorScheme.destructive,
            ),
          ),
        ),
      );
    }

    final profile = profileState.profile;
    if (profile == null) {
      return ShadCard(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Text(
            'No profile information available',
            style: theme.textTheme.p,
          ),
        ),
      );
    }

    return ShadCard(
      title: Text('Profile Information', style: theme.textTheme.h4),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile picture and basic info
            LayoutBuilder(
              builder: (context, constraints) {
                // Use column layout for narrow screens
                if (constraints.maxWidth < 400) {
                  return Column(
                    children: [
                      // Profile picture centered
                      const CoFunderProfilePictureWidget(
                        size: 80,
                        showEditButton: false,
                      ),
                      const SizedBox(height: 16),
                      // Basic info
                      _buildBasicInfo(profile, theme),
                    ],
                  );
                } else {
                  // Use row layout for wider screens
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Profile picture
                      const CoFunderProfilePictureWidget(
                        size: 100,
                        showEditButton: false,
                      ),
                      const SizedBox(width: 24),
                      // Basic info
                      Expanded(child: _buildBasicInfo(profile, theme)),
                    ],
                  );
                }
              },
            ),

            const SizedBox(height: 32),

            // Edit button
            if (onEditPressed != null)
              ShadButton(
                onPressed: onEditPressed,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(LucideIcons.pencil, size: 16),
                    const SizedBox(width: 8),
                    const Text('Edit Profile'),
                  ],
                ),
              ),

            const SizedBox(height: 32),

            // Personal Information Section
            _buildSection('Personal Information', [
              _buildInfoRow(
                'Full Name',
                profile.name ?? 'Not provided',
                LucideIcons.user,
              ),
              _buildInfoRow(
                'Email',
                profile.email ?? 'Not provided',
                LucideIcons.mail,
              ),
              _buildInfoRow(
                'Phone Number',
                profile.phoneNumber ?? 'Not provided',
                LucideIcons.phone,
              ),
              _buildInfoRow(
                'Address',
                profile.address ?? 'Not provided',
                LucideIcons.mapPin,
              ),
            ], theme),

            const SizedBox(height: 24),

            // Investment Information Section
            _buildSection('Investment Information', [
              _buildInfoRow(
                'Current Level',
                'Level ${profile.currentLevel}',
                LucideIcons.trendingUp,
              ),
              _buildInfoRow(
                'AML/KYC Status',
                _formatStatus(profile.amlKycStatus),
                LucideIcons.shield,
              ),
              _buildInfoRow(
                'Level 4 Subscribed',
                profile.level4Subscribed ? 'Yes' : 'No',
                LucideIcons.star,
              ),
              _buildInfoRow(
                'Funding Capacity',
                profile.fundingCapacity != null
                    ? '£${NumberFormat('#,##0.00').format(profile.fundingCapacity)}'
                    : 'Not specified',
                LucideIcons.dollarSign,
              ),
              _buildInfoRow(
                'Risk Tolerance',
                profile.riskTolerance ?? 'Not specified',
                LucideIcons.activity,
              ),
            ], theme),

            const SizedBox(height: 24),

            // Preferred Sectors Section
            if (profile.preferredSectors.isNotEmpty)
              _buildSection('Preferred Sectors', [
                _buildSectorsList(profile.preferredSectors, theme),
              ], theme),

            const SizedBox(height: 24),

            // Account Information Section
            _buildSection('Account Information', [
              _buildInfoRow(
                'Educational Content Read',
                '${profile.readEducationalContent.length} articles',
                LucideIcons.bookOpen,
              ),
              _buildInfoRow(
                'Member Since',
                _formatDate(profile.created),
                LucideIcons.calendar,
              ),
              _buildInfoRow(
                'Last Updated',
                _formatDate(profile.updated),
                LucideIcons.clock,
              ),
            ], theme),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfo(CoFunderProfile profile, ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(profile.name ?? 'Co-Funder', style: theme.textTheme.h3),
        const SizedBox(height: 4),
        Text(
          'Level ${profile.currentLevel}',
          style: theme.textTheme.large.copyWith(
            color: theme.colorScheme.mutedForeground,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getStatusColor(profile.amlKycStatus, theme),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            _formatStatus(profile.amlKycStatus),
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.background,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSection(
    String title,
    List<Widget> children,
    ShadThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: theme.textTheme.h4),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Builder(
      builder: (context) {
        final theme = ShadTheme.of(context);
        return Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(icon, size: 16, color: theme.colorScheme.mutedForeground),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(value, style: theme.textTheme.p),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectorsList(List<String> sectors, ShadThemeData theme) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children:
          sectors
              .map(
                (sector) => Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.muted,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    sector,
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ),
              )
              .toList(),
    );
  }

  String _formatDate(DateTime date) {
    return DateFormat('MMM dd, yyyy').format(date);
  }

  String _formatStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'KYC Pending';
      case 'approved':
        return 'KYC Approved';
      case 'rejected':
        return 'Rejected';
      case 'under_review':
        return 'KYC Under Review';
      default:
        return status;
    }
  }

  Color _getStatusColor(String status, ShadThemeData theme) {
    switch (status.toLowerCase()) {
      case 'approved':
        return Colors.green;
      case 'rejected':
        return theme.colorScheme.destructive;
      case 'pending':
      case 'under_review':
        return Colors.orange;
      default:
        return theme.colorScheme.muted;
    }
  }
}
