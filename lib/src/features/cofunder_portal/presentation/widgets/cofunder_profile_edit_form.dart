import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/utils/cofunder_profile_validation.dart';
import '../providers/cofunder_profile_provider.dart';
import 'cofunder_profile_picture_widget.dart';

/// Form widget for editing co-funder profile information
class CoFunderProfileEditForm extends ConsumerStatefulWidget {
  final VoidCallback? onSaved;
  final VoidCallback? onCancelled;

  const CoFunderProfileEditForm({super.key, this.onSaved, this.onCancelled});

  @override
  ConsumerState<CoFunderProfileEditForm> createState() =>
      _CoFunderProfileEditFormState();
}

class _CoFunderProfileEditFormState
    extends ConsumerState<CoFunderProfileEditForm> {
  final _formKey = GlobalKey<ShadFormState>();

  // Form controllers
  late final TextEditingController _nameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  late final TextEditingController _addressController;
  late final TextEditingController _fundingCapacityController;

  // Form state
  String? _selectedRiskTolerance;
  List<String> _selectedSectors = [];
  Map<String, dynamic> _notificationPreferences = {};

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadProfileData();
  }

  void _initializeControllers() {
    _nameController = TextEditingController();
    _emailController = TextEditingController();
    _phoneController = TextEditingController();
    _addressController = TextEditingController();
    _fundingCapacityController = TextEditingController();
  }

  void _loadProfileData() {
    final profileState = ref.read(coFunderProfileProvider);
    final profile = profileState.profile;

    if (profile != null) {
      _nameController.text = profile.name ?? '';
      _emailController.text = profile.email ?? '';
      _phoneController.text = profile.phoneNumber ?? '';
      _addressController.text = profile.address ?? '';
      _fundingCapacityController.text =
          profile.fundingCapacity?.toString() ?? '';
      _selectedRiskTolerance = profile.riskTolerance;
      _selectedSectors = List.from(profile.preferredSectors);
      _notificationPreferences = Map.from(
        profile.notificationPreferences ?? {},
      );
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _fundingCapacityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(coFunderProfileProvider);
    final profile = profileState.profile;

    return ShadCard(
      title: Text('Edit Profile', style: theme.textTheme.h4),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: ShadForm(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Picture Section
              Center(
                child: CoFunderProfilePictureWidget(
                  size: 120,
                  showEditButton: true,
                  // Don't override onEditPressed - let the widget handle it internally
                ),
              ),

              const SizedBox(height: 32),

              // Personal Information Section
              Text('Personal Information', style: theme.textTheme.h4),
              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'name',
                controller: _nameController,
                label: const Text('Full Name'),
                placeholder: const Text('Enter your full name'),
                validator:
                    (value) => CoFunderProfileValidation.validateName(value),
                enabled: false, // Personal information is read-only
                decoration: ShadDecoration(color: theme.colorScheme.muted),
              ),

              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'email',
                controller: _emailController,
                label: const Text('Email Address'),
                placeholder: const Text('Enter your email address'),
                keyboardType: TextInputType.emailAddress,
                validator: CoFunderProfileValidation.validateEmail,
                enabled: false, // Personal information is read-only
                decoration: ShadDecoration(color: theme.colorScheme.muted),
              ),

              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'phone',
                controller: _phoneController,
                label: const Text('Phone Number'),
                placeholder: const Text('Enter your phone number'),
                keyboardType: TextInputType.phone,
                validator: CoFunderProfileValidation.validatePhoneNumber,
                enabled: false, // Personal information is read-only
                decoration: ShadDecoration(color: theme.colorScheme.muted),
              ),

              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'address',
                controller: _addressController,
                label: const Text('Address'),
                placeholder: const Text('Enter your address'),
                maxLines: 2,
                validator:
                    (value) => CoFunderProfileValidation.validateAddressLine(
                      value,
                      required: false,
                    ),
                enabled: false, // Personal information is read-only
                decoration: ShadDecoration(color: theme.colorScheme.muted),
              ),

              const SizedBox(height: 24),

              // Investment Information Section
              Text('Investment Information', style: theme.textTheme.h4),
              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'fundingCapacity',
                controller: _fundingCapacityController,
                label: const Text('Funding Capacity (£)'),
                placeholder: const Text('Enter your funding capacity'),
                keyboardType: TextInputType.number,
                validator: CoFunderProfileValidation.validateFundingCapacity,
              ),

              const SizedBox(height: 16),

              // Risk Tolerance Dropdown
              ShadSelectFormField<String>(
                id: 'riskTolerance',
                label: const Text('Risk Tolerance'),
                placeholder: const Text('Select your risk tolerance'),
                initialValue: _selectedRiskTolerance,
                onChanged: (value) {
                  setState(() {
                    _selectedRiskTolerance = value;
                  });
                },
                options:
                    CoFunderProfileValidation.getRiskToleranceOptions()
                        .map(
                          (option) =>
                              ShadOption(value: option, child: Text(option)),
                        )
                        .toList(),
                selectedOptionBuilder: (context, value) => Text(value),
                validator: CoFunderProfileValidation.validateRiskTolerance,
              ),

              const SizedBox(height: 16),

              // Preferred Sectors (Multi-select)
              Text('Preferred Sectors', style: theme.textTheme.large),
              const SizedBox(height: 8),
              Text(
                'Select up to 10 sectors you\'re interested in investing in',
                style: theme.textTheme.muted,
              ),
              const SizedBox(height: 12),
              _buildSectorSelection(theme),

              const SizedBox(height: 24),

              // Current Level Display (Read-only)
              if (profile != null) ...[
                Text('Account Information', style: theme.textTheme.h4),
                const SizedBox(height: 16),

                ShadCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Icon(
                          LucideIcons.star,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 12),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Current Level', style: theme.textTheme.large),
                            Text(
                              'Level ${profile.currentLevel}',
                              style: theme.textTheme.h4,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),
              ],

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: ShadButton.outline(
                      onPressed: profileState.isUpdating ? null : _handleCancel,
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ShadButton(
                      onPressed: profileState.isUpdating ? null : _handleSave,
                      child:
                          profileState.isUpdating
                              ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text('Saving...'),
                                ],
                              )
                              : const Text('Save Changes'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectorSelection(ShadThemeData theme) {
    final availableSectors = CoFunderProfileValidation.getAvailableSectors();

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.border),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(12),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children:
            availableSectors.map((sector) {
              final isSelected = _selectedSectors.contains(sector);
              return FilterChip(
                label: Text(sector),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected && _selectedSectors.length < 10) {
                      _selectedSectors.add(sector);
                    } else if (!selected) {
                      _selectedSectors.remove(sector);
                    }
                  });
                },
                backgroundColor: theme.colorScheme.background,
                selectedColor: theme.colorScheme.primary.withValues(alpha: 0.2),
                checkmarkColor: theme.colorScheme.primary,
              );
            }).toList(),
      ),
    );
  }

  void _handleCancel() {
    if (widget.onCancelled != null) {
      widget.onCancelled!();
    } else {
      Navigator.of(context).pop();
    }
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate sector selection
    final sectorValidation = CoFunderProfileValidation.validatePreferredSectors(
      _selectedSectors,
    );
    if (sectorValidation != null) {
      ShadToaster.of(context).show(
        ShadToast(
          title: const Text('Validation Error'),
          description: Text(sectorValidation),
        ),
      );
      return;
    }

    // Personal information fields are read-only, so we only update the co-funder profile

    // Parse funding capacity
    double? fundingCapacity;
    if (_fundingCapacityController.text.trim().isNotEmpty) {
      fundingCapacity = double.tryParse(_fundingCapacityController.text.trim());
    }

    // Update co-funder profile
    final success = await ref
        .read(coFunderProfileProvider.notifier)
        .updateProfile(
          fundingCapacity: fundingCapacity,
          preferredSectors: _selectedSectors,
          riskTolerance: _selectedRiskTolerance,
          notificationPreferences: _notificationPreferences,
        );

    if (mounted && success) {
      ShadToaster.of(context).show(
        const ShadToast(
          title: Text('Success'),
          description: Text('Profile updated successfully'),
        ),
      );

      if (widget.onSaved != null) {
        widget.onSaved!();
      } else {
        Navigator.of(context).pop();
      }
    }
  }
}
