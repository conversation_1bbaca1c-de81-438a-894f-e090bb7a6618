import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/providers/cofunder_profile_provider.dart';

/// Widget for managing co-funder preferences
class CoFunderPreferencesWidget extends ConsumerStatefulWidget {
  const CoFunderPreferencesWidget({super.key});

  @override
  ConsumerState<CoFunderPreferencesWidget> createState() =>
      _CoFunderPreferencesWidgetState();
}

class _CoFunderPreferencesWidgetState
    extends ConsumerState<CoFunderPreferencesWidget> {
  late Map<String, dynamic> _preferences;
  bool _hasChanges = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _preferences = _getDefaultPreferences();
  }

  Map<String, dynamic> _getDefaultPreferences() {
    return {
      'email_notifications': true,
      'push_notifications': true,
      'sms_notifications': false,
      'investment_opportunities': true,
      'funding_updates': true,
      'document_updates': true,
      'system_announcements': true,
      'marketing_emails': false,
      'weekly_digest': true,
      'language': 'en',
      'timezone': 'Europe/London',
      'privacy_public_profile': false,
      'privacy_share_data': false,
      'investment_alerts': true,
      'portfolio_updates': true,
      'educational_content_alerts': true,
      'level_upgrade_notifications': true,
    };
  }

  void _initializePreferencesFromProfile(CoFunderProfile profile) {
    if (!_isInitialized && profile.notificationPreferences != null) {
      setState(() {
        _preferences = {
          ..._getDefaultPreferences(),
          ...profile.notificationPreferences!,
        };
        _isInitialized = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(coFunderProfileProvider);

    // Initialize preferences from profile data when available
    if (profileState.profile != null) {
      _initializePreferencesFromProfile(profileState.profile!);
    }

    return ShadCard(
      title: Text('Preferences', style: theme.textTheme.h4),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child:
            profileState.isLoading
                ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator.adaptive(),
                      SizedBox(height: 16),
                      Text('Loading preferences...'),
                    ],
                  ),
                )
                : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Notification Preferences
                    _buildSection('Notification Preferences', [
                      _buildSwitchTile(
                        'Email Notifications',
                        'Receive notifications via email',
                        'email_notifications',
                        LucideIcons.mail,
                      ),
                      _buildSwitchTile(
                        'Push Notifications',
                        'Receive push notifications on your device',
                        'push_notifications',
                        LucideIcons.bell,
                      ),
                      _buildSwitchTile(
                        'SMS Notifications',
                        'Receive important updates via SMS',
                        'sms_notifications',
                        LucideIcons.messageSquare,
                      ),
                    ], theme),

                    const SizedBox(height: 24),

                    // Investment Preferences
                    _buildSection('Investment Preferences', [
                      _buildSwitchTile(
                        'Investment Opportunities',
                        'Notifications about new investment opportunities',
                        'investment_opportunities',
                        LucideIcons.trendingUp,
                      ),
                      _buildSwitchTile(
                        'Funding Updates',
                        'Updates on your current investments',
                        'funding_updates',
                        LucideIcons.dollarSign,
                      ),
                      _buildSwitchTile(
                        'Portfolio Updates',
                        'Regular updates on your investment portfolio',
                        'portfolio_updates',
                        LucideIcons.trendingUp,
                      ),
                      _buildSwitchTile(
                        'Investment Alerts',
                        'Urgent alerts about investment changes',
                        'investment_alerts',
                        LucideIcons.bell,
                      ),
                    ], theme),

                    const SizedBox(height: 24),

                    // Content Preferences
                    _buildSection('Content Preferences', [
                      _buildSwitchTile(
                        'Document Updates',
                        'Notifications when documents are added or updated',
                        'document_updates',
                        LucideIcons.file,
                      ),
                      _buildSwitchTile(
                        'Educational Content',
                        'Notifications about new educational content',
                        'educational_content_alerts',
                        LucideIcons.bookOpen,
                      ),
                      _buildSwitchTile(
                        'Level Upgrades',
                        'Notifications about level upgrade opportunities',
                        'level_upgrade_notifications',
                        LucideIcons.star,
                      ),
                      _buildSwitchTile(
                        'System Announcements',
                        'Important platform updates and announcements',
                        'system_announcements',
                        LucideIcons.megaphone,
                      ),
                      _buildSwitchTile(
                        'Weekly Digest',
                        'Weekly summary of your account activity',
                        'weekly_digest',
                        LucideIcons.calendar,
                      ),
                    ], theme),

                    const SizedBox(height: 24),

                    // Privacy Preferences
                    _buildSection('Privacy Preferences', [
                      _buildSwitchTile(
                        'Public Profile',
                        'Make your profile visible to other users',
                        'privacy_public_profile',
                        LucideIcons.eye,
                      ),
                      _buildSwitchTile(
                        'Share Data for Analytics',
                        'Help improve our services with anonymous usage data',
                        'privacy_share_data',
                        LucideIcons.activity,
                      ),
                      _buildSwitchTile(
                        'Marketing Emails',
                        'Receive promotional emails and updates',
                        'marketing_emails',
                        LucideIcons.mail,
                      ),
                    ], theme),

                    const SizedBox(height: 32),

                    // Save button
                    if (_hasChanges)
                      ShadButton(
                        onPressed:
                            profileState.isUpdating ? null : _savePreferences,
                        child:
                            profileState.isUpdating
                                ? const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator.adaptive(
                                        strokeWidth: 2,
                                      ),
                                    ),
                                    SizedBox(width: 8),
                                    Text('Saving...'),
                                  ],
                                )
                                : const Text('Save Preferences'),
                      ),

                    // Error display
                    if (profileState.error != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 16.0),
                        child: Container(
                          padding: const EdgeInsets.all(12.0),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.destructive.withValues(
                              alpha: 0.1,
                            ),
                            borderRadius: BorderRadius.circular(8.0),
                            border: Border.all(
                              color: theme.colorScheme.destructive.withValues(
                                alpha: 0.3,
                              ),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                LucideIcons.x,
                                color: theme.colorScheme.destructive,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  profileState.error!,
                                  style: theme.textTheme.small.copyWith(
                                    color: theme.colorScheme.destructive,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
      ),
    );
  }

  Widget _buildSection(
    String title,
    List<Widget> children,
    ShadThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: theme.textTheme.h4),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    String key,
    IconData icon,
  ) {
    return Builder(
      builder: (context) {
        final theme = ShadTheme.of(context);
        return Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Row(
            children: [
              Icon(icon, size: 20, color: theme.colorScheme.mutedForeground),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, style: theme.textTheme.p),
                    Text(
                      subtitle,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ),
              Switch.adaptive(
                value: _preferences[key] ?? false,
                onChanged: (value) {
                  setState(() {
                    _preferences[key] = value;
                    _hasChanges = true;
                  });
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _savePreferences() async {
    // Save preferences to the co-funder profile
    final success = await ref
        .read(coFunderProfileProvider.notifier)
        .updateProfile(notificationPreferences: _preferences);

    if (mounted && success) {
      setState(() {
        _hasChanges = false;
      });

      ShadToaster.of(context).show(
        const ShadToast(
          title: Text('Success'),
          description: Text('Preferences saved successfully'),
        ),
      );
    }
  }
}
