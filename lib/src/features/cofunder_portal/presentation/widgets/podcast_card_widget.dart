import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/content_item_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_podcast_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/utils/responsive_layout.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';

class PodcastCardWidget extends StatelessWidget {
  final ContentItemModel contentItem;
  final CoFunderPodcastModel? podcastData;
  final VoidCallback? onTap;
  final bool isCompact;
  final bool showPlayButton;

  const PodcastCardWidget({
    super.key,
    required this.contentItem,
    this.podcastData,
    this.onTap,
    this.isCompact = false,
    this.showPlayButton = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isDesktop = CoFunderResponsiveLayout.isDesktop(context);
    final isTablet = CoFunderResponsiveLayout.isTablet(context);

    if (isCompact) {
      return _buildCompactCard(context, theme, isDesktop);
    } else if (isDesktop || isTablet) {
      return _buildDesktopCard(context, theme, isDesktop);
    } else {
      return _buildMobileCard(context, theme);
    }
  }

  Widget _buildCompactCard(
    BuildContext context,
    ShadThemeData theme,
    bool isDesktop,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: theme.colorScheme.card,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: theme.colorScheme.border),
        ),
        child: Row(
          children: [
            _buildThumbnail(theme, size: 48),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    contentItem.title,
                    style: theme.textTheme.small.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  _buildMetadata(theme, isCompact: true),
                ],
              ),
            ),
            if (showPlayButton) ...[
              const SizedBox(width: 8),
              _buildPlayButton(theme, size: 32),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopCard(
    BuildContext context,
    ShadThemeData theme,
    bool isDesktop,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: ShadCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCardHeader(theme),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    contentItem.title,
                    style: theme.textTheme.h4.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  if (contentItem.summary != null &&
                      contentItem.summary!.isNotEmpty) ...[
                    Text(
                      contentItem.summary!,
                      style: theme.textTheme.p.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 12),
                  ],
                  _buildMetadata(theme),
                  const SizedBox(height: 16),
                  _buildCardActions(theme),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileCard(BuildContext context, ShadThemeData theme) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: ShadCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildThumbnail(theme, size: 80),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          contentItem.title,
                          style: theme.textTheme.h4.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 8),
                        _buildMetadata(theme),
                      ],
                    ),
                  ),
                ],
              ),
              if (contentItem.summary != null &&
                  contentItem.summary!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Text(
                  contentItem.summary!,
                  style: theme.textTheme.p.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(height: 16),
              _buildCardActions(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardHeader(ShadThemeData theme) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Stack(
        children: [
          if (contentItem.thumbnailImage != null &&
              contentItem.thumbnailImage!.isNotEmpty)
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              child: Image.network(
                _getThumbnailUrl(),
                width: double.infinity,
                height: 120,
                fit: BoxFit.cover,
                errorBuilder:
                    (context, error, stackTrace) =>
                        _buildDefaultThumbnail(theme),
              ),
            )
          else
            _buildDefaultThumbnail(theme),
          Positioned(top: 8, right: 8, child: _buildPodcastBadge(theme)),
          if (podcastData?.featured == true)
            Positioned(top: 8, left: 8, child: _buildFeaturedBadge(theme)),
        ],
      ),
    );
  }

  Widget _buildThumbnail(ShadThemeData theme, {double size = 60}) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        borderRadius: BorderRadius.circular(8),
      ),
      child:
          contentItem.thumbnailImage != null &&
                  contentItem.thumbnailImage!.isNotEmpty
              ? ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  _getThumbnailUrl(),
                  width: size,
                  height: size,
                  fit: BoxFit.cover,
                  errorBuilder:
                      (context, error, stackTrace) =>
                          _buildDefaultThumbnail(theme, size: size),
                ),
              )
              : _buildDefaultThumbnail(theme, size: size),
    );
  }

  Widget _buildDefaultThumbnail(ShadThemeData theme, {double? size}) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        borderRadius: BorderRadius.circular(size != null ? 8 : 0),
      ),
      child: Icon(
        LucideIcons.headphones,
        size: (size ?? 120) * 0.4,
        color: theme.colorScheme.mutedForeground,
      ),
    );
  }

  Widget _buildPodcastBadge(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.purple.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(LucideIcons.headphones, size: 12, color: Colors.white),
          const SizedBox(width: 4),
          Text(
            'Podcast',
            style: theme.textTheme.small.copyWith(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedBadge(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        'Featured',
        style: theme.textTheme.small.copyWith(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildMetadata(ShadThemeData theme, {bool isCompact = false}) {
    final dateFormat = DateFormat.yMMMd();
    final publishedDate =
        contentItem.publishedAt != null
            ? dateFormat.format(contentItem.publishedAt!)
            : 'No date';

    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: [
        if (podcastData?.durationSeconds != null) ...[
          _buildMetadataItem(
            theme,
            LucideIcons.clock,
            podcastData!.formattedDuration,
            isCompact: isCompact,
          ),
        ],
        if (podcastData?.episodeNumber != null) ...[
          _buildMetadataItem(
            theme,
            LucideIcons.hash,
            'Episode ${podcastData!.episodeNumber}',
            isCompact: isCompact,
          ),
        ],
        _buildMetadataItem(
          theme,
          LucideIcons.calendar,
          publishedDate,
          isCompact: isCompact,
        ),
        if (podcastData?.hasGuestSpeakers == true && !isCompact) ...[
          _buildMetadataItem(
            theme,
            LucideIcons.users,
            podcastData!.guestSpeakersText,
            isCompact: isCompact,
          ),
        ],
      ],
    );
  }

  Widget _buildMetadataItem(
    ShadThemeData theme,
    IconData icon,
    String text, {
    bool isCompact = false,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: isCompact ? 12 : 14,
          color: theme.colorScheme.mutedForeground,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: (isCompact ? theme.textTheme.small : theme.textTheme.small)
              .copyWith(
                color: theme.colorScheme.mutedForeground,
                fontSize: isCompact ? 10 : 12,
              ),
        ),
      ],
    );
  }

  Widget _buildPlayButton(ShadThemeData theme, {double size = 40}) {
    return ShadButton.outline(
      onPressed: onTap,
      child: Icon(
        LucideIcons.play,
        size: size * 0.5,
        color: theme.colorScheme.primary,
      ),
    );
  }

  Widget _buildCardActions(ShadThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: ShadButton.outline(
            onPressed: onTap,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(LucideIcons.play, size: 16),
                const SizedBox(width: 8),
                const Text('Listen Now'),
              ],
            ),
          ),
        ),
        const SizedBox(width: 12),
        ShadButton.ghost(
          onPressed: () {
            // TODO: Implement share functionality
          },
          child: const Icon(LucideIcons.share2, size: 16),
        ),
      ],
    );
  }

  String _getThumbnailUrl() {
    final pbService = PocketBaseService();
    return contentItem.getImageUrl(pbService.client) ?? '';
  }
}
