import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:signature/signature.dart'; // For signature pad
import 'dart:convert'; // For base64 encoding/decoding if needed for signature

// A dialog for capturing signature
class SignatureCaptureDialog extends StatefulWidget {
  final SignatureController controller;
  final VoidCallback onSave;

  const SignatureCaptureDialog({
    super.key,
    required this.controller,
    required this.onSave,
  });

  @override
  State<SignatureCaptureDialog> createState() => _SignatureCaptureDialogState();
}

class _SignatureCaptureDialogState extends State<SignatureCaptureDialog> {
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Provide Signature'),
      content: SizedBox(
        width:
            double.maxFinite, // Ensure the dialog content takes a defined width
        child: Container(
          height: 200, // Adjust as needed
          decoration: BoxDecoration(border: Border.all(color: Colors.grey)),
          child: Signature(
            controller: widget.controller,
            backgroundColor: Colors.white,
            height: 198,
          ),
        ),
      ),
      actions: <Widget>[
        TextButton(
          child: const Text('Clear'),
          onPressed: () {
            widget.controller.clear();
          },
        ),
        TextButton(
          child: const Text('Cancel'),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        ElevatedButton(
          child: const Text('Save Signature'),
          onPressed: () {
            if (widget.controller.isNotEmpty) {
              widget.onSave();
              Navigator.of(context).pop();
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please provide a signature before saving.'),
                ),
              );
            }
          },
        ),
      ],
    );
  }
}

class AgreementStepWidget extends StatefulWidget {
  final GlobalKey<ShadFormState> formKey;
  final String stepTitle;
  final String agreementFormName;
  final Widget agreementContent; // To display detailed agreement text
  final String agreementStatement;
  final bool initialAgreedValue;
  final ValueChanged<bool> onAgreedChanged;
  final String? initialSignatureBase64; // Existing signature
  final ValueChanged<String?>
  onSignatureSaved; // Callback with base64 signature
  final TextEditingController? notesController; // Optional controller for notes
  final String? notesHintText; // Optional hint text for the notes field

  const AgreementStepWidget({
    super.key,
    required this.formKey,
    required this.stepTitle,
    required this.agreementFormName,
    required this.agreementContent,
    required this.agreementStatement,
    required this.initialAgreedValue,
    required this.onAgreedChanged,
    this.initialSignatureBase64,
    required this.onSignatureSaved,
    this.notesController,
    this.notesHintText = 'Optional: Add any notes here',
  });

  @override
  State<AgreementStepWidget> createState() => _AgreementStepWidgetState();
}

class _AgreementStepWidgetState extends State<AgreementStepWidget> {
  late SignatureController _signatureController;
  String? _currentSignatureBase64;
  late bool _agreed;

  @override
  void initState() {
    super.initState();
    _agreed = widget.initialAgreedValue;
    _currentSignatureBase64 = widget.initialSignatureBase64;
    _signatureController = SignatureController(
      penStrokeWidth: 3,
      penColor: Colors.black,
      exportBackgroundColor: Colors.white,
    );
    if (_currentSignatureBase64 != null &&
        _currentSignatureBase64!.isNotEmpty) {
      // If there's an initial signature, we can't directly load it back into points easily
      // For display, we'll just show the image. For re-signing, they'd clear it.
    }
  }

  @override
  void dispose() {
    _signatureController.dispose();
    super.dispose();
  }

  Future<void> _captureSignature() async {
    // Clear previous signature before capturing new one
    _signatureController.clear();
    await showDialog(
      context: context,
      builder:
          (context) => SignatureCaptureDialog(
            controller: _signatureController,
            onSave: () async {
              if (_signatureController.isNotEmpty) {
                final pngBytes = await _signatureController.toPngBytes();
                if (pngBytes != null) {
                  final base64 = base64Encode(pngBytes);
                  setState(() {
                    _currentSignatureBase64 = base64;
                  });
                  widget.onSignatureSaved(base64);
                }
              }
            },
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    // final isMobile = MediaQuery.of(context).size.width <= 600; // Temporarily unused

    return ShadForm(
      key: widget.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min, // Added to help with layout
        children: [
          Text(
            widget.stepTitle,
            style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),

          // Display Agreement Content
          Container(
            constraints: const BoxConstraints(
              maxHeight: 250,
            ), // Constrain the height
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              border: Border.all(color: theme.colorScheme.border),
              borderRadius: theme.radius,
            ),
            child: SingleChildScrollView(
              child: widget.agreementContent,
            ), // Ensure content is scrollable
          ),
          const SizedBox(height: 24),

          // Signature Section
          Text(
            'Signature:',
            style: theme.textTheme.large.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          if (_currentSignatureBase64 != null &&
              _currentSignatureBase64!.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: theme.colorScheme.border),
                borderRadius: theme.radius,
              ),
              constraints: const BoxConstraints(
                maxHeight: 100,
                maxWidth: 200,
              ), // Constrain signature image
              child: Image.memory(
                base64Decode(_currentSignatureBase64!),
                fit: BoxFit.contain,
              ),
            )
          else
            Text('No signature provided yet.', style: theme.textTheme.muted),
          const SizedBox(height: 12),
          ShadButton.outline(
            onPressed: _captureSignature,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(LucideIcons.pencil, size: 16),
                const SizedBox(width: 8),
                Text(
                  _currentSignatureBase64 != null &&
                          _currentSignatureBase64!.isNotEmpty
                      ? 'Re-sign Document'
                      : 'Sign Document',
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Optional Investor Notes
          if (widget.notesController != null) ...[
            Text(
              'Additional Notes (Optional):',
              style: theme.textTheme.large.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            ShadInputFormField(
              id:
                  'investor_notes_field_${widget.agreementFormName.replaceAll(' ', '_').toLowerCase()}',
              controller: widget.notesController!,
              placeholder: Text(
                widget.notesHintText ?? 'Optional: Add any notes here',
              ),
              maxLines: 3,
              // No validator needed for an optional field
            ),
            const SizedBox(height: 24),
          ],

          // Agreement Checkbox
          ShadCheckboxFormField(
            id:
                'agreement_checkbox_step_${widget.agreementFormName.replaceAll(' ', '_').toLowerCase()}',
            label: Text(widget.agreementStatement),
            initialValue: _agreed, // Ensure _agreed is initialized
            onChanged: (value) {
              final bool newAgreedValue =
                  value ?? false; // Ensure non-null boolean
              setState(() {
                _agreed = newAgreedValue;
              });
              widget.onAgreedChanged(newAgreedValue);
            },
            validator: (v) {
              final bool currentVal = v ?? false;
              if (_currentSignatureBase64 == null ||
                  _currentSignatureBase64!.isEmpty) {
                return 'Please sign the document before agreeing.';
              }
              if (!currentVal) {
                return 'You must agree to this form to proceed.';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }
}
