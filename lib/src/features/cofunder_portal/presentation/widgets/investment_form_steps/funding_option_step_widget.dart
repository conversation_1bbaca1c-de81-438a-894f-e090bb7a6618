import 'package:flutter/material.dart' hide Radio; // Hide Material Radio
import 'package:flutter/material.dart' as material show Card, Radio; // Use Material Card and Radio explicitly
import 'package:shadcn_ui/shadcn_ui.dart'; // Still use Shadcn for overall theme and form structure

class FundingOptionStepWidget extends StatelessWidget {
  final GlobalKey<ShadFormState> formKey;
  final String? initialValue;
  final ValueChanged<String?> onChanged;

  const FundingOptionStepWidget({
    super.key,
    required this.formKey,
    required this.initialValue,
    required this.onChanged,
  });

  Widget _buildOptionCard({
    required BuildContext context,
    required String title,
    required String description,
    required String value,
    required String? groupValue,
    required ValueChanged<String?> onOptionChanged,
    required bool isMobile,
    required bool isDarkMode,
  }) {
    final theme = ShadTheme.of(context);
    final bool isSelected = groupValue == value;

    return material.Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected
              ? theme.colorScheme.primary // Use Shadcn primary for selected
              : (isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
          width: isSelected ? 2 : 1,
        ),
      ),
      color: isDarkMode ? theme.colorScheme.background : Colors.white, // Use Shadcn background
      child: InkWell(
        onTap: () => onOptionChanged(value),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(isMobile ? 12 : 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  material.Radio<String>( // Explicitly use Material Radio
                    value: value,
                    groupValue: groupValue,
                    onChanged: onOptionChanged,
                    activeColor: theme.colorScheme.primary,
                  ),
                  Expanded( // Ensure title doesn't overflow
                    child: Text(
                      title,
                      style: theme.textTheme.large.copyWith( // Use Shadcn text styles
                        fontWeight: FontWeight.w600,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: isMobile ? 6 : 8),
              Padding(
                padding: EdgeInsets.only(left: isMobile ? 40 : 48), // Align with radio button text
                child: Text(
                  description,
                  style: theme.textTheme.muted.copyWith( // Use Shadcn muted style
                    height: 1.5,
                    fontSize: isMobile ? 12 : 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isMobile = MediaQuery.of(context).size.width <= 600;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return ShadForm(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Step 1: Choose Your Funding Option', style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.w600)),
          const SizedBox(height: 24),
          _buildOptionCard(
            context: context,
            title: 'Discretionary Funding',
            description: 'You authorize 3Pay to make decisions on your behalf regarding which claims to fund, trusting their expertise.',
            value: 'discretionary',
            groupValue: initialValue,
            onOptionChanged: onChanged,
            isMobile: isMobile,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: 16),
          _buildOptionCard(
            context: context,
            title: 'Non-Discretionary Funding',
            description: 'You retain sole authority and responsibility for selecting which specific claims to fund.',
            value: 'non_discretionary',
            groupValue: initialValue,
            onOptionChanged: onChanged,
            isMobile: isMobile,
            isDarkMode: isDarkMode,
          ),
          // Hidden ShadRadioGroupFormField for validation purposes
          Opacity(
            opacity: 0,
            child: AbsorbPointer(
              child: ShadRadioGroupFormField<String>(
                key: ValueKey(initialValue), // Add ValueKey here
                id: 'funding_type_validation_hidden',
                initialValue: initialValue,
                onChanged: (_) {}, // No-op, actual change handled by cards
                items: [ShadRadio(value: 'discretionary', label: Text('')), ShadRadio(value: 'non_discretionary', label: Text(''))],
                validator: (v) {
                  // The initialValue passed to the widget is the actual selected value from the parent.
                  // So, we validate against that directly.
                  if (initialValue == null) return 'Please select a funding option.';
                  return null;
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}