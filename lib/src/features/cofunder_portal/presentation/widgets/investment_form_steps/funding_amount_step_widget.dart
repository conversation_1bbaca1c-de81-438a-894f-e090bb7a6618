import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart'; // For currency formatting
import 'package:shadcn_ui/shadcn_ui.dart'; // Still use Shadcn for overall theme and form structure

class FundingAmountStepWidget extends StatefulWidget {
  final GlobalKey<ShadFormState> formKey;
  final TextEditingController controller;
  final String? selectedFundingType; // To determine interest display
  final ValueChanged<double> onAmountChanged; // Callback to update parent state

  const FundingAmountStepWidget({
    super.key,
    required this.formKey,
    required this.controller,
    required this.selectedFundingType,
    required this.onAmountChanged,
  });

  @override
  State<FundingAmountStepWidget> createState() =>
      _FundingAmountStepWidgetState();
}

class _FundingAmountStepWidgetState extends State<FundingAmountStepWidget> {
  String? _amountError;
  double _currentAmount = 0;

  // Placeholder interest rates - replace with actual logic
  final double _discretionaryInterestRate = 0.15; // Example: 15%

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
    // Initialize _currentAmount if controller has text
    if (widget.controller.text.isNotEmpty) {
      _onTextChanged();
    }
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    final text = widget.controller.text;
    double parsedAmount = 0;
    String? error;

    if (text.isEmpty) {
      // error = 'Please enter an amount.'; // Validator will handle this
    } else {
      parsedAmount = double.tryParse(text) ?? 0;
      if (parsedAmount <= 0) {
        // error = 'Amount must be greater than zero.'; // Validator
      } else if (parsedAmount < 1000) {
        // Example minimum
        // error = 'Minimum investment is £1,000.'; // Validator
      } else if (parsedAmount > 1000000) {
        // Example maximum
        // error = 'Maximum investment is £1,000,000.'; // Validator
      }
    }
    // Only update state for display, validation is separate
    if (mounted) {
      setState(() {
        _currentAmount = parsedAmount;
        // _amountError = error; // Let ShadForm validator handle errors
      });
      widget.onAmountChanged(_currentAmount);
    }
  }

  String _formatCurrency(double amount) {
    return NumberFormat.currency(locale: 'en_GB', symbol: '£').format(amount);
  }

  double _calculateExpectedReturn() {
    if (widget.selectedFundingType == 'discretionary') {
      return _currentAmount * _discretionaryInterestRate;
    }
    // For non-discretionary, actual rate depends on selected case stage.
    // This is a placeholder calculation.
    return _currentAmount * 0.10; // Example placeholder for non-discretionary
  }

  String _getInterestRateText() {
    if (widget.selectedFundingType == 'discretionary') {
      return '${(_discretionaryInterestRate * 100).toStringAsFixed(0)}%';
    }
    return 'Case Specific'; // Placeholder
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isMobile = MediaQuery.of(context).size.width <= 600;
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    final subtextColor = isDarkMode ? Colors.grey[400] : Colors.grey[600];

    return ShadForm(
      key: widget.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Step 2: Investment Amount',
            style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          Text(
            'Minimum £1,000 - Maximum £1,000,000 (Example limits)', // Update with actual limits
            style: theme.textTheme.muted.copyWith(fontSize: isMobile ? 12 : 14),
          ),
          const SizedBox(height: 24),
          // Using ShadInputFormField for consistency with Shadcn form validation
          ShadInputFormField(
            id: 'investment_amount_step_widget',
            controller: widget.controller,
            label: const Text('Amount to Invest'),
            leading: Padding(
              padding: const EdgeInsets.only(left: 8.0, right: 4.0),
              child: Text(
                '£',
                style: theme.textTheme.p.copyWith(
                  fontSize: 16,
                  color: subtextColor,
                ),
              ),
            ),
            placeholder: const Text('e.g., 50000'),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
            ],
            validator: (v) {
              if (v == null || v.isEmpty) return 'Please enter an amount.';
              final val = double.tryParse(v);
              if (val == null) return 'Please enter a valid number.';
              if (val <= 0) return 'Amount must be greater than zero.';
              if (val < 1000) return 'Minimum investment is £1,000.'; // Example
              if (val > 1000000)
                return 'Maximum investment is £1,000,000.'; // Example
              return null;
            },
            // onChanged is handled by the controller listener for _currentAmount updates
          ),

          if (_currentAmount > 0) ...[
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Indicative Return',
                        style: theme.textTheme.small.copyWith(
                          color: subtextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatCurrency(_calculateExpectedReturn()),
                        style: theme.textTheme.h4.copyWith(
                          fontWeight: FontWeight.w600,
                          color: textColor,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(isMobile ? 16 : 20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        widget.selectedFundingType == 'non_discretionary'
                            ? LucideIcons.lock
                            : LucideIcons.trendingUp,
                        size: isMobile ? 14 : 16,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        '${_getInterestRateText()} Interest',
                        style: theme.textTheme.small.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
