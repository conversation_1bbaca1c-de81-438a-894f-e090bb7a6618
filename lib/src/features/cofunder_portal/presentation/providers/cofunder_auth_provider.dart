import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/services/cofunder_base_service.dart';

/// Authentication state for co-funders
class CoFunderAuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final RecordModel? user;
  final CoFunderProfile? profile;
  final String? error;

  const CoFunderAuthState({
    this.isAuthenticated = false,
    this.isLoading = false,
    this.user,
    this.profile,
    this.error,
  });

  CoFunderAuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    RecordModel? user,
    CoFunderProfile? profile,
    String? error,
  }) {
    return CoFunderAuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      profile: profile ?? this.profile,
      error: error ?? this.error,
    );
  }

  bool get isCoFunder => user?.data['user_type'] == 'co_funder';
  bool get hasProfile => profile != null;
  bool get isProfileComplete => profile?.isProfileComplete ?? false;
}

/// Co-funder authentication provider
class CoFunderAuthNotifier extends StateNotifier<CoFunderAuthState> {
  final CoFunderBaseService _service;
  final PocketBase _pb;

  CoFunderAuthNotifier(this._service, this._pb)
    : super(const CoFunderAuthState()) {
    _initializeAuth();
  }

  /// Initialize authentication state
  void _initializeAuth() {
    if (_pb.authStore.isValid) {
      final user = _pb.authStore.record;
      if (user != null && user.data['user_type'] == 'co_funder') {
        state = state.copyWith(isAuthenticated: true, user: user);
        _loadCoFunderProfile();
      }
    }
  }

  /// Load co-funder profile
  Future<void> _loadCoFunderProfile() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final profile = await _service.getCurrentCoFunderProfile();

      state = state.copyWith(isLoading: false, profile: profile);
    } catch (e) {
      LoggerService.error('Error loading co-funder profile', e);
      state = state.copyWith(
        isLoading: false,
        error: _service.getErrorMessage(e),
      );
    }
  }

  /// Sign in co-funder
  Future<bool> signIn(String email, String password) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final authData = await _pb
          .collection('users')
          .authWithPassword(email, password);

      if (authData.record.data['user_type'] != 'co_funder') {
        _pb.authStore.clear();
        state = state.copyWith(
          isLoading: false,
          error: 'Invalid user type. This portal is for co-funders only.',
        );
        return false;
      }

      state = state.copyWith(
        isAuthenticated: true,
        isLoading: false,
        user: authData.record,
      );

      // Refresh FCM token after successful co-funder sign in
      try {
        await FirebaseApiService.refreshToken();
        LoggerService.info('FCM token refreshed for co-funder');
      } catch (e) {
        LoggerService.warning('Failed to refresh FCM token for co-funder: $e');
        // Don't fail the sign in process if FCM token refresh fails
      }

      await _loadCoFunderProfile();
      return true;
    } catch (e) {
      LoggerService.error('Co-funder sign in error', e);
      state = state.copyWith(
        isLoading: false,
        error: _service.getErrorMessage(e),
      );
      return false;
    }
  }

  /// Sign out co-funder
  Future<void> signOut() async {
    try {
      // Clear FCM token before signing out
      try {
        await FirebaseApiService.clearToken();
        LoggerService.info('FCM token cleared for co-funder sign out');
      } catch (e) {
        LoggerService.warning('Failed to clear FCM token for co-funder: $e');
        // Continue with sign out even if FCM token clearing fails
      }

      _pb.authStore.clear();
      state = const CoFunderAuthState();
      LoggerService.info('Co-funder signed out successfully');
    } catch (e) {
      LoggerService.error('Error signing out co-funder', e);
    }
  }

  /// Create or update co-funder profile
  Future<bool> createOrUpdateProfile({
    Map<String, dynamic>? notificationPreferences,
    double? fundingCapacity,
    List<String>? preferredSectors,
    String? riskTolerance,
    int? currentLevel,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      CoFunderProfile profile;

      if (state.profile == null) {
        // Create new profile
        profile = await _service.createCoFunderProfile(
          currentLevel: currentLevel ?? 1,
          notificationPreferences: notificationPreferences,
        );
      } else {
        // Update existing profile
        profile = await _service.updateCoFunderProfile(
          currentLevel: currentLevel,
          notificationPreferences: notificationPreferences,
          fundingCapacity: fundingCapacity,
          preferredSectors: preferredSectors,
          riskTolerance: riskTolerance,
        );
      }

      state = state.copyWith(isLoading: false, profile: profile);

      return true;
    } catch (e) {
      LoggerService.error('Error creating/updating co-funder profile', e);
      state = state.copyWith(
        isLoading: false,
        error: _service.getErrorMessage(e),
      );
      return false;
    }
  }

  /// Refresh authentication state
  Future<void> refresh() async {
    if (_pb.authStore.isValid) {
      await _loadCoFunderProfile();
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for co-funder authentication
final coFunderAuthProvider =
    StateNotifierProvider<CoFunderAuthNotifier, CoFunderAuthState>((ref) {
      final service = CoFunderBaseService();
      final pocketBase = PocketBaseService().pb;
      return CoFunderAuthNotifier(service, pocketBase);
    });

/// Provider for current co-funder profile
final currentCoFunderProfileProvider = Provider<CoFunderProfile?>((ref) {
  final authState = ref.watch(coFunderAuthProvider);
  return authState.profile;
});

/// Provider for co-funder authentication status
final isCoFunderAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(coFunderAuthProvider);
  return authState.isAuthenticated && authState.isCoFunder;
});
