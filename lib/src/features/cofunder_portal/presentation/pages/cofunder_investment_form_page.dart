import 'dart:async'; // For StreamSubscription
import 'dart:convert'; // For base64Decode
import 'dart:typed_data'; // For Uint8List
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http; // For MultipartFile
import 'package:pocketbase/pocketbase.dart'; // For PocketBase types
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:intl/intl.dart'; // For DateFormat
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart'; // Import PocketBaseService
import 'package:three_pay_group_litigation_platform/src/core/theme/app_theme.dart'; // For consistent styling
// Removed incorrect imports for AnimatedThreeLoader and AppConstants
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/application/bloc/cofounder_progress_bloc.dart'; // Import the new BLoC
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_profile_page.dart'; // For profile navigation
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/educational_content_list_page.dart'; // For navigation

import '../widgets/investment_form_steps/funding_option_step_widget.dart';
import '../widgets/investment_form_steps/funding_amount_step_widget.dart';
import '../widgets/investment_form_steps/agreement_step_widget.dart';
import 'view_investment_cases_page.dart'; // Import for routeName

class CofunderInvestmentFormPage extends StatefulWidget {
  static const String routeName = '/cofunder-investment-form';
  final String? caseId; // Made caseId optional

  const CofunderInvestmentFormPage({
    super.key,
    this.caseId,
  }); // Made caseId optional

  @override
  State<CofunderInvestmentFormPage> createState() =>
      _CofunderInvestmentFormPageState();
}

class _CofunderInvestmentFormPageState
    extends State<CofunderInvestmentFormPage> {
  int _currentStep = 0;
  bool _isSubmitting = false; // Loading state for submission
  bool _isLoadingProfile = true; // Loading state for initial profile check
  bool _isLoadingProgress = true; // Loading state for CofounderProgressBloc
  int? _coFunderLevel;
  CofounderProgress? _cofounderProgress;
  String? _profileError;
  // State for agreement documents
  String? _lendersApplicationContent;
  String? _declarationOfInterestContent;
  String? _nonRecourseAgreementContent;
  String? _coFunderName; // To replace placeholders in documents
  bool _isLoadingAgreements = true;
  String? _agreementLoadingError;

  late CofounderProgressBloc _cofounderProgressBloc;
  StreamSubscription? _progressBlocSubscription;

  final _formKeys = [
    GlobalKey<ShadFormState>(), // Step 0: Funding Type
    GlobalKey<ShadFormState>(), // Step 1: Amount
    GlobalKey<ShadFormState>(), // Step 2: Lender's Agreement
    GlobalKey<ShadFormState>(), // Step 3: Declaration of Interest
    GlobalKey<ShadFormState>(), // Step 4: Non-Recourse Agreement
  ];

  // Form data
  String? _selectedFundingType;
  String? _activeCaseId; // To store the caseId for the investment
  final TextEditingController _investmentAmountController =
      TextEditingController();
  bool _agreedToLendersForm = false;
  String? _lendersFormSignatureBase64;
  bool _agreedToDeclarationForm = false;
  String? _declarationFormSignatureBase64;
  bool _agreedToNonRecourseForm = false;
  String? _nonRecourseFormSignatureBase64;
  final TextEditingController _investorNotesController =
      TextEditingController(); // Controller for notes

  @override
  void initState() {
    super.initState();
    _activeCaseId = widget.caseId;
    // The page now creates and owns this BLoC instance.
    _cofounderProgressBloc = CofounderProgressBloc(PocketBaseService());

    _progressBlocSubscription = _cofounderProgressBloc.stream.listen((state) {
      if (!mounted) return;

      if (state is CofounderProgressLoading ||
          state is CofounderProgressInitial) {
        setState(() {
          _isLoadingProgress = true;
          // Optionally clear _profileError here if loading starts, to prevent stale errors
          // _profileError = null;
        });
      } else if (state is CofounderProgressLoaded) {
        CofounderProgress? loadedProgress = state.progress;
        String? newProfileError;
        List<String> unmetConditions = [];

        if (loadedProgress == null) {
          newProfileError =
              "Could not verify your co-funding eligibility (progress data missing). Please try again.";
        } else {
          // Debug print to see the actual values
          // ignore: avoid_print
          print("DEBUG: CofounderProgress values:");
          // ignore: avoid_print
          print("hasReadAllArticles: ${loadedProgress.hasReadAllArticles}");
          // ignore: avoid_print
          print(
            "hasSubmittedUpgradeRequest: ${loadedProgress.hasSubmittedUpgradeRequest}",
          );
          // ignore: avoid_print
          print(
            "hasActivatedSubscription: ${loadedProgress.hasActivatedSubscription}",
          );
          // ignore: avoid_print
          print("hasSignedNDA: ${loadedProgress.hasSignedNDA}");
          // ignore: avoid_print
          print("isEligibleForClaims: ${loadedProgress.isEligibleForClaims}");
          // ignore: avoid_print
          print("hasLevel3Approved: ${loadedProgress.hasLevel3Approved}");

          // Check each condition and add to unmet conditions if not met
          // Only check if the user is eligible for claims (passed the knowledge test)
          // Other conditions will be met naturally through the user's journey

          // Debug print to show progress values
          // ignore: avoid_print
          print("DEBUG: CofounderProgress values:");
          // ignore: avoid_print
          print("hasReadAllArticles: ${loadedProgress.hasReadAllArticles}");
          // ignore: avoid_print
          print(
            "hasSubmittedUpgradeRequest: ${loadedProgress.hasSubmittedUpgradeRequest}",
          );
          // ignore: avoid_print
          print(
            "hasActivatedSubscription: ${loadedProgress.hasActivatedSubscription}",
          );
          // ignore: avoid_print
          print("hasSignedNDA: ${loadedProgress.hasSignedNDA}");
          // ignore: avoid_print
          print("isEligibleForClaims: ${loadedProgress.isEligibleForClaims}");
          // ignore: avoid_print
          print("hasLevel3Approved: ${loadedProgress.hasLevel3Approved}");

          // Only check if eligible for claims (passed knowledge test)
          if (!loadedProgress.isEligibleForClaims) {
            unmetConditions.add("Pass the knowledge test");
          }

          // Only set error if there are unmet conditions
          if (unmetConditions.isNotEmpty) {
            newProfileError =
                "You have not met all requirements to make an investment commitment. Please complete the following: ${unmetConditions.join(', ')}.";
          } else {
            newProfileError = null; // All checks passed
          }
        }
        setState(() {
          _cofounderProgress = loadedProgress;
          _profileError = newProfileError;
          _isLoadingProgress = false;
        });
      } else if (state is CofounderProgressError) {
        setState(() {
          _profileError = "Error loading your progress: ${state.message}";
          _isLoadingProgress = false;
        });
      }
    });
    _performInitialChecks();
  }

  Future<void> _performInitialChecks() async {
    if (!mounted) return;
    setState(() {
      _isLoadingProfile = true;
      _isLoadingProgress = true; // Also set progress loading to true initially
      _profileError = null;
    });

    final pbService = PocketBaseService();
    final userId = pbService.currentUser?.id;

    if (userId == null) {
      if (mounted) {
        setState(() {
          _isLoadingProfile = false;
          _isLoadingProgress = false; // Stop progress loading if not logged in
          _isLoadingAgreements = false; // Also stop agreement loading
          _profileError = "Not logged in. Cannot proceed with commitment.";
          _agreementLoadingError =
              "User not authenticated. Cannot load agreements.";
        });
      }
      return;
    }

    try {
      final profileRecord = await pbService.client
          .collection('co_funder_profiles')
          .getFirstListItem('user_id="$userId"');
      if (!mounted) return;
      setState(() {
        _coFunderLevel = profileRecord.data['current_level'] as int?;
        _isLoadingProfile = false;
      });
      // After basic profile is loaded, fetch detailed progress and agreement templates
      await _loadAgreementTemplatesAndUserName(); // Fetch agreement content
      if (mounted) {
        _cofounderProgressBloc.add(LoadCofounderProgress());
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoadingProfile = false;
        _isLoadingProgress = false; // Stop progress loading on profile error
        _isLoadingAgreements = false; // Also stop agreement loading
        _profileError = "Error fetching your profile: ${e.toString()}";
        _agreementLoadingError =
            "Profile fetch failed, cannot load agreements.";
      });
      // ignore: avoid_print
      print('Error fetching co-funder profile for form: $e');
    }
  }

  Future<void> _loadAgreementTemplatesAndUserName() async {
    if (!mounted) return;
    setState(() {
      _isLoadingAgreements = true;
      _agreementLoadingError = null;
    });

    final pbService = PocketBaseService();
    final userId = pbService.currentUser?.id;

    if (userId == null) {
      if (mounted) {
        setState(() {
          _isLoadingAgreements = false;
          _agreementLoadingError =
              "User not authenticated. Cannot load agreements.";
        });
      }
      return;
    }

    try {
      // Fetch user's name
      final userRecord = await pbService.client
          .collection('users')
          .getOne(userId);
      _coFunderName =
          userRecord.data['name'] ??
          "${userRecord.data['first_name'] ?? ''} ${userRecord.data['last_name'] ?? ''}"
              .trim();
      if (_coFunderName!.isEmpty) {
        _coFunderName = "Co-Funder";
      }

      final today = DateFormat.yMMMMd().format(DateTime.now());

      // Fetch Lender's Application
      final lendersAppTemplates = await pbService.client
          .collection('document_templates')
          .getList(
            filter: 'type="lenders_application"',
            sort: '-version',
            perPage: 1,
          );
      if (lendersAppTemplates.items.isNotEmpty) {
        String content =
            lendersAppTemplates.items.first.data['content'] ??
            'Content not available.';
        content = content.replaceAll('{{co_funder_name}}', _coFunderName!);
        content = content.replaceAll('{{date}}', today);
        _lendersApplicationContent = content;
      } else {
        _lendersApplicationContent =
            'Lender\'s Application template not found.';
      }

      // Fetch Declaration of Continued Interest
      final declarationTemplates = await pbService.client
          .collection('document_templates')
          .getList(
            filter: 'type="declaration_of_continued_interest"',
            sort: '-version',
            perPage: 1,
          );
      if (declarationTemplates.items.isNotEmpty) {
        String content =
            declarationTemplates.items.first.data['content'] ??
            'Content not available.';
        content = content.replaceAll('{{co_funder_name}}', _coFunderName!);
        content = content.replaceAll('{{date}}', today);
        _declarationOfInterestContent = content;
      } else {
        _declarationOfInterestContent =
            'Declaration of Continued Interest template not found.';
      }

      // Fetch Non-Recourse Agreement
      final nonRecourseTemplates = await pbService.client
          .collection('document_templates')
          .getList(filter: 'type="non_recourse"', sort: '-version', perPage: 1);
      if (nonRecourseTemplates.items.isNotEmpty) {
        String content =
            nonRecourseTemplates.items.first.data['content'] ??
            'Content not available.';
        content = content.replaceAll('{{co_funder_name}}', _coFunderName!);
        content = content.replaceAll('{{date}}', today);
        _nonRecourseAgreementContent = content;
      } else {
        _nonRecourseAgreementContent =
            'Non-Recourse Agreement template not found.';
      }

      if (mounted) {
        setState(() {
          _isLoadingAgreements = false;
        });
      }
    } catch (e) {
      // ignore: avoid_print
      print("Error loading agreement templates: $e");
      if (mounted) {
        setState(() {
          _isLoadingAgreements = false;
          _agreementLoadingError =
              "Failed to load agreement templates: ${e.toString()}";
        });
      }
    }
  }
  // The _validateAllPrerequisites method is now inlined into the BLoC listener.
  // This method can be removed.

  @override
  void dispose() {
    _investmentAmountController.dispose();
    _investorNotesController.dispose();
    _progressBlocSubscription?.cancel();
    _cofounderProgressBloc.close(); // Close the BLoC since this state owns it
    super.dispose();
  }

  void _advanceToStep(int step) {
    setState(() {
      _currentStep = step;
    });
  }

  Future<void> _handleNextButtonPress() async {
    if (!mounted) return;
    if (!_formKeys[_currentStep].currentState!.saveAndValidate()) {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Validation Error'),
          description: const Text(
            'Please correct the errors in the current step.',
          ),
        ),
      );
      return;
    }

    // ignore: avoid_print
    print('--- _handleNextButtonPress ---');
    // ignore: avoid_print
    print('Current Step: $_currentStep');
    // ignore: avoid_print
    print('Selected Funding Type: $_selectedFundingType');
    // ignore: avoid_print
    print('Active Case ID before logic: $_activeCaseId');

    if (_currentStep == 0) {
      // Funding Type step
      if (_selectedFundingType == 'discretionary') {
        // ignore: avoid_print
        print('Branch: Discretionary');
        // For discretionary funding, user doesn't select a specific case.
        // 3Pay makes decisions. Clear any active case ID.
        setState(() {
          _activeCaseId = null;
        });
        // ignore: avoid_print
        print('Active Case ID after discretionary set: $_activeCaseId');
        _advanceToStep(1); // Proceed to Amount step
      } else if (_selectedFundingType == 'non_discretionary') {
        // ignore: avoid_print
        print('Branch: Non-Discretionary');
        // Non-discretionary: user MUST select a specific case.

        // IMPORTANT: Always allow navigation to ViewInvestmentCasesPage
        // ignore: avoid_print
        print(
          'DEBUG: Navigating to ViewInvestmentCasesPage with selectionMode=true',
        );

        final selectedCaseId = await Navigator.pushNamed(
          context,
          ViewInvestmentCasesPage.routeName,
          arguments: {'selectionMode': true},
        );
        // ignore: avoid_print
        print('Selected Case ID from ViewInvestmentCasesPage: $selectedCaseId');

        if (selectedCaseId is String && selectedCaseId.isNotEmpty) {
          if (!mounted) return;
          setState(() {
            _activeCaseId = selectedCaseId;
          });
          // ignore: avoid_print
          print(
            'Active Case ID after non-discretionary selection: $_activeCaseId',
          );
          _advanceToStep(
            1,
          ); // Proceed to Amount step with the new/confirmed caseId
        } else {
          // ignore: avoid_print
          print('Branch: Non-Discretionary - No case selected or backed out');
          // User backed out or no case selected. Stay on current step.
          // Toast removed as per user request.
        }
      } else {
        // ignore: avoid_print
        print('Branch: Fallback - No funding type selected or invalid');
        // Fallback if somehow validation passed without a selection
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Selection Required'),
            description: const Text('Please select a funding type.'),
          ),
        );
      }
    } else if (_currentStep < 4) {
      // For Amount and Agreement steps (1, 2, 3)
      _advanceToStep(_currentStep + 1);
    } else if (_currentStep == 4) {
      // Last agreement step
      _submitInvestment();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }

  Future<void> _submitInvestment() async {
    if (_isSubmitting) return; // Prevent double submission

    setState(() {
      _isSubmitting = true;
    });

    final pb = PocketBaseService().client;
    final userId = PocketBaseService().currentUser?.id;

    if (userId == null) {
      if (!mounted) return;
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Error: Not Logged In'),
          description: const Text(
            'Cannot submit investment. Please log in again.',
          ),
        ),
      );
      setState(() {
        _isSubmitting = false;
      });
      return;
    }

    // --- Fetch CoFunder Profile ID ---
    String? coFunderProfileId;
    try {
      final profileRecord = await pb
          .collection('co_funder_profiles')
          .getFirstListItem('user_id="$userId"');
      coFunderProfileId = profileRecord.id;
    } catch (e) {
      if (!mounted) return;
      // ignore: avoid_print
      print('Error fetching co-funder profile: $e');
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Error: Profile Not Found'),
          description: const Text(
            'Could not find your co-funder profile. Please contact support.',
          ),
        ),
      );
      setState(() {
        _isSubmitting = false;
      });
      return;
    }

    // --- Prepare Data ---
    final amount = double.tryParse(_investmentAmountController.text);
    if (amount == null) {
      if (!mounted) return;
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Error: Invalid Amount'),
          description: const Text('Please enter a valid investment amount.'),
        ),
      );
      setState(() {
        _isSubmitting = false;
      });
      return;
    }

    final body = <String, dynamic>{
      'co_funder_profile_id': coFunderProfileId,
      'case_id': _activeCaseId, // Can be null for discretionary
      'funding_type': _selectedFundingType,
      'amount_committed': amount,
      'lenders_application_form_agreed': _agreedToLendersForm,
      'declaration_of_continued_interest_form_agreed': _agreedToDeclarationForm,
      'non_recourse_form_agreed': _agreedToNonRecourseForm,
      'investor_notes': _investorNotesController.text.trim(),
      'status': 'pending_approval', // Initial status
    };

    // --- Prepare Files ---
    final files = <http.MultipartFile>[];
    try {
      if (_lendersFormSignatureBase64 != null &&
          _lendersFormSignatureBase64!.isNotEmpty) {
        final bytes = base64Decode(_lendersFormSignatureBase64!);
        files.add(
          http.MultipartFile.fromBytes(
            'lenders_application_form_signature',
            bytes,
            filename:
                'signature_lenders_${DateTime.now().millisecondsSinceEpoch}.png',
          ),
        );
      }
      if (_declarationFormSignatureBase64 != null &&
          _declarationFormSignatureBase64!.isNotEmpty) {
        final bytes = base64Decode(_declarationFormSignatureBase64!);
        files.add(
          http.MultipartFile.fromBytes(
            'declaration_of_continued_interest_form_signature',
            bytes,
            filename:
                'signature_declaration_${DateTime.now().millisecondsSinceEpoch}.png',
          ),
        );
      }
      if (_nonRecourseFormSignatureBase64 != null &&
          _nonRecourseFormSignatureBase64!.isNotEmpty) {
        final bytes = base64Decode(_nonRecourseFormSignatureBase64!);
        files.add(
          http.MultipartFile.fromBytes(
            'non_recourse_form_signature',
            bytes,
            filename:
                'signature_nonrecourse_${DateTime.now().millisecondsSinceEpoch}.png',
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      // ignore: avoid_print
      print('Error decoding signature: $e');
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Error: Signature Problem'),
          description: const Text('Could not process one of the signatures.'),
        ),
      );
      setState(() {
        _isSubmitting = false;
      });
      return;
    }

    // --- Submit to PocketBase ---
    try {
      await pb
          .collection('funding_commitments')
          .create(body: body, files: files);

      if (!mounted) return;
      ShadToaster.of(context).show(
        ShadToast(
          title: const Text('Investment Submitted Successfully'),
          description: Text(
            'Your investment commitment for case ${_activeCaseId ?? 'Discretionary'} has been recorded.',
          ),
          duration: const Duration(seconds: 5),
        ),
      );
      Navigator.of(context).pop(); // Go back after successful submission
    } on ClientException catch (e) {
      if (!mounted) return;
      // ignore: avoid_print
      print('PocketBase Submission Error: ${e.statusCode} ${e.response}');
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: Text('Submission Failed (Code: ${e.statusCode})'),
          description: Text(
            e.response['message']?.toString() ??
                'An unknown error occurred during submission.',
          ),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      // ignore: avoid_print
      print('Generic Submission Error: $e');
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Submission Failed'),
          description: const Text(
            'An unexpected error occurred. Please try again.',
          ),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  // Helper to build HTML content display
  Widget _buildHtmlWidget(String? htmlContent) {
    if (htmlContent == null) {
      return const Text('Content not available.');
    }
    // Basic HTML stripping. Consider using flutter_html for proper rendering.
    return SelectableText(
      htmlContent.replaceAll(RegExp(r'<[^>]*>'), ''), // Remove HTML tags
      style: const TextStyle(fontSize: 14),
    );
  }

  Widget _buildStepContent(BuildContext context) {
    // Check for agreement loading/error states specifically for agreement steps
    if (_currentStep >= 2 && _currentStep <= 4) {
      if (_isLoadingAgreements) {
        return const Center(child: CircularProgressIndicator.adaptive());
      }
      if (_agreementLoadingError != null) {
        return ShadAlert.destructive(
          icon: const Icon(LucideIcons.shieldAlert, size: 20), // Corrected Icon
          title: const Text('Error Loading Agreement'),
          description: Text(_agreementLoadingError!),
        );
      }
    }

    switch (_currentStep) {
      case 0:
        return FundingOptionStepWidget(
          formKey: _formKeys[0],
          initialValue: _selectedFundingType,
          onChanged: (value) {
            setState(() {
              _selectedFundingType = value;
              // No automatic navigation here, _handleNextButtonPress will manage flow
            });
          },
        );
      case 1:
        return FundingAmountStepWidget(
          formKey: _formKeys[1],
          controller: _investmentAmountController,
          selectedFundingType: _selectedFundingType,
          onAmountChanged: (amount) {
            // This callback is mostly for the FundingAmountStepWidget's internal state
            // to update its display. The actual amount is read from the controller
            // upon form submission or when _nextStep is called.
            // If you need to react to amount changes immediately in CofunderInvestmentFormPage,
            // you can add logic here.
          },
        );
      case 2: // Lender's Application Form
        return AgreementStepWidget(
          formKey: _formKeys[2],
          stepTitle: "Step 3: Lender's Application Form",
          agreementFormName: "Lender's Application Form",
          agreementStatement:
              "I have read and agree to the Lender's Application form.",
          initialAgreedValue: _agreedToLendersForm,
          onAgreedChanged: (value) {
            setState(() {
              _agreedToLendersForm = value;
            });
          },
          agreementContent: SingleChildScrollView(
            // Ensure content is scrollable
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: _buildHtmlWidget(_lendersApplicationContent),
            ),
          ),
          initialSignatureBase64: _lendersFormSignatureBase64,
          onSignatureSaved: (signatureBase64) {
            setState(() {
              _lendersFormSignatureBase64 = signatureBase64;
            });
          },
        );
      case 3: // Declaration of Continued Interest
        return AgreementStepWidget(
          formKey: _formKeys[3],
          stepTitle: "Step 4: Declaration of Continued Interest",
          agreementFormName: "Declaration of Continued Interest Form",
          agreementStatement:
              "I have read and agree to the Declaration of Continued Interest form.",
          initialAgreedValue: _agreedToDeclarationForm,
          onAgreedChanged: (value) {
            setState(() {
              _agreedToDeclarationForm = value;
            });
          },
          agreementContent: SingleChildScrollView(
            // Ensure content is scrollable
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: _buildHtmlWidget(_declarationOfInterestContent),
            ),
          ),
          initialSignatureBase64: _declarationFormSignatureBase64,
          onSignatureSaved: (signatureBase64) {
            setState(() {
              _declarationFormSignatureBase64 = signatureBase64;
            });
          },
        );
      case 4: // Non-Recourse Form
        return AgreementStepWidget(
          formKey: _formKeys[4],
          stepTitle: "Step 5: Non-Recourse Form",
          agreementFormName: "Non-Recourse Form",
          agreementStatement: "I have read and agree to the Non-Recourse form.",
          initialAgreedValue: _agreedToNonRecourseForm,
          onAgreedChanged: (value) {
            setState(() {
              _agreedToNonRecourseForm = value;
            });
          },
          agreementContent: SingleChildScrollView(
            // Ensure content is scrollable
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: _buildHtmlWidget(_nonRecourseAgreementContent),
            ),
          ),
          initialSignatureBase64: _nonRecourseFormSignatureBase64,
          onSignatureSaved: (signatureBase64) {
            setState(() {
              _nonRecourseFormSignatureBase64 = signatureBase64;
            });
          },
          notesController: _investorNotesController, // Pass the controller
        );
      default:
        return const SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    // Combined loading check
    if (_isLoadingProfile || _isLoadingProgress) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            'Loading Form...',
            style: theme.textTheme.h4.copyWith(color: AppTheme.textOnDark),
          ),
          backgroundColor: AppTheme.primaryColor,
          iconTheme: IconThemeData(color: AppTheme.textOnDark),
        ),
        body: const Center(child: CircularProgressIndicator.adaptive()),
      );
    }

    // Check for errors after loading is complete
    if (_profileError != null) {
      // Debug print to help diagnose the issue
      // ignore: avoid_print
      print("DEBUG: Access denied due to profile error: $_profileError");

      return Scaffold(
        appBar: AppBar(
          title: Text(
            'Access Denied',
            style: theme.textTheme.h4.copyWith(color: AppTheme.textOnDark),
          ),
          backgroundColor: AppTheme.primaryColor,
          iconTheme: IconThemeData(color: AppTheme.textOnDark),
        ),
        body: SingleChildScrollView(
          // Make it scrollable
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ShadAlert.destructive(
                icon: const Icon(LucideIcons.shieldAlert, size: 20),
                title: const Text('Cannot Proceed With funding commitment'),
                description: Text(_profileError!),
              ),
              const SizedBox(height: 24),
              // Always show ProgressChecklist if there's a profile error,
              // providing it with the page's BLoC instance.
              // The checklist will display the current progress status.
              BlocProvider.value(
                value:
                    _cofounderProgressBloc, // Use the page-owned BLoC instance
                child: const ProgressChecklist(),
              ),
              const SizedBox(height: 16), // Adjusted spacing
              // Specific call to action if level is the primary issue known before full progress check
              // This condition might be true if _profileError was set due to _coFunderLevel < 4
              // or if _validateAllPrerequisites also determined this.
              if ((_coFunderLevel ?? 0) < 4 &&
                  (_profileError ?? "").contains("Level 4"))
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: ShadButton.link(
                    child: const Text('Go to Profile to Upgrade Level'),
                    onPressed:
                        () => Navigator.pushNamed(
                          context,
                          CoFunderProfilePage.routeName,
                        ),
                  ),
                ),
              const SizedBox(height: 24),
              ShadButton.outline(
                child: const Text('Go Back'),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _selectedFundingType == 'discretionary'
              ? 'Discretionary Funding'
              : (_activeCaseId != null && _activeCaseId!.isNotEmpty
                  ? 'Commit to Case: $_activeCaseId'
                  : 'Select Case to Fund'),
          style: theme.textTheme.h4.copyWith(color: AppTheme.textOnDark),
        ),
        backgroundColor: AppTheme.primaryColor,
        iconTheme: IconThemeData(color: AppTheme.textOnDark),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Text('Step ${_currentStep + 1} of 5', style: theme.textTheme.muted),
            const SizedBox(height: 20),

            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                final offsetAnimation = Tween<Offset>(
                  begin: const Offset(0.1, 0.0),
                  end: Offset.zero,
                ).animate(animation);
                return FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position: offsetAnimation,
                    child: child,
                  ),
                );
              },
              child: Container(
                key: ValueKey<int>(_currentStep),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: theme.colorScheme.border),
                  borderRadius: theme.radius,
                ),
                child: _buildStepContent(context),
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (_currentStep > 0)
                  ShadButton.outline(
                    onPressed: _previousStep,
                    child: const Text('Previous'),
                  )
                else
                  const SizedBox(),
                ShadButton(
                  onPressed: _isSubmitting ? null : _handleNextButtonPress,
                  child:
                      _isSubmitting
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : Text(
                            _currentStep == 4 ? 'Submit Commitment' : 'Next',
                          ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
} // End of _CofunderInvestmentFormPageState

// --- ProgressChecklist Widget and its helper (MOVED TO TOP LEVEL) ---
class ProgressChecklist extends StatelessWidget {
  const ProgressChecklist({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CofounderProgressBloc, CofounderProgressState>(
      builder: (context, state) {
        if (state is CofounderProgressLoading) {
          // Use standard progress indicator directly
          return const Center(child: CircularProgressIndicator.adaptive());
        }

        if (state is CofounderProgressLoaded) {
          return Card(
            margin: const EdgeInsets.symmetric(vertical: 16), // Adjusted margin
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Complete these steps to become eligible:', // Changed title
                    style: ShadTheme.of(context).textTheme.h4.copyWith(
                      // Use Shadcn theme
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildProgressItem(
                    // This will be a top-level function or static method
                    context,
                    'Read All Articles',
                    state.progress.hasReadAllArticles,
                    'Start your journey by reading all available articles',
                    onTap:
                        () => Navigator.of(context).pushNamed(
                          EducationalContentListPage.routeName,
                        ), // Corrected route
                  ),
                  _buildProgressItem(
                    context,
                    'Complete Your Profile',
                    state
                        .progress
                        .hasLevel3Approved, // Checked if Level 3 is effectively approved (subscription activated)
                    'Submite all upgrade requests pass our KYC checks',
                  ),
                  _buildProgressItem(
                    context,
                    'Activate Subscription',
                    state.progress.hasActivatedSubscription,
                    'Achieve Level 4 and activate your subscription to become an associate.', // Updated level info
                  ),
                  _buildProgressItem(
                    context,
                    'Sign NDA',
                    state.progress.hasSignedNDA,
                    'Sign the Non-Disclosure Agreement (Level 4)', // Added level info
                    // TODO: Add onTap to navigate to NDA page if needed
                  ),
                  _buildProgressItem(
                    context,
                    'Eligible for Claims',
                    state.progress.isEligibleForClaims,
                    'Please pass the knowledge test to become eligible to fund claims.',
                    isLast: true,
                  ),
                ],
              ),
            ),
          );
        }

        return const SizedBox.shrink(); // Should ideally show an error or retry for other states
      },
    );
  }
} // End of ProgressChecklist

// Top-level helper function or static method within ProgressChecklist
Widget _buildProgressItem(
  BuildContext context,
  String title,
  bool isCompleted,
  String description, {
  bool isLast = false,
  VoidCallback? onTap,
}) {
  final theme = ShadTheme.of(context); // Use Shadcn theme
  return GestureDetector(
    onTap: onTap,
    child: Column(
      children: [
        Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color:
                    isCompleted
                        ? theme
                            .colorScheme
                            .primary // Use theme color
                        : theme.colorScheme.muted, // Use theme color
              ),
              child:
                  isCompleted
                      ? Icon(
                        LucideIcons.check,
                        color: theme.colorScheme.primaryForeground,
                        size: 16,
                      ) // Use theme color
                      : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.p.copyWith(
                          // Use theme text style
                          fontWeight: FontWeight.bold,
                          color:
                              onTap != null
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.foreground,
                        ),
                      ),
                      if (onTap != null) ...[
                        const SizedBox(width: 4),
                        Icon(
                          LucideIcons
                              .arrowRight, // Changed to a more common icon
                          size: 14,
                          color: theme.colorScheme.primary.withOpacity(0.7),
                        ),
                      ],
                    ],
                  ),
                  Text(
                    description,
                    style: theme.textTheme.small.copyWith(
                      // Use theme text style
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        if (!isLast)
          Container(
            margin: const EdgeInsets.only(left: 11),
            width: 2,
            height: 24,
            color: theme.colorScheme.border, // Use theme color
          ),
      ],
    ),
  );
}
