import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // For date formatting
import 'package:shadcn_ui/shadcn_ui.dart';
// PocketBase SDK import is no longer needed here directly if service handles it, but model needs it.
// For simplicity, we can keep it if ContentItemModel.fromRecord uses RecordModel from pocketbase package.
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/content_item_model.dart'; // Import the new model
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/educational_content_detail_page.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart'; // Import the service

class EducationalContentListPage extends StatefulWidget {
  static const String routeName = '/cofunder-educational-content';

  const EducationalContentListPage({super.key});

  @override
  State<EducationalContentListPage> createState() =>
      _EducationalContentListPageState();
}

class _EducationalContentListPageState
    extends State<EducationalContentListPage> {
  final PocketBase _pb = PocketBaseService().client; // Use the service instance
  List<ContentItemModel> _contentItems = [];
  bool _isLoading = true;
  String? _error;
  Set<String> _readArticleIds = {}; // To store IDs of read articles
  String? _coFunderProfileId;
  int _currentUserLevel = 0; // Added to store user's level
  int? _selectedFilterLevel; // null for All, 0 for Level 0, etc.
  final PocketBaseService _pbService = PocketBaseService();

  @override
  void initState() {
    super.initState();
    _fetchInitialData();
  }

  Future<void> _fetchInitialData() async {
    setState(() => _isLoading = true);
    await _fetchCoFunderProfileAndReadArticles();
    await _fetchContentItems(); // This will use the fetched _readArticleIds
    if (mounted) {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchCoFunderProfileAndReadArticles() async {
    if (!_pbService.isSignedIn || _pbService.currentUser == null) {
      // Not logged in or no current user, so no read articles to fetch
      if (mounted) setState(() => _readArticleIds = {});
      return;
    }

    try {
      final user = _pbService.currentUser;
      if (user == null) {
        if (mounted) {
          setState(() {
            _error = "User not logged in.";
            _isLoading = false; // Stop loading as we can't proceed
          });
        }
        return;
      }

      // Fetch co-funder profile to get current_level and read articles
      final profileResult = await _pbService.client
          .collection('co_funder_profiles')
          .getFirstListItem(
            'user_id="${user.id}"',
            expand: 'read_educational_content',
          );

      _coFunderProfileId = profileResult.id;
      // Get current_level from the profile
      final int coFunderLevel = profileResult.data['current_level'] ?? 0;

      final List<dynamic> readArticlesData =
          profileResult.expand['read_educational_content'] ?? [];
      final Set<String> readIds =
          readArticlesData
              .whereType<RecordModel>()
              .map((articleRecord) => articleRecord.id)
              .toSet();

      if (mounted) {
        setState(() {
          _readArticleIds = readIds;
          _currentUserLevel =
              coFunderLevel; // Use level from co_funder_profiles
        });
      }
    } catch (e) {
      // ignore: avoid_print
      print(
        "Error fetching co-funder profile, user level, or read articles: $e",
      );
      if (mounted) {
        setState(() {
          _error = "Could not load profile data or read status.";
          _readArticleIds = {};
          _currentUserLevel = 0; // Reset level on error
        });
      }
    }
  }

  Future<void> _fetchContentItems() async {
    // This method might be called after _fetchCoFunderProfileAndReadArticles
    // or independently if a refresh mechanism is added.
    // If called independently and not part of initial load, ensure _isLoading is handled.
    if (mounted && !_isLoading) setState(() => _isLoading = true);
    if (mounted) setState(() => _error = null);

    try {
      String baseFilter = 'type = "educational_module" && status = "published"';
      String levelFilterSegment = "";

      if (_selectedFilterLevel != null) {
        // Show items for the specific selected level OR items meant for all levels (empty target_user_levels)
        levelFilterSegment =
            ' && (target_user_levels = "[]" || target_user_levels ?~ ${_selectedFilterLevel})';
      }
      // If _selectedFilterLevel is null, no additional level filter is applied, showing all published modules of type "educational_module".

      String sortCriteria =
          '+published_at'; // Default to ascending by published date
      if (_selectedFilterLevel != null &&
          (_selectedFilterLevel == 2 ||
              _selectedFilterLevel == 3 ||
              _selectedFilterLevel == 4)) {
        sortCriteria = '-published_at'; // Descending for levels 2, 3, 4
      }

      final result = await _pb
          .collection('content_items')
          .getList(
            filter: '$baseFilter$levelFilterSegment',
            sort: sortCriteria,
          );
      final items =
          result.items
              .map((record) => ContentItemModel.fromRecord(record))
              .toList();
      if (mounted) {
        setState(() {
          _contentItems = items;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to load content: ${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Cofunder Articles',
          style: theme.textTheme.h4.copyWith(
            color: theme.colorScheme.primaryForeground,
          ),
        ),
        backgroundColor: theme.colorScheme.primary,
        iconTheme: IconThemeData(color: theme.colorScheme.primaryForeground),
      ),
      body: RefreshIndicator(
        onRefresh: _fetchInitialData,
        child: Column(
          children: [
            _buildFilterSection(context),
            Expanded(
              child:
                  _isLoading &&
                          _contentItems
                              .isEmpty // Show loader only if list is empty during initial load
                      ? const Center(
                        child: CircularProgressIndicator.adaptive(),
                      )
                      : _error != null
                      ? Center(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            _error!,
                            style: TextStyle(
                              color: theme.colorScheme.destructive,
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                      : _contentItems.isEmpty
                      ? LayoutBuilder(
                        // Ensure "No content" message is scrollable for RefreshIndicator
                        builder: (context, constraints) {
                          return SingleChildScrollView(
                            physics: const AlwaysScrollableScrollPhysics(),
                            child: ConstrainedBox(
                              constraints: BoxConstraints(
                                minHeight: constraints.maxHeight,
                              ),
                              child: Center(
                                child: Text(
                                  'No educational modules found for the selected filter.',
                                  style: theme.textTheme.p,
                                ),
                              ),
                            ),
                          );
                        },
                      )
                      : ListView.builder(
                        physics:
                            const AlwaysScrollableScrollPhysics(), // Ensure list is scrollable for RefreshIndicator
                        padding: const EdgeInsets.all(16.0),
                        itemCount: _contentItems.length,
                        itemBuilder: (context, index) {
                          final item = _contentItems[index];
                          return FadeInListItem(
                            child: _buildContentItemCard(context, item),
                          );
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterSection(BuildContext context) {
    final theme = ShadTheme.of(context);
    final textTheme = theme.textTheme;

    // Define the levels for filtering including "All"
    final List<Map<String, dynamic>> filterLevels = [
      {'label': 'All Content', 'level': null, 'icon': LucideIcons.layoutGrid},
      {'label': 'Level 1', 'level': 1, 'icon': LucideIcons.star},
      {'label': 'Level 2', 'level': 2, 'icon': LucideIcons.star},
      {'label': 'Level 3', 'level': 3, 'icon': LucideIcons.star},
      {'label': 'Level 4', 'level': 4, 'icon': LucideIcons.star},
    ];

    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.background,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter section header
          Padding(
            padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
            child: Row(
              children: [
                Icon(
                  LucideIcons.listFilter,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Filter by Level',
                  style: textTheme.large.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.foreground,
                  ),
                ),
                const Spacer(),
                // Optional: Add a reset button
                if (_selectedFilterLevel != null)
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedFilterLevel = null;
                      });
                      _fetchContentItems();
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          LucideIcons.x,
                          size: 14,
                          color: theme.colorScheme.mutedForeground,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Reset',
                          style: textTheme.small.copyWith(
                            color: theme.colorScheme.mutedForeground,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          // Filter buttons
          Padding(
            padding: const EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  ...filterLevels.map((filter) {
                    final String label = filter['label'];
                    final int? level = filter['level'];
                    final IconData icon = filter['icon'];
                    final bool isActive = _selectedFilterLevel == level;

                    // Determine star fill for level indicators
                    Widget levelIcon =
                        level == null
                            ? Icon(
                              icon,
                              size: 16,
                              color:
                                  isActive
                                      ? theme.colorScheme.primaryForeground
                                      : theme.colorScheme.primary,
                            )
                            : Row(
                              mainAxisSize: MainAxisSize.min,
                              children: List.generate(
                                level,
                                (index) => Icon(
                                  LucideIcons.star,
                                  size: 12,
                                  color:
                                      isActive
                                          ? theme.colorScheme.primaryForeground
                                          : theme.colorScheme.primary,
                                ),
                              ),
                            );

                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        child:
                            isActive
                                ? ShadButton(
                                  size: ShadButtonSize.sm,
                                  onPressed: () {
                                    setState(() {
                                      _selectedFilterLevel = level;
                                    });
                                    _fetchContentItems();
                                  },
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      levelIcon, // Ensure levelIcon is adapted for active state
                                      const SizedBox(width: 6),
                                      Text(
                                        label,
                                        style: TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w500,
                                          color:
                                              theme
                                                  .colorScheme
                                                  .primaryForeground,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                                : ShadButton.outline(
                                  size: ShadButtonSize.sm,
                                  onPressed: () {
                                    setState(() {
                                      _selectedFilterLevel = level;
                                    });
                                    _fetchContentItems();
                                  },
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      levelIcon, // Ensure levelIcon is adapted for inactive state
                                      const SizedBox(width: 6),
                                      Text(
                                        label,
                                        style: TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w500,
                                          color: theme.colorScheme.foreground,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                      ),
                    );
                  }).toList(),

                  // Visual indicator that the list is scrollable
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Container(
                      height: 36,
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            theme.colorScheme.background.withOpacity(0.0),
                            theme.colorScheme.background.withOpacity(0.8),
                          ],
                        ),
                      ),
                      child: Center(
                        child: Icon(
                          LucideIcons.chevronRight,
                          size: 16,
                          color: theme.colorScheme.mutedForeground.withOpacity(
                            0.5,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentItemCard(BuildContext context, ContentItemModel item) {
    final theme = ShadTheme.of(context);
    final textTheme = theme.textTheme;
    final dateFormat = DateFormat.yMMMd();
    final bool hasThumbnail =
        item.thumbnailImage != null && item.thumbnailImage!.isNotEmpty;
    final bool canAccess =
        item.targetUserLevels.isEmpty ||
        item.targetUserLevels.contains(_currentUserLevel);

    // Determine content type icon and color
    IconData contentTypeIcon;
    Color contentTypeColor;
    String contentTypeLabel =
        item.type.isNotEmpty
            ? item.type[0].toUpperCase() +
                item.type.substring(1).replaceAll('_', ' ')
            : 'N/A';

    switch (item.type) {
      case 'blog':
        contentTypeIcon = LucideIcons.fileText;
        contentTypeColor = Colors.blue;
        break;
      case 'podcast':
        contentTypeIcon = LucideIcons.headphones;
        contentTypeColor = Colors.purple;
        break;
      case 'educational_module':
        contentTypeIcon = LucideIcons.graduationCap;
        contentTypeColor = Colors.green;
        break;
      case 'newsletter':
        contentTypeIcon = LucideIcons.mail;
        contentTypeColor = Colors.orange;
        break;
      default:
        contentTypeIcon = LucideIcons.file;
        contentTypeColor = theme.colorScheme.primary;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 4.0),
      child: InkWell(
        onTap: () {
          if (canAccess) {
            Navigator.pushNamed(
              context,
              EducationalContentDetailPage.routeName,
              arguments: item,
            );
          } else {
            // Show locked content dialog
            showDialog(
              context: context,
              builder:
                  (context) => ShadDialog(
                    title: Row(
                      children: [
                        Icon(
                          LucideIcons.lock,
                          size: 18,
                          color: theme.colorScheme.destructive,
                        ),
                        const SizedBox(width: 8),
                        const Text('Content Locked'),
                      ],
                    ),
                    description: Text(
                      'This content requires Level(s) ${item.targetUserLevels.join(", ")} to access. Your current level is $_currentUserLevel. Please upgrade your level to view this content.',
                    ),
                    actions: [
                      ShadButton(
                        child: const Text('OK'),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
            );
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: ShadCard(
          radius: BorderRadius.circular(12),
          shadows: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color:
                canAccess
                    ? theme.colorScheme.border
                    : theme.colorScheme.destructive.withOpacity(0.2),
            width: 1,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top section with title, type badge, and read status
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Content type and read status row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Content type badge
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: contentTypeColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                contentTypeIcon,
                                size: 14,
                                color: contentTypeColor,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                contentTypeLabel,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: contentTypeColor,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Read status badge
                        if (_readArticleIds.contains(item.id))
                          ShadBadge.outline(
                            backgroundColor: Colors.green.withOpacity(0.1),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  LucideIcons.check,
                                  size: 12,
                                  color: Colors.green,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Read',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // Title with improved styling
                    Text(
                      item.title,
                      style: textTheme.h4.copyWith(
                        fontWeight: FontWeight.bold,
                        height: 1.2,
                        color:
                            canAccess
                                ? theme.colorScheme.foreground
                                : theme.colorScheme.foreground.withOpacity(0.7),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    // Summary with improved styling
                    Text(
                      item.summary ?? 'No summary available.',
                      style: textTheme.muted.copyWith(
                        height: 1.5,
                        color:
                            canAccess
                                ? theme.colorScheme.foreground.withOpacity(0.8)
                                : theme.colorScheme.foreground.withOpacity(0.5),
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Image section
              Stack(
                children: [
                  Hero(
                    tag: 'content_thumbnail_${item.id}',
                    child: Container(
                      height: 180,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.muted,
                        image:
                            hasThumbnail && item.getImageUrl(_pb) != null
                                ? DecorationImage(
                                  image: NetworkImage(item.getImageUrl(_pb)!),
                                  fit: BoxFit.cover,
                                  colorFilter:
                                      !canAccess
                                          ? ColorFilter.mode(
                                            Colors.black.withOpacity(0.2),
                                            BlendMode.darken,
                                          )
                                          : null,
                                )
                                : null,
                      ),
                      alignment: Alignment.center,
                      child:
                          !hasThumbnail || item.getImageUrl(_pb) == null
                              ? Icon(
                                LucideIcons.image,
                                size: 60,
                                color: theme.colorScheme.mutedForeground
                                    .withOpacity(0.5),
                              )
                              : null,
                    ),
                  ),

                  // Lock overlay for locked content
                  if (!canAccess)
                    Positioned.fill(
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.background.withOpacity(
                              0.8,
                            ),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: Icon(
                            LucideIcons.lock,
                            size: 32,
                            color: theme.colorScheme.destructive,
                          ),
                        ),
                      ),
                    ),

                  // Published date overlay
                  if (item.publishedAt != null)
                    Positioned(
                      bottom: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.background.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              LucideIcons.calendar,
                              size: 12,
                              color: theme.colorScheme.mutedForeground,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              dateFormat.format(item.publishedAt!),
                              style: textTheme.small.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),

              // Bottom section with tags and levels
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Tags
                    if (item.tags.isNotEmpty) ...[
                      Wrap(
                        spacing: 6.0,
                        runSpacing: 6.0,
                        children:
                            item.tags
                                .map(
                                  (tag) => ShadBadge.outline(
                                    backgroundColor: theme.colorScheme.secondary
                                        .withOpacity(0.1),
                                    child: Text(
                                      tag,
                                      style: textTheme.small.copyWith(
                                        color: theme.colorScheme.secondary,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                )
                                .toList(),
                      ),
                      const SizedBox(height: 12),
                    ],

                    // User levels row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Level requirements
                        Row(
                          children: [
                            Icon(
                              LucideIcons.users,
                              size: 14,
                              color: theme.colorScheme.mutedForeground,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              item.targetUserLevels.isNotEmpty
                                  ? 'Required Level${item.targetUserLevels.length > 1 ? 's' : ''}: '
                                  : 'Available to All Levels',
                              style: textTheme.small.copyWith(
                                color: theme.colorScheme.mutedForeground,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),

                        // Level badges
                        if (item.targetUserLevels.isNotEmpty)
                          Flexible(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children:
                                  item.targetUserLevels.map((level) {
                                    final bool hasLevel =
                                        level <= _currentUserLevel;
                                    return Padding(
                                      padding: const EdgeInsets.only(left: 4),
                                      child: Container(
                                        width: 24,
                                        height: 24,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color:
                                              hasLevel
                                                  ? theme.colorScheme.primary
                                                  : theme
                                                      .colorScheme
                                                      .destructive
                                                      .withOpacity(0.1),
                                          border: Border.all(
                                            color:
                                                hasLevel
                                                    ? theme.colorScheme.primary
                                                    : theme
                                                        .colorScheme
                                                        .destructive,
                                            width: 1,
                                          ),
                                        ),
                                        alignment: Alignment.center,
                                        child: Text(
                                          level.toString(),
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            color:
                                                hasLevel
                                                    ? theme
                                                        .colorScheme
                                                        .primaryForeground
                                                    : theme
                                                        .colorScheme
                                                        .destructive,
                                          ),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                            ),
                          ),
                      ],
                    ),

                    // Visual indicator for tappable card
                    const SizedBox(height: 12),
                    Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color:
                              canAccess
                                  ? theme.colorScheme.primary.withOpacity(0.1)
                                  : theme.colorScheme.destructive.withOpacity(
                                    0.1,
                                  ),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              canAccess
                                  ? LucideIcons.arrowRight
                                  : LucideIcons.lock,
                              size: 14,
                              color:
                                  canAccess
                                      ? theme.colorScheme.primary
                                      : theme.colorScheme.destructive,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              canAccess ? 'View Content' : 'Locked Content',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color:
                                    canAccess
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.destructive,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Simple Fade-in animation widget for list items
class FadeInListItem extends StatefulWidget {
  final Widget child;
  const FadeInListItem({super.key, required this.child});

  @override
  State<FadeInListItem> createState() => _FadeInListItemState();
}

class _FadeInListItemState extends State<FadeInListItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeIn));
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _opacityAnimation,
      child: SlideTransition(position: _slideAnimation, child: widget.child),
    );
  }
}
