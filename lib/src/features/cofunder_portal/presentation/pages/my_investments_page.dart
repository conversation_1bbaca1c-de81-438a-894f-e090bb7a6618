import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:intl/intl.dart'; // For date formatting
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:pocketbase/pocketbase.dart'; // For RecordModel
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/investment_case_detail_page.dart'; // For navigation

// Data model
class InvestmentCommitment {
  final String id;
  final String caseTitle;
  final double amountCommitted;
  final DateTime commitmentDate;
  final String fundingType; // 'discretionary' or 'non_discretionary'
  final String status; // 'Pending Approval', 'Active', 'Completed'
  final double frfrEarned;
  final String?
  originalCaseId; // To store the ID of the related funding_application

  InvestmentCommitment({
    required this.id,
    required this.caseTitle,
    required this.amountCommitted,
    required this.commitmentDate,
    required this.fundingType,
    required this.status,
    required this.frfrEarned,
    this.originalCaseId,
  });

  factory InvestmentCommitment.fromRecord(
    RecordModel record,
    String caseTitleOverride,
  ) {
    String? idFromData = record.data['case_id'] as String?;

    if (record.data['funding_type'] != 'discretionary' &&
        (idFromData == null || idFromData.isEmpty)) {}
    return InvestmentCommitment(
      id: record.id,
      caseTitle: caseTitleOverride,
      amountCommitted:
          (record.data['amount_committed'] as num?)?.toDouble() ?? 0.0,
      commitmentDate: DateTime.tryParse(record.created) ?? DateTime.now(),
      fundingType: record.data['funding_type'] as String? ?? 'unknown',
      status: record.data['status'] as String? ?? 'Unknown',
      frfrEarned: (record.data['frfr_earned'] as num?)?.toDouble() ?? 0.0,
      originalCaseId: idFromData,
    );
  }
}

class MyInvestmentsPage extends StatefulWidget {
  const MyInvestmentsPage({super.key});

  static const String routeName = '/my-investments';

  @override
  State<MyInvestmentsPage> createState() => _MyInvestmentsPageState();
}

class _MyInvestmentsPageState extends State<MyInvestmentsPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  List<InvestmentCommitment> _investments = [];
  bool _isLoading = true;
  String? _errorMessage;

  double get _totalAmountInvested =>
      _investments.fold(0.0, (sum, item) => sum + item.amountCommitted);
  double get _totalFrfrEarned =>
      _investments.fold(0.0, (sum, item) => sum + item.frfrEarned);

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _fetchInvestments();
  }

  Future<void> _fetchInvestments() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final pbService = PocketBaseService();
    final userId = pbService.currentUser?.id;

    if (userId == null) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = "User not authenticated. Please log in.";
        });
      }
      return;
    }

    try {
      RecordModel coFunderProfile;
      try {
        coFunderProfile = await pbService.client
            .collection('co_funder_profiles')
            .getFirstListItem('user_id="$userId"');
      } catch (e) {
        throw Exception("Co-funder profile not found.");
      }
      final coFunderProfileId = coFunderProfile.id;

      final result = await pbService.client
          .collection('funding_commitments')
          .getList(
            filter: 'co_funder_profile_id="$coFunderProfileId"',
            sort: '-created',
            expand: 'case_id',
          );

      final List<InvestmentCommitment> fetchedInvestments = [];
      for (var record in result.items) {
        String caseTitle = "Discretionary Investment";

        if (record.expand.containsKey('case_id')) {
        } else {}

        if (record.expand.containsKey('case_id') &&
            record.expand['case_id'] != null &&
            (record.expand['case_id'] as List).isNotEmpty) {
          final RecordModel fundingApplicationRecord =
              (record.expand['case_id'] as List<RecordModel>).first;

          caseTitle =
              fundingApplicationRecord.data['claim_title'] as String? ??
              'Case Title Not Found in Funding App (Expanded)';
        } else if (record.data['funding_type'] != 'discretionary' &&
            record.data['case_id'] != null &&
            (record.data['case_id'] as String).isNotEmpty) {
          try {
            final fundingApplicationRecord = await pbService.client
                .collection('funding_applications')
                .getOne(record.data['case_id']);

            caseTitle =
                fundingApplicationRecord.data['case_title'] as String? ??
                'Case Title Not Found in Funding App (Direct)';
          } catch (e) {
            caseTitle = "Case Details Unavailable";
          }
        }

        fetchedInvestments.add(
          InvestmentCommitment.fromRecord(record, caseTitle),
        );
      }

      if (mounted) {
        setState(() {
          _investments = fetchedInvestments;
          _isLoading = false;
          if (_investments.isNotEmpty) {
            _animationController.forward();
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = "Failed to load investments: ${e.toString()}";
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final currencyFormat = NumberFormat.currency(locale: 'en_GB', symbol: '£');
    final dateFormat = DateFormat('dd MMM yyyy');

    return Scaffold(
      appBar: AppBar(
        title: Text('My Funding Portfolio', style: theme.textTheme.h2),
        backgroundColor: theme.colorScheme.background,
        elevation: 0,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator.adaptive())
                : _errorMessage != null
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ShadAlert.destructive(
                        icon: const Icon(LucideIcons.shieldAlert, size: 20),
                        title: const Text('Error'),
                        description: Text(_errorMessage!),
                      ),
                      const SizedBox(height: 16),
                      ShadButton(
                        onPressed: _fetchInvestments,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
                : _investments.isEmpty
                ? Center(
                  child: Text(
                    'You have not made any investment commitments yet.',
                    style: theme.textTheme.p,
                    textAlign: TextAlign.center,
                  ),
                )
                : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSummarySection(theme, currencyFormat),
                    const SizedBox(height: 24),
                    Text('My Commitments', style: theme.textTheme.h4),
                    const SizedBox(height: 16),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _investments.length,
                        itemBuilder: (context, index) {
                          final investment = _investments[index];
                          return FadeTransition(
                            opacity: _fadeAnimation,
                            child: _buildInvestmentItem(
                              investment,
                              theme,
                              currencyFormat,
                              dateFormat,
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
      ),
    );
  }

  Widget _buildSummarySection(
    ShadThemeData theme,
    NumberFormat currencyFormat,
  ) {
    return ShadCard(
      title: Text('Portfolio Overview', style: theme.textTheme.h3),
      description: Text(
        'Summary of your investment activities.',
        style: theme.textTheme.muted,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Total Amount Invested:', style: theme.textTheme.large),
                Text(
                  currencyFormat.format(_totalAmountInvested),
                  style: theme.textTheme.large.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Total FRFR Earned:', style: theme.textTheme.large),
                Text(
                  currencyFormat.format(_totalFrfrEarned),
                  style: theme.textTheme.large.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvestmentItem(
    InvestmentCommitment investment,
    ShadThemeData theme,
    NumberFormat currencyFormat,
    DateFormat dateFormat,
  ) {
    return ShadCard(
      title: Text(investment.caseTitle, style: theme.textTheme.h4),
      description: Text(
        'Committed on: ${dateFormat.format(investment.commitmentDate)}',
        style: theme.textTheme.muted,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow(
              theme,
              'Amount Committed:',
              currencyFormat.format(investment.amountCommitted),
            ),
            _buildDetailRow(
              theme,
              'Funding Type:',
              investment.fundingType.capitalize(),
            ),
            _buildDetailRow(theme, 'Status:', investment.status),
            _buildDetailRow(
              theme,
              'FRFR Earned:',
              currencyFormat.format(investment.frfrEarned),
            ),
          ],
        ),
      ),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ShadButton.outline(
            child: const Text('View Details'),
            onPressed: () {
              if (investment.originalCaseId != null &&
                  investment.originalCaseId!.isNotEmpty) {
                Navigator.pushNamed(
                  context,
                  InvestmentCaseDetailPage.routeName,
                  arguments: {'caseId': investment.originalCaseId!},
                );
              } else {
                String message =
                    'Case details are not available. OriginalCaseID is ${investment.originalCaseId}.';
                if (investment.fundingType == 'discretionary') {
                  message =
                      'This is a discretionary investment and does not have a specific case detail page.';
                } else if (investment.originalCaseId == null) {
                  message =
                      'Case ID is missing for this non-discretionary investment. (originalCaseId is null)';
                } else if (investment.originalCaseId!.isEmpty) {
                  message =
                      'Case ID is empty for this non-discretionary investment. (originalCaseId is empty string)';
                }
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(message),
                    duration: const Duration(seconds: 3),
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(ShadThemeData theme, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: theme.textTheme.small),
          Text(
            value,
            style: theme.textTheme.small.copyWith(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    if (isEmpty) {
      return this;
    }
    return "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
  }
}
