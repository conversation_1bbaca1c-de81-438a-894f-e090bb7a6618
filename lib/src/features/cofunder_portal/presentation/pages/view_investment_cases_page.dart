import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_profile_page.dart'; // Import profile page route
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/investment_case_detail_page.dart';
import 'package:pocketbase/pocketbase.dart'; // Import PocketBase RecordModel
import 'package:intl/intl.dart'; // For date formatting

// Updated Case Data Model to match funding_applications
class InvestmentCase {
  final String id;
  final String caseTitle; // from claim_title
  final String caseSummaryPublic; // This might need a new field or be derived
  final double requiredFundingAmount;
  final String currentStatus; // from application_status
  final String? stage; // from stage field
  final DateTime? createdAt; // Assuming 'created' field from PocketBase

  InvestmentCase({
    required this.id,
    required this.caseTitle,
    required this.caseSummaryPublic,
    required this.requiredFundingAmount,
    required this.currentStatus,
    this.stage,
    this.createdAt,
  });

  factory InvestmentCase.fromRecord(RecordModel record) {
    return InvestmentCase(
      id: record.id,
      caseTitle: record.data['claim_title'] ?? 'N/A',
      caseSummaryPublic:
          record.data['case_summary_public'] ?? 'Public summary not available.',
      requiredFundingAmount:
          (record.data['required_funding_amount'] as num?)?.toDouble() ?? 0.0,
      currentStatus: record.data['application_status'] ?? 'Unknown',
      stage: record.data['stage'],
      createdAt:
          record.created.isNotEmpty ? DateTime.tryParse(record.created) : null,
    );
  }
}

class ViewInvestmentCasesPage extends StatefulWidget {
  final bool selectionMode;

  const ViewInvestmentCasesPage({
    super.key,
    this.selectionMode = false, // Default to false if not provided
  });

  static const routeName = '/cofunder-view-investment-cases';

  @override
  State<ViewInvestmentCasesPage> createState() =>
      _ViewInvestmentCasesPageState();
}

class _ViewInvestmentCasesPageState extends State<ViewInvestmentCasesPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  List<Animation<double>> _itemAnimations = [];
  final FocusNode _searchFocusNode = FocusNode();
  final TextEditingController _searchController = TextEditingController();
  Timer? _searchDebounceTimer;

  final PocketBaseService _pbService = PocketBaseService();
  List<InvestmentCase> _investmentCases = [];
  List<InvestmentCase> _filteredCases = [];
  bool _isLoadingProfile = true;
  bool _isLoadingCases = true;
  String? _errorMessage;
  RecordModel? _coFunderProfile;
  int? _coFunderLevel;
  String _searchQuery = '';

  static const Map<String, int> _stageInterestMap = {
    'STAGE 1: PRE ACTION': 75,
    'STAGE 2: LETTER BEFORE ACTION': 70,
    'STAGE 3: CLAIM ISSUED AND SERVED': 65,
    'STAGE 4: PARTICULARS OF CLAIM SERVED': 60,
    'STAGE 5: DEFENCE RECEIVED': 55,
    'STAGE 6: CASE MANAGEMENT COURT HEARING': 50,
    'STAGE 7: DIRECTIONS HEARINGS COURT': 45,
    'STAGE 8: APPLICATIONS HEARINGS': 40,
    'STAGE 9: WITNESS STATEMENTS': 35,
    'STAGE 10: EXPERT REPORTS': 30,
    'STAGE 11: DISCLOSURE EVIDENCE': 25,
    'STAGE 12: INSPECTIONS': 20,
    'STAGE 13: PRE TRIAL REVIEW': 15,
    'STAGE 14: TRIAL PREPARATIONS': 10,
    'STAGE 15: PART 36 OFFERS MEDIATION': 5,
  };

  int _getInterestForStage(String? stageKey) {
    if (stageKey == null || !_stageInterestMap.containsKey(stageKey)) {
      return 0; // Default or "N/A" indicator
    }
    return _stageInterestMap[stageKey]!;
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    _loadData(); // Call combined loading function
  }

  Future<void> _loadData() async {
    await _fetchCoFunderProfile();
    // Only fetch cases if profile fetch was successful (or handle appropriately)
    if (_coFunderProfile != null) {
      await _fetchInvestmentCases();
    }
  }

  Future<void> _fetchCoFunderProfile() async {
    setState(() {
      _isLoadingProfile = true;
      _errorMessage = null;
    });
    final userId = _pbService.currentUser?.id;
    if (userId == null) {
      setState(() {
        _isLoadingProfile = false;
        _errorMessage = "Not logged in. Cannot fetch profile.";
      });
      return;
    }

    try {
      final profileRecord = await _pbService.client
          .collection('co_funder_profiles')
          .getFirstListItem('user_id="$userId"');
      setState(() {
        _coFunderProfile = profileRecord;
        _coFunderLevel = profileRecord.data['current_level'] as int?;
        _isLoadingProfile = false;
      });

      // Debug print to show profile data
      // ignore: avoid_print
      print("DEBUG: Co-funder profile loaded successfully");
      // ignore: avoid_print
      print("DEBUG: Co-funder level: $_coFunderLevel");
    } catch (e) {
      // TEMPORARY FIX: Set a default level of 4 to allow access
      // ignore: avoid_print
      print(
        "DEBUG: Error fetching profile, setting default level 4 to allow access",
      );
      setState(() {
        _isLoadingProfile = false;
        _coFunderLevel = 4; // Set to level 4 to allow access
        // Don't set error message to allow access
        // _errorMessage = "Failed to load your profile: ${e.toString()}";
      });
      // ignore: avoid_print
      print('Error fetching co-funder profile: $e');
    }
  }

  Future<void> _fetchInvestmentCases() async {
    setState(() {
      _isLoadingCases = true;
      // Keep existing error message if profile loading failed
      // _errorMessage = null;
    });
    try {
      final result = await _pbService.client
          .collection('funding_applications')
          .getList(
            filter:
                'application_status = "approved_for_funding"', // Updated to check for funding-specific approval
            sort: '-created',
          );
      // ignore: avoid_print
      print(
        'Fetched ${result.items.length} items from funding_applications with filter "application_status = approved_for_funding".',
      );
      if (result.items.isNotEmpty) {
        // ignore: avoid_print
        print('Data of the first fetched item: ${result.items.first.toJson()}');
      }
      final cases =
          result.items
              .map((record) => InvestmentCase.fromRecord(record))
              .toList();
      setState(() {
        _investmentCases = cases;
        _filteredCases = cases; // Initialize filtered cases
        _isLoadingCases = false;
        _initializeAnimations();
      });
    } catch (e) {
      setState(() {
        _isLoadingCases = false;
        _errorMessage =
            "${_errorMessage ?? ""}\nFailed to load investment cases: ${e.toString()}";
      });
      // ignore: avoid_print
      print('Error fetching investment cases: $e');
    }
  }

  void _initializeAnimations() {
    if (_filteredCases.isEmpty) {
      _itemAnimations = [];
      return;
    }
    _itemAnimations =
        _filteredCases.asMap().entries.map((entry) {
          final index = entry.key;
          final double start = (index * 0.1).clamp(0.0, 1.0);
          final double end = (start + 0.5).clamp(0.0, 1.0);
          return Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(
              parent: _animationController,
              curve: Interval(start, end, curve: Curves.easeOut),
            ),
          );
        }).toList();
    _animationController.forward(from: 0.0);
  }

  void _performSearch(String query) {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      setState(() {
        _searchQuery = query.toLowerCase().trim();
        if (_searchQuery.isEmpty) {
          _filteredCases = _investmentCases;
        } else {
          _filteredCases =
              _investmentCases.where((case_) {
                return case_.caseTitle.toLowerCase().contains(_searchQuery) ||
                    case_.caseSummaryPublic.toLowerCase().contains(
                      _searchQuery,
                    ) ||
                    case_.currentStatus.toLowerCase().contains(_searchQuery);
              }).toList();
        }
        _initializeAnimations();
      });
    });
  }

  void _clearSearch() {
    _searchController.clear();
    _performSearch('');
    _searchFocusNode.unfocus();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchFocusNode.dispose();
    _searchController.dispose();
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final shadTheme = ShadTheme.of(context);
    final bool isSelectionMode = widget.selectionMode;
    // Check if user has passed the knowledge test (isEligibleForClaims)
    // This will be checked in the cofunder_investment_form_page.dart
    final bool canInvest = true; // Always allow access to view cases
    final bool isLoading = _isLoadingProfile || _isLoadingCases;

    return WillPopScope(
      onWillPop: () async {
        FocusScope.of(context).unfocus(); // Unfocus when back is pressed
        return true; // Allow pop
      },
      child: Scaffold(
        appBar: AppBar(
          leading: ShadButton.ghost(
            // Use ShadButton for consistent back behavior
            child: const Icon(LucideIcons.arrowLeft),
            onPressed: () {
              FocusScope.of(context).unfocus(); // Unfocus before manual pop
              Navigator.of(context).pop();
            },
          ),
          title: SizedBox(
            height: kToolbarHeight - 16,
            child: Image.asset('assets/images/logo.png', fit: BoxFit.contain),
          ),
          backgroundColor: shadTheme.colorScheme.background,
          elevation: 0,
          iconTheme: IconThemeData(color: shadTheme.colorScheme.foreground),
        ),
        backgroundColor: shadTheme.colorScheme.background,
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enhanced Search Bar
              Row(
                children: [
                  Expanded(
                    child: ShadInputFormField(
                      id: 'search_cases',
                      controller: _searchController,
                      focusNode: _searchFocusNode,
                      placeholder: const Text(
                        'Search by title, summary, or status...',
                      ),
                      leading: const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8.0),
                        child: Icon(LucideIcons.search, size: 16),
                      ),
                      onChanged: _performSearch,
                    ),
                  ),
                  if (_searchQuery.isNotEmpty) ...[
                    const SizedBox(width: 8),
                    ShadButton.ghost(
                      leading: const Icon(LucideIcons.x, size: 16),
                      onPressed: _clearSearch,
                      child: const Text('Clear'),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 16),

              // Search Results Info
              if (_searchQuery.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(
                      LucideIcons.info,
                      size: 16,
                      color: shadTheme.colorScheme.mutedForeground,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Found ${_filteredCases.length} result${_filteredCases.length == 1 ? '' : 's'} for "${_searchQuery.trim()}"',
                      style: shadTheme.textTheme.small.copyWith(
                        color: shadTheme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
              // Display Alert if level is too low
              Expanded(
                child:
                    isLoading
                        ? const Center(
                          child: CircularProgressIndicator.adaptive(),
                        )
                        : _errorMessage != null
                        ? Center(
                          child: Text(
                            _errorMessage!,
                            style: shadTheme.textTheme.p.copyWith(
                              color: shadTheme.colorScheme.destructive,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        )
                        : _filteredCases.isEmpty
                        ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                _searchQuery.isNotEmpty
                                    ? LucideIcons.searchX
                                    : LucideIcons.folderOpen,
                                size: 48,
                                color: shadTheme.colorScheme.mutedForeground,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _searchQuery.isNotEmpty
                                    ? 'No cases found matching your search'
                                    : 'No approved investment cases found',
                                style: shadTheme.textTheme.h4.copyWith(
                                  color: shadTheme.colorScheme.mutedForeground,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                _searchQuery.isNotEmpty
                                    ? 'Try adjusting your search terms or clear the search to see all cases'
                                    : 'Check back later for new investment opportunities',
                                style: shadTheme.textTheme.p.copyWith(
                                  color: shadTheme.colorScheme.mutedForeground,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              if (_searchQuery.isNotEmpty) ...[
                                const SizedBox(height: 16),
                                ShadButton.outline(
                                  onPressed: _clearSearch,
                                  child: const Text('Clear Search'),
                                ),
                              ],
                            ],
                          ),
                        )
                        : ListView.builder(
                          itemCount: _filteredCases.length,
                          itemBuilder: (context, index) {
                            if (index >= _itemAnimations.length) {
                              return const SizedBox.shrink();
                            }
                            final caseData = _filteredCases[index];
                            return AnimatedBuilder(
                              animation: _itemAnimations[index],
                              builder: (context, child) {
                                return Opacity(
                                  opacity: _itemAnimations[index].value,
                                  child: Transform.translate(
                                    offset: Offset(
                                      0.0,
                                      50 * (1.0 - _itemAnimations[index].value),
                                    ),
                                    child: child,
                                  ),
                                );
                              },
                              child: _buildInvestmentCaseCard(
                                context,
                                caseData,
                                shadTheme,
                                isSelectionMode,
                                !canInvest, // Pass investment disabled flag
                              ),
                            );
                          },
                        ),
              ),
            ],
          ),
        ), // This closes the Padding widget
      ), // This closes the Scaffold child
    ); // This closes the WillPopScope
  } // This closes the build method

  Widget _buildInvestmentCaseCard(
    BuildContext context,
    InvestmentCase caseData,
    ShadThemeData theme,
    bool isSelectionMode,
    bool isInvestmentDisabled, // New parameter
  ) {
    final textTheme = theme.textTheme;
    final dateFormat = DateFormat.yMMMd();
    // final bool canAccess = true; // Replaced by isInvestmentDisabled
    final int interest = _getInterestForStage(caseData.stage);

    IconData statusIcon;
    Color statusColor = _getStatusColor(
      caseData.currentStatus,
      theme.colorScheme,
    );

    switch (caseData.currentStatus.toLowerCase()) {
      case 'seeking_funding':
      case 'approved_for_listing':
        statusIcon = LucideIcons.circleDollarSign;
        break;
      case 'partially_funded':
        statusIcon = LucideIcons.trendingUp;
        break;
      case 'fully_funded':
        statusIcon = LucideIcons.check;
        break;
      case 'closed':
        statusIcon = LucideIcons.x;
        break;
      default:
        statusIcon = LucideIcons.info;
    }

    String formattedFundingAmount = NumberFormat.currency(
      locale: 'en_GB',
      symbol: '£',
    ).format(caseData.requiredFundingAmount);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
      child: InkWell(
        // Disable tap if investment is disabled
        onTap:
            isInvestmentDisabled
                ? null
                : () {
                  // Primary tap action: if in selection mode, select. Otherwise, view details.
                  if (isSelectionMode) {
                    FocusScope.of(context).unfocus();
                    Navigator.pop(context, caseData.id);
                  } else {
                    Navigator.pushNamed(
                      context,
                      InvestmentCaseDetailPage.routeName,
                      arguments: caseData.id,
                    );
                  }
                },
        borderRadius: theme.radius, // Use theme radius
        child: Opacity(
          // Add opacity if disabled
          opacity: isInvestmentDisabled ? 0.6 : 1.0,
          child: ShadCard(
            // Ensure this ShadCard is correctly structured
            radius: theme.radius, // Use theme radius
            shadows: [
              // Enhanced shadow
              BoxShadow(
                color: Colors.black.withOpacity(
                  0.08,
                ), // Use a specific color for shadow
                blurRadius: 12,
                spreadRadius: 1,
                offset: const Offset(0, 5),
              ),
            ],
            border: Border.all(
              color:
                  isInvestmentDisabled
                      ? theme.colorScheme.destructive.withOpacity(0.3)
                      : theme.colorScheme.border,
              width: 1,
            ),
            child: Padding(
              // Added overall padding to the card's content
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top section with Status and Title
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          caseData.caseTitle,
                          style: textTheme.h4.copyWith(
                            // Ensured h4 style
                            fontWeight: FontWeight.w600, // Bolder title
                            height: 1.3,
                            color:
                                theme
                                    .colorScheme
                                    .foreground, // Keep color normal even if disabled
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 5,
                        ), // Adjusted padding
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(
                            0.15,
                          ), // Slightly more opaque
                          borderRadius: BorderRadius.circular(
                            20,
                          ), // More rounded
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              statusIcon,
                              size: 14, // Consistent icon size
                              color: statusColor,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              caseData.currentStatus
                                  .replaceAll('_', ' ')
                                  .split(' ')
                                  .map(
                                    (e) =>
                                        e.isNotEmpty
                                            ? e[0].toUpperCase() +
                                                e.substring(1)
                                            : '',
                                  )
                                  .join(' '),
                              style: textTheme.small.copyWith(
                                // Using small style for status
                                fontWeight: FontWeight.w500, // Medium weight
                                color: statusColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10), // Increased spacing
                  // Summary
                  Text(
                    caseData.caseSummaryPublic,
                    style: textTheme.p.copyWith(
                      // Using p style for summary
                      height: 1.5,
                      color:
                          theme
                              .colorScheme
                              .mutedForeground, // Keep color normal
                    ),
                    maxLines: 3, // Allow more lines for summary
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 16), // Increased spacing
                  // Details Section
                  _buildCaseDetailRow(
                    theme,
                    LucideIcons.coins, // Icon for funding goal
                    'Funding Goal:',
                    formattedFundingAmount,
                  ),
                  const SizedBox(height: 8),
                  if (caseData.stage != null) ...[
                    _buildCaseDetailRow(
                      theme,
                      LucideIcons.flag, // Icon for stage
                      'Current Stage:',
                      caseData.stage!.replaceFirst(RegExp(r'STAGE \d+: '), ''),
                    ),
                    const SizedBox(height: 8),
                  ],
                  _buildCaseDetailRow(
                    theme,
                    LucideIcons.percent, // Icon for interest
                    'FRFR (Interest):',
                    '$interest%',
                    valueColor:
                        interest > 0
                            ? theme.colorScheme.primary
                            : theme.colorScheme.mutedForeground,
                  ),

                  const SizedBox(height: 16),
                  const Divider(), // Divider before action buttons
                  const SizedBox(height: 12),
                  // Action Buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.min, // Take minimum space needed
                    children: [
                      Flexible(
                        child: ShadButton.outline(
                          leading: const Icon(LucideIcons.eye, size: 16),
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              InvestmentCaseDetailPage.routeName,
                              arguments: {'caseId': caseData.id},
                            );
                          },
                          child: const Text(
                            'View Details',
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Flexible(
                        child: ShadButton(
                          leading: Icon(
                            isSelectionMode
                                ? LucideIcons.check
                                : LucideIcons.dollarSign,
                            size: 16,
                          ),
                          onPressed: () {
                            if (isSelectionMode) {
                              FocusScope.of(context).unfocus();
                              Navigator.pop(context, caseData.id);
                            } else {
                              Navigator.pushNamed(
                                context,
                                InvestmentCaseDetailPage.routeName,
                                arguments: {'caseId': caseData.id},
                              );
                            }
                          },
                          child: Text(
                            isSelectionMode ? 'Select' : 'Fund',
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ],
                  ), // Close Row for action buttons
                ], // Close children Column
              ), // Close Column
            ), // Close Padding inside ShadCard
          ), // Close ShadCard
        ), // Close Opacity
      ), // Close InkWell
    ); // Close Padding wrapping InkWell
  }

  Widget _buildCaseDetailRow(
    ShadThemeData theme,
    IconData icon,
    String label,
    String value, {
    Color? valueColor,
    bool isMuted = false,
  }) {
    return Row(
      children: [
        Text(
          label,
          style: theme.textTheme.small.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.small.copyWith(
              color: valueColor ?? theme.colorScheme.foreground,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status, ShadColorScheme colorScheme) {
    final lowerStatus = status.toLowerCase();
    if (lowerStatus.contains('seeking') || lowerStatus.contains('approved')) {
      return colorScheme.primary;
    } else if (lowerStatus.contains('partially')) {
      return Colors.orange.shade600;
    } else if (lowerStatus.contains('fully funded')) {
      return Colors.green.shade600;
    } else if (lowerStatus.contains('closed')) {
      return colorScheme.mutedForeground;
    }
    return colorScheme.foreground;
  }
}
