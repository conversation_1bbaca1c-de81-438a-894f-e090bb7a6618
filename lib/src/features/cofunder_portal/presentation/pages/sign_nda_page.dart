import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/theme/app_theme.dart';
import 'package:intl/intl.dart';
import 'package:signature/signature.dart';
import 'dart:typed_data';
import 'dart:convert';
import 'package:http/http.dart' as http;

class SignNDAPage extends StatefulWidget {
  static const String routeName = '/sign-nda';

  const SignNDAPage({super.key});

  @override
  State<SignNDAPage> createState() => _SignNDAPageState();
}

class _SignNDAPageState extends State<SignNDAPage> {
  final PocketBaseService _pbService = PocketBaseService();
  final SignatureController _signatureController = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.black,
    exportBackgroundColor: Colors.white,
  );

  bool _isLoading = true;
  bool _isSubmitting = false;
  String? _errorMessage;
  String? _ndaContent;
  String? _coFunderName;
  String? _coFunderProfileId;

  @override
  void initState() {
    super.initState();
    _loadNDATemplate();
  }

  @override
  void dispose() {
    _signatureController.dispose();
    super.dispose();
  }

  Future<void> _loadNDATemplate() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Fetch the current user's profile
      final userId = _pbService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Get user's name
      final userRecord = await _pbService.client
          .collection('users')
          .getOne(userId);
      _coFunderName =
          userRecord.data['name'] ??
          "${userRecord.data['first_name'] ?? ''} ${userRecord.data['last_name'] ?? ''}"
              .trim();

      if (_coFunderName!.isEmpty) {
        _coFunderName = "Co-Funder";
      }

      // Get co-funder profile ID
      final profileRecord = await _pbService.client
          .collection('co_funder_profiles')
          .getFirstListItem('user_id="$userId"');
      _coFunderProfileId = profileRecord.id;

      // Check if NDA is already signed
      if (profileRecord.data['nda_signed_at'] != null &&
          profileRecord.data['nda_signed_at'] != "") {
        setState(() {
          _isLoading = false;
          _errorMessage =
              "You have already signed the NDA on ${DateFormat.yMMMMd().format(DateTime.parse(profileRecord.data['nda_signed_at']))}";
        });
        return;
      }

      // Fetch NDA template
      final ndaTemplates = await _pbService.client
          .collection('document_templates')
          .getList(filter: 'type="nda"', sort: '-version', perPage: 1);

      if (ndaTemplates.items.isEmpty) {
        throw Exception('No NDA template found');
      }

      final ndaTemplate = ndaTemplates.items.first;
      String content = ndaTemplate.data['content'];

      // Replace dynamic fields in the content
      final today = DateFormat.yMMMMd().format(DateTime.now());
      content = content.replaceAll('{{co_funder_name}}', _coFunderName!);
      content = content.replaceAll('{{date}}', today);

      setState(() {
        _ndaContent = content;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = "Failed to load NDA template: ${e.toString()}";
      });
    }
  }

  Future<void> _submitSignedNDA() async {
    if (_signatureController.isEmpty) {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Signature Required'),
          description: const Text('Please sign the NDA before submitting.'),
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Get signature as PNG bytes
      final Uint8List? signatureBytes = await _signatureController.toPngBytes();

      if (signatureBytes == null) {
        throw Exception('Failed to capture signature');
      }

      // Create a filename for the signature
      final fileName =
          'nda_signature_${DateTime.now().millisecondsSinceEpoch}.png';

      // Create a multipart file from the signature bytes
      final signatureFile = http.MultipartFile.fromBytes(
        'nda_signature', // field name in PocketBase
        signatureBytes,
        filename: fileName,
      );

      // Update co-funder profile with NDA signature date and signature file
      await _pbService.client
          .collection('co_funder_profiles')
          .update(
            _coFunderProfileId!,
            body: {'nda_signed_at': DateTime.now().toIso8601String()},
            files: [signatureFile],
          );

      // Show success message
      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast(
            title: const Text('NDA Signed Successfully'),
            description: const Text(
              'Thank you for signing the Non-Disclosure Agreement.',
            ),
          ),
        );

        // Navigate back to previous screen
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() {
        _isSubmitting = false;
        _errorMessage = "Failed to submit signed NDA: ${e.toString()}";
      });
    }
  }

  void _clearSignature() {
    _signatureController.clear();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Sign Non-Disclosure Agreement',
          style: theme.textTheme.h4.copyWith(color: AppTheme.textOnDark),
        ),
        backgroundColor: AppTheme.primaryColor,
        iconTheme: IconThemeData(color: AppTheme.textOnDark),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator.adaptive())
              : _errorMessage != null
              ? Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ShadAlert.destructive(
                        title: const Text('Error'),
                        description: Text(_errorMessage!),
                      ),
                      const SizedBox(height: 16),
                      ShadButton.outline(
                        child: const Text('Go Back'),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ),
              )
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ShadCard(
                      title: const Text('Non-Disclosure Agreement'),
                      description: const Text(
                        'Please read the agreement carefully before signing.',
                      ),
                      child: Container(
                        height: 400,
                        padding: const EdgeInsets.all(16.0),
                        decoration: BoxDecoration(
                          border: Border.all(color: theme.colorScheme.border),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: SingleChildScrollView(
                          child:
                              _ndaContent != null
                                  ? _buildHtmlContent(_ndaContent!)
                                  : const Text('Error loading NDA content'),
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    ShadCard(
                      title: const Text('Your Signature'),
                      description: const Text(
                        'Please sign below to indicate your agreement.',
                      ),
                      child: Column(
                        children: [
                          Container(
                            height: 200,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: theme.colorScheme.border,
                              ),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            child: Signature(
                              controller: _signatureController,
                              width: double.infinity,
                              height: 200,
                              backgroundColor: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              ShadButton.outline(
                                onPressed: _clearSignature,
                                child: const Text('Clear Signature'),
                              ),
                              ShadButton(
                                onPressed:
                                    _isSubmitting ? null : _submitSignedNDA,
                                child:
                                    _isSubmitting
                                        ? const SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                          ),
                                        )
                                        : const Text('Sign & Submit'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  // Simple HTML rendering - in a real app, you would use a proper HTML renderer
  Widget _buildHtmlContent(String htmlContent) {
    // This is a very basic implementation that just displays the HTML as formatted text
    // In a real app, you would use a package like flutter_html to render HTML properly
    return SelectableText(
      htmlContent.replaceAll(RegExp(r'<[^>]*>'), ''), // Remove HTML tags
      style: const TextStyle(fontSize: 14),
    );
  }
}
