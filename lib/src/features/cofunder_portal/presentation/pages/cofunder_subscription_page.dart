import 'dart:async'; // Added for Future.delayed
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:http/http.dart' as http;
import 'package:pocketbase/pocketbase.dart'; // Added for RecordModel
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart'; // Added
import 'package:shadcn_ui/shadcn_ui.dart'; // Added for ShadToaster/Dialog

// Replace with your Deno service's base URL
const String denoServiceBaseUrl =
    'https://3paywebhook-production.up.railway.app'; // Example: 'http://your-deno-service-url.com' or 'http://localhost:8080' if local
// const String denoServiceBaseUrl = 'http://localhost:8080';

class CofunderOneTimePaymentPage extends StatefulWidget {
  final String pocketBaseUserId;
  final String stripePriceId; // Still useful for metadata/tracking the product
  final int amount; // Amount in smallest currency unit (e.g., cents)
  final String currency; // e.g., "gbp", "usd"

  const CofunderOneTimePaymentPage({
    Key? key,
    required this.pocketBaseUserId,
    required this.stripePriceId,
    required this.amount,
    required this.currency,
  }) : super(key: key);

  @override
  _CofunderOneTimePaymentPageState createState() =>
      _CofunderOneTimePaymentPageState();
}

class _CofunderOneTimePaymentPageState
    extends State<CofunderOneTimePaymentPage> {
  final PocketBaseService _pbService = PocketBaseService(); // Added
  bool _isLoading = false;
  String _paymentStatusMessage = '';
  bool _isPolling = false; // To manage polling state

  Future<Map<String, dynamic>> _fetchPaymentSheetParams() async {
    final url = Uri.parse(
      '$denoServiceBaseUrl/initiate-onetime-payment-sheet',
    ); // Updated endpoint
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'userId': widget.pocketBaseUserId,
          'priceId': widget.stripePriceId,
          'amount': widget.amount,
          'currency': widget.currency,
        }),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        final errorBody = json.decode(response.body);
        throw Exception(
          'Failed to fetch payment sheet params: ${errorBody['error'] ?? response.body}',
        );
      }
    } catch (e) {
      throw Exception('Failed to connect to backend: $e');
    }
  }

  Future<void> _initAndPresentPaymentSheet() async {
    setState(() {
      _isLoading = true;
      _paymentStatusMessage = 'Initializing payment...';
    });

    try {
      final paymentSheetData = await _fetchPaymentSheetParams();

      final String paymentIntentClientSecret =
          paymentSheetData['paymentIntentClientSecret'];
      final String ephemeralKeySecret = paymentSheetData['ephemeralKeySecret'];
      final String customerId = paymentSheetData['customerId'];
      final String publishableKey = paymentSheetData['publishableKey'];

      if (publishableKey.isEmpty) {
        throw Exception('Publishable key from backend is empty.');
      }

      // Initialize Stripe with the publishable key from backend
      Stripe.publishableKey = publishableKey;
      await Stripe.instance
          .applySettings(); // Apply settings if needed (e.g. for Apple Pay)

      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          merchantDisplayName: '3Pay Litigation Platform', // Your merchant name
          paymentIntentClientSecret: paymentIntentClientSecret,
          customerEphemeralKeySecret: ephemeralKeySecret,
          customerId: customerId,
          // applePay: const PaymentSheetApplePay(merchantCountryCode: 'US'), // Optional: Configure Apple Pay
          // googlePay: const PaymentSheetGooglePay(merchantCountryCode: 'US', testEnv: true), // Optional: Configure Google Pay
          style: ThemeMode.system, // Or ThemeMode.light, ThemeMode.dark
        ),
      );

      setState(() {
        _paymentStatusMessage = 'Presenting payment sheet...';
      });

      await Stripe.instance.presentPaymentSheet();

      // Payment sheet closed by user or payment completed/failed.
      // The actual payment confirmation happens via webhooks.
      // Here, you can update UI based on user closing the sheet,
      // but rely on webhooks for actual subscription status.
      // For immediate feedback, you might check subscription status again after a short delay
      // or navigate to a page that reflects the subscription status.
      // --- Start Polling for Payment Status ---
      await _handlePaymentSheetClosed();
      // --- End Polling for Payment Status ---
    } on StripeException catch (e) {
      // Access error details correctly from StripeException
      final message =
          e.error.localizedMessage ?? e.error.message ?? e.toString();
      setState(() {
        _paymentStatusMessage = 'Payment failed: $message';
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Stripe Error: $message')));
      });
    } catch (e) {
      setState(() {
        _paymentStatusMessage = 'An unexpected error occurred: $e';
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: $e')));
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _handlePaymentSheetClosed() async {
    setState(() {
      _isLoading = true; // Keep loading indicator while polling
      _isPolling = true;
      _paymentStatusMessage = 'Confirming payment status...';
    });

    const int maxRetries = 10; // e.g., 10 retries
    const Duration retryDelay = Duration(seconds: 2); // e.g., 2 seconds delay
    String finalStatus = "pending";

    for (int i = 0; i < maxRetries; i++) {
      try {
        final profileRecords = await _pbService.client
            .collection('co_funder_profiles')
            .getFullList(filter: 'user_id = "${widget.pocketBaseUserId}"');
        if (profileRecords.isNotEmpty) {
          final profile = profileRecords.first;
          final status = profile.data['subscription_status'] as String?;
          final bool subscribed =
              profile.data['level_4_subscribed'] as bool? ?? false;

          if (status == 'active_onetime' && subscribed) {
            finalStatus = "success";
            break;
          } else if (status == 'payment_failed_onetime') {
            finalStatus = "failed";
            break;
          }
        }
      } catch (e) {
        print("Error polling payment status: $e");
        // Continue polling, maybe a temporary network issue
      }
      if (i < maxRetries - 1) {
        await Future.delayed(retryDelay);
      }
    }

    setState(() {
      _isLoading = false;
      _isPolling = false;
    });

    if (!mounted) return;

    if (finalStatus == "success") {
      _paymentStatusMessage = 'Payment Successful! Your membership is active.';
      await showDialog(
        context: context,
        builder:
            (context) => ShadDialog(
              title: const Text('Payment Successful!'),
              description: const Text(
                'Your 3Pay Global Associate Membership is now active.',
              ),
              actions: [
                ShadButton(
                  child: const Text('OK'),
                  onPressed: () {
                    Navigator.of(context).pop(); // Close dialog
                    Navigator.of(
                      context,
                    ).pop(); // Go back from payment page (to dashboard)
                  },
                ),
              ],
            ),
      );
    } else if (finalStatus == "failed") {
      _paymentStatusMessage =
          'Payment Failed. Please try again or contact support.';
      await showDialog(
        context: context,
        builder:
            (context) => ShadDialog(
              title: const Text('Payment Failed'),
              description: const Text(
                'Unfortunately, your payment could not be processed. Please try again or contact support if the issue persists.',
              ),
              actions: [
                ShadButton.outline(
                  child: const Text('Cancel'),
                  onPressed: () {
                    Navigator.of(context).pop(); // Close dialog
                    Navigator.of(context).pop(); // Go back from payment page
                  },
                ),
                ShadButton(
                  child: const Text('Retry Payment'),
                  onPressed: () {
                    Navigator.of(context).pop(); // Close dialog
                    _initAndPresentPaymentSheet(); // Attempt payment again
                  },
                ),
              ],
            ),
      );
    } else {
      // Timeout or still pending
      _paymentStatusMessage =
          'Payment is processing. We will notify you of the status. You can check your dashboard later.';
      await showDialog(
        context: context,
        builder:
            (context) => ShadDialog(
              title: const Text('Payment Processing'),
              description: Text(_paymentStatusMessage),
              actions: [
                ShadButton(
                  child: const Text('OK'),
                  onPressed: () {
                    Navigator.of(context).pop(); // Close dialog
                    Navigator.of(context).pop(); // Go back from payment page
                  },
                ),
              ],
            ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    // Format amount for display (e.g., £600.00 from 60000 pence)
    final formattedAmount = (widget.amount / 100).toStringAsFixed(2);
    // Basic currency symbol handling, can be expanded
    final currencySymbol =
        widget.currency.toLowerCase() == 'gbp'
            ? '£'
            : widget.currency.toLowerCase() == 'usd'
            ? '\$'
            : widget.currency.toUpperCase();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Activate Membership',
          style: textTheme.h4.copyWith(color: colorScheme.foreground),
        ),
        backgroundColor: colorScheme.background,
        elevation: 0,
        iconTheme: IconThemeData(color: colorScheme.foreground),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Text(
              'Unlock Your 3Pay Global Associate Membership',
              style: textTheme.h2.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'Gain full access to the platform and exclusive benefits with a one-time payment.',
              style: textTheme.p.copyWith(
                color: colorScheme.mutedForeground,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 28),
            ShadCard(
              radius: theme.radius,
              padding: const EdgeInsets.all(20),
              backgroundColor: colorScheme.card,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Membership Benefits Include:',
                    style: textTheme.h4.copyWith(
                      fontSize: 18,
                      color: colorScheme.popoverForeground,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildBenefitItem(
                    LucideIcons.gem,
                    'Access to premium content on litigation funding',
                    theme,
                  ),
                  _buildBenefitItem(
                    LucideIcons.trendingUp,
                    'Deeper insights into our business model',
                    theme,
                  ), // Changed to trendingUp
                  _buildBenefitItem(
                    LucideIcons.video,
                    'Exclusive webinars and educational materials',
                    theme,
                  ),
                  _buildBenefitItem(
                    LucideIcons.messageCircle,
                    'Priority customer support',
                    theme,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 28),
            Text(
              'One-Time Payment: $currencySymbol$formattedAmount ${widget.currency.toUpperCase()}',
              style: textTheme.h3.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.foreground,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            if (_isLoading || _isPolling)
              const Center(child: CircularProgressIndicator())
            else
              ShadButton(
                // Use default constructor for primary style
                width: double.infinity,
                size: ShadButtonSize.lg,
                onPressed: _initAndPresentPaymentSheet,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      LucideIcons.creditCard,
                      size: 20,
                      color: colorScheme.primaryForeground,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Proceed to Secure Payment',
                      style: textTheme.p.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: colorScheme.primaryForeground,
                      ),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 20),
            if (_paymentStatusMessage.isNotEmpty &&
                !_isPolling &&
                !_isLoading) // Show message only if not actively polling/loading
              Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text(
                    _paymentStatusMessage,
                    style: textTheme.p.copyWith(
                      color:
                          _paymentStatusMessage.toLowerCase().contains('failed')
                              ? colorScheme.destructive
                              : _paymentStatusMessage.toLowerCase().contains(
                                'success',
                              )
                              ? colorScheme
                                  .primary // Or a success color like Colors.green
                              : colorScheme.mutedForeground,
                      fontSize: 15,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBenefitItem(IconData icon, String text, ShadThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(icon, size: 22, color: theme.colorScheme.primary),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              text,
              style: theme.textTheme.p.copyWith(
                fontSize: 15,
                color: theme.colorScheme.popoverForeground,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Example of how you might navigate to this page:
// Navigator.push(
//   context,
//   MaterialPageRoute(
//     builder: (context) => CofunderOneTimePaymentPage(
//       pocketBaseUserId: "current_user_pb_id",
//       stripePriceId: "price_xxxxxxxxxxxxxx",
//       amount: 60000, // e.g., £600.00 in pence
//       currency: "gbp",
//     ),
//   ),
// );
