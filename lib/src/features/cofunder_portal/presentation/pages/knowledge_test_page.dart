import 'package:shadcn_ui/shadcn_ui.dart' as shadcn;
import 'package:flutter/material.dart' as material;
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';

// Updated data model for a knowledge test question
class KnowledgeTestQuestion {
  final String id; // Using question_text as a unique enough ID for this context
  final String questionText;
  final List<String> options;
  final String correctAnswer;

  KnowledgeTestQuestion({
    required this.id,
    required this.questionText,
    required this.options,
    required this.correctAnswer,
  });

  factory KnowledgeTestQuestion.fromMap(Map<String, dynamic> map, int index) {
    return KnowledgeTestQuestion(
      id: map['id'] ?? 'q_$index', // Fallback ID
      questionText: map['question_text'] ?? 'N/A',
      options: List<String>.from(map['options'] ?? []),
      correctAnswer: map['correct_answer'] ?? '',
    );
  }
}

class KnowledgeTestPage extends material.StatefulWidget {
  static const routeName = '/knowledge-test';
  // Optionally, pass a testId if you have multiple tests
  // final String? testId;

  const KnowledgeTestPage({super.key /*, this.testId */});

  @override
  material.State<KnowledgeTestPage> createState() => _KnowledgeTestPageState();
}

class _KnowledgeTestPageState extends material.State<KnowledgeTestPage> {
  final PocketBaseService _pbService = PocketBaseService();
  List<KnowledgeTestQuestion> _questions = [];
  bool _isLoading = true;
  String? _errorMessage;
  String _testTitle = "Knowledge Test"; // Default title

  int _currentQuestionIndex = 0;
  String? _selectedAnswer;
  final Map<String, String> _answers = {}; // questionId: selectedOption

  @override
  void initState() {
    super.initState();
    _fetchTestQuestions();
  }

  Future<void> _fetchTestQuestions() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      // Fetch the specific test record, e.g., "Imported Knowledge Test - Legacy Data"
      // Or fetch the first one if no specific ID is provided.
      // For simplicity, fetching the first available test.
      final result = await _pbService.client
          .collection('knowledge_tests')
          .getList(
            page: 1,
            perPage: 1,
            // filter: 'title = "Imported Knowledge Test - Legacy Data"', // Or use widget.testId
          );

      if (result.items.isNotEmpty) {
        final testRecord = result.items.first;
        _testTitle = testRecord.data['title'] ?? _testTitle;
        final questionsData =
            testRecord.data['questions'] as List<dynamic>? ?? [];

        _questions =
            questionsData
                .asMap()
                .entries
                .map(
                  (entry) => KnowledgeTestQuestion.fromMap(
                    entry.value as Map<String, dynamic>,
                    entry.key,
                  ),
                )
                .toList();
      } else {
        _errorMessage = "No knowledge tests found.";
      }
    } catch (e) {
      _errorMessage = "Failed to load test questions: ${e.toString()}";
      // ignore: avoid_print
      print('Error fetching knowledge test: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _nextQuestion() {
    if (_questions.isEmpty) return;
    if (_selectedAnswer != null) {
      _answers[_questions[_currentQuestionIndex].id] = _selectedAnswer!;
    }
    if (_currentQuestionIndex < _questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
        _selectedAnswer = _answers[_questions[_currentQuestionIndex].id];
      });
    } else {
      _submitTest();
    }
  }

  void _previousQuestion() {
    if (_questions.isEmpty) return;
    if (_selectedAnswer != null) {
      _answers[_questions[_currentQuestionIndex].id] = _selectedAnswer!;
    }
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
        _selectedAnswer = _answers[_questions[_currentQuestionIndex].id];
      });
    }
  }

  void _onAnswerSelected(String answer) {
    setState(() {
      _selectedAnswer = answer;
    });
  }

  void _submitTest() {
    if (_questions.isEmpty) return;
    if (_selectedAnswer != null) {
      _answers[_questions[_currentQuestionIndex].id] = _selectedAnswer!;
    }

    int correctCount = 0;
    _answers.forEach((questionId, selectedOption) {
      final question = _questions.firstWhere((q) => q.id == questionId);
      if (question.correctAnswer == selectedOption) {
        correctCount++;
      }
    });

    double score =
        _questions.isNotEmpty ? (correctCount / _questions.length) * 100 : 0;

    shadcn.showShadDialog(
      context: context,
      builder: (material.BuildContext context) {
        final theme = material.Theme.of(context);
        return shadcn.ShadDialog(
          title: material.Row(
            children: [
              material.Icon(
                score >= 80
                    ? shadcn.LucideIcons.fileCheck2
                    : shadcn
                        .LucideIcons
                        .shieldAlert, // Using icons found in search results
                color:
                    score >= 80
                        ? theme.colorScheme.primary
                        : theme.colorScheme.error,
                size: 20,
              ),
              const material.SizedBox(width: 8),
              material.Text(
                'Test Submitted',
                style: theme.textTheme.titleLarge,
              ),
            ],
          ),
          description: material.Text(
            'You answered $correctCount out of ${_questions.length} questions correctly.\nYour score: ${score.toStringAsFixed(1)}%',
            style: theme.textTheme.bodyMedium,
          ),
          actions: [
            shadcn.ShadButton(
              child: material.Text(
                'OK',
                style: material.TextStyle(color: theme.colorScheme.onPrimary),
              ), // Changed text to child
              onPressed: () {
                material.Navigator.of(context).pop(); // Close dialog
                material.Navigator.of(context).pop(); // Go back from test page
              },
            ),
          ],
        );
      },
    );
  }

  @override
  material.Widget build(material.BuildContext context) {
    final theme = material.Theme.of(context);

    if (_isLoading) {
      return material.Scaffold(
        appBar: material.AppBar(title: material.Text(_testTitle)),
        body: const material.Center(
          child: material.CircularProgressIndicator.adaptive(),
        ),
      );
    }

    if (_errorMessage != null) {
      return material.Scaffold(
        appBar: material.AppBar(title: material.Text(_testTitle)),
        body: material.Center(
          child: material.Padding(
            padding: const material.EdgeInsets.all(16),
            child: material.Text(_errorMessage!),
          ),
        ),
      );
    }

    if (_questions.isEmpty) {
      return material.Scaffold(
        appBar: material.AppBar(title: material.Text(_testTitle)),
        body: const material.Center(
          child: material.Text('No questions available for this test.'),
        ),
      );
    }

    final currentQuestion = _questions[_currentQuestionIndex];

    return material.Scaffold(
      appBar: material.AppBar(
        title: material.Text(_testTitle),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
      body: material.Padding(
        padding: const material.EdgeInsets.all(16.0),
        child: material.Column(
          crossAxisAlignment: material.CrossAxisAlignment.stretch,
          children: [
            material.Text(
              'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
              textAlign: material.TextAlign.center,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: material.FontWeight.bold,
              ),
            ),
            const material.SizedBox(height: 8),
            material.LinearProgressIndicator(
              value: (_currentQuestionIndex + 1) / _questions.length,
              backgroundColor: theme.colorScheme.surfaceContainerHighest,
              valueColor: material.AlwaysStoppedAnimation<material.Color>(
                theme.colorScheme.primary,
              ),
            ),
            const material.SizedBox(height: 24),
            material.Card(
              elevation: 2,
              shape: material.RoundedRectangleBorder(
                borderRadius: material.BorderRadius.circular(12),
              ),
              child: material.Padding(
                padding: const material.EdgeInsets.all(16.0),
                child: material.Text(
                  currentQuestion.questionText,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: material.FontWeight.w500,
                  ),
                  textAlign: material.TextAlign.center,
                ),
              ),
            ),
            const material.SizedBox(height: 24),
            material.Expanded(
              child: material.ListView.builder(
                itemCount: currentQuestion.options.length,
                itemBuilder: (context, index) {
                  final option = currentQuestion.options[index];
                  final bool isSelected = _selectedAnswer == option;
                  return material.AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: material.Curves.easeInOut,
                    margin: const material.EdgeInsets.symmetric(vertical: 6.0),
                    decoration: material.BoxDecoration(
                      color:
                          isSelected
                              ? theme.colorScheme.primaryContainer
                              : theme.colorScheme.surface,
                      borderRadius: material.BorderRadius.circular(10.0),
                      border: material.Border.all(
                        color:
                            isSelected
                                ? theme.colorScheme.primary
                                : theme.colorScheme.outline.withValues(
                                  alpha: 0.5,
                                ),
                        width: isSelected ? 2.0 : 1.0,
                      ),
                      boxShadow:
                          isSelected
                              ? [
                                material.BoxShadow(
                                  color: theme.colorScheme.primary.withValues(
                                    alpha: 0.3,
                                  ),
                                  spreadRadius: 2,
                                  blurRadius: 4,
                                  offset: const material.Offset(0, 2),
                                ),
                              ]
                              : [
                                material.BoxShadow(
                                  color: material.Colors.black.withValues(
                                    alpha: 0.05,
                                  ),
                                  spreadRadius: 1,
                                  blurRadius: 3,
                                  offset: const material.Offset(0, 1),
                                ),
                              ],
                    ),
                    child: material.InkWell(
                      onTap: () => _onAnswerSelected(option),
                      borderRadius: material.BorderRadius.circular(10.0),
                      child: material.Padding(
                        padding: const material.EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 12.0,
                        ),
                        child: material.Row(
                          children: [
                            material.AnimatedSwitcher(
                              duration: const Duration(milliseconds: 200),
                              transitionBuilder: (
                                material.Widget child,
                                material.Animation<double> animation,
                              ) {
                                return material.ScaleTransition(
                                  scale: animation,
                                  child: child,
                                );
                              },
                              child: material.Icon(
                                isSelected
                                    ? material.Icons.check_circle_rounded
                                    : material
                                        .Icons
                                        .radio_button_unchecked_rounded,
                                key: material.ValueKey<bool>(isSelected),
                                color:
                                    isSelected
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            const material.SizedBox(width: 12),
                            material.Expanded(
                              child: material.Text(
                                option,
                                style: theme.textTheme.bodyLarge?.copyWith(
                                  color:
                                      isSelected
                                          ? theme.colorScheme.onPrimaryContainer
                                          : theme.colorScheme.onSurface,
                                  fontWeight:
                                      isSelected
                                          ? material.FontWeight.bold
                                          : material.FontWeight.normal,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            const material.SizedBox(height: 20),
            material.Row(
              mainAxisAlignment: material.MainAxisAlignment.spaceBetween,
              children: [
                if (_currentQuestionIndex > 0)
                  material.ElevatedButton.icon(
                    icon: const material.Icon(
                      material.Icons.arrow_back_ios_new,
                    ),
                    label: const material.Text('Previous'),
                    onPressed: _previousQuestion,
                    style: material.ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.secondary,
                      foregroundColor: theme.colorScheme.onSecondary,
                      padding: const material.EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                    ),
                  )
                else
                  const material.SizedBox(),
                material.ElevatedButton.icon(
                  icon: material.Icon(
                    _currentQuestionIndex < _questions.length - 1
                        ? material.Icons.arrow_forward_ios_rounded
                        : material.Icons.check_circle_outline_rounded,
                  ),
                  label: material.Text(
                    _currentQuestionIndex < _questions.length - 1
                        ? 'Next'
                        : 'Submit Test',
                  ),
                  onPressed: _selectedAnswer == null ? null : _nextQuestion,
                  style: material.ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const material.EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
