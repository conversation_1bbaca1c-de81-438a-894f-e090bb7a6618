import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:pocketbase/pocketbase.dart'; // Import PocketBase RecordModel
import 'package:three_pay_group_litigation_platform/src/core/theme/app_theme.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/solicitor_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/professional_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/services/claim_documents_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/document_preview_widget.dart';
import '../../utils/responsive_layout.dart';
import '../widgets/error_widgets.dart';
import '../widgets/loading_widgets.dart';

// Data Model for Investment Case Details
class InvestmentCaseDetails {
  final String id;
  final String caseTitle;
  final String? caseSummaryPublic;
  final String currentStatus;
  final double requiredFundingAmount;
  final double? totalFundingSecured;
  final String? solicitorName;
  final String? lawFirmName;
  final String? barristerDetailsPublic;
  final List<UploadedDocumentCategory>
  documents; // Updated to use proper document model
  final String? stage;
  final double? legalOpinionSuccessProspects;
  final String? expectedFrfrSchedule; // Interest rate/FRFR information

  // Claim classification fields
  final String? claimantType;
  final String? claimIndustry;
  final String? claimType;

  // Placeholders for sections that might not have direct data yet
  final List<String> fundingTranches;
  final String riskAssessment;
  final String investmentTerms;

  // Additional fields for solicitor view
  final List<SolicitorProfileModel> associatedSolicitors;
  final List<String> claimAdminIds;
  final List<Expert> experts;
  final List<Barrister> barristers;

  InvestmentCaseDetails({
    required this.id,
    required this.caseTitle,
    this.caseSummaryPublic,
    required this.currentStatus,
    required this.requiredFundingAmount,
    this.totalFundingSecured,
    this.solicitorName,
    this.lawFirmName,
    this.barristerDetailsPublic,
    required this.documents,
    this.stage,
    this.legalOpinionSuccessProspects,
    this.expectedFrfrSchedule,
    // Claim classification fields
    this.claimantType,
    this.claimIndustry,
    this.claimType,
    // Placeholders
    this.fundingTranches = const ['Tranche information not yet available.'],
    this.riskAssessment = 'Risk assessment details not yet available.',
    this.investmentTerms = 'Specific investment terms not yet available.',
    // Additional fields for solicitor view
    this.associatedSolicitors = const [],
    this.claimAdminIds = const [],
    this.experts = const [],
    this.barristers = const [],
  });

  factory InvestmentCaseDetails.fromRecord(RecordModel record) {
    final data = record.data;

    // Get solicitor profile using the new recommended approach
    Map<String, dynamic>? solicitorProfile;
    try {
      final expandedSolicitorProfiles = record.get<List<RecordModel>>(
        "expand.solicitor_profile_id",
      );
      if (expandedSolicitorProfiles != null &&
          expandedSolicitorProfiles.isNotEmpty) {
        solicitorProfile = expandedSolicitorProfiles.first.data;
      }
    } catch (e) {
      debugPrint("Error getting expanded solicitor profile: $e");
    }

    // Parse documents - will be fetched separately from claim_documents collection
    List<UploadedDocumentCategory> fetchedDocuments = [];

    // Parse funding secured
    double? totalSecured = (data['total_funding_secured'] as num?)?.toDouble();

    // Parse associated solicitors
    List<SolicitorProfileModel> associatedSolicitors = [];
    try {
      final expandedSolicitors = record.get<List<RecordModel>>(
        "expand.associated_solicitors",
      );
      if (expandedSolicitors != null) {
        for (var solicitorRecord in expandedSolicitors) {
          final solicitorData = solicitorRecord.data;

          // Try to get user data from expanded user_id
          String? userEmail;
          String? userName;
          try {
            final expandedUser = solicitorRecord.get<RecordModel>(
              "expand.user_id",
            );
            if (expandedUser != null) {
              userEmail = expandedUser.data['email'];
              userName = expandedUser.data['name'];
            }
          } catch (e) {
            debugPrint("Error getting expanded user data: $e");
          }

          // Create a complete data map with user info
          final completeData = Map<String, dynamic>.from(solicitorData);
          if (userEmail != null) completeData['userEmail'] = userEmail;
          if (userName != null) completeData['userName'] = userName;

          associatedSolicitors.add(
            SolicitorProfileModel.fromJson(completeData),
          );
        }
      }
    } catch (e) {
      debugPrint("Error parsing associated solicitors: $e");
    }

    // Parse claim admin IDs
    List<String> claimAdminIds = [];
    try {
      if (data['claim_admins'] is List) {
        claimAdminIds = List<String>.from(data['claim_admins']);
      }
    } catch (e) {
      debugPrint("Error parsing claim admin IDs: $e");
    }

    // Parse experts
    List<Expert> experts = [];
    try {
      final expandedExperts = record.get<List<RecordModel>>("expand.experts");
      if (expandedExperts != null) {
        for (var expertRecord in expandedExperts) {
          experts.add(Expert.fromJson(expertRecord.data));
        }
      }
    } catch (e) {
      debugPrint("Error parsing experts: $e");
    }

    // Parse barristers
    List<Barrister> barristers = [];
    try {
      final expandedBarristers = record.get<List<RecordModel>>(
        "expand.barristers",
      );
      if (expandedBarristers != null) {
        for (var barristerRecord in expandedBarristers) {
          barristers.add(Barrister.fromJson(barristerRecord.data));
        }
      }
    } catch (e) {
      debugPrint("Error parsing barristers: $e");
    }

    return InvestmentCaseDetails(
      id: record.id,
      caseTitle: data['claim_title'] ?? 'N/A',
      caseSummaryPublic:
          data['case_summary_public'] ?? 'Public summary not available.',
      currentStatus: data['application_status'] ?? 'Unknown',
      requiredFundingAmount:
          (data['required_funding_amount'] as num?)?.toDouble() ?? 0.0,
      totalFundingSecured: totalSecured, // Using the fetched or null value
      solicitorName: solicitorProfile?['solicitor_name'],
      lawFirmName: solicitorProfile?['law_firm_name'],
      barristerDetailsPublic:
          data['barrister_details_public'] ??
          'Barrister details not specified.',
      documents: fetchedDocuments,
      stage: data['stage'],
      legalOpinionSuccessProspects:
          (data['legal_opinion_success_prospects'] as num?)?.toDouble(),
      // Claim classification fields
      claimantType: data['claimant_type'],
      claimIndustry: data['claim_industry'],
      claimType: data['claim_type'],
      // Using placeholder values for fields not directly in funding_applications
      fundingTranches:
          data['funding_tranches'] is List
              ? List<String>.from(data['funding_tranches'])
              : ['Tranche information not yet available.'],
      expectedFrfrSchedule:
          data['expected_frfr_schedule'] ??
          'FRFR schedule details not yet available.',
      riskAssessment:
          data['risk_assessment_summary_public'] ??
          'Public risk assessment summary not yet available.',
      investmentTerms:
          data['investment_terms_summary_public'] ??
          'Public investment terms summary not yet available.',
      // Additional fields for solicitor view
      associatedSolicitors: associatedSolicitors,
      claimAdminIds: claimAdminIds,
      experts: experts,
      barristers: barristers,
    );
  }
}

class InvestmentCaseDetailPage extends StatefulWidget {
  static const String routeName = '/investment-case-detail';
  final String caseId;

  const InvestmentCaseDetailPage({super.key, required this.caseId});

  @override
  State<InvestmentCaseDetailPage> createState() =>
      _InvestmentCaseDetailPageState();
}

class _InvestmentCaseDetailPageState extends State<InvestmentCaseDetailPage> {
  final PocketBaseService _pbService = PocketBaseService();
  final ClaimDocumentsService _claimDocumentsService = ClaimDocumentsService();
  InvestmentCaseDetails? _caseDetails;
  bool _isLoadingCase = true;
  bool _isLoadingProfile = true;
  String? _errorMessage;
  RecordModel? _userProfile; // Generic user profile
  String _userType = ''; // 'co_funder', 'solicitor', etc.

  // Stage to interest rate mapping (same as in view_investment_cases_page.dart)
  static const Map<String, int> _stageInterestMap = {
    'STAGE 1: PRE ACTION': 75,
    'STAGE 2: LETTER BEFORE ACTION': 70,
    'STAGE 3: CLAIM ISSUED AND SERVED': 65,
    'STAGE 4: PARTICULARS OF CLAIM SERVED': 60,
    'STAGE 5: DEFENCE RECEIVED': 55,
    'STAGE 6: CASE MANAGEMENT COURT HEARING': 50,
    'STAGE 7: DIRECTIONS HEARINGS COURT': 45,
    'STAGE 8: APPLICATIONS HEARINGS': 40,
    'STAGE 9: WITNESS STATEMENTS': 35,
    'STAGE 10: EXPERT REPORTS': 30,
    'STAGE 11: DISCLOSURE EVIDENCE': 25,
    'STAGE 12: INSPECTIONS': 20,
    'STAGE 13: PRE TRIAL REVIEW': 15,
    'STAGE 14: TRIAL PREPARATIONS': 10,
    'STAGE 15: PART 36 OFFERS MEDIATION': 5,
  };

  int _getInterestForStage(String? stageKey) {
    if (stageKey == null || !_stageInterestMap.containsKey(stageKey)) {
      return 0; // Default or "N/A" indicator
    }
    return _stageInterestMap[stageKey]!;
  }

  @override
  void initState() {
    super.initState();
    _loadAllDetails(); // Call a new method to load both case and profile
  }

  Future<void> _loadAllDetails() async {
    await _fetchUserProfile();
    // Only fetch case details if profile fetch was successful or no critical error
    if (_userProfile != null || _errorMessage == null) {
      await _fetchCaseDetails();
    }
  }

  Future<void> _fetchUserProfile() async {
    setState(() {
      _isLoadingProfile = true;
    });

    final currentUser = _pbService.currentUser;
    final userId = currentUser?.id;

    if (userId == null) {
      setState(() {
        _isLoadingProfile = false;
        _errorMessage = "Not logged in. Cannot fetch profile.";
      });
      return;
    }

    try {
      // First, determine the user type
      final userType = currentUser?.data['user_type'] as String? ?? '';

      setState(() {
        _userType = userType;
      });

      // Fetch the appropriate profile based on user type
      RecordModel? profileRecord;

      if (userType == 'co_funder') {
        profileRecord = await _pbService.client
            .collection('co_funder_profiles')
            .getFirstListItem('user_id="$userId"');
      } else if (userType == 'solicitor') {
        profileRecord = await _pbService.client
            .collection('solicitor_profiles')
            .getFirstListItem('user_id="$userId"');
      }

      setState(() {
        _userProfile = profileRecord;
        _isLoadingProfile = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingProfile = false;
        _errorMessage = "Failed to load your profile: ${e.toString()}";
      });
      debugPrint('Error fetching user profile on detail page: $e');
    }
  }

  Future<void> _fetchCaseDetails() async {
    setState(() {
      _isLoadingCase = true;
      _errorMessage = null;
    });

    try {
      // Expand all related records needed for both co-funder and solicitor views
      final record = await _pbService.client
          .collection('funding_applications')
          .getOne(
            widget.caseId,
            expand:
                'solicitor_profile_id,associated_solicitors.user_id,barristers,experts',
          );

      // Fetch documents from claim_documents collection
      List<UploadedDocumentCategory> documents = [];
      try {
        documents = await _claimDocumentsService
            .getDocumentsForFundingApplication(widget.caseId);
      } catch (e) {
        debugPrint('Error fetching documents: $e');
        // Continue without documents if there's an error
      }

      // Create case details with fetched documents
      final caseDetails = InvestmentCaseDetails.fromRecord(record);
      final updatedCaseDetails = InvestmentCaseDetails(
        id: caseDetails.id,
        caseTitle: caseDetails.caseTitle,
        caseSummaryPublic: caseDetails.caseSummaryPublic,
        currentStatus: caseDetails.currentStatus,
        requiredFundingAmount: caseDetails.requiredFundingAmount,
        totalFundingSecured: caseDetails.totalFundingSecured,
        solicitorName: caseDetails.solicitorName,
        lawFirmName: caseDetails.lawFirmName,
        barristerDetailsPublic: caseDetails.barristerDetailsPublic,
        documents: documents, // Use fetched documents
        stage: caseDetails.stage,
        legalOpinionSuccessProspects: caseDetails.legalOpinionSuccessProspects,
        expectedFrfrSchedule: caseDetails.expectedFrfrSchedule,
        // Claim classification fields
        claimantType: caseDetails.claimantType,
        claimIndustry: caseDetails.claimIndustry,
        claimType: caseDetails.claimType,
        fundingTranches: caseDetails.fundingTranches,
        riskAssessment: caseDetails.riskAssessment,
        investmentTerms: caseDetails.investmentTerms,
        associatedSolicitors: caseDetails.associatedSolicitors,
        claimAdminIds: caseDetails.claimAdminIds,
        experts: caseDetails.experts,
        barristers: caseDetails.barristers,
      );

      setState(() {
        _caseDetails = updatedCaseDetails;
        _isLoadingCase = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingCase = false;
        _errorMessage = "Failed to load case details: ${e.toString()}";
      });
      debugPrint('Error fetching investment case details: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final bool isLoading = _isLoadingCase || _isLoadingProfile;

    if (isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Loading Case Details...')),
        body: CoFunderLoadingWidgets.investmentCaseDetailLoading(context),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: CoFunderErrorWidgets.investmentLoadingError(
          context: context,
          onRetry: () => _loadAllDetails(),
        ),
      );
    }

    if (_caseDetails == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Case Not Found')),
        body: CoFunderErrorWidgets.investmentCaseNotFound(
          context: context,
          onGoBack: () => Navigator.of(context).pop(),
        ),
      );
    }

    final caseDetails = _caseDetails!;

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      body: CustomScrollView(
        slivers: [
          // Enhanced App Bar with better title handling
          SliverAppBar(
            expandedHeight: 180,
            floating: false,
            pinned: true,
            backgroundColor: AppTheme.primaryColor,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              title: SizedBox(
                width: double.infinity,
                child: Text(
                  caseDetails.caseTitle,
                  style: theme.textTheme.h4.copyWith(
                    color: AppTheme.textOnDark,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.left,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppTheme.primaryColor,
                      AppTheme.primaryColor.withValues(alpha: 0.9),
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 80),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Full title display in expanded state with flexible sizing

                        // Combined Status Row
                        Row(
                          children: [
                            // FRFR Badge
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 3,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.amber.withValues(alpha: 0.9),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.star,
                                    size: 12,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(width: 3),
                                  Text(
                                    '${_getInterestForStage(caseDetails.stage)}% FRFR',
                                    style: theme.textTheme.small.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 11,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 6),
                            // Stage Badge
                            if (caseDetails.stage != null)
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 3,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withValues(alpha: 0.8),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    caseDetails.stage!,
                                    style: theme.textTheme.small.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                      fontSize: 10,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Main Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Quick Stats Row
                  _buildQuickStatsRow(context, caseDetails),

                  const SizedBox(height: 24),

                  // Public Summary (if available)
                  if (caseDetails.caseSummaryPublic != null)
                    _buildSummaryCard(context, caseDetails.caseSummaryPublic!),

                  if (caseDetails.caseSummaryPublic != null)
                    const SizedBox(height: 24),

                  // Claim Classification Information
                  _buildClaimClassificationCard(context, caseDetails),

                  const SizedBox(height: 24),

                  // Funding Information with Progress
                  _buildFundingCard(context, caseDetails),

                  const SizedBox(height: 24),

                  // Documents with enhanced design
                  _buildDocumentsCard(context, caseDetails),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Horizontal scrollable stats cards
  Widget _buildQuickStatsRow(
    BuildContext context,
    InvestmentCaseDetails caseDetails,
  ) {
    return SizedBox(
      height: 100,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildCompactStatCard(
            context,
            'Required',
            NumberFormat.currency(
              locale: 'en_GB',
              symbol: '£',
            ).format(caseDetails.requiredFundingAmount),
            Icons.account_balance_wallet,
            Colors.blue.shade700,
          ),
          const SizedBox(width: 12),
          _buildCompactStatCard(
            context,
            'FRFR Rate',
            '${_getInterestForStage(caseDetails.stage)}%',
            Icons.trending_up,
            Colors.green.shade600,
          ),
          const SizedBox(width: 12),
          _buildCompactStatCard(
            context,
            'Documents',
            '${caseDetails.documents.length}',
            Icons.folder_copy,
            Colors.purple.shade600,
          ),
          if (caseDetails.totalFundingSecured != null) ...[
            const SizedBox(width: 12),
            _buildCompactStatCard(
              context,
              'Secured',
              NumberFormat.currency(
                locale: 'en_GB',
                symbol: '£',
              ).format(caseDetails.totalFundingSecured!),
              Icons.check_circle,
              Colors.teal.shade600,
            ),
          ],
        ],
      ),
    );
  }

  // Compact horizontal stat card
  Widget _buildCompactStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = ShadTheme.of(context);

    return Container(
      width: 140,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [color, color.withValues(alpha: 0.8)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(icon, size: 18, color: Colors.white),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(Icons.arrow_upward, size: 12, color: Colors.white),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: theme.textTheme.small.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
              fontWeight: FontWeight.w500,
              fontSize: 11,
            ),
          ),
          Text(
            value,
            style: theme.textTheme.p.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.white,
              fontSize: 14,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // Summary Card with better styling
  Widget _buildSummaryCard(BuildContext context, String summary) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Public Summary',
                  style: theme.textTheme.h4.copyWith(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              summary,
              style: theme.textTheme.p.copyWith(
                height: 1.6,
                color: theme.colorScheme.foreground,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Claim Classification Card
  Widget _buildClaimClassificationCard(
    BuildContext context,
    InvestmentCaseDetails caseDetails,
  ) {
    final theme = ShadTheme.of(context);

    // Check if any classification data is available
    final hasClassificationData =
        caseDetails.claimantType != null ||
        caseDetails.claimIndustry != null ||
        caseDetails.claimType != null;

    if (!hasClassificationData) {
      return const SizedBox.shrink(); // Don't show the card if no data
    }

    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.indigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.category,
                    color: Colors.indigo.shade600,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Claim Classification',
                        style: theme.textTheme.h4.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Case type and industry details',
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Classification details in a grid layout
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                if (caseDetails.claimantType != null)
                  _buildClassificationItem(
                    context,
                    'Claimant Type',
                    caseDetails.claimantType!,
                    Icons.people,
                    Colors.blue.shade600,
                  ),
                if (caseDetails.claimIndustry != null)
                  _buildClassificationItem(
                    context,
                    'Industry',
                    caseDetails.claimIndustry!,
                    Icons.business,
                    Colors.green.shade600,
                  ),
                if (caseDetails.claimType != null)
                  _buildClassificationItem(
                    context,
                    'Claim Type',
                    caseDetails.claimType!,
                    Icons.gavel,
                    Colors.orange.shade600,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Individual classification item
  Widget _buildClassificationItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = ShadTheme.of(context);

    return Container(
      constraints: const BoxConstraints(minWidth: 150),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: theme.textTheme.small.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: theme.textTheme.p.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 13,
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced Funding Card with progress visualization
  Widget _buildFundingCard(
    BuildContext context,
    InvestmentCaseDetails caseDetails,
  ) {
    final theme = ShadTheme.of(context);
    final fundingProgress =
        caseDetails.totalFundingSecured != null
            ? (caseDetails.totalFundingSecured! /
                    caseDetails.requiredFundingAmount)
                .clamp(0.0, 1.0)
            : 0.0;

    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_balance,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Funding Information',
                  style: theme.textTheme.h4.copyWith(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Funding amounts
            _buildFundingRow(
              context,
              'Total Required',
              NumberFormat.currency(
                locale: 'en_GB',
                symbol: '£',
              ).format(caseDetails.requiredFundingAmount),
              Icons.gps_fixed,
              Colors.blue,
            ),

            if (caseDetails.totalFundingSecured != null) ...[
              const SizedBox(height: 12),
              _buildFundingRow(
                context,
                'Secured',
                NumberFormat.currency(
                  locale: 'en_GB',
                  symbol: '£',
                ).format(caseDetails.totalFundingSecured!),
                Icons.check_circle,
                Colors.green,
              ),

              const SizedBox(height: 16),
              // Progress bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Funding Progress',
                        style: theme.textTheme.small.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '${(fundingProgress * 100).toStringAsFixed(1)}%',
                        style: theme.textTheme.small.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.muted,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: fundingProgress,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],

            const SizedBox(height: 20),

            // FRFR Rate with enhanced design
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.green.shade600, Colors.green.shade700],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(Icons.star, color: Colors.white, size: 24),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Fixed Rate Funder Returns (FRFR)',
                          style: theme.textTheme.small.copyWith(
                            color: Colors.white.withValues(alpha: 0.9),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_getInterestForStage(caseDetails.stage)}%',
                          style: theme.textTheme.h3.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(Icons.arrow_forward_ios, color: Colors.white, size: 16),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Funding row helper
  Widget _buildFundingRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = ShadTheme.of(context);

    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 8),
        Text(
          label,
          style: theme.textTheme.p.copyWith(fontWeight: FontWeight.w500),
        ),
        const Spacer(),
        Text(
          value,
          style: theme.textTheme.p.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  // Enhanced Documents Card
  Widget _buildDocumentsCard(
    BuildContext context,
    InvestmentCaseDetails caseDetails,
  ) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.folder_special,
                    color: Colors.orange.shade600,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Case Documents',
                        style: theme.textTheme.h4.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Download and review case materials',
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade600,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${caseDetails.documents.length}',
                    style: theme.textTheme.small.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (caseDetails.documents.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.muted.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: theme.colorScheme.mutedForeground,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'No documents available for this claim.',
                        style: theme.textTheme.p.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            else
              ...caseDetails.documents.map(
                (doc) =>
                    _buildEnhancedDocumentRow(context, doc, caseDetails.id),
              ),
          ],
        ),
      ),
    );
  }

  // Enhanced document row with better styling
  Widget _buildEnhancedDocumentRow(
    BuildContext context,
    UploadedDocumentCategory doc,
    String caseId,
  ) {
    final theme = ShadTheme.of(context);

    // Get the current version of the document
    final currentVersion =
        doc.versions.isNotEmpty
            ? doc.versions.firstWhere(
              (v) => v.fileId == doc.currentVersionFileId,
              orElse: () => doc.versions.last,
            )
            : null;

    if (currentVersion == null) {
      return Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: theme.colorScheme.muted.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.error_outline,
              color: theme.colorScheme.mutedForeground,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                '${doc.logicalName} - No version available',
                style: theme.textTheme.p.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Get file extension for icon
    final extension = currentVersion.filename.split('.').last.toLowerCase();
    IconData fileIcon = Icons.description;
    Color iconColor = Colors.blue;

    switch (extension) {
      case 'pdf':
        fileIcon = Icons.picture_as_pdf;
        iconColor = Colors.red;
        break;
      case 'doc':
      case 'docx':
        fileIcon = Icons.description;
        iconColor = Colors.blue;
        break;
      case 'xls':
      case 'xlsx':
        fileIcon = Icons.table_chart;
        iconColor = Colors.green;
        break;
      default:
        fileIcon = Icons.insert_drive_file;
        iconColor = Colors.grey;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(fileIcon, color: iconColor, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  doc.logicalName,
                  style: theme.textTheme.p.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  currentVersion.filename,
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
                if (currentVersion.notes != null &&
                    currentVersion.notes!.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    currentVersion.notes!,
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ShadButton.outline(
                size: ShadButtonSize.sm,
                child: const Icon(Icons.visibility, size: 16),
                onPressed: () => _previewDocument(context, currentVersion),
              ),
              const SizedBox(width: 8),
              ShadButton.outline(
                size: ShadButtonSize.sm,
                child: const Icon(Icons.download, size: 16),
                onPressed:
                    () => _downloadDocument(context, currentVersion, caseId),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper method to handle document download
  Future<void> _downloadDocument(
    BuildContext context,
    DocumentVersion version,
    String caseId,
  ) async {
    try {
      // Get the file URL using the ClaimDocumentsService
      final fileUrl = await _claimDocumentsService.getFileUrl(version.fileId);

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show success toast
      if (context.mounted) {
        ShadToaster.of(context).show(
          ShadToast(
            title: const Text('Download'),
            description: Text('Downloading ${version.filename}...'),
          ),
        );
      }

      // Launch the URL to download the file
      final uri = Uri.parse(fileUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw Exception('Could not launch download URL');
      }

      debugPrint('Document URL: $fileUrl');
    } catch (e) {
      debugPrint('Error downloading document: $e');

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show error toast
      if (context.mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Download Error'),
            description: Text(
              'Failed to download ${version.filename}: ${e.toString()}',
            ),
          ),
        );
      }
    }
  }

  // Helper method to handle document preview
  Future<void> _previewDocument(
    BuildContext context,
    DocumentVersion version,
  ) async {
    try {
      // Get the file URL using the ClaimDocumentsService
      final fileUrl = await _claimDocumentsService.getFileUrl(version.fileId);

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Navigate to document preview screen
      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => DocumentPreviewWidget(
                  url: fileUrl,
                  fileName: version.filename,
                ),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error previewing document: $e');

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Show error toast
      if (context.mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Preview Error'),
            description: Text(
              'Failed to preview ${version.filename}: ${e.toString()}',
            ),
          ),
        );
      }
    }
  }
}
