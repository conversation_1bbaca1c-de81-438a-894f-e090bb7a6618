import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:file_picker/file_picker.dart';
import 'package:http/http.dart' as http;
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

class CoFunderProfileLevel3FormPage extends StatefulWidget {
  static const String routeName = '/cofunder-profile-level3-form';

  CoFunderProfileLevel3FormPage({super.key});

  @override
  State<CoFunderProfileLevel3FormPage> createState() =>
      _CoFunderProfileLevel3FormPageState();
}

class _CoFunderProfileLevel3FormPageState
    extends State<CoFunderProfileLevel3FormPage> {
  final _formKey = GlobalKey<ShadFormState>();
  final PocketBaseService _pbService = PocketBaseService();
  bool _isSubmitting = false;

  // --- State Variables for Form Fields ---
  String? _netWorth;
  String? _surplusCash;
  String? _riskAppetite;
  String? _idType;
  final Map<String, String?> _assetPortfolio = {
    'property': null,
    'gold': null,
    'silver': null,
    'diamonds': null,
    'other_precious_metals': null,
    'crypto_currencies': null,
    'publicly_quoted_shares': null,
    'other_commodities': null,
  };
  final Map<String, String> _bankAccountDetails = {
    'bank_name': '',
    'account_number': '',
    'sort_code': '',
  };
  PlatformFile? _idDocument;
  PlatformFile? _proofDocument;

  // --- Dropdown Options ---
  final List<String> _netWorthOptions = const [
    '0 - £100,000',
    '£100,000 - £250,000',
    '£250,000 - £500,000',
    '£500,000 - £750,000',
    '£750,000 - £1m',
    '£1m - £2.5m',
    '£2.5m - £5m',
    '£5m+',
  ];

  final List<String> _surplusCashOptions = const [
    '0 - £100,000',
    '£100,000 - £250,000',
    '£250,000 - £500,000',
    '£500,000 - £750,000',
    '£750,000 - £1m',
    '£1m - £2.5m',
    '£2.5m - £5m',
    '£5m+',
  ];

  final List<String> _riskAppetiteOptions = const ['Low', 'Medium', 'High'];

  final List<Map<String, String>> _idTypeOptions = const [
    {'value': 'passport', 'label': 'Passport'},
    {'value': 'drivers_license', 'label': 'Driver\'s License'},
    {'value': 'national_id', 'label': 'National ID Photo Card'},
  ];

  final List<String> _assetValueRangeOptions = const [
    '£1,000 - £10,000',
    '£10,000 - £50,000',
    '£50,000 - £100,000',
    '£100,000 - £500,000',
    '£500,000 - £1,000,000',
    '£1,000,000+',
  ];

  // --- Helper Methods ---
  bool _hasAtLeastOneAsset() {
    return _assetPortfolio.values.any(
      (value) => value != null && value.isNotEmpty,
    );
  }

  Future<void> _pickFile(String type) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png', 'gif'],
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.single;
        if (file.size > 5 * 1024 * 1024) {
          // 5MB limit
          if (!mounted) return;
          ShadToaster.of(context).show(
            ShadToast.destructive(
              title: const Text('File Too Large'),
              description: const Text('File size must be less than 5MB.'),
            ),
          );
          return;
        }
        setState(() {
          if (type == 'id') {
            _idDocument = file;
          } else if (type == 'proof') {
            _proofDocument = file;
          }
        });
      }
    } catch (e) {
      LoggerService.error('Error picking file', e);
      if (!mounted) return;
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Error Picking File'),
          description: Text('An error occurred: ${e.toString()}'),
        ),
      );
    }
  }

  // --- Submission Logic ---
  Future<void> _submitLevel2Profile() async {
    if (!_formKey.currentState!.saveAndValidate()) {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Validation Error'),
          description: const Text('Please correct the errors in the form.'),
        ),
      );
      return;
    }

    if (_idDocument == null || _proofDocument == null) {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Documents Required'),
          description: const Text(
            'Please upload both ID and Proof of Residence documents.',
          ),
        ),
      );
      return;
    }

    if (!_hasAtLeastOneAsset()) {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Asset Portfolio Incomplete'),
          description: const Text(
            'Please select at least one asset value range.',
          ),
        ),
      );
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      final userId = _pbService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not logged in.');
      }

      // Fetch the existing co_funder_profiles record
      final coFunderProfileRecords = await _pbService.client
          .collection('co_funder_profiles')
          .getFullList(filter: 'user_id = "$userId"');

      if (coFunderProfileRecords.isEmpty) {
        throw Exception('Co-funder profile not found for the current user.');
      }
      final coFunderProfileId = coFunderProfileRecords.first.id;

      final body = <String, dynamic>{
        'net_worth': _netWorth,
        'available_surplus_cash': _surplusCash,
        'risk_appetite': _riskAppetite?.toLowerCase(),
        'id_type': _idType,
        'bank_account_details': jsonEncode(_bankAccountDetails),
        'assets_portfolio': jsonEncode(_assetPortfolio),
        'kyc_status':
            'pending_level_3_submission', // Or a more appropriate status for level 3
        'level_3_application_submitted_at':
            DateTime.now()
                .toIso8601String(), // Assuming a new timestamp field for L3
        'level_3_application_submitted': true, // Set this new field
      };

      final files = <http.MultipartFile>[
        http.MultipartFile.fromBytes(
          'identity_document', // Corrected field name
          _idDocument!.bytes!,
          filename: _idDocument!.name,
        ),
        http.MultipartFile.fromBytes(
          'proof_of_residence',
          _proofDocument!.bytes!,
          filename: _proofDocument!.name,
        ),
      ];

      await _pbService.client
          .collection('co_funder_profiles')
          .update(coFunderProfileId, body: body, files: files);

      // Attempt to create a notification - ideally this is a server-side hook
      try {
        await _pbService.client
            .collection('notifications')
            .create(
              body: {
                'recipientId': [userId], // Use array format for recipientId
                'title': 'Level 3 Application Submitted',
                'message':
                    'Your Level 3 profile information has been successfully submitted and is now pending review.',
                'type': 'level_3_submitted',
                'isRead': false, // Use isRead instead of is_read
                // 'icon': 'LucideIcons.fileCheck2' // Example icon
              },
            );
      } catch (notifError) {
        LoggerService.error(
          'Failed to create level 3 submission notification',
          notifError,
        );
        // Non-critical, so we don't necessarily show an error to the user for this
      }

      if (!mounted) return;
      ShadToaster.of(context).show(
        ShadToast(
          title: const Text('Profile Update Submitted'),
          description: const Text(
            'Your Level 3 profile information has been submitted for review.',
          ),
        ),
      );
      Navigator.of(context).pop();
    } catch (e) {
      LoggerService.error('Error submitting Level 3 profile', e);
      if (!mounted) return;
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Submission Error'),
          description: Text('An error occurred: ${e.toString()}'),
        ),
      );
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final materialTheme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Access Desire Level (Level 3)',
          style: materialTheme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: materialTheme.appBarTheme.backgroundColor,
        iconTheme: materialTheme.appBarTheme.iconTheme,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              materialTheme.colorScheme.surface,
              materialTheme.colorScheme.surface.withOpacity(0.8),
            ],
          ),
        ),
        child: ShadForm(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Progress indicator
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Column(
                    children: [
                      LinearProgressIndicator(
                        value: 0.75,
                        backgroundColor: theme.colorScheme.muted,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          theme.colorScheme.primary,
                        ),
                        minHeight: 8,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Profile Completion',
                            style: theme.textTheme.small.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            '75%',
                            style: theme.textTheme.small.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Financial Information Section
                _buildSectionCard(
                  title: 'Financial Information',
                  icon: LucideIcons.wallet,
                  children: [
                    _buildAnimatedFormField(
                      child: ShadSelectFormField<String>(
                        id: 'net_worth',
                        label: const Text('Net Worth'),
                        placeholder: const Text('Select your net worth range'),
                        options:
                            _netWorthOptions
                                .map(
                                  (o) => ShadOption(value: o, child: Text(o)),
                                )
                                .toList(),
                        selectedOptionBuilder: (context, value) => Text(value),
                        onChanged: (value) => setState(() => _netWorth = value),
                        validator:
                            (v) =>
                                v == null || v.isEmpty
                                    ? 'Net worth is required.'
                                    : null,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildAnimatedFormField(
                      child: ShadSelectFormField<String>(
                        id: 'surplus_cash',
                        label: const Text('Available Surplus Cash'),
                        placeholder: const Text('Select surplus cash range'),
                        options:
                            _surplusCashOptions
                                .map(
                                  (o) => ShadOption(value: o, child: Text(o)),
                                )
                                .toList(),
                        selectedOptionBuilder: (context, value) => Text(value),
                        onChanged:
                            (value) => setState(() => _surplusCash = value),
                        validator:
                            (v) =>
                                v == null || v.isEmpty
                                    ? 'Surplus cash is required.'
                                    : null,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildAnimatedFormField(
                      child: ShadSelectFormField<String>(
                        id: 'risk_appetite',
                        label: const Text('Risk Appetite'),
                        placeholder: const Text('Select your risk appetite'),
                        options:
                            _riskAppetiteOptions
                                .map(
                                  (o) => ShadOption(value: o, child: Text(o)),
                                )
                                .toList(),
                        selectedOptionBuilder: (context, value) => Text(value),
                        onChanged:
                            (value) => setState(() => _riskAppetite = value),
                        validator:
                            (v) =>
                                v == null || v.isEmpty
                                    ? 'Risk appetite is required.'
                                    : null,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Identification & Residence Section
                _buildSectionCard(
                  title: 'Identification & Residence',
                  icon: LucideIcons.userCheck,
                  children: [
                    _buildAnimatedFormField(
                      child: ShadSelectFormField<String>(
                        id: 'id_type',
                        label: const Text('ID Type'),
                        placeholder: const Text('Select ID type'),
                        options:
                            _idTypeOptions
                                .map(
                                  (o) => ShadOption(
                                    value: o['value']!,
                                    child: Text(o['label']!),
                                  ),
                                )
                                .toList(),
                        selectedOptionBuilder: (context, value) {
                          final selectedLabel =
                              _idTypeOptions.firstWhere(
                                (opt) => opt['value'] == value,
                                orElse: () => {'label': value},
                              )['label'];
                          return Text(selectedLabel!);
                        },
                        onChanged: (value) => setState(() => _idType = value),
                        validator:
                            (v) =>
                                v == null || v.isEmpty
                                    ? 'ID type is required.'
                                    : null,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildFileUploadRow(
                      label: 'Identification Document',
                      file: _idDocument,
                      onTap: () => _pickFile('id'),
                    ),
                    const SizedBox(height: 16),
                    _buildFileUploadRow(
                      label: 'Proof of Residence',
                      file: _proofDocument,
                      onTap: () => _pickFile('proof'),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Asset Portfolio Section
                _buildSectionCard(
                  title: 'Asset Portfolio',
                  icon: LucideIcons.briefcase,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Text(
                        'Select value range for each asset you hold (at least one required).',
                        style: theme.textTheme.muted,
                      ),
                    ),
                    ..._assetPortfolio.keys.map((assetKey) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: _buildAnimatedFormField(
                          child: ShadSelectFormField<String>(
                            id: 'asset_$assetKey',
                            label: Text(_formatAssetKey(assetKey)),
                            placeholder: const Text('Select value range'),
                            options: [
                              const ShadOption(
                                value: '',
                                child: Text('N/A - Do not hold'),
                              ),
                              ..._assetValueRangeOptions
                                  .map(
                                    (o) => ShadOption(value: o, child: Text(o)),
                                  )
                                  .toList(),
                            ],
                            initialValue: _assetPortfolio[assetKey],
                            selectedOptionBuilder:
                                (context, value) => Text(
                                  value.isEmpty ? 'N/A - Do not hold' : value,
                                ),
                            onChanged:
                                (value) => setState(
                                  () =>
                                      _assetPortfolio[assetKey] =
                                          value == '' ? null : value,
                                ),
                          ),
                        ),
                      );
                    }).toList(),
                  ],
                ),

                const SizedBox(height: 20),

                // Bank Account Details Section
                _buildSectionCard(
                  title: 'Bank Account Details',
                  icon: LucideIcons.building,
                  children: [
                    _buildAnimatedFormField(
                      child: ShadInputFormField(
                        id: 'bank_name',
                        label: const Text('Bank Name'),
                        placeholder: const Text('Enter bank name'),
                        initialValue: _bankAccountDetails['bank_name'],
                        onChanged: (v) => _bankAccountDetails['bank_name'] = v,
                        validator:
                            (v) => v.isEmpty ? 'Bank name is required.' : null,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildAnimatedFormField(
                      child: ShadInputFormField(
                        id: 'account_number',
                        label: const Text('Account Number'),
                        placeholder: const Text('Enter account number'),
                        initialValue: _bankAccountDetails['account_number'],
                        keyboardType: TextInputType.number,
                        onChanged:
                            (v) => _bankAccountDetails['account_number'] = v,
                        validator:
                            (v) =>
                                v.isEmpty
                                    ? 'Account number is required.'
                                    : null,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildAnimatedFormField(
                      child: ShadInputFormField(
                        id: 'sort_code',
                        label: const Text('Sort Code'),
                        placeholder: const Text(
                          'Enter sort code (e.g., 12-34-56)',
                        ),
                        initialValue: _bankAccountDetails['sort_code'],
                        onChanged: (v) => _bankAccountDetails['sort_code'] = v,
                        validator:
                            (v) => v.isEmpty ? 'Sort code is required.' : null,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // Submit Buttons
                Container(
                  decoration: BoxDecoration(
                    color: theme.colorScheme.card,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ShadButton.outline(
                        child: const Text('Cancel'),
                        onPressed:
                            _isSubmitting
                                ? null
                                : () => Navigator.of(context).pop(),
                      ),
                      const SizedBox(width: 16),
                      ShadButton(
                        enabled: !_isSubmitting,
                        child:
                            _isSubmitting
                                ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: theme.colorScheme.background,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    const Text('Submitting...'),
                                  ],
                                )
                                : Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(LucideIcons.check, size: 16),
                                    const SizedBox(width: 8),
                                    const Text('Submit Profile Update'),
                                  ],
                                ),
                        onPressed: _submitLevel2Profile,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    final theme = ShadTheme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: theme.colorScheme.primary, size: 20),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: theme.textTheme.h4.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          // Section content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedFormField({required Widget child}) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeOutQuad,
      builder: (context, value, animatedChild) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, 15 * (1 - value)),
            child: animatedChild,
          ),
        );
      },
      child: child,
    );
  }

  Widget _buildFileUploadRow({
    required String label,
    required PlatformFile? file,
    required VoidCallback onTap,
  }) {
    final theme = ShadTheme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.small.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              border: Border.all(color: theme.colorScheme.border),
              borderRadius: BorderRadius.circular(8),
              color:
                  file != null
                      ? theme.colorScheme.primary.withOpacity(0.05)
                      : theme.colorScheme.background,
            ),
            child: Row(
              children: [
                Icon(
                  LucideIcons.cloudUpload,
                  size: 18,
                  color:
                      file != null
                          ? theme.colorScheme.primary
                          : theme.colorScheme.muted.withOpacity(0.8),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    file?.name ?? 'Select File (PDF, DOC, JPG, PNG - Max 5MB)',
                    style: theme.textTheme.small.copyWith(
                      color:
                          file != null
                              ? theme.colorScheme.primary
                              : theme.colorScheme.muted.withOpacity(0.8),
                      fontWeight:
                          file != null ? FontWeight.w500 : FontWeight.normal,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (file != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${(file.size / 1024 / 1024).toStringAsFixed(1)} MB',
                      style: theme.textTheme.small.copyWith(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  String _formatAssetKey(String key) {
    return key
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }
}
