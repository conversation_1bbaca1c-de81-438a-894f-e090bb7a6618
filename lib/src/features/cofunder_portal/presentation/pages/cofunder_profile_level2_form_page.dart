import 'dart:convert'; // For jsonEncode
import 'package:flutter/material.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:country_picker/country_picker.dart';
import 'package:intl/intl.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

class CoFunderProfileLevel2FormPage extends StatefulWidget {
  static const String routeName = '/cofunder-profile-level2-form';
  CoFunderProfileLevel2FormPage({super.key});

  @override
  State<CoFunderProfileLevel2FormPage> createState() =>
      _CoFunderProfileLevel2FormPageState();
}

class _CoFunderProfileLevel2FormPageState
    extends State<CoFunderProfileLevel2FormPage> {
  final _formKey = GlobalKey<ShadFormState>(); // Changed to ShadFormState
  final PocketBaseService _pbService = PocketBaseService();
  bool _isSubmitting = false;

  // --- Controllers for KYC fields ---
  final _occupationController = TextEditingController();
  final _dateController = TextEditingController(); // For Date of Birth display
  final _residentialAddressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateProvinceController = TextEditingController();
  final _postcodeController = TextEditingController();

  // --- State for selected country/nationality & DOB ---
  String? _nationalityName;
  String? _nationalityCode;
  String? _countryOfResidenceName;
  String? _countryOfResidenceCode;
  DateTime? _selectedDateOfBirth;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _occupationController.dispose();
    _dateController.dispose();
    _residentialAddressController.dispose();
    _cityController.dispose();
    _stateProvinceController.dispose();
    _postcodeController.dispose();
    super.dispose();
  }

  Future<void> _submitKycProfile() async {
    if (!_formKey.currentState!.saveAndValidate()) {
      // Changed to saveAndValidate
      ShadToaster.of(context).show(
        // Changed to ShadToaster
        ShadToast.destructive(
          title: const Text('Validation Error'),
          description: const Text('Please correct the errors in the form.'),
        ),
      );
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      final userId = _pbService.currentUser?.id;
      if (userId == null) throw Exception('User not logged in.');

      final profileRecords = await _pbService.client
          .collection('co_funder_profiles')
          .getFullList(filter: 'user_id = "$userId"');
      if (profileRecords.isEmpty)
        throw Exception('Co-funder profile not found.');

      final coFunderProfileId = profileRecords.first.id;

      final body = <String, dynamic>{
        'occupation': _occupationController.text,
        'date_of_birth': _selectedDateOfBirth?.toIso8601String().split('T')[0],
        'nationality': _nationalityName,
        'nationality_code': _nationalityCode,
        'country_of_residence': _countryOfResidenceName,
        'country_of_residence_code': _countryOfResidenceCode,
        'residential_address': _residentialAddressController.text,
        'city': _cityController.text,
        'state_province': _stateProvinceController.text,
        'postcode': _postcodeController.text,
        'kyc_status':
            'pending_kyc_level_2_submission', // You might want to update this status based on the new boolean fields later
        'level_2_kyc_submitted_at': DateTime.now().toIso8601String(),
        'level_2_application_submitted': true, // Set this new field
      };

      await _pbService.client
          .collection('co_funder_profiles')
          .update(coFunderProfileId, body: body);

      // Attempt to create a notification - ideally this is a server-side hook
      try {
        await _pbService.client
            .collection('notifications')
            .create(
              body: {
                'recipientId': [userId], // Use array format for recipientId
                'title': 'Level 2 Application Submitted',
                'message':
                    'Your KYC (Level 2) information has been successfully submitted and is now pending review.',
                'type': 'level_2_submitted',
                'isRead': false, // Use isRead instead of is_read
                // 'icon': 'LucideIcons.fileCheck2' // Example icon
              },
            );
      } catch (notifError) {
        LoggerService.error(
          'Failed to create level 2 submission notification',
          notifError,
        );
        // Non-critical, so we don't necessarily show an error to the user for this
      }

      if (!mounted) return;
      ShadToaster.of(context).show(
        ShadToast(
          title: const Text('KYC Information Submitted'),
          description: const Text(
            'Your KYC information has been submitted for review.',
          ),
        ),
      );
      Navigator.of(context).pop();
    } catch (e) {
      LoggerService.error('Error submitting KYC profile', e);
      if (!mounted) return;
      ShadToaster.of(context).show(
        // Changed to ShadToaster
        ShadToast.destructive(
          title: const Text('Submission Error'),
          description: Text('An error occurred: ${e.toString()}'),
        ),
      );
    } finally {
      if (mounted) setState(() => _isSubmitting = false);
    }
  }

  // _buildTextFormField removed as we'll use Shadcn components directly or new helpers

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    final theme = ShadTheme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: theme.colorScheme.primary, size: 20),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: theme.textTheme.h4.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedFormField({required Widget child}) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeOutQuad,
      builder: (context, value, animatedChild) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, 15 * (1 - value)),
            child: animatedChild,
          ),
        );
      },
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context); // ShadTheme
    final materialTheme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Complete Your Profile (KYC)',
          style: materialTheme.appBarTheme.titleTextStyle?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: materialTheme.appBarTheme.backgroundColor,
        iconTheme: materialTheme.appBarTheme.iconTheme,
        elevation: 0, // Match level 3
      ),
      body: Container(
        // Added Container for gradient background
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              materialTheme.colorScheme.surface,
              materialTheme.colorScheme.surface.withOpacity(0.8),
            ],
          ),
        ),
        child: ShadForm(
          // Changed to ShadForm
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16), // Simplified padding
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildSectionCard(
                  title: 'Personal Information',
                  icon: LucideIcons.user, // Changed to a valid icon
                  children: [
                    _buildAnimatedFormField(
                      child: ShadInputFormField(
                        id: 'occupation',
                        controller: _occupationController,
                        label: const Text('Occupation'),
                        placeholder: const Text('Enter your occupation'),
                        validator:
                            (v) => v.isEmpty ? 'Occupation is required.' : null,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildAnimatedFormField(
                      child: GestureDetector(
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate:
                                _selectedDateOfBirth ??
                                DateTime(
                                  DateTime.now().year - 18,
                                  DateTime.now().month,
                                  DateTime.now().day,
                                ),
                            firstDate: DateTime(1900),
                            lastDate: DateTime(
                              DateTime.now().year - 18,
                              DateTime.now().month,
                              DateTime.now().day,
                            ),
                          );
                          if (date != null) {
                            setState(() {
                              _selectedDateOfBirth = date;
                              _dateController.text = DateFormat(
                                'yyyy-MM-dd',
                              ).format(date);
                            });
                          }
                        },
                        child: AbsorbPointer(
                          child: ShadInputFormField(
                            id: 'date_of_birth',
                            controller: _dateController,
                            label: const Text('Date of Birth'),
                            placeholder: const Text(
                              'Select your date of birth',
                            ),
                            readOnly: true,
                            validator:
                                (v) =>
                                    v.isEmpty
                                        ? 'Date of Birth is required.'
                                        : null,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildAnimatedFormField(
                      child: GestureDetector(
                        onTap: () {
                          showCountryPicker(
                            context: context,
                            showPhoneCode: false,
                            onSelect: (Country country) {
                              setState(() {
                                _nationalityName = country.name;
                                _nationalityCode = country.countryCode;
                                // Update the controller for ShadInputFormField
                                // The TextEditingController for nationality will rebuild with the new _nationalityName
                              });
                            },
                          );
                        },
                        child: AbsorbPointer(
                          child: ShadInputFormField(
                            id: 'nationality',
                            // Use a key that changes when _nationalityName changes to force rebuild, or ensure controller is updated.
                            // For simplicity, relying on setState to rebuild the parent and thus this field with new controller text.
                            controller: TextEditingController(
                              text: _nationalityName ?? '',
                            ),
                            label: const Text('Nationality'),
                            placeholder: const Text('Select your nationality'),
                            readOnly: true,
                            validator:
                                (v) =>
                                    _nationalityName == null ||
                                            _nationalityName!.isEmpty
                                        ? 'Nationality is required.'
                                        : null,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                _buildSectionCard(
                  title: 'Contact & Residence',
                  icon: LucideIcons.mapPin, // Changed to mapPin
                  children: [
                    _buildAnimatedFormField(
                      child: GestureDetector(
                        onTap: () {
                          showCountryPicker(
                            context: context,
                            showPhoneCode: false,
                            onSelect: (Country country) {
                              setState(() {
                                _countryOfResidenceName = country.name;
                                _countryOfResidenceCode = country.countryCode;
                              });
                            },
                          );
                        },
                        child: AbsorbPointer(
                          child: ShadInputFormField(
                            id: 'country_of_residence',
                            controller: TextEditingController(
                              text: _countryOfResidenceName ?? '',
                            ),
                            label: const Text('Country of Residence'),
                            placeholder: const Text(
                              'Select your country of residence',
                            ),
                            readOnly: true,
                            validator:
                                (v) =>
                                    _countryOfResidenceName == null ||
                                            _countryOfResidenceName!.isEmpty
                                        ? 'Country of Residence is required.'
                                        : null,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildAnimatedFormField(
                      child: ShadInputFormField(
                        id: 'residential_address',
                        controller: _residentialAddressController,
                        label: const Text('Residential Address (Street)'),
                        placeholder: const Text('Enter your street address'),
                        validator:
                            (v) =>
                                v.isEmpty
                                    ? 'Residential Address is required.'
                                    : null,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildAnimatedFormField(
                      child: ShadInputFormField(
                        id: 'city',
                        controller: _cityController,
                        label: const Text('City'),
                        placeholder: const Text('Enter your city'),
                        validator:
                            (v) => v.isEmpty ? 'City is required.' : null,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildAnimatedFormField(
                      child: ShadInputFormField(
                        id: 'state_province',
                        controller: _stateProvinceController,
                        label: const Text('State/Province'),
                        placeholder: const Text('Enter your state or province'),
                        validator:
                            (v) =>
                                v.isEmpty
                                    ? 'State/Province is required.'
                                    : null,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildAnimatedFormField(
                      child: ShadInputFormField(
                        id: 'postcode',
                        controller: _postcodeController,
                        label: const Text('Postcode/Zip Code'),
                        placeholder: const Text(
                          'Enter your postcode or zip code',
                        ),
                        validator:
                            (v) =>
                                v.isEmpty
                                    ? 'Postcode/Zip Code is required.'
                                    : null,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24), // Adjusted spacing
                Container(
                  // Submit buttons container like level 3
                  decoration: BoxDecoration(
                    color: theme.colorScheme.card,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ShadButton.outline(
                        child: const Text('Cancel'),
                        onPressed:
                            _isSubmitting
                                ? null
                                : () => Navigator.of(context).pop(),
                      ),
                      const SizedBox(width: 16),
                      ShadButton(
                        enabled: !_isSubmitting,
                        onPressed:
                            _isSubmitting
                                ? null
                                : _submitKycProfile, // Corrected onPressed placement
                        child:
                            _isSubmitting
                                ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: theme.colorScheme.background,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    const Text('Submitting...'),
                                  ],
                                )
                                : const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(LucideIcons.check, size: 16),
                                    SizedBox(width: 8),
                                    Text('Submit KYC Information'),
                                  ],
                                ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 40), // Bottom padding
              ],
            ),
          ),
        ),
      ),
    );
  }
}
