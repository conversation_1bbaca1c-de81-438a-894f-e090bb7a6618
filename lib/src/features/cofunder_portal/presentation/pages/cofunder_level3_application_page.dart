import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
// Assuming your theme is located here, adjust if necessary
// import 'package:three_pay_group_litigation_platform/src/core/theme/app_theme.dart';

class CofunderLevel3ApplicationPage extends StatefulWidget {
  const CofunderLevel3ApplicationPage({super.key});

  static const String routeName = '/cofunder-level3-application';

  @override
  State<CofunderLevel3ApplicationPage> createState() =>
      _CofunderLevel3ApplicationPageState();
}

class _CofunderLevel3ApplicationPageState
    extends State<CofunderLevel3ApplicationPage> {
  final _formKey = GlobalKey<ShadFormState>();
  bool _ndaAgreed = false;
  String _amlKycStatus = 'idle'; // idle, pending, successful, failed
  String? _netWorth;
  final _assetsPortfolioController = TextEditingController();
  String? _availableSurplusCash;
  String? _riskAppetite;

  // Mock file names
  String? _identityDocumentName;
  String? _proofOfResidenceName;

  // Mock bank details
  final _bankAccountNameController = TextEditingController();
  final _bankSortCodeController = TextEditingController();
  final _bankAccountNumberController = TextEditingController();

  @override
  void dispose() {
    _assetsPortfolioController.dispose();
    _bankAccountNameController.dispose();
    _bankSortCodeController.dispose();
    _bankAccountNumberController.dispose();
    super.dispose();
  }

  void _simulateAmlKyc() {
    setState(() {
      _amlKycStatus = 'pending';
    });
    Future.delayed(const Duration(seconds: 3), () {
      setState(() {
        _amlKycStatus = 'successful'; // Or 'failed' for different simulation
      });
    });
  }

  void _mockPickFile(Function(String) onFilePicked) {
    // Simulate file picking
    Future.delayed(const Duration(milliseconds: 500), () {
      setState(() {
        onFilePicked(
          'mock_document_${DateTime.now().millisecondsSinceEpoch}.pdf',
        );
      });
    });
  }

  Widget _buildNdaSection() {
    return ShadCard(
      title: const Text('Non-Disclosure Agreement (NDA)'),
      description: const Text('Please read and agree to the NDA to proceed.'),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(
                color: ShadTheme.of(context).colorScheme.border,
              ),
              borderRadius: ShadTheme.of(context).radius,
            ),
            height: 150,
            child: const SingleChildScrollView(
              child: Text(
                'This is a placeholder for the Non-Disclosure Agreement. '
                'By checking the box below, you acknowledge that you have read, understood, '
                'and agree to be bound by the terms and conditions of this NDA. '
                'This agreement is made between you ("the Recipient") and [Company Name] ("the Disclosing Party"). '
                'The purpose of this NDA is to protect confidential information that may be disclosed '
                'during your application process and potential engagement as a Co-Funder. '
                'Confidential Information includes, but is not limited to, business plans, financial data, '
                'case details, strategies, and any other proprietary information. '
                'The Recipient agrees not to disclose any Confidential Information to third parties and '
                'to use it solely for the purpose of evaluating the Co-Funder opportunity. '
                'This obligation of confidentiality shall survive the termination of any discussions or agreements. ',
                // Add more placeholder text as needed
              ),
            ),
          ),
          const SizedBox(height: 16),
          ShadCheckbox(
            label: const Text('I agree to the Non-Disclosure Agreement'),
            value: _ndaAgreed,
            onChanged: (value) {
              setState(() {
                _ndaAgreed = value ?? false;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAmlKycSection() {
    return ShadCard(
      title: const Text('AML/KYC Verification'),
      description: const Text('Identity verification is required for Level 3.'),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'As part of regulatory requirements, we need to perform an Anti-Money Laundering (AML) and Know Your Customer (KYC) check.',
          ),
          const SizedBox(height: 16),
          if (_amlKycStatus == 'idle')
            ShadButton(
              child: const Text('Start Identity Verification (Mock)'),
              onPressed: _simulateAmlKyc,
            ),
          if (_amlKycStatus == 'pending')
            const Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 16),
                Text('Verification in progress...'),
              ],
            ),
          if (_amlKycStatus == 'successful')
            const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green),
                SizedBox(width: 8),
                Text('Mock Verification Successful'),
              ],
            ),
          if (_amlKycStatus == 'failed')
            const Row(
              children: [
                Icon(Icons.error, color: Colors.red),
                SizedBox(width: 8),
                Text('Mock Verification Failed. Please try again.'),
                // Optionally add a retry button
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildFinancialInfoSection() {
    final theme = ShadTheme.of(context);
    return ShadCard(
      title: const Text('Financial Information'),
      description: const Text('Provide your financial details.'),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShadSelectFormField<String>(
            id: 'net_worth',
            label: const Text('Net Worth'),
            placeholder: const Text('Select your net worth range'),
            options: const [
              ShadOption(value: '<100k', child: Text('Under \$100,000')),
              ShadOption(
                value: '100k-500k',
                child: Text('\$100,000 - \$499,999'),
              ),
              ShadOption(
                value: '500k-1m',
                child: Text('\$500,000 - \$999,999'),
              ),
              ShadOption(
                value: '1m-5m',
                child: Text('\$1,000,000 - \$4,999,999'),
              ),
              ShadOption(value: '5m+', child: Text('\$5,000,000+')),
            ],
            selectedOptionBuilder: (context, value) => Text(value),
            onChanged: (value) {
              setState(() {
                _netWorth = value;
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select your net worth.';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          ShadInputFormField(
            id: 'assets_portfolio_summary',
            label: const Text('Assets Portfolio Summary'),
            controller: _assetsPortfolioController,
            placeholder: const Text('E.g., Stocks, Real Estate, Bonds'),
            maxLines: 3,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please provide a summary of your assets portfolio.';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          ShadSelectFormField<String>(
            id: 'available_surplus_cash',
            label: const Text('Available Surplus Cash for Investment'),
            placeholder: const Text('Select cash range'),
            options: const [
              ShadOption(value: '<10k', child: Text('Under \$10,000')),
              ShadOption(value: '10k-50k', child: Text('\$10,000 - \$49,999')),
              ShadOption(value: '50k-100k', child: Text('\$50,000 - \$99,999')),
              ShadOption(
                value: '100k-250k',
                child: Text('\$100,000 - \$249,999'),
              ),
              ShadOption(value: '250k+', child: Text('\$250,000+')),
            ],
            selectedOptionBuilder: (context, value) => Text(value),
            onChanged: (value) {
              setState(() {
                _availableSurplusCash = value;
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select your available surplus cash.';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          ShadSelectFormField<String>(
            id: 'risk_appetite',
            label: const Text('Risk Appetite'),
            options: const [
              ShadOption(value: 'low', child: Text('Low')),
              ShadOption(value: 'medium', child: Text('Medium')),
              ShadOption(value: 'high', child: Text('High')),
            ],
            selectedOptionBuilder:
                (context, value) =>
                    Text(value[0].toUpperCase() + value.substring(1)),
            onChanged: (value) {
              setState(() {
                _riskAppetite = value;
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select your risk appetite.';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentUploadSection() {
    return ShadCard(
      title: const Text('Document Uploads'),
      description: const Text('Upload necessary identification documents.'),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShadButton.outline(
            onPressed:
                () => _mockPickFile(
                  (fileName) => _identityDocumentName = fileName,
                ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_identityDocumentName == null) ...[
                  const Icon(Icons.upload_file, size: 16),
                  const SizedBox(width: 8),
                ],
                Text(
                  _identityDocumentName ??
                      'Upload Identity Document (Passport/License)',
                ),
              ],
            ),
          ),
          if (_identityDocumentName != null)
            Padding(
              padding: const EdgeInsets.only(top: 4.0, left: 4.0),
              child: Text(
                'File: $_identityDocumentName',
                style: ShadTheme.of(context).textTheme.muted,
              ),
            ),
          const SizedBox(height: 16),
          ShadButton.outline(
            onPressed:
                () => _mockPickFile(
                  (fileName) => _proofOfResidenceName = fileName,
                ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_proofOfResidenceName == null) ...[
                  const Icon(Icons.upload_file, size: 16),
                  const SizedBox(width: 8),
                ],
                Text(_proofOfResidenceName ?? 'Upload Proof of Residence'),
              ],
            ),
          ),
          if (_proofOfResidenceName != null)
            Padding(
              padding: const EdgeInsets.only(top: 4.0, left: 4.0),
              child: Text(
                'File: $_proofOfResidenceName',
                style: ShadTheme.of(context).textTheme.muted,
              ),
            ),
          const SizedBox(height: 8),
          Text(
            'For UI demonstration purposes, clicking these buttons simulates a file selection and displays a mock file name. No actual file upload occurs.',
            style: ShadTheme.of(context).textTheme.muted.copyWith(fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildBankDetailsSection() {
    return ShadCard(
      title: const Text('Bank Account Details'),
      description: const Text('This information will be handled securely.'),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShadInputFormField(
            id: 'bank_account_name',
            label: const Text('Account Holder Name'),
            controller: _bankAccountNameController,
            placeholder: const Text('John Doe'),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter the account holder name.';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          ShadInputFormField(
            id: 'bank_sort_code',
            label: const Text('Sort Code / ABA Routing Number'),
            controller: _bankSortCodeController,
            placeholder: const Text('XX-XX-XX or XXXXXXXXX'),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter the sort code or ABA routing number.';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          ShadInputFormField(
            id: 'bank_account_number',
            label: const Text('Account Number / IBAN'),
            controller: _bankAccountNumberController,
            placeholder: const Text('XXXXXXXXXX'),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter the account number or IBAN.';
              }
              return null;
            },
          ),
          const SizedBox(height: 8),
          Text(
            'Sensitive data like bank details would be encrypted and stored securely in a real application.',
            style: ShadTheme.of(context).textTheme.muted.copyWith(fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ShadButton.outline(
            child: const Text('Back'),
            onPressed: () {
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }
            },
          ),
          const SizedBox(width: 16),
          ShadButton.outline(
            child: const Text('Save Draft (Mock)'),
            onPressed: () {
              // Mock save draft
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Draft saved (mock).')),
              );
            },
          ),
          const SizedBox(width: 16),
          ShadButton(
            child: const Text('Submit Application for Level 3'),
            onPressed: () {
              if (_formKey.currentState!.validate()) {
                if (!_ndaAgreed) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please agree to the NDA to submit.'),
                    ),
                  );
                  return;
                }
                if (_amlKycStatus != 'successful') {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please complete AML/KYC verification.'),
                    ),
                  );
                  return;
                }
                if (_identityDocumentName == null ||
                    _proofOfResidenceName == null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please upload required documents.'),
                    ),
                  );
                  return;
                }
                // Mock submission
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Application submitted for Level 3 (mock).'),
                  ),
                );
                // Potentially navigate away or show success message
                // Navigator.pop(context); // Example
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please correct the errors in the form.'),
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Co-Funder Level 3 Application'),
        centerTitle: true,
        // backgroundColor: theme.colorScheme.primary, // Example theming
        // foregroundColor: theme.colorScheme.onPrimary,
      ),
      body: ShadForm(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Optional: Add an intro text or progress indicator for multi-step
              // Text("Step 1 of X: NDA Agreement", style: theme.textTheme.h4),
              // const SizedBox(height: 20),
              _buildNdaSection(),
              const SizedBox(height: 24),

              _buildAmlKycSection(),
              const SizedBox(height: 24),

              _buildFinancialInfoSection(),
              const SizedBox(height: 24),

              _buildDocumentUploadSection(),
              const SizedBox(height: 24),

              _buildBankDetailsSection(),
              const SizedBox(height: 24),

              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }
}
