import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/content_item_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_podcast_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/widgets/enhanced_podcast_player_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/utils/responsive_layout.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

class PodcastDetailPage extends StatefulWidget {
  final ContentItemModel contentItem;
  final CoFunderPodcastModel? podcastData;

  const PodcastDetailPage({
    super.key,
    required this.contentItem,
    this.podcastData,
  });

  static const String routeName = '/podcast-detail';

  @override
  State<PodcastDetailPage> createState() => _PodcastDetailPageState();
}

class _PodcastDetailPageState extends State<PodcastDetailPage> {
  final PocketBaseService _pbService = PocketBaseService();
  CoFunderPodcastModel? _podcastData;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _podcastData = widget.podcastData;
    if (_podcastData == null) {
      _fetchPodcastData();
    }
  }

  Future<void> _fetchPodcastData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await _pbService.client
          .collection('cofunder_podcasts')
          .getFirstListItem('content_item_slug = "${widget.contentItem.slug}"');

      setState(() {
        _podcastData = CoFunderPodcastModel.fromRecord(result);
        _isLoading = false;
      });
    } catch (e) {
      LoggerService.error('Error fetching podcast data', e);
      setState(() {
        _error = 'Failed to load podcast details';
        _isLoading = false;
      });
    }
  }

  Future<void> _incrementPlayCount() async {
    if (_podcastData == null) return;

    try {
      final currentPlayCount = _podcastData!.playCount ?? 0;
      await _pbService.client
          .collection('cofunder_podcasts')
          .update(_podcastData!.id, body: {'play_count': currentPlayCount + 1});

      setState(() {
        _podcastData = _podcastData!.copyWith(playCount: currentPlayCount + 1);
      });
    } catch (e) {
      LoggerService.error('Error incrementing play count', e);
    }
  }

  String? get _audioUrl {
    if (_podcastData?.audioFile == null || _podcastData!.audioFile!.isEmpty) {
      return null;
    }

    try {
      return Uri.parse(
        '${_pbService.client.baseURL}/api/files/${_podcastData!.collectionId}/${_podcastData!.id}/${_podcastData!.audioFile}',
      ).toString();
    } catch (e) {
      LoggerService.error('Error constructing audio URL', e);
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isDesktop = CoFunderResponsiveLayout.isDesktop(context);
    final isTablet = CoFunderResponsiveLayout.isTablet(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.contentItem.title,
          style: theme.textTheme.h4.copyWith(
            color: theme.colorScheme.primaryForeground,
          ),
        ),
        backgroundColor: theme.colorScheme.primary,
        iconTheme: IconThemeData(color: theme.colorScheme.primaryForeground),
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _error != null
              ? _buildErrorState(theme)
              : _buildContent(context, theme, isDesktop, isTablet),
    );
  }

  Widget _buildErrorState(ShadThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            LucideIcons.circleAlert,
            size: 64,
            color: theme.colorScheme.destructive,
          ),
          const SizedBox(height: 16),
          Text(
            _error!,
            style: theme.textTheme.h4.copyWith(
              color: theme.colorScheme.destructive,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ShadButton.outline(
            onPressed: _fetchPodcastData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    ShadThemeData theme,
    bool isDesktop,
    bool isTablet,
  ) {
    final dateFormat = DateFormat.yMMMd();
    final imageUrl = widget.contentItem.getImageUrl(_pbService.client);

    if (isDesktop || isTablet) {
      return _buildDesktopLayout(context, theme, dateFormat, imageUrl);
    } else {
      return _buildMobileLayout(context, theme, dateFormat, imageUrl);
    }
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    ShadThemeData theme,
    DateFormat dateFormat,
    String? imageUrl,
  ) {
    return SingleChildScrollView(
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth:
                CoFunderResponsiveLayout.isLargeDesktop(context) ? 1200 : 900,
          ),
          padding: CoFunderResponsiveLayout.getResponsivePadding(context),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left column - Image and player
              Expanded(
                flex: 2,
                child: Column(
                  children: [
                    _buildHeroImage(theme, imageUrl, height: 300),
                    const SizedBox(height: 24),
                    if (_audioUrl != null && _podcastData != null)
                      EnhancedPodcastPlayerWidget(
                        podcast: _podcastData!,
                        audioUrl: _audioUrl!,
                        onPlayCountIncrement: _incrementPlayCount,
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 32),
              // Right column - Content
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(theme, dateFormat),
                    const SizedBox(height: 24),
                    _buildDescription(theme),
                    if (_podcastData?.showNotes != null) ...[
                      const SizedBox(height: 32),
                      _buildShowNotes(theme),
                    ],
                    if (_podcastData?.transcript != null) ...[
                      const SizedBox(height: 32),
                      _buildTranscript(theme),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileLayout(
    BuildContext context,
    ShadThemeData theme,
    DateFormat dateFormat,
    String? imageUrl,
  ) {
    return SingleChildScrollView(
      padding: CoFunderResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeroImage(theme, imageUrl, height: 200),
          const SizedBox(height: 16),
          _buildHeader(theme, dateFormat),
          const SizedBox(height: 16),
          if (_audioUrl != null && _podcastData != null)
            EnhancedPodcastPlayerWidget(
              podcast: _podcastData!,
              audioUrl: _audioUrl!,
              onPlayCountIncrement: _incrementPlayCount,
            ),
          const SizedBox(height: 24),
          _buildDescription(theme),
          if (_podcastData?.showNotes != null) ...[
            const SizedBox(height: 24),
            _buildShowNotes(theme),
          ],
          if (_podcastData?.transcript != null) ...[
            const SizedBox(height: 24),
            _buildTranscript(theme),
          ],
        ],
      ),
    );
  }

  Widget _buildHeroImage(
    ShadThemeData theme,
    String? imageUrl, {
    required double height,
  }) {
    return Container(
      width: double.infinity,
      height: height,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        borderRadius: BorderRadius.circular(12),
      ),
      child:
          imageUrl != null
              ? ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.network(
                  imageUrl,
                  width: double.infinity,
                  height: height,
                  fit: BoxFit.cover,
                  errorBuilder:
                      (context, error, stackTrace) =>
                          _buildDefaultImage(theme, height),
                ),
              )
              : _buildDefaultImage(theme, height),
    );
  }

  Widget _buildDefaultImage(ShadThemeData theme, double height) {
    return Container(
      width: double.infinity,
      height: height,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        LucideIcons.headphones,
        size: height * 0.3,
        color: theme.colorScheme.mutedForeground,
      ),
    );
  }

  Widget _buildHeader(ShadThemeData theme, DateFormat dateFormat) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Podcast badge
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.purple.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                LucideIcons.headphones,
                size: 16,
                color: Colors.purple,
              ),
              const SizedBox(width: 6),
              Text(
                'Podcast',
                style: theme.textTheme.small.copyWith(
                  color: Colors.purple,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        // Title
        Text(
          _podcastData?.getEpisodeTitle(widget.contentItem.title) ??
              widget.contentItem.title,
          style: theme.textTheme.h1.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        // Metadata
        _buildMetadata(theme, dateFormat),
      ],
    );
  }

  Widget _buildMetadata(ShadThemeData theme, DateFormat dateFormat) {
    final publishedDate =
        widget.contentItem.publishedAt != null
            ? dateFormat.format(widget.contentItem.publishedAt!)
            : 'No date';

    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: [
        if (_podcastData?.durationSeconds != null)
          _buildMetadataItem(
            theme,
            LucideIcons.clock,
            _podcastData!.formattedDuration,
          ),
        _buildMetadataItem(theme, LucideIcons.calendar, publishedDate),
        if (_podcastData?.playCount != null)
          _buildMetadataItem(
            theme,
            LucideIcons.play,
            '${_podcastData!.playCount} plays',
          ),
        if (_podcastData?.hasGuestSpeakers == true)
          _buildMetadataItem(
            theme,
            LucideIcons.users,
            _podcastData!.guestSpeakersText,
          ),
      ],
    );
  }

  Widget _buildMetadataItem(ShadThemeData theme, IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.mutedForeground),
        const SizedBox(width: 6),
        Text(
          text,
          style: theme.textTheme.small.copyWith(
            color: theme.colorScheme.mutedForeground,
          ),
        ),
      ],
    );
  }

  Widget _buildDescription(ShadThemeData theme) {
    if (widget.contentItem.summary == null ||
        widget.contentItem.summary!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description',
          style: theme.textTheme.h3.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        Text(
          widget.contentItem.summary!,
          style: theme.textTheme.p.copyWith(height: 1.6),
        ),
      ],
    );
  }

  Widget _buildShowNotes(ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Show Notes',
          style: theme.textTheme.h3.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        Html(
          data: _podcastData!.showNotes!,
          style: {
            "body": Style(
              fontSize: FontSize(16),
              color: theme.colorScheme.foreground,
              lineHeight: LineHeight(1.6),
            ),
            "p": Style(
              fontSize: FontSize(16),
              color: theme.colorScheme.foreground,
              lineHeight: LineHeight(1.6),
            ),
            "h1": Style(
              fontSize: FontSize(24),
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.foreground,
            ),
            "h2": Style(
              fontSize: FontSize(20),
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.foreground,
            ),
            "h3": Style(
              fontSize: FontSize(18),
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.foreground,
            ),
          },
        ),
      ],
    );
  }

  Widget _buildTranscript(ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Transcript',
          style: theme.textTheme.h3.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.muted.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: theme.colorScheme.border),
          ),
          child: Html(
            data: _podcastData!.transcript!,
            style: {
              "body": Style(
                fontSize: FontSize(14),
                color: theme.colorScheme.foreground,
                lineHeight: LineHeight(1.5),
              ),
              "p": Style(
                fontSize: FontSize(14),
                color: theme.colorScheme.foreground,
                lineHeight: LineHeight(1.5),
              ),
            },
          ),
        ),
      ],
    );
  }
}
