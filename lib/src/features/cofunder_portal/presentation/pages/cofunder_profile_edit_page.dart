import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/form_submission_error_alert_widget.dart';
import '../providers/cofunder_profile_provider.dart';
import '../widgets/cofunder_profile_edit_form.dart';

/// Page for editing co-funder profile information
class CoFunderProfileEditPage extends ConsumerStatefulWidget {
  static const String routeName = '/cofunder/profile/edit';

  const CoFunderProfileEditPage({super.key});

  @override
  ConsumerState<CoFunderProfileEditPage> createState() =>
      _CoFunderProfileEditPageState();
}

class _CoFunderProfileEditPageState
    extends ConsumerState<CoFunderProfileEditPage> {
  @override
  void initState() {
    super.initState();
    // Load profile data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(coFunderProfileProvider.notifier).loadProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(coFunderProfileProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Profile', style: theme.textTheme.h4),
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Error Alert
            if (profileState.error != null)
              FormSubmissionErrorAlertWidget(
                title: 'Error',
                description: profileState.error!,
              ),

            const SizedBox(height: 16),

            // Loading State
            if (profileState.isLoading)
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Loading profile...'),
                  ],
                ),
              )
            else if (profileState.profile == null)
              ShadCard(
                title: Text('Profile Not Found', style: theme.textTheme.h4),
                child: const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(LucideIcons.info, size: 48),
                      SizedBox(height: 16),
                      Text(
                        'Unable to load profile information. Please try again.',
                      ),
                    ],
                  ),
                ),
              )
            else
              // Edit Form
              CoFunderProfileEditForm(
                onSaved: _handleSaved,
                onCancelled: _handleCancelled,
              ),
          ],
        ),
      ),
    );
  }

  void _handleSaved() {
    // Clear any messages
    ref.read(coFunderProfileProvider.notifier).clearMessages();

    // Navigate back
    Navigator.of(context).pop();
  }

  void _handleCancelled() {
    // Clear any messages
    ref.read(coFunderProfileProvider.notifier).clearMessages();

    // Navigate back
    Navigator.of(context).pop();
  }
}

/// Responsive edit page for larger screens
class ResponsiveCoFunderProfileEditPage extends ConsumerStatefulWidget {
  static const String routeName = '/cofunder/profile/edit-responsive';

  const ResponsiveCoFunderProfileEditPage({super.key});

  @override
  ConsumerState<ResponsiveCoFunderProfileEditPage> createState() =>
      _ResponsiveCoFunderProfileEditPageState();
}

class _ResponsiveCoFunderProfileEditPageState
    extends ConsumerState<ResponsiveCoFunderProfileEditPage> {
  @override
  void initState() {
    super.initState();
    // Load profile data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(coFunderProfileProvider.notifier).loadProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(coFunderProfileProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Profile', style: theme.textTheme.h4),
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
      ),
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 800),
          padding: const EdgeInsets.all(24.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Error Alert
                if (profileState.error != null)
                  FormSubmissionErrorAlertWidget(
                    title: 'Error',
                    description: profileState.error!,
                  ),

                const SizedBox(height: 16),

                // Loading State
                if (profileState.isLoading)
                  const Center(
                    child: Column(
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading profile...'),
                      ],
                    ),
                  )
                else if (profileState.profile == null)
                  ShadCard(
                    title: Text('Profile Not Found', style: theme.textTheme.h4),
                    child: const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Icon(LucideIcons.info, size: 48),
                          SizedBox(height: 16),
                          Text(
                            'Unable to load profile information. Please try again.',
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  // Edit Form
                  CoFunderProfileEditForm(
                    onSaved: _handleSaved,
                    onCancelled: _handleCancelled,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleSaved() {
    // Clear any messages
    ref.read(coFunderProfileProvider.notifier).clearMessages();

    // Navigate back
    Navigator.of(context).pop();
  }

  void _handleCancelled() {
    // Clear any messages
    ref.read(coFunderProfileProvider.notifier).clearMessages();

    // Navigate back
    Navigator.of(context).pop();
  }
}
