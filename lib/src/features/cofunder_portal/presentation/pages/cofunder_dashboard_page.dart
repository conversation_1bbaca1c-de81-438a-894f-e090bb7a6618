import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_profile_level2_form_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/educational_content_list_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_profile_level3_form_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/knowledge_test_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/my_investments_page.dart';
import '../../utils/responsive_layout.dart';
import '../../utils/animations.dart';
import '../widgets/error_widgets.dart';
import '../widgets/loading_widgets.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_subscription_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_investment_form_page.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/educational_content_detail_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/content_item_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_profile_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/presentation/pages/notifications_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/sign_nda_page.dart';
import 'package:three_pay_group_litigation_platform/src/core/providers/notification_counter_provider.dart';

// Data Structures
class CoFunderInfo {
  final String name;
  final int level;
  final bool isSubscribedToNewsletter;
  final bool level2ApplicationSubmitted;
  final bool level2Approved;
  final bool level3ApplicationSubmitted;
  final bool level3Approved;
  final bool level4Subscribed; // New field
  final bool hasPassedKnowledgeTest; // Added field
  final bool hasSignedNDA; // Added field for NDA signing status

  CoFunderInfo({
    required this.name,
    required this.level,
    this.isSubscribedToNewsletter = false,
    this.level2ApplicationSubmitted = false,
    this.level2Approved = false,
    this.level3ApplicationSubmitted = false,
    this.level3Approved = false,
    this.level4Subscribed = false, // New field
    this.hasPassedKnowledgeTest = false, // Added field
    this.hasSignedNDA = false, // Added field for NDA signing status
  });

  factory CoFunderInfo.fromUserAndProfileRecord(
    RecordModel userRecord,
    RecordModel? profileRecord,
    bool hasPassedKnowledgeTest,
  ) {
    int userLevel = 0;
    final levelString =
        userRecord.data['level']
            as String?; // Assuming level is on userRecord for now
    if (levelString != null) {
      userLevel = int.tryParse(levelString) ?? 0;
    }

    return CoFunderInfo(
      name:
          userRecord.data['name'] ??
          userRecord.data['first_name'] ??
          'Co-Funder',
      level:
          profileRecord?.data['current_level'] ??
          userLevel, // Prefer profile level if available
      isSubscribedToNewsletter:
          (profileRecord?.data['notification_preferences']
              as Map<String, dynamic>?)?['news_letter'] ??
          false,
      level2ApplicationSubmitted:
          profileRecord?.data['level_2_application_submitted'] ?? false,
      level2Approved: profileRecord?.data['level_2_approved'] ?? false,
      level3ApplicationSubmitted:
          profileRecord?.data['level_3_application_submitted'] ?? false,
      level3Approved: profileRecord?.data['level_3_approved'] ?? false,
      level4Subscribed:
          profileRecord?.data['level_4_subscribed'] ?? false, // New field
      hasPassedKnowledgeTest: hasPassedKnowledgeTest, // Added field
      hasSignedNDA:
          profileRecord?.data['nda_signed_at'] != null &&
          profileRecord?.data['nda_signed_at'] !=
              "", // Check if NDA has been signed and not empty
    );
  }
}

class CoFunderDashboardPage extends ConsumerStatefulWidget {
  const CoFunderDashboardPage({super.key});

  static const String routeName = '/cofunder-dashboard';

  @override
  ConsumerState<CoFunderDashboardPage> createState() =>
      _CoFunderDashboardPageState();
}

class _CoFunderDashboardPageState extends ConsumerState<CoFunderDashboardPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final PocketBaseService _pbService = PocketBaseService();
  CoFunderInfo? _coFunderInfo;
  List<ContentItemModel> _educationalContent = [];
  bool _isLoadingCoFunder = true;
  bool _isLoadingEducationalContent = true;
  int _readModulesCount = 0;
  bool _isLoadingProfileDetails = true;
  String? _errorCoFunder;
  String? _errorEducationalContent;
  bool _isUpdatingNewsletter = false;

  static const int _requiredModulesForLevel2 = 14;
  static const int _requiredModulesForLevel3 = 21;

  // Define responsive breakpoints (using CoFunderResponsiveLayout)
  static const double _tabletBreakpoint = 900.0;
  static const double _desktopBreakpoint = 1200.0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 700),
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    );
    _fetchDashboardData();
  }

  Future<void> _fetchDashboardData() async {
    await _fetchCoFunderProfileAndReadCount();
    _fetchEducationalContent();
  }

  Future<void> _fetchCoFunderProfileAndReadCount() async {
    setState(() {
      _isLoadingCoFunder = true;
      _isLoadingProfileDetails = true;
      _errorCoFunder = null;
    });
    try {
      if (!_pbService.client.authStore.isValid ||
          _pbService.client.authStore.record == null) {
        throw Exception('User not authenticated');
      }
      final userId = _pbService.client.authStore.record!.id;

      final userRecord = await _pbService.client
          .collection('users')
          .getOne(userId);
      RecordModel? profileRecord;
      int readCount = 0;
      bool hasPassedKnowledgeTest = false;

      try {
        profileRecord = await _pbService.client
            .collection('co_funder_profiles')
            .getFirstListItem(
              'user_id="$userId"',
              expand: 'read_educational_content',
            );

        final List<dynamic> readArticlesData =
            profileRecord.get<List<dynamic>>(
              'expand.read_educational_content',
            ) ??
            [];
        readCount = readArticlesData.length;

        // Fetch knowledge test status
        try {
          await _pbService.client
              .collection('knowledge_test_attempts')
              .getFirstListItem(
                'co_funder_profile_id="${profileRecord.id}" && passed=true',
              );
          hasPassedKnowledgeTest = true;
        } on ClientException catch (e) {
          if (e.statusCode == 404) {
            // No passed test attempt found, which is fine.
            hasPassedKnowledgeTest = false;
          } else {
            // Log other client exceptions for knowledge test attempts
            LoggerService.error(
              "PocketBase Error fetching knowledge test attempts: ${e.statusCode} ${e.response}",
            );
          }
        }
      } catch (e) {
        LoggerService.error(
          "No co-funder profile found or error fetching profile details",
          e,
        );
        // Proceed without profile specific data, or handle as an error state for profile
      }

      // Pass userRecord, profileRecord, and hasPassedKnowledgeTest
      final coFunderInfo = CoFunderInfo.fromUserAndProfileRecord(
        userRecord,
        profileRecord,
        hasPassedKnowledgeTest,
      );

      if (mounted) {
        setState(() {
          _coFunderInfo = coFunderInfo;
          _readModulesCount = readCount;
          _isLoadingCoFunder = false;
          _isLoadingProfileDetails = false;
        });
      }
      _animationController.forward();
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorCoFunder = "Failed to load co-funder data: ${e.toString()}";
          _isLoadingCoFunder = false;
          _isLoadingProfileDetails = false;
        });
      }
    }
  }

  Future<void> _fetchEducationalContent() async {
    setState(() => _isLoadingEducationalContent = true);
    try {
      final result = await _pbService.client
          .collection('content_items')
          .getList(
            page: 1,
            perPage: 5,
            filter:
                'status = "published" && (type = "educational_module" || type = "article" || type = "video")',
            sort: '-published_at',
          );
      final items =
          result.items
              .map((record) => ContentItemModel.fromRecord(record))
              .toList();
      setState(() {
        _educationalContent = items;
        _isLoadingEducationalContent = false;
      });
    } catch (e) {
      setState(() {
        _errorEducationalContent =
            "Failed to load educational content: ${e.toString()}";
        _isLoadingEducationalContent = false;
      });
    }
  }

  Future<void> _subscribeToNewsletter() async {
    if (_coFunderInfo == null) return;

    setState(() {
      _isUpdatingNewsletter = true;
    });

    try {
      // Get current co-funder profile
      final userId = _pbService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final profileRecord = await _pbService.client
          .collection('co_funder_profiles')
          .getFirstListItem('user_id="$userId"');

      // Get current notification preferences or create new ones
      final currentPreferences =
          profileRecord.data['notification_preferences']
              as Map<String, dynamic>? ??
          {};

      // Update with newsletter subscription
      final updatedPreferences = Map<String, dynamic>.from(currentPreferences);
      updatedPreferences['news_letter'] = true;

      // Update the profile
      await _pbService.client
          .collection('co_funder_profiles')
          .update(
            profileRecord.id,
            body: {'notification_preferences': updatedPreferences},
          );

      // Refresh the dashboard data to reflect the change
      await _fetchCoFunderProfileAndReadCount();

      if (mounted) {
        ShadToaster.of(context).show(
          const ShadToast(
            title: Text('Newsletter Subscription'),
            description: Text('Successfully subscribed to newsletter!'),
          ),
        );
      }

      LoggerService.info('Newsletter subscription updated for user: $userId');
    } catch (e) {
      LoggerService.error('Error updating newsletter subscription', e);

      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast(
            title: const Text('Error'),
            description: Text(
              'Failed to subscribe to newsletter: ${e.toString()}',
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdatingNewsletter = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final textTheme = theme.textTheme;

    return Scaffold(
      appBar: AppBar(
        title: Text('Co-Funder Dashboard', style: textTheme.h4),
        centerTitle: true,
        elevation: 0,
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
        actions: [
          Consumer(
            builder: (context, ref, child) {
              final unreadCount = ref.watch(currentUnreadCountProvider);

              return Stack(
                alignment: Alignment.center,
                children: [
                  ShadButton.ghost(
                    child: Icon(
                      LucideIcons.bell,
                      size: 24,
                      color: theme.colorScheme.foreground,
                    ),
                    onPressed: () {
                      Navigator.of(
                        context,
                      ).pushNamed(NotificationsPage.routeName);
                    },
                  ),
                  if (unreadCount > 0)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.destructive,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          unreadCount.toString(),
                          style: theme.textTheme.small.copyWith(
                            color: theme.colorScheme.destructiveForeground,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
          ShadButton.ghost(
            child: Icon(
              LucideIcons.user,
              size: 24,
              color: theme.colorScheme.foreground,
            ),
            onPressed: () {
              Navigator.of(context).pushNamed(CoFunderProfilePage.routeName);
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      body:
          _isLoadingCoFunder
              ? const Center(child: CircularProgressIndicator.adaptive())
              : _errorCoFunder != null
              ? Center(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(_errorCoFunder!),
                ),
              )
              : _coFunderInfo == null
              ? const Center(child: Text('Could not load user information.'))
              : FadeTransition(
                opacity: _fadeAnimation,
                // Wrap the scrollable content with RefreshIndicator
                child: RefreshIndicator.adaptive(
                  // Use .adaptive for platform-specific look
                  onRefresh:
                      _fetchDashboardData, // Call the data fetching method on refresh
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final isDesktop = CoFunderResponsiveLayout.isDesktop(
                        context,
                      );
                      final isTablet = CoFunderResponsiveLayout.isTablet(
                        context,
                      );

                      // Responsive padding
                      final horizontalPadding =
                          CoFunderResponsiveLayout.getHorizontalPadding(
                            context,
                          );
                      final verticalPadding =
                          CoFunderResponsiveLayout.getVerticalPadding(context);
                      final sectionSpacing =
                          CoFunderResponsiveLayout.getSectionSpacing(context);

                      return SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: EdgeInsets.symmetric(
                          horizontal: horizontalPadding,
                          vertical: verticalPadding,
                        ),
                        child: _buildResponsiveContent(
                          context,
                          theme,
                          textTheme,
                          isDesktop,
                          isTablet,
                          sectionSpacing,
                        ),
                      );
                    },
                  ),
                ),
              ),
    );
  }

  Widget _buildResponsiveContent(
    BuildContext context,
    ShadThemeData theme,
    ShadTextTheme textTheme,
    bool isDesktop,
    bool isTablet,
    double sectionSpacing,
  ) {
    if (isDesktop) {
      // Desktop layout: Two-column layout with sidebar
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main content column
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildWelcomeSection(context, textTheme, _coFunderInfo!),
                SizedBox(height: sectionSpacing),
                if (!_coFunderInfo!.hasSignedNDA &&
                    _coFunderInfo!.level4Subscribed) ...[
                  _buildNDASection(context, theme, _coFunderInfo!),
                  SizedBox(height: sectionSpacing),
                ],
                _buildEducationalContentSection(context, theme, textTheme),
                SizedBox(height: sectionSpacing),
                _buildInvestmentOpportunitiesSection(
                  context,
                  theme,
                  _coFunderInfo!,
                ),
                SizedBox(height: sectionSpacing),
                _buildMyPortfolioSection(context, theme, _coFunderInfo!),
              ],
            ),
          ),
          const SizedBox(width: 32),
          // Sidebar column
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProgressToNextLevelSection(
                  context,
                  theme,
                  _coFunderInfo!,
                ),
                if (_coFunderInfo?.hasPassedKnowledgeTest == false) ...[
                  SizedBox(height: sectionSpacing),
                  _buildKnowledgeTestSection(context, theme, _coFunderInfo!),
                ],
                SizedBox(height: sectionSpacing),
                _buildNewsletterSection(context, theme, _coFunderInfo!),
              ],
            ),
          ),
        ],
      );
    } else if (isTablet) {
      // Tablet layout: Mixed layout with some sections side-by-side
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(context, textTheme, _coFunderInfo!),
          SizedBox(height: sectionSpacing),
          if (!_coFunderInfo!.hasSignedNDA &&
              _coFunderInfo!.level4Subscribed) ...[
            _buildNDASection(context, theme, _coFunderInfo!),
            SizedBox(height: sectionSpacing),
          ],
          _buildEducationalContentSection(context, theme, textTheme),
          SizedBox(height: sectionSpacing),
          // Two-column layout for progress and knowledge test on tablet
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildProgressToNextLevelSection(
                  context,
                  theme,
                  _coFunderInfo!,
                ),
              ),
              if (_coFunderInfo?.hasPassedKnowledgeTest == false) ...[
                const SizedBox(width: 24),
                Expanded(
                  child: _buildKnowledgeTestSection(
                    context,
                    theme,
                    _coFunderInfo!,
                  ),
                ),
              ],
            ],
          ),
          SizedBox(height: sectionSpacing),
          _buildInvestmentOpportunitiesSection(context, theme, _coFunderInfo!),
          SizedBox(height: sectionSpacing),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildMyPortfolioSection(context, theme, _coFunderInfo!),
              ),
              const SizedBox(width: 24),
              Expanded(
                child: _buildNewsletterSection(context, theme, _coFunderInfo!),
              ),
            ],
          ),
          const SizedBox(height: 40),
        ],
      );
    } else {
      // Mobile layout: Single column
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(context, textTheme, _coFunderInfo!),
          SizedBox(height: sectionSpacing),
          if (!_coFunderInfo!.hasSignedNDA &&
              _coFunderInfo!.level4Subscribed) ...[
            _buildNDASection(context, theme, _coFunderInfo!),
            SizedBox(height: sectionSpacing),
          ],
          _buildEducationalContentSection(context, theme, textTheme),
          SizedBox(height: sectionSpacing),
          _buildProgressToNextLevelSection(context, theme, _coFunderInfo!),
          SizedBox(height: sectionSpacing),
          if (_coFunderInfo?.hasPassedKnowledgeTest == false) ...[
            _buildKnowledgeTestSection(context, theme, _coFunderInfo!),
            SizedBox(height: sectionSpacing),
          ],
          _buildInvestmentOpportunitiesSection(context, theme, _coFunderInfo!),
          SizedBox(height: sectionSpacing),
          _buildMyPortfolioSection(context, theme, _coFunderInfo!),
          SizedBox(height: sectionSpacing),
          _buildNewsletterSection(context, theme, _coFunderInfo!),
          const SizedBox(height: 40),
        ],
      );
    }
  }

  Widget _buildWelcomeSection(
    BuildContext context,
    ShadTextTheme textTheme,
    CoFunderInfo coFunder,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Welcome, ${coFunder.name}!',
          style: textTheme.h2.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 4),
        Text(
          'You are currently Level ${coFunder.level}',
          style: textTheme.p.copyWith(
            color: ShadTheme.of(context).colorScheme.mutedForeground,
          ),
        ),
      ],
    );
  }

  Widget _buildEducationalContentSection(
    BuildContext context,
    ShadThemeData theme,
    ShadTextTheme textTheme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Learn & Grow', style: textTheme.h3),
            ShadButton.link(
              child: Text(
                'View All Content',
                style: textTheme.small.copyWith(
                  color: theme.colorScheme.primary,
                ),
              ),
              onPressed: () {
                Navigator.pushNamed(
                  context,
                  EducationalContentListPage.routeName,
                );
              },
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Add login prompt card for unauthenticated desktop users
        LayoutBuilder(
          builder: (context, constraints) {
            final isDesktop = CoFunderResponsiveLayout.isDesktop(context);
            final isAuthenticated =
                _pbService.client.authStore.isValid &&
                _pbService.currentUser != null;

            // Show login prompt card only on desktop for unauthenticated users
            if (isDesktop &&
                !isAuthenticated &&
                _hasEducationalContentForLogin()) {
              return Column(
                children: [
                  _buildLoginPromptCard(context, theme, textTheme),
                  const SizedBox(height: 24),
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),
        _isLoadingEducationalContent
            ? const SizedBox(
              height: 240,
              child: Center(child: CircularProgressIndicator.adaptive()),
            )
            : _errorEducationalContent != null
            ? SizedBox(
              height: 240,
              child: Center(child: Text(_errorEducationalContent!)),
            )
            : _educationalContent.isEmpty
            ? SizedBox(
              height: 240,
              child: Center(
                child: Text(
                  'No educational content available right now.',
                  style: textTheme.p,
                ),
              ),
            )
            : SizedBox(
              height: 260,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                itemCount: _educationalContent.length,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                separatorBuilder: (context, index) => const SizedBox(width: 16),
                itemBuilder: (context, index) {
                  final item = _educationalContent[index];
                  return _buildEducationalContentCard(context, theme, item);
                },
              ),
            ),
      ],
    );
  }

  // Helper method to check if there's educational content suitable for login prompt
  bool _hasEducationalContentForLogin() {
    return _educationalContent.any(
      (item) => item.type == 'blog' || item.type == 'podcast',
    );
  }

  // Build login prompt card for unauthenticated desktop users
  Widget _buildLoginPromptCard(
    BuildContext context,
    ShadThemeData theme,
    ShadTextTheme textTheme,
  ) {
    return ShadCard(
      radius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary.withValues(alpha: 0.05),
              theme.colorScheme.secondary.withValues(alpha: 0.05),
            ],
          ),
          border: Border.all(
            color: theme.colorScheme.primary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Icon section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                LucideIcons.lock,
                size: 32,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(width: 20),
            // Content section
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Unlock Educational Content',
                    style: textTheme.h4.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.foreground,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Sign in to access our comprehensive library of blogs, podcasts, and educational modules on litigation funding.',
                    style: textTheme.p.copyWith(
                      color: theme.colorScheme.mutedForeground,
                      height: 1.5,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Icon(
                        LucideIcons.bookOpen,
                        size: 16,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Expert insights',
                        style: textTheme.small.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Icon(
                        LucideIcons.headphones,
                        size: 16,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Podcasts',
                        style: textTheme.small.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Icon(
                        LucideIcons.graduationCap,
                        size: 16,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Learning modules',
                        style: textTheme.small.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 20),
            // Action button
            ShadButton(
              size: ShadButtonSize.lg,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    LucideIcons.logIn,
                    size: 18,
                    color: theme.colorScheme.primaryForeground,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Sign In',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primaryForeground,
                    ),
                  ),
                ],
              ),
              onPressed: () {
                Navigator.pushNamed(context, '/signin');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEducationalContentCard(
    BuildContext context,
    ShadThemeData theme,
    ContentItemModel item,
  ) {
    final textTheme = theme.textTheme;
    final bool canAccess =
        _coFunderInfo != null &&
        (item.targetUserLevels.isEmpty ||
            item.targetUserLevels.contains(_coFunderInfo!.level));
    final userLevel = _coFunderInfo?.level ?? 0;

    String contentType = '';
    Color contentTypeColor = theme.colorScheme.primary;
    IconData contentTypeIcon = LucideIcons.file;

    switch (item.type) {
      case 'blog':
        contentType = 'Blog';
        contentTypeIcon = LucideIcons.fileText;
        contentTypeColor = Colors.blue;
        break;
      case 'podcast':
        contentType = 'Podcast';
        contentTypeIcon = LucideIcons.headphones;
        contentTypeColor = Colors.purple;
        break;
      case 'educational_module':
        contentType = 'Module';
        contentTypeIcon = LucideIcons.graduationCap;
        contentTypeColor = Colors.green;
        break;
      case 'newsletter':
        contentType = 'Newsletter';
        contentTypeIcon = LucideIcons.mail;
        contentTypeColor = Colors.orange;
        break;
      default:
        contentType = item.type
            .split('_')
            .map(
              (word) =>
                  word.isNotEmpty
                      ? word[0].toUpperCase() + word.substring(1)
                      : '',
            )
            .join(' ');
    }

    return ShadCard(
      width: 280,
      radius: BorderRadius.circular(12),
      footer: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.muted.withOpacity(0.3),
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(12),
            bottomRight: Radius.circular(12),
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            canAccess
                ? ShadButton.outline(
                  size: ShadButtonSize.sm,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Read More',
                        style: TextStyle(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        LucideIcons.arrowRight,
                        size: 14,
                        color: theme.colorScheme.primary,
                      ),
                    ],
                  ),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      EducationalContentDetailPage.routeName,
                      arguments: item,
                    );
                  },
                )
                : ShadButton.outline(
                  size: ShadButtonSize.sm,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        LucideIcons.lock,
                        size: 14,
                        color: theme.colorScheme.destructive,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'View Details',
                        style: TextStyle(
                          fontSize: 12,
                          color: theme.colorScheme.destructive,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder:
                          (context) => ShadDialog(
                            title: Row(
                              children: [
                                Icon(
                                  LucideIcons.lock,
                                  size: 18,
                                  color: theme.colorScheme.destructive,
                                ),
                                const SizedBox(width: 8),
                                const Text('Content Locked'),
                              ],
                            ),
                            description: Text(
                              'This content requires Level(s) ${item.targetUserLevels.join(", ")} to access. Your current level is $userLevel. Please upgrade your level to view this content.',
                            ),
                            actions: [
                              ShadButton(
                                child: const Text('OK'),
                                onPressed: () => Navigator.of(context).pop(),
                              ),
                            ],
                          ),
                    );
                  },
                ),
          ],
        ),
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: contentTypeColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            contentTypeIcon,
                            size: 12,
                            color: contentTypeColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            contentType,
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: contentTypeColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    if (item.publishedAt != null)
                      Text(
                        DateFormat.yMMMd().format(item.publishedAt!),
                        style: textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                          fontSize: 10,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  item.title,
                  style: textTheme.h4.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    height: 1.3,
                    color:
                        canAccess
                            ? theme.colorScheme.foreground
                            : theme.colorScheme.foreground.withOpacity(0.7),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: Text(
                    item.summary ?? '',
                    style: textTheme.muted.copyWith(
                      fontSize: 12,
                      height: 1.5,
                      color:
                          canAccess
                              ? theme.colorScheme.foreground.withOpacity(0.8)
                              : theme.colorScheme.foreground.withOpacity(0.5),
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          if (!canAccess)
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.destructive.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  LucideIcons.lock,
                  size: 16,
                  color: theme.colorScheme.destructive,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Placeholder for your Stripe checkout link
  static const String _level4StripePriceId = 'price_1PwwuNLUEgLoxoT0dvBZKak8';

  Widget _buildProgressToNextLevelSection(
    BuildContext context,
    ShadThemeData theme,
    CoFunderInfo coFunder,
  ) {
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;
    String title;
    String description;
    String buttonText;
    IconData levelIcon;
    VoidCallback? onPressedAction;
    double currentProgress = 0;
    int totalRequired = 1;
    bool isComplete = false;

    if (_isLoadingProfileDetails) {
      return const Center(child: CircularProgressIndicator.adaptive());
    }

    // Case 1: User's current_level is 0 or 1
    if (coFunder.level < 2) {
      // Handles level 0 and 1
      if (coFunder.level2Approved) {
        // Admin approved L2, but current_level in DB might not be 2 yet.
        // Guide towards L3 content.
        title = 'Level 2 Approved! Continue Learning';
        description =
            'Your Level 2 access is approved! Explore educational content to prepare for Level 3.';
        buttonText = 'Explore Level 2 Modules';
        levelIcon = LucideIcons.bookOpenCheck;
        currentProgress =
            _readModulesCount
                .toDouble(); // Assuming _readModulesCount is cumulative
        totalRequired =
            _requiredModulesForLevel3 > 0
                ? _requiredModulesForLevel3
                : 1; // Progress towards L3 modules
        isComplete = _readModulesCount >= _requiredModulesForLevel3;
        onPressedAction =
            () => Navigator.pushNamed(
              context,
              EducationalContentListPage.routeName,
            );
      } else if (coFunder.level2ApplicationSubmitted) {
        // L2 Submitted, pending admin approval
        title = 'Level 2 Application Pending';
        description =
            'Your Level 2 profile is under review. We will notify you once it\'s processed.';
        buttonText = 'Status: Pending Review';
        levelIcon = LucideIcons.hourglass;
        isComplete = true;
        currentProgress = 1.0;
        totalRequired = 1;
        onPressedAction = null;
      } else if (_readModulesCount >= _requiredModulesForLevel2) {
        // Ready to apply for L2
        title = 'Advance to Level 2: Interest';
        description =
            'Complete your profile to access advanced litigation educational content.';
        buttonText = 'Complete Profile for Level 2';
        levelIcon = LucideIcons.userCheck;
        currentProgress = _readModulesCount.toDouble();
        totalRequired =
            _requiredModulesForLevel2 > 0 ? _requiredModulesForLevel2 : 1;
        isComplete = true;
        onPressedAction =
            () => Navigator.pushNamed(
              context,
              CoFunderProfileLevel2FormPage.routeName,
            );
      } else {
        // Needs to read L1/L2 modules
        title = 'Begin Your Journey (Level 1)';
        description =
            'Read educational modules to understand litigation funding and progress to Level 2.';
        buttonText = 'Explore Educational Content';
        levelIcon = LucideIcons.bookOpen;
        currentProgress = _readModulesCount.toDouble();
        totalRequired =
            _requiredModulesForLevel2 > 0 ? _requiredModulesForLevel2 : 1;
        isComplete = _readModulesCount >= _requiredModulesForLevel2;
        onPressedAction =
            () => Navigator.pushNamed(
              context,
              EducationalContentListPage.routeName,
            );
      }
    }
    // Case 2: User's current_level is 2
    else if (coFunder.level == 2) {
      if (coFunder.level3Approved) {
        // Admin approved L3 application. User's current_level in DB is still 2.
        // They MUST read all L3 modules to have current_level auto-updated to 3 (logic for auto-update is in EducationalContentDetailPage).
        if (_readModulesCount >= _requiredModulesForLevel3) {
          // They've read all L3 modules. The auto-upgrade to current_level=3 should ideally happen
          // from EducationalContentDetailPage when the final L3 module is marked read.
          // This card section might show briefly if dashboard refreshes before current_level updates,
          // or if the auto-upgrade mechanism needs a nudge/has an issue.
          // Once current_level becomes 3, this whole `else if (coFunder.level == 2)` block will be skipped.
          title = 'Level 3 Unlocked!';
          description =
              'You have completed all requirements for Level 3. One more article to go!';
          buttonText = 'Read Final Article';
          levelIcon = LucideIcons.bookOpenCheck;
          currentProgress = _readModulesCount.toDouble();
          totalRequired =
              _requiredModulesForLevel3 > 0 ? _requiredModulesForLevel3 : 1;
          isComplete =
              _readModulesCount >=
              _requiredModulesForLevel3; // This will be false until all read
          onPressedAction =
              () => Navigator.pushNamed(
                context,
                EducationalContentListPage.routeName,
              );
        } else {
          // L3 approved by admin, but still needs to read L3 modules.
          title = 'Final Step to Level 3 (Associate)';
          description =
              'Your Level 3 application is approved! Please read the remaining educational modules to complete your upgrade.';
          buttonText = 'Complete Level 3 Modules';
          levelIcon = LucideIcons.bookOpenCheck;
          currentProgress = _readModulesCount.toDouble();
          totalRequired =
              _requiredModulesForLevel3 > 0 ? _requiredModulesForLevel3 : 1;
          isComplete =
              _readModulesCount >=
              _requiredModulesForLevel3; // This will be false until all read
          onPressedAction =
              () => Navigator.pushNamed(
                context,
                EducationalContentListPage.routeName,
              );
        }
      } else if (coFunder.level3ApplicationSubmitted) {
        // L3 Application Submitted, pending admin approval
        title = 'Level 3 Application Pending';
        description = 'Your Level 3 application is under review by our team.';
        buttonText = 'Status: Pending Review';
        levelIcon = LucideIcons.hourglass;
        isComplete = true;
        currentProgress = 1.0;
        totalRequired = 1;
        onPressedAction = null; // No action for user here
      } else if (_readModulesCount >= _requiredModulesForLevel3) {
        // Has read all L3 modules, now ready to APPLY for L3
        title = 'Apply for Level 3 (Associate)';
        description =
            'You have completed the required educational modules. Submit your application to become an Associate.';
        buttonText = 'Apply for Level 3';
        levelIcon = LucideIcons.award;
        currentProgress = _readModulesCount.toDouble();
        totalRequired =
            _requiredModulesForLevel3 > 0 ? _requiredModulesForLevel3 : 1;
        isComplete = true; // Module reading part is complete
        onPressedAction =
            () => Navigator.pushNamed(
              context,
              CoFunderProfileLevel3FormPage.routeName,
            );
      } else {
        // Still needs to read L3 modules (before being able to apply)
        title = 'Progress to Level 3: Desire!'; // Corrected title
        description =
            'Read all required educational modules to become an Associate.';
        buttonText = 'Explore Level 3 Modules'; // Corrected button text
        levelIcon = LucideIcons.bookOpenCheck;
        currentProgress = _readModulesCount.toDouble();
        totalRequired =
            _requiredModulesForLevel3 > 0 ? _requiredModulesForLevel3 : 1;
        isComplete =
            _readModulesCount >=
            _requiredModulesForLevel3; // This will be false
        onPressedAction =
            () => Navigator.pushNamed(
              context,
              EducationalContentListPage.routeName,
            );
      }
    }
    // Case 3: User's current_level is 3 or higher
    else if (coFunder.level >= 3 && _readModulesCount < 22) {
      // If they are already L3+, this specific "progress card" is redundant
      // as _buildKnowledgeTestSection is shown separately in the main build method.
      title = 'Level 3 Unlocked!';
      description =
          'You have completed all requirements for Level 3. One more article to go!';
      buttonText = 'Read Final Article';
      levelIcon = LucideIcons.bookOpenCheck;
      currentProgress = _readModulesCount.toDouble();
      totalRequired =
          _requiredModulesForLevel3 > 0 ? _requiredModulesForLevel3 : 1;
      isComplete =
          _readModulesCount >=
          _requiredModulesForLevel3; // This will be false until all read
      onPressedAction =
          () => Navigator.pushNamed(
            context,
            EducationalContentListPage.routeName,
          );
    }
    // Fallback, should ideally not be reached if logic is exhaustive
    else {
      return const SizedBox.shrink();
    }

    double progressValue = (currentProgress / totalRequired).clamp(0.0, 1.0);

    return ShadCard(
      width: double.infinity,
      title: Row(
        children: [
          Icon(levelIcon, size: 20, color: colorScheme.primary),
          const SizedBox(width: 10),
          Text(title, style: textTheme.h4.copyWith(fontSize: 18)),
        ],
      ),
      description: Padding(
        padding: const EdgeInsets.only(top: 4.0, bottom: 12.0),
        child: Text(description, style: textTheme.p.copyWith(fontSize: 15)),
      ),
      footer: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: progressValue,
                  backgroundColor: colorScheme.muted.withOpacity(0.3),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    isComplete
                        ? colorScheme.primary
                        : colorScheme.primary.withOpacity(0.7),
                  ),
                  minHeight: 8,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ShadButton(
            onPressed: onPressedAction,
            child: Center(
              // Centering the Row
              child: Row(
                mainAxisSize: MainAxisSize.min, // Making Row compact
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    buttonText,
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Icon(levelIcon, size: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showRequirementAlert(
    BuildContext context,
    String title,
    String message,
  ) {
    final theme = ShadTheme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return ShadDialog(
          title: Text(
            title,
            style: textTheme.h4.copyWith(color: colorScheme.foreground),
          ),
          description: Text(
            message,
            style: textTheme.p.copyWith(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: ShadButton(
                    onPressed: () {
                      Navigator.of(dialogContext).pop();
                    },
                    child: Center(
                      // Centering the Row
                      child: Row(
                        mainAxisSize: MainAxisSize.min, // Making Row compact
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Got it',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildKnowledgeTestSection(
    BuildContext context,
    ShadThemeData theme,
    CoFunderInfo coFunder,
  ) {
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    // Show this section only if user is Level 3 (Associate) or higher
    if (coFunder.level < 3 || _readModulesCount < 22) {
      return const SizedBox.shrink();
    }

    String titleText;
    String descriptionText;
    String buttonText;
    VoidCallback onPressedAction;
    IconData buttonIcon;

    if (coFunder.level4Subscribed) {
      titleText = 'Assess your understanding of Litigation Funding!';
      descriptionText =
          'You are now a 3Pay Global Associate. Complete the knowledge test to demonstrate your understanding and qualify as an Associate.';
      buttonText = 'Take Knowledge Test';
      buttonIcon = LucideIcons.clipboardCheck;
      onPressedAction = () {
        Navigator.pushNamed(context, KnowledgeTestPage.routeName);
      };
    } else {
      titleText = 'Activate Your Associate Membership';
      descriptionText =
          'A one time fee is required to activate your 3Pay Global Associate Membership. This allows you full access to claims in the platform';
      buttonText = 'Pay £600/lifetime';
      buttonIcon =
          LucideIcons.creditCard; // Or LucideIcons.lock, LucideIcons.zap
      onPressedAction = () {
        final String? currentUserId = _pbService.currentUser?.id;
        if (currentUserId == null) {
          if (context.mounted) {
            ShadToaster.of(context).show(
              ShadToast.destructive(
                title: const Text('User Not Found'),
                description: const Text(
                  'Could not identify current user. Please log in again.',
                ),
              ),
            );
          }
          return;
        }
        if (_level4StripePriceId == 'price_YOUR_LEVEL_4_PRICE_ID_HERE') {
          if (context.mounted) {
            ShadToaster.of(context).show(
              ShadToast.destructive(
                title: const Text('Configuration Error'),
                description: const Text(
                  'Stripe Price ID for Level 4 is not configured.',
                ),
              ),
            );
          }
          return;
        }

        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => CofunderOneTimePaymentPage(
                  // Renamed page
                  pocketBaseUserId: currentUserId,
                  stripePriceId: _level4StripePriceId,
                  amount: 60000, // Example: £600.00 in pence (600 * 100)
                  currency: "gbp", // Example currency
                ),
          ),
        );
      };
    }

    return ShadCard(
      width: double.infinity,
      title: Row(
        children: [
          Icon(LucideIcons.graduationCap, size: 20, color: colorScheme.primary),
          const SizedBox(width: 10),
          Expanded(
            child: Text(titleText, style: textTheme.h4.copyWith(fontSize: 18)),
          ),
        ],
      ),
      description: Padding(
        padding: const EdgeInsets.only(top: 4.0, bottom: 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  LucideIcons.info,
                  color: colorScheme.secondary.withOpacity(0.7),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    descriptionText,
                    style: textTheme.p.copyWith(
                      color: colorScheme.foreground,
                      fontSize: 15,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      footer: ShadButton(
        onPressed: onPressedAction,
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(buttonIcon, size: 20),
              const SizedBox(width: 10),
              Text(
                buttonText,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInvestmentOpportunitiesSection(
    BuildContext context,
    ShadThemeData theme,
    CoFunderInfo coFunder,
  ) {
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return ShadCard(
      width: double.infinity,
      title: Row(
        children: [
          Icon(LucideIcons.briefcase, size: 20, color: colorScheme.primary),
          const SizedBox(width: 10),
          Text(
            'Funding Opportunities',
            style: textTheme.h4.copyWith(fontSize: 18),
          ),
        ],
      ),
      description: Padding(
        padding: const EdgeInsets.only(top: 4.0, bottom: 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  LucideIcons.search,
                  color: colorScheme.secondary.withOpacity(0.7),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Browse and fund curated litigation cases.',
                    style: textTheme.p.copyWith(
                      color: colorScheme.foreground,
                      fontSize: 15,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  LucideIcons.info,
                  color: colorScheme.secondary.withOpacity(0.7),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Access to funding claims depends on your current level and successful verification.',
                    style: textTheme.p.copyWith(
                      color: colorScheme.mutedForeground,
                      fontSize: 13,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      footer: ShadButton.outline(
        onPressed: () {
          // Navigate to CofunderInvestmentFormPage, caseId will be handled within the form
          Navigator.pushNamed(context, CofunderInvestmentFormPage.routeName);
        },
        child: Center(
          // Centering the Row
          child: Row(
            mainAxisSize: MainAxisSize.min, // Making Row compact
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(LucideIcons.folderSearch, size: 20),
              const SizedBox(width: 10),
              Text(
                'Explore Approved Claims',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.secondaryForeground,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNewsletterSection(
    BuildContext context,
    ShadThemeData theme,
    CoFunderInfo coFunder,
  ) {
    // Hide the entire newsletter card if user is already subscribed
    if (coFunder.isSubscribedToNewsletter) {
      return const SizedBox.shrink();
    }

    final textTheme = theme.textTheme;
    return ShadCard(
      title: Text('Stay Updated', style: textTheme.h4.copyWith(fontSize: 16)),
      description: Text(
        'Subscribe to our newsletter for the latest updates and insights.',
        style: textTheme.muted.copyWith(fontSize: 12),
      ),
      footer: ShadButton.outline(
        onPressed: _isUpdatingNewsletter ? null : _subscribeToNewsletter,
        child:
            _isUpdatingNewsletter
                ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator.adaptive(strokeWidth: 2),
                )
                : const Text('Subscribe Now'),
      ),
    );
  }

  Widget _buildMyPortfolioSection(
    BuildContext context,
    ShadThemeData theme,
    CoFunderInfo coFunder,
  ) {
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    if (coFunder.level < 3) {
      return const SizedBox.shrink();
    }

    return ShadCard(
      width: double.infinity,
      title: Row(
        children: [
          Icon(LucideIcons.trendingUp, size: 20, color: colorScheme.primary),
          const SizedBox(width: 10),
          Text(
            'My Funding Portfolio',
            style: textTheme.h4.copyWith(fontSize: 18),
          ),
        ],
      ),
      description: Padding(
        padding: const EdgeInsets.only(top: 4.0, bottom: 12.0),
        child: Text(
          'View your committed fundings, track their status, and see your returns.',
          style: textTheme.p.copyWith(
            color: colorScheme.foreground,
            fontSize: 15,
          ),
        ),
      ),
      footer: ShadButton(
        onPressed: () {
          Navigator.pushNamed(context, MyInvestmentsPage.routeName);
        },
        child: Center(
          // Centering the Row
          child: Row(
            mainAxisSize: MainAxisSize.min, // Making Row compact
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(LucideIcons.layoutDashboard, size: 20),
              const SizedBox(width: 10),
              Text(
                'View My Funding',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNDASection(
    BuildContext context,
    ShadThemeData theme,
    CoFunderInfo coFunder,
  ) {
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return ShadCard(
      width: double.infinity,
      title: Row(
        children: [
          Icon(LucideIcons.fileText, size: 20, color: colorScheme.primary),
          const SizedBox(width: 10),
          Text(
            'Non-Disclosure Agreement Required',
            style: textTheme.h4.copyWith(fontSize: 18),
          ),
        ],
      ),
      description: Padding(
        padding: const EdgeInsets.only(top: 4.0, bottom: 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  LucideIcons.info,
                  color: colorScheme.destructive,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'You need to sign a Non-Disclosure Agreement (NDA) before accessing sensitive case information.',
                    style: textTheme.p.copyWith(
                      color: colorScheme.foreground,
                      fontSize: 15,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      footer: ShadButton(
        onPressed: () {
          Navigator.pushNamed(context, SignNDAPage.routeName);
        },
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(LucideIcons.fileText, size: 20),
              const SizedBox(width: 10),
              Text(
                'Sign NDA Now',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
