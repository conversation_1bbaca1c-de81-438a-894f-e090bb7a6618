import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/form_submission_error_alert_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/widgets/account_settings_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/providers/cofunder_profile_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/widgets/cofunder_profile_info_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/widgets/cofunder_preferences_widget.dart';
import '../../utils/responsive_layout.dart';
import '../widgets/loading_widgets.dart';

class CoFunderProfilePage extends ConsumerStatefulWidget {
  static const String routeName = '/cofunder-profile';

  const CoFunderProfilePage({super.key});

  @override
  ConsumerState<CoFunderProfilePage> createState() =>
      _CoFunderProfilePageState();
}

class _CoFunderProfilePageState extends ConsumerState<CoFunderProfilePage>
    with SingleTickerProviderStateMixin {
  final PocketBaseService _pbService = PocketBaseService();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Load profile data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(coFunderProfileProvider.notifier).loadProfile();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _navigateToEditProfile() {
    Navigator.of(context).pushNamed('/cofunder/profile/edit');
  }

  Future<void> _refreshProfile() async {
    await ref.read(coFunderProfileProvider.notifier).loadProfile();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final currentUser = _pbService.currentUser;
    final profileState = ref.watch(coFunderProfileProvider);

    // Handle authentication check
    if (currentUser == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text('My Profile', style: theme.textTheme.h4),
          backgroundColor: theme.colorScheme.background,
          iconTheme: IconThemeData(color: theme.colorScheme.foreground),
        ),
        body: const Center(child: Text('Please sign in to view your profile')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('My Profile', style: theme.textTheme.h4),
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(LucideIcons.user), text: 'Profile'),
            Tab(icon: Icon(LucideIcons.settings), text: 'Preferences'),
            Tab(icon: Icon(LucideIcons.logOut), text: 'Account'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Error display
          if (profileState.error != null)
            FormSubmissionErrorAlertWidget(
              title: 'Error',
              description: profileState.error!,
            ),

          // Success message display
          if (profileState.successMessage != null)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: Row(
                children: [
                  Icon(LucideIcons.check, color: Colors.green, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      profileState.successMessage!,
                      style: theme.textTheme.small.copyWith(
                        color: Colors.green,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(LucideIcons.x, size: 16),
                    onPressed: () {
                      ref
                          .read(coFunderProfileProvider.notifier)
                          .clearSuccessMessage();
                    },
                  ),
                ],
              ),
            ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Profile Tab
                RefreshIndicator.adaptive(
                  onRefresh: _refreshProfile,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: CoFunderResponsiveLayout.getResponsivePadding(
                      context,
                    ),
                    child: CoFunderProfileInfoWidget(
                      onEditPressed: _navigateToEditProfile,
                    ),
                  ),
                ),

                // Preferences Tab
                RefreshIndicator.adaptive(
                  onRefresh: _refreshProfile,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: CoFunderResponsiveLayout.getResponsivePadding(
                      context,
                    ),
                    child: CoFunderPreferencesWidget(),
                  ),
                ),

                // Account Tab
                RefreshIndicator.adaptive(
                  onRefresh: _refreshProfile,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: CoFunderResponsiveLayout.getResponsivePadding(
                      context,
                    ),
                    child: AccountSettingsWidget(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
