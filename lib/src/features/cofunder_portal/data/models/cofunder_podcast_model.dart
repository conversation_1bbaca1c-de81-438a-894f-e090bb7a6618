import 'package:pocketbase/pocketbase.dart';

class CoFunderPodcastModel {
  final String id;
  final String collectionId;
  final String collectionName;
  final DateTime created;
  final DateTime updated;
  final String contentItemSlug;
  final int? episodeNumber;
  final int? durationSeconds;
  final String? showNotes;
  final String? transcript;
  final String? audioFile; // Filename for audio file
  final int? downloadCount;
  final int? playCount;
  final bool? featured;
  final String? season;
  final List<Map<String, dynamic>>? guestSpeakers;

  CoFunderPodcastModel({
    required this.id,
    required this.collectionId,
    required this.collectionName,
    required this.created,
    required this.updated,
    required this.contentItemSlug,
    this.episodeNumber,
    this.durationSeconds,
    this.showNotes,
    this.transcript,
    this.audioFile,
    this.downloadCount,
    this.playCount,
    this.featured,
    this.season,
    this.guestSpeakers,
  });

  factory CoFunderPodcastModel.fromRecord(RecordModel record) {
    return CoFunderPodcastModel(
      id: record.id,
      collectionId: record.collectionId,
      collectionName: record.collectionName,
      created: DateTime.parse(record.get<String>('created')),
      updated: DateTime.parse(record.get<String>('updated')),
      contentItemSlug: record.data['content_item_slug'] ?? '',
      episodeNumber: record.data['episode_number']?.toInt(),
      durationSeconds: record.data['duration_seconds']?.toInt(),
      showNotes: record.data['show_notes'],
      transcript: record.data['transcript'],
      audioFile: record.data['audio_file'],
      downloadCount: record.data['download_count']?.toInt() ?? 0,
      playCount: record.data['play_count']?.toInt() ?? 0,
      featured: record.data['featured'] ?? false,
      season: record.data['season'],
      guestSpeakers:
          record.data['guest_speakers'] != null
              ? List<Map<String, dynamic>>.from(record.data['guest_speakers'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'collectionId': collectionId,
      'collectionName': collectionName,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
      'content_item_slug': contentItemSlug,
      'episode_number': episodeNumber,
      'duration_seconds': durationSeconds,
      'show_notes': showNotes,
      'transcript': transcript,
      'audio_file': audioFile,
      'download_count': downloadCount,
      'play_count': playCount,
      'featured': featured,
      'season': season,
      'guest_speakers': guestSpeakers,
    };
  }

  /// Get formatted duration string (e.g., "1h 23m" or "45m 30s")
  String get formattedDuration {
    if (durationSeconds == null) return 'Unknown duration';

    final duration = Duration(seconds: durationSeconds!);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// Get episode title with number if available
  String getEpisodeTitle(String baseTitle) {
    if (episodeNumber != null) {
      return 'Episode $episodeNumber: $baseTitle';
    }
    return baseTitle;
  }

  /// Check if podcast has guest speakers
  bool get hasGuestSpeakers {
    return guestSpeakers != null && guestSpeakers!.isNotEmpty;
  }

  /// Get guest speakers names as a formatted string
  String get guestSpeakersText {
    if (!hasGuestSpeakers) return '';

    final names =
        guestSpeakers!
            .map((speaker) => speaker['name'] as String? ?? 'Unknown')
            .toList();

    if (names.length == 1) {
      return 'Guest: ${names.first}';
    } else if (names.length == 2) {
      return 'Guests: ${names.join(' & ')}';
    } else {
      return 'Guests: ${names.take(names.length - 1).join(', ')} & ${names.last}';
    }
  }

  CoFunderPodcastModel copyWith({
    String? id,
    String? collectionId,
    String? collectionName,
    DateTime? created,
    DateTime? updated,
    String? contentItemSlug,
    int? episodeNumber,
    int? durationSeconds,
    String? showNotes,
    String? transcript,
    String? audioFile,
    int? downloadCount,
    int? playCount,
    bool? featured,
    String? season,
    List<Map<String, dynamic>>? guestSpeakers,
  }) {
    return CoFunderPodcastModel(
      id: id ?? this.id,
      collectionId: collectionId ?? this.collectionId,
      collectionName: collectionName ?? this.collectionName,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      contentItemSlug: contentItemSlug ?? this.contentItemSlug,
      episodeNumber: episodeNumber ?? this.episodeNumber,
      durationSeconds: durationSeconds ?? this.durationSeconds,
      showNotes: showNotes ?? this.showNotes,
      transcript: transcript ?? this.transcript,
      audioFile: audioFile ?? this.audioFile,
      downloadCount: downloadCount ?? this.downloadCount,
      playCount: playCount ?? this.playCount,
      featured: featured ?? this.featured,
      season: season ?? this.season,
      guestSpeakers: guestSpeakers ?? this.guestSpeakers,
    );
  }
}
