class CoFunderProfile {
  final String id;
  final String? userId; // Links to users collection
  final Map<String, dynamic>? userExpanded; // Expanded user data
  final int currentLevel;
  final String amlKycStatus;
  final List<String> readEducationalContent;
  final Map<String, dynamic>? investmentPreferences;
  final Map<String, dynamic>? notificationPreferences;
  final double? fundingCapacity;
  final List<String> preferredSectors;
  final String? riskTolerance;
  final bool level4Subscribed;
  final DateTime created;
  final DateTime updated;

  CoFunderProfile({
    required this.id,
    this.userId,
    this.userExpanded,
    required this.currentLevel,
    required this.amlKycStatus,
    required this.readEducationalContent,
    this.investmentPreferences,
    this.notificationPreferences,
    this.fundingCapacity,
    required this.preferredSectors,
    this.riskTolerance,
    required this.level4Subscribed,
    required this.created,
    required this.updated,
  });

  // Convenience getters for expanded user data
  String? get name => userExpanded?['name'] as String?;
  String? get email => userExpanded?['email'] as String?;
  String? get phoneNumber => userExpanded?['phone_number'] as String?;
  String? get address => userExpanded?['address'] as String?;

  /// Check if the co-funder profile is complete
  /// A profile is considered complete when it has:
  /// - Basic user information (name, email)
  /// - Investment preferences set
  /// - At least one preferred sector
  /// - Risk tolerance defined
  bool get isProfileComplete {
    // Check if basic user info is available
    if (name == null || name!.isEmpty || email == null || email!.isEmpty) {
      return false;
    }

    // Check if investment preferences are set
    if (investmentPreferences == null || investmentPreferences!.isEmpty) {
      return false;
    }

    // Check if at least one preferred sector is selected
    if (preferredSectors.isEmpty) {
      return false;
    }

    // Check if risk tolerance is defined
    if (riskTolerance == null || riskTolerance!.isEmpty) {
      return false;
    }

    return true;
  }

  factory CoFunderProfile.fromJson(Map<String, dynamic> json) {
    return CoFunderProfile(
      id: json['id'] as String,
      userId: json['user_id'] as String?,
      userExpanded: json['expand']?['user_id'] as Map<String, dynamic>?,
      currentLevel: json['current_level'] as int? ?? 1,
      amlKycStatus: json['aml_kyc_status'] as String? ?? 'pending',
      readEducationalContent:
          (json['read_educational_content'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      investmentPreferences:
          json['investment_preferences'] as Map<String, dynamic>?,
      notificationPreferences:
          json['notification_preferences'] as Map<String, dynamic>?,
      fundingCapacity: (json['funding_capacity'] as num?)?.toDouble(),
      preferredSectors:
          (json['preferred_sectors'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      riskTolerance: json['risk_tolerance'] as String?,
      level4Subscribed: json['level_4_subscribed'] as bool? ?? false,
      created:
          json['created'] != null
              ? DateTime.tryParse(json['created'] as String) ?? DateTime.now()
              : DateTime.now(),
      updated:
          json['updated'] != null
              ? DateTime.tryParse(json['updated'] as String) ?? DateTime.now()
              : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'current_level': currentLevel,
      'aml_kyc_status': amlKycStatus,
      'read_educational_content': readEducationalContent,
      'investment_preferences': investmentPreferences,
      'notification_preferences': notificationPreferences,
      'funding_capacity': fundingCapacity,
      'preferred_sectors': preferredSectors,
      'risk_tolerance': riskTolerance,
      'level_4_subscribed': level4Subscribed,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  CoFunderProfile copyWith({
    String? id,
    String? userId,
    Map<String, dynamic>? userExpanded,
    int? currentLevel,
    String? amlKycStatus,
    List<String>? readEducationalContent,
    Map<String, dynamic>? investmentPreferences,
    Map<String, dynamic>? notificationPreferences,
    double? fundingCapacity,
    List<String>? preferredSectors,
    String? riskTolerance,
    bool? level4Subscribed,
    DateTime? created,
    DateTime? updated,
  }) {
    return CoFunderProfile(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userExpanded: userExpanded ?? this.userExpanded,
      currentLevel: currentLevel ?? this.currentLevel,
      amlKycStatus: amlKycStatus ?? this.amlKycStatus,
      readEducationalContent:
          readEducationalContent ?? this.readEducationalContent,
      investmentPreferences:
          investmentPreferences ?? this.investmentPreferences,
      notificationPreferences:
          notificationPreferences ?? this.notificationPreferences,
      fundingCapacity: fundingCapacity ?? this.fundingCapacity,
      preferredSectors: preferredSectors ?? this.preferredSectors,
      riskTolerance: riskTolerance ?? this.riskTolerance,
      level4Subscribed: level4Subscribed ?? this.level4Subscribed,
      created: created ?? this.created,
      updated: updated ?? this.updated,
    );
  }

  @override
  String toString() {
    return 'CoFunderProfile(id: $id, userId: $userId, currentLevel: $currentLevel, amlKycStatus: $amlKycStatus, level4Subscribed: $level4Subscribed)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CoFunderProfile &&
        other.id == id &&
        other.userId == userId &&
        other.currentLevel == currentLevel &&
        other.amlKycStatus == amlKycStatus;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        currentLevel.hashCode ^
        amlKycStatus.hashCode;
  }
}
