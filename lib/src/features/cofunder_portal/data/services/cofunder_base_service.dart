import 'package:pocketbase/pocketbase.dart';
import 'package:http/http.dart' as http;
import 'package:three_pay_group_litigation_platform/src/core/services/enhanced_notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/data/models/enhanced_notification_model.dart';

/// Base service class for co-funder portal operations
/// Provides common functionality and follows existing codebase patterns
class CoFunderBaseService {
  final PocketBase _pb;
  final PocketBaseService _pocketBaseService;
  late final EnhancedNotificationService _notificationService;

  CoFunderBaseService()
    : _pb = PocketBaseService().pb,
      _pocketBaseService = PocketBaseService() {
    _notificationService = EnhancedNotificationService(_pocketBaseService);
  }

  /// Get the current authenticated user
  RecordModel? get currentUser => _pb.authStore.record;

  /// Check if user is authenticated
  bool get isAuthenticated => _pb.authStore.isValid;

  /// Check if current user is a co-funder
  bool get isCoFunder {
    final user = currentUser;
    return user != null && user.data['user_type'] == 'co_funder';
  }

  /// Get current user ID
  String? get currentUserId => currentUser?.id;

  /// Verify authentication and co-funder role
  void _verifyCoFunderAuth() {
    if (!isAuthenticated) {
      throw Exception('User not authenticated');
    }
    if (!isCoFunder) {
      throw Exception('User is not a co-funder');
    }
  }

  /// Get co-funder profile for current user
  Future<CoFunderProfile?> getCurrentCoFunderProfile() async {
    try {
      _verifyCoFunderAuth();

      final userId = currentUserId!;
      final records = await _pb
          .collection('co_funder_profiles')
          .getList(filter: 'user_id = "$userId"', expand: 'user_id');

      if (records.items.isEmpty) {
        LoggerService.info('No co-funder profile found for user: $userId');
        return null;
      }

      final recordData = records.items.first.toJson();
      LoggerService.info(
        'Retrieved co-funder profile data: ${recordData.keys}',
      );

      return CoFunderProfile.fromJson(recordData);
    } catch (e) {
      LoggerService.error('Error getting co-funder profile', e);
      rethrow;
    }
  }

  /// Create co-funder profile for current user
  Future<CoFunderProfile> createCoFunderProfile({
    int currentLevel = 1,
    Map<String, dynamic>? investmentPreferences,
    Map<String, dynamic>? notificationPreferences,
  }) async {
    try {
      _verifyCoFunderAuth();

      final userId = currentUserId!;

      final profileData = {
        'user_id': userId,
        'current_level': currentLevel,
        'aml_kyc_status': 'pending',
        'read_educational_content': <String>[],
        'investment_preferences': investmentPreferences ?? {},
        'notification_preferences': notificationPreferences ?? {},
        'preferred_sectors': <String>[],
        'level_4_subscribed': false,
      };

      final record = await _pb
          .collection('co_funder_profiles')
          .create(body: profileData, expand: 'user_id');

      LoggerService.info('Created co-funder profile for user: $userId');
      return CoFunderProfile.fromJson(record.toJson());
    } catch (e) {
      LoggerService.error('Error creating co-funder profile', e);
      rethrow;
    }
  }

  /// Update co-funder profile
  Future<CoFunderProfile> updateCoFunderProfile({
    int? currentLevel,
    String? amlKycStatus,
    Map<String, dynamic>? investmentPreferences,
    Map<String, dynamic>? notificationPreferences,
    double? fundingCapacity,
    List<String>? preferredSectors,
    String? riskTolerance,
    bool? level4Subscribed,
  }) async {
    try {
      _verifyCoFunderAuth();

      final profile = await getCurrentCoFunderProfile();
      if (profile == null) {
        throw Exception('Co-funder profile not found');
      }

      final updateData = <String, dynamic>{};
      if (currentLevel != null) updateData['current_level'] = currentLevel;
      if (amlKycStatus != null) updateData['aml_kyc_status'] = amlKycStatus;
      if (investmentPreferences != null)
        updateData['investment_preferences'] = investmentPreferences;
      if (notificationPreferences != null)
        updateData['notification_preferences'] = notificationPreferences;
      if (fundingCapacity != null)
        updateData['funding_capacity'] = fundingCapacity;
      if (preferredSectors != null)
        updateData['preferred_sectors'] = preferredSectors;
      if (riskTolerance != null) updateData['risk_tolerance'] = riskTolerance;
      if (level4Subscribed != null)
        updateData['level_4_subscribed'] = level4Subscribed;

      final record = await _pb
          .collection('co_funder_profiles')
          .update(profile.id, body: updateData, expand: 'user_id');

      LoggerService.info(
        'Updated co-funder profile for user: ${currentUserId}',
      );
      return CoFunderProfile.fromJson(record.toJson());
    } catch (e) {
      LoggerService.error('Error updating co-funder profile', e);
      rethrow;
    }
  }

  /// Upload profile picture
  Future<void> uploadProfilePicture(
    List<int> fileBytes,
    String fileName,
  ) async {
    try {
      _verifyCoFunderAuth();

      final userId = currentUserId!;

      final multipartFile = http.MultipartFile.fromBytes(
        'avatar',
        fileBytes,
        filename: fileName,
      );

      await _pb.collection('users').update(userId, files: [multipartFile]);

      LoggerService.info('Uploaded profile picture for user: $userId');
    } catch (e) {
      LoggerService.error('Error uploading profile picture', e);
      rethrow;
    }
  }

  /// Get profile picture URL
  String? getProfilePictureUrl() {
    try {
      final user = currentUser;
      if (user == null) return null;

      final avatarField = user.getStringValue('avatar');
      if (avatarField.isEmpty) return null;

      return _pb.files.getURL(user, avatarField).toString();
    } catch (e) {
      LoggerService.error('Error getting profile picture URL', e);
      return null;
    }
  }

  /// Get profile picture URL from user record
  String? getProfilePictureUrlFromRecord(RecordModel userRecord) {
    try {
      final avatarField = userRecord.getStringValue('avatar');
      if (avatarField.isEmpty) return null;

      return _pb.files.getURL(userRecord, avatarField).toString();
    } catch (e) {
      LoggerService.error('Error getting profile picture URL from record', e);
      return null;
    }
  }

  /// Get user record by ID (for URL generation)
  Future<RecordModel> getUserRecord(String userId) async {
    try {
      return await _pb.collection('users').getOne(userId);
    } catch (e) {
      LoggerService.error('Error getting user record', e);
      rethrow;
    }
  }

  /// Get notifications for current co-funder
  Future<List<EnhancedNotificationModel>> getCoFunderNotifications({
    int page = 1,
    int perPage = 20,
    bool unreadOnly = false,
  }) async {
    try {
      _verifyCoFunderAuth();

      await _notificationService.initialize();
      final allNotifications = _notificationService.notifications.value;

      LoggerService.info(
        'Enhanced notification service has ${allNotifications.length} total notifications',
      );

      // Filter notifications for co-funder if needed
      var filteredNotifications = allNotifications;
      if (unreadOnly) {
        filteredNotifications =
            allNotifications.where((n) => !n.isRead).toList();
      }

      // Apply pagination
      final startIndex = (page - 1) * perPage;
      final endIndex = startIndex + perPage;

      if (startIndex >= filteredNotifications.length) {
        return [];
      }

      final paginatedNotifications = filteredNotifications.sublist(
        startIndex,
        endIndex > filteredNotifications.length
            ? filteredNotifications.length
            : endIndex,
      );

      LoggerService.info(
        'Returning ${paginatedNotifications.length} notifications for co-funder (page $page)',
      );

      return paginatedNotifications;
    } catch (e) {
      LoggerService.error('Error getting co-funder notifications', e);
      rethrow;
    }
  }

  /// Get error message from exception
  String getErrorMessage(dynamic error) {
    if (error is ClientException) {
      final response = error.response;
      if (response.containsKey('message')) {
        return response['message'] as String;
      }
      if (response.containsKey('data')) {
        final data = response['data'] as Map<String, dynamic>;
        if (data.isNotEmpty) {
          final firstError = data.values.first;
          if (firstError is Map && firstError.containsKey('message')) {
            return firstError['message'] as String;
          }
        }
      }
    }
    return error.toString();
  }

  /// Dispose resources
  void dispose() {
    // Clean up any resources if needed
  }
}
