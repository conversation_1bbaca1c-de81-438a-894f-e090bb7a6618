import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as li;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/toast_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/storage_type.dart';
import 'package:three_pay_group_litigation_platform/src/shared/presentation/widgets/storage_indicator_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin/presentation/widgets/storage_status_widget.dart';

/// Storage configuration model
class StorageConfiguration {
  final bool autoSync;
  final bool enableCaching;
  final int cacheRetentionDays;
  final bool compressUploads;
  final int maxFileSize;
  final List<String> allowedFileTypes;
  final bool enableVersioning;
  final int maxVersions;
  final bool enableNotifications;

  const StorageConfiguration({
    required this.autoSync,
    required this.enableCaching,
    required this.cacheRetentionDays,
    required this.compressUploads,
    required this.maxFileSize,
    required this.allowedFileTypes,
    required this.enableVersioning,
    required this.maxVersions,
    required this.enableNotifications,
  });

  StorageConfiguration copyWith({
    bool? autoSync,
    bool? enableCaching,
    int? cacheRetentionDays,
    bool? compressUploads,
    int? maxFileSize,
    List<String>? allowedFileTypes,
    bool? enableVersioning,
    int? maxVersions,
    bool? enableNotifications,
  }) {
    return StorageConfiguration(
      autoSync: autoSync ?? this.autoSync,
      enableCaching: enableCaching ?? this.enableCaching,
      cacheRetentionDays: cacheRetentionDays ?? this.cacheRetentionDays,
      compressUploads: compressUploads ?? this.compressUploads,
      maxFileSize: maxFileSize ?? this.maxFileSize,
      allowedFileTypes: allowedFileTypes ?? this.allowedFileTypes,
      enableVersioning: enableVersioning ?? this.enableVersioning,
      maxVersions: maxVersions ?? this.maxVersions,
      enableNotifications: enableNotifications ?? this.enableNotifications,
    );
  }
}

/// Storage settings page for configuring Google Drive integration
class StorageSettingsPage extends ConsumerStatefulWidget {
  const StorageSettingsPage({super.key});

  @override
  ConsumerState<StorageSettingsPage> createState() =>
      _StorageSettingsPageState();
}

class _StorageSettingsPageState extends ConsumerState<StorageSettingsPage> {
  StorageConfiguration _config = const StorageConfiguration(
    autoSync: true,
    enableCaching: true,
    cacheRetentionDays: 7,
    compressUploads: false,
    maxFileSize: 10,
    allowedFileTypes: ['pdf', 'jpg', 'jpeg', 'png', 'gif'],
    enableVersioning: true,
    maxVersions: 5,
    enableNotifications: true,
  );

  bool _isLoading = false;
  bool _hasChanges = false;

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Storage Settings'),
        actions: [
          if (_hasChanges)
            TextButton(onPressed: _saveSettings, child: const Text('Save')),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStorageOverview(theme),
            const SizedBox(height: 24),
            _buildSyncSettings(theme),
            const SizedBox(height: 24),
            _buildCacheSettings(theme),
            const SizedBox(height: 24),
            _buildUploadSettings(theme),
            const SizedBox(height: 24),
            _buildVersioningSettings(theme),
            const SizedBox(height: 24),
            _buildNotificationSettings(theme),
            const SizedBox(height: 24),
            _buildTroubleshootingSection(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildStorageOverview(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: theme.radius,
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const StorageIndicatorWidget(
                storageType: StorageType.googleDrive,
                showLabel: false,
                iconSize: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Google Drive Storage',
                      style: theme.textTheme.h3.copyWith(
                        color: theme.colorScheme.foreground,
                      ),
                    ),
                    Text(
                      'Primary storage for all documents',
                      style: theme.textTheme.large.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ),
              ShadButton.outline(
                onPressed: _testConnection,
                child: const Text('Test Connection'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const StorageStatusWidget(
            showDetailedMetrics: false,
            allowRefresh: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSyncSettings(ShadThemeData theme) {
    return _buildSettingsSection(
      theme,
      'Synchronization',
      li.LucideIcons.refreshCw,
      [
        _buildSwitchSetting(
          theme,
          'Auto-sync',
          'Automatically sync documents with Google Drive',
          _config.autoSync,
          (value) => _updateConfig(_config.copyWith(autoSync: value)),
        ),
      ],
    );
  }

  Widget _buildCacheSettings(ShadThemeData theme) {
    return _buildSettingsSection(
      theme,
      'Cache Settings',
      li.LucideIcons.database,
      [
        _buildSwitchSetting(
          theme,
          'Enable caching',
          'Cache documents locally for faster access',
          _config.enableCaching,
          (value) => _updateConfig(_config.copyWith(enableCaching: value)),
        ),
        if (_config.enableCaching) ...[
          const SizedBox(height: 12),
          _buildSliderSetting(
            theme,
            'Cache retention',
            'Days to keep cached documents',
            _config.cacheRetentionDays.toDouble(),
            1,
            30,
            (value) => _updateConfig(
              _config.copyWith(cacheRetentionDays: value.round()),
            ),
            '${_config.cacheRetentionDays} days',
          ),
        ],
      ],
    );
  }

  Widget _buildUploadSettings(ShadThemeData theme) {
    return _buildSettingsSection(
      theme,
      'Upload Settings',
      li.LucideIcons.uploadCloud,
      [
        _buildSwitchSetting(
          theme,
          'Compress uploads',
          'Compress files before uploading to save space',
          _config.compressUploads,
          (value) => _updateConfig(_config.copyWith(compressUploads: value)),
        ),
        const SizedBox(height: 12),
        _buildSliderSetting(
          theme,
          'Max file size',
          'Maximum file size for uploads',
          _config.maxFileSize.toDouble(),
          1,
          100,
          (value) =>
              _updateConfig(_config.copyWith(maxFileSize: value.round())),
          '${_config.maxFileSize} MB',
        ),
        const SizedBox(height: 12),
        _buildFileTypesSettings(theme),
      ],
    );
  }

  Widget _buildVersioningSettings(ShadThemeData theme) {
    return _buildSettingsSection(
      theme,
      'Version Control',
      li.LucideIcons.gitBranch,
      [
        _buildSwitchSetting(
          theme,
          'Enable versioning',
          'Keep multiple versions of documents',
          _config.enableVersioning,
          (value) => _updateConfig(_config.copyWith(enableVersioning: value)),
        ),
        if (_config.enableVersioning) ...[
          const SizedBox(height: 12),
          _buildSliderSetting(
            theme,
            'Max versions',
            'Maximum number of versions to keep',
            _config.maxVersions.toDouble(),
            1,
            20,
            (value) =>
                _updateConfig(_config.copyWith(maxVersions: value.round())),
            '${_config.maxVersions} versions',
          ),
        ],
      ],
    );
  }

  Widget _buildNotificationSettings(ShadThemeData theme) {
    return _buildSettingsSection(theme, 'Notifications', li.LucideIcons.bell, [
      _buildSwitchSetting(
        theme,
        'Storage notifications',
        'Receive notifications about storage events',
        _config.enableNotifications,
        (value) => _updateConfig(_config.copyWith(enableNotifications: value)),
      ),
    ]);
  }

  Widget _buildTroubleshootingSection(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: theme.radius,
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                li.LucideIcons.wrench,
                size: 20,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 12),
              Text(
                'Troubleshooting',
                style: theme.textTheme.h4.copyWith(
                  color: theme.colorScheme.foreground,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ShadButton.outline(
                  onPressed: _clearCache,
                  child: const Text('Clear Cache'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ShadButton.outline(
                  onPressed: _resetSettings,
                  child: const Text('Reset Settings'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ShadButton.outline(
            onPressed: _exportLogs,
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(li.LucideIcons.download, size: 16),
                SizedBox(width: 8),
                Text('Export Logs'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    ShadThemeData theme,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: theme.radius,
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: theme.colorScheme.primary),
              const SizedBox(width: 12),
              Text(
                title,
                style: theme.textTheme.h4.copyWith(
                  color: theme.colorScheme.foreground,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchSetting(
    ShadThemeData theme,
    String title,
    String description,
    bool value,
    Function(bool) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.large.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.foreground,
                ),
              ),
              Text(
                description,
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
            ],
          ),
        ),
        Switch(value: value, onChanged: onChanged),
      ],
    );
  }

  Widget _buildSliderSetting(
    ShadThemeData theme,
    String title,
    String description,
    double value,
    double min,
    double max,
    Function(double) onChanged,
    String valueText,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.large.copyWith(
                      fontWeight: FontWeight.w500,
                      color: theme.colorScheme.foreground,
                    ),
                  ),
                  Text(
                    description,
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              valueText,
              style: theme.textTheme.large.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: (max - min).round(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildFileTypesSettings(ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Allowed file types',
          style: theme.textTheme.large.copyWith(
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.foreground,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              _config.allowedFileTypes
                  .map(
                    (type) => Chip(
                      label: Text(type.toUpperCase()),
                      deleteIcon: const Icon(li.LucideIcons.x, size: 14),
                      onDeleted: () => _removeFileType(type),
                    ),
                  )
                  .toList(),
        ),
        const SizedBox(height: 8),
        ShadButton.outline(
          onPressed: _addFileType,
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(li.LucideIcons.plus, size: 16),
              SizedBox(width: 8),
              Text('Add File Type'),
            ],
          ),
        ),
      ],
    );
  }

  void _updateConfig(StorageConfiguration newConfig) {
    setState(() {
      _config = newConfig;
      _hasChanges = true;
    });
  }

  void _removeFileType(String type) {
    final newTypes = List<String>.from(_config.allowedFileTypes);
    newTypes.remove(type);
    _updateConfig(_config.copyWith(allowedFileTypes: newTypes));
  }

  void _addFileType() {
    // TODO: Show dialog to add new file type
    ToastService.showInfo(context, 'Add file type dialog coming soon');
  }

  void _testConnection() async {
    ToastService.showInfo(context, 'Testing Google Drive connection...');

    // Simulate connection test
    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      ToastService.showSuccess(context, 'Google Drive connection successful');
    }
  }

  void _clearCache() async {
    ToastService.showInfo(context, 'Clearing cache...');

    // Simulate cache clearing
    await Future.delayed(const Duration(seconds: 1));

    if (mounted) {
      ToastService.showSuccess(context, 'Cache cleared successfully');
    }
  }

  void _resetSettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Reset Settings'),
            content: const Text(
              'Are you sure you want to reset all storage settings to defaults?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  setState(() {
                    _config = const StorageConfiguration(
                      autoSync: true,
                      enableCaching: true,
                      cacheRetentionDays: 7,
                      compressUploads: false,
                      maxFileSize: 10,
                      allowedFileTypes: [
                        'pdf',
                        'docx',
                        'doc',
                        'txt',
                        'jpg',
                        'png',
                      ],
                      enableVersioning: true,
                      maxVersions: 5,
                      enableNotifications: true,
                    );
                    _hasChanges = true;
                  });
                  ToastService.showSuccess(
                    context,
                    'Settings reset to defaults',
                  );
                },
                child: const Text('Reset'),
              ),
            ],
          ),
    );
  }

  void _exportLogs() {
    ToastService.showInfo(context, 'Exporting storage logs...');
    LoggerService.info('Storage logs export requested');
  }

  void _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate saving settings
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        _hasChanges = false;
        _isLoading = false;
      });

      if (mounted) {
        ToastService.showSuccess(
          context,
          'Storage settings saved successfully',
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ToastService.showError(
          context,
          'Failed to save settings: ${e.toString()}',
        );
      }
    }
  }
}
