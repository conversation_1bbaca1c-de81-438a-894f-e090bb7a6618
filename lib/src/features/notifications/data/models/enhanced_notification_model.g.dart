// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enhanced_notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EnhancedNotificationModelImpl _$$EnhancedNotificationModelImplFromJson(
  Map<String, dynamic> json,
) => _$EnhancedNotificationModelImpl(
  id: json['id'] as String,
  title: json['title'] as String,
  message: json['message'] as String,
  type: json['type'] as String? ?? 'info',
  isRead: json['isRead'] as bool? ?? false,
  recipientId:
      (json['recipientId'] as List<dynamic>?)?.map((e) => e as String).toList(),
  created:
      json['created'] == null
          ? null
          : DateTime.parse(json['created'] as String),
  updated:
      json['updated'] == null
          ? null
          : DateTime.parse(json['updated'] as String),
  readAt:
      json['readAt'] == null ? null : DateTime.parse(json['readAt'] as String),
  isGlobal: json['isGlobal'] as bool? ?? false,
  link: json['link'] as String?,
  icon: json['icon'] as String?,
);

Map<String, dynamic> _$$EnhancedNotificationModelImplToJson(
  _$EnhancedNotificationModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'message': instance.message,
  'type': instance.type,
  'isRead': instance.isRead,
  'recipientId': instance.recipientId,
  'created': instance.created?.toIso8601String(),
  'updated': instance.updated?.toIso8601String(),
  'readAt': instance.readAt?.toIso8601String(),
  'isGlobal': instance.isGlobal,
  'link': instance.link,
  'icon': instance.icon,
};

_$NotificationReadStateImpl _$$NotificationReadStateImplFromJson(
  Map<String, dynamic> json,
) => _$NotificationReadStateImpl(
  id: json['id'] as String,
  notificationId: json['notificationId'] as String,
  userId: json['userId'] as String,
  isRead: json['isRead'] as bool? ?? false,
  readAt:
      json['readAt'] == null ? null : DateTime.parse(json['readAt'] as String),
  created:
      json['created'] == null
          ? null
          : DateTime.parse(json['created'] as String),
  updated:
      json['updated'] == null
          ? null
          : DateTime.parse(json['updated'] as String),
);

Map<String, dynamic> _$$NotificationReadStateImplToJson(
  _$NotificationReadStateImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'notificationId': instance.notificationId,
  'userId': instance.userId,
  'isRead': instance.isRead,
  'readAt': instance.readAt?.toIso8601String(),
  'created': instance.created?.toIso8601String(),
  'updated': instance.updated?.toIso8601String(),
};
