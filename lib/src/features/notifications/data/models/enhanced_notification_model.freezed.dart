// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'enhanced_notification_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

EnhancedNotificationModel _$EnhancedNotificationModelFromJson(
  Map<String, dynamic> json,
) {
  return _EnhancedNotificationModel.fromJson(json);
}

/// @nodoc
mixin _$EnhancedNotificationModel {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  bool get isRead => throw _privateConstructorUsedError;
  List<String>? get recipientId => throw _privateConstructorUsedError;
  DateTime? get created => throw _privateConstructorUsedError;
  DateTime? get updated => throw _privateConstructorUsedError;
  DateTime? get readAt => throw _privateConstructorUsedError;
  bool get isGlobal => throw _privateConstructorUsedError;
  String? get link => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;

  /// Serializes this EnhancedNotificationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EnhancedNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EnhancedNotificationModelCopyWith<EnhancedNotificationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EnhancedNotificationModelCopyWith<$Res> {
  factory $EnhancedNotificationModelCopyWith(
    EnhancedNotificationModel value,
    $Res Function(EnhancedNotificationModel) then,
  ) = _$EnhancedNotificationModelCopyWithImpl<$Res, EnhancedNotificationModel>;
  @useResult
  $Res call({
    String id,
    String title,
    String message,
    String type,
    bool isRead,
    List<String>? recipientId,
    DateTime? created,
    DateTime? updated,
    DateTime? readAt,
    bool isGlobal,
    String? link,
    String? icon,
  });
}

/// @nodoc
class _$EnhancedNotificationModelCopyWithImpl<
  $Res,
  $Val extends EnhancedNotificationModel
>
    implements $EnhancedNotificationModelCopyWith<$Res> {
  _$EnhancedNotificationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EnhancedNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? message = null,
    Object? type = null,
    Object? isRead = null,
    Object? recipientId = freezed,
    Object? created = freezed,
    Object? updated = freezed,
    Object? readAt = freezed,
    Object? isGlobal = null,
    Object? link = freezed,
    Object? icon = freezed,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as String,
            title:
                null == title
                    ? _value.title
                    : title // ignore: cast_nullable_to_non_nullable
                        as String,
            message:
                null == message
                    ? _value.message
                    : message // ignore: cast_nullable_to_non_nullable
                        as String,
            type:
                null == type
                    ? _value.type
                    : type // ignore: cast_nullable_to_non_nullable
                        as String,
            isRead:
                null == isRead
                    ? _value.isRead
                    : isRead // ignore: cast_nullable_to_non_nullable
                        as bool,
            recipientId:
                freezed == recipientId
                    ? _value.recipientId
                    : recipientId // ignore: cast_nullable_to_non_nullable
                        as List<String>?,
            created:
                freezed == created
                    ? _value.created
                    : created // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
            updated:
                freezed == updated
                    ? _value.updated
                    : updated // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
            readAt:
                freezed == readAt
                    ? _value.readAt
                    : readAt // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
            isGlobal:
                null == isGlobal
                    ? _value.isGlobal
                    : isGlobal // ignore: cast_nullable_to_non_nullable
                        as bool,
            link:
                freezed == link
                    ? _value.link
                    : link // ignore: cast_nullable_to_non_nullable
                        as String?,
            icon:
                freezed == icon
                    ? _value.icon
                    : icon // ignore: cast_nullable_to_non_nullable
                        as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$EnhancedNotificationModelImplCopyWith<$Res>
    implements $EnhancedNotificationModelCopyWith<$Res> {
  factory _$$EnhancedNotificationModelImplCopyWith(
    _$EnhancedNotificationModelImpl value,
    $Res Function(_$EnhancedNotificationModelImpl) then,
  ) = __$$EnhancedNotificationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String message,
    String type,
    bool isRead,
    List<String>? recipientId,
    DateTime? created,
    DateTime? updated,
    DateTime? readAt,
    bool isGlobal,
    String? link,
    String? icon,
  });
}

/// @nodoc
class __$$EnhancedNotificationModelImplCopyWithImpl<$Res>
    extends
        _$EnhancedNotificationModelCopyWithImpl<
          $Res,
          _$EnhancedNotificationModelImpl
        >
    implements _$$EnhancedNotificationModelImplCopyWith<$Res> {
  __$$EnhancedNotificationModelImplCopyWithImpl(
    _$EnhancedNotificationModelImpl _value,
    $Res Function(_$EnhancedNotificationModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of EnhancedNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? message = null,
    Object? type = null,
    Object? isRead = null,
    Object? recipientId = freezed,
    Object? created = freezed,
    Object? updated = freezed,
    Object? readAt = freezed,
    Object? isGlobal = null,
    Object? link = freezed,
    Object? icon = freezed,
  }) {
    return _then(
      _$EnhancedNotificationModelImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as String,
        title:
            null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                    as String,
        message:
            null == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                    as String,
        type:
            null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                    as String,
        isRead:
            null == isRead
                ? _value.isRead
                : isRead // ignore: cast_nullable_to_non_nullable
                    as bool,
        recipientId:
            freezed == recipientId
                ? _value._recipientId
                : recipientId // ignore: cast_nullable_to_non_nullable
                    as List<String>?,
        created:
            freezed == created
                ? _value.created
                : created // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
        updated:
            freezed == updated
                ? _value.updated
                : updated // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
        readAt:
            freezed == readAt
                ? _value.readAt
                : readAt // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
        isGlobal:
            null == isGlobal
                ? _value.isGlobal
                : isGlobal // ignore: cast_nullable_to_non_nullable
                    as bool,
        link:
            freezed == link
                ? _value.link
                : link // ignore: cast_nullable_to_non_nullable
                    as String?,
        icon:
            freezed == icon
                ? _value.icon
                : icon // ignore: cast_nullable_to_non_nullable
                    as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$EnhancedNotificationModelImpl implements _EnhancedNotificationModel {
  const _$EnhancedNotificationModelImpl({
    required this.id,
    required this.title,
    required this.message,
    this.type = 'info',
    this.isRead = false,
    final List<String>? recipientId,
    this.created,
    this.updated,
    this.readAt,
    this.isGlobal = false,
    this.link,
    this.icon,
  }) : _recipientId = recipientId;

  factory _$EnhancedNotificationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$EnhancedNotificationModelImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String message;
  @override
  @JsonKey()
  final String type;
  @override
  @JsonKey()
  final bool isRead;
  final List<String>? _recipientId;
  @override
  List<String>? get recipientId {
    final value = _recipientId;
    if (value == null) return null;
    if (_recipientId is EqualUnmodifiableListView) return _recipientId;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? created;
  @override
  final DateTime? updated;
  @override
  final DateTime? readAt;
  @override
  @JsonKey()
  final bool isGlobal;
  @override
  final String? link;
  @override
  final String? icon;

  @override
  String toString() {
    return 'EnhancedNotificationModel(id: $id, title: $title, message: $message, type: $type, isRead: $isRead, recipientId: $recipientId, created: $created, updated: $updated, readAt: $readAt, isGlobal: $isGlobal, link: $link, icon: $icon)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EnhancedNotificationModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.isRead, isRead) || other.isRead == isRead) &&
            const DeepCollectionEquality().equals(
              other._recipientId,
              _recipientId,
            ) &&
            (identical(other.created, created) || other.created == created) &&
            (identical(other.updated, updated) || other.updated == updated) &&
            (identical(other.readAt, readAt) || other.readAt == readAt) &&
            (identical(other.isGlobal, isGlobal) ||
                other.isGlobal == isGlobal) &&
            (identical(other.link, link) || other.link == link) &&
            (identical(other.icon, icon) || other.icon == icon));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    title,
    message,
    type,
    isRead,
    const DeepCollectionEquality().hash(_recipientId),
    created,
    updated,
    readAt,
    isGlobal,
    link,
    icon,
  );

  /// Create a copy of EnhancedNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EnhancedNotificationModelImplCopyWith<_$EnhancedNotificationModelImpl>
  get copyWith => __$$EnhancedNotificationModelImplCopyWithImpl<
    _$EnhancedNotificationModelImpl
  >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EnhancedNotificationModelImplToJson(this);
  }
}

abstract class _EnhancedNotificationModel implements EnhancedNotificationModel {
  const factory _EnhancedNotificationModel({
    required final String id,
    required final String title,
    required final String message,
    final String type,
    final bool isRead,
    final List<String>? recipientId,
    final DateTime? created,
    final DateTime? updated,
    final DateTime? readAt,
    final bool isGlobal,
    final String? link,
    final String? icon,
  }) = _$EnhancedNotificationModelImpl;

  factory _EnhancedNotificationModel.fromJson(Map<String, dynamic> json) =
      _$EnhancedNotificationModelImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get message;
  @override
  String get type;
  @override
  bool get isRead;
  @override
  List<String>? get recipientId;
  @override
  DateTime? get created;
  @override
  DateTime? get updated;
  @override
  DateTime? get readAt;
  @override
  bool get isGlobal;
  @override
  String? get link;
  @override
  String? get icon;

  /// Create a copy of EnhancedNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EnhancedNotificationModelImplCopyWith<_$EnhancedNotificationModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}

NotificationReadState _$NotificationReadStateFromJson(
  Map<String, dynamic> json,
) {
  return _NotificationReadState.fromJson(json);
}

/// @nodoc
mixin _$NotificationReadState {
  String get id => throw _privateConstructorUsedError;
  String get notificationId => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  bool get isRead => throw _privateConstructorUsedError;
  DateTime? get readAt => throw _privateConstructorUsedError;
  DateTime? get created => throw _privateConstructorUsedError;
  DateTime? get updated => throw _privateConstructorUsedError;

  /// Serializes this NotificationReadState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NotificationReadState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationReadStateCopyWith<NotificationReadState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationReadStateCopyWith<$Res> {
  factory $NotificationReadStateCopyWith(
    NotificationReadState value,
    $Res Function(NotificationReadState) then,
  ) = _$NotificationReadStateCopyWithImpl<$Res, NotificationReadState>;
  @useResult
  $Res call({
    String id,
    String notificationId,
    String userId,
    bool isRead,
    DateTime? readAt,
    DateTime? created,
    DateTime? updated,
  });
}

/// @nodoc
class _$NotificationReadStateCopyWithImpl<
  $Res,
  $Val extends NotificationReadState
>
    implements $NotificationReadStateCopyWith<$Res> {
  _$NotificationReadStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationReadState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? notificationId = null,
    Object? userId = null,
    Object? isRead = null,
    Object? readAt = freezed,
    Object? created = freezed,
    Object? updated = freezed,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as String,
            notificationId:
                null == notificationId
                    ? _value.notificationId
                    : notificationId // ignore: cast_nullable_to_non_nullable
                        as String,
            userId:
                null == userId
                    ? _value.userId
                    : userId // ignore: cast_nullable_to_non_nullable
                        as String,
            isRead:
                null == isRead
                    ? _value.isRead
                    : isRead // ignore: cast_nullable_to_non_nullable
                        as bool,
            readAt:
                freezed == readAt
                    ? _value.readAt
                    : readAt // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
            created:
                freezed == created
                    ? _value.created
                    : created // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
            updated:
                freezed == updated
                    ? _value.updated
                    : updated // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$NotificationReadStateImplCopyWith<$Res>
    implements $NotificationReadStateCopyWith<$Res> {
  factory _$$NotificationReadStateImplCopyWith(
    _$NotificationReadStateImpl value,
    $Res Function(_$NotificationReadStateImpl) then,
  ) = __$$NotificationReadStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String notificationId,
    String userId,
    bool isRead,
    DateTime? readAt,
    DateTime? created,
    DateTime? updated,
  });
}

/// @nodoc
class __$$NotificationReadStateImplCopyWithImpl<$Res>
    extends
        _$NotificationReadStateCopyWithImpl<$Res, _$NotificationReadStateImpl>
    implements _$$NotificationReadStateImplCopyWith<$Res> {
  __$$NotificationReadStateImplCopyWithImpl(
    _$NotificationReadStateImpl _value,
    $Res Function(_$NotificationReadStateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NotificationReadState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? notificationId = null,
    Object? userId = null,
    Object? isRead = null,
    Object? readAt = freezed,
    Object? created = freezed,
    Object? updated = freezed,
  }) {
    return _then(
      _$NotificationReadStateImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as String,
        notificationId:
            null == notificationId
                ? _value.notificationId
                : notificationId // ignore: cast_nullable_to_non_nullable
                    as String,
        userId:
            null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                    as String,
        isRead:
            null == isRead
                ? _value.isRead
                : isRead // ignore: cast_nullable_to_non_nullable
                    as bool,
        readAt:
            freezed == readAt
                ? _value.readAt
                : readAt // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
        created:
            freezed == created
                ? _value.created
                : created // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
        updated:
            freezed == updated
                ? _value.updated
                : updated // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationReadStateImpl implements _NotificationReadState {
  const _$NotificationReadStateImpl({
    required this.id,
    required this.notificationId,
    required this.userId,
    this.isRead = false,
    this.readAt,
    this.created,
    this.updated,
  });

  factory _$NotificationReadStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationReadStateImplFromJson(json);

  @override
  final String id;
  @override
  final String notificationId;
  @override
  final String userId;
  @override
  @JsonKey()
  final bool isRead;
  @override
  final DateTime? readAt;
  @override
  final DateTime? created;
  @override
  final DateTime? updated;

  @override
  String toString() {
    return 'NotificationReadState(id: $id, notificationId: $notificationId, userId: $userId, isRead: $isRead, readAt: $readAt, created: $created, updated: $updated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationReadStateImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.isRead, isRead) || other.isRead == isRead) &&
            (identical(other.readAt, readAt) || other.readAt == readAt) &&
            (identical(other.created, created) || other.created == created) &&
            (identical(other.updated, updated) || other.updated == updated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    notificationId,
    userId,
    isRead,
    readAt,
    created,
    updated,
  );

  /// Create a copy of NotificationReadState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationReadStateImplCopyWith<_$NotificationReadStateImpl>
  get copyWith =>
      __$$NotificationReadStateImplCopyWithImpl<_$NotificationReadStateImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationReadStateImplToJson(this);
  }
}

abstract class _NotificationReadState implements NotificationReadState {
  const factory _NotificationReadState({
    required final String id,
    required final String notificationId,
    required final String userId,
    final bool isRead,
    final DateTime? readAt,
    final DateTime? created,
    final DateTime? updated,
  }) = _$NotificationReadStateImpl;

  factory _NotificationReadState.fromJson(Map<String, dynamic> json) =
      _$NotificationReadStateImpl.fromJson;

  @override
  String get id;
  @override
  String get notificationId;
  @override
  String get userId;
  @override
  bool get isRead;
  @override
  DateTime? get readAt;
  @override
  DateTime? get created;
  @override
  DateTime? get updated;

  /// Create a copy of NotificationReadState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationReadStateImplCopyWith<_$NotificationReadStateImpl>
  get copyWith => throw _privateConstructorUsedError;
}
