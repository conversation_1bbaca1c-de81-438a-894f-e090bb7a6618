import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_model.freezed.dart';
part 'notification_model.g.dart';

@freezed
class NotificationModel with _$NotificationModel {
  const factory NotificationModel({
    required String id,
    required String title,
    required String message,
    @Default('info') String type,
    @Default(false) bool isRead,
    List<String>? recipientId, // Stored as an array in PB, even for single relation
    DateTime? created,
    DateTime? updated,
  }) = _NotificationModel;

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);

  // Helper to create from PocketBase RecordModel if needed, though direct fromJson is often enough
  // factory NotificationModel.fromRecord(RecordModel record) {
  //   final data = Map<String, dynamic>.from(record.data);
  //   data['id'] = record.id;
  //   data['created'] = record.created;
  //   data['updated'] = record.updated;
  //   // PocketBase relation fields are returned as arrays of IDs
  //   if (data['recipientId'] != null && data['recipientId'] is String) {
  //     data['recipientId'] = [data['recipientId']];
  //   } else if (data['recipientId'] != null && data['recipientId'] is List) {
  //     // Ensure it's List<String>
  //     data['recipientId'] = List<String>.from(data['recipientId']);
  //   } else {
  //      data['recipientId'] = <String>[]; // Default to empty list if null or unexpected
  //   }
  //   return NotificationModel.fromJson(data);
  // }
}