import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pocketbase/pocketbase.dart';

part 'enhanced_notification_model.freezed.dart';
part 'enhanced_notification_model.g.dart';

@freezed
class EnhancedNotificationModel with _$EnhancedNotificationModel {
  const factory EnhancedNotificationModel({
    required String id,
    required String title,
    required String message,
    @Default('info') String type,
    @Default(false) bool isRead,
    List<String>? recipientId,
    DateTime? created,
    DateTime? updated,
    DateTime? readAt,
    @Default(false) bool isGlobal,
    String? link,
    String? icon,
  }) = _EnhancedNotificationModel;

  factory EnhancedNotificationModel.fromJson(Map<String, dynamic> json) =>
      _$EnhancedNotificationModelFromJson(json);

  factory EnhancedNotificationModel.fromRecord(RecordModel record) {
    final recipientIds = record.data['recipientId'] as List<dynamic>?;
    final isGlobal = recipientIds == null || recipientIds.isEmpty;

    return EnhancedNotificationModel(
      id: record.id,
      title: record.data['title'] ?? 'No Title',
      message: record.data['message'] ?? 'No Message',
      type: record.data['type'] ?? 'info',
      created: DateTime.tryParse(record.created),
      updated: DateTime.tryParse(record.updated),
      isRead: record.data['isRead'] ?? false,
      recipientId: recipientIds?.cast<String>(),
      isGlobal: isGlobal,
      link: record.data['link'],
      icon: record.data['icon'],
    );
  }

  // Helper method to convert to the original NotificationModel for backward compatibility
  factory EnhancedNotificationModel.fromOriginalModel(
    dynamic originalModel, {
    DateTime? readAt,
    bool isGlobal = false,
  }) {
    return EnhancedNotificationModel(
      id: originalModel.id,
      title: originalModel.title,
      message: originalModel.message,
      type: originalModel.type,
      isRead: originalModel.isRead,
      recipientId: originalModel.recipientId,
      created: originalModel.created,
      updated: originalModel.updated,
      readAt: readAt,
      isGlobal: isGlobal,
      link: originalModel.link,
      icon: originalModel.icon,
    );
  }
}

// Extension to add utility methods
extension EnhancedNotificationModelExtension on EnhancedNotificationModel {
  /// Returns true if this notification is visible to the given user
  bool isVisibleToUser(String userId) {
    if (isGlobal) return true;
    return recipientId?.contains(userId) ?? false;
  }

  /// Returns a user-friendly type display name
  String get typeDisplayName {
    switch (type.toLowerCase()) {
      case 'info':
        return 'Information';
      case 'alert':
        return 'Alert';
      case 'action_required':
        return 'Action Required';
      case 'success':
        return 'Success';
      case 'warning':
        return 'Warning';
      case 'error':
        return 'Error';
      default:
        return type;
    }
  }

  /// Returns true if this notification requires user action
  bool get requiresAction {
    return type.toLowerCase() == 'action_required';
  }

  /// Returns a formatted time string for when the notification was read
  String? get readTimeFormatted {
    if (readAt == null) return null;
    return readAt!.toLocal().toString();
  }

  /// Returns true if this notification was read today
  bool get wasReadToday {
    if (readAt == null) return false;
    final now = DateTime.now();
    final readDate = readAt!.toLocal();
    return now.year == readDate.year &&
        now.month == readDate.month &&
        now.day == readDate.day;
  }

  /// Get formatted time ago string
  String get ageFormatted {
    if (created == null) return 'Unknown time';

    final now = DateTime.now();
    final difference = now.difference(created!);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${(difference.inDays / 7).floor()}w ago';
    }
  }
}

/// Model for tracking user-specific read states for global notifications
@freezed
class NotificationReadState with _$NotificationReadState {
  const factory NotificationReadState({
    required String id,
    required String notificationId,
    required String userId,
    @Default(false) bool isRead,
    DateTime? readAt,
    DateTime? created,
    DateTime? updated,
  }) = _NotificationReadState;

  factory NotificationReadState.fromJson(Map<String, dynamic> json) =>
      _$NotificationReadStateFromJson(json);

  factory NotificationReadState.fromRecord(RecordModel record) {
    return NotificationReadState(
      id: record.id,
      notificationId: record.data['notification_id'] ?? '',
      userId: record.data['user_id'] ?? '',
      isRead: record.data['is_read'] ?? false,
      readAt:
          record.data['read_at'] != null
              ? DateTime.tryParse(record.data['read_at'])
              : null,
      created: DateTime.tryParse(record.created),
      updated: DateTime.tryParse(record.updated),
    );
  }
}

/// Utility class for notification filtering and sorting
class NotificationUtils {
  /// Filters notifications by read status
  static List<EnhancedNotificationModel> filterByReadStatus(
    List<EnhancedNotificationModel> notifications,
    bool showRead,
  ) {
    return notifications.where((n) => n.isRead == showRead).toList();
  }

  /// Filters notifications by type
  static List<EnhancedNotificationModel> filterByType(
    List<EnhancedNotificationModel> notifications,
    String type,
  ) {
    return notifications.where((n) => n.type == type).toList();
  }

  /// Sorts notifications by creation date (newest first by default)
  static List<EnhancedNotificationModel> sortByDate(
    List<EnhancedNotificationModel> notifications, {
    bool newestFirst = true,
  }) {
    final sorted = List<EnhancedNotificationModel>.from(notifications);
    sorted.sort((a, b) {
      if (a.created == null && b.created == null) return 0;
      if (a.created == null) return newestFirst ? 1 : -1;
      if (b.created == null) return newestFirst ? -1 : 1;

      return newestFirst
          ? b.created!.compareTo(a.created!)
          : a.created!.compareTo(b.created!);
    });
    return sorted;
  }

  /// Groups notifications by type
  static Map<String, List<EnhancedNotificationModel>> groupByType(
    List<EnhancedNotificationModel> notifications,
  ) {
    final grouped = <String, List<EnhancedNotificationModel>>{};
    for (final notification in notifications) {
      grouped.putIfAbsent(notification.type, () => []).add(notification);
    }
    return grouped;
  }

  /// Returns unread notifications count
  static int getUnreadCount(List<EnhancedNotificationModel> notifications) {
    return notifications.where((n) => !n.isRead).length;
  }

  /// Returns notifications that require action
  static List<EnhancedNotificationModel> getActionRequired(
    List<EnhancedNotificationModel> notifications,
  ) {
    return notifications.where((n) => n.requiresAction && !n.isRead).toList();
  }
}
