import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/data/models/enhanced_notification_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/presentation/pages/notification_detail_page.dart';

class NotificationsPage extends StatefulWidget {
  static const String routeName = '/notifications';

  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  late final NotificationService _notificationService;
  late final PocketBaseService _pbService;
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = true;
  String? _error;
  String _selectedFilter = 'all'; // all, unread, read
  String _selectedType = 'all'; // all, info, alert, action_required

  @override
  void initState() {
    super.initState();
    _pbService = PocketBaseService();
    _notificationService = NotificationService(_pbService);
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      await _notificationService.initialize();
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      LoggerService.error('Error initializing notifications', e);
      if (mounted) {
        setState(() {
          _error = 'Failed to load notifications: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _notificationService.dispose();
    super.dispose();
  }

  List<EnhancedNotificationModel> _getFilteredNotifications(
    List<EnhancedNotificationModel> notifications,
  ) {
    var filtered = notifications;

    // Filter by type first (if selected)
    if (_selectedType != 'all') {
      filtered = NotificationUtils.filterByType(filtered, _selectedType);
    }

    // Then filter by read status
    if (_selectedFilter == 'unread') {
      filtered = NotificationUtils.filterByReadStatus(filtered, false);
    } else if (_selectedFilter == 'read') {
      filtered = NotificationUtils.filterByReadStatus(filtered, true);
    }

    // Sort by date (newest first)
    return NotificationUtils.sortByDate(filtered, newestFirst: true);
  }

  /// Get unread count considering current type filter
  int _getUnreadCountForCurrentFilters(
    List<EnhancedNotificationModel> notifications,
  ) {
    var filtered = notifications;

    // Apply type filter if selected
    if (_selectedType != 'all') {
      filtered = NotificationUtils.filterByType(filtered, _selectedType);
    }

    // Count unread notifications
    return NotificationUtils.getUnreadCount(filtered);
  }

  /// Group notifications by date
  Map<String, List<EnhancedNotificationModel>> _getNotificationsGroupedByDate(
    List<EnhancedNotificationModel> notifications,
  ) {
    final filtered = _getFilteredNotifications(notifications);
    final grouped = <String, List<EnhancedNotificationModel>>{};

    for (final notification in filtered) {
      final group = _getDateGroup(notification);
      grouped.putIfAbsent(group, () => []).add(notification);
    }

    // Sort groups by priority (Today, Yesterday, This Week, etc.)
    final sortedGroups = <String, List<EnhancedNotificationModel>>{};
    const groupOrder = [
      'Today',
      'Yesterday',
      'This Week',
      'This Month',
      'Older',
      'Unknown',
    ];

    for (final group in groupOrder) {
      if (grouped.containsKey(group)) {
        sortedGroups[group] = grouped[group]!;
      }
    }

    // Add any groups not in the standard order
    for (final entry in grouped.entries) {
      if (!groupOrder.contains(entry.key)) {
        sortedGroups[entry.key] = entry.value;
      }
    }

    return sortedGroups;
  }

  /// Get date group for a notification
  String _getDateGroup(EnhancedNotificationModel notification) {
    if (notification.created == null) return 'Unknown';

    final now = DateTime.now();
    final notificationDate = notification.created!;
    final difference = now.difference(notificationDate);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return 'This Week';
    } else if (difference.inDays < 30) {
      return 'This Month';
    } else {
      return 'Older';
    }
  }

  Future<void> _markAsRead(EnhancedNotificationModel notification) async {
    if (notification.isRead) return;

    try {
      await _notificationService.markAsRead(notification.id);
    } catch (e) {
      LoggerService.error('Error marking notification as read', e);
      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Error'),
            description: Text(
              'Could not mark notification as read: ${e.toString()}',
            ),
          ),
        );
      }
    }
  }

  /// Mark all notifications as read
  Future<void> _markAllAsRead(
    List<EnhancedNotificationModel> notifications,
  ) async {
    try {
      final unreadNotifications = notifications.where((n) => !n.isRead);

      for (final notification in unreadNotifications) {
        await _notificationService.markAsRead(notification.id);
      }

      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast(
            title: const Text('Success'),
            description: const Text('All notifications marked as read'),
          ),
        );
      }
    } catch (e) {
      LoggerService.error('Error marking all notifications as read', e);
      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Error'),
            description: Text(
              'Could not mark all notifications as read: ${e.toString()}',
            ),
          ),
        );
      }
    }
  }

  void _navigateToDetail(EnhancedNotificationModel notification) {
    Navigator.of(
      context,
    ).pushNamed(NotificationDetailPage.routeName, arguments: notification.id);

    // Mark as read when opening detail
    if (!notification.isRead) {
      _markAsRead(notification);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isDesktop = MediaQuery.of(context).size.width >= 768;

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: _buildAppBar(theme),
      body: Column(
        children: [
          // Filters section
          _buildFiltersSection(theme),

          // Content section
          Expanded(child: _buildContent(theme, isDesktop)),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ShadThemeData theme) {
    return AppBar(
      backgroundColor: theme.colorScheme.background,
      elevation: 0,
      scrolledUnderElevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: Icon(LucideIcons.arrowLeft, color: theme.colorScheme.foreground),
      ),
      title: ValueListenableBuilder<List<EnhancedNotificationModel>>(
        valueListenable: _notificationService.notifications,
        builder: (context, notifications, child) {
          final filteredNotifications = _getFilteredNotifications(
            notifications,
          );

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Notifications',
                style: theme.textTheme.h3.copyWith(fontWeight: FontWeight.bold),
              ),
              if (notifications.isNotEmpty)
                Text(
                  '${filteredNotifications.length} notification${filteredNotifications.length == 1 ? '' : 's'}',
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
            ],
          );
        },
      ),
      actions: [
        ValueListenableBuilder<List<EnhancedNotificationModel>>(
          valueListenable: _notificationService.notifications,
          builder: (context, notifications, child) {
            final unreadCount = NotificationUtils.getUnreadCount(notifications);

            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Mark all as read button
                if (unreadCount > 0)
                  TextButton.icon(
                    onPressed: () => _showMarkAllAsReadDialog(notifications),
                    icon: Icon(LucideIcons.checkCheck, size: 16),
                    label: const Text('Mark All Read'),
                    style: TextButton.styleFrom(
                      foregroundColor: theme.colorScheme.primary,
                    ),
                  ),

                // Refresh button
                IconButton(
                  onPressed: _initializeNotifications,
                  icon: Icon(
                    LucideIcons.refreshCw,
                    color: theme.colorScheme.mutedForeground,
                  ),
                  tooltip: 'Refresh',
                ),

                const SizedBox(width: 8),
              ],
            );
          },
        ),
      ],
    );
  }

  void _showMarkAllAsReadDialog(List<EnhancedNotificationModel> notifications) {
    showDialog(
      context: context,
      builder:
          (context) => ShadDialog(
            title: const Text('Mark All as Read'),
            description: const Text(
              'Are you sure you want to mark all notifications as read? This action cannot be undone.',
            ),
            actions: [
              ShadButton.outline(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ShadButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _markAllAsRead(notifications);
                },
                child: const Text('Mark All Read'),
              ),
            ],
          ),
    );
  }

  Widget _buildContent(ShadThemeData theme, bool isDesktop) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return _buildErrorState(theme, _error!);
    }

    return ValueListenableBuilder<List<EnhancedNotificationModel>>(
      valueListenable: _notificationService.notifications,
      builder: (context, notifications, child) {
        if (notifications.isEmpty) {
          return _buildEmptyState(theme, 'No notifications yet');
        }

        final groupedNotifications = _getNotificationsGroupedByDate(
          notifications,
        );

        if (groupedNotifications.isEmpty) {
          return _buildEmptyState(theme, 'No notifications match your filters');
        }

        return _buildNotificationsList(theme, groupedNotifications, isDesktop);
      },
    );
  }

  Widget _buildErrorState(ShadThemeData theme, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              LucideIcons.triangle,
              size: 48,
              color: theme.colorScheme.destructive,
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading Notifications',
              style: theme.textTheme.h3,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: theme.textTheme.p.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ShadButton(
              onPressed: _initializeNotifications,
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(ShadThemeData theme, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              LucideIcons.bell,
              size: 48,
              color: theme.colorScheme.mutedForeground,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: theme.textTheme.h4.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Notifications will appear here when available',
              style: theme.textTheme.p.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationsList(
    ShadThemeData theme,
    Map<String, List<EnhancedNotificationModel>> groupedNotifications,
    bool isDesktop,
  ) {
    return RefreshIndicator(
      onRefresh: _initializeNotifications,
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(isDesktop ? 24 : 16),
        itemCount: _calculateItemCount(groupedNotifications),
        itemBuilder: (context, index) {
          return _buildListItem(groupedNotifications, index, theme);
        },
      ),
    );
  }

  int _calculateItemCount(
    Map<String, List<EnhancedNotificationModel>> grouped,
  ) {
    int count = 0;
    for (final group in grouped.values) {
      count += 1 + group.length; // 1 for header + notifications count
    }
    return count;
  }

  Widget _buildListItem(
    Map<String, List<EnhancedNotificationModel>> grouped,
    int index,
    ShadThemeData theme,
  ) {
    int currentIndex = 0;

    for (final entry in grouped.entries) {
      final groupName = entry.key;
      final notifications = entry.value;

      // Check if this is the group header
      if (currentIndex == index) {
        return _buildGroupHeader(groupName, theme);
      }
      currentIndex++;

      // Check if this is one of the notifications in this group
      for (int i = 0; i < notifications.length; i++) {
        if (currentIndex == index) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _buildNotificationCard(notifications[i], theme),
          );
        }
        currentIndex++;
      }
    }

    return const SizedBox.shrink();
  }

  Widget _buildGroupHeader(String groupName, ShadThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 8),
      child: Text(
        groupName,
        style: theme.textTheme.h4.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildFiltersSection(ShadThemeData theme) {
    return ValueListenableBuilder<List<EnhancedNotificationModel>>(
      valueListenable: _notificationService.notifications,
      builder: (context, notifications, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.card,
            border: Border(
              bottom: BorderSide(color: theme.colorScheme.border, width: 1),
            ),
          ),
          child: Column(
            children: [
              // Filter chips
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildFilterChip(
                      'All',
                      _selectedFilter == 'all',
                      () => setState(() => _selectedFilter = 'all'),
                      theme,
                    ),
                    const SizedBox(width: 8),
                    _buildFilterChip(
                      'Unread (${_getUnreadCountForCurrentFilters(notifications)})',
                      _selectedFilter == 'unread',
                      () => setState(() => _selectedFilter = 'unread'),
                      theme,
                    ),
                    const SizedBox(width: 8),
                    _buildFilterChip(
                      'Read',
                      _selectedFilter == 'read',
                      () => setState(() => _selectedFilter = 'read'),
                      theme,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 12),

              // Type filters
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildTypeFilterChip(
                      'All Types',
                      _selectedType == 'all',
                      () => setState(() => _selectedType = 'all'),
                      theme,
                    ),
                    const SizedBox(width: 8),
                    _buildTypeFilterChip(
                      'INFO',
                      _selectedType == 'info',
                      () => setState(() => _selectedType = 'info'),
                      theme,
                    ),
                    const SizedBox(width: 8),
                    _buildTypeFilterChip(
                      'ALERT',
                      _selectedType == 'alert',
                      () => setState(() => _selectedType = 'alert'),
                      theme,
                    ),
                    const SizedBox(width: 8),
                    _buildTypeFilterChip(
                      'ACTION REQUIRED',
                      _selectedType == 'action_required',
                      () => setState(() => _selectedType = 'action_required'),
                      theme,
                    ),
                    const SizedBox(width: 8),
                    _buildTypeFilterChip(
                      'SUCCESS',
                      _selectedType == 'success',
                      () => setState(() => _selectedType = 'success'),
                      theme,
                    ),
                    const SizedBox(width: 8),
                    _buildTypeFilterChip(
                      'WARNING',
                      _selectedType == 'warning',
                      () => setState(() => _selectedType = 'warning'),
                      theme,
                    ),
                    const SizedBox(width: 8),
                    _buildTypeFilterChip(
                      'ERROR',
                      _selectedType == 'error',
                      () => setState(() => _selectedType = 'error'),
                      theme,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFilterChip(
    String label,
    bool isSelected,
    VoidCallback onTap,
    ShadThemeData theme,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.secondary,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color:
                isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.border,
          ),
        ),
        child: Text(
          label,
          style: theme.textTheme.small.copyWith(
            color:
                isSelected
                    ? theme.colorScheme.primaryForeground
                    : theme.colorScheme.foreground,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildTypeFilterChip(
    String label,
    bool isSelected,
    VoidCallback onTap,
    ShadThemeData theme,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? theme.colorScheme.primary.withValues(alpha: 0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.border,
          ),
        ),
        child: Text(
          label,
          style: theme.textTheme.small.copyWith(
            fontSize: 11,
            color:
                isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.mutedForeground,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationCard(
    EnhancedNotificationModel notification,
    ShadThemeData theme,
  ) {
    final iconData = _getIconForType(notification.type);
    final typeColor = _getColorForType(notification.type, theme);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ShadCard(
        child: InkWell(
          onTap: () => _navigateToDetail(notification),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: typeColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(iconData, color: typeColor, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.title,
                              style: theme.textTheme.p.copyWith(
                                fontWeight:
                                    notification.isRead
                                        ? FontWeight.normal
                                        : FontWeight.w600,
                              ),
                            ),
                          ),
                          if (!notification.isRead)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        notification.message,
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Spacer(),
                          Text(
                            notification.ageFormatted,
                            style: theme.textTheme.small.copyWith(
                              color: theme.colorScheme.mutedForeground,
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getIconForType(String type) {
    switch (type.toLowerCase()) {
      case 'alert':
        return Icons.warning_amber;
      case 'action_required':
        return Icons.schedule;
      case 'success':
        return Icons.check_circle_outline;
      case 'warning':
        return Icons.warning_outlined;
      case 'error':
        return Icons.error_outline;
      default:
        return Icons.info_outline;
    }
  }

  Color _getColorForType(String type, ShadThemeData theme) {
    switch (type.toLowerCase()) {
      case 'alert':
        return theme.colorScheme.destructive;
      case 'action_required':
        return theme.colorScheme.primary;
      case 'success':
        return Colors.green;
      case 'warning':
        return Colors.orange;
      case 'error':
        return theme.colorScheme.destructive;
      default:
        return theme.colorScheme.primary;
    }
  }
}
