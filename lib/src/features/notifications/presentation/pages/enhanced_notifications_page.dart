import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/enhanced_notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/data/models/enhanced_notification_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/presentation/pages/notification_detail_page.dart';

class EnhancedNotificationsPage extends StatefulWidget {
  static const String routeName = '/enhanced-notifications';

  const EnhancedNotificationsPage({super.key});

  @override
  State<EnhancedNotificationsPage> createState() =>
      _EnhancedNotificationsPageState();
}

class _EnhancedNotificationsPageState extends State<EnhancedNotificationsPage> {
  late final EnhancedNotificationService _notificationService;
  late final PocketBaseService _pbService;
  bool _isLoading = true;
  String? _error;
  String _selectedFilter = 'all'; // all, unread, read
  String _selectedType = 'all'; // all, info, alert, action_required

  @override
  void initState() {
    super.initState();
    _pbService = PocketBaseService();
    _notificationService = EnhancedNotificationService(_pbService);
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      await _notificationService.initialize();
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      LoggerService.error('Error initializing notifications', e);
      if (mounted) {
        setState(() {
          _error = 'Failed to load notifications: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _notificationService.dispose();
    super.dispose();
  }

  List<EnhancedNotificationModel> _getFilteredNotifications(
    List<EnhancedNotificationModel> notifications,
  ) {
    var filtered = notifications;

    // Filter by read status
    if (_selectedFilter == 'unread') {
      filtered = NotificationUtils.filterByReadStatus(filtered, false);
    } else if (_selectedFilter == 'read') {
      filtered = NotificationUtils.filterByReadStatus(filtered, true);
    }

    // Filter by type
    if (_selectedType != 'all') {
      filtered = NotificationUtils.filterByType(filtered, _selectedType);
    }

    // Sort by date (newest first)
    return NotificationUtils.sortByDate(filtered, newestFirst: true);
  }

  Future<void> _markAsRead(EnhancedNotificationModel notification) async {
    if (notification.isRead) return;

    try {
      await _notificationService.markAsRead(notification.id);
    } catch (e) {
      LoggerService.error('Error marking notification as read', e);
      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Error'),
            description: Text(
              'Could not mark notification as read: ${e.toString()}',
            ),
          ),
        );
      }
    }
  }

  void _navigateToDetail(EnhancedNotificationModel notification) {
    Navigator.of(
      context,
    ).pushNamed(NotificationDetailPage.routeName, arguments: notification.id);

    // Mark as read when opening detail
    if (!notification.isRead) {
      _markAsRead(notification);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Notifications', style: theme.textTheme.h4),
        centerTitle: true,
        elevation: 0,
        backgroundColor: theme.colorScheme.background,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            LucideIcons.arrowLeft,
            size: 20,
            color: theme.colorScheme.foreground,
          ),
        ),
      ),
      body: Column(
        children: [_buildFilters(theme), Expanded(child: _buildBody(theme))],
      ),
    );
  }

  Widget _buildFilters(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: ShadSelect<String>(
              placeholder: const Text('Filter by status'),
              options: const [
                ShadOption(value: 'all', child: Text('All')),
                ShadOption(value: 'unread', child: Text('Unread')),
                ShadOption(value: 'read', child: Text('Read')),
              ],
              selectedOptionBuilder:
                  (context, value) => Text(
                    value == 'all'
                        ? 'All'
                        : value == 'unread'
                        ? 'Unread'
                        : 'Read',
                  ),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedFilter = value;
                  });
                }
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ShadSelect<String>(
              placeholder: const Text('Filter by type'),
              options: const [
                ShadOption(value: 'all', child: Text('All Types')),
                ShadOption(value: 'info', child: Text('Information')),
                ShadOption(value: 'alert', child: Text('Alert')),
                ShadOption(
                  value: 'action_required',
                  child: Text('Action Required'),
                ),
                ShadOption(value: 'success', child: Text('Success')),
                ShadOption(value: 'warning', child: Text('Warning')),
                ShadOption(value: 'error', child: Text('Error')),
              ],
              selectedOptionBuilder: (context, value) {
                switch (value) {
                  case 'all':
                    return const Text('All Types');
                  case 'info':
                    return const Text('Information');
                  case 'alert':
                    return const Text('Alert');
                  case 'action_required':
                    return const Text('Action Required');
                  case 'success':
                    return const Text('Success');
                  case 'warning':
                    return const Text('Warning');
                  case 'error':
                    return const Text('Error');
                  default:
                    return Text(value);
                }
              },
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedType = value;
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody(ShadThemeData theme) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              LucideIcons.circleAlert,
              size: 48,
              color: theme.colorScheme.destructive,
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: theme.textTheme.h4.copyWith(
                color: theme.colorScheme.destructive,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: theme.textTheme.p,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ShadButton(
              onPressed: _initializeNotifications,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return ValueListenableBuilder<List<EnhancedNotificationModel>>(
      valueListenable: _notificationService.notifications,
      builder: (context, notifications, child) {
        final filteredNotifications = _getFilteredNotifications(notifications);

        if (filteredNotifications.isEmpty) {
          return _buildEmptyState(theme);
        }

        return RefreshIndicator(
          onRefresh: _initializeNotifications,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredNotifications.length,
            itemBuilder: (context, index) {
              final notification = filteredNotifications[index];
              return _buildNotificationCard(notification, theme);
            },
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(ShadThemeData theme) {
    String message;
    IconData icon;

    switch (_selectedFilter) {
      case 'unread':
        message = 'No unread notifications';
        icon = LucideIcons.circleCheck;
        break;
      case 'read':
        message = 'No read notifications';
        icon = LucideIcons.inbox;
        break;
      default:
        message = 'No notifications yet';
        icon = LucideIcons.bell;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 48, color: theme.colorScheme.mutedForeground),
          const SizedBox(height: 16),
          Text(
            message,
            style: theme.textTheme.h4.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Notifications will appear here when available',
            style: theme.textTheme.p.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(
    EnhancedNotificationModel notification,
    ShadThemeData theme,
  ) {
    final iconData = _getIconForType(notification.type);
    final typeColor = _getColorForType(notification.type, theme);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ShadCard(
        child: InkWell(
          onTap: () => _navigateToDetail(notification),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: typeColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(iconData, color: typeColor, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.title,
                              style: theme.textTheme.p.copyWith(
                                fontWeight:
                                    notification.isRead
                                        ? FontWeight.normal
                                        : FontWeight.w600,
                              ),
                            ),
                          ),
                          if (!notification.isRead)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        notification.message,
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          if (notification.isGlobal)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.secondary,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Global',
                                style: theme.textTheme.small.copyWith(
                                  fontSize: 10,
                                  color: theme.colorScheme.secondaryForeground,
                                ),
                              ),
                            ),
                          const Spacer(),
                          Text(
                            notification.ageFormatted,
                            style: theme.textTheme.small.copyWith(
                              color: theme.colorScheme.mutedForeground,
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getIconForType(String type) {
    switch (type.toLowerCase()) {
      case 'alert':
        return LucideIcons.triangleAlert;
      case 'action_required':
        return LucideIcons.clock;
      case 'success':
        return LucideIcons.circleCheck;
      case 'warning':
        return LucideIcons.circleAlert;
      case 'error':
        return LucideIcons.circleX;
      default:
        return LucideIcons.info;
    }
  }

  Color _getColorForType(String type, ShadThemeData theme) {
    switch (type.toLowerCase()) {
      case 'alert':
        return theme.colorScheme.destructive;
      case 'action_required':
        return theme.colorScheme.primary;
      case 'success':
        return Colors.green;
      case 'warning':
        return Colors.orange;
      case 'error':
        return theme.colorScheme.destructive;
      default:
        return theme.colorScheme.primary;
    }
  }
}
