import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:pocketbase/pocketbase.dart';

class NotificationModel {
  final String id;
  final String title;
  final String message;
  final String type;
  final DateTime created;
  bool isRead;
  final String? link;
  final String? icon;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.created,
    required this.isRead,
    this.link,
    this.icon,
  });

  factory NotificationModel.fromRecord(RecordModel record) {
    return NotificationModel(
      id: record.id,
      title: record.data['title'] ?? 'No Title',
      message: record.data['message'] ?? 'No Message',
      type: record.data['type'] ?? 'general_info',
      created:
          DateTime.tryParse(record.get<String>('created')) ?? DateTime.now(),
      isRead: record.data['isRead'] ?? false,
      link: record.data['link'],
      icon: record.data['icon'],
    );
  }
}

class NotificationDetailPage extends StatefulWidget {
  static const String routeName = '/notification-detail';

  final String notificationId;

  const NotificationDetailPage({super.key, required this.notificationId});

  @override
  State<NotificationDetailPage> createState() => _NotificationDetailPageState();
}

class _NotificationDetailPageState extends State<NotificationDetailPage> {
  final PocketBaseService _pbService = PocketBaseService();
  late final NotificationService _notificationService;

  NotificationModel? _notification;
  bool _isLoading = true;
  String? _error;
  bool _isMarkingAsRead = false;

  @override
  void initState() {
    super.initState();
    _notificationService = NotificationService(_pbService);
    _fetchNotificationDetails();
  }

  Future<void> _fetchNotificationDetails() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final userId = _pbService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated.');
      }

      // Fetch the notification with permission check
      final record = await _pbService.client
          .collection('notifications')
          .getOne(widget.notificationId);

      // Check if user has permission to view this notification
      final recipientIds = record.data['recipientId'] as List<dynamic>?;
      final hasPermission =
          recipientIds == null ||
          recipientIds.isEmpty ||
          recipientIds.contains(userId);

      if (!hasPermission) {
        throw Exception(
          'You do not have permission to view this notification.',
        );
      }

      final notification = NotificationModel.fromRecord(record);

      if (mounted) {
        setState(() {
          _notification = notification;
          _isLoading = false;
        });

        // Auto-mark as read if currently unread
        if (!notification.isRead) {
          _markAsRead();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });

        LoggerService.error('Error fetching notification details', e);
      }
    }
  }

  Future<void> _markAsRead() async {
    if (_notification == null || _notification!.isRead || _isMarkingAsRead) {
      return;
    }

    setState(() {
      _isMarkingAsRead = true;
    });

    try {
      // Use the enhanced notification service to properly handle global vs user-specific notifications
      await _notificationService.markAsRead(_notification!.id);

      if (mounted) {
        setState(() {
          _notification!.isRead = true;
          _isMarkingAsRead = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isMarkingAsRead = false;
        });

        LoggerService.error('Error marking notification as read', e);

        // Show error toast but don't block the UI
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Error'),
            description: Text(
              'Could not mark notification as read: ${e.toString()}',
            ),
          ),
        );
      }
    }
  }

  IconData _getIconForType(String type) {
    switch (type) {
      case 'success':
        return LucideIcons.check;
      case 'error':
        return LucideIcons.x;
      case 'warning':
        return LucideIcons.info;
      case 'info':
      default:
        return LucideIcons.info;
    }
  }

  Color _getColorForType(String type, ShadThemeData theme) {
    switch (type) {
      case 'success':
        return Colors.green;
      case 'error':
        return theme.colorScheme.destructive;
      case 'warning':
        return Colors.amber;
      case 'info':
      default:
        return theme.colorScheme.primary;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Notification Details', style: theme.textTheme.h4),
        centerTitle: true,
        elevation: 0,
        backgroundColor: theme.colorScheme.background,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            LucideIcons.arrowLeft,
            size: 20,
            color: theme.colorScheme.foreground,
          ),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ),
      body: _buildBody(theme),
    );
  }

  Widget _buildBody(ShadThemeData theme) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator.adaptive());
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                LucideIcons.x,
                size: 48,
                color: theme.colorScheme.destructive,
              ),
              const SizedBox(height: 16),
              Text(
                'Error Loading Notification',
                style: theme.textTheme.h3,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: theme.textTheme.p.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ShadButton(
                onPressed: _fetchNotificationDetails,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (_notification == null) {
      return Center(
        child: Text('Notification not found', style: theme.textTheme.p),
      );
    }

    return _buildNotificationContent(theme);
  }

  Widget _buildNotificationContent(ShadThemeData theme) {
    final notification = _notification!;
    final iconData = _getIconForType(notification.type);
    final typeColor = _getColorForType(notification.type, theme);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isDesktop = constraints.maxWidth > 768;
          final maxWidth = isDesktop ? 600.0 : double.infinity;

          return Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: maxWidth),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with icon and type
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: typeColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(iconData, size: 24, color: typeColor),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              notification.type.toUpperCase(),
                              style: theme.textTheme.small.copyWith(
                                color: typeColor,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0.5,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              DateFormat.yMMMd().add_jm().format(
                                notification.created.toLocal(),
                              ),
                              style: theme.textTheme.small.copyWith(
                                color: theme.colorScheme.mutedForeground,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Read status indicator
                      if (!notification.isRead && !_isMarkingAsRead)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary,
                            shape: BoxShape.circle,
                          ),
                        )
                      else if (_isMarkingAsRead)
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              theme.colorScheme.primary,
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 32),

                  // Title
                  Text(
                    notification.title,
                    style: theme.textTheme.h2.copyWith(
                      fontWeight: FontWeight.bold,
                      height: 1.3,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Message content
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.muted.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.colorScheme.border,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      notification.message,
                      style: theme.textTheme.p.copyWith(
                        height: 1.6,
                        fontSize: 16,
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Additional actions or links if available
                  if (notification.link != null &&
                      notification.link!.isNotEmpty)
                    ShadButton(
                      onPressed: () {
                        // TODO: Handle link navigation
                        LoggerService.info('Navigate to: ${notification.link}');
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(LucideIcons.externalLink, size: 16),
                          const SizedBox(width: 8),
                          const Text('View Details'),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
