import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/providers/app_badge_provider.dart';
import 'package:three_pay_group_litigation_platform/src/core/providers/notification_counter_provider.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Debug page for testing app badge functionality
class BadgeTestPage extends ConsumerWidget {
  static const String routeName = '/debug/badge-test';

  const BadgeTestPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final badgeState = ref.watch(appBadgeProvider);
    final currentCount = ref.watch(currentUnreadCountProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('App Badge Test'),
        backgroundColor: theme.colorScheme.background,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Badge Status Card
            ShadCard(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Badge Status',
                      style: theme.textTheme.h4,
                    ),
                    const SizedBox(height: 12),
                    _buildStatusRow('Supported', badgeState.isSupported),
                    _buildStatusRow('Initialized', badgeState.isInitialized),
                    _buildStatusRow('Updating', badgeState.isUpdating),
                    const SizedBox(height: 8),
                    Text(
                      'Current Badge Count: ${badgeState.currentCount}',
                      style: theme.textTheme.p,
                    ),
                    Text(
                      'Notification Count: $currentCount',
                      style: theme.textTheme.p,
                    ),
                    if (badgeState.error != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Error: ${badgeState.error}',
                        style: theme.textTheme.p.copyWith(
                          color: theme.colorScheme.destructive,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Platform Info Card
            ShadCard(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Platform Information',
                      style: theme.textTheme.h4,
                    ),
                    const SizedBox(height: 12),
                    ...ref.read(appBadgeProvider.notifier).getPlatformInfo().entries.map(
                      (entry) => Padding(
                        padding: const EdgeInsets.only(bottom: 4.0),
                        child: Text(
                          '${entry.key}: ${entry.value}',
                          style: theme.textTheme.p,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Test Controls
            Text(
              'Test Controls',
              style: theme.textTheme.h4,
            ),
            const SizedBox(height: 12),
            
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                ShadButton(
                  onPressed: () async {
                    await ref.read(appBadgeProvider.notifier).refreshBadge();
                    LoggerService.info('Badge refreshed manually');
                  },
                  child: const Text('Refresh Badge'),
                ),
                ShadButton(
                  onPressed: () async {
                    await ref.read(appBadgeProvider.notifier).clearBadge();
                    LoggerService.info('Badge cleared manually');
                  },
                  child: const Text('Clear Badge'),
                ),
                ShadButton(
                  onPressed: () async {
                    // Simulate notification count change
                    await ref.read(notificationCounterProvider.notifier).refresh();
                    LoggerService.info('Notification counter refreshed');
                  },
                  child: const Text('Refresh Notifications'),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Instructions
            ShadCard(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions',
                      style: theme.textTheme.h4,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '1. Check that "Supported" shows true on iOS/Android\n'
                      '2. The badge count should match notification count\n'
                      '3. Use "Refresh Badge" to manually sync\n'
                      '4. Use "Clear Badge" to remove the badge\n'
                      '5. Check your device home screen to see the badge\n'
                      '6. Badge should update automatically when notifications change',
                      style: theme.textTheme.p,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool status) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        children: [
          Text('$label: '),
          Icon(
            status ? Icons.check_circle : Icons.cancel,
            color: status ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(status ? 'Yes' : 'No'),
        ],
      ),
    );
  }
}
