import 'package:flutter/material.dart';

/// Responsive layout utilities for admin portal
/// Maintains consistency with existing portal breakpoints
class AdminResponsiveLayout {
  // Breakpoints consistent with existing portals
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;

  /// Check if current screen size is mobile
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  /// Check if current screen size is tablet
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }

  /// Check if current screen size is desktop
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isDesktop(context)) {
      return const EdgeInsets.all(24.0);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(20.0);
    } else {
      return const EdgeInsets.all(16.0);
    }
  }

  /// Get responsive margin based on screen size
  static EdgeInsets getResponsiveMargin(BuildContext context) {
    if (isDesktop(context)) {
      return const EdgeInsets.all(16.0);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(12.0);
    } else {
      return const EdgeInsets.all(8.0);
    }
  }

  /// Get responsive card padding
  static EdgeInsets getCardPadding(BuildContext context) {
    if (isDesktop(context)) {
      return const EdgeInsets.all(20.0);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(16.0);
    } else {
      return const EdgeInsets.all(12.0);
    }
  }

  /// Get responsive grid column count
  static int getGridColumnCount(BuildContext context) {
    if (isDesktop(context)) {
      return 4;
    } else if (isTablet(context)) {
      return 3;
    } else {
      return 2;
    }
  }

  /// Get responsive grid cross axis spacing
  static double getGridSpacing(BuildContext context) {
    if (isDesktop(context)) {
      return 20.0;
    } else if (isTablet(context)) {
      return 16.0;
    } else {
      return 12.0;
    }
  }

  /// Get responsive sidebar width
  static double getSidebarWidth(
    BuildContext context, {
    bool isCollapsed = false,
  }) {
    if (isCollapsed) {
      return 80.0;
    }

    if (isDesktop(context)) {
      return 280.0;
    } else if (isTablet(context)) {
      return 250.0;
    } else {
      return 0.0; // No sidebar on mobile
    }
  }

  /// Get responsive app bar height
  static double getAppBarHeight(BuildContext context) {
    if (isDesktop(context)) {
      return 64.0;
    } else {
      return 56.0;
    }
  }

  /// Get responsive font size multiplier
  static double getFontSizeMultiplier(BuildContext context) {
    if (isDesktop(context)) {
      return 1.0;
    } else if (isTablet(context)) {
      return 0.95;
    } else {
      return 0.9;
    }
  }

  /// Get responsive icon size
  static double getIconSize(BuildContext context, {double baseSize = 24.0}) {
    final multiplier = getFontSizeMultiplier(context);
    return baseSize * multiplier;
  }

  /// Get responsive button height
  static double getButtonHeight(BuildContext context) {
    if (isDesktop(context)) {
      return 40.0;
    } else if (isTablet(context)) {
      return 44.0;
    } else {
      return 48.0; // Larger touch targets on mobile
    }
  }

  /// Get responsive content max width
  static double getContentMaxWidth(BuildContext context) {
    if (isDesktop(context)) {
      return 1200.0;
    } else if (isTablet(context)) {
      return 800.0;
    } else {
      return double.infinity;
    }
  }

  /// Get responsive dialog width
  static double getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (isDesktop(context)) {
      return screenWidth * 0.4;
    } else if (isTablet(context)) {
      return screenWidth * 0.6;
    } else {
      return screenWidth * 0.9;
    }
  }

  /// Get responsive bottom sheet height
  static double getBottomSheetHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    if (isDesktop(context)) {
      return screenHeight * 0.6;
    } else if (isTablet(context)) {
      return screenHeight * 0.7;
    } else {
      return screenHeight * 0.8;
    }
  }

  /// Build responsive widget based on screen size
  static Widget responsive({
    required BuildContext context,
    required Widget mobile,
    Widget? tablet,
    Widget? desktop,
  }) {
    if (isDesktop(context) && desktop != null) {
      return desktop;
    } else if (isTablet(context) && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }

  /// Get responsive cross axis count for grid views
  static int getCrossAxisCount(
    BuildContext context, {
    int mobileCount = 1,
    int tabletCount = 2,
    int desktopCount = 3,
  }) {
    if (isDesktop(context)) {
      return desktopCount;
    } else if (isTablet(context)) {
      return tabletCount;
    } else {
      return mobileCount;
    }
  }

  /// Get responsive aspect ratio for cards
  static double getCardAspectRatio(BuildContext context) {
    if (isDesktop(context)) {
      return 16 / 9;
    } else if (isTablet(context)) {
      return 4 / 3;
    } else {
      return 3 / 2;
    }
  }

  /// Get responsive list tile height
  static double getListTileHeight(BuildContext context) {
    if (isDesktop(context)) {
      return 56.0;
    } else if (isTablet(context)) {
      return 60.0;
    } else {
      return 64.0;
    }
  }

  /// Get responsive border radius
  static BorderRadius getResponsiveBorderRadius(BuildContext context) {
    if (isDesktop(context)) {
      return BorderRadius.circular(8.0);
    } else if (isTablet(context)) {
      return BorderRadius.circular(10.0);
    } else {
      return BorderRadius.circular(12.0);
    }
  }

  /// Get responsive elevation
  static double getResponsiveElevation(BuildContext context) {
    if (isDesktop(context)) {
      return 2.0;
    } else if (isTablet(context)) {
      return 3.0;
    } else {
      return 4.0;
    }
  }
}
