import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_error_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_loading_state_provider.dart';

/// Main loading widget for admin operations
class AdminLoadingWidget extends StatelessWidget {
  final String? message;
  final AdminLoadingType type;
  final bool showProgress;
  final double? progress;

  const AdminLoadingWidget({
    super.key,
    this.message,
    this.type = AdminLoadingType.operation,
    this.showProgress = false,
    this.progress,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Center(
      child: ShadCard(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Loading indicator
              if (showProgress && progress != null) ...[
                SizedBox(
                  width: 60,
                  height: 60,
                  child: CircularProgressIndicator(
                    value: progress,
                    strokeWidth: 4,
                    backgroundColor: theme.colorScheme.muted,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${(progress! * 100).toInt()}%',
                  style: theme.textTheme.small.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ] else ...[
                const SizedBox(
                  width: 40,
                  height: 40,
                  child: CircularProgressIndicator(strokeWidth: 3),
                ),
              ],

              const SizedBox(height: 16),

              // Loading message
              Text(
                message ?? _getDefaultMessage(type),
                style: theme.textTheme.p,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getDefaultMessage(AdminLoadingType type) {
    switch (type) {
      case AdminLoadingType.initial:
        return 'Loading...';
      case AdminLoadingType.refresh:
        return 'Refreshing data...';
      case AdminLoadingType.pagination:
        return 'Loading more items...';
      case AdminLoadingType.operation:
        return 'Processing...';
      case AdminLoadingType.background:
        return 'Working in background...';
    }
  }
}

/// Skeleton loading widget for data lists
class AdminSkeletonWidget extends StatelessWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;

  const AdminSkeletonWidget({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Container(
      width: width,
      height: height ?? 16,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withOpacity(0.3),
        borderRadius: borderRadius ?? BorderRadius.circular(4),
      ),
    );
  }
}

/// Loading skeleton for dashboard KPI cards
class AdminDashboardSkeletonWidget extends StatelessWidget {
  final int cardCount;

  const AdminDashboardSkeletonWidget({super.key, this.cardCount = 4});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // KPI cards skeleton
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 4,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.4,
          children: List.generate(
            cardCount,
            (index) => _buildKPICardSkeleton(),
          ),
        ),
        const SizedBox(height: 32),

        // Activity feed skeleton
        _buildActivityFeedSkeleton(),
      ],
    );
  }

  Widget _buildKPICardSkeleton() {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const AdminSkeletonWidget(width: 28, height: 28),
                const AdminSkeletonWidget(width: 14, height: 14),
              ],
            ),
            const SizedBox(height: 12),
            const AdminSkeletonWidget(width: 70, height: 10),
            const SizedBox(height: 4),
            const AdminSkeletonWidget(width: 50, height: 20),
            const SizedBox(height: 2),
            const AdminSkeletonWidget(width: 80, height: 8),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityFeedSkeleton() {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const AdminSkeletonWidget(width: 120, height: 20),
            const SizedBox(height: 16),
            ...List.generate(5, (index) => _buildActivityItemSkeleton()),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItemSkeleton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          const AdminSkeletonWidget(
            width: 32,
            height: 32,
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const AdminSkeletonWidget(width: double.infinity, height: 14),
                const SizedBox(height: 4),
                const AdminSkeletonWidget(width: 150, height: 12),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Loading skeleton for user management table
class AdminUserTableSkeletonWidget extends StatelessWidget {
  final int rowCount;

  const AdminUserTableSkeletonWidget({super.key, this.rowCount = 10});

  @override
  Widget build(BuildContext context) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Table header skeleton
            Row(
              children: [
                const Expanded(child: AdminSkeletonWidget(height: 16)),
                const SizedBox(width: 16),
                const Expanded(child: AdminSkeletonWidget(height: 16)),
                const SizedBox(width: 16),
                const Expanded(child: AdminSkeletonWidget(height: 16)),
                const SizedBox(width: 16),
                const AdminSkeletonWidget(width: 80, height: 16),
              ],
            ),
            const SizedBox(height: 16),

            // Table rows skeleton
            ...List.generate(rowCount, (index) => _buildTableRowSkeleton()),
          ],
        ),
      ),
    );
  }

  Widget _buildTableRowSkeleton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          const AdminSkeletonWidget(
            width: 32,
            height: 32,
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ),
          const SizedBox(width: 12),
          const Expanded(child: AdminSkeletonWidget(height: 14)),
          const SizedBox(width: 16),
          const Expanded(child: AdminSkeletonWidget(height: 14)),
          const SizedBox(width: 16),
          const Expanded(child: AdminSkeletonWidget(height: 14)),
          const SizedBox(width: 16),
          const AdminSkeletonWidget(width: 60, height: 14),
        ],
      ),
    );
  }
}

/// Progress indicator widget for specific operations
class AdminProgressIndicator extends ConsumerWidget {
  final String operationId;
  final String? title;
  final bool showCancel;
  final VoidCallback? onCancel;

  const AdminProgressIndicator({
    super.key,
    required this.operationId,
    this.title,
    this.showCancel = false,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final operation = ref.watch(loadingOperationProvider(operationId));

    if (operation == null) return const SizedBox.shrink();

    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            if (title != null) ...[
              Text(
                title!,
                style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 12),
            ],

            // Progress indicator
            if (operation.progress != null) ...[
              LinearProgressIndicator(
                value: operation.progress,
                backgroundColor: theme.colorScheme.muted,
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${(operation.progress! * 100).toInt()}%',
                    style: theme.textTheme.small.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    operation.formattedDuration,
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ),
            ] else ...[
              const LinearProgressIndicator(),
              const SizedBox(height: 8),
              Text(
                operation.formattedDuration,
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
            ],

            // Operation message
            if (operation.message != null) ...[
              const SizedBox(height: 8),
              Text(
                operation.message!,
                style: theme.textTheme.small,
                textAlign: TextAlign.center,
              ),
            ],

            // Cancel button
            if (showCancel && onCancel != null) ...[
              const SizedBox(height: 12),
              ShadButton.outline(
                onPressed: onCancel,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(lucide.LucideIcons.x, size: 16),
                    const SizedBox(width: 8),
                    const Text('Cancel'),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Loading overlay widget
class AdminLoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? message;
  final double? progress;

  const AdminLoadingOverlay({
    super.key,
    required this.child,
    required this.isLoading,
    this.message,
    this.progress,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: AdminLoadingWidget(
              message: message,
              showProgress: progress != null,
              progress: progress,
            ),
          ),
      ],
    );
  }
}
