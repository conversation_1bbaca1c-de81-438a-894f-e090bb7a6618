import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/user_activity_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/utils/admin_responsive_layout.dart';

/// Activity timeline widget for displaying user activities
class ActivityTimelineWidget extends StatelessWidget {
  final List<UserActivityEvent> activities;
  final bool showUserInfo;
  final double? height;

  const ActivityTimelineWidget({
    super.key,
    required this.activities,
    this.showUserInfo = true,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(lucide.LucideIcons.activity, size: 20),
                const SizedBox(width: 8),
                Text('Activity Timeline', style: theme.textTheme.h4),
                const Spacer(),
                Text(
                  '${activities.length} activities',
                  style: theme.textTheme.muted,
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          SizedBox(
            height: height ?? 400,
            child: activities.isEmpty
                ? _buildEmptyState(theme)
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: activities.length,
                    itemBuilder: (context, index) {
                      final activity = activities[index];
                      return ActivityTimelineItem(
                        activity: activity,
                        showUserInfo: showUserInfo,
                        isFirst: index == 0,
                        isLast: index == activities.length - 1,
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ShadThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            lucide.LucideIcons.activity,
            size: 48,
            color: theme.colorScheme.muted,
          ),
          const SizedBox(height: 16),
          Text(
            'No activities found',
            style: theme.textTheme.h4,
          ),
          const SizedBox(height: 8),
          Text(
            'Activities will appear here when users interact with the platform',
            style: theme.textTheme.muted,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Individual timeline item widget
class ActivityTimelineItem extends StatelessWidget {
  final UserActivityEvent activity;
  final bool showUserInfo;
  final bool isFirst;
  final bool isLast;

  const ActivityTimelineItem({
    super.key,
    required this.activity,
    required this.showUserInfo,
    required this.isFirst,
    required this.isLast,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator
          SizedBox(
            width: 40,
            child: Column(
              children: [
                if (!isFirst)
                  Container(
                    width: 2,
                    height: 12,
                    color: theme.colorScheme.border,
                  ),
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getActivityColor(activity.type),
                    shape: BoxShape.circle,
                  ),
                ),
                if (!isLast)
                  Expanded(
                    child: Container(
                      width: 2,
                      color: theme.colorScheme.border,
                    ),
                  ),
              ],
            ),
          ),

          // Activity content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 12, bottom: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getActivityIcon(activity.type),
                        size: 16,
                        color: _getActivityColor(activity.type),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _getActivityTitle(activity),
                          style: theme.textTheme.p.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Text(
                        activity.formattedTimestamp,
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  if (showUserInfo && activity.userName != null) ...[
                    Text(
                      'User: ${activity.userName}',
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                    const SizedBox(height: 4),
                  ],
                  Text(
                    _getActivityDescription(activity),
                    style: theme.textTheme.small,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getActivityColor(UserActivityType type) {
    switch (type) {
      case UserActivityType.login:
        return Colors.green;
      case UserActivityType.logout:
        return Colors.orange;
      case UserActivityType.documentUpload:
        return Colors.blue;
      case UserActivityType.documentDownload:
        return Colors.indigo;
      case UserActivityType.claimSubmission:
        return Colors.purple;
      case UserActivityType.formSubmission:
        return Colors.teal;
      case UserActivityType.profileUpdate:
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  IconData _getActivityIcon(UserActivityType type) {
    switch (type) {
      case UserActivityType.login:
        return lucide.LucideIcons.logIn;
      case UserActivityType.logout:
        return lucide.LucideIcons.logOut;
      case UserActivityType.pageView:
        return lucide.LucideIcons.eye;
      case UserActivityType.documentUpload:
        return lucide.LucideIcons.upload;
      case UserActivityType.documentDownload:
        return lucide.LucideIcons.download;
      case UserActivityType.formSubmission:
        return lucide.LucideIcons.send;
      case UserActivityType.searchQuery:
        return lucide.LucideIcons.search;
      case UserActivityType.profileUpdate:
        return lucide.LucideIcons.user;
      case UserActivityType.claimSubmission:
        return lucide.LucideIcons.fileText;
      case UserActivityType.investmentAction:
        return lucide.LucideIcons.trendingUp;
      case UserActivityType.communicationSent:
        return lucide.LucideIcons.messageSquare;
      case UserActivityType.notificationRead:
        return lucide.LucideIcons.bell;
      default:
        return lucide.LucideIcons.activity;
    }
  }

  String _getActivityTitle(UserActivityEvent activity) {
    switch (activity.type) {
      case UserActivityType.login:
        return 'User Login';
      case UserActivityType.logout:
        return 'User Logout';
      case UserActivityType.pageView:
        return 'Page View';
      case UserActivityType.documentUpload:
        return 'Document Upload';
      case UserActivityType.documentDownload:
        return 'Document Download';
      case UserActivityType.formSubmission:
        return 'Form Submission';
      case UserActivityType.searchQuery:
        return 'Search Query';
      case UserActivityType.profileUpdate:
        return 'Profile Update';
      case UserActivityType.claimSubmission:
        return 'Claim Submission';
      case UserActivityType.investmentAction:
        return 'Investment Action';
      case UserActivityType.communicationSent:
        return 'Communication Sent';
      case UserActivityType.notificationRead:
        return 'Notification Read';
      default:
        return activity.type.displayName;
    }
  }

  String _getActivityDescription(UserActivityEvent activity) {
    if (activity.description.isNotEmpty) {
      return activity.description;
    }

    // Generate description from details
    final details = activity.details;
    switch (activity.type) {
      case UserActivityType.pageView:
        final page = details['page'] as String? ?? 'Unknown page';
        return 'Viewed $page';
      case UserActivityType.documentUpload:
        final fileName = details['file_name'] as String? ?? 'document';
        return 'Uploaded $fileName';
      case UserActivityType.searchQuery:
        final query = details['query'] as String? ?? 'Unknown query';
        return 'Searched for "$query"';
      case UserActivityType.formSubmission:
        final formType = details['form_type'] as String? ?? 'form';
        return 'Submitted $formType';
      default:
        return 'Performed ${activity.type.displayName.toLowerCase()}';
    }
  }
}

/// Activity heatmap widget for visualizing activity patterns
class ActivityHeatmapWidget extends ConsumerWidget {
  final List<UserActivityEvent> activities;
  final DateTime startDate;
  final DateTime endDate;
  final double? height;

  const ActivityHeatmapWidget({
    super.key,
    required this.activities,
    required this.startDate,
    required this.endDate,
    this.height,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final heatmapData = _generateHeatmapData();
    final theme = ShadTheme.of(context);

    return ShadCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(lucide.LucideIcons.calendar, size: 20),
                const SizedBox(width: 8),
                Text('Activity Heatmap', style: theme.textTheme.h4),
                const Spacer(),
                Text(
                  '${_formatDateRange(startDate, endDate)}',
                  style: theme.textTheme.muted,
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          SizedBox(
            height: height ?? 200,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildHeatmapGrid(heatmapData, theme),
            ),
          ),
        ],
      ),
    );
  }

  Map<DateTime, int> _generateHeatmapData() {
    final data = <DateTime, int>{};

    for (final activity in activities) {
      final day = DateTime(
        activity.timestamp.year,
        activity.timestamp.month,
        activity.timestamp.day,
      );
      data[day] = (data[day] ?? 0) + 1;
    }

    return data;
  }

  Widget _buildHeatmapGrid(Map<DateTime, int> data, ShadThemeData theme) {
    final maxValue = data.values.isEmpty ? 1 : data.values.reduce((a, b) => a > b ? a : b);
    final days = _generateDateRange(startDate, endDate);

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7, // Days of week
        crossAxisSpacing: 2,
        mainAxisSpacing: 2,
      ),
      itemCount: days.length,
      itemBuilder: (context, index) {
        final day = days[index];
        final value = data[day] ?? 0;
        final intensity = maxValue > 0 ? value / maxValue : 0.0;

        return Tooltip(
          message: '${_formatDate(day)}: $value activities',
          child: Container(
            decoration: BoxDecoration(
              color: _getHeatmapColor(intensity),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        );
      },
    );
  }

  List<DateTime> _generateDateRange(DateTime start, DateTime end) {
    final days = <DateTime>[];
    var current = DateTime(start.year, start.month, start.day);
    final endDay = DateTime(end.year, end.month, end.day);

    while (current.isBefore(endDay) || current.isAtSameMomentAs(endDay)) {
      days.add(current);
      current = current.add(const Duration(days: 1));
    }

    return days;
  }

  Color _getHeatmapColor(double intensity) {
    if (intensity == 0) return Colors.grey.shade200;
    return Colors.blue.withOpacity(0.2 + (intensity * 0.8));
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateRange(DateTime start, DateTime end) {
    return '${_formatDate(start)} - ${_formatDate(end)}';
  }
}

/// Activity type distribution widget
class ActivityTypeDistributionWidget extends StatelessWidget {
  final List<UserActivityEvent> activities;

  const ActivityTypeDistributionWidget({
    super.key,
    required this.activities,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final distribution = _calculateTypeDistribution();

    return ShadCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(lucide.LucideIcons.pieChart, size: 20),
                const SizedBox(width: 8),
                Text('Activity Types', style: theme.textTheme.h4),
              ],
            ),
          ),
          const Divider(height: 1),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: distribution.entries.map((entry) {
                final percentage = activities.isNotEmpty 
                    ? (entry.value / activities.length * 100)
                    : 0.0;
                
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: _getActivityColor(entry.key),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          entry.key.displayName,
                          style: theme.textTheme.small,
                        ),
                      ),
                      Text(
                        '${entry.value} (${percentage.toStringAsFixed(1)}%)',
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Map<UserActivityType, int> _calculateTypeDistribution() {
    final distribution = <UserActivityType, int>{};
    
    for (final activity in activities) {
      distribution[activity.type] = (distribution[activity.type] ?? 0) + 1;
    }
    
    // Sort by count descending
    final sortedEntries = distribution.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return Map.fromEntries(sortedEntries);
  }

  Color _getActivityColor(UserActivityType type) {
    switch (type) {
      case UserActivityType.login:
        return Colors.green;
      case UserActivityType.logout:
        return Colors.orange;
      case UserActivityType.pageView:
        return Colors.blue;
      case UserActivityType.documentUpload:
        return Colors.purple;
      case UserActivityType.documentDownload:
        return Colors.indigo;
      case UserActivityType.claimSubmission:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
