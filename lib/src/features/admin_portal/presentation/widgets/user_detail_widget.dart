import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_user_management_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/user_management_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_auth_provider.dart';

/// User detail widget for admin management
class UserDetailWidget extends ConsumerStatefulWidget {
  final String userId;
  final VoidCallback? onUserUpdated;

  const UserDetailWidget({super.key, required this.userId, this.onUserUpdated});

  @override
  ConsumerState<UserDetailWidget> createState() => _UserDetailWidgetState();
}

class _UserDetailWidgetState extends ConsumerState<UserDetailWidget> {
  UserStatus? _selectedStatus;
  bool _isUpdating = false;

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final userAsync = ref.watch(userDetailProvider(widget.userId));
    final activityLogsAsync = ref.watch(
      userActivityLogsProvider(widget.userId),
    );

    return Dialog(
      child: Container(
        width: 700,
        height: 600,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Text('User Details', style: theme.textTheme.h3),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Content
            Expanded(
              child: userAsync.when(
                data:
                    (user) =>
                        _buildUserDetailContent(theme, user, activityLogsAsync),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => _buildErrorContent(theme, error),
              ),
            ),

            // Actions
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ShadButton.outline(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                ),
                const SizedBox(width: 8),
                if (userAsync.hasValue && userAsync.value!.canBeEdited)
                  ShadButton(
                    onPressed:
                        _isUpdating
                            ? null
                            : () => _updateUserStatus(userAsync.value!),
                    child:
                        _isUpdating
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Text('Update Status'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserDetailContent(
    ShadThemeData theme,
    AdminUserModel user,
    AsyncValue<List<Map<String, dynamic>>> activityLogsAsync,
  ) {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          // User header
          _buildUserHeader(theme, user),
          const SizedBox(height: 16),

          // Tab bar
          TabBar(
            tabs: const [
              Tab(text: 'Profile'),
              Tab(text: 'Activity'),
              Tab(text: 'Settings'),
            ],
            labelColor: theme.colorScheme.primary,
            unselectedLabelColor: theme.colorScheme.mutedForeground,
            indicatorColor: theme.colorScheme.primary,
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              children: [
                _buildProfileTab(theme, user),
                _buildActivityTab(theme, activityLogsAsync),
                _buildSettingsTab(theme, user),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserHeader(ShadThemeData theme, AdminUserModel user) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // Avatar
          CircleAvatar(
            radius: 32,
            backgroundColor: theme.colorScheme.primary,
            backgroundImage:
                user.avatarUrl != null ? NetworkImage(user.avatarUrl!) : null,
            child:
                user.avatarUrl == null
                    ? Text(
                      user.initials,
                      style: TextStyle(
                        color: theme.colorScheme.primaryForeground,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                    : null,
          ),
          const SizedBox(width: 16),

          // User info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.displayName,
                  style: theme.textTheme.h3.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(user.email, style: theme.textTheme.muted),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildStatusChip(theme, user.status),
                    const SizedBox(width: 8),
                    _buildUserTypeChip(theme, user.userType),
                    if (user.verified) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              lucide.LucideIcons.checkCircle,
                              size: 12,
                              color: Colors.green,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Verified',
                              style: TextStyle(
                                color: Colors.green,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // Status dropdown
          if (user.canBeEdited)
            SizedBox(
              width: 150,
              child: ShadSelect<UserStatus>(
                placeholder: const Text('Change Status'),
                options:
                    UserStatus.values
                        .where((status) => status != UserStatus.all)
                        .map(
                          (status) => ShadOption(
                            value: status,
                            child: Text(status.displayName),
                          ),
                        )
                        .toList(),
                selectedOptionBuilder:
                    (context, value) => Text(value.displayName),
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value;
                  });
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(ShadThemeData theme, UserStatus status) {
    final color = Color(
      int.parse(
        status == UserStatus.active
            ? '0xFF10b981'
            : status == UserStatus.pending
            ? '0xFFf59e0b'
            : '0xFF6b7280',
      ),
    );

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildUserTypeChip(ShadThemeData theme, UserType userType) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: theme.colorScheme.secondary,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        userType.displayName,
        style: TextStyle(
          color: theme.colorScheme.secondaryForeground,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildProfileTab(ShadThemeData theme, AdminUserModel user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoSection('Basic Information', [
            _buildInfoRow('Full Name', user.displayName),
            _buildInfoRow('Email', user.email),
            _buildInfoRow('Phone', user.phoneNumber ?? 'Not provided'),
            _buildInfoRow('User Type', user.userType.displayName),
            _buildInfoRow('Status', user.status.displayName),
            _buildInfoRow('Verified', user.verified ? 'Yes' : 'No'),
          ]),

          const SizedBox(height: 24),

          _buildInfoSection('Account Details', [
            _buildInfoRow('Created', _formatDate(user.created)),
            _buildInfoRow('Last Updated', _formatDate(user.updated)),
            _buildInfoRow(
              'Last Login',
              user.lastLogin != null ? _formatDate(user.lastLogin!) : 'Never',
            ),
            _buildInfoRow('Opt Out', user.optOut ? 'Yes' : 'No'),
          ]),

          if (user.userType == UserType.solicitor && user.firmName != null) ...[
            const SizedBox(height: 24),
            _buildInfoSection('Solicitor Details', [
              _buildInfoRow('Firm Name', user.firmName!),
            ]),
          ],

          if (user.userType == UserType.coFunder &&
              user.accessLevel != null) ...[
            const SizedBox(height: 24),
            _buildInfoSection('Co-Funder Details', [
              _buildInfoRow('Access Level', user.accessLevel!),
            ]),
          ],
        ],
      ),
    );
  }

  Widget _buildActivityTab(
    ShadThemeData theme,
    AsyncValue<List<Map<String, dynamic>>> activityLogsAsync,
  ) {
    return activityLogsAsync.when(
      data:
          (logs) =>
              logs.isEmpty
                  ? const Center(child: Text('No activity logs found'))
                  : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: logs.length,
                    itemBuilder: (context, index) {
                      final log = logs[index];
                      return _buildActivityItem(theme, log);
                    },
                  ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) =>
              Center(child: Text('Error loading activity: $error')),
    );
  }

  Widget _buildSettingsTab(ShadThemeData theme, AdminUserModel user) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('User Settings', style: theme.textTheme.h4),
          const SizedBox(height: 16),

          // Settings content would go here
          const Text('User settings management will be implemented here.'),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: ShadTheme.of(
            context,
          ).textTheme.h4.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    final theme = ShadTheme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: theme.textTheme.small.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.mutedForeground,
              ),
            ),
          ),
          Expanded(child: Text(value, style: theme.textTheme.small)),
        ],
      ),
    );
  }

  Widget _buildActivityItem(ShadThemeData theme, Map<String, dynamic> log) {
    final action = log['action'] as String? ?? 'Unknown';
    final timestamp = log['timestamp'] as String?;
    final details = log['details'] as Map<String, dynamic>? ?? {};

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.border),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                lucide.LucideIcons.activity,
                size: 16,
                color: theme.colorScheme.mutedForeground,
              ),
              const SizedBox(width: 8),
              Text(
                action.replaceAll('_', ' ').toUpperCase(),
                style: theme.textTheme.small.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (timestamp != null)
                Text(
                  _formatDate(DateTime.tryParse(timestamp) ?? DateTime.now()),
                  style: theme.textTheme.muted.copyWith(fontSize: 11),
                ),
            ],
          ),
          if (details.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              details.toString(),
              style: theme.textTheme.muted.copyWith(fontSize: 11),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorContent(ShadThemeData theme, Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            lucide.LucideIcons.alertCircle,
            size: 48,
            color: theme.colorScheme.destructive,
          ),
          const SizedBox(height: 16),
          Text('Error loading user details', style: theme.textTheme.h4),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: theme.textTheme.muted,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _updateUserStatus(AdminUserModel user) async {
    if (_selectedStatus == null || _selectedStatus == user.status) return;

    final adminUser = ref.read(adminAuthProvider).adminUser;
    if (adminUser == null) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      final success = await ref
          .read(userManagementProvider.notifier)
          .updateUserStatus(
            user.id,
            _selectedStatus!,
            'Status updated by admin',
            adminUser.id,
          );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'User status updated to ${_selectedStatus!.displayName}',
            ),
            backgroundColor: Colors.green,
          ),
        );

        widget.onUserUpdated?.call();
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update user status: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
