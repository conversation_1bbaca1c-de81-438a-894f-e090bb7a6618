import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/dashboard_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/realtime_update_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/widgets/connection_status_widget.dart';

/// Live activity feed widget showing real-time events
class LiveActivityFeedWidget extends ConsumerStatefulWidget {
  final int maxItems;
  final bool showConnectionStatus;
  final VoidCallback? onViewAll;

  const LiveActivityFeedWidget({
    super.key,
    this.maxItems = 10,
    this.showConnectionStatus = true,
    this.onViewAll,
  });

  @override
  ConsumerState<LiveActivityFeedWidget> createState() => _LiveActivityFeedWidgetState();
}

class _LiveActivityFeedWidgetState extends ConsumerState<LiveActivityFeedWidget> {
  RealtimeEventType? _selectedFilter;
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final realtimeState = ref.watch(realtimeUpdateProvider);
    final activities = _getFilteredActivities(realtimeState.recentActivities);

    return ShadCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme, realtimeState.connectionStatus),
          const SizedBox(height: 16),
          _buildFilterRow(theme),
          const SizedBox(height: 16),
          _buildActivityList(theme, activities),
        ],
      ),
    );
  }

  Widget _buildHeader(ShadThemeData theme, ConnectionStatus connectionStatus) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            lucide.LucideIcons.activity,
            size: 20,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            'Live Activity',
            style: theme.textTheme.h4.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          if (widget.showConnectionStatus) ...[
            ConnectionStatusWidget(status: connectionStatus),
            const SizedBox(width: 8),
          ],
          if (widget.onViewAll != null)
            ShadIconButton.ghost(
              icon: const Icon(lucide.LucideIcons.externalLink, size: 16),
              onPressed: widget.onViewAll,
            ),
          ShadIconButton.ghost(
            icon: const Icon(lucide.LucideIcons.refreshCw, size: 16),
            onPressed: () {
              ref.read(realtimeUpdateProvider.notifier).reconnect();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFilterRow(ShadThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildFilterChip(theme, null, 'All'),
            const SizedBox(width: 8),
            ...RealtimeEventType.values.map((type) => 
              Padding(
                padding: const EdgeInsets.only(right: 8),
                child: _buildFilterChip(theme, type, type.displayName),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(ShadThemeData theme, RealtimeEventType? type, String label) {
    final isSelected = _selectedFilter == type;
    
    return ShadButton.ghost(
      size: ShadButtonSize.sm,
      onPressed: () {
        setState(() {
          _selectedFilter = isSelected ? null : type;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primary.withOpacity(0.1) : null,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : theme.colorScheme.border,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isSelected ? theme.colorScheme.primary : theme.colorScheme.foreground,
          ),
        ),
      ),
    );
  }

  Widget _buildActivityList(ShadThemeData theme, List<RealtimeEvent> activities) {
    if (activities.isEmpty) {
      return Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                lucide.LucideIcons.activity,
                size: 48,
                color: theme.colorScheme.mutedForeground,
              ),
              const SizedBox(height: 16),
              Text(
                _selectedFilter != null 
                    ? 'No ${_selectedFilter!.displayName.toLowerCase()} activities'
                    : 'No recent activity',
                style: theme.textTheme.muted,
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      height: 300,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        controller: _scrollController,
        itemCount: activities.length,
        itemBuilder: (context, index) {
          final activity = activities[index];
          return ActivityListTile(
            activity: activity,
            isLast: index == activities.length - 1,
          );
        },
      ),
    );
  }

  List<RealtimeEvent> _getFilteredActivities(List<RealtimeEvent> activities) {
    var filtered = activities;
    
    if (_selectedFilter != null) {
      filtered = activities.where((activity) => activity.type == _selectedFilter).toList();
    }
    
    return filtered.take(widget.maxItems).toList();
  }
}

/// Individual activity list tile
class ActivityListTile extends StatelessWidget {
  final RealtimeEvent activity;
  final bool isLast;

  const ActivityListTile({
    super.key,
    required this.activity,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Container(
      margin: EdgeInsets.only(bottom: isLast ? 16 : 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildActivityIcon(theme),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.description,
                  style: theme.textTheme.p.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      activity.type.displayName,
                      style: theme.textTheme.small.copyWith(
                        color: _getEventTypeColor(activity.type),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '•',
                      style: TextStyle(color: theme.colorScheme.mutedForeground),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      activity.formattedTimestamp,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityIcon(ShadThemeData theme) {
    final iconData = _getEventTypeIcon(activity.type);
    final iconColor = _getEventTypeColor(activity.type);

    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Icon(
        iconData,
        size: 16,
        color: iconColor,
      ),
    );
  }

  IconData _getEventTypeIcon(RealtimeEventType type) {
    switch (type) {
      case RealtimeEventType.userRegistration:
        return lucide.LucideIcons.userPlus;
      case RealtimeEventType.userStatusChange:
        return lucide.LucideIcons.userCheck;
      case RealtimeEventType.claimSubmission:
        return lucide.LucideIcons.fileText;
      case RealtimeEventType.claimStatusUpdate:
        return lucide.LucideIcons.fileEdit;
      case RealtimeEventType.systemAlert:
        return lucide.LucideIcons.alertTriangle;
      case RealtimeEventType.securityEvent:
        return lucide.LucideIcons.shield;
      case RealtimeEventType.adminAction:
        return lucide.LucideIcons.settings;
      case RealtimeEventType.bulkOperation:
        return lucide.LucideIcons.layers;
      case RealtimeEventType.notification:
        return lucide.LucideIcons.bell;
    }
  }

  Color _getEventTypeColor(RealtimeEventType type) {
    switch (type) {
      case RealtimeEventType.userRegistration:
        return Colors.green;
      case RealtimeEventType.userStatusChange:
        return Colors.blue;
      case RealtimeEventType.claimSubmission:
        return Colors.purple;
      case RealtimeEventType.claimStatusUpdate:
        return Colors.orange;
      case RealtimeEventType.systemAlert:
        return Colors.amber;
      case RealtimeEventType.securityEvent:
        return Colors.red;
      case RealtimeEventType.adminAction:
        return Colors.indigo;
      case RealtimeEventType.bulkOperation:
        return Colors.teal;
      case RealtimeEventType.notification:
        return Colors.cyan;
    }
  }
}
