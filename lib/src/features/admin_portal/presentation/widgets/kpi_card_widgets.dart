import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/dashboard_models.dart';

class KPICard extends StatelessWidget {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color iconColor;
  final double? trend;
  final bool isLoading;
  final VoidCallback? onTap;

  const KPICard({
    super.key,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.iconColor,
    this.trend,
    this.isLoading = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: ShadCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.muted.copyWith(fontSize: 14),
                  ),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: iconColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, size: 18, color: iconColor),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              isLoading
                  ? const SizedBox(
                    height: 24,
                    child: Center(child: LinearProgressIndicator()),
                  )
                  : Text(
                    value,
                    style: theme.textTheme.h3.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              const SizedBox(height: 8),
              Row(
                children: [
                  if (trend != null) ...[
                    Icon(
                      trend! > 0
                          ? lucide.LucideIcons.trendingUp
                          : trend! < 0
                          ? lucide.LucideIcons.trendingDown
                          : lucide.LucideIcons.minus,
                      size: 14,
                      color:
                          trend! > 0
                              ? Colors.green
                              : trend! < 0
                              ? Colors.red
                              : Colors.grey,
                    ),
                    const SizedBox(width: 4),
                  ],
                  Text(
                    subtitle,
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.muted,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Align(
                alignment: Alignment.bottomRight,
                child: Icon(
                  lucide.LucideIcons.externalLink,
                  size: 14,
                  color: theme.colorScheme.muted,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class RecentActivityCard extends StatelessWidget {
  final List<RecentActivity>? activities;
  final bool isLoading;
  final VoidCallback? onViewAll;

  const RecentActivityCard({
    super.key,
    this.activities,
    this.isLoading = false,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Activity',
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (onViewAll != null)
                  ShadButton.ghost(
                    onPressed: onViewAll,
                    child: Row(
                      children: [
                        Text(
                          'View All',
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          lucide.LucideIcons.arrowRight,
                          size: 14,
                          color: theme.colorScheme.primary,
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          if (isLoading)
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: LinearProgressIndicator(),
            ),
          if (activities == null || activities!.isEmpty)
            const Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: Text('No recent activity')),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              itemCount: activities!.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final activity = activities![index];
                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getActivityColor(
                        activity.type,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getActivityIcon(activity.type),
                      size: 18,
                      color: _getActivityColor(activity.type),
                    ),
                  ),
                  title: Text(activity.description),
                  subtitle: Text(
                    activity.formattedTimestamp,
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.muted,
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Color _getActivityColor(String type) {
    switch (type) {
      case 'user_registration':
        return Colors.blue;
      case 'claim_submission':
        return Colors.green;
      case 'user_status_change':
        return Colors.purple;
      case 'claim_status_update':
        return Colors.orange;
      case 'system_alert':
        return Colors.red;
      case 'security_event':
        return Colors.amber;
      case 'admin_action':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'user_registration':
        return lucide.LucideIcons.userPlus;
      case 'claim_submission':
        return lucide.LucideIcons.fileText;
      case 'user_status_change':
        return lucide.LucideIcons.user;
      case 'claim_status_update':
        return lucide.LucideIcons.fileEdit;
      case 'system_alert':
        return lucide.LucideIcons.alertCircle;
      case 'security_event':
        return lucide.LucideIcons.shield;
      case 'admin_action':
        return lucide.LucideIcons.settings;
      default:
        return lucide.LucideIcons.activity;
    }
  }
}

class SystemHealthCard extends StatelessWidget {
  final SystemHealth systemHealth;
  final bool isLoading;
  final VoidCallback? onTap;

  const SystemHealthCard({
    super.key,
    required this.systemHealth,
    this.isLoading = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: ShadCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getStatusColor(
                        systemHealth.status,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getStatusIcon(systemHealth.status),
                      size: 18,
                      color: _getStatusColor(systemHealth.status),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'System Health',
                        style: theme.textTheme.h4.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        systemHealth.description,
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.muted,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(
                        systemHealth.status,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      _getStatusText(systemHealth.status),
                      style: TextStyle(
                        color: _getStatusColor(systemHealth.status),
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              if (isLoading)
                const LinearProgressIndicator()
              else
                Column(
                  children: [
                    _buildUsageRow(
                      context,
                      'CPU',
                      systemHealth.cpuUsage,
                      theme,
                    ),
                    const SizedBox(height: 8),
                    _buildUsageRow(
                      context,
                      'Memory',
                      systemHealth.memoryUsage,
                      theme,
                    ),
                    const SizedBox(height: 8),
                    _buildUsageRow(
                      context,
                      'Disk',
                      systemHealth.diskUsage,
                      theme,
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUsageRow(
    BuildContext context,
    String label,
    double percentage,
    ShadThemeData theme,
  ) {
    return Row(
      children: [
        SizedBox(width: 60, child: Text(label, style: theme.textTheme.small)),
        Expanded(
          child: LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: theme.colorScheme.muted.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              _getUsageColor(percentage),
            ),
          ),
        ),
        const SizedBox(width: 8),
        SizedBox(
          width: 50,
          child: Text(
            '${percentage.toStringAsFixed(1)}%',
            style: theme.textTheme.small.copyWith(fontWeight: FontWeight.w500),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(SystemHealthStatus status) {
    switch (status) {
      case SystemHealthStatus.healthy:
        return Colors.green;
      case SystemHealthStatus.warning:
        return Colors.orange;
      case SystemHealthStatus.critical:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(SystemHealthStatus status) {
    switch (status) {
      case SystemHealthStatus.healthy:
        return lucide.LucideIcons.activity;
      case SystemHealthStatus.warning:
        return lucide.LucideIcons.alertTriangle;
      case SystemHealthStatus.critical:
        return lucide.LucideIcons.alertOctagon;
      default:
        return lucide.LucideIcons.helpCircle;
    }
  }

  String _getStatusText(SystemHealthStatus status) {
    switch (status) {
      case SystemHealthStatus.healthy:
        return 'Healthy';
      case SystemHealthStatus.warning:
        return 'Warning';
      case SystemHealthStatus.critical:
        return 'Critical';
      default:
        return 'Unknown';
    }
  }

  Color _getUsageColor(double percentage) {
    if (percentage < 60) {
      return Colors.green;
    } else if (percentage < 80) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
