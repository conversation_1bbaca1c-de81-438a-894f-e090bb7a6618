import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_audit_models.dart';

/// Audit log entry widget
class AuditLogEntryWidget extends StatelessWidget {
  final AdminAuditEntry entry;
  final VoidCallback? onTap;

  const AuditLogEntryWidget({super.key, required this.entry, this.onTap});

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Severity icon
              _buildSeverityIcon(),
              const SizedBox(width: 12),

              // Main content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Action and admin
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            entry.action,
                            style: theme.textTheme.small.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        _buildActionTypeChip(theme),
                      ],
                    ),
                    const SizedBox(height: 4),

                    // Admin info
                    Text(
                      'Admin: ${entry.adminName}',
                      style: theme.textTheme.muted.copyWith(fontSize: 12),
                    ),

                    // Target user info
                    if (entry.targetUserName != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        'Target: ${entry.targetUserName}',
                        style: theme.textTheme.muted.copyWith(fontSize: 12),
                      ),
                    ],

                    // Timestamp
                    const SizedBox(height: 4),
                    Text(
                      entry.formattedTimestamp,
                      style: theme.textTheme.muted.copyWith(fontSize: 11),
                    ),
                  ],
                ),
              ),

              // Chevron
              Icon(
                lucide.LucideIcons.chevronRight,
                size: 16,
                color: theme.colorScheme.mutedForeground,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSeverityIcon() {
    IconData icon;
    Color color;

    switch (entry.severity) {
      case AdminActionSeverity.info:
        icon = lucide.LucideIcons.info;
        color = Colors.blue;
        break;
      case AdminActionSeverity.warning:
        icon = lucide.LucideIcons.alertTriangle;
        color = Colors.orange;
        break;
      case AdminActionSeverity.critical:
        icon = lucide.LucideIcons.alertCircle;
        color = Colors.red;
        break;
      case AdminActionSeverity.security:
        icon = lucide.LucideIcons.shield;
        color = Colors.purple;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(icon, size: 16, color: color),
    );
  }

  Widget _buildActionTypeChip(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: theme.colorScheme.secondary,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        entry.actionType.displayName,
        style: TextStyle(
          color: theme.colorScheme.secondaryForeground,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

/// Audit log detail dialog
class AuditLogDetailDialog extends StatelessWidget {
  final AdminAuditEntry entry;

  const AuditLogDetailDialog({super.key, required this.entry});

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Dialog(
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Text('Audit Log Details', style: theme.textTheme.h3),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Content
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailSection(context, 'Basic Information', [
                      _buildDetailRow(context, 'Action', entry.action),
                      _buildDetailRow(
                        context,
                        'Action Type',
                        entry.actionType.displayName,
                      ),
                      _buildDetailRow(
                        context,
                        'Severity',
                        entry.severity.displayName,
                      ),
                      _buildDetailRow(
                        context,
                        'Timestamp',
                        entry.formattedTimestamp,
                      ),
                    ]),

                    const SizedBox(height: 24),

                    _buildDetailSection(context, 'Admin Information', [
                      _buildDetailRow(context, 'Admin Name', entry.adminName),
                      _buildDetailRow(context, 'Admin Email', entry.adminEmail),
                      _buildDetailRow(context, 'Admin ID', entry.adminId),
                    ]),

                    if (entry.targetUserName != null) ...[
                      const SizedBox(height: 24),
                      _buildDetailSection(context, 'Target Information', [
                        _buildDetailRow(
                          context,
                          'Target Name',
                          entry.targetUserName!,
                        ),
                        if (entry.targetUserEmail != null)
                          _buildDetailRow(
                            context,
                            'Target Email',
                            entry.targetUserEmail!,
                          ),
                        if (entry.targetUserId != null)
                          _buildDetailRow(
                            context,
                            'Target ID',
                            entry.targetUserId!,
                          ),
                      ]),
                    ],

                    if (entry.entityType != null) ...[
                      const SizedBox(height: 24),
                      _buildDetailSection(context, 'Entity Information', [
                        _buildDetailRow(
                          context,
                          'Entity Type',
                          entry.entityType!,
                        ),
                        if (entry.entityId != null)
                          _buildDetailRow(
                            context,
                            'Entity ID',
                            entry.entityId!,
                          ),
                      ]),
                    ],

                    const SizedBox(height: 24),

                    _buildDetailSection(context, 'Technical Information', [
                      _buildDetailRow(context, 'IP Address', entry.ipAddress),
                      _buildDetailRow(context, 'User Agent', entry.userAgent),
                      _buildDetailRow(context, 'Entry ID', entry.id),
                    ]),

                    if (entry.details.isNotEmpty) ...[
                      const SizedBox(height: 24),
                      _buildDetailSection(context, 'Additional Details', [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.muted.withValues(
                              alpha: 0.3,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            entry.details.toString(),
                            style: theme.textTheme.muted.copyWith(fontSize: 12),
                          ),
                        ),
                      ]),
                    ],
                  ],
                ),
              ),
            ),

            // Actions
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ShadButton.outline(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailSection(
    BuildContext context,
    String title,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: ShadTheme.of(
            context,
          ).textTheme.h4.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value) {
    final theme = ShadTheme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: theme.textTheme.small.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.mutedForeground,
              ),
            ),
          ),
          Expanded(child: Text(value, style: theme.textTheme.small)),
        ],
      ),
    );
  }
}

/// Audit log loading widget
class AuditLogLoadingWidget extends StatelessWidget {
  const AuditLogLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading audit logs...'),
        ],
      ),
    );
  }
}

/// Audit log error widget
class AuditLogErrorWidget extends StatelessWidget {
  final Object error;

  const AuditLogErrorWidget({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            lucide.LucideIcons.alertCircle,
            size: 48,
            color: theme.colorScheme.destructive,
          ),
          const SizedBox(height: 16),
          Text('Error loading audit logs', style: theme.textTheme.h4),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: theme.textTheme.muted,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Audit statistics card widget
class AuditStatisticsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;

  const AuditStatisticsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, size: 16, color: color),
                  ),
                  const Spacer(),
                  if (onTap != null)
                    Icon(
                      lucide.LucideIcons.externalLink,
                      size: 14,
                      color: theme.colorScheme.mutedForeground,
                    ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: theme.textTheme.h3.copyWith(fontWeight: FontWeight.bold),
              ),
              Text(title, style: theme.textTheme.muted.copyWith(fontSize: 12)),
            ],
          ),
        ),
      ),
    );
  }
}

/// Security alert indicator
class SecurityAlertIndicator extends StatelessWidget {
  final int alertCount;
  final VoidCallback? onTap;

  const SecurityAlertIndicator({
    super.key,
    required this.alertCount,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (alertCount == 0) return const SizedBox.shrink();

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              lucide.LucideIcons.alertTriangle,
              size: 12,
              color: Colors.white,
            ),
            const SizedBox(width: 4),
            Text(
              '$alertCount Security Alert${alertCount > 1 ? 's' : ''}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
