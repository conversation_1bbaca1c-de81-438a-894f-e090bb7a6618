import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/dashboard_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/realtime_update_provider.dart';

/// Widget to display real-time connection status
class ConnectionStatusWidget extends ConsumerWidget {
  final ConnectionStatus status;
  final bool showText;
  final bool showTooltip;

  const ConnectionStatusWidget({
    super.key,
    required this.status,
    this.showText = false,
    this.showTooltip = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final statusInfo = _getStatusInfo(status);

    Widget statusWidget = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildStatusIcon(statusInfo),
        if (showText) ...[
          const SizedBox(width: 8),
          Text(
            statusInfo.label,
            style: theme.textTheme.small.copyWith(
              color: statusInfo.color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );

    if (showTooltip) {
      statusWidget = Tooltip(
        message: statusInfo.tooltip,
        child: statusWidget,
      );
    }

    return statusWidget;
  }

  Widget _buildStatusIcon(_StatusInfo statusInfo) {
    if (status == ConnectionStatus.connecting) {
      return SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(statusInfo.color),
        ),
      );
    }

    return Icon(
      statusInfo.icon,
      size: 16,
      color: statusInfo.color,
    );
  }

  _StatusInfo _getStatusInfo(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        return _StatusInfo(
          icon: lucide.LucideIcons.wifi,
          color: Colors.green,
          label: 'Connected',
          tooltip: 'Real-time updates are active',
        );
      case ConnectionStatus.connecting:
        return _StatusInfo(
          icon: lucide.LucideIcons.loader,
          color: Colors.orange,
          label: 'Connecting',
          tooltip: 'Establishing real-time connection...',
        );
      case ConnectionStatus.disconnected:
        return _StatusInfo(
          icon: lucide.LucideIcons.wifiOff,
          color: Colors.grey,
          label: 'Disconnected',
          tooltip: 'Real-time updates unavailable - using cached data',
        );
      case ConnectionStatus.error:
        return _StatusInfo(
          icon: lucide.LucideIcons.alertCircle,
          color: Colors.red,
          label: 'Error',
          tooltip: 'Connection error - attempting to reconnect',
        );
    }
  }
}

/// Detailed connection status card
class ConnectionStatusCard extends ConsumerWidget {
  const ConnectionStatusCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final realtimeState = ref.watch(realtimeUpdateProvider);
    final notifier = ref.read(realtimeUpdateProvider.notifier);

    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  lucide.LucideIcons.activity,
                  size: 20,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Real-time Status',
                  style: theme.textTheme.h4.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                ConnectionStatusWidget(
                  status: realtimeState.connectionStatus,
                  showText: true,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatusDetails(theme, realtimeState),
            if (realtimeState.hasError) ...[
              const SizedBox(height: 16),
              _buildErrorSection(theme, realtimeState, notifier),
            ],
            const SizedBox(height: 16),
            _buildActionButtons(theme, realtimeState, notifier),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusDetails(ShadThemeData theme, RealtimeUpdateState state) {
    return Column(
      children: [
        _buildDetailRow(
          theme,
          'Status',
          state.connectionStatus.displayName,
          _getStatusColor(state.connectionStatus),
        ),
        const SizedBox(height: 8),
        if (state.lastConnected != null)
          _buildDetailRow(
            theme,
            'Last Connected',
            _formatDateTime(state.lastConnected!),
            theme.colorScheme.mutedForeground,
          ),
        const SizedBox(height: 8),
        _buildDetailRow(
          theme,
          'Recent Events',
          '${state.recentActivities.length}',
          theme.colorScheme.mutedForeground,
        ),
        if (state.reconnectAttempts > 0) ...[
          const SizedBox(height: 8),
          _buildDetailRow(
            theme,
            'Reconnect Attempts',
            '${state.reconnectAttempts}',
            Colors.orange,
          ),
        ],
      ],
    );
  }

  Widget _buildDetailRow(ShadThemeData theme, String label, String value, Color valueColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: theme.textTheme.small.copyWith(
            color: theme.colorScheme.mutedForeground,
          ),
        ),
        Text(
          value,
          style: theme.textTheme.small.copyWith(
            color: valueColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorSection(ShadThemeData theme, RealtimeUpdateState state, RealtimeUpdateNotifier notifier) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                lucide.LucideIcons.alertCircle,
                size: 16,
                color: Colors.red,
              ),
              const SizedBox(width: 8),
              Text(
                'Connection Error',
                style: theme.textTheme.small.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            state.lastError ?? 'Unknown error',
            style: theme.textTheme.small.copyWith(
              color: Colors.red.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ShadThemeData theme, RealtimeUpdateState state, RealtimeUpdateNotifier notifier) {
    return Row(
      children: [
        Expanded(
          child: ShadButton.outline(
            onPressed: state.isConnecting ? null : () => notifier.reconnect(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  lucide.LucideIcons.refreshCw,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(state.isConnecting ? 'Connecting...' : 'Reconnect'),
              ],
            ),
          ),
        ),
        if (state.lastError != null) ...[
          const SizedBox(width: 12),
          ShadIconButton.ghost(
            icon: const Icon(lucide.LucideIcons.x, size: 16),
            onPressed: () => notifier.clearError(),
          ),
        ],
      ],
    );
  }

  Color _getStatusColor(ConnectionStatus status) {
    switch (status) {
      case ConnectionStatus.connected:
        return Colors.green;
      case ConnectionStatus.connecting:
        return Colors.orange;
      case ConnectionStatus.disconnected:
        return Colors.grey;
      case ConnectionStatus.error:
        return Colors.red;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}

class _StatusInfo {
  final IconData icon;
  final Color color;
  final String label;
  final String tooltip;

  const _StatusInfo({
    required this.icon,
    required this.color,
    required this.label,
    required this.tooltip,
  });
}
