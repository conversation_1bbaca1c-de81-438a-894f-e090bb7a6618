import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_error_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/routes/admin_routes.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_auth_provider.dart';

/// Main error display widget for admin portal
class AdminErrorWidget extends StatelessWidget {
  final AdminError error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showDetails;

  const AdminErrorWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.onDismiss,
    this.showDetails = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Error icon
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: _getErrorColor().withOpacity(0.1),
                borderRadius: BorderRadius.circular(32),
              ),
              child: Icon(
                _getErrorIcon(),
                size: 32,
                color: _getErrorColor(),
              ),
            ),
            const SizedBox(height: 16),

            // Error message
            Text(
              error.message,
              style: theme.textTheme.p.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),

            // Error details (if enabled)
            if (showDetails && error.context != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.muted.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Details:',
                      style: theme.textTheme.small.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      error.context!,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 24),

            // Recovery actions
            if (error.recoveryActions.isNotEmpty)
              Wrap(
                spacing: 12,
                runSpacing: 12,
                alignment: WrapAlignment.center,
                children: error.recoveryActions.map((action) {
                  return _buildRecoveryActionButton(context, action);
                }).toList(),
              ),

            // Dismiss button
            if (onDismiss != null) ...[
              const SizedBox(height: 12),
              ShadButton.ghost(
                onPressed: onDismiss,
                child: const Text('Dismiss'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRecoveryActionButton(BuildContext context, RecoveryAction action) {
    return ShadButton(
      onPressed: () => _handleRecoveryAction(context, action),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getActionIcon(action), size: 16),
          const SizedBox(width: 8),
          Text(action.displayName),
        ],
      ),
    );
  }

  void _handleRecoveryAction(BuildContext context, RecoveryAction action) {
    switch (action) {
      case RecoveryAction.retry:
        onRetry?.call();
        break;
      case RecoveryAction.refresh:
        // Refresh current page
        Navigator.of(context).pushReplacementNamed(
          ModalRoute.of(context)?.settings.name ?? AdminRoutes.dashboard,
        );
        break;
      case RecoveryAction.goBack:
        Navigator.of(context).pop();
        break;
      case RecoveryAction.reLogin:
        Navigator.of(context).pushNamedAndRemoveUntil(
          AdminRoutes.login,
          (route) => false,
        );
        break;
      case RecoveryAction.contactSupport:
        // TODO: Implement contact support functionality
        break;
      case RecoveryAction.checkConnection:
        // TODO: Implement connection check
        break;
      case RecoveryAction.waitAndRetry:
        // TODO: Implement wait and retry with timer
        Future.delayed(const Duration(seconds: 3), () {
          onRetry?.call();
        });
        break;
    }
  }

  IconData _getErrorIcon() {
    switch (error.type) {
      case AdminErrorType.authentication:
        return lucide.LucideIcons.lock;
      case AdminErrorType.authorization:
        return lucide.LucideIcons.shield;
      case AdminErrorType.network:
        return lucide.LucideIcons.wifiOff;
      case AdminErrorType.validation:
        return lucide.LucideIcons.alertTriangle;
      case AdminErrorType.serverError:
        return lucide.LucideIcons.server;
      case AdminErrorType.notFound:
        return lucide.LucideIcons.search;
      case AdminErrorType.rateLimit:
        return lucide.LucideIcons.clock;
      case AdminErrorType.maintenance:
        return lucide.LucideIcons.wrench;
    }
  }

  IconData _getActionIcon(RecoveryAction action) {
    switch (action) {
      case RecoveryAction.retry:
        return lucide.LucideIcons.refreshCw;
      case RecoveryAction.refresh:
        return lucide.LucideIcons.rotateCcw;
      case RecoveryAction.goBack:
        return lucide.LucideIcons.arrowLeft;
      case RecoveryAction.reLogin:
        return lucide.LucideIcons.logIn;
      case RecoveryAction.contactSupport:
        return lucide.LucideIcons.helpCircle;
      case RecoveryAction.checkConnection:
        return lucide.LucideIcons.wifi;
      case RecoveryAction.waitAndRetry:
        return lucide.LucideIcons.timer;
    }
  }

  Color _getErrorColor() {
    switch (error.severity) {
      case AdminErrorSeverity.info:
        return Colors.blue;
      case AdminErrorSeverity.warning:
        return Colors.orange;
      case AdminErrorSeverity.error:
        return Colors.red;
      case AdminErrorSeverity.critical:
        return Colors.red.shade700;
    }
  }
}

/// Inline error widget for form fields and small components
class AdminInlineErrorWidget extends StatelessWidget {
  final String message;
  final AdminErrorSeverity severity;
  final VoidCallback? onDismiss;

  const AdminInlineErrorWidget({
    super.key,
    required this.message,
    this.severity = AdminErrorSeverity.error,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final color = _getSeverityColor(severity);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            _getSeverityIcon(severity),
            size: 16,
            color: color,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: theme.textTheme.small.copyWith(color: color),
            ),
          ),
          if (onDismiss != null) ...[
            const SizedBox(width: 8),
            InkWell(
              onTap: onDismiss,
              child: Icon(
                lucide.LucideIcons.x,
                size: 16,
                color: color,
              ),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getSeverityIcon(AdminErrorSeverity severity) {
    switch (severity) {
      case AdminErrorSeverity.info:
        return lucide.LucideIcons.info;
      case AdminErrorSeverity.warning:
        return lucide.LucideIcons.alertTriangle;
      case AdminErrorSeverity.error:
      case AdminErrorSeverity.critical:
        return lucide.LucideIcons.alertCircle;
    }
  }

  Color _getSeverityColor(AdminErrorSeverity severity) {
    switch (severity) {
      case AdminErrorSeverity.info:
        return Colors.blue;
      case AdminErrorSeverity.warning:
        return Colors.orange;
      case AdminErrorSeverity.error:
        return Colors.red;
      case AdminErrorSeverity.critical:
        return Colors.red.shade700;
    }
  }
}

/// Error banner widget for page-level errors
class AdminErrorBannerWidget extends StatelessWidget {
  final AdminError error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;

  const AdminErrorBannerWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final color = _getErrorColor();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            _getErrorIcon(),
            color: color,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  error.type.displayName,
                  style: theme.textTheme.small.copyWith(
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                Text(
                  error.message,
                  style: theme.textTheme.small.copyWith(color: color),
                ),
              ],
            ),
          ),
          if (onRetry != null) ...[
            const SizedBox(width: 12),
            ShadIconButton.ghost(
              icon: Icon(lucide.LucideIcons.refreshCw, size: 16),
              onPressed: onRetry,
            ),
          ],
          if (onDismiss != null) ...[
            const SizedBox(width: 8),
            ShadIconButton.ghost(
              icon: Icon(lucide.LucideIcons.x, size: 16),
              onPressed: onDismiss,
            ),
          ],
        ],
      ),
    );
  }

  IconData _getErrorIcon() {
    switch (error.type) {
      case AdminErrorType.authentication:
        return lucide.LucideIcons.lock;
      case AdminErrorType.authorization:
        return lucide.LucideIcons.shield;
      case AdminErrorType.network:
        return lucide.LucideIcons.wifiOff;
      case AdminErrorType.validation:
        return lucide.LucideIcons.alertTriangle;
      case AdminErrorType.serverError:
        return lucide.LucideIcons.server;
      case AdminErrorType.notFound:
        return lucide.LucideIcons.search;
      case AdminErrorType.rateLimit:
        return lucide.LucideIcons.clock;
      case AdminErrorType.maintenance:
        return lucide.LucideIcons.wrench;
    }
  }

  Color _getErrorColor() {
    switch (error.severity) {
      case AdminErrorSeverity.info:
        return Colors.blue;
      case AdminErrorSeverity.warning:
        return Colors.orange;
      case AdminErrorSeverity.error:
        return Colors.red;
      case AdminErrorSeverity.critical:
        return Colors.red.shade700;
    }
  }
}
