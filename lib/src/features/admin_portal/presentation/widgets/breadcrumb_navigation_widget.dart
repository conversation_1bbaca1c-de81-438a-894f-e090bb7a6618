import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/routes/admin_routes.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_navigation_provider.dart';

/// Breadcrumb navigation widget for admin portal
class BreadcrumbNavigationWidget extends ConsumerWidget {
  final String currentRoute;
  final Map<String, String>? parameters;

  const BreadcrumbNavigationWidget({
    super.key,
    required this.currentRoute,
    this.parameters,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final breadcrumbs = AdminRoutes.generateBreadcrumbs(currentRoute);

    if (breadcrumbs.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.border,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            lucide.LucideIcons.home,
            size: 16,
            color: theme.colorScheme.mutedForeground,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildBreadcrumbTrail(context, theme, breadcrumbs),
          ),
        ],
      ),
    );
  }

  Widget _buildBreadcrumbTrail(
    BuildContext context,
    ShadThemeData theme,
    List<BreadcrumbItem> breadcrumbs,
  ) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: breadcrumbs.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isLast = index == breadcrumbs.length - 1;

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (index > 0) ...[
                Icon(
                  lucide.LucideIcons.chevronRight,
                  size: 14,
                  color: theme.colorScheme.mutedForeground,
                ),
                const SizedBox(width: 8),
              ],
              _buildBreadcrumbItem(context, theme, item, isLast),
              if (!isLast) const SizedBox(width: 8),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildBreadcrumbItem(
    BuildContext context,
    ShadThemeData theme,
    BreadcrumbItem item,
    bool isLast,
  ) {
    if (isLast) {
      return _buildCurrentPageItem(theme, item);
    } else {
      return _buildClickableItem(context, theme, item);
    }
  }

  Widget _buildCurrentPageItem(ShadThemeData theme, BreadcrumbItem item) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (item.icon != null) ...[
          Icon(
            item.icon,
            size: 14,
            color: theme.colorScheme.foreground,
          ),
          const SizedBox(width: 6),
        ],
        Text(
          item.title,
          style: theme.textTheme.small.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.foreground,
          ),
        ),
      ],
    );
  }

  Widget _buildClickableItem(
    BuildContext context,
    ShadThemeData theme,
    BreadcrumbItem item,
  ) {
    return InkWell(
      onTap: () => _navigateToBreadcrumb(context, item),
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (item.icon != null) ...[
              Icon(
                item.icon,
                size: 14,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 6),
            ],
            Text(
              item.title,
              style: theme.textTheme.small.copyWith(
                color: theme.colorScheme.primary,
                decoration: TextDecoration.underline,
                decorationColor: theme.colorScheme.primary.withOpacity(0.5),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToBreadcrumb(BuildContext context, BreadcrumbItem item) {
    String route = item.route;
    
    // Apply parameters if available
    if (item.parameters != null) {
      route = AdminRoutes.buildRouteWithParams(route, item.parameters!);
    }
    
    Navigator.of(context).pushNamedAndRemoveUntil(
      route,
      (existingRoute) => existingRoute.settings.name == AdminRoutes.dashboard,
    );
  }
}

/// Compact breadcrumb widget for mobile layouts
class CompactBreadcrumbWidget extends ConsumerWidget {
  final String currentRoute;

  const CompactBreadcrumbWidget({
    super.key,
    required this.currentRoute,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final breadcrumbs = AdminRoutes.generateBreadcrumbs(currentRoute);

    if (breadcrumbs.isEmpty) {
      return const SizedBox.shrink();
    }

    final currentPage = breadcrumbs.last;
    final hasParent = breadcrumbs.length > 1;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          if (hasParent) ...[
            InkWell(
              onTap: () => _navigateBack(context, breadcrumbs),
              borderRadius: BorderRadius.circular(4),
              child: Padding(
                padding: const EdgeInsets.all(4),
                child: Icon(
                  lucide.LucideIcons.arrowLeft,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          if (currentPage.icon != null) ...[
            Icon(
              currentPage.icon,
              size: 16,
              color: theme.colorScheme.foreground,
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              currentPage.title,
              style: theme.textTheme.h4.copyWith(
                fontWeight: FontWeight.w600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateBack(BuildContext context, List<BreadcrumbItem> breadcrumbs) {
    if (breadcrumbs.length > 1) {
      final parentItem = breadcrumbs[breadcrumbs.length - 2];
      Navigator.of(context).pushReplacementNamed(parentItem.route);
    }
  }
}

/// Breadcrumb dropdown for complex navigation hierarchies
class BreadcrumbDropdownWidget extends ConsumerWidget {
  final String currentRoute;
  final List<BreadcrumbItem> additionalItems;

  const BreadcrumbDropdownWidget({
    super.key,
    required this.currentRoute,
    this.additionalItems = const [],
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final breadcrumbs = AdminRoutes.generateBreadcrumbs(currentRoute);
    final allItems = [...breadcrumbs, ...additionalItems];

    if (allItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return PopupMenuButton<BreadcrumbItem>(
      icon: Icon(
        lucide.LucideIcons.moreHorizontal,
        size: 16,
        color: theme.colorScheme.mutedForeground,
      ),
      itemBuilder: (context) {
        return allItems.map((item) {
          final isCurrent = item.route == currentRoute;
          
          return PopupMenuItem<BreadcrumbItem>(
            value: item,
            enabled: !isCurrent,
            child: Row(
              children: [
                if (item.icon != null) ...[
                  Icon(
                    item.icon,
                    size: 16,
                    color: isCurrent 
                        ? theme.colorScheme.primary 
                        : theme.colorScheme.foreground,
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  item.title,
                  style: TextStyle(
                    fontWeight: isCurrent ? FontWeight.w600 : FontWeight.normal,
                    color: isCurrent 
                        ? theme.colorScheme.primary 
                        : theme.colorScheme.foreground,
                  ),
                ),
                if (isCurrent) ...[
                  const SizedBox(width: 8),
                  Icon(
                    lucide.LucideIcons.check,
                    size: 14,
                    color: theme.colorScheme.primary,
                  ),
                ],
              ],
            ),
          );
        }).toList();
      },
      onSelected: (item) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          item.route,
          (route) => route.settings.name == AdminRoutes.dashboard,
        );
      },
    );
  }
}
