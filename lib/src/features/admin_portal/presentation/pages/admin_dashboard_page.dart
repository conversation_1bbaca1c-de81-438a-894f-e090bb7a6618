import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_auth_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_dashboard_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/widgets/admin_app_bar_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/widgets/admin_navigation_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/user_management_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/audit_log_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/widgets/dashboard_overview_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/analytics_dashboard_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/utils/admin_responsive_layout.dart';

/// Main admin dashboard page with responsive layout
class AdminDashboardPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin/dashboard';

  const AdminDashboardPage({super.key});

  @override
  ConsumerState<AdminDashboardPage> createState() => _AdminDashboardPageState();
}

class _AdminDashboardPageState extends ConsumerState<AdminDashboardPage> {
  @override
  void initState() {
    super.initState();

    // Check authentication on page load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authState = ref.read(adminAuthProvider);
      if (!authState.isAuthenticated) {
        Navigator.of(context).pushReplacementNamed('/admin/login');
        return;
      }

      // Log admin dashboard access
      ref.read(adminAuthProvider.notifier).logActivity(
        'admin_dashboard_access',
        {'timestamp': DateTime.now().toIso8601String()},
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(adminAuthProvider);
    final dashboardState = ref.watch(adminDashboardProvider);
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    final isMobile = AdminResponsiveLayout.isMobile(context);

    // Redirect to login if not authenticated
    if (!authState.isAuthenticated) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      appBar: const AdminAppBarWidget(),
      drawer: isMobile ? _buildMobileDrawer() : null,
      body: Row(
        children: [
          // Sidebar for desktop and tablet
          if (isDesktop)
            AdminSidebarWidget(
              selectedItem: dashboardState.selectedNavItem,
              onItemSelected: _onNavigationItemSelected,
            ),

          // Main content area
          Expanded(child: _buildMainContent()),
        ],
      ),
      bottomNavigationBar:
          isMobile
              ? AdminBottomNavWidget(
                selectedItem: dashboardState.selectedNavItem,
                onItemSelected: _onNavigationItemSelected,
              )
              : null,
    );
  }

  Widget _buildMobileDrawer() {
    return Drawer(
      child: AdminSidebarWidget(
        selectedItem: ref.watch(adminDashboardProvider).selectedNavItem,
        onItemSelected: (item) {
          _onNavigationItemSelected(item);
          Navigator.of(context).pop(); // Close drawer
        },
      ),
    );
  }

  Widget _buildMainContent() {
    final dashboardState = ref.watch(adminDashboardProvider);

    return Container(
      width: double.infinity,
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      child: _buildContentForNavItem(dashboardState.selectedNavItem),
    );
  }

  Widget _buildContentForNavItem(AdminNavigationItem navItem) {
    switch (navItem) {
      case AdminNavigationItem.dashboard:
        return _buildDashboardOverview();
      case AdminNavigationItem.userManagement:
        return _buildUserManagementPlaceholder();
      case AdminNavigationItem.contentManagement:
        return _buildContentManagementPlaceholder();
      case AdminNavigationItem.analytics:
        return _buildAnalyticsPlaceholder();
      case AdminNavigationItem.notifications:
        return _buildNotificationsPlaceholder();
      case AdminNavigationItem.auditLogs:
        return _buildAuditLogsPlaceholder();
      case AdminNavigationItem.settings:
        return _buildSettingsPlaceholder();
    }
  }

  Widget _buildDashboardOverview() {
    return const DashboardOverviewWidget();
  }

  // User Management content
  Widget _buildUserManagementPlaceholder() {
    return const UserManagementPage();
  }

  Widget _buildContentManagementPlaceholder() {
    return const Center(child: Text('Content Management - Coming Soon'));
  }

  Widget _buildAnalyticsPlaceholder() {
    return const AnalyticsDashboardPage();
  }

  Widget _buildNotificationsPlaceholder() {
    return const Center(child: Text('Notifications - Coming Soon'));
  }

  Widget _buildAuditLogsPlaceholder() {
    return const AuditLogPage();
  }

  Widget _buildSettingsPlaceholder() {
    return const Center(child: Text('Settings - Coming Soon'));
  }

  void _onNavigationItemSelected(AdminNavigationItem item) {
    ref.read(adminDashboardProvider.notifier).setSelectedNavItem(item);
  }
}
