import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/user_activity_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/activity_monitoring_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/widgets/activity_visualization_widgets.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/utils/admin_responsive_layout.dart';

/// User activity monitoring page with real-time activity tracking
class UserActivityMonitoringPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin/activity-monitoring';

  const UserActivityMonitoringPage({super.key});

  @override
  ConsumerState<UserActivityMonitoringPage> createState() => _UserActivityMonitoringPageState();
}

class _UserActivityMonitoringPageState extends ConsumerState<UserActivityMonitoringPage> {
  ActivityFilter _currentFilter = const ActivityFilter();
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize with default filter for last 7 days
    final defaultFilter = ActivityFilter(
      startDate: DateTime.now().subtract(const Duration(days: 7)),
      endDate: DateTime.now(),
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(activityMonitoringProvider.notifier).updateFilter(defaultFilter);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final activityState = ref.watch(activityMonitoringProvider);
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('User Activity Monitoring'),
        backgroundColor: theme.colorScheme.background,
        foregroundColor: theme.colorScheme.foreground,
        elevation: 0,
        actions: [
          _buildExportButton(),
          const SizedBox(width: 8),
          _buildRefreshButton(),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        children: [
          // Filters section
          _buildFiltersSection(),
          
          // Main content
          Expanded(
            child: _buildContent(activityState, isDesktop),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    final theme = ShadTheme.of(context);
    final isDesktop = AdminResponsiveLayout.isDesktop(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.background,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.border,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          if (isDesktop)
            _buildDesktopFilters()
          else
            _buildMobileFilters(),
        ],
      ),
    );
  }

  Widget _buildDesktopFilters() {
    return Row(
      children: [
        // Search field
        SizedBox(
          width: 250,
          child: ShadInput(
            controller: _searchController,
            placeholder: 'Search activities...',
            prefix: const Icon(lucide.LucideIcons.search, size: 16),
            onChanged: _onSearchChanged,
          ),
        ),
        const SizedBox(width: 16),
        
        // Activity type filter
        _buildActivityTypeFilter(),
        const SizedBox(width: 16),
        
        // Date range filter
        _buildDateRangeFilter(),
        const SizedBox(width: 16),
        
        // Scope filter
        _buildScopeFilter(),
        
        const Spacer(),
        
        // Apply filters button
        ShadButton(
          onPressed: _applyFilters,
          child: const Text('Apply Filters'),
        ),
      ],
    );
  }

  Widget _buildMobileFilters() {
    return Column(
      children: [
        // Search field
        ShadInput(
          controller: _searchController,
          placeholder: 'Search activities...',
          prefix: const Icon(lucide.LucideIcons.search, size: 16),
          onChanged: _onSearchChanged,
        ),
        const SizedBox(height: 12),
        
        // Filter buttons row
        Row(
          children: [
            Expanded(child: _buildActivityTypeFilter()),
            const SizedBox(width: 8),
            Expanded(child: _buildDateRangeFilter()),
            const SizedBox(width: 8),
            ShadButton(
              onPressed: _applyFilters,
              size: ShadButtonSize.sm,
              child: const Text('Apply'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActivityTypeFilter() {
    return ShadSelect<UserActivityType>(
      placeholder: const Text('Activity Type'),
      options: UserActivityType.values.map((type) => 
        ShadOption(
          value: type,
          child: Text(type.displayName),
        ),
      ).toList(),
      selectedOptionBuilder: (context, value) => Text(value.displayName),
      onChanged: (value) {
        setState(() {
          _currentFilter = _currentFilter.copyWith(activityType: value);
        });
      },
    );
  }

  Widget _buildDateRangeFilter() {
    return ShadButton.outline(
      onPressed: _showDateRangePicker,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(lucide.LucideIcons.calendar, size: 16),
          const SizedBox(width: 8),
          Text(_getDateRangeText()),
        ],
      ),
    );
  }

  Widget _buildScopeFilter() {
    return ShadSelect<ActivityScope>(
      placeholder: const Text('Scope'),
      options: ActivityScope.values.map((scope) => 
        ShadOption(
          value: scope,
          child: Text(scope.displayName),
        ),
      ).toList(),
      selectedOptionBuilder: (context, value) => Text(value.displayName),
      onChanged: (value) {
        setState(() {
          _currentFilter = _currentFilter.copyWith(scope: value);
        });
      },
    );
  }

  Widget _buildExportButton() {
    return ShadButton.outline(
      onPressed: _onExportPressed,
      size: ShadButtonSize.sm,
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(lucide.LucideIcons.download, size: 16),
          SizedBox(width: 8),
          Text('Export'),
        ],
      ),
    );
  }

  Widget _buildRefreshButton() {
    return ShadButton.outline(
      onPressed: _onRefreshPressed,
      size: ShadButtonSize.sm,
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(lucide.LucideIcons.refreshCw, size: 16),
          SizedBox(width: 8),
          Text('Refresh'),
        ],
      ),
    );
  }

  Widget _buildContent(ActivityMonitoringState state, bool isDesktop) {
    if (state.isLoading && state.activities.isEmpty) {
      return _buildLoadingState();
    } else if (state.hasError) {
      return _buildErrorState(state.error!);
    } else if (state.hasData) {
      return _buildActivityContent(state, isDesktop);
    } else {
      return _buildEmptyState();
    }
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading activity data...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    final theme = ShadTheme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            lucide.LucideIcons.alertCircle,
            size: 64,
            color: theme.colorScheme.destructive,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading activities',
            style: theme.textTheme.h3,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: theme.textTheme.muted,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ShadButton(
            onPressed: _onRefreshPressed,
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = ShadTheme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            lucide.LucideIcons.activity,
            size: 64,
            color: theme.colorScheme.muted,
          ),
          const SizedBox(height: 16),
          Text(
            'No activities found',
            style: theme.textTheme.h3,
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your filters or check back later',
            style: theme.textTheme.muted,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityContent(ActivityMonitoringState state, bool isDesktop) {
    return SingleChildScrollView(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary stats
          _buildSummaryStats(state.activities),
          const SizedBox(height: 24),
          
          // Visualizations
          if (isDesktop)
            _buildDesktopVisualizationsLayout(state.activities)
          else
            _buildMobileVisualizationsLayout(state.activities),
          
          const SizedBox(height: 24),
          
          // Activity timeline
          ActivityTimelineWidget(
            activities: state.activities,
            showUserInfo: true,
            height: 600,
          ),
          
          // Load more button
          if (state.hasMore) ...[
            const SizedBox(height: 16),
            Center(
              child: ShadButton.outline(
                onPressed: state.isLoading ? null : _loadMore,
                child: state.isLoading
                    ? const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Loading...'),
                        ],
                      )
                    : const Text('Load More'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryStats(List<UserActivityEvent> activities) {
    final theme = ShadTheme.of(context);
    final uniqueUsers = activities.map((a) => a.userId).toSet().length;
    final totalActivities = activities.length;
    final mostActiveType = _getMostActiveType(activities);

    return Row(
      children: [
        Expanded(
          child: ShadCard(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Total Activities', style: theme.textTheme.small),
                  const SizedBox(height: 4),
                  Text('$totalActivities', style: theme.textTheme.h2),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ShadCard(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Active Users', style: theme.textTheme.small),
                  const SizedBox(height: 4),
                  Text('$uniqueUsers', style: theme.textTheme.h2),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ShadCard(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Most Active', style: theme.textTheme.small),
                  const SizedBox(height: 4),
                  Text(mostActiveType, style: theme.textTheme.h4),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopVisualizationsLayout(List<UserActivityEvent> activities) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: ActivityHeatmapWidget(
            activities: activities,
            startDate: _currentFilter.startDate ?? DateTime.now().subtract(const Duration(days: 7)),
            endDate: _currentFilter.endDate ?? DateTime.now(),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ActivityTypeDistributionWidget(
            activities: activities,
          ),
        ),
      ],
    );
  }

  Widget _buildMobileVisualizationsLayout(List<UserActivityEvent> activities) {
    return Column(
      children: [
        ActivityHeatmapWidget(
          activities: activities,
          startDate: _currentFilter.startDate ?? DateTime.now().subtract(const Duration(days: 7)),
          endDate: _currentFilter.endDate ?? DateTime.now(),
        ),
        const SizedBox(height: 16),
        ActivityTypeDistributionWidget(
          activities: activities,
        ),
      ],
    );
  }

  String _getMostActiveType(List<UserActivityEvent> activities) {
    if (activities.isEmpty) return 'None';
    
    final typeCount = <UserActivityType, int>{};
    for (final activity in activities) {
      typeCount[activity.type] = (typeCount[activity.type] ?? 0) + 1;
    }
    
    final mostActive = typeCount.entries.reduce((a, b) => a.value > b.value ? a : b);
    return mostActive.key.displayName;
  }

  String _getDateRangeText() {
    if (_currentFilter.startDate != null && _currentFilter.endDate != null) {
      return '${_formatDate(_currentFilter.startDate!)} - ${_formatDate(_currentFilter.endDate!)}';
    }
    return 'Select Date Range';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _onSearchChanged(String value) {
    // TODO: Implement search functionality
  }

  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _currentFilter.startDate != null && _currentFilter.endDate != null
          ? DateTimeRange(start: _currentFilter.startDate!, end: _currentFilter.endDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _currentFilter = _currentFilter.copyWith(
          startDate: picked.start,
          endDate: picked.end,
        );
      });
    }
  }

  void _applyFilters() {
    ref.read(activityMonitoringProvider.notifier).updateFilter(_currentFilter);
  }

  void _onExportPressed() {
    // TODO: Implement export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality coming soon')),
    );
  }

  void _onRefreshPressed() {
    ref.read(activityMonitoringProvider.notifier).refresh();
  }

  void _loadMore() {
    ref.read(activityMonitoringProvider.notifier).loadMore();
  }
}
