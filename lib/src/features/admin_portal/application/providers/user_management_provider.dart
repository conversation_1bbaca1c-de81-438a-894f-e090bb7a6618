import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/services/user_management_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/services/admin_audit_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_user_management_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_audit_models.dart';

/// User management state
class UserManagementState {
  final List<AdminUserModel> users;
  final bool isLoading;
  final String? error;
  final UserFilter filter;
  final int totalCount;
  final Set<String> selectedUserIds;
  final bool isBulkOperationInProgress;
  final BulkOperationResult? lastBulkResult;

  const UserManagementState({
    this.users = const [],
    this.isLoading = false,
    this.error,
    this.filter = const UserFilter(),
    this.totalCount = 0,
    this.selectedUserIds = const {},
    this.isBulkOperationInProgress = false,
    this.lastBulkResult,
  });

  UserManagementState copyWith({
    List<AdminUserModel>? users,
    bool? isLoading,
    String? error,
    UserFilter? filter,
    int? totalCount,
    Set<String>? selectedUserIds,
    bool? isBulkOperationInProgress,
    BulkOperationResult? lastBulkResult,
  }) {
    return UserManagementState(
      users: users ?? this.users,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      filter: filter ?? this.filter,
      totalCount: totalCount ?? this.totalCount,
      selectedUserIds: selectedUserIds ?? this.selectedUserIds,
      isBulkOperationInProgress:
          isBulkOperationInProgress ?? this.isBulkOperationInProgress,
      lastBulkResult: lastBulkResult ?? this.lastBulkResult,
    );
  }

  bool get hasSelection => selectedUserIds.isNotEmpty;
  int get selectedCount => selectedUserIds.length;
  bool get isAllSelected =>
      users.isNotEmpty && selectedUserIds.length == users.length;
}

/// User management provider
class UserManagementNotifier extends StateNotifier<UserManagementState> {
  final UserManagementService _userService;

  UserManagementNotifier(this._userService)
    : super(const UserManagementState()) {
    loadUsers();
  }

  /// Load users with current filter
  Future<void> loadUsers() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final filter = state.filter;

      // Load users and total count in parallel
      final futures = await Future.wait([
        _userService.getAllUsers(
          type: filter.userType,
          status: filter.status,
          searchQuery:
              filter.searchQuery.isNotEmpty ? filter.searchQuery : null,
          startDate: filter.startDate,
          endDate: filter.endDate,
          sortOption: filter.sortOption,
          page: filter.page,
          perPage: filter.perPage,
        ),
        _userService.getUserCount(
          type: filter.userType,
          status: filter.status,
          searchQuery:
              filter.searchQuery.isNotEmpty ? filter.searchQuery : null,
          startDate: filter.startDate,
          endDate: filter.endDate,
        ),
      ]);

      final users = futures[0] as List<AdminUserModel>;
      final totalCount = futures[1] as int;

      state = state.copyWith(
        users: users,
        totalCount: totalCount,
        isLoading: false,
        selectedUserIds: {}, // Clear selection on reload
      );

      LoggerService.info('Loaded ${users.length} users for admin management');
    } catch (e) {
      LoggerService.error('Failed to load users for admin management', e);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load users: ${e.toString()}',
      );
    }
  }

  /// Update filter and reload users
  Future<void> updateFilter(UserFilter newFilter) async {
    state = state.copyWith(filter: newFilter);
    await loadUsers();
  }

  /// Update search query
  Future<void> updateSearchQuery(String query) async {
    final newFilter = state.filter.copyWith(
      searchQuery: query,
      page: 1, // Reset to first page
    );
    await updateFilter(newFilter);
  }

  /// Update user type filter
  Future<void> updateUserTypeFilter(UserType userType) async {
    final newFilter = state.filter.copyWith(
      userType: userType,
      page: 1, // Reset to first page
    );
    await updateFilter(newFilter);
  }

  /// Update status filter
  Future<void> updateStatusFilter(UserStatus status) async {
    final newFilter = state.filter.copyWith(
      status: status,
      page: 1, // Reset to first page
    );
    await updateFilter(newFilter);
  }

  /// Update sort option
  Future<void> updateSortOption(SortOption sortOption) async {
    final newFilter = state.filter.copyWith(sortOption: sortOption);
    await updateFilter(newFilter);
  }

  /// Go to specific page
  Future<void> goToPage(int page) async {
    final newFilter = state.filter.copyWith(page: page);
    await updateFilter(newFilter);
  }

  /// Toggle user selection
  void toggleUserSelection(String userId) {
    final selectedIds = Set<String>.from(state.selectedUserIds);

    if (selectedIds.contains(userId)) {
      selectedIds.remove(userId);
    } else {
      selectedIds.add(userId);
    }

    state = state.copyWith(selectedUserIds: selectedIds);
  }

  /// Select all users on current page
  void selectAllUsers() {
    final allUserIds = state.users.map((user) => user.id).toSet();
    state = state.copyWith(selectedUserIds: allUserIds);
  }

  /// Clear all selections
  void clearSelection() {
    state = state.copyWith(selectedUserIds: {});
  }

  /// Update user status
  Future<bool> updateUserStatus(
    String userId,
    UserStatus newStatus,
    String reason,
    String adminId,
  ) async {
    try {
      await _userService.updateUserStatus(userId, newStatus, reason, adminId);

      // Update user in current list
      final updatedUsers =
          state.users.map((user) {
            if (user.id == userId) {
              return user.copyWith(status: newStatus);
            }
            return user;
          }).toList();

      state = state.copyWith(users: updatedUsers);

      LoggerService.info(
        'Successfully updated user status: $userId to ${newStatus.value}',
      );
      return true;
    } catch (e) {
      LoggerService.error('Failed to update user status: $userId', e);
      state = state.copyWith(
        error: 'Failed to update user status: ${e.toString()}',
      );
      return false;
    }
  }

  /// Bulk update user status
  Future<BulkOperationResult?> bulkUpdateUserStatus(
    UserStatus newStatus,
    String reason,
    String adminId,
  ) async {
    if (state.selectedUserIds.isEmpty) return null;

    try {
      state = state.copyWith(isBulkOperationInProgress: true, error: null);

      final result = await _userService.bulkUpdateUserStatus(
        state.selectedUserIds.toList(),
        newStatus,
        reason,
        adminId,
      );

      // Update users in current list
      final updatedUsers =
          state.users.map((user) {
            if (state.selectedUserIds.contains(user.id)) {
              return user.copyWith(status: newStatus);
            }
            return user;
          }).toList();

      state = state.copyWith(
        users: updatedUsers,
        isBulkOperationInProgress: false,
        lastBulkResult: result,
        selectedUserIds: {}, // Clear selection after operation
      );

      LoggerService.info(
        'Completed bulk user status update: ${result.successCount}/${result.totalCount} successful',
      );
      return result;
    } catch (e) {
      LoggerService.error('Failed bulk user status update', e);
      state = state.copyWith(
        isBulkOperationInProgress: false,
        error: 'Bulk operation failed: ${e.toString()}',
      );
      return null;
    }
  }

  /// Delete user
  Future<bool> deleteUser(String userId, String reason, String adminId) async {
    try {
      await _userService.deleteUser(userId, reason, adminId);

      // Remove user from current list or update status
      final updatedUsers =
          state.users.map((user) {
            if (user.id == userId) {
              return user.copyWith(status: UserStatus.deactivated);
            }
            return user;
          }).toList();

      state = state.copyWith(users: updatedUsers);

      LoggerService.info('Successfully deleted user: $userId');
      return true;
    } catch (e) {
      LoggerService.error('Failed to delete user: $userId', e);
      state = state.copyWith(error: 'Failed to delete user: ${e.toString()}');
      return false;
    }
  }

  /// Search users
  Future<List<AdminUserModel>> searchUsers(
    String query, {
    UserType? type,
  }) async {
    try {
      return await _userService.searchUsers(query, type: type);
    } catch (e) {
      LoggerService.error('Failed to search users with query: $query', e);
      return [];
    }
  }

  /// Refresh users
  Future<void> refresh() async {
    await loadUsers();
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clear last bulk result
  void clearLastBulkResult() {
    state = state.copyWith(lastBulkResult: null);
  }
}

/// Provider for user management service
final userManagementServiceProvider = Provider<UserManagementService>((ref) {
  return UserManagementService();
});

/// Provider for user management state
final userManagementProvider =
    StateNotifierProvider<UserManagementNotifier, UserManagementState>((ref) {
      final userService = ref.watch(userManagementServiceProvider);
      return UserManagementNotifier(userService);
    });

/// Provider for user detail by ID
final userDetailProvider = FutureProvider.family<AdminUserModel, String>((
  ref,
  userId,
) async {
  final userService = ref.watch(userManagementServiceProvider);
  return userService.getUserById(userId);
});

/// Provider for user activity logs
final userActivityLogsProvider =
    FutureProvider.family<List<Map<String, dynamic>>, String>((
      ref,
      userId,
    ) async {
      final userService = ref.watch(userManagementServiceProvider);
      return userService.getUserActivityLogs(userId);
    });

/// Provider for filtered users count
final filteredUsersCountProvider = Provider<int>((ref) {
  final userManagementState = ref.watch(userManagementProvider);
  return userManagementState.totalCount;
});

/// Provider for selected users
final selectedUsersProvider = Provider<List<AdminUserModel>>((ref) {
  final userManagementState = ref.watch(userManagementProvider);
  final selectedIds = userManagementState.selectedUserIds;

  return userManagementState.users
      .where((user) => selectedIds.contains(user.id))
      .toList();
});

/// Provider for pagination info
final paginationInfoProvider = Provider<Map<String, int>>((ref) {
  final userManagementState = ref.watch(userManagementProvider);
  final filter = userManagementState.filter;
  final totalCount = userManagementState.totalCount;

  final totalPages = (totalCount / filter.perPage).ceil();
  final startItem = (filter.page - 1) * filter.perPage + 1;
  final endItem = (filter.page * filter.perPage).clamp(0, totalCount);

  return {
    'currentPage': filter.page,
    'totalPages': totalPages,
    'totalCount': totalCount,
    'startItem': startItem,
    'endItem': endItem,
    'perPage': filter.perPage,
  };
});
