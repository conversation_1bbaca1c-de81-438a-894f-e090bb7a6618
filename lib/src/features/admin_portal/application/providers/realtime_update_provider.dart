import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/dashboard_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/services/realtime_update_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/dashboard_statistics_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/user_management_provider.dart';

/// Provider for real-time updates
final realtimeUpdateProvider = StateNotifierProvider<RealtimeUpdateNotifier, RealtimeUpdateState>((ref) {
  return RealtimeUpdateNotifier(ref);
});

/// Provider for recent activities (derived from real-time events)
final recentActivitiesProvider = Provider<List<RealtimeEvent>>((ref) {
  final realtimeState = ref.watch(realtimeUpdateProvider);
  return realtimeState.recentActivities;
});

/// Provider for connection status
final connectionStatusProvider = Provider<ConnectionStatus>((ref) {
  final realtimeState = ref.watch(realtimeUpdateProvider);
  return realtimeState.connectionStatus;
});

/// Real-time update state notifier
class RealtimeUpdateNotifier extends StateNotifier<RealtimeUpdateState> {
  final Ref _ref;
  final RealtimeUpdateService _realtimeService = RealtimeUpdateService();
  StreamSubscription<RealtimeEvent>? _eventSubscription;
  Timer? _initializationTimer;

  RealtimeUpdateNotifier(this._ref) : super(const RealtimeUpdateState()) {
    _initialize();
  }

  /// Initialize the real-time update service
  Future<void> _initialize() async {
    try {
      LoggerService.info('Initializing real-time update provider');
      
      state = state.copyWith(
        connectionStatus: ConnectionStatus.connecting,
        lastError: null,
      );

      // Initialize the service
      await _realtimeService.initialize();

      // Subscribe to events
      _eventSubscription = _realtimeService.eventStream.listen(
        _handleRealtimeEvent,
        onError: _handleError,
        onDone: _handleDisconnection,
      );

      state = state.copyWith(
        connectionStatus: ConnectionStatus.connected,
        lastConnected: DateTime.now(),
        reconnectAttempts: 0,
      );

      LoggerService.info('Real-time update provider initialized successfully');
    } catch (e) {
      LoggerService.error('Failed to initialize real-time updates', e);
      
      state = state.copyWith(
        connectionStatus: ConnectionStatus.error,
        lastError: e.toString(),
        reconnectAttempts: state.reconnectAttempts + 1,
      );

      // Schedule retry
      _scheduleRetry();
    }
  }

  /// Handle incoming real-time events
  void _handleRealtimeEvent(RealtimeEvent event) {
    try {
      LoggerService.info('Received real-time event: ${event.type.displayName}');

      // Add to recent activities (keep last 50 events)
      final updatedActivities = [event, ...state.recentActivities].take(50).toList();
      
      state = state.copyWith(
        recentActivities: updatedActivities,
        lastConnected: DateTime.now(),
      );

      // Trigger relevant provider updates based on event type
      _triggerProviderUpdates(event);
    } catch (e) {
      LoggerService.error('Error handling real-time event', e);
    }
  }

  /// Trigger updates in relevant providers based on event type
  void _triggerProviderUpdates(RealtimeEvent event) {
    try {
      switch (event.type) {
        case RealtimeEventType.userRegistration:
        case RealtimeEventType.userStatusChange:
          // Refresh user management data
          _ref.refresh(userManagementProvider);
          // Refresh dashboard statistics
          _ref.read(dashboardStatisticsProvider.notifier).refreshStatistics();
          break;
          
        case RealtimeEventType.claimSubmission:
        case RealtimeEventType.claimStatusUpdate:
          // Refresh dashboard statistics for claims
          _ref.read(dashboardStatisticsProvider.notifier).refreshStatistics();
          break;
          
        case RealtimeEventType.adminAction:
          // Refresh audit logs if needed
          // Note: This would trigger audit log provider refresh when implemented
          break;
          
        case RealtimeEventType.notification:
          // Handle notification updates
          // Note: This would trigger notification provider refresh when implemented
          break;
          
        case RealtimeEventType.systemAlert:
        case RealtimeEventType.securityEvent:
          // Handle system alerts
          LoggerService.warning('System alert received: ${event.description}');
          break;
          
        case RealtimeEventType.bulkOperation:
          // Refresh multiple providers for bulk operations
          _ref.refresh(userManagementProvider);
          _ref.read(dashboardStatisticsProvider.notifier).refreshStatistics();
          break;
      }
    } catch (e) {
      LoggerService.error('Error triggering provider updates', e);
    }
  }

  /// Handle connection errors
  void _handleError(dynamic error) {
    LoggerService.error('Real-time connection error', error);
    
    state = state.copyWith(
      connectionStatus: ConnectionStatus.error,
      lastError: error.toString(),
      reconnectAttempts: state.reconnectAttempts + 1,
    );

    _scheduleRetry();
  }

  /// Handle connection disconnection
  void _handleDisconnection() {
    LoggerService.warning('Real-time connection disconnected');
    
    state = state.copyWith(
      connectionStatus: ConnectionStatus.disconnected,
    );

    _scheduleRetry();
  }

  /// Schedule retry with exponential backoff
  void _scheduleRetry() {
    if (state.reconnectAttempts >= 5) {
      LoggerService.error('Max reconnection attempts reached');
      return;
    }

    final delay = Duration(seconds: 2 * state.reconnectAttempts);
    LoggerService.info('Scheduling reconnection in ${delay.inSeconds} seconds');

    _initializationTimer?.cancel();
    _initializationTimer = Timer(delay, () {
      _initialize();
    });
  }

  /// Manually trigger reconnection
  Future<void> reconnect() async {
    LoggerService.info('Manual reconnection triggered');
    
    // Cancel any pending retry
    _initializationTimer?.cancel();
    
    // Reset reconnection attempts
    state = state.copyWith(reconnectAttempts: 0);
    
    // Reinitialize
    await _initialize();
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(lastError: null);
  }

  /// Add a simulated event (for testing)
  void addSimulatedEvent(RealtimeEvent event) {
    _realtimeService.simulateEvent(event);
  }

  /// Get connection health status
  bool get isHealthy {
    return state.isConnected && 
           state.lastConnected != null &&
           DateTime.now().difference(state.lastConnected!).inMinutes < 5;
  }

  /// Get formatted connection status
  String get connectionStatusText {
    switch (state.connectionStatus) {
      case ConnectionStatus.connected:
        return 'Connected';
      case ConnectionStatus.connecting:
        return 'Connecting...';
      case ConnectionStatus.disconnected:
        return 'Disconnected';
      case ConnectionStatus.error:
        return 'Connection Error';
    }
  }

  /// Get recent activities by type
  List<RealtimeEvent> getActivitiesByType(RealtimeEventType type) {
    return state.recentActivities.where((event) => event.type == type).toList();
  }

  /// Get activities from the last N minutes
  List<RealtimeEvent> getRecentActivities({int minutes = 60}) {
    final cutoff = DateTime.now().subtract(Duration(minutes: minutes));
    return state.recentActivities
        .where((event) => event.timestamp.isAfter(cutoff))
        .toList();
  }

  /// Get activity count by type
  Map<RealtimeEventType, int> getActivityCounts() {
    final counts = <RealtimeEventType, int>{};
    for (final event in state.recentActivities) {
      counts[event.type] = (counts[event.type] ?? 0) + 1;
    }
    return counts;
  }

  @override
  void dispose() {
    LoggerService.info('Disposing real-time update provider');
    
    _initializationTimer?.cancel();
    _eventSubscription?.cancel();
    _realtimeService.dispose();
    
    super.dispose();
  }
}
