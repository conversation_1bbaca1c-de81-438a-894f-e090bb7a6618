import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/analytics_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/services/analytics_service.dart';

/// Analytics state model
class AnalyticsState {
  final PlatformMetrics? metrics;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;

  const AnalyticsState({
    this.metrics,
    this.isLoading = false,
    this.error,
    this.lastUpdated,
  });

  AnalyticsState copyWith({
    PlatformMetrics? metrics,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return AnalyticsState(
      metrics: metrics ?? this.metrics,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  bool get hasData => metrics != null;
  bool get hasError => error != null;
  bool get isStale => lastUpdated != null && 
      DateTime.now().difference(lastUpdated!).inMinutes > 5;
}

/// Analytics state notifier
class AnalyticsNotifier extends StateNotifier<AnalyticsState> {
  final AnalyticsService _analyticsService;

  AnalyticsNotifier(this._analyticsService) : super(const AnalyticsState());

  /// Fetch platform metrics for the specified time period
  Future<void> fetchMetrics({
    required AnalyticsTimePeriod period,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      LoggerService.info('Fetching analytics metrics for period: ${period.value}');
      
      state = state.copyWith(isLoading: true, error: null);

      final metrics = await _analyticsService.getPlatformMetrics(
        period: period,
        startDate: startDate,
        endDate: endDate,
      );

      state = state.copyWith(
        metrics: metrics,
        isLoading: false,
        lastUpdated: DateTime.now(),
      );

      LoggerService.info('Analytics metrics fetched successfully');
    } catch (e) {
      LoggerService.error('Error fetching analytics metrics', e);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to fetch analytics data: ${e.toString()}',
      );
    }
  }

  /// Refresh current metrics
  Future<void> refreshMetrics() async {
    if (state.metrics != null) {
      await fetchMetrics(
        period: state.metrics!.period,
        startDate: state.metrics!.startDate,
        endDate: state.metrics!.endDate,
      );
    } else {
      await fetchMetrics(period: AnalyticsTimePeriod.last30Days);
    }
  }

  /// Clear current state
  void clearState() {
    state = const AnalyticsState();
  }

  /// Get formatted KPI value
  String getKPIValue(PlatformKPI kpi) {
    if (state.metrics == null) return '--';

    switch (kpi) {
      case PlatformKPI.totalUsers:
        return state.metrics!.userMetrics.totalUsers.toString();
      case PlatformKPI.activeUsers:
        return (state.metrics!.userMetrics.totalUsers * 0.7).round().toString(); // Placeholder
      case PlatformKPI.userGrowthRate:
        return '${state.metrics!.userMetrics.growthRate.toStringAsFixed(1)}%';
      case PlatformKPI.totalClaims:
        return state.metrics!.claimMetrics.totalClaims.toString();
      case PlatformKPI.activeClaims:
        return state.metrics!.claimMetrics.activeClaims.toString();
      case PlatformKPI.claimSuccessRate:
        return '${state.metrics!.claimMetrics.successRate.toStringAsFixed(1)}%';
      case PlatformKPI.fundingVolume:
        return _formatCurrency(state.metrics!.fundingMetrics.totalVolume);
      case PlatformKPI.averageFundingAmount:
        return _formatCurrency(state.metrics!.fundingMetrics.averageAmount);
      case PlatformKPI.platformRevenue:
        return _formatCurrency(state.metrics!.fundingMetrics.totalVolume * 0.05); // 5% fee
      case PlatformKPI.userEngagement:
        return '${state.metrics!.engagementMetrics.averageSessionDuration.toStringAsFixed(1)}m';
      case PlatformKPI.systemUptime:
        return '${state.metrics!.systemMetrics.uptime.toStringAsFixed(1)}%';
      case PlatformKPI.responseTime:
        return '${state.metrics!.systemMetrics.averageResponseTime.toStringAsFixed(0)}ms';
    }
  }

  /// Get KPI subtitle/description
  String getKPISubtitle(PlatformKPI kpi) {
    if (state.metrics == null) return 'Loading...';

    switch (kpi) {
      case PlatformKPI.totalUsers:
        return 'All registered users';
      case PlatformKPI.activeUsers:
        return 'Active in last 30 days';
      case PlatformKPI.userGrowthRate:
        return 'Weekly growth rate';
      case PlatformKPI.totalClaims:
        return 'All submitted claims';
      case PlatformKPI.activeClaims:
        return 'Currently active';
      case PlatformKPI.claimSuccessRate:
        return 'Completion rate';
      case PlatformKPI.fundingVolume:
        return 'Total funding raised';
      case PlatformKPI.averageFundingAmount:
        return 'Per claim average';
      case PlatformKPI.platformRevenue:
        return 'Platform fees earned';
      case PlatformKPI.userEngagement:
        return 'Avg session duration';
      case PlatformKPI.systemUptime:
        return 'System availability';
      case PlatformKPI.responseTime:
        return 'Average response time';
    }
  }

  /// Format currency values
  String _formatCurrency(double value) {
    if (value >= 1000000) {
      return '£${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '£${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return '£${value.toStringAsFixed(0)}';
    }
  }
}

/// Time series data state
class TimeSeriesState {
  final List<ChartDataPoint> data;
  final bool isLoading;
  final String? error;

  const TimeSeriesState({
    this.data = const [],
    this.isLoading = false,
    this.error,
  });

  TimeSeriesState copyWith({
    List<ChartDataPoint>? data,
    bool? isLoading,
    String? error,
  }) {
    return TimeSeriesState(
      data: data ?? this.data,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  bool get hasData => data.isNotEmpty;
  bool get hasError => error != null;
}

/// Time series data notifier
class TimeSeriesNotifier extends StateNotifier<TimeSeriesState> {
  final AnalyticsService _analyticsService;

  TimeSeriesNotifier(this._analyticsService) : super(const TimeSeriesState());

  /// Fetch time series data for a specific KPI
  Future<void> fetchTimeSeriesData({
    required PlatformKPI kpi,
    required AnalyticsTimePeriod period,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      LoggerService.info('Fetching time series data for KPI: ${kpi.value}');
      
      state = state.copyWith(isLoading: true, error: null);

      final data = await _analyticsService.getTimeSeriesData(
        kpi: kpi,
        period: period,
        startDate: startDate,
        endDate: endDate,
      );

      state = state.copyWith(
        data: data,
        isLoading: false,
      );

      LoggerService.info('Time series data fetched successfully');
    } catch (e) {
      LoggerService.error('Error fetching time series data', e);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to fetch time series data: ${e.toString()}',
      );
    }
  }

  /// Clear current state
  void clearState() {
    state = const TimeSeriesState();
  }
}

/// Analytics service provider
final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  return AnalyticsService();
});

/// Analytics provider for platform metrics
final analyticsProvider = StateNotifierProvider.family<AnalyticsNotifier, AnalyticsState, AnalyticsTimePeriod>(
  (ref, period) {
    final service = ref.watch(analyticsServiceProvider);
    final notifier = AnalyticsNotifier(service);
    
    // Auto-fetch data when provider is created
    Future.microtask(() => notifier.fetchMetrics(period: period));
    
    return notifier;
  },
);

/// Time series data provider
final timeSeriesProvider = StateNotifierProvider.family<TimeSeriesNotifier, TimeSeriesState, Map<String, dynamic>>(
  (ref, params) {
    final service = ref.watch(analyticsServiceProvider);
    final notifier = TimeSeriesNotifier(service);
    
    final kpi = params['kpi'] as PlatformKPI;
    final period = params['period'] as AnalyticsTimePeriod;
    final startDate = params['startDate'] as DateTime?;
    final endDate = params['endDate'] as DateTime?;
    
    // Auto-fetch data when provider is created
    Future.microtask(() => notifier.fetchTimeSeriesData(
      kpi: kpi,
      period: period,
      startDate: startDate,
      endDate: endDate,
    ));
    
    return notifier;
  },
);

/// Available analytics time periods provider
final analyticsTimePeriodsProvider = Provider<List<AnalyticsTimePeriod>>((ref) {
  return AnalyticsTimePeriod.values;
});

/// Available platform KPIs provider
final platformKPIsProvider = Provider<List<PlatformKPI>>((ref) {
  return PlatformKPI.values;
});
