import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/services/admin_auth_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_user_model.dart';

/// Admin authentication state
class AdminAuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final AdminUser? adminUser;
  final String? error;
  final DateTime? lastActivity;
  final bool sessionExpired;

  const AdminAuthState({
    this.isAuthenticated = false,
    this.isLoading = false,
    this.adminUser,
    this.error,
    this.lastActivity,
    this.sessionExpired = false,
  });

  /// Check if user has specific permission
  bool hasPermission(String permission) {
    return adminUser?.hasPermission(permission) ?? false;
  }

  /// Check if user is super admin
  bool get isSuperAdmin {
    return adminUser?.permissionLevel == AdminPermissionLevel.superAdmin;
  }

  /// Check if user can manage users
  bool get canManageUsers => adminUser?.canManageUsers ?? false;

  /// Check if user can manage content
  bool get canManageContent => adminUser?.canManageContent ?? false;

  /// Check if user can view analytics
  bool get canViewAnalytics => adminUser?.canViewAnalytics ?? false;

  /// Check if user can manage settings
  bool get canManageSettings => adminUser?.canManageSettings ?? false;

  AdminAuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    AdminUser? adminUser,
    String? error,
    DateTime? lastActivity,
    bool? sessionExpired,
  }) {
    return AdminAuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      adminUser: adminUser ?? this.adminUser,
      error: error,
      lastActivity: lastActivity ?? this.lastActivity,
      sessionExpired: sessionExpired ?? this.sessionExpired,
    );
  }
}

/// Admin authentication provider
class AdminAuthNotifier extends StateNotifier<AdminAuthState> {
  final AdminAuthService _authService;
  final PocketBase _pb;

  AdminAuthNotifier(this._authService, this._pb) : super(const AdminAuthState()) {
    _initializeAuth();
  }

  /// Initialize authentication state
  void _initializeAuth() {
    try {
      if (_pb.authStore.isValid) {
        final user = _pb.authStore.record;
        if (user != null && user.data['user_type'] == 'admin') {
          final adminUser = AdminUser.fromRecord(user);
          state = state.copyWith(
            isAuthenticated: true,
            adminUser: adminUser,
            lastActivity: DateTime.now(),
          );
          LoggerService.info('Admin authentication state initialized for: ${adminUser.email}');
        }
      }
    } catch (e) {
      LoggerService.error('Error initializing admin auth state', e);
      state = state.copyWith(error: 'Failed to initialize authentication');
    }
  }

  /// Sign in admin user
  Future<bool> signIn(String email, String password) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final adminUser = await _authService.signInAdmin(email, password);

      state = state.copyWith(
        isAuthenticated: true,
        isLoading: false,
        adminUser: adminUser,
        lastActivity: DateTime.now(),
        sessionExpired: false,
      );

      LoggerService.info('Admin sign in successful: ${adminUser.email}');
      return true;
    } on AdminAuthException catch (e) {
      LoggerService.warning('Admin authentication failed: ${e.message}');
      state = state.copyWith(
        isLoading: false,
        error: e.message,
        isAuthenticated: false,
        adminUser: null,
      );
      return false;
    } catch (e) {
      LoggerService.error('Unexpected error during admin sign in', e);
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred. Please try again.',
        isAuthenticated: false,
        adminUser: null,
      );
      return false;
    }
  }

  /// Sign out admin user
  Future<void> signOut() async {
    try {
      final currentUser = state.adminUser;
      
      // Log admin logout
      if (currentUser != null) {
        await _authService.logAdminActivity('admin_logout', {
          'user_id': currentUser.id,
          'timestamp': DateTime.now().toIso8601String(),
        });
      }

      await _authService.signOut();

      state = const AdminAuthState();
      LoggerService.info('Admin sign out successful');
    } catch (e) {
      LoggerService.error('Error during admin sign out', e);
      // Force clear state even if logout fails
      state = const AdminAuthState();
    }
  }

  /// Refresh admin session
  Future<bool> refreshSession() async {
    try {
      await _authService.refreshAdminSession();
      
      // Update last activity
      state = state.copyWith(
        lastActivity: DateTime.now(),
        sessionExpired: false,
      );
      
      return true;
    } on AdminAuthException catch (e) {
      LoggerService.warning('Admin session refresh failed: ${e.message}');
      
      // Handle session expiration
      if (e.code == 'PRIVILEGES_REVOKED' || e.code == 'ACCOUNT_DEACTIVATED') {
        state = state.copyWith(
          isAuthenticated: false,
          adminUser: null,
          sessionExpired: true,
          error: e.message,
        );
      }
      
      return false;
    } catch (e) {
      LoggerService.error('Error refreshing admin session', e);
      return false;
    }
  }

  /// Update last activity timestamp
  void updateActivity() {
    if (state.isAuthenticated) {
      state = state.copyWith(lastActivity: DateTime.now());
    }
  }

  /// Check if session is expired (30 minutes of inactivity)
  bool isSessionExpired() {
    if (!state.isAuthenticated || state.lastActivity == null) {
      return false;
    }
    
    final now = DateTime.now();
    final lastActivity = state.lastActivity!;
    final difference = now.difference(lastActivity);
    
    return difference.inMinutes > 30; // 30 minutes timeout
  }

  /// Handle session timeout
  Future<void> handleSessionTimeout() async {
    if (isSessionExpired()) {
      LoggerService.info('Admin session expired due to inactivity');
      
      state = state.copyWith(
        sessionExpired: true,
        error: 'Session expired due to inactivity. Please sign in again.',
      );
      
      await signOut();
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Update admin user data
  void updateAdminUser(AdminUser updatedUser) {
    if (state.isAuthenticated && state.adminUser?.id == updatedUser.id) {
      state = state.copyWith(adminUser: updatedUser);
    }
  }

  /// Check specific permission
  bool hasPermission(String permission) {
    return state.hasPermission(permission);
  }

  /// Log admin activity
  Future<void> logActivity(String action, Map<String, dynamic> details) async {
    try {
      await _authService.logAdminActivity(action, details);
      updateActivity(); // Update last activity timestamp
    } catch (e) {
      LoggerService.error('Failed to log admin activity: $action', e);
    }
  }
}

/// Provider for admin authentication service
final adminAuthServiceProvider = Provider<AdminAuthService>((ref) {
  return AdminAuthService();
});

/// Provider for admin authentication state
final adminAuthProvider = StateNotifierProvider<AdminAuthNotifier, AdminAuthState>((ref) {
  final authService = ref.watch(adminAuthServiceProvider);
  final pocketBase = authService.pb;
  return AdminAuthNotifier(authService, pocketBase);
});

/// Provider for checking if current user is admin
final isAdminProvider = Provider<bool>((ref) {
  final authState = ref.watch(adminAuthProvider);
  return authState.isAuthenticated;
});

/// Provider for current admin user
final currentAdminUserProvider = Provider<AdminUser?>((ref) {
  final authState = ref.watch(adminAuthProvider);
  return authState.adminUser;
});

/// Provider for admin permissions
final adminPermissionsProvider = Provider<List<String>>((ref) {
  final authState = ref.watch(adminAuthProvider);
  return authState.adminUser?.permissions ?? [];
});
