import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_error_models.dart';

/// Provider for admin loading state management
final adminLoadingStateProvider = StateNotifierProvider<AdminLoadingStateNotifier, AdminLoadingState>((ref) {
  return AdminLoadingStateNotifier();
});

/// Provider for checking if any operation is loading
final isAnyOperationLoadingProvider = Provider<bool>((ref) {
  final loadingState = ref.watch(adminLoadingStateProvider);
  return loadingState.isLoading;
});

/// Provider for getting loading operations by type
final loadingOperationsByTypeProvider = Provider.family<List<LoadingOperation>, AdminLoadingType>((ref, type) {
  final loadingState = ref.watch(adminLoadingStateProvider);
  return loadingState.operations.values.where((op) => op.type == type).toList();
});

/// Provider for getting a specific loading operation
final loadingOperationProvider = Provider.family<LoadingOperation?, String>((ref, operationId) {
  final loadingState = ref.watch(adminLoadingStateProvider);
  return loadingState.operations[operationId];
});

/// Admin loading state
class AdminLoadingState {
  final Map<String, LoadingOperation> operations;
  final bool isLoading;
  final DateTime? lastUpdated;

  const AdminLoadingState({
    this.operations = const {},
    this.isLoading = false,
    this.lastUpdated,
  });

  AdminLoadingState copyWith({
    Map<String, LoadingOperation>? operations,
    bool? isLoading,
    DateTime? lastUpdated,
  }) {
    return AdminLoadingState(
      operations: operations ?? this.operations,
      isLoading: isLoading ?? this.isLoading,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Get operations by type
  List<LoadingOperation> getOperationsByType(AdminLoadingType type) {
    return operations.values.where((op) => op.type == type).toList();
  }

  /// Get operations by operation type
  List<LoadingOperation> getOperationsByOperationType(AdminOperationType operationType) {
    return operations.values.where((op) => op.operationType == operationType).toList();
  }

  /// Check if specific operation type is loading
  bool isOperationTypeLoading(AdminOperationType operationType) {
    return operations.values.any((op) => op.operationType == operationType);
  }

  /// Get total number of active operations
  int get activeOperationCount => operations.length;

  /// Get operations that are taking too long
  List<LoadingOperation> get slowOperations {
    return operations.values.where((op) => op.isTakingTooLong).toList();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdminLoadingState &&
        other.isLoading == isLoading &&
        other.operations.length == operations.length;
  }

  @override
  int get hashCode {
    return isLoading.hashCode ^ operations.length.hashCode;
  }
}

/// Admin loading state notifier
class AdminLoadingStateNotifier extends StateNotifier<AdminLoadingState> {
  static const int maxOperations = 50;
  static const Duration operationTimeout = Duration(minutes: 5);
  
  Timer? _cleanupTimer;

  AdminLoadingStateNotifier() : super(const AdminLoadingState()) {
    _startCleanupTimer();
  }

  /// Start a loading operation
  void setLoading(
    String operationId,
    AdminLoadingType type, {
    AdminOperationType? operationType,
    String? message,
    Map<String, dynamic>? metadata,
  }) {
    try {
      LoggerService.info('Starting loading operation: $operationId ($type)');

      final operations = Map<String, LoadingOperation>.from(state.operations);
      
      // Remove old operation if it exists
      operations.remove(operationId);
      
      // Add new operation
      operations[operationId] = LoadingOperation(
        id: operationId,
        type: type,
        operationType: operationType,
        message: message,
        startTime: DateTime.now(),
        metadata: metadata,
      );

      // Limit number of operations
      if (operations.length > maxOperations) {
        final sortedOps = operations.values.toList()
          ..sort((a, b) => a.startTime.compareTo(b.startTime));
        
        for (int i = 0; i < operations.length - maxOperations; i++) {
          operations.remove(sortedOps[i].id);
        }
      }

      state = state.copyWith(
        operations: operations,
        isLoading: operations.isNotEmpty,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      LoggerService.error('Error setting loading state for operation: $operationId', e);
    }
  }

  /// Update progress for an operation
  void setProgress(
    String operationId,
    double progress, {
    String? message,
    Map<String, dynamic>? metadata,
  }) {
    try {
      final operations = Map<String, LoadingOperation>.from(state.operations);
      
      if (operations.containsKey(operationId)) {
        operations[operationId] = operations[operationId]!.copyWith(
          progress: progress.clamp(0.0, 1.0),
          message: message,
          metadata: metadata,
        );
        
        state = state.copyWith(
          operations: operations,
          lastUpdated: DateTime.now(),
        );
        
        LoggerService.info('Updated progress for operation $operationId: ${(progress * 100).toInt()}%');
      }
    } catch (e) {
      LoggerService.error('Error updating progress for operation: $operationId', e);
    }
  }

  /// Update message for an operation
  void updateMessage(String operationId, String message) {
    try {
      final operations = Map<String, LoadingOperation>.from(state.operations);
      
      if (operations.containsKey(operationId)) {
        operations[operationId] = operations[operationId]!.copyWith(message: message);
        
        state = state.copyWith(
          operations: operations,
          lastUpdated: DateTime.now(),
        );
      }
    } catch (e) {
      LoggerService.error('Error updating message for operation: $operationId', e);
    }
  }

  /// Clear a loading operation
  void clearLoading(String operationId) {
    try {
      LoggerService.info('Clearing loading operation: $operationId');

      final operations = Map<String, LoadingOperation>.from(state.operations);
      final removedOperation = operations.remove(operationId);
      
      if (removedOperation != null) {
        state = state.copyWith(
          operations: operations,
          isLoading: operations.isNotEmpty,
          lastUpdated: DateTime.now(),
        );
        
        LoggerService.info('Cleared loading operation: $operationId (duration: ${removedOperation.formattedDuration})');
      }
    } catch (e) {
      LoggerService.error('Error clearing loading state for operation: $operationId', e);
    }
  }

  /// Clear all loading operations
  void clearAllLoading() {
    try {
      LoggerService.info('Clearing all loading operations');
      
      state = state.copyWith(
        operations: {},
        isLoading: false,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      LoggerService.error('Error clearing all loading operations', e);
    }
  }

  /// Clear loading operations by type
  void clearLoadingByType(AdminLoadingType type) {
    try {
      LoggerService.info('Clearing loading operations by type: $type');

      final operations = Map<String, LoadingOperation>.from(state.operations);
      operations.removeWhere((key, operation) => operation.type == type);
      
      state = state.copyWith(
        operations: operations,
        isLoading: operations.isNotEmpty,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      LoggerService.error('Error clearing loading operations by type: $type', e);
    }
  }

  /// Clear loading operations by operation type
  void clearLoadingByOperationType(AdminOperationType operationType) {
    try {
      LoggerService.info('Clearing loading operations by operation type: $operationType');

      final operations = Map<String, LoadingOperation>.from(state.operations);
      operations.removeWhere((key, operation) => operation.operationType == operationType);
      
      state = state.copyWith(
        operations: operations,
        isLoading: operations.isNotEmpty,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      LoggerService.error('Error clearing loading operations by operation type: $operationType', e);
    }
  }

  /// Check if a specific operation is loading
  bool isOperationLoading(String operationId) {
    return state.operations.containsKey(operationId);
  }

  /// Get operation by ID
  LoadingOperation? getOperation(String operationId) {
    return state.operations[operationId];
  }

  /// Get all operations as a list
  List<LoadingOperation> getAllOperations() {
    return state.operations.values.toList();
  }

  /// Get operations that are taking too long
  List<LoadingOperation> getSlowOperations() {
    return state.slowOperations;
  }

  /// Start cleanup timer to remove old operations
  void _startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _cleanupOldOperations();
    });
  }

  /// Clean up operations that have been running too long
  void _cleanupOldOperations() {
    try {
      final operations = Map<String, LoadingOperation>.from(state.operations);
      final now = DateTime.now();
      bool hasChanges = false;
      
      operations.removeWhere((key, operation) {
        final shouldRemove = now.difference(operation.startTime) > operationTimeout;
        if (shouldRemove) {
          LoggerService.warning('Removing timed out operation: $key (duration: ${operation.formattedDuration})');
          hasChanges = true;
        }
        return shouldRemove;
      });
      
      if (hasChanges) {
        state = state.copyWith(
          operations: operations,
          isLoading: operations.isNotEmpty,
          lastUpdated: DateTime.now(),
        );
      }
    } catch (e) {
      LoggerService.error('Error cleaning up old operations', e);
    }
  }

  @override
  void dispose() {
    _cleanupTimer?.cancel();
    super.dispose();
  }
}
