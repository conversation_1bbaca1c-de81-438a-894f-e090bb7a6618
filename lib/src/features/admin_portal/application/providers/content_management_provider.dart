import 'dart:async';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/content_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/services/content_management_service.dart';

/// Content management state model
class ContentManagementState {
  final List<ContentItem> contentItems;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;
  final int totalCount;
  final bool hasMore;

  const ContentManagementState({
    this.contentItems = const [],
    this.isLoading = false,
    this.error,
    this.lastUpdated,
    this.totalCount = 0,
    this.hasMore = false,
  });

  ContentManagementState copyWith({
    List<ContentItem>? contentItems,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
    int? totalCount,
    bool? hasMore,
  }) {
    return ContentManagementState(
      contentItems: contentItems ?? this.contentItems,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      totalCount: totalCount ?? this.totalCount,
      hasMore: hasMore ?? this.hasMore,
    );
  }

  bool get hasData => contentItems.isNotEmpty;
  bool get hasError => error != null;
  bool get isStale => lastUpdated != null && 
      DateTime.now().difference(lastUpdated!).inMinutes > 5;
}

/// Content filter parameters
class ContentFilter {
  final ContentType? type;
  final ContentStatus? status;
  final ContentCategory? category;
  final String? searchQuery;

  const ContentFilter({
    this.type,
    this.status,
    this.category,
    this.searchQuery,
  });

  ContentFilter copyWith({
    ContentType? type,
    ContentStatus? status,
    ContentCategory? category,
    String? searchQuery,
  }) {
    return ContentFilter(
      type: type ?? this.type,
      status: status ?? this.status,
      category: category ?? this.category,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ContentFilter &&
          runtimeType == other.runtimeType &&
          type == other.type &&
          status == other.status &&
          category == other.category &&
          searchQuery == other.searchQuery;

  @override
  int get hashCode =>
      type.hashCode ^
      status.hashCode ^
      category.hashCode ^
      searchQuery.hashCode;
}

/// Content management state notifier
class ContentManagementNotifier extends StateNotifier<ContentManagementState> {
  final ContentManagementService _contentService;
  ContentFilter _currentFilter = const ContentFilter();
  int _currentPage = 1;

  ContentManagementNotifier(this._contentService) : super(const ContentManagementState());

  /// Fetch content items with the given filter
  Future<void> fetchContentItems({
    ContentFilter? filter,
    bool refresh = false,
  }) async {
    try {
      if (filter != null) {
        _currentFilter = filter;
      }

      if (refresh) {
        _currentPage = 1;
        state = state.copyWith(isLoading: true, error: null);
      } else if (state.isLoading) {
        return; // Already loading
      }

      LoggerService.info('Fetching content items with filter');

      final contentItems = await _contentService.getContentItems(
        type: _currentFilter.type,
        status: _currentFilter.status,
        category: _currentFilter.category,
        searchQuery: _currentFilter.searchQuery,
        page: _currentPage,
        perPage: 20,
      );

      if (refresh || _currentPage == 1) {
        state = state.copyWith(
          contentItems: contentItems,
          isLoading: false,
          lastUpdated: DateTime.now(),
          hasMore: contentItems.length >= 20,
        );
      } else {
        state = state.copyWith(
          contentItems: [...state.contentItems, ...contentItems],
          isLoading: false,
          lastUpdated: DateTime.now(),
          hasMore: contentItems.length >= 20,
        );
      }

      LoggerService.info('Fetched ${contentItems.length} content items successfully');
    } catch (e) {
      LoggerService.error('Error fetching content items', e);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to fetch content items: ${e.toString()}',
      );
    }
  }

  /// Load more content items (pagination)
  Future<void> loadMore() async {
    if (state.isLoading || !state.hasMore) return;

    _currentPage++;
    await fetchContentItems();
  }

  /// Refresh current content items
  Future<void> refresh() async {
    await fetchContentItems(refresh: true);
  }

  /// Update filter and fetch content items
  Future<void> updateFilter(ContentFilter filter) async {
    _currentFilter = filter;
    _currentPage = 1;
    await fetchContentItems(refresh: true);
  }

  /// Create a new content item
  Future<ContentItem> createContentItem({
    required String title,
    required String content,
    required String authorId,
    required ContentType type,
    String? excerpt,
    ContentCategory category = ContentCategory.news,
    List<String>? tags,
    String? featuredImageId,
    ContentStatus status = ContentStatus.draft,
    Map<String, dynamic>? seoMetadata,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      LoggerService.info('Creating new content item: $title');

      final newItem = await _contentService.createContentItem(
        title: title,
        content: content,
        authorId: authorId,
        type: type,
        excerpt: excerpt,
        category: category,
        tags: tags,
        featuredImageId: featuredImageId,
        status: status,
        seoMetadata: seoMetadata,
        metadata: metadata,
      );

      // Add to current list if it matches the filter
      if (_shouldIncludeInCurrentList(newItem)) {
        state = state.copyWith(
          contentItems: [newItem, ...state.contentItems],
          lastUpdated: DateTime.now(),
        );
      }

      LoggerService.info('Content item created successfully');
      return newItem;
    } catch (e) {
      LoggerService.error('Error creating content item', e);
      rethrow;
    }
  }

  /// Update an existing content item
  Future<ContentItem> updateContentItem({
    required String id,
    String? title,
    String? content,
    String? excerpt,
    ContentCategory? category,
    List<String>? tags,
    String? featuredImageId,
    ContentStatus? status,
    DateTime? scheduledAt,
    Map<String, dynamic>? seoMetadata,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      LoggerService.info('Updating content item: $id');

      final updatedItem = await _contentService.updateContentItem(
        id: id,
        title: title,
        content: content,
        excerpt: excerpt,
        category: category,
        tags: tags,
        featuredImageId: featuredImageId,
        status: status,
        scheduledAt: scheduledAt,
        seoMetadata: seoMetadata,
        metadata: metadata,
      );

      // Update in current list
      final updatedList = state.contentItems.map((item) {
        return item.id == id ? updatedItem : item;
      }).toList();

      state = state.copyWith(
        contentItems: updatedList,
        lastUpdated: DateTime.now(),
      );

      LoggerService.info('Content item updated successfully');
      return updatedItem;
    } catch (e) {
      LoggerService.error('Error updating content item', e);
      rethrow;
    }
  }

  /// Delete a content item
  Future<void> deleteContentItem(String id) async {
    try {
      LoggerService.info('Deleting content item: $id');

      await _contentService.deleteContentItem(id);

      // Remove from current list
      final updatedList = state.contentItems.where((item) => item.id != id).toList();

      state = state.copyWith(
        contentItems: updatedList,
        lastUpdated: DateTime.now(),
      );

      LoggerService.info('Content item deleted successfully');
    } catch (e) {
      LoggerService.error('Error deleting content item', e);
      rethrow;
    }
  }

  /// Duplicate a content item
  Future<ContentItem> duplicateContentItem(String id) async {
    try {
      LoggerService.info('Duplicating content item: $id');

      final duplicatedItem = await _contentService.duplicateContentItem(id);

      // Add to current list if it matches the filter
      if (_shouldIncludeInCurrentList(duplicatedItem)) {
        state = state.copyWith(
          contentItems: [duplicatedItem, ...state.contentItems],
          lastUpdated: DateTime.now(),
        );
      }

      LoggerService.info('Content item duplicated successfully');
      return duplicatedItem;
    } catch (e) {
      LoggerService.error('Error duplicating content item', e);
      rethrow;
    }
  }

  /// Clear current state
  void clearState() {
    state = const ContentManagementState();
    _currentPage = 1;
  }

  /// Check if a content item should be included in the current filtered list
  bool _shouldIncludeInCurrentList(ContentItem item) {
    if (_currentFilter.type != null && item.type != _currentFilter.type) {
      return false;
    }
    if (_currentFilter.status != null && item.status != _currentFilter.status) {
      return false;
    }
    if (_currentFilter.category != null && item.category != _currentFilter.category) {
      return false;
    }
    if (_currentFilter.searchQuery != null && _currentFilter.searchQuery!.isNotEmpty) {
      final query = _currentFilter.searchQuery!.toLowerCase();
      if (!item.title.toLowerCase().contains(query) &&
          !item.content.toLowerCase().contains(query) &&
          !(item.excerpt?.toLowerCase().contains(query) ?? false)) {
        return false;
      }
    }
    return true;
  }

  /// Get current filter
  ContentFilter get currentFilter => _currentFilter;
}

/// Content editor state for managing draft auto-saving
class ContentEditorState {
  final ContentItem? contentItem;
  final bool isLoading;
  final bool isSaving;
  final String? error;
  final DateTime? lastSaved;
  final bool hasUnsavedChanges;

  const ContentEditorState({
    this.contentItem,
    this.isLoading = false,
    this.isSaving = false,
    this.error,
    this.lastSaved,
    this.hasUnsavedChanges = false,
  });

  ContentEditorState copyWith({
    ContentItem? contentItem,
    bool? isLoading,
    bool? isSaving,
    String? error,
    DateTime? lastSaved,
    bool? hasUnsavedChanges,
  }) {
    return ContentEditorState(
      contentItem: contentItem ?? this.contentItem,
      isLoading: isLoading ?? this.isLoading,
      isSaving: isSaving ?? this.isSaving,
      error: error,
      lastSaved: lastSaved ?? this.lastSaved,
      hasUnsavedChanges: hasUnsavedChanges ?? this.hasUnsavedChanges,
    );
  }

  bool get hasData => contentItem != null;
  bool get hasError => error != null;
}

/// Content editor notifier with auto-save functionality
class ContentEditorNotifier extends StateNotifier<ContentEditorState> {
  final ContentManagementService _contentService;
  Timer? _autoSaveTimer;

  ContentEditorNotifier(this._contentService) : super(const ContentEditorState());

  /// Load content item for editing
  Future<void> loadContentItem(String id) async {
    try {
      LoggerService.info('Loading content item for editing: $id');
      
      state = state.copyWith(isLoading: true, error: null);

      final contentItem = await _contentService.getContentItem(id);

      state = state.copyWith(
        contentItem: contentItem,
        isLoading: false,
        hasUnsavedChanges: false,
      );

      LoggerService.info('Content item loaded successfully');
    } catch (e) {
      LoggerService.error('Error loading content item', e);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load content item: ${e.toString()}',
      );
    }
  }

  /// Mark content as having unsaved changes and schedule auto-save
  void markAsChanged() {
    state = state.copyWith(hasUnsavedChanges: true);
    _scheduleAutoSave();
  }

  /// Save content item
  Future<void> saveContentItem({
    String? title,
    String? content,
    String? excerpt,
    ContentCategory? category,
    List<String>? tags,
    String? featuredImageId,
    ContentStatus? status,
    DateTime? scheduledAt,
    Map<String, dynamic>? seoMetadata,
    Map<String, dynamic>? metadata,
  }) async {
    if (state.contentItem == null) return;

    try {
      LoggerService.info('Saving content item: ${state.contentItem!.id}');
      
      state = state.copyWith(isSaving: true, error: null);

      final updatedItem = await _contentService.updateContentItem(
        id: state.contentItem!.id,
        title: title,
        content: content,
        excerpt: excerpt,
        category: category,
        tags: tags,
        featuredImageId: featuredImageId,
        status: status,
        scheduledAt: scheduledAt,
        seoMetadata: seoMetadata,
        metadata: metadata,
      );

      state = state.copyWith(
        contentItem: updatedItem,
        isSaving: false,
        hasUnsavedChanges: false,
        lastSaved: DateTime.now(),
      );

      LoggerService.info('Content item saved successfully');
    } catch (e) {
      LoggerService.error('Error saving content item', e);
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save content item: ${e.toString()}',
      );
    }
  }

  /// Schedule auto-save
  void _scheduleAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer(const Duration(seconds: 30), () {
      if (state.hasUnsavedChanges && !state.isSaving) {
        // Auto-save as draft
        saveContentItem(status: ContentStatus.draft);
      }
    });
  }

  /// Clear current state
  void clearState() {
    _autoSaveTimer?.cancel();
    state = const ContentEditorState();
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    super.dispose();
  }
}

/// Media upload state
class MediaUploadState {
  final List<MediaAsset> mediaAssets;
  final bool isUploading;
  final double uploadProgress;
  final String? error;

  const MediaUploadState({
    this.mediaAssets = const [],
    this.isUploading = false,
    this.uploadProgress = 0.0,
    this.error,
  });

  MediaUploadState copyWith({
    List<MediaAsset>? mediaAssets,
    bool? isUploading,
    double? uploadProgress,
    String? error,
  }) {
    return MediaUploadState(
      mediaAssets: mediaAssets ?? this.mediaAssets,
      isUploading: isUploading ?? this.isUploading,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      error: error,
    );
  }

  bool get hasData => mediaAssets.isNotEmpty;
  bool get hasError => error != null;
}

/// Media upload notifier
class MediaUploadNotifier extends StateNotifier<MediaUploadState> {
  final ContentManagementService _contentService;

  MediaUploadNotifier(this._contentService) : super(const MediaUploadState());

  /// Upload media file
  Future<MediaAsset> uploadMedia({
    required File file,
    required String contentId,
    String? altText,
    String? caption,
  }) async {
    try {
      LoggerService.info('Uploading media file: ${file.path}');
      
      state = state.copyWith(isUploading: true, uploadProgress: 0.0, error: null);

      final mediaAsset = await _contentService.uploadContentMedia(
        file: file,
        contentId: contentId,
        altText: altText,
        caption: caption,
      );

      state = state.copyWith(
        mediaAssets: [...state.mediaAssets, mediaAsset],
        isUploading: false,
        uploadProgress: 1.0,
      );

      LoggerService.info('Media uploaded successfully');
      return mediaAsset;
    } catch (e) {
      LoggerService.error('Error uploading media', e);
      state = state.copyWith(
        isUploading: false,
        error: 'Failed to upload media: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// Load media assets for content
  Future<void> loadContentMedia(String contentId) async {
    try {
      LoggerService.info('Loading media for content: $contentId');

      final mediaAssets = await _contentService.getContentMedia(contentId);

      state = state.copyWith(mediaAssets: mediaAssets);

      LoggerService.info('Media assets loaded successfully');
    } catch (e) {
      LoggerService.error('Error loading content media', e);
      state = state.copyWith(
        error: 'Failed to load media: ${e.toString()}',
      );
    }
  }

  /// Clear current state
  void clearState() {
    state = const MediaUploadState();
  }
}

/// Content management service provider
final contentManagementServiceProvider = Provider<ContentManagementService>((ref) {
  return ContentManagementService();
});

/// Content management provider
final contentManagementProvider = StateNotifierProvider<ContentManagementNotifier, ContentManagementState>((ref) {
  final service = ref.watch(contentManagementServiceProvider);
  return ContentManagementNotifier(service);
});

/// Content editor provider
final contentEditorProvider = StateNotifierProvider<ContentEditorNotifier, ContentEditorState>((ref) {
  final service = ref.watch(contentManagementServiceProvider);
  return ContentEditorNotifier(service);
});

/// Media upload provider
final mediaUploadProvider = StateNotifierProvider<MediaUploadNotifier, MediaUploadState>((ref) {
  final service = ref.watch(contentManagementServiceProvider);
  return MediaUploadNotifier(service);
});

/// Available content types provider
final contentTypesProvider = Provider<List<ContentType>>((ref) {
  return ContentType.values;
});

/// Available content statuses provider
final contentStatusesProvider = Provider<List<ContentStatus>>((ref) {
  return ContentStatus.values;
});

/// Available content categories provider
final contentCategoriesProvider = Provider<List<ContentCategory>>((ref) {
  return ContentCategory.values;
});
