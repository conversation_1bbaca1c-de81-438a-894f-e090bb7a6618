import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/user_activity_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/services/user_activity_monitoring_service.dart';

/// Activity monitoring state model
class ActivityMonitoringState {
  final List<UserActivityEvent> activities;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;
  final int totalCount;
  final bool hasMore;

  const ActivityMonitoringState({
    this.activities = const [],
    this.isLoading = false,
    this.error,
    this.lastUpdated,
    this.totalCount = 0,
    this.hasMore = false,
  });

  ActivityMonitoringState copyWith({
    List<UserActivityEvent>? activities,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
    int? totalCount,
    bool? hasMore,
  }) {
    return ActivityMonitoringState(
      activities: activities ?? this.activities,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      totalCount: totalCount ?? this.totalCount,
      hasMore: hasMore ?? this.hasMore,
    );
  }

  bool get hasData => activities.isNotEmpty;
  bool get hasError => error != null;
  bool get isStale => lastUpdated != null && 
      DateTime.now().difference(lastUpdated!).inMinutes > 5;
}

/// Activity filter parameters
class ActivityFilter {
  final String? userId;
  final UserActivityType? activityType;
  final DateTime? startDate;
  final DateTime? endDate;
  final ActivityScope scope;

  const ActivityFilter({
    this.userId,
    this.activityType,
    this.startDate,
    this.endDate,
    this.scope = ActivityScope.allUsers,
  });

  ActivityFilter copyWith({
    String? userId,
    UserActivityType? activityType,
    DateTime? startDate,
    DateTime? endDate,
    ActivityScope? scope,
  }) {
    return ActivityFilter(
      userId: userId ?? this.userId,
      activityType: activityType ?? this.activityType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      scope: scope ?? this.scope,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ActivityFilter &&
          runtimeType == other.runtimeType &&
          userId == other.userId &&
          activityType == other.activityType &&
          startDate == other.startDate &&
          endDate == other.endDate &&
          scope == other.scope;

  @override
  int get hashCode =>
      userId.hashCode ^
      activityType.hashCode ^
      startDate.hashCode ^
      endDate.hashCode ^
      scope.hashCode;
}

/// Activity monitoring state notifier
class ActivityMonitoringNotifier extends StateNotifier<ActivityMonitoringState> {
  final UserActivityMonitoringService _monitoringService;
  ActivityFilter _currentFilter = const ActivityFilter();
  int _currentPage = 1;

  ActivityMonitoringNotifier(this._monitoringService) : super(const ActivityMonitoringState());

  /// Fetch activities with the given filter
  Future<void> fetchActivities({
    ActivityFilter? filter,
    bool refresh = false,
  }) async {
    try {
      if (filter != null) {
        _currentFilter = filter;
      }

      if (refresh) {
        _currentPage = 1;
        state = state.copyWith(isLoading: true, error: null);
      } else if (state.isLoading) {
        return; // Already loading
      }

      LoggerService.info('Fetching activities with filter: ${_currentFilter.toString()}');

      final activities = await _monitoringService.getUserActivities(
        userId: _currentFilter.userId,
        activityType: _currentFilter.activityType,
        startDate: _currentFilter.startDate,
        endDate: _currentFilter.endDate,
        page: _currentPage,
        perPage: 50,
      );

      if (refresh || _currentPage == 1) {
        state = state.copyWith(
          activities: activities,
          isLoading: false,
          lastUpdated: DateTime.now(),
          hasMore: activities.length >= 50,
        );
      } else {
        state = state.copyWith(
          activities: [...state.activities, ...activities],
          isLoading: false,
          lastUpdated: DateTime.now(),
          hasMore: activities.length >= 50,
        );
      }

      LoggerService.info('Fetched ${activities.length} activities successfully');
    } catch (e) {
      LoggerService.error('Error fetching activities', e);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to fetch activities: ${e.toString()}',
      );
    }
  }

  /// Load more activities (pagination)
  Future<void> loadMore() async {
    if (state.isLoading || !state.hasMore) return;

    _currentPage++;
    await fetchActivities();
  }

  /// Refresh current activities
  Future<void> refresh() async {
    await fetchActivities(refresh: true);
  }

  /// Update filter and fetch activities
  Future<void> updateFilter(ActivityFilter filter) async {
    _currentFilter = filter;
    _currentPage = 1;
    await fetchActivities(refresh: true);
  }

  /// Clear current state
  void clearState() {
    state = const ActivityMonitoringState();
    _currentPage = 1;
  }

  /// Get current filter
  ActivityFilter get currentFilter => _currentFilter;
}

/// Engagement metrics state
class EngagementMetricsState {
  final UserEngagementMetrics? metrics;
  final bool isLoading;
  final String? error;

  const EngagementMetricsState({
    this.metrics,
    this.isLoading = false,
    this.error,
  });

  EngagementMetricsState copyWith({
    UserEngagementMetrics? metrics,
    bool? isLoading,
    String? error,
  }) {
    return EngagementMetricsState(
      metrics: metrics ?? this.metrics,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  bool get hasData => metrics != null;
  bool get hasError => error != null;
}

/// Engagement metrics notifier
class EngagementMetricsNotifier extends StateNotifier<EngagementMetricsState> {
  final UserActivityMonitoringService _monitoringService;

  EngagementMetricsNotifier(this._monitoringService) : super(const EngagementMetricsState());

  /// Fetch engagement metrics for a user
  Future<void> fetchEngagementMetrics({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      LoggerService.info('Fetching engagement metrics for user: $userId');
      
      state = state.copyWith(isLoading: true, error: null);

      final metrics = await _monitoringService.getUserEngagementMetrics(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );

      state = state.copyWith(
        metrics: metrics,
        isLoading: false,
      );

      LoggerService.info('Engagement metrics fetched successfully');
    } catch (e) {
      LoggerService.error('Error fetching engagement metrics', e);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to fetch engagement metrics: ${e.toString()}',
      );
    }
  }

  /// Clear current state
  void clearState() {
    state = const EngagementMetricsState();
  }
}

/// Activity heatmap state
class ActivityHeatmapState {
  final Map<DateTime, int> heatmapData;
  final bool isLoading;
  final String? error;

  const ActivityHeatmapState({
    this.heatmapData = const {},
    this.isLoading = false,
    this.error,
  });

  ActivityHeatmapState copyWith({
    Map<DateTime, int>? heatmapData,
    bool? isLoading,
    String? error,
  }) {
    return ActivityHeatmapState(
      heatmapData: heatmapData ?? this.heatmapData,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  bool get hasData => heatmapData.isNotEmpty;
  bool get hasError => error != null;
}

/// Activity heatmap notifier
class ActivityHeatmapNotifier extends StateNotifier<ActivityHeatmapState> {
  final UserActivityMonitoringService _monitoringService;

  ActivityHeatmapNotifier(this._monitoringService) : super(const ActivityHeatmapState());

  /// Fetch heatmap data
  Future<void> fetchHeatmapData({
    UserActivityType? activityType,
    DateTime? startDate,
    DateTime? endDate,
    ActivityScope scope = ActivityScope.allUsers,
  }) async {
    try {
      LoggerService.info('Fetching activity heatmap data');
      
      state = state.copyWith(isLoading: true, error: null);

      final heatmapData = await _monitoringService.getActivityHeatmapData(
        activityType: activityType,
        startDate: startDate,
        endDate: endDate,
        scope: scope,
      );

      state = state.copyWith(
        heatmapData: heatmapData,
        isLoading: false,
      );

      LoggerService.info('Heatmap data fetched successfully');
    } catch (e) {
      LoggerService.error('Error fetching heatmap data', e);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to fetch heatmap data: ${e.toString()}',
      );
    }
  }

  /// Clear current state
  void clearState() {
    state = const ActivityHeatmapState();
  }
}

/// Activity monitoring service provider
final activityMonitoringServiceProvider = Provider<UserActivityMonitoringService>((ref) {
  return UserActivityMonitoringService();
});

/// Activity monitoring provider
final activityMonitoringProvider = StateNotifierProvider<ActivityMonitoringNotifier, ActivityMonitoringState>((ref) {
  final service = ref.watch(activityMonitoringServiceProvider);
  return ActivityMonitoringNotifier(service);
});

/// Engagement metrics provider
final engagementMetricsProvider = StateNotifierProvider<EngagementMetricsNotifier, EngagementMetricsState>((ref) {
  final service = ref.watch(activityMonitoringServiceProvider);
  return EngagementMetricsNotifier(service);
});

/// Activity heatmap provider
final activityHeatmapProvider = StateNotifierProvider<ActivityHeatmapNotifier, ActivityHeatmapState>((ref) {
  final service = ref.watch(activityMonitoringServiceProvider);
  return ActivityHeatmapNotifier(service);
});

/// Available activity types provider
final activityTypesProvider = Provider<List<UserActivityType>>((ref) {
  return UserActivityType.values;
});

/// Available activity scopes provider
final activityScopesProvider = Provider<List<ActivityScope>>((ref) {
  return ActivityScope.values;
});
