import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/services/admin_audit_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_audit_models.dart';

/// Audit log state
class AuditLogState {
  final List<AdminAuditEntry> logs;
  final bool isLoading;
  final String? error;
  final AuditLogFilter filter;
  final int totalCount;
  final AuditStatistics? statistics;
  final bool isExporting;
  final String? exportError;

  const AuditLogState({
    this.logs = const [],
    this.isLoading = false,
    this.error,
    this.filter = const AuditLogFilter(),
    this.totalCount = 0,
    this.statistics,
    this.isExporting = false,
    this.exportError,
  });

  AuditLogState copyWith({
    List<AdminAuditEntry>? logs,
    bool? isLoading,
    String? error,
    AuditLogFilter? filter,
    int? totalCount,
    AuditStatistics? statistics,
    bool? isExporting,
    String? exportError,
  }) {
    return AuditLogState(
      logs: logs ?? this.logs,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      filter: filter ?? this.filter,
      totalCount: totalCount ?? this.totalCount,
      statistics: statistics ?? this.statistics,
      isExporting: isExporting ?? this.isExporting,
      exportError: exportError,
    );
  }

  bool get hasLogs => logs.isNotEmpty;
  int get currentPage => filter.page;
  int get totalPages => (totalCount / filter.perPage).ceil();
}

/// Audit log provider
class AuditLogNotifier extends StateNotifier<AuditLogState> {
  final AdminAuditService _auditService;

  AuditLogNotifier(this._auditService) : super(const AuditLogState()) {
    loadAuditLogs();
    loadStatistics();
  }

  /// Load audit logs with current filter
  Future<void> loadAuditLogs() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final filter = state.filter;
      
      // Load logs and total count in parallel
      final futures = await Future.wait([
        _auditService.getAuditLogs(
          adminId: filter.adminId,
          action: filter.action,
          actionType: filter.actionType,
          severity: filter.severity,
          targetUserId: filter.targetUserId,
          startDate: filter.startDate,
          endDate: filter.endDate,
          searchQuery: filter.searchQuery.isNotEmpty ? filter.searchQuery : null,
          page: filter.page,
          perPage: filter.perPage,
          sortBy: filter.sortBy,
        ),
        _auditService.getAuditLogCount(
          adminId: filter.adminId,
          action: filter.action,
          actionType: filter.actionType,
          severity: filter.severity,
          targetUserId: filter.targetUserId,
          startDate: filter.startDate,
          endDate: filter.endDate,
          searchQuery: filter.searchQuery.isNotEmpty ? filter.searchQuery : null,
        ),
      ]);

      final logs = futures[0] as List<AdminAuditEntry>;
      final totalCount = futures[1] as int;

      state = state.copyWith(
        logs: logs,
        totalCount: totalCount,
        isLoading: false,
      );

      LoggerService.info('Loaded ${logs.length} audit log entries');
    } catch (e) {
      LoggerService.error('Failed to load audit logs', e);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load audit logs: ${e.toString()}',
      );
    }
  }

  /// Load audit statistics
  Future<void> loadStatistics() async {
    try {
      final statistics = await _auditService.getAuditStatistics();
      state = state.copyWith(statistics: statistics);
      LoggerService.info('Loaded audit statistics');
    } catch (e) {
      LoggerService.error('Failed to load audit statistics', e);
    }
  }

  /// Update filter and reload logs
  Future<void> updateFilter(AuditLogFilter newFilter) async {
    state = state.copyWith(filter: newFilter);
    await loadAuditLogs();
  }

  /// Update search query
  Future<void> updateSearchQuery(String query) async {
    final newFilter = state.filter.copyWith(
      searchQuery: query,
      page: 1, // Reset to first page
    );
    await updateFilter(newFilter);
  }

  /// Update admin filter
  Future<void> updateAdminFilter(String? adminId) async {
    final newFilter = state.filter.copyWith(
      adminId: adminId,
      page: 1, // Reset to first page
    );
    await updateFilter(newFilter);
  }

  /// Update action type filter
  Future<void> updateActionTypeFilter(AdminActionType? actionType) async {
    final newFilter = state.filter.copyWith(
      actionType: actionType,
      page: 1, // Reset to first page
    );
    await updateFilter(newFilter);
  }

  /// Update severity filter
  Future<void> updateSeverityFilter(AdminActionSeverity? severity) async {
    final newFilter = state.filter.copyWith(
      severity: severity,
      page: 1, // Reset to first page
    );
    await updateFilter(newFilter);
  }

  /// Update date range filter
  Future<void> updateDateRangeFilter(DateTime? startDate, DateTime? endDate) async {
    final newFilter = state.filter.copyWith(
      startDate: startDate,
      endDate: endDate,
      page: 1, // Reset to first page
    );
    await updateFilter(newFilter);
  }

  /// Go to specific page
  Future<void> goToPage(int page) async {
    final newFilter = state.filter.copyWith(page: page);
    await updateFilter(newFilter);
  }

  /// Go to next page
  Future<void> nextPage() async {
    if (state.currentPage < state.totalPages) {
      await goToPage(state.currentPage + 1);
    }
  }

  /// Go to previous page
  Future<void> previousPage() async {
    if (state.currentPage > 1) {
      await goToPage(state.currentPage - 1);
    }
  }

  /// Export audit logs
  Future<String?> exportAuditLogs(AuditExportRequest request) async {
    try {
      state = state.copyWith(isExporting: true, exportError: null);

      final exportData = await _auditService.exportAuditLogs(request);
      
      state = state.copyWith(isExporting: false);
      
      LoggerService.info('Audit logs exported successfully');
      return exportData;
    } catch (e) {
      LoggerService.error('Failed to export audit logs', e);
      state = state.copyWith(
        isExporting: false,
        exportError: 'Failed to export audit logs: ${e.toString()}',
      );
      return null;
    }
  }

  /// Refresh audit logs
  Future<void> refresh() async {
    await Future.wait([
      loadAuditLogs(),
      loadStatistics(),
    ]);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clear export error
  void clearExportError() {
    state = state.copyWith(exportError: null);
  }

  /// Reset filters
  Future<void> resetFilters() async {
    await updateFilter(const AuditLogFilter());
  }

  /// Get security events
  Future<void> loadSecurityEvents() async {
    final securityFilter = state.filter.copyWith(
      severity: AdminActionSeverity.security,
      page: 1,
    );
    await updateFilter(securityFilter);
  }

  /// Get critical events
  Future<void> loadCriticalEvents() async {
    final criticalFilter = state.filter.copyWith(
      severity: AdminActionSeverity.critical,
      page: 1,
    );
    await updateFilter(criticalFilter);
  }

  /// Get today's logs
  Future<void> loadTodayLogs() async {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = DateTime(today.year, today.month, today.day, 23, 59, 59);
    
    final todayFilter = state.filter.copyWith(
      startDate: todayStart,
      endDate: todayEnd,
      page: 1,
    );
    await updateFilter(todayFilter);
  }
}

/// Provider for audit service
final auditServiceProvider = Provider<AdminAuditService>((ref) {
  return AdminAuditService();
});

/// Provider for audit log state
final auditLogProvider = StateNotifierProvider<AuditLogNotifier, AuditLogState>((ref) {
  final auditService = ref.watch(auditServiceProvider);
  return AuditLogNotifier(auditService);
});

/// Provider for audit statistics
final auditStatisticsProvider = Provider<AuditStatistics?>((ref) {
  final auditLogState = ref.watch(auditLogProvider);
  return auditLogState.statistics;
});

/// Provider for security events count
final securityEventsCountProvider = Provider<int>((ref) {
  final statistics = ref.watch(auditStatisticsProvider);
  return statistics?.securityEvents ?? 0;
});

/// Provider for critical events count
final criticalEventsCountProvider = Provider<int>((ref) {
  final statistics = ref.watch(auditStatisticsProvider);
  return statistics?.criticalEvents ?? 0;
});

/// Provider for today's audit logs count
final todayAuditLogsCountProvider = Provider<int>((ref) {
  final statistics = ref.watch(auditStatisticsProvider);
  return statistics?.todayEntries ?? 0;
});

/// Provider for pagination info
final auditPaginationInfoProvider = Provider<Map<String, int>>((ref) {
  final auditLogState = ref.watch(auditLogProvider);
  final filter = auditLogState.filter;
  final totalCount = auditLogState.totalCount;
  
  final totalPages = (totalCount / filter.perPage).ceil();
  final startItem = (filter.page - 1) * filter.perPage + 1;
  final endItem = (filter.page * filter.perPage).clamp(0, totalCount);
  
  return {
    'currentPage': filter.page,
    'totalPages': totalPages,
    'totalCount': totalCount,
    'startItem': startItem,
    'endItem': endItem,
    'perPage': filter.perPage,
  };
});

/// Provider for filtered logs count
final filteredAuditLogsCountProvider = Provider<int>((ref) {
  final auditLogState = ref.watch(auditLogProvider);
  return auditLogState.totalCount;
});
