import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/routes/admin_routes.dart';

/// Provider for admin navigation state
final adminNavigationProvider = StateNotifierProvider<AdminNavigationNotifier, AdminNavigationState>((ref) {
  return AdminNavigationNotifier();
});

/// Provider for current route
final currentRouteProvider = Provider<String>((ref) {
  final navigationState = ref.watch(adminNavigationProvider);
  return navigationState.currentRoute;
});

/// Provider for breadcrumbs
final breadcrumbsProvider = Provider<List<BreadcrumbItem>>((ref) {
  final navigationState = ref.watch(adminNavigationProvider);
  return navigationState.breadcrumbs;
});

/// Provider for navigation history
final navigationHistoryProvider = Provider<List<String>>((ref) {
  final navigationState = ref.watch(adminNavigationProvider);
  return navigationState.navigationHistory;
});

/// Admin navigation state
class AdminNavigationState {
  final String currentRoute;
  final List<BreadcrumbItem> breadcrumbs;
  final List<String> navigationHistory;
  final Map<String, String> routeParameters;
  final bool isLoading;
  final String? error;

  const AdminNavigationState({
    this.currentRoute = '',
    this.breadcrumbs = const [],
    this.navigationHistory = const [],
    this.routeParameters = const {},
    this.isLoading = false,
    this.error,
  });

  AdminNavigationState copyWith({
    String? currentRoute,
    List<BreadcrumbItem>? breadcrumbs,
    List<String>? navigationHistory,
    Map<String, String>? routeParameters,
    bool? isLoading,
    String? error,
  }) {
    return AdminNavigationState(
      currentRoute: currentRoute ?? this.currentRoute,
      breadcrumbs: breadcrumbs ?? this.breadcrumbs,
      navigationHistory: navigationHistory ?? this.navigationHistory,
      routeParameters: routeParameters ?? this.routeParameters,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdminNavigationState &&
        other.currentRoute == currentRoute &&
        other.isLoading == isLoading &&
        other.error == error;
  }

  @override
  int get hashCode {
    return currentRoute.hashCode ^ isLoading.hashCode ^ error.hashCode;
  }
}

/// Admin navigation state notifier
class AdminNavigationNotifier extends StateNotifier<AdminNavigationState> {
  static const int maxHistoryLength = 50;

  AdminNavigationNotifier() : super(const AdminNavigationState());

  /// Navigate to a specific route
  void navigateTo(
    String route, {
    Map<String, String>? parameters,
    bool addToHistory = true,
  }) {
    try {
      LoggerService.info('Navigating to route: $route');

      // Validate route exists
      if (!AdminRoutes.routeExists(route)) {
        LoggerService.warning('Invalid route: $route');
        _setError('Invalid route: $route');
        return;
      }

      // Build final route with parameters
      String finalRoute = route;
      if (parameters != null && parameters.isNotEmpty) {
        finalRoute = AdminRoutes.buildRouteWithParams(route, parameters);
      }

      // Generate breadcrumbs
      final breadcrumbs = AdminRoutes.generateBreadcrumbs(route);

      // Update navigation history
      List<String> updatedHistory = List.from(state.navigationHistory);
      if (addToHistory && route != state.currentRoute) {
        updatedHistory.add(route);
        
        // Limit history size
        if (updatedHistory.length > maxHistoryLength) {
          updatedHistory = updatedHistory.sublist(updatedHistory.length - maxHistoryLength);
        }
      }

      state = state.copyWith(
        currentRoute: finalRoute,
        breadcrumbs: breadcrumbs,
        navigationHistory: updatedHistory,
        routeParameters: parameters ?? {},
        error: null,
      );

      LoggerService.info('Navigation updated: $finalRoute');
    } catch (e) {
      LoggerService.error('Error navigating to route: $route', e);
      _setError('Navigation failed: ${e.toString()}');
    }
  }

  /// Go back to previous route
  void goBack() {
    try {
      if (state.navigationHistory.isNotEmpty) {
        final previousRoute = state.navigationHistory.last;
        final updatedHistory = List<String>.from(state.navigationHistory)..removeLast();
        
        LoggerService.info('Going back to: $previousRoute');
        
        state = state.copyWith(
          currentRoute: previousRoute,
          breadcrumbs: AdminRoutes.generateBreadcrumbs(previousRoute),
          navigationHistory: updatedHistory,
          routeParameters: {},
          error: null,
        );
      } else {
        LoggerService.info('No navigation history, going to dashboard');
        navigateTo(AdminRoutes.dashboard, addToHistory: false);
      }
    } catch (e) {
      LoggerService.error('Error going back', e);
      _setError('Navigation back failed: ${e.toString()}');
    }
  }

  /// Update current route (for external navigation)
  void updateCurrentRoute(String route, {Map<String, String>? parameters}) {
    try {
      LoggerService.info('Updating current route: $route');

      final breadcrumbs = AdminRoutes.generateBreadcrumbs(route);
      
      state = state.copyWith(
        currentRoute: route,
        breadcrumbs: breadcrumbs,
        routeParameters: parameters ?? {},
        error: null,
      );
    } catch (e) {
      LoggerService.error('Error updating current route: $route', e);
      _setError('Route update failed: ${e.toString()}');
    }
  }

  /// Clear navigation history
  void clearHistory() {
    LoggerService.info('Clearing navigation history');
    
    state = state.copyWith(
      navigationHistory: [],
      error: null,
    );
  }

  /// Get route title
  String getRouteTitle(String? route) {
    if (route == null || route.isEmpty) return 'Admin Portal';
    return AdminRoutes.getRouteTitle(route);
  }

  /// Check if can go back
  bool get canGoBack => state.navigationHistory.isNotEmpty;

  /// Get current page title
  String get currentPageTitle => getRouteTitle(state.currentRoute);

  /// Get navigation analytics data
  Map<String, dynamic> getNavigationAnalytics() {
    return {
      'current_route': state.currentRoute,
      'history_length': state.navigationHistory.length,
      'breadcrumb_depth': state.breadcrumbs.length,
      'has_parameters': state.routeParameters.isNotEmpty,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Set loading state
  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  /// Set error state
  void _setError(String error) {
    state = state.copyWith(error: error, isLoading: false);
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Reset navigation state
  void reset() {
    LoggerService.info('Resetting navigation state');
    
    state = const AdminNavigationState();
  }

  /// Get most visited routes
  List<String> getMostVisitedRoutes({int limit = 5}) {
    final routeCounts = <String, int>{};
    
    for (final route in state.navigationHistory) {
      routeCounts[route] = (routeCounts[route] ?? 0) + 1;
    }
    
    final sortedRoutes = routeCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedRoutes
        .take(limit)
        .map((entry) => entry.key)
        .toList();
  }

  /// Check if route is in history
  bool isRouteInHistory(String route) {
    return state.navigationHistory.contains(route);
  }

  /// Get route visit count
  int getRouteVisitCount(String route) {
    return state.navigationHistory.where((r) => r == route).length;
  }
}
