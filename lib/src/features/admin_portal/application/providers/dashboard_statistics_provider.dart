import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/services/dashboard_statistics_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/dashboard_models.dart';

/// Dashboard statistics state
class DashboardStatisticsState {
  final PlatformStatistics? statistics;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;
  final bool isRefreshing;

  const DashboardStatisticsState({
    this.statistics,
    this.isLoading = false,
    this.error,
    this.lastUpdated,
    this.isRefreshing = false,
  });

  DashboardStatisticsState copyWith({
    PlatformStatistics? statistics,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
    bool? isRefreshing,
  }) {
    return DashboardStatisticsState(
      statistics: statistics ?? this.statistics,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  bool get hasData => statistics != null;
  bool get isStale => statistics?.isStale ?? true;
}

/// Dashboard statistics provider
class DashboardStatisticsNotifier extends StateNotifier<DashboardStatisticsState> {
  final DashboardStatisticsService _statisticsService;
  Timer? _refreshTimer;
  Timer? _autoRefreshTimer;

  DashboardStatisticsNotifier(this._statisticsService) : super(const DashboardStatisticsState()) {
    _loadInitialData();
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _autoRefreshTimer?.cancel();
    super.dispose();
  }

  /// Load initial dashboard statistics
  Future<void> _loadInitialData() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Try to get cached data first
      final cachedStats = await _statisticsService.getCachedStatistics();
      if (cachedStats != null && !cachedStats.isStale) {
        state = state.copyWith(
          statistics: cachedStats,
          isLoading: false,
          lastUpdated: DateTime.now(),
        );
        LoggerService.info('Loaded cached dashboard statistics');
        return;
      }

      // Fetch fresh data
      await _fetchFreshData();
    } catch (e) {
      LoggerService.error('Failed to load initial dashboard statistics', e);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load dashboard statistics: ${e.toString()}',
      );
    }
  }

  /// Fetch fresh statistics data
  Future<void> _fetchFreshData() async {
    try {
      final statistics = await _statisticsService.getPlatformStatistics();
      
      // Cache the new data
      await _statisticsService.cacheStatistics(statistics);
      
      state = state.copyWith(
        statistics: statistics,
        isLoading: false,
        isRefreshing: false,
        error: null,
        lastUpdated: DateTime.now(),
      );

      LoggerService.info('Dashboard statistics updated successfully');
    } catch (e) {
      LoggerService.error('Failed to fetch fresh dashboard statistics', e);
      
      state = state.copyWith(
        isLoading: false,
        isRefreshing: false,
        error: 'Failed to update statistics: ${e.toString()}',
      );
    }
  }

  /// Refresh statistics manually
  Future<void> refresh() async {
    if (state.isLoading || state.isRefreshing) return;

    try {
      state = state.copyWith(isRefreshing: true, error: null);
      await _fetchFreshData();
    } catch (e) {
      LoggerService.error('Failed to refresh dashboard statistics', e);
      state = state.copyWith(
        isRefreshing: false,
        error: 'Failed to refresh statistics: ${e.toString()}',
      );
    }
  }

  /// Start auto-refresh timer
  void _startAutoRefresh() {
    _autoRefreshTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (state.isStale && !state.isLoading && !state.isRefreshing) {
        LoggerService.info('Auto-refreshing dashboard statistics');
        refresh();
      }
    });
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Force refresh with loading state
  Future<void> forceRefresh() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _fetchFreshData();
    } catch (e) {
      LoggerService.error('Failed to force refresh dashboard statistics', e);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to refresh statistics: ${e.toString()}',
      );
    }
  }

  /// Get specific KPI value
  String getKPIValue(DashboardKPI kpi) {
    final stats = state.statistics;
    if (stats == null) return '0';

    switch (kpi) {
      case DashboardKPI.totalUsers:
        return stats.totalUsers.toString();
      case DashboardKPI.activeUsers:
        return (stats.activeSolicitors + stats.activeCoFunders + stats.activeClaimants).toString();
      case DashboardKPI.newRegistrations:
        return stats.newRegistrationsWeek.toString();
      case DashboardKPI.totalClaims:
        return stats.totalClaims.toString();
      case DashboardKPI.activeClaims:
        return stats.activeClaims.toString();
      case DashboardKPI.fundingVolume:
        return stats.formattedFundingVolume;
      case DashboardKPI.systemHealth:
        return stats.systemHealth.status.displayName;
      case DashboardKPI.recentActivity:
        return stats.recentActivities.length.toString();
    }
  }

  /// Get KPI trend
  TrendIndicator? getKPITrend(DashboardKPI kpi) {
    final stats = state.statistics;
    if (stats == null) return null;

    switch (kpi) {
      case DashboardKPI.newRegistrations:
        return stats.registrationTrend;
      case DashboardKPI.activeClaims:
        return stats.claimsTrend;
      default:
        return null;
    }
  }

  /// Get KPI subtitle
  String? getKPISubtitle(DashboardKPI kpi) {
    final stats = state.statistics;
    if (stats == null) return null;

    switch (kpi) {
      case DashboardKPI.totalUsers:
        return '+${stats.newRegistrationsWeek} this week';
      case DashboardKPI.activeUsers:
        return '${stats.activeSolicitors} solicitors, ${stats.activeCoFunders} co-funders';
      case DashboardKPI.newRegistrations:
        return 'This week';
      case DashboardKPI.totalClaims:
        return '${stats.activeClaims} active';
      case DashboardKPI.activeClaims:
        return 'Currently in progress';
      case DashboardKPI.fundingVolume:
        return 'Total platform volume';
      case DashboardKPI.systemHealth:
        return stats.systemHealth.description;
      case DashboardKPI.recentActivity:
        return 'Recent actions';
    }
  }
}

/// Provider for dashboard statistics service
final dashboardStatisticsServiceProvider = Provider<DashboardStatisticsService>((ref) {
  return DashboardStatisticsService();
});

/// Provider for dashboard statistics state
final dashboardStatisticsProvider = StateNotifierProvider<DashboardStatisticsNotifier, DashboardStatisticsState>((ref) {
  final statisticsService = ref.watch(dashboardStatisticsServiceProvider);
  return DashboardStatisticsNotifier(statisticsService);
});

/// Provider for platform statistics
final platformStatisticsProvider = Provider<PlatformStatistics?>((ref) {
  final dashboardState = ref.watch(dashboardStatisticsProvider);
  return dashboardState.statistics;
});

/// Provider for system health
final systemHealthProvider = Provider<SystemHealth?>((ref) {
  final statistics = ref.watch(platformStatisticsProvider);
  return statistics?.systemHealth;
});

/// Provider for recent activities
final recentActivitiesProvider = Provider<List<RecentActivity>>((ref) {
  final statistics = ref.watch(platformStatisticsProvider);
  return statistics?.recentActivities ?? [];
});

/// Provider for user statistics
final userStatisticsProvider = Provider<Map<String, int>>((ref) {
  final statistics = ref.watch(platformStatisticsProvider);
  if (statistics == null) return {};
  
  return {
    'total': statistics.totalUsers,
    'solicitors': statistics.activeSolicitors,
    'coFunders': statistics.activeCoFunders,
    'claimants': statistics.activeClaimants,
    'newToday': statistics.newRegistrationsToday,
    'newWeek': statistics.newRegistrationsWeek,
    'newMonth': statistics.newRegistrationsMonth,
  };
});

/// Provider for claim statistics
final claimStatisticsProvider = Provider<Map<String, int>>((ref) {
  final statistics = ref.watch(platformStatisticsProvider);
  if (statistics == null) return {};
  
  return {
    'total': statistics.totalClaims,
    'active': statistics.activeClaims,
    ...statistics.claimsByStatus,
  };
});

/// Provider for funding statistics
final fundingStatisticsProvider = Provider<Map<String, dynamic>>((ref) {
  final statistics = ref.watch(platformStatisticsProvider);
  if (statistics == null) return {};
  
  return {
    'totalVolume': statistics.totalFundingVolume,
    'formattedVolume': statistics.formattedFundingVolume,
    'byMonth': statistics.fundingByMonth,
  };
});

/// Provider for dashboard loading state
final dashboardLoadingProvider = Provider<bool>((ref) {
  final dashboardState = ref.watch(dashboardStatisticsProvider);
  return dashboardState.isLoading;
});

/// Provider for dashboard error state
final dashboardErrorProvider = Provider<String?>((ref) {
  final dashboardState = ref.watch(dashboardStatisticsProvider);
  return dashboardState.error;
});

/// Provider for dashboard refresh state
final dashboardRefreshingProvider = Provider<bool>((ref) {
  final dashboardState = ref.watch(dashboardStatisticsProvider);
  return dashboardState.isRefreshing;
});
