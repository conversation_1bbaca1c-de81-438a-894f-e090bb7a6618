import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/services/admin_auth_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_auth_provider.dart';

/// Admin navigation items
enum AdminNavigationItem {
  dashboard('dashboard', 'Dashboard', 'view_dashboard'),
  userManagement('users', 'User Management', 'manage_users'),
  contentManagement('content', 'Content Management', 'manage_content'),
  analytics('analytics', 'Analytics', 'view_analytics'),
  notifications('notifications', 'Notifications', 'manage_notifications'),
  auditLogs('audit-logs', 'Audit Logs', 'view_audit_logs'),
  settings('settings', 'Settings', 'manage_settings');

  const AdminNavigationItem(this.route, this.label, this.permission);

  final String route;
  final String label;
  final String permission;
}

/// Dashboard quick stats model
class DashboardStats {
  final int totalUsers;
  final int totalSolicitors;
  final int totalCoFunders;
  final int totalClaimants;
  final int totalClaims;
  final int activeClaims;
  final double totalFundingAmount;
  final int pendingApprovals;
  final int todaySignups;
  final int unreadNotifications;
  final DateTime lastUpdated;

  const DashboardStats({
    required this.totalUsers,
    required this.totalSolicitors,
    required this.totalCoFunders,
    required this.totalClaimants,
    required this.totalClaims,
    required this.activeClaims,
    required this.totalFundingAmount,
    required this.pendingApprovals,
    required this.todaySignups,
    required this.unreadNotifications,
    required this.lastUpdated,
  });

  DashboardStats copyWith({
    int? totalUsers,
    int? totalSolicitors,
    int? totalCoFunders,
    int? totalClaimants,
    int? totalClaims,
    int? activeClaims,
    double? totalFundingAmount,
    int? pendingApprovals,
    int? todaySignups,
    int? unreadNotifications,
    DateTime? lastUpdated,
  }) {
    return DashboardStats(
      totalUsers: totalUsers ?? this.totalUsers,
      totalSolicitors: totalSolicitors ?? this.totalSolicitors,
      totalCoFunders: totalCoFunders ?? this.totalCoFunders,
      totalClaimants: totalClaimants ?? this.totalClaimants,
      totalClaims: totalClaims ?? this.totalClaims,
      activeClaims: activeClaims ?? this.activeClaims,
      totalFundingAmount: totalFundingAmount ?? this.totalFundingAmount,
      pendingApprovals: pendingApprovals ?? this.pendingApprovals,
      todaySignups: todaySignups ?? this.todaySignups,
      unreadNotifications: unreadNotifications ?? this.unreadNotifications,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

/// Admin dashboard state
class AdminDashboardState {
  final AdminNavigationItem selectedNavItem;
  final bool isLoading;
  final String? error;
  final DashboardStats? stats;
  final bool sidebarCollapsed;
  final DateTime? lastRefresh;

  const AdminDashboardState({
    this.selectedNavItem = AdminNavigationItem.dashboard,
    this.isLoading = false,
    this.error,
    this.stats,
    this.sidebarCollapsed = false,
    this.lastRefresh,
  });

  AdminDashboardState copyWith({
    AdminNavigationItem? selectedNavItem,
    bool? isLoading,
    String? error,
    DashboardStats? stats,
    bool? sidebarCollapsed,
    DateTime? lastRefresh,
  }) {
    return AdminDashboardState(
      selectedNavItem: selectedNavItem ?? this.selectedNavItem,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      stats: stats ?? this.stats,
      sidebarCollapsed: sidebarCollapsed ?? this.sidebarCollapsed,
      lastRefresh: lastRefresh ?? this.lastRefresh,
    );
  }
}

/// Admin dashboard provider
class AdminDashboardNotifier extends StateNotifier<AdminDashboardState> {
  final AdminAuthService _authService;
  final PocketBaseService _pocketBaseService;

  AdminDashboardNotifier(this._authService, this._pocketBaseService)
    : super(const AdminDashboardState()) {
    _loadDashboardStats();
  }

  /// Load dashboard statistics
  Future<void> _loadDashboardStats() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Load stats from PocketBase
      final stats = await _fetchDashboardStats();

      state = state.copyWith(
        isLoading: false,
        stats: stats,
        lastRefresh: DateTime.now(),
      );

      LoggerService.info('Dashboard stats loaded successfully');
    } catch (e) {
      LoggerService.error('Failed to load dashboard stats', e);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load dashboard statistics',
      );
    }
  }

  /// Fetch dashboard statistics from PocketBase
  Future<DashboardStats> _fetchDashboardStats() async {
    try {
      // Get total users count
      final usersResult = await _pocketBaseService.getList(
        collectionName: 'users',
        perPage: 1,
      );
      final totalUsers = usersResult.totalItems;

      // Get solicitors count
      final solicitorsResult = await _pocketBaseService.getList(
        collectionName: 'users',
        filter: 'user_type = "solicitor"',
        perPage: 1,
      );
      final totalSolicitors = solicitorsResult.totalItems;

      // Get co-funders count
      final coFundersResult = await _pocketBaseService.getList(
        collectionName: 'users',
        filter: 'user_type = "co_funder"',
        perPage: 1,
      );
      final totalCoFunders = coFundersResult.totalItems;

      // Get claimants count
      final claimantsResult = await _pocketBaseService.getList(
        collectionName: 'users',
        filter: 'user_type = "claimant"',
        perPage: 1,
      );
      final totalClaimants = claimantsResult.totalItems;

      // Get total claims count
      final claimsResult = await _pocketBaseService.getList(
        collectionName: 'funding_applications',
        perPage: 1,
      );
      final totalClaims = claimsResult.totalItems;

      // Get active claims count
      final activeClaimsResult = await _pocketBaseService.getList(
        collectionName: 'funding_applications',
        filter: 'status != "completed" && status != "rejected"',
        perPage: 1,
      );
      final activeClaims = activeClaimsResult.totalItems;

      // Get pending approvals count (solicitors and co-funders)
      final pendingSolicitorsResult = await _pocketBaseService.getList(
        collectionName: 'solicitor_profiles',
        filter: 'status = "pending"',
        perPage: 1,
      );
      final pendingCoFundersResult = await _pocketBaseService.getList(
        collectionName: 'co_funder_profiles',
        filter: 'status = "pending"',
        perPage: 1,
      );
      final pendingApprovals =
          pendingSolicitorsResult.totalItems +
          pendingCoFundersResult.totalItems;

      // Get today's signups
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);
      final todaySignupsResult = await _pocketBaseService.getList(
        collectionName: 'users',
        filter: 'created >= "${todayStart.toIso8601String()}"',
        perPage: 1,
      );
      final todaySignups = todaySignupsResult.totalItems;

      // Calculate total funding amount (placeholder - would need actual calculation)
      double totalFundingAmount = 0.0;
      // TODO: Implement actual funding amount calculation from investments

      // Get unread notifications count (placeholder)
      int unreadNotifications = 0;
      // TODO: Implement actual unread notifications count

      return DashboardStats(
        totalUsers: totalUsers,
        totalSolicitors: totalSolicitors,
        totalCoFunders: totalCoFunders,
        totalClaimants: totalClaimants,
        totalClaims: totalClaims,
        activeClaims: activeClaims,
        totalFundingAmount: totalFundingAmount,
        pendingApprovals: pendingApprovals,
        todaySignups: todaySignups,
        unreadNotifications: unreadNotifications,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      LoggerService.error('Error fetching dashboard stats', e);
      rethrow;
    }
  }

  /// Refresh dashboard statistics
  Future<void> refreshStats() async {
    await _loadDashboardStats();
  }

  /// Set selected navigation item
  void setSelectedNavItem(AdminNavigationItem item) {
    state = state.copyWith(selectedNavItem: item);
    LoggerService.info('Admin navigation changed to: ${item.label}');
  }

  /// Toggle sidebar collapsed state
  void toggleSidebar() {
    state = state.copyWith(sidebarCollapsed: !state.sidebarCollapsed);
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Get available navigation items based on permissions
  List<AdminNavigationItem> getAvailableNavItems() {
    final currentUser = _authService.getCurrentAdminUser();
    if (currentUser == null) {
      return [];
    }

    final availableItems =
        AdminNavigationItem.values.where((item) {
          return currentUser.hasPermission(item.permission);
        }).toList();

    // Ensure dashboard is always available for authenticated admin users
    if (availableItems.isEmpty ||
        !availableItems.contains(AdminNavigationItem.dashboard)) {
      return [AdminNavigationItem.dashboard, ...availableItems];
    }

    return availableItems;
  }

  /// Check if navigation item is available
  bool isNavItemAvailable(AdminNavigationItem item) {
    final currentUser = _authService.getCurrentAdminUser();
    return currentUser?.hasPermission(item.permission) ?? false;
  }
}

/// Provider for admin dashboard state
final adminDashboardProvider =
    StateNotifierProvider<AdminDashboardNotifier, AdminDashboardState>((ref) {
      final authService = ref.watch(adminAuthServiceProvider);
      final pocketBaseService = PocketBaseService();
      return AdminDashboardNotifier(authService, pocketBaseService);
    });

/// Provider for available navigation items
final availableNavItemsProvider = Provider<List<AdminNavigationItem>>((ref) {
  // Watch auth state to trigger updates when user logs in/out
  final authState = ref.watch(adminAuthProvider);
  final dashboardNotifier = ref.watch(adminDashboardProvider.notifier);

  // Return empty list if not authenticated
  if (!authState.isAuthenticated) {
    return [];
  }

  return dashboardNotifier.getAvailableNavItems();
});

/// Provider for dashboard statistics
final dashboardStatsProvider = Provider<DashboardStats?>((ref) {
  final dashboardState = ref.watch(adminDashboardProvider);
  return dashboardState.stats;
});

/// Provider for selected navigation item
final selectedNavItemProvider = Provider<AdminNavigationItem>((ref) {
  final dashboardState = ref.watch(adminDashboardProvider);
  return dashboardState.selectedNavItem;
});

/// Provider for sidebar collapsed state
final sidebarCollapsedProvider = Provider<bool>((ref) {
  final dashboardState = ref.watch(adminDashboardProvider);
  return dashboardState.sidebarCollapsed;
});
