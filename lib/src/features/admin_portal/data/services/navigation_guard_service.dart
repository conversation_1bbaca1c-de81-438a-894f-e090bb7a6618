import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_user_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/routes/admin_routes.dart';

/// Service for handling navigation guards and route protection
class NavigationGuardService {
  static final NavigationGuardService _instance = NavigationGuardService._internal();
  
  NavigationGuardService._internal();
  
  factory NavigationGuardService() => _instance;

  /// Check if the current user can access the specified route
  Future<NavigationGuardResult> canActivate(
    String route,
    AdminUser? currentUser, {
    Map<String, String>? parameters,
  }) async {
    try {
      LoggerService.info('Checking route access: $route for user: ${currentUser?.email}');

      // Check authentication
      if (currentUser == null) {
        LoggerService.warning('Route access denied - user not authenticated: $route');
        return NavigationGuardResult.denied(
          reason: 'Authentication required',
          redirectRoute: AdminRoutes.login,
        );
      }

      // Check if user is admin
      if (currentUser.userType != 'admin') {
        LoggerService.warning('Route access denied - user not admin: $route');
        return NavigationGuardResult.denied(
          reason: 'Admin access required',
          redirectRoute: AdminRoutes.login,
        );
      }

      // Check if user account is active
      if (!currentUser.isActive) {
        LoggerService.warning('Route access denied - user account inactive: $route');
        return NavigationGuardResult.denied(
          reason: 'Account is inactive',
          redirectRoute: AdminRoutes.login,
        );
      }

      // Check route-specific permissions
      final hasPermission = await _hasPermissionForRoute(route, currentUser);
      if (!hasPermission) {
        LoggerService.warning('Route access denied - insufficient permissions: $route');
        return NavigationGuardResult.denied(
          reason: 'Insufficient permissions',
          redirectRoute: AdminRoutes.dashboard,
        );
      }

      // Validate route parameters if provided
      if (parameters != null) {
        final paramValidation = await _validateRouteParameters(route, parameters, currentUser);
        if (!paramValidation.isValid) {
          LoggerService.warning('Route access denied - invalid parameters: $route');
          return NavigationGuardResult.denied(
            reason: paramValidation.reason ?? 'Invalid route parameters',
            redirectRoute: AdminRoutes.dashboard,
          );
        }
      }

      LoggerService.info('Route access granted: $route');
      return NavigationGuardResult.allowed();
    } catch (e) {
      LoggerService.error('Error checking route access: $route', e);
      return NavigationGuardResult.denied(
        reason: 'Access check failed',
        redirectRoute: AdminRoutes.dashboard,
      );
    }
  }

  /// Check if user has permission for specific route
  Future<bool> _hasPermissionForRoute(String route, AdminUser user) async {
    try {
      final requiredPermissions = AdminRoutes.getRoutePermissions(route);
      
      // If no specific permissions required, allow access
      if (requiredPermissions.isEmpty) {
        return true;
      }

      // Check if user has all required permissions
      for (final permission in requiredPermissions) {
        if (!user.hasPermission(permission)) {
          LoggerService.info('User missing permission: $permission for route: $route');
          return false;
        }
      }

      return true;
    } catch (e) {
      LoggerService.error('Error checking permissions for route: $route', e);
      return false;
    }
  }

  /// Validate route parameters
  Future<ParameterValidationResult> _validateRouteParameters(
    String route,
    Map<String, String> parameters,
    AdminUser user,
  ) async {
    try {
      switch (route) {
        case AdminRoutes.userDetail:
          return await _validateUserDetailParameters(parameters, user);
        
        // Add more route-specific parameter validations as needed
        default:
          return ParameterValidationResult.valid();
      }
    } catch (e) {
      LoggerService.error('Error validating route parameters: $route', e);
      return ParameterValidationResult.invalid('Parameter validation failed');
    }
  }

  /// Validate user detail route parameters
  Future<ParameterValidationResult> _validateUserDetailParameters(
    Map<String, String> parameters,
    AdminUser user,
  ) async {
    final userId = parameters['userId'];
    
    if (userId == null || userId.isEmpty) {
      return ParameterValidationResult.invalid('User ID is required');
    }

    // Additional validation could include:
    // - Check if user exists
    // - Check if current admin can view this user
    // - Validate user ID format
    
    return ParameterValidationResult.valid();
  }

  /// Check session validity
  Future<bool> isSessionValid(AdminUser? user) async {
    try {
      if (user == null) return false;
      
      // Check if session has expired
      if (user.sessionExpiry != null && user.sessionExpiry!.isBefore(DateTime.now())) {
        LoggerService.warning('User session expired: ${user.email}');
        return false;
      }

      // Additional session validation logic could be added here
      // - Check with server for session validity
      // - Verify token freshness
      // - Check for concurrent sessions
      
      return true;
    } catch (e) {
      LoggerService.error('Error validating session', e);
      return false;
    }
  }

  /// Get redirect route for unauthorized access
  String getUnauthorizedRedirectRoute(String attemptedRoute, AdminUser? user) {
    if (user == null) {
      return AdminRoutes.login;
    }
    
    // If user is authenticated but lacks permissions, redirect to dashboard
    return AdminRoutes.dashboard;
  }

  /// Log navigation attempt for audit purposes
  Future<void> logNavigationAttempt({
    required String route,
    required AdminUser? user,
    required bool allowed,
    String? reason,
    Map<String, String>? parameters,
  }) async {
    try {
      final logData = {
        'route': route,
        'user_id': user?.id,
        'user_email': user?.email,
        'allowed': allowed,
        'reason': reason,
        'parameters': parameters,
        'timestamp': DateTime.now().toIso8601String(),
      };

      if (allowed) {
        LoggerService.info('Navigation allowed: $logData');
      } else {
        LoggerService.warning('Navigation denied: $logData');
      }

      // TODO: Send to audit logging service when implemented
      // await AdminAuditService().logAdminAction(
      //   action: 'navigation_attempt',
      //   adminId: user?.id ?? 'unknown',
      //   details: logData,
      //   severity: allowed ? AdminActionSeverity.info : AdminActionSeverity.warning,
      // );
    } catch (e) {
      LoggerService.error('Error logging navigation attempt', e);
    }
  }
}

/// Result of navigation guard check
class NavigationGuardResult {
  final bool isAllowed;
  final String? reason;
  final String? redirectRoute;

  const NavigationGuardResult._({
    required this.isAllowed,
    this.reason,
    this.redirectRoute,
  });

  factory NavigationGuardResult.allowed() {
    return const NavigationGuardResult._(isAllowed: true);
  }

  factory NavigationGuardResult.denied({
    required String reason,
    String? redirectRoute,
  }) {
    return NavigationGuardResult._(
      isAllowed: false,
      reason: reason,
      redirectRoute: redirectRoute,
    );
  }

  bool get isDenied => !isAllowed;
}

/// Result of parameter validation
class ParameterValidationResult {
  final bool isValid;
  final String? reason;

  const ParameterValidationResult._({
    required this.isValid,
    this.reason,
  });

  factory ParameterValidationResult.valid() {
    return const ParameterValidationResult._(isValid: true);
  }

  factory ParameterValidationResult.invalid(String reason) {
    return ParameterValidationResult._(
      isValid: false,
      reason: reason,
    );
  }

  bool get isInvalid => !isValid;
}
