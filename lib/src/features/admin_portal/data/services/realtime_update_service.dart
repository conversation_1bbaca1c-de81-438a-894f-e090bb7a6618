import 'dart:async';
import 'dart:math';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/dashboard_models.dart';

/// Service for managing real-time updates via PocketBase subscriptions
class RealtimeUpdateService {
  static final RealtimeUpdateService _instance =
      RealtimeUpdateService._internal();
  final PocketBaseService _pocketBaseService = PocketBaseService();

  // Subscription management
  final Map<String, dynamic> _subscriptions = {};
  final StreamController<RealtimeEvent> _eventController =
      StreamController<RealtimeEvent>.broadcast();

  // Connection state
  bool _isInitialized = false;
  bool _isConnecting = false;
  int _reconnectAttempts = 0;
  Timer? _reconnectTimer;
  Timer? _heartbeatTimer;

  // Configuration
  static const int maxReconnectAttempts = 5;
  static const Duration baseReconnectDelay = Duration(seconds: 2);
  static const Duration heartbeatInterval = Duration(seconds: 30);
  static const List<String> criticalCollections = [
    'users',
    'funding_applications',
    'notifications',
    'user_activity_logs',
    'user_activity_logs',
  ];

  RealtimeUpdateService._internal();

  factory RealtimeUpdateService() => _instance;

  /// Stream of real-time events
  Stream<RealtimeEvent> get eventStream => _eventController.stream;

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Check if currently connecting
  bool get isConnecting => _isConnecting;

  /// Initialize the real-time update service
  Future<void> initialize() async {
    if (_isInitialized || _isConnecting) return;

    _isConnecting = true;
    LoggerService.info('Initializing real-time update service');

    try {
      // Subscribe to critical collections
      await Future.wait(
        criticalCollections.map(
          (collection) => subscribeToCollection(collection),
        ),
      );

      // Start heartbeat to monitor connection
      _startHeartbeat();

      _isInitialized = true;
      _isConnecting = false;
      _reconnectAttempts = 0;

      LoggerService.info('Real-time update service initialized successfully');
    } catch (e) {
      _isConnecting = false;
      LoggerService.error('Failed to initialize real-time updates', e);

      // Schedule reconnection attempt
      _scheduleReconnect();
      rethrow;
    }
  }

  /// Subscribe to a specific collection
  Future<void> subscribeToCollection(String collection) async {
    try {
      LoggerService.info('Subscribing to collection: $collection');

      // Note: This is a placeholder implementation since PocketBase Dart SDK
      // real-time subscriptions need to be implemented based on the actual SDK
      // For now, we'll simulate the subscription

      _subscriptions[collection] = {
        'collection': collection,
        'subscribed_at': DateTime.now(),
        'active': true,
      };

      LoggerService.info('Successfully subscribed to collection: $collection');
    } catch (e) {
      LoggerService.error('Failed to subscribe to collection: $collection', e);
      rethrow;
    }
  }

  /// Unsubscribe from a specific collection
  Future<void> unsubscribeFromCollection(String collection) async {
    try {
      if (_subscriptions.containsKey(collection)) {
        // Unsubscribe logic would go here
        _subscriptions.remove(collection);
        LoggerService.info('Unsubscribed from collection: $collection');
      }
    } catch (e) {
      LoggerService.error(
        'Failed to unsubscribe from collection: $collection',
        e,
      );
    }
  }

  /// Simulate real-time event (for testing purposes)
  void simulateEvent(RealtimeEvent event) {
    if (_isInitialized) {
      _eventController.add(event);
      LoggerService.info(
        'Simulated real-time event: ${event.type.displayName}',
      );
    }
  }

  /// Handle incoming real-time events
  void _handleRealtimeEvent(
    String collection,
    String action,
    Map<String, dynamic> data,
  ) {
    try {
      final event = RealtimeEvent(
        id: data['id'] ?? _generateEventId(),
        type: _getEventType(collection, action),
        collection: collection,
        action: action,
        data: data,
        timestamp: DateTime.now(),
        userId: data['user_id'],
        userName: data['user_name'] ?? data['name'],
        description: _generateEventDescription(collection, action, data),
        metadata: {
          'collection': collection,
          'action': action,
          'processed_at': DateTime.now().toIso8601String(),
        },
      );

      _eventController.add(event);
      LoggerService.info(
        'Processed real-time event: ${event.type.displayName} in $collection',
      );
    } catch (e) {
      LoggerService.error('Error processing real-time event', e);
    }
  }

  /// Determine event type based on collection and action
  RealtimeEventType _getEventType(String collection, String action) {
    switch (collection) {
      case 'users':
        return action == 'create'
            ? RealtimeEventType.userRegistration
            : RealtimeEventType.userStatusChange;
      case 'funding_applications':
        return action == 'create'
            ? RealtimeEventType.claimSubmission
            : RealtimeEventType.claimStatusUpdate;
      case 'user_activity_logs':
        return RealtimeEventType.adminAction;
      case 'notifications':
        return RealtimeEventType.notification;
      case 'user_activity_logs':
        return RealtimeEventType.systemAlert;
      default:
        return RealtimeEventType.systemAlert;
    }
  }

  /// Generate human-readable event description
  String _generateEventDescription(
    String collection,
    String action,
    Map<String, dynamic> data,
  ) {
    final userName = data['user_name'] ?? data['name'] ?? 'Unknown User';

    switch (collection) {
      case 'users':
        return action == 'create'
            ? 'New user registered: $userName'
            : 'User profile updated: $userName';
      case 'funding_applications':
        final claimTitle = data['claim_title'] ?? 'Untitled Claim';
        return action == 'create'
            ? 'New funding application submitted: $claimTitle'
            : 'Funding application updated: $claimTitle';
      case 'user_activity_logs':
        final actionName = data['action'] ?? 'Unknown Action';
        return 'Admin action performed: $actionName by $userName';
      case 'notifications':
        return 'New notification: ${data['title'] ?? 'System Notification'}';
      default:
        return 'System event in $collection';
    }
  }

  /// Generate unique event ID
  String _generateEventId() {
    return 'evt_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }

  /// Start heartbeat to monitor connection
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(heartbeatInterval, (timer) {
      _checkConnection();
    });
  }

  /// Check connection health
  void _checkConnection() {
    // This would check the actual PocketBase connection
    // For now, we'll assume the connection is healthy if initialized
    if (!_isInitialized) {
      LoggerService.warning(
        'Connection check failed - service not initialized',
      );
      _scheduleReconnect();
    }
  }

  /// Schedule reconnection attempt with exponential backoff
  void _scheduleReconnect() {
    if (_reconnectAttempts >= maxReconnectAttempts) {
      LoggerService.error(
        'Max reconnection attempts reached. Stopping reconnection.',
      );
      return;
    }

    _reconnectAttempts++;
    final delay = Duration(
      seconds:
          baseReconnectDelay.inSeconds * pow(2, _reconnectAttempts - 1).toInt(),
    );

    LoggerService.info(
      'Scheduling reconnection attempt $_reconnectAttempts in ${delay.inSeconds}s',
    );

    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(delay, () {
      _attemptReconnect();
    });
  }

  /// Attempt to reconnect
  Future<void> _attemptReconnect() async {
    LoggerService.info('Attempting to reconnect (attempt $_reconnectAttempts)');

    try {
      _isInitialized = false;
      await initialize();
    } catch (e) {
      LoggerService.error('Reconnection attempt failed', e);
      _scheduleReconnect();
    }
  }

  /// Dispose of the service and clean up resources
  void dispose() {
    LoggerService.info('Disposing real-time update service');

    // Cancel timers
    _reconnectTimer?.cancel();
    _heartbeatTimer?.cancel();

    // Unsubscribe from all collections
    for (final collection in _subscriptions.keys.toList()) {
      unsubscribeFromCollection(collection);
    }

    // Close event stream
    _eventController.close();

    _isInitialized = false;
    _isConnecting = false;
    _reconnectAttempts = 0;

    LoggerService.info('Real-time update service disposed');
  }
}
