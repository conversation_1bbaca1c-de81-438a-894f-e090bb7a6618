import 'package:flutter/material.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/user_activity_models.dart';

/// Service for monitoring and analyzing user activities
class UserActivityMonitoringService {
  static final UserActivityMonitoringService _instance = UserActivityMonitoringService._internal();
  final PocketBaseService _pocketBaseService = PocketBaseService();
  
  UserActivityMonitoringService._internal();
  
  factory UserActivityMonitoringService() => _instance;

  /// Get user activities with filtering options
  Future<List<UserActivityEvent>> getUserActivities({
    String? userId,
    UserActivityType? activityType,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int perPage = 50,
  }) async {
    try {
      LoggerService.info('Fetching user activities with filters');
      
      String filter = _buildActivityFilter(
        userId: userId,
        activityType: activityType,
        startDate: startDate,
        endDate: endDate,
      );
      
      final records = await _pocketBaseService.getList(
        collectionName: 'user_activity_logs',
        page: page,
        perPage: perPage,
        filter: filter,
        sort: '-created',
        expand: 'user_id',
      );
      
      final activities = records.items
          .map((record) => _convertRecordToActivity(record))
          .toList();
      
      LoggerService.info('Fetched ${activities.length} user activities');
      return activities;
    } catch (e) {
      LoggerService.error('Error fetching user activities', e);
      rethrow;
    }
  }

  /// Get user engagement metrics for a specific user
  Future<UserEngagementMetrics> getUserEngagementMetrics({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      LoggerService.info('Calculating engagement metrics for user: $userId');
      
      final activities = await getUserActivities(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
        perPage: 1000, // Get more data for accurate metrics
      );
      
      final metrics = _calculateEngagementMetrics(activities);
      
      LoggerService.info('Calculated engagement metrics successfully');
      return metrics;
    } catch (e) {
      LoggerService.error('Error calculating engagement metrics', e);
      rethrow;
    }
  }

  /// Get activity heatmap data for visualization
  Future<Map<DateTime, int>> getActivityHeatmapData({
    UserActivityType? activityType,
    DateTime? startDate,
    DateTime? endDate,
    ActivityScope scope = ActivityScope.allUsers,
  }) async {
    try {
      LoggerService.info('Generating activity heatmap data');
      
      final activities = await getUserActivities(
        activityType: activityType,
        startDate: startDate,
        endDate: endDate,
        perPage: 1000,
      );
      
      final heatmapData = <DateTime, int>{};
      
      for (final activity in activities) {
        final day = DateTime(
          activity.timestamp.year,
          activity.timestamp.month,
          activity.timestamp.day,
        );
        heatmapData[day] = (heatmapData[day] ?? 0) + 1;
      }
      
      LoggerService.info('Generated heatmap data for ${heatmapData.length} days');
      return heatmapData;
    } catch (e) {
      LoggerService.error('Error generating heatmap data', e);
      rethrow;
    }
  }

  /// Get user sessions from activities
  Future<List<UserSession>> getUserSessions({
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      LoggerService.info('Fetching user sessions');
      
      final activities = await getUserActivities(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
        perPage: 1000,
      );
      
      final sessions = _groupActivitiesIntoSessions(activities);
      
      LoggerService.info('Found ${sessions.length} user sessions');
      return sessions;
    } catch (e) {
      LoggerService.error('Error fetching user sessions', e);
      rethrow;
    }
  }

  /// Get activity patterns for a user
  Future<ActivityPattern> getUserActivityPattern({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      LoggerService.info('Analyzing activity pattern for user: $userId');
      
      final activities = await getUserActivities(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
        perPage: 1000,
      );
      
      final pattern = ActivityPattern.fromActivities(userId, activities);
      
      LoggerService.info('Generated activity pattern successfully');
      return pattern;
    } catch (e) {
      LoggerService.error('Error analyzing activity pattern', e);
      rethrow;
    }
  }

  /// Get top active users
  Future<List<Map<String, dynamic>>> getTopActiveUsers({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 10,
  }) async {
    try {
      LoggerService.info('Fetching top active users');
      
      final activities = await getUserActivities(
        startDate: startDate,
        endDate: endDate,
        perPage: 1000,
      );
      
      final userActivityCount = <String, int>{};
      final userNames = <String, String>{};
      
      for (final activity in activities) {
        userActivityCount[activity.userId] = (userActivityCount[activity.userId] ?? 0) + 1;
        if (activity.userName != null) {
          userNames[activity.userId] = activity.userName!;
        }
      }
      
      final topUsers = userActivityCount.entries
          .toList()
          ..sort((a, b) => b.value.compareTo(a.value))
          ..take(limit);
      
      final result = topUsers.map((entry) => {
        'user_id': entry.key,
        'user_name': userNames[entry.key] ?? 'Unknown',
        'activity_count': entry.value,
      }).toList();
      
      LoggerService.info('Found ${result.length} top active users');
      return result;
    } catch (e) {
      LoggerService.error('Error fetching top active users', e);
      rethrow;
    }
  }

  /// Build filter string for activity queries
  String _buildActivityFilter({
    String? userId,
    UserActivityType? activityType,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    final filters = <String>[];
    
    if (userId != null) {
      filters.add('user_id = "$userId"');
    }
    
    if (activityType != null) {
      filters.add('action = "${activityType.value}"');
    }
    
    if (startDate != null) {
      filters.add('created >= "${startDate.toIso8601String()}"');
    }
    
    if (endDate != null) {
      filters.add('created <= "${endDate.toIso8601String()}"');
    }
    
    return filters.join(' && ');
  }

  /// Convert PocketBase record to UserActivityEvent
  UserActivityEvent _convertRecordToActivity(dynamic record) {
    final data = record.data as Map<String, dynamic>;
    
    return UserActivityEvent(
      id: record.id as String,
      userId: data['user_id'] as String? ?? '',
      userName: data['user_name'] as String?,
      userType: data['user_type'] as String?,
      type: UserActivityType.fromString(data['action'] as String? ?? ''),
      description: data['description'] as String? ?? '',
      details: Map<String, dynamic>.from(data['details'] as Map? ?? {}),
      timestamp: DateTime.parse(record.get<String>('created')),
      sessionId: data['session_id'] as String?,
      ipAddress: data['ip_address'] as String?,
      userAgent: data['user_agent'] as String?,
    );
  }

  /// Calculate engagement metrics from activities
  UserEngagementMetrics _calculateEngagementMetrics(List<UserActivityEvent> activities) {
    if (activities.isEmpty) {
      return const UserEngagementMetrics.empty();
    }
    
    // Group activities into sessions
    final sessions = _groupActivitiesIntoSessions(activities);
    
    // Calculate session duration
    final avgSessionDuration = sessions.isNotEmpty
        ? sessions.map((s) => s.duration.inMinutes).reduce((a, b) => a + b) / sessions.length
        : 0.0;
    
    // Calculate page views
    final pageViews = activities.where((a) => a.type == UserActivityType.pageView).length;
    
    // Calculate actions per session
    final avgActionsPerSession = sessions.isNotEmpty
        ? activities.length / sessions.length
        : 0.0;
    
    // Calculate feature usage
    final featureUsage = <String, int>{};
    for (final activity in activities) {
      final feature = activity.details['feature'] as String? ?? 'unknown';
      featureUsage[feature] = (featureUsage[feature] ?? 0) + 1;
    }
    
    return UserEngagementMetrics(
      sessionDuration: avgSessionDuration,
      pageViews: pageViews,
      actionsPerSession: avgActionsPerSession,
      featureUsage: featureUsage,
      totalActivities: activities.length,
      totalSessions: sessions.length,
    );
  }

  /// Group activities into sessions based on time gaps
  List<UserSession> _groupActivitiesIntoSessions(List<UserActivityEvent> activities) {
    if (activities.isEmpty) return [];
    
    final sortedActivities = activities..sort((a, b) => a.timestamp.compareTo(b.timestamp));
    final sessions = <UserSession>[];
    List<UserActivityEvent> currentSessionActivities = [];
    
    const sessionTimeoutMinutes = 30; // 30 minutes timeout
    
    for (final activity in sortedActivities) {
      if (currentSessionActivities.isEmpty) {
        currentSessionActivities.add(activity);
      } else {
        final lastActivity = currentSessionActivities.last;
        final timeDiff = activity.timestamp.difference(lastActivity.timestamp);
        
        if (timeDiff.inMinutes <= sessionTimeoutMinutes) {
          currentSessionActivities.add(activity);
        } else {
          // Create session from current activities
          if (currentSessionActivities.isNotEmpty) {
            sessions.add(UserSession.fromActivities(currentSessionActivities));
          }
          currentSessionActivities = [activity];
        }
      }
    }
    
    // Add the last session
    if (currentSessionActivities.isNotEmpty) {
      sessions.add(UserSession.fromActivities(currentSessionActivities));
    }
    
    return sessions;
  }
}
