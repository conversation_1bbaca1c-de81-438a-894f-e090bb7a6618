import 'package:flutter/material.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/analytics_models.dart';

/// Service for fetching and calculating analytics data
class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  final PocketBaseService _pocketBaseService = PocketBaseService();

  AnalyticsService._internal();

  factory AnalyticsService() => _instance;

  /// Get comprehensive platform metrics for the specified time period
  Future<PlatformMetrics> getPlatformMetrics({
    required AnalyticsTimePeriod period,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      LoggerService.info(
        'Fetching platform metrics for period: ${period.value}',
      );

      final dateRange = _getDateRange(period, startDate, endDate);

      // Parallel data fetching for performance
      final results = await Future.wait([
        _getUserMetrics(dateRange),
        _getClaimMetrics(dateRange),
        _getFundingMetrics(dateRange),
        _getEngagementMetrics(dateRange),
        _getSystemMetrics(dateRange),
      ]);

      final platformMetrics = PlatformMetrics(
        userMetrics: results[0] as UserMetrics,
        claimMetrics: results[1] as ClaimMetrics,
        fundingMetrics: results[2] as FundingMetrics,
        engagementMetrics: results[3] as EngagementMetrics,
        systemMetrics: results[4] as SystemMetrics,
        period: period,
        startDate: dateRange.start,
        endDate: dateRange.end,
      );

      LoggerService.info('Platform metrics fetched successfully');
      return platformMetrics;
    } catch (e) {
      LoggerService.error('Error fetching platform metrics', e);
      rethrow;
    }
  }

  /// Get time series data for a specific KPI
  Future<List<ChartDataPoint>> getTimeSeriesData({
    required PlatformKPI kpi,
    required AnalyticsTimePeriod period,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      LoggerService.info('Fetching time series data for KPI: ${kpi.value}');

      final dateRange = _getDateRange(period, startDate, endDate);

      switch (kpi) {
        case PlatformKPI.totalUsers:
          return _getUserTimeSeriesData(dateRange);
        case PlatformKPI.totalClaims:
          return _getClaimTimeSeriesData(dateRange);
        case PlatformKPI.fundingVolume:
          return _getFundingTimeSeriesData(dateRange);
        case PlatformKPI.userEngagement:
          return _getEngagementTimeSeriesData(dateRange);
        case PlatformKPI.responseTime:
          return _getResponseTimeSeriesData(dateRange);
        default:
          LoggerService.warning(
            'KPI ${kpi.value} not implemented for time series',
          );
          return [];
      }
    } catch (e) {
      LoggerService.error(
        'Error fetching time series data for KPI: ${kpi.value}',
        e,
      );
      rethrow;
    }
  }

  /// Get date range based on analytics time period
  DateTimeRange _getDateRange(
    AnalyticsTimePeriod period,
    DateTime? startDate,
    DateTime? endDate,
  ) {
    final now = DateTime.now();

    switch (period) {
      case AnalyticsTimePeriod.last24Hours:
        return DateTimeRange(
          start: now.subtract(const Duration(hours: 24)),
          end: now,
        );
      case AnalyticsTimePeriod.last7Days:
        return DateTimeRange(
          start: now.subtract(const Duration(days: 7)),
          end: now,
        );
      case AnalyticsTimePeriod.last30Days:
        return DateTimeRange(
          start: now.subtract(const Duration(days: 30)),
          end: now,
        );
      case AnalyticsTimePeriod.last90Days:
        return DateTimeRange(
          start: now.subtract(const Duration(days: 90)),
          end: now,
        );
      case AnalyticsTimePeriod.lastYear:
        return DateTimeRange(
          start: now.subtract(const Duration(days: 365)),
          end: now,
        );
      case AnalyticsTimePeriod.custom:
        return DateTimeRange(
          start: startDate ?? now.subtract(const Duration(days: 30)),
          end: endDate ?? now,
        );
    }
  }

  /// Get user metrics for the specified date range
  Future<UserMetrics> _getUserMetrics(DateTimeRange range) async {
    try {
      final filter =
          'created >= "${range.start.toIso8601String()}" && created <= "${range.end.toIso8601String()}"';

      final users = await _pocketBaseService.getFullList(
        collectionName: 'users',
        filter: filter,
      );

      // Calculate metrics
      int totalUsers = users.length;
      int solicitors = 0;
      int coFunders = 0;
      int claimants = 0;

      final dailyRegistrations = <DateTime, int>{};
      final typeDistribution = <String, int>{};

      for (final user in users) {
        final userType = user.data['user_type'] as String?;
        final created = DateTime.parse(user.get<String>('created'));
        final day = DateTime(created.year, created.month, created.day);

        switch (userType) {
          case 'solicitor':
            solicitors++;
            break;
          case 'co_funder':
            coFunders++;
            break;
          case 'claimant':
            claimants++;
            break;
        }

        // Count daily registrations
        dailyRegistrations[day] = (dailyRegistrations[day] ?? 0) + 1;

        // Count by type
        if (userType != null) {
          typeDistribution[userType] = (typeDistribution[userType] ?? 0) + 1;
        }
      }

      final growthRate = _calculateGrowthRate(dailyRegistrations);

      return UserMetrics(
        totalUsers: totalUsers,
        solicitors: solicitors,
        coFunders: coFunders,
        claimants: claimants,
        dailyRegistrations: dailyRegistrations,
        growthRate: growthRate,
        typeDistribution: typeDistribution,
      );
    } catch (e) {
      LoggerService.error('Error fetching user metrics', e);
      rethrow;
    }
  }

  /// Get claim metrics for the specified date range
  Future<ClaimMetrics> _getClaimMetrics(DateTimeRange range) async {
    try {
      final filter =
          'created >= "${range.start.toIso8601String()}" && created <= "${range.end.toIso8601String()}"';

      final claims = await _pocketBaseService.getFullList(
        collectionName: 'funding_applications',
        filter: filter,
      );

      int totalClaims = claims.length;
      int activeClaims = 0;
      int completedClaims = 0;

      final dailySubmissions = <DateTime, int>{};
      final statusDistribution = <String, int>{};

      for (final claim in claims) {
        final status = claim.data['status'] as String? ?? 'pending';
        final created = DateTime.parse(claim.get<String>('created'));
        final day = DateTime(created.year, created.month, created.day);

        // Count daily submissions
        dailySubmissions[day] = (dailySubmissions[day] ?? 0) + 1;

        // Count by status
        statusDistribution[status] = (statusDistribution[status] ?? 0) + 1;

        // Categorize claims
        switch (status.toLowerCase()) {
          case 'pending':
          case 'under_review':
          case 'in_progress':
          case 'approved':
          case 'funded':
            activeClaims++;
            break;
          case 'completed':
          case 'closed':
            completedClaims++;
            break;
        }
      }

      final successRate =
          totalClaims > 0 ? (completedClaims / totalClaims) * 100 : 0.0;

      return ClaimMetrics(
        totalClaims: totalClaims,
        activeClaims: activeClaims,
        completedClaims: completedClaims,
        successRate: successRate,
        dailySubmissions: dailySubmissions,
        statusDistribution: statusDistribution,
      );
    } catch (e) {
      LoggerService.error('Error fetching claim metrics', e);
      rethrow;
    }
  }

  /// Get funding metrics for the specified date range
  Future<FundingMetrics> _getFundingMetrics(DateTimeRange range) async {
    try {
      // TODO: Implement actual funding metrics when investment collections are available
      // For now, return placeholder data based on claims

      final claims = await _pocketBaseService.getFullList(
        collectionName: 'funding_applications',
        filter:
            'created >= "${range.start.toIso8601String()}" && created <= "${range.end.toIso8601String()}"',
      );

      double totalVolume = 0.0;
      int totalInvestments = 0;
      final dailyVolume = <DateTime, double>{};
      final volumeByType = <String, double>{};

      // Generate placeholder funding data based on claims
      for (final claim in claims) {
        final created = DateTime.parse(claim.get<String>('created'));
        final day = DateTime(created.year, created.month, created.day);
        final claimType = claim.data['claim_type'] as String? ?? 'general';

        // Placeholder funding amount (would come from actual investment records)
        final fundingAmount = 50000.0 + (claim.hashCode % 100000);

        totalVolume += fundingAmount;
        totalInvestments++;

        dailyVolume[day] = (dailyVolume[day] ?? 0.0) + fundingAmount;
        volumeByType[claimType] =
            (volumeByType[claimType] ?? 0.0) + fundingAmount;
      }

      final averageAmount =
          totalInvestments > 0 ? totalVolume / totalInvestments : 0.0;

      return FundingMetrics(
        totalVolume: totalVolume,
        averageAmount: averageAmount,
        totalInvestments: totalInvestments,
        dailyVolume: dailyVolume,
        volumeByType: volumeByType,
      );
    } catch (e) {
      LoggerService.error('Error fetching funding metrics', e);
      rethrow;
    }
  }

  /// Get engagement metrics for the specified date range
  Future<EngagementMetrics> _getEngagementMetrics(DateTimeRange range) async {
    try {
      // TODO: Implement actual engagement tracking when analytics collections are available
      // For now, return placeholder data

      final users = await _pocketBaseService.getFullList(
        collectionName: 'users',
        filter:
            'created >= "${range.start.toIso8601String()}" && created <= "${range.end.toIso8601String()}"',
      );

      // Placeholder engagement metrics
      final totalSessions =
          users.length * 5; // Assume 5 sessions per user on average
      final averageSessionDuration = 8.5; // 8.5 minutes average
      final bounceRate = 35.2; // 35.2% bounce rate

      final pageViews = <String, int>{
        'dashboard': totalSessions ~/ 2,
        'claims': totalSessions ~/ 3,
        'profile': totalSessions ~/ 4,
        'documents': totalSessions ~/ 5,
      };

      final dailyActiveUsers = <DateTime, int>{};

      // Generate daily active users data
      for (int i = 0; i < range.duration.inDays; i++) {
        final date = range.start.add(Duration(days: i));
        final day = DateTime(date.year, date.month, date.day);
        dailyActiveUsers[day] =
            (users.length * 0.3).round() + (i % 10); // Placeholder
      }

      return EngagementMetrics(
        averageSessionDuration: averageSessionDuration,
        totalSessions: totalSessions,
        bounceRate: bounceRate,
        pageViews: pageViews,
        dailyActiveUsers: dailyActiveUsers,
      );
    } catch (e) {
      LoggerService.error('Error fetching engagement metrics', e);
      rethrow;
    }
  }

  /// Get system metrics for the specified date range
  Future<SystemMetrics> _getSystemMetrics(DateTimeRange range) async {
    try {
      // TODO: Implement actual system monitoring when monitoring collections are available
      // For now, return placeholder data

      final uptime = 99.8; // 99.8% uptime
      final averageResponseTime = 245.0; // 245ms average response time
      final totalRequests = 150000; // 150k requests
      final errorCount = 125; // 125 errors
      final errorRate = (errorCount / totalRequests) * 100;

      final responseTimeHistory = <DateTime, double>{};

      // Generate response time history
      for (int i = 0; i < range.duration.inDays; i++) {
        final date = range.start.add(Duration(days: i));
        final day = DateTime(date.year, date.month, date.day);
        responseTimeHistory[day] =
            averageResponseTime + (i % 50) - 25; // Vary ±25ms
      }

      return SystemMetrics(
        uptime: uptime,
        averageResponseTime: averageResponseTime,
        totalRequests: totalRequests,
        errorCount: errorCount,
        errorRate: errorRate,
        responseTimeHistory: responseTimeHistory,
      );
    } catch (e) {
      LoggerService.error('Error fetching system metrics', e);
      rethrow;
    }
  }

  /// Get user time series data
  Future<List<ChartDataPoint>> _getUserTimeSeriesData(
    DateTimeRange range,
  ) async {
    try {
      final users = await _pocketBaseService.getFullList(
        collectionName: 'users',
        filter:
            'created >= "${range.start.toIso8601String()}" && created <= "${range.end.toIso8601String()}"',
      );

      final dailyData = <DateTime, int>{};

      for (final user in users) {
        final created = DateTime.parse(user.get<String>('created'));
        final day = DateTime(created.year, created.month, created.day);
        dailyData[day] = (dailyData[day] ?? 0) + 1;
      }

      return _convertToChartDataPoints(dailyData);
    } catch (e) {
      LoggerService.error('Error fetching user time series data', e);
      return [];
    }
  }

  /// Get claim time series data
  Future<List<ChartDataPoint>> _getClaimTimeSeriesData(
    DateTimeRange range,
  ) async {
    try {
      final claims = await _pocketBaseService.getFullList(
        collectionName: 'funding_applications',
        filter:
            'created >= "${range.start.toIso8601String()}" && created <= "${range.end.toIso8601String()}"',
      );

      final dailyData = <DateTime, int>{};

      for (final claim in claims) {
        final created = DateTime.parse(claim.get<String>('created'));
        final day = DateTime(created.year, created.month, created.day);
        dailyData[day] = (dailyData[day] ?? 0) + 1;
      }

      return _convertToChartDataPoints(dailyData);
    } catch (e) {
      LoggerService.error('Error fetching claim time series data', e);
      return [];
    }
  }

  /// Get funding time series data
  Future<List<ChartDataPoint>> _getFundingTimeSeriesData(
    DateTimeRange range,
  ) async {
    try {
      final claims = await _pocketBaseService.getFullList(
        collectionName: 'funding_applications',
        filter:
            'created >= "${range.start.toIso8601String()}" && created <= "${range.end.toIso8601String()}"',
      );

      final dailyData = <DateTime, double>{};

      for (final claim in claims) {
        final created = DateTime.parse(claim.get<String>('created'));
        final day = DateTime(created.year, created.month, created.day);
        final fundingAmount =
            50000.0 + (claim.hashCode % 100000); // Placeholder
        dailyData[day] = (dailyData[day] ?? 0.0) + fundingAmount;
      }

      return _convertDoubleToChartDataPoints(dailyData);
    } catch (e) {
      LoggerService.error('Error fetching funding time series data', e);
      return [];
    }
  }

  /// Get engagement time series data
  Future<List<ChartDataPoint>> _getEngagementTimeSeriesData(
    DateTimeRange range,
  ) async {
    try {
      // TODO: Implement actual engagement tracking
      // For now, return placeholder data

      final dailyData = <DateTime, int>{};

      for (int i = 0; i < range.duration.inDays; i++) {
        final date = range.start.add(Duration(days: i));
        final day = DateTime(date.year, date.month, date.day);
        dailyData[day] = 50 + (i % 20); // Placeholder engagement data
      }

      return _convertToChartDataPoints(dailyData);
    } catch (e) {
      LoggerService.error('Error fetching engagement time series data', e);
      return [];
    }
  }

  /// Get response time series data
  Future<List<ChartDataPoint>> _getResponseTimeSeriesData(
    DateTimeRange range,
  ) async {
    try {
      // TODO: Implement actual response time monitoring
      // For now, return placeholder data

      final dailyData = <DateTime, double>{};

      for (int i = 0; i < range.duration.inDays; i++) {
        final date = range.start.add(Duration(days: i));
        final day = DateTime(date.year, date.month, date.day);
        dailyData[day] = 200.0 + (i % 100); // Placeholder response time data
      }

      return _convertDoubleToChartDataPoints(dailyData);
    } catch (e) {
      LoggerService.error('Error fetching response time series data', e);
      return [];
    }
  }

  /// Convert integer data to chart data points
  List<ChartDataPoint> _convertToChartDataPoints(Map<DateTime, int> data) {
    return data.entries
        .map(
          (entry) => ChartDataPoint(
            timestamp: entry.key,
            value: entry.value.toDouble(),
          ),
        )
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  /// Convert double data to chart data points
  List<ChartDataPoint> _convertDoubleToChartDataPoints(
    Map<DateTime, double> data,
  ) {
    return data.entries
        .map(
          (entry) => ChartDataPoint(timestamp: entry.key, value: entry.value),
        )
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  /// Calculate growth rate from daily registrations
  double _calculateGrowthRate(Map<DateTime, int> dailyRegistrations) {
    if (dailyRegistrations.isEmpty) return 0.0;

    final sortedDates = dailyRegistrations.keys.toList()..sort();
    if (sortedDates.length < 2) return 0.0;

    final firstWeek = sortedDates
        .take(7)
        .map((date) => dailyRegistrations[date] ?? 0)
        .fold(0, (a, b) => a + b);
    final lastWeek = sortedDates
        .skip(sortedDates.length - 7)
        .map((date) => dailyRegistrations[date] ?? 0)
        .fold(0, (a, b) => a + b);

    if (firstWeek == 0) return lastWeek > 0 ? 100.0 : 0.0;

    return ((lastWeek - firstWeek) / firstWeek) * 100;
  }
}
