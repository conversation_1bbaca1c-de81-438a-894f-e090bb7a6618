import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/dashboard_models.dart';

/// Service for fetching and calculating dashboard statistics
class DashboardStatisticsService {
  static final DashboardStatisticsService _instance = DashboardStatisticsService._internal();
  final PocketBaseService _pocketBaseService = PocketBaseService();
  
  DashboardStatisticsService._internal();
  
  factory DashboardStatisticsService() => _instance;

  /// Get comprehensive platform statistics
  Future<PlatformStatistics> getPlatformStatistics() async {
    try {
      LoggerService.info('Fetching platform statistics');
      
      // Parallel data fetching for performance
      final results = await Future.wait([
        _getUserStatistics(),
        _getClaimStatistics(),
        _getFundingStatistics(),
        _getSystemHealth(),
        _getRecentActivity(),
      ]);
      
      final userStats = results[0] as Map<String, dynamic>;
      final claimStats = results[1] as Map<String, dynamic>;
      final fundingStats = results[2] as Map<String, dynamic>;
      final systemHealth = results[3] as SystemHealth;
      final recentActivities = results[4] as List<RecentActivity>;
      
      final statistics = PlatformStatistics(
        totalUsers: userStats['total'] as int,
        activeSolicitors: userStats['solicitors'] as int,
        activeCoFunders: userStats['coFunders'] as int,
        activeClaimants: userStats['claimants'] as int,
        totalClaims: claimStats['total'] as int,
        activeClaims: claimStats['active'] as int,
        totalFundingVolume: fundingStats['volume'] as double,
        newRegistrationsToday: userStats['newToday'] as int,
        newRegistrationsWeek: userStats['newWeek'] as int,
        newRegistrationsMonth: userStats['newMonth'] as int,
        systemHealth: systemHealth,
        recentActivities: recentActivities,
        claimsByStatus: Map<String, int>.from(claimStats['byStatus'] as Map),
        usersByType: Map<String, int>.from(userStats['byType'] as Map),
        fundingByMonth: Map<String, double>.from(fundingStats['byMonth'] as Map),
        lastUpdated: DateTime.now(),
      );
      
      LoggerService.info('Platform statistics fetched successfully');
      return statistics;
    } catch (e) {
      LoggerService.error('Error fetching platform statistics', e);
      rethrow;
    }
  }

  /// Get user statistics
  Future<Map<String, dynamic>> _getUserStatistics() async {
    try {
      // Get all users
      final usersResult = await _pocketBaseService.getList(
        collectionName: 'users',
        perPage: 500, // Adjust based on expected user count
      );
      
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final weekAgo = today.subtract(const Duration(days: 7));
      final monthAgo = DateTime(now.year, now.month - 1, now.day);
      
      int total = usersResult.totalItems;
      int solicitors = 0;
      int coFunders = 0;
      int claimants = 0;
      int admins = 0;
      int newToday = 0;
      int newWeek = 0;
      int newMonth = 0;
      
      for (final user in usersResult.items) {
        final userType = user.data['user_type'] as String?;
        final created = DateTime.tryParse(user.data['created'] as String? ?? '') ?? DateTime.now();
        final isActive = user.data['active'] as bool? ?? true;
        
        // Count by type (only active users)
        if (isActive) {
          switch (userType) {
            case 'solicitor':
              solicitors++;
              break;
            case 'co_funder':
              coFunders++;
              break;
            case 'claimant':
              claimants++;
              break;
            case 'admin':
              admins++;
              break;
          }
        }
        
        // Count new registrations
        if (created.isAfter(today)) newToday++;
        if (created.isAfter(weekAgo)) newWeek++;
        if (created.isAfter(monthAgo)) newMonth++;
      }
      
      return {
        'total': total,
        'solicitors': solicitors,
        'coFunders': coFunders,
        'claimants': claimants,
        'admins': admins,
        'newToday': newToday,
        'newWeek': newWeek,
        'newMonth': newMonth,
        'byType': {
          'solicitor': solicitors,
          'co_funder': coFunders,
          'claimant': claimants,
          'admin': admins,
        },
      };
    } catch (e) {
      LoggerService.error('Error fetching user statistics', e);
      rethrow;
    }
  }

  /// Get claim statistics
  Future<Map<String, dynamic>> _getClaimStatistics() async {
    try {
      // Get all funding applications (claims)
      final claimsResult = await _pocketBaseService.getList(
        collectionName: 'funding_applications',
        perPage: 500, // Adjust based on expected claim count
      );
      
      int total = claimsResult.totalItems;
      int active = 0;
      int pending = 0;
      int approved = 0;
      int rejected = 0;
      int completed = 0;
      
      Map<String, int> byStatus = {};
      
      for (final claim in claimsResult.items) {
        final status = claim.data['status'] as String? ?? 'pending';
        
        // Count by status
        byStatus[status] = (byStatus[status] ?? 0) + 1;
        
        switch (status.toLowerCase()) {
          case 'pending':
          case 'under_review':
          case 'in_progress':
            active++;
            pending++;
            break;
          case 'approved':
          case 'funded':
            active++;
            approved++;
            break;
          case 'rejected':
          case 'declined':
            rejected++;
            break;
          case 'completed':
          case 'closed':
            completed++;
            break;
          default:
            active++;
            break;
        }
      }
      
      return {
        'total': total,
        'active': active,
        'pending': pending,
        'approved': approved,
        'rejected': rejected,
        'completed': completed,
        'byStatus': byStatus,
      };
    } catch (e) {
      LoggerService.error('Error fetching claim statistics', e);
      rethrow;
    }
  }

  /// Get funding statistics
  Future<Map<String, dynamic>> _getFundingStatistics() async {
    try {
      // TODO: Implement actual funding volume calculation
      // This would require investment/funding records
      
      // For now, return placeholder data
      final now = DateTime.now();
      Map<String, double> byMonth = {};
      
      // Generate last 6 months of placeholder data
      for (int i = 5; i >= 0; i--) {
        final month = DateTime(now.year, now.month - i, 1);
        final monthKey = '${month.year}-${month.month.toString().padLeft(2, '0')}';
        byMonth[monthKey] = (i + 1) * 50000.0; // Placeholder values
      }
      
      final totalVolume = byMonth.values.fold(0.0, (sum, value) => sum + value);
      
      return {
        'volume': totalVolume,
        'byMonth': byMonth,
        'averagePerClaim': totalVolume > 0 ? totalVolume / 10 : 0.0, // Placeholder
      };
    } catch (e) {
      LoggerService.error('Error fetching funding statistics', e);
      rethrow;
    }
  }

  /// Get system health status
  Future<SystemHealth> _getSystemHealth() async {
    try {
      // TODO: Implement actual system health monitoring
      // This would integrate with monitoring services
      
      // For now, return healthy status with placeholder metrics
      return SystemHealth(
        status: SystemHealthStatus.healthy,
        description: 'All systems operational',
        cpuUsage: 45.2,
        memoryUsage: 62.8,
        diskUsage: 34.1,
        activeConnections: 127,
        lastChecked: DateTime.now(),
      );
    } catch (e) {
      LoggerService.error('Error fetching system health', e);
      
      // Return warning status on error
      return SystemHealth(
        status: SystemHealthStatus.warning,
        description: 'Unable to fetch system metrics',
        cpuUsage: 0.0,
        memoryUsage: 0.0,
        diskUsage: 0.0,
        activeConnections: 0,
        lastChecked: DateTime.now(),
      );
    }
  }

  /// Get recent activity
  Future<List<RecentActivity>> _getRecentActivity() async {
    try {
      // Get recent audit logs for activity feed
      final logsResult = await _pocketBaseService.getList(
        collectionName: 'user_activity_logs',
        perPage: 10,
        sort: '-created',
      );
      
      List<RecentActivity> activities = [];
      
      for (final log in logsResult.items) {
        final action = log.data['action'] as String? ?? 'Unknown action';
        final details = log.data['details'] as Map<String, dynamic>? ?? {};
        final userId = details['user_id'] as String?;
        final userName = details['user_name'] as String? ?? details['admin_name'] as String?;
        
        activities.add(RecentActivity(
          id: log.id,
          type: action,
          description: _formatActivityDescription(action, details),
          userId: userId,
          userName: userName,
          timestamp: DateTime.tryParse(log.data['created'] as String? ?? '') ?? DateTime.now(),
          metadata: details,
        ));
      }
      
      return activities;
    } catch (e) {
      LoggerService.error('Error fetching recent activity', e);
      return [];
    }
  }

  /// Format activity description for display
  String _formatActivityDescription(String action, Map<String, dynamic> details) {
    final userName = details['user_name'] as String? ?? details['admin_name'] as String? ?? 'Unknown user';
    
    switch (action.toLowerCase()) {
      case 'user_login':
        return '$userName signed in';
      case 'user_logout':
        return '$userName signed out';
      case 'user_registration':
        return '$userName registered';
      case 'user_status_update':
        final newStatus = details['new_status'] as String? ?? 'unknown';
        return '$userName status changed to $newStatus';
      case 'claim_created':
        return '$userName created a new claim';
      case 'claim_updated':
        return '$userName updated a claim';
      case 'admin_login':
        return 'Admin $userName signed in';
      case 'bulk_user_status_update':
        final count = details['total_users'] as int? ?? 0;
        return 'Admin $userName updated $count users';
      default:
        return '$userName performed $action';
    }
  }

  /// Get cached statistics if available and not stale
  Future<PlatformStatistics?> getCachedStatistics() async {
    try {
      // TODO: Implement caching mechanism
      // For now, always return null to force fresh data
      return null;
    } catch (e) {
      LoggerService.error('Error getting cached statistics', e);
      return null;
    }
  }

  /// Cache statistics for performance
  Future<void> cacheStatistics(PlatformStatistics statistics) async {
    try {
      // TODO: Implement caching mechanism
      LoggerService.info('Statistics cached successfully');
    } catch (e) {
      LoggerService.error('Error caching statistics', e);
    }
  }
}
