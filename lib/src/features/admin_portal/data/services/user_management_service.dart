import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_user_management_models.dart';

/// Service for admin user management operations
class UserManagementService {
  static final UserManagementService _instance =
      UserManagementService._internal();
  final PocketBaseService _pocketBaseService = PocketBaseService();

  UserManagementService._internal();

  factory UserManagementService() => _instance;

  /// Get all users with filtering and pagination
  Future<List<AdminUserModel>> getAllUsers({
    UserType? type,
    UserStatus? status,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    SortOption sortOption = SortOption.dateDesc,
    int page = 1,
    int perPage = 50,
  }) async {
    try {
      LoggerService.info(
        'Fetching users for admin with filters: type=$type, status=$status, search=$searchQuery',
      );

      final filter = _buildFilterQuery(
        type,
        status,
        searchQuery,
        startDate,
        endDate,
      );

      final records = await _pocketBaseService.getList(
        collectionName: 'users',
        page: page,
        perPage: perPage,
        filter: filter,
        sort: sortOption.value,
      );

      final users =
          records.items
              .map((record) => AdminUserModel.fromRecord(record))
              .toList();

      LoggerService.info(
        'Successfully fetched ${users.length} users for admin',
      );
      return users;
    } catch (e) {
      LoggerService.error('Error fetching users for admin', e);
      rethrow;
    }
  }

  /// Get total user count with filters
  Future<int> getUserCount({
    UserType? type,
    UserStatus? status,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final filter = _buildFilterQuery(
        type,
        status,
        searchQuery,
        startDate,
        endDate,
      );

      final result = await _pocketBaseService.getList(
        collectionName: 'users',
        page: 1,
        perPage: 1,
        filter: filter,
      );

      return result.totalItems;
    } catch (e) {
      LoggerService.error('Error getting user count', e);
      rethrow;
    }
  }

  /// Get single user by ID with expanded profile data
  Future<AdminUserModel> getUserById(String userId) async {
    try {
      LoggerService.info('Fetching user details for ID: $userId');

      final record = await _pocketBaseService.getOne(
        collectionName: 'users',
        recordId: userId,
      );

      final user = AdminUserModel.fromRecord(record);
      LoggerService.info(
        'Successfully fetched user details for: ${user.email}',
      );

      return user;
    } catch (e) {
      LoggerService.error('Error fetching user by ID: $userId', e);
      rethrow;
    }
  }

  /// Update user status
  Future<AdminUserModel> updateUserStatus(
    String userId,
    UserStatus newStatus,
    String reason,
    String adminId,
  ) async {
    try {
      LoggerService.info('Updating user status: $userId to ${newStatus.value}');

      final updatedRecord = await _pocketBaseService.updateRecord(
        collectionName: 'users',
        recordId: userId,
        data: {
          'status': newStatus.value,
          'updated': DateTime.now().toIso8601String(),
        },
      );

      // Log audit trail
      await _logUserManagementActivity('user_status_update', {
        'user_id': userId,
        'old_status': 'unknown', // Would need to fetch old status first
        'new_status': newStatus.value,
        'reason': reason,
        'admin_id': adminId,
        'timestamp': DateTime.now().toIso8601String(),
      });

      final user = AdminUserModel.fromRecord(updatedRecord);
      LoggerService.info('Successfully updated user status for: ${user.email}');

      return user;
    } catch (e) {
      LoggerService.error('Error updating user status for: $userId', e);
      rethrow;
    }
  }

  /// Update user basic information
  Future<AdminUserModel> updateUserInfo(
    String userId,
    Map<String, dynamic> updates,
    String adminId,
  ) async {
    try {
      LoggerService.info('Updating user info for: $userId');

      final updatedRecord = await _pocketBaseService.updateRecord(
        collectionName: 'users',
        recordId: userId,
        data: {...updates, 'updated': DateTime.now().toIso8601String()},
      );

      // Log audit trail
      await _logUserManagementActivity('user_info_update', {
        'user_id': userId,
        'updated_fields': updates.keys.toList(),
        'admin_id': adminId,
        'timestamp': DateTime.now().toIso8601String(),
      });

      final user = AdminUserModel.fromRecord(updatedRecord);
      LoggerService.info('Successfully updated user info for: ${user.email}');

      return user;
    } catch (e) {
      LoggerService.error('Error updating user info for: $userId', e);
      rethrow;
    }
  }

  /// Bulk update user status
  Future<BulkOperationResult> bulkUpdateUserStatus(
    List<String> userIds,
    UserStatus newStatus,
    String reason,
    String adminId,
  ) async {
    final startTime = DateTime.now();
    int successCount = 0;
    int failureCount = 0;
    List<String> errors = [];

    try {
      LoggerService.info(
        'Starting bulk user status update for ${userIds.length} users',
      );

      for (final userId in userIds) {
        try {
          await updateUserStatus(userId, newStatus, reason, adminId);
          successCount++;
        } catch (e) {
          failureCount++;
          errors.add('Failed to update user $userId: ${e.toString()}');
          LoggerService.warning(
            'Failed to update user $userId in bulk operation: $e',
          );
        }
      }

      final duration = DateTime.now().difference(startTime);

      // Log bulk operation
      await _logUserManagementActivity('bulk_user_status_update', {
        'total_users': userIds.length,
        'success_count': successCount,
        'failure_count': failureCount,
        'new_status': newStatus.value,
        'reason': reason,
        'admin_id': adminId,
        'duration_ms': duration.inMilliseconds,
        'timestamp': DateTime.now().toIso8601String(),
      });

      LoggerService.info(
        'Completed bulk user status update: $successCount success, $failureCount failures',
      );

      return BulkOperationResult(
        totalCount: userIds.length,
        successCount: successCount,
        failureCount: failureCount,
        errors: errors,
        duration: duration,
      );
    } catch (e) {
      LoggerService.error('Error in bulk user status update', e);
      rethrow;
    }
  }

  /// Delete user (soft delete by setting status)
  Future<void> deleteUser(String userId, String reason, String adminId) async {
    try {
      LoggerService.info('Soft deleting user: $userId');

      await _pocketBaseService.updateRecord(
        collectionName: 'users',
        recordId: userId,
        data: {
          'status': UserStatus.deactivated.value,
          'opt_out': true,
          'updated': DateTime.now().toIso8601String(),
        },
      );

      // Log audit trail
      await _logUserManagementActivity('user_delete', {
        'user_id': userId,
        'reason': reason,
        'admin_id': adminId,
        'timestamp': DateTime.now().toIso8601String(),
      });

      LoggerService.info('Successfully soft deleted user: $userId');
    } catch (e) {
      LoggerService.error('Error deleting user: $userId', e);
      rethrow;
    }
  }

  /// Search users by query
  Future<List<AdminUserModel>> searchUsers(
    String query, {
    UserType? type,
    int limit = 20,
  }) async {
    try {
      LoggerService.info('Searching users with query: $query');

      final typeFilter =
          type != null && type != UserType.all
              ? 'user_type = "${type.value}"'
              : '';

      final searchFilter = '''
        (name ~ "$query" || 
         email ~ "$query" || 
         first_name ~ "$query" || 
         last_name ~ "$query")
      ''';

      final combinedFilter =
          typeFilter.isNotEmpty
              ? '$typeFilter && ($searchFilter)'
              : searchFilter;

      final records = await _pocketBaseService.getList(
        collectionName: 'users',
        page: 1,
        perPage: limit,
        filter: combinedFilter,
        sort: '-created',
      );

      final users =
          records.items
              .map((record) => AdminUserModel.fromRecord(record))
              .toList();

      LoggerService.info('Found ${users.length} users matching query: $query');
      return users;
    } catch (e) {
      LoggerService.error('Error searching users with query: $query', e);
      rethrow;
    }
  }

  /// Build filter query for PocketBase
  String _buildFilterQuery(
    UserType? type,
    UserStatus? status,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
  ) {
    List<String> filters = [];

    // User type filter
    if (type != null && type != UserType.all) {
      filters.add('user_type = "${type.value}"');
    }

    // Status filter
    if (status != null && status != UserStatus.all) {
      filters.add('status = "${status.value}"');
    }

    // Search query filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filters.add('''
        (name ~ "$searchQuery" || 
         email ~ "$searchQuery" || 
         first_name ~ "$searchQuery" || 
         last_name ~ "$searchQuery")
      ''');
    }

    // Date range filter
    if (startDate != null) {
      filters.add('created >= "${startDate.toIso8601String()}"');
    }
    if (endDate != null) {
      filters.add('created <= "${endDate.toIso8601String()}"');
    }

    return filters.join(' && ');
  }

  /// Log user management activity for audit trail
  Future<void> _logUserManagementActivity(
    String action,
    Map<String, dynamic> details,
  ) async {
    try {
      await _pocketBaseService.createRecord(
        collectionName: 'user_activity_logs',
        data: {
          'action': action,
          'details': details,
          'timestamp': DateTime.now().toIso8601String(),
          'user_type': 'admin',
          'ip_address': 'unknown', // TODO: Get actual IP if needed
          'user_agent': 'flutter_app',
        },
      );
    } catch (e) {
      LoggerService.error('Failed to log user management activity: $action', e);
      // Don't throw - logging failure shouldn't break the main operation
    }
  }

  /// Get user activity logs
  Future<List<Map<String, dynamic>>> getUserActivityLogs(
    String userId, {
    int limit = 50,
  }) async {
    try {
      final records = await _pocketBaseService.getList(
        collectionName: 'user_activity_logs',
        filter: 'details.user_id = "$userId"',
        sort: '-created',
        perPage: limit,
      );

      return records.items.map((record) => record.data).toList();
    } catch (e) {
      LoggerService.error('Error fetching user activity logs for: $userId', e);
      rethrow;
    }
  }
}
