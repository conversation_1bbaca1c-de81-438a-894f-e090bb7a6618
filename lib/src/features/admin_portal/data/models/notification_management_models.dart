/// Notification types for different purposes
enum NotificationType {
  announcement('announcement', 'Announcement'),
  systemAlert('system_alert', 'System Alert'),
  userUpdate('user_update', 'User Update'),
  claimUpdate('claim_update', 'Claim Update'),
  fundingUpdate('funding_update', 'Funding Update'),
  educational('educational', 'Educational'),
  promotional('promotional', 'Promotional'),
  reminder('reminder', 'Reminder');

  const NotificationType(this.value, this.displayName);

  final String value;
  final String displayName;

  static NotificationType fromString(String value) {
    return NotificationType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => NotificationType.announcement,
    );
  }
}

/// Notification priority levels
enum NotificationPriority {
  low('low', 'Low'),
  normal('normal', 'Normal'),
  high('high', 'High'),
  urgent('urgent', 'Urgent');

  const NotificationPriority(this.value, this.displayName);

  final String value;
  final String displayName;

  static NotificationPriority fromString(String value) {
    return NotificationPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => NotificationPriority.normal,
    );
  }
}

/// Delivery channels for notifications
enum DeliveryChannel {
  inApp('in_app', 'In-App'),
  email('email', 'Email'),
  both('both', 'Both');

  const DeliveryChannel(this.value, this.displayName);

  final String value;
  final String displayName;

  static DeliveryChannel fromString(String value) {
    return DeliveryChannel.values.firstWhere(
      (channel) => channel.value == value,
      orElse: () => DeliveryChannel.inApp,
    );
  }
}

/// Targeting criteria for user segmentation
enum TargetingCriteria {
  userType('user_type', 'User Type'),
  accessLevel('access_level', 'Access Level'),
  registrationDate('registration_date', 'Registration Date'),
  lastActivity('last_activity', 'Last Activity'),
  claimStatus('claim_status', 'Claim Status'),
  fundingActivity('funding_activity', 'Funding Activity'),
  location('location', 'Location'),
  custom('custom', 'Custom');

  const TargetingCriteria(this.value, this.displayName);

  final String value;
  final String displayName;

  static TargetingCriteria fromString(String value) {
    return TargetingCriteria.values.firstWhere(
      (criteria) => criteria.value == value,
      orElse: () => TargetingCriteria.userType,
    );
  }
}

/// User segments for quick targeting
enum UserSegment {
  allUsers('all_users', 'All Users'),
  solicitors('solicitors', 'Solicitors'),
  coFunders('co_funders', 'Co-Funders'),
  claimants('claimants', 'Claimants'),
  activeUsers('active_users', 'Active Users'),
  newUsers('new_users', 'New Users'),
  inactiveUsers('inactive_users', 'Inactive Users'),
  custom('custom', 'Custom');

  const UserSegment(this.value, this.displayName);

  final String value;
  final String displayName;

  static UserSegment fromString(String value) {
    return UserSegment.values.firstWhere(
      (segment) => segment.value == value,
      orElse: () => UserSegment.allUsers,
    );
  }
}

/// Campaign status
enum CampaignStatus {
  draft('draft', 'Draft'),
  scheduled('scheduled', 'Scheduled'),
  sending('sending', 'Sending'),
  sent('sent', 'Sent'),
  failed('failed', 'Failed'),
  cancelled('cancelled', 'Cancelled');

  const CampaignStatus(this.value, this.displayName);

  final String value;
  final String displayName;

  static CampaignStatus fromString(String value) {
    return CampaignStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => CampaignStatus.draft,
    );
  }
}

/// Targeting rule for user segmentation
class TargetingRule {
  final TargetingCriteria criteria;
  final String operator;
  final String value;
  final bool exclude;

  const TargetingRule({
    required this.criteria,
    required this.operator,
    required this.value,
    this.exclude = false,
  });

  factory TargetingRule.fromJson(Map<String, dynamic> json) {
    return TargetingRule(
      criteria: TargetingCriteria.fromString(json['criteria'] as String? ?? ''),
      operator: json['operator'] as String? ?? 'equals',
      value: json['value'] as String? ?? '',
      exclude: json['exclude'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'criteria': criteria.value,
      'operator': operator,
      'value': value,
      'exclude': exclude,
    };
  }

  TargetingRule copyWith({
    TargetingCriteria? criteria,
    String? operator,
    String? value,
    bool? exclude,
  }) {
    return TargetingRule(
      criteria: criteria ?? this.criteria,
      operator: operator ?? this.operator,
      value: value ?? this.value,
      exclude: exclude ?? this.exclude,
    );
  }
}

/// Notification campaign model
class NotificationCampaign {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final NotificationPriority priority;
  final DeliveryChannel deliveryChannel;
  final List<TargetingRule> targetingRules;
  final int targetUserCount;
  final int? actualRecipientCount;
  final CampaignStatus status;
  final DateTime createdAt;
  final DateTime? scheduledAt;
  final DateTime? sentAt;
  final String createdBy;
  final String? createdByName;
  final String? templateId;
  final Map<String, dynamic> variables;
  final Map<String, dynamic> metadata;

  const NotificationCampaign({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.priority,
    required this.deliveryChannel,
    required this.targetingRules,
    required this.targetUserCount,
    this.actualRecipientCount,
    required this.status,
    required this.createdAt,
    this.scheduledAt,
    this.sentAt,
    required this.createdBy,
    this.createdByName,
    this.templateId,
    required this.variables,
    required this.metadata,
  });

  factory NotificationCampaign.fromJson(Map<String, dynamic> json) {
    return NotificationCampaign(
      id: json['id'] as String? ?? '',
      title: json['title'] as String? ?? '',
      message: json['message'] as String? ?? '',
      type: NotificationType.fromString(json['type'] as String? ?? ''),
      priority: NotificationPriority.fromString(json['priority'] as String? ?? ''),
      deliveryChannel: DeliveryChannel.fromString(json['delivery_channel'] as String? ?? ''),
      targetingRules: (json['targeting_rules'] as List? ?? [])
          .map((rule) => TargetingRule.fromJson(rule as Map<String, dynamic>))
          .toList(),
      targetUserCount: json['target_user_count'] as int? ?? 0,
      actualRecipientCount: json['actual_recipient_count'] as int?,
      status: CampaignStatus.fromString(json['status'] as String? ?? ''),
      createdAt: DateTime.tryParse(json['created'] as String? ?? '') ?? DateTime.now(),
      scheduledAt: json['scheduled_at'] != null ? DateTime.tryParse(json['scheduled_at'] as String) : null,
      sentAt: json['sent_at'] != null ? DateTime.tryParse(json['sent_at'] as String) : null,
      createdBy: json['created_by'] as String? ?? '',
      createdByName: json['created_by_name'] as String?,
      templateId: json['template_id'] as String?,
      variables: Map<String, dynamic>.from(json['variables'] as Map? ?? {}),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.value,
      'priority': priority.value,
      'delivery_channel': deliveryChannel.value,
      'targeting_rules': targetingRules.map((rule) => rule.toJson()).toList(),
      'target_user_count': targetUserCount,
      'actual_recipient_count': actualRecipientCount,
      'status': status.value,
      'created': createdAt.toIso8601String(),
      'scheduled_at': scheduledAt?.toIso8601String(),
      'sent_at': sentAt?.toIso8601String(),
      'created_by': createdBy,
      'created_by_name': createdByName,
      'template_id': templateId,
      'variables': variables,
      'metadata': metadata,
    };
  }

  bool get isDraft => status == CampaignStatus.draft;
  bool get isScheduled => status == CampaignStatus.scheduled;
  bool get isSent => status == CampaignStatus.sent;
  bool get isFailed => status == CampaignStatus.failed;

  String get formattedCreatedAt {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }

  String get formattedScheduledAt {
    if (scheduledAt == null) return 'Not scheduled';
    return '${scheduledAt!.day}/${scheduledAt!.month}/${scheduledAt!.year} ${scheduledAt!.hour}:${scheduledAt!.minute.toString().padLeft(2, '0')}';
  }

  NotificationCampaign copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    NotificationPriority? priority,
    DeliveryChannel? deliveryChannel,
    List<TargetingRule>? targetingRules,
    int? targetUserCount,
    int? actualRecipientCount,
    CampaignStatus? status,
    DateTime? createdAt,
    DateTime? scheduledAt,
    DateTime? sentAt,
    String? createdBy,
    String? createdByName,
    String? templateId,
    Map<String, dynamic>? variables,
    Map<String, dynamic>? metadata,
  }) {
    return NotificationCampaign(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      deliveryChannel: deliveryChannel ?? this.deliveryChannel,
      targetingRules: targetingRules ?? this.targetingRules,
      targetUserCount: targetUserCount ?? this.targetUserCount,
      actualRecipientCount: actualRecipientCount ?? this.actualRecipientCount,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      sentAt: sentAt ?? this.sentAt,
      createdBy: createdBy ?? this.createdBy,
      createdByName: createdByName ?? this.createdByName,
      templateId: templateId ?? this.templateId,
      variables: variables ?? this.variables,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Notification template model
class NotificationTemplate {
  final String id;
  final String name;
  final String description;
  final NotificationType type;
  final String titleTemplate;
  final String messageTemplate;
  final List<String> variables;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  final Map<String, dynamic> metadata;

  const NotificationTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.titleTemplate,
    required this.messageTemplate,
    required this.variables,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    required this.metadata,
  });

  factory NotificationTemplate.fromJson(Map<String, dynamic> json) {
    return NotificationTemplate(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      description: json['description'] as String? ?? '',
      type: NotificationType.fromString(json['type'] as String? ?? ''),
      titleTemplate: json['title_template'] as String? ?? '',
      messageTemplate: json['message_template'] as String? ?? '',
      variables: List<String>.from(json['variables'] as List? ?? []),
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.tryParse(json['created'] as String? ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updated'] as String? ?? '') ?? DateTime.now(),
      createdBy: json['created_by'] as String? ?? '',
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.value,
      'title_template': titleTemplate,
      'message_template': messageTemplate,
      'variables': variables,
      'is_active': isActive,
      'created': createdAt.toIso8601String(),
      'updated': updatedAt.toIso8601String(),
      'created_by': createdBy,
      'metadata': metadata,
    };
  }

  /// Render template with provided variables
  String renderTitle(Map<String, dynamic> variables) {
    return _renderTemplate(titleTemplate, variables);
  }

  /// Render template with provided variables
  String renderMessage(Map<String, dynamic> variables) {
    return _renderTemplate(messageTemplate, variables);
  }

  String _renderTemplate(String template, Map<String, dynamic> variables) {
    String rendered = template;
    for (final entry in variables.entries) {
      rendered = rendered.replaceAll('{{${entry.key}}}', entry.value.toString());
    }
    return rendered;
  }

  NotificationTemplate copyWith({
    String? id,
    String? name,
    String? description,
    NotificationType? type,
    String? titleTemplate,
    String? messageTemplate,
    List<String>? variables,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    Map<String, dynamic>? metadata,
  }) {
    return NotificationTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      titleTemplate: titleTemplate ?? this.titleTemplate,
      messageTemplate: messageTemplate ?? this.messageTemplate,
      variables: variables ?? this.variables,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Notification analytics model
class NotificationAnalytics {
  final String campaignId;
  final int totalSent;
  final int totalDelivered;
  final int totalRead;
  final int totalClicked;
  final double deliveryRate;
  final double readRate;
  final double clickThroughRate;
  final Map<String, int> deliveryByChannel;
  final Map<String, int> engagementByUserType;
  final DateTime lastUpdated;

  const NotificationAnalytics({
    required this.campaignId,
    required this.totalSent,
    required this.totalDelivered,
    required this.totalRead,
    required this.totalClicked,
    required this.deliveryRate,
    required this.readRate,
    required this.clickThroughRate,
    required this.deliveryByChannel,
    required this.engagementByUserType,
    required this.lastUpdated,
  });

  factory NotificationAnalytics.fromJson(Map<String, dynamic> json) {
    return NotificationAnalytics(
      campaignId: json['campaign_id'] as String? ?? '',
      totalSent: json['total_sent'] as int? ?? 0,
      totalDelivered: json['total_delivered'] as int? ?? 0,
      totalRead: json['total_read'] as int? ?? 0,
      totalClicked: json['total_clicked'] as int? ?? 0,
      deliveryRate: (json['delivery_rate'] as num?)?.toDouble() ?? 0.0,
      readRate: (json['read_rate'] as num?)?.toDouble() ?? 0.0,
      clickThroughRate: (json['click_through_rate'] as num?)?.toDouble() ?? 0.0,
      deliveryByChannel: Map<String, int>.from(json['delivery_by_channel'] as Map? ?? {}),
      engagementByUserType: Map<String, int>.from(json['engagement_by_user_type'] as Map? ?? {}),
      lastUpdated: DateTime.tryParse(json['last_updated'] as String? ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'campaign_id': campaignId,
      'total_sent': totalSent,
      'total_delivered': totalDelivered,
      'total_read': totalRead,
      'total_clicked': totalClicked,
      'delivery_rate': deliveryRate,
      'read_rate': readRate,
      'click_through_rate': clickThroughRate,
      'delivery_by_channel': deliveryByChannel,
      'engagement_by_user_type': engagementByUserType,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  String get formattedDeliveryRate => '${deliveryRate.toStringAsFixed(1)}%';
  String get formattedReadRate => '${readRate.toStringAsFixed(1)}%';
  String get formattedClickThroughRate => '${clickThroughRate.toStringAsFixed(1)}%';

  NotificationAnalytics copyWith({
    String? campaignId,
    int? totalSent,
    int? totalDelivered,
    int? totalRead,
    int? totalClicked,
    double? deliveryRate,
    double? readRate,
    double? clickThroughRate,
    Map<String, int>? deliveryByChannel,
    Map<String, int>? engagementByUserType,
    DateTime? lastUpdated,
  }) {
    return NotificationAnalytics(
      campaignId: campaignId ?? this.campaignId,
      totalSent: totalSent ?? this.totalSent,
      totalDelivered: totalDelivered ?? this.totalDelivered,
      totalRead: totalRead ?? this.totalRead,
      totalClicked: totalClicked ?? this.totalClicked,
      deliveryRate: deliveryRate ?? this.deliveryRate,
      readRate: readRate ?? this.readRate,
      clickThroughRate: clickThroughRate ?? this.clickThroughRate,
      deliveryByChannel: deliveryByChannel ?? this.deliveryByChannel,
      engagementByUserType: engagementByUserType ?? this.engagementByUserType,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
