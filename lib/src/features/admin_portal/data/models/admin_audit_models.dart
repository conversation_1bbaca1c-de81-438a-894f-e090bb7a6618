import 'package:pocketbase/pocketbase.dart';

/// Admin action types for categorization
enum AdminActionType {
  userManagement('user_management', 'User Management'),
  contentManagement('content_management', 'Content Management'),
  systemConfiguration('system_configuration', 'System Configuration'),
  securityEvent('security_event', 'Security Event'),
  dataExport('data_export', 'Data Export'),
  bulkOperation('bulk_operation', 'Bulk Operation'),
  authentication('authentication', 'Authentication'),
  notification('notification', 'Notification');

  const AdminActionType(this.value, this.displayName);
  
  final String value;
  final String displayName;
  
  static AdminActionType fromString(String value) {
    return AdminActionType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => AdminActionType.userManagement,
    );
  }
}

/// Admin action severity levels
enum AdminActionSeverity {
  info('info', 'Info', '#3b82f6'),
  warning('warning', 'Warning', '#f59e0b'),
  critical('critical', 'Critical', '#ef4444'),
  security('security', 'Security', '#8b5cf6');

  const AdminActionSeverity(this.value, this.displayName, this.color);
  
  final String value;
  final String displayName;
  final String color;
  
  static AdminActionSeverity fromString(String value) {
    return AdminActionSeverity.values.firstWhere(
      (severity) => severity.value == value,
      orElse: () => AdminActionSeverity.info,
    );
  }
}

/// Admin audit log entry model
class AdminAuditEntry {
  final String id;
  final DateTime timestamp;
  final String adminId;
  final String adminName;
  final String adminEmail;
  final AdminActionType actionType;
  final String action;
  final AdminActionSeverity severity;
  final String? targetUserId;
  final String? targetUserName;
  final String? targetUserEmail;
  final String? entityType;
  final String? entityId;
  final Map<String, dynamic> details;
  final String ipAddress;
  final String userAgent;
  final DateTime created;
  final DateTime updated;

  const AdminAuditEntry({
    required this.id,
    required this.timestamp,
    required this.adminId,
    required this.adminName,
    required this.adminEmail,
    required this.actionType,
    required this.action,
    required this.severity,
    this.targetUserId,
    this.targetUserName,
    this.targetUserEmail,
    this.entityType,
    this.entityId,
    required this.details,
    required this.ipAddress,
    required this.userAgent,
    required this.created,
    required this.updated,
  });

  /// Create AdminAuditEntry from PocketBase record
  factory AdminAuditEntry.fromRecord(RecordModel record) {
    final data = record.data;
    
    // Parse action type
    final actionTypeString = data['action_type'] as String? ?? 'user_management';
    final actionType = AdminActionType.fromString(actionTypeString);
    
    // Parse severity
    final severityString = data['severity'] as String? ?? 'info';
    final severity = AdminActionSeverity.fromString(severityString);
    
    // Parse dates
    final timestamp = DateTime.tryParse(data['timestamp'] as String? ?? '') ?? DateTime.now();
    final created = DateTime.tryParse(data['created'] as String? ?? '') ?? DateTime.now();
    final updated = DateTime.tryParse(data['updated'] as String? ?? '') ?? DateTime.now();
    
    // Parse details
    Map<String, dynamic> details = {};
    try {
      if (data['details'] != null) {
        details = Map<String, dynamic>.from(data['details'] as Map);
      }
    } catch (e) {
      details = {};
    }
    
    // Extract admin info from expanded data or details
    String adminName = 'Unknown Admin';
    String adminEmail = '';
    
    // Try to get admin info from expanded data first
    try {
      final adminData = record.get<RecordModel>('expand.admin_id');
      if (adminData != null) {
        adminName = adminData.data['name'] as String? ?? 'Unknown Admin';
        adminEmail = adminData.data['email'] as String? ?? '';
      }
    } catch (e) {
      // Fallback to details or default values
      adminName = details['admin_name'] as String? ?? 'Unknown Admin';
      adminEmail = details['admin_email'] as String? ?? '';
    }
    
    // Extract target user info
    String? targetUserName;
    String? targetUserEmail;
    
    try {
      final targetUserData = record.get<RecordModel>('expand.target_user_id');
      if (targetUserData != null) {
        targetUserName = targetUserData.data['name'] as String?;
        targetUserEmail = targetUserData.data['email'] as String?;
      }
    } catch (e) {
      // Fallback to details
      targetUserName = details['target_user_name'] as String?;
      targetUserEmail = details['target_user_email'] as String?;
    }
    
    return AdminAuditEntry(
      id: record.id,
      timestamp: timestamp,
      adminId: data['admin_id'] as String? ?? '',
      adminName: adminName,
      adminEmail: adminEmail,
      actionType: actionType,
      action: data['action'] as String? ?? '',
      severity: severity,
      targetUserId: data['target_user_id'] as String?,
      targetUserName: targetUserName,
      targetUserEmail: targetUserEmail,
      entityType: data['entity_type'] as String?,
      entityId: data['entity_id'] as String?,
      details: details,
      ipAddress: data['ip_address'] as String? ?? 'unknown',
      userAgent: data['user_agent'] as String? ?? 'unknown',
      created: created,
      updated: updated,
    );
  }

  /// Get formatted timestamp
  String get formattedTimestamp {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  /// Get action description
  String get actionDescription {
    if (targetUserName != null) {
      return '$action for $targetUserName';
    }
    return action;
  }

  /// Check if this is a security-related event
  bool get isSecurityEvent {
    return severity == AdminActionSeverity.security || 
           severity == AdminActionSeverity.critical ||
           actionType == AdminActionType.securityEvent;
  }

  /// Get severity color
  String get severityColor => severity.color;

  /// Convert to JSON for export
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'admin_id': adminId,
      'admin_name': adminName,
      'admin_email': adminEmail,
      'action_type': actionType.value,
      'action': action,
      'severity': severity.value,
      'target_user_id': targetUserId,
      'target_user_name': targetUserName,
      'target_user_email': targetUserEmail,
      'entity_type': entityType,
      'entity_id': entityId,
      'details': details,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'AdminAuditEntry(id: $id, action: $action, admin: $adminName, severity: ${severity.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdminAuditEntry && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Audit log filter model
class AuditLogFilter {
  final String? adminId;
  final String? action;
  final AdminActionType? actionType;
  final AdminActionSeverity? severity;
  final String? targetUserId;
  final DateTime? startDate;
  final DateTime? endDate;
  final String searchQuery;
  final int page;
  final int perPage;
  final String sortBy;

  const AuditLogFilter({
    this.adminId,
    this.action,
    this.actionType,
    this.severity,
    this.targetUserId,
    this.startDate,
    this.endDate,
    this.searchQuery = '',
    this.page = 1,
    this.perPage = 50,
    this.sortBy = '-created',
  });

  AuditLogFilter copyWith({
    String? adminId,
    String? action,
    AdminActionType? actionType,
    AdminActionSeverity? severity,
    String? targetUserId,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
    int? page,
    int? perPage,
    String? sortBy,
  }) {
    return AuditLogFilter(
      adminId: adminId ?? this.adminId,
      action: action ?? this.action,
      actionType: actionType ?? this.actionType,
      severity: severity ?? this.severity,
      targetUserId: targetUserId ?? this.targetUserId,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      searchQuery: searchQuery ?? this.searchQuery,
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      sortBy: sortBy ?? this.sortBy,
    );
  }
}

/// Export format options
enum AuditExportFormat {
  csv('csv', 'CSV'),
  json('json', 'JSON'),
  pdf('pdf', 'PDF');

  const AuditExportFormat(this.value, this.displayName);
  
  final String value;
  final String displayName;
}

/// Export request model
class AuditExportRequest {
  final AuditLogFilter filter;
  final AuditExportFormat format;
  final List<String> fields;
  final String filename;

  const AuditExportRequest({
    required this.filter,
    required this.format,
    required this.fields,
    required this.filename,
  });
}

/// Security alert model
class SecurityAlert {
  final String id;
  final AdminAuditEntry auditEntry;
  final String alertType;
  final String description;
  final DateTime triggeredAt;
  final bool acknowledged;
  final String? acknowledgedBy;
  final DateTime? acknowledgedAt;

  const SecurityAlert({
    required this.id,
    required this.auditEntry,
    required this.alertType,
    required this.description,
    required this.triggeredAt,
    required this.acknowledged,
    this.acknowledgedBy,
    this.acknowledgedAt,
  });
}

/// Audit statistics model
class AuditStatistics {
  final int totalEntries;
  final int todayEntries;
  final int securityEvents;
  final int criticalEvents;
  final Map<AdminActionType, int> actionTypeCounts;
  final Map<AdminActionSeverity, int> severityCounts;
  final List<String> topAdmins;
  final DateTime lastUpdated;

  const AuditStatistics({
    required this.totalEntries,
    required this.todayEntries,
    required this.securityEvents,
    required this.criticalEvents,
    required this.actionTypeCounts,
    required this.severityCounts,
    required this.topAdmins,
    required this.lastUpdated,
  });
}
