/// User activity event types
enum UserActivityType {
  login('login', 'Login'),
  logout('logout', 'Logout'),
  pageView('page_view', 'Page View'),
  documentUpload('document_upload', 'Document Upload'),
  documentDownload('document_download', 'Document Download'),
  formSubmission('form_submission', 'Form Submission'),
  searchQuery('search_query', 'Search Query'),
  profileUpdate('profile_update', 'Profile Update'),
  claimSubmission('claim_submission', 'Claim Submission'),
  investmentAction('investment_action', 'Investment Action'),
  communicationSent('communication_sent', 'Communication Sent'),
  notificationRead('notification_read', 'Notification Read');

  const UserActivityType(this.value, this.displayName);

  final String value;
  final String displayName;

  static UserActivityType fromString(String value) {
    return UserActivityType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => UserActivityType.pageView,
    );
  }
}

/// Activity monitoring scope
enum ActivityScope {
  allUsers('all_users', 'All Users'),
  solicitors('solicitors', 'Solicitors'),
  coFunders('co_funders', 'Co-Funders'),
  claimants('claimants', 'Claimants'),
  specificUser('specific_user', 'Specific User');

  const ActivityScope(this.value, this.displayName);

  final String value;
  final String displayName;
}

/// Engagement metrics types
enum EngagementMetric {
  sessionDuration('session_duration', 'Session Duration'),
  pageViews('page_views', 'Page Views'),
  actionsPerSession('actions_per_session', 'Actions Per Session'),
  returnVisitRate('return_visit_rate', 'Return Visit Rate'),
  featureUsage('feature_usage', 'Feature Usage'),
  documentInteractions('document_interactions', 'Document Interactions');

  const EngagementMetric(this.value, this.displayName);

  final String value;
  final String displayName;
}

/// User activity event model
class UserActivityEvent {
  final String id;
  final String userId;
  final String? userName;
  final String? userType;
  final UserActivityType type;
  final String description;
  final Map<String, dynamic> details;
  final DateTime timestamp;
  final String? sessionId;
  final String? ipAddress;
  final String? userAgent;

  const UserActivityEvent({
    required this.id,
    required this.userId,
    this.userName,
    this.userType,
    required this.type,
    required this.description,
    required this.details,
    required this.timestamp,
    this.sessionId,
    this.ipAddress,
    this.userAgent,
  });

  factory UserActivityEvent.fromJson(Map<String, dynamic> json) {
    return UserActivityEvent(
      id: json['id'] as String? ?? '',
      userId: json['user_id'] as String? ?? '',
      userName: json['user_name'] as String?,
      userType: json['user_type'] as String?,
      type: UserActivityType.fromString(json['activity_type'] as String? ?? ''),
      description: json['description'] as String? ?? '',
      details: Map<String, dynamic>.from(json['details'] as Map? ?? {}),
      timestamp: DateTime.tryParse(json['timestamp'] as String? ?? '') ?? DateTime.now(),
      sessionId: json['session_id'] as String?,
      ipAddress: json['ip_address'] as String?,
      userAgent: json['user_agent'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'user_name': userName,
      'user_type': userType,
      'activity_type': type.value,
      'description': description,
      'details': details,
      'timestamp': timestamp.toIso8601String(),
      'session_id': sessionId,
      'ip_address': ipAddress,
      'user_agent': userAgent,
    };
  }

  String get formattedTimestamp {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  UserActivityEvent copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userType,
    UserActivityType? type,
    String? description,
    Map<String, dynamic>? details,
    DateTime? timestamp,
    String? sessionId,
    String? ipAddress,
    String? userAgent,
  }) {
    return UserActivityEvent(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userType: userType ?? this.userType,
      type: type ?? this.type,
      description: description ?? this.description,
      details: details ?? this.details,
      timestamp: timestamp ?? this.timestamp,
      sessionId: sessionId ?? this.sessionId,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
    );
  }
}

/// User session model
class UserSession {
  final String id;
  final String userId;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration duration;
  final List<UserActivityEvent> activities;
  final String? ipAddress;
  final String? userAgent;

  const UserSession({
    required this.id,
    required this.userId,
    required this.startTime,
    this.endTime,
    required this.duration,
    required this.activities,
    this.ipAddress,
    this.userAgent,
  });

  factory UserSession.fromActivities(List<UserActivityEvent> activities) {
    if (activities.isEmpty) {
      throw ArgumentError('Cannot create session from empty activities list');
    }

    final sortedActivities = activities..sort((a, b) => a.timestamp.compareTo(b.timestamp));
    final startTime = sortedActivities.first.timestamp;
    final endTime = sortedActivities.last.timestamp;
    final duration = endTime.difference(startTime);

    return UserSession(
      id: sortedActivities.first.sessionId ?? 'unknown',
      userId: sortedActivities.first.userId,
      startTime: startTime,
      endTime: endTime,
      duration: duration,
      activities: sortedActivities,
      ipAddress: sortedActivities.first.ipAddress,
      userAgent: sortedActivities.first.userAgent,
    );
  }

  bool get isActive => endTime == null;
  int get activityCount => activities.length;
  int get pageViews => activities.where((a) => a.type == UserActivityType.pageView).length;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'duration_minutes': duration.inMinutes,
      'activity_count': activityCount,
      'page_views': pageViews,
      'ip_address': ipAddress,
      'user_agent': userAgent,
    };
  }
}

/// User engagement metrics model
class UserEngagementMetrics {
  final double sessionDuration;
  final int pageViews;
  final double actionsPerSession;
  final Map<String, int> featureUsage;
  final int totalActivities;
  final int totalSessions;
  final double returnVisitRate;

  const UserEngagementMetrics({
    required this.sessionDuration,
    required this.pageViews,
    required this.actionsPerSession,
    required this.featureUsage,
    required this.totalActivities,
    this.totalSessions = 0,
    this.returnVisitRate = 0.0,
  });

  const UserEngagementMetrics.empty()
      : sessionDuration = 0.0,
        pageViews = 0,
        actionsPerSession = 0.0,
        featureUsage = const {},
        totalActivities = 0,
        totalSessions = 0,
        returnVisitRate = 0.0;

  factory UserEngagementMetrics.fromJson(Map<String, dynamic> json) {
    return UserEngagementMetrics(
      sessionDuration: (json['session_duration'] as num?)?.toDouble() ?? 0.0,
      pageViews: json['page_views'] as int? ?? 0,
      actionsPerSession: (json['actions_per_session'] as num?)?.toDouble() ?? 0.0,
      featureUsage: Map<String, int>.from(json['feature_usage'] as Map? ?? {}),
      totalActivities: json['total_activities'] as int? ?? 0,
      totalSessions: json['total_sessions'] as int? ?? 0,
      returnVisitRate: (json['return_visit_rate'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'session_duration': sessionDuration,
      'page_views': pageViews,
      'actions_per_session': actionsPerSession,
      'feature_usage': featureUsage,
      'total_activities': totalActivities,
      'total_sessions': totalSessions,
      'return_visit_rate': returnVisitRate,
    };
  }

  String get formattedSessionDuration {
    if (sessionDuration >= 60) {
      return '${(sessionDuration / 60).toStringAsFixed(1)}h';
    } else {
      return '${sessionDuration.toStringAsFixed(1)}m';
    }
  }

  UserEngagementMetrics copyWith({
    double? sessionDuration,
    int? pageViews,
    double? actionsPerSession,
    Map<String, int>? featureUsage,
    int? totalActivities,
    int? totalSessions,
    double? returnVisitRate,
  }) {
    return UserEngagementMetrics(
      sessionDuration: sessionDuration ?? this.sessionDuration,
      pageViews: pageViews ?? this.pageViews,
      actionsPerSession: actionsPerSession ?? this.actionsPerSession,
      featureUsage: featureUsage ?? this.featureUsage,
      totalActivities: totalActivities ?? this.totalActivities,
      totalSessions: totalSessions ?? this.totalSessions,
      returnVisitRate: returnVisitRate ?? this.returnVisitRate,
    );
  }
}

/// Activity pattern model
class ActivityPattern {
  final String userId;
  final Map<int, int> hourlyDistribution; // Hour of day -> activity count
  final Map<int, int> dailyDistribution; // Day of week -> activity count
  final Map<UserActivityType, int> typeDistribution;
  final List<String> mostUsedFeatures;
  final double averageSessionLength;
  final DateTime lastActivity;

  const ActivityPattern({
    required this.userId,
    required this.hourlyDistribution,
    required this.dailyDistribution,
    required this.typeDistribution,
    required this.mostUsedFeatures,
    required this.averageSessionLength,
    required this.lastActivity,
  });

  factory ActivityPattern.fromActivities(String userId, List<UserActivityEvent> activities) {
    final hourlyDist = <int, int>{};
    final dailyDist = <int, int>{};
    final typeDist = <UserActivityType, int>{};
    final featureUsage = <String, int>{};

    for (final activity in activities) {
      final hour = activity.timestamp.hour;
      final day = activity.timestamp.weekday;

      hourlyDist[hour] = (hourlyDist[hour] ?? 0) + 1;
      dailyDist[day] = (dailyDist[day] ?? 0) + 1;
      typeDist[activity.type] = (typeDist[activity.type] ?? 0) + 1;

      final feature = activity.details['feature'] as String? ?? 'unknown';
      featureUsage[feature] = (featureUsage[feature] ?? 0) + 1;
    }

    final mostUsedFeatures = featureUsage.entries
        .toList()
        ..sort((a, b) => b.value.compareTo(a.value))
        ..take(5)
        .map((e) => e.key)
        .toList();

    return ActivityPattern(
      userId: userId,
      hourlyDistribution: hourlyDist,
      dailyDistribution: dailyDist,
      typeDistribution: typeDist,
      mostUsedFeatures: mostUsedFeatures,
      averageSessionLength: 0.0, // Would be calculated from sessions
      lastActivity: activities.isNotEmpty 
          ? activities.map((a) => a.timestamp).reduce((a, b) => a.isAfter(b) ? a : b)
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'hourly_distribution': hourlyDistribution,
      'daily_distribution': dailyDistribution,
      'type_distribution': typeDistribution.map((k, v) => MapEntry(k.value, v)),
      'most_used_features': mostUsedFeatures,
      'average_session_length': averageSessionLength,
      'last_activity': lastActivity.toIso8601String(),
    };
  }
}
