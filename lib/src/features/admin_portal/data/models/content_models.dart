/// Content types supported by the platform
enum ContentType {
  blogPost('blog_post', 'Blog Post'),
  educationalContent('educational_content', 'Educational Content'),
  podcast('podcast', 'Podcast'),
  announcement('announcement', 'Announcement'),
  newsletter('newsletter', 'Newsletter');

  const ContentType(this.value, this.displayName);

  final String value;
  final String displayName;

  static ContentType fromString(String value) {
    return ContentType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ContentType.blogPost,
    );
  }
}

/// Content publication status
enum ContentStatus {
  draft('draft', 'Draft'),
  review('review', 'Under Review'),
  published('published', 'Published'),
  archived('archived', 'Archived'),
  scheduled('scheduled', 'Scheduled');

  const ContentStatus(this.value, this.displayName);

  final String value;
  final String displayName;

  static ContentStatus fromString(String value) {
    return ContentStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => ContentStatus.draft,
    );
  }
}

/// Content categories for organization
enum ContentCategory {
  litigation('litigation', 'Litigation'),
  funding('funding', 'Funding'),
  legal('legal', 'Legal'),
  business('business', 'Business'),
  technology('technology', 'Technology'),
  news('news', 'News');

  const ContentCategory(this.value, this.displayName);

  final String value;
  final String displayName;

  static ContentCategory fromString(String value) {
    return ContentCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => ContentCategory.news,
    );
  }
}

/// Rich text editor features
enum EditorFeature {
  bold('bold', 'Bold'),
  italic('italic', 'Italic'),
  underline('underline', 'Underline'),
  heading('heading', 'Heading'),
  bulletList('bullet_list', 'Bullet List'),
  numberedList('numbered_list', 'Numbered List'),
  link('link', 'Link'),
  image('image', 'Image'),
  video('video', 'Video'),
  quote('quote', 'Quote'),
  code('code', 'Code'),
  table('table', 'Table');

  const EditorFeature(this.value, this.displayName);

  final String value;
  final String displayName;
}

/// Base content item model
class ContentItem {
  final String id;
  final String title;
  final String content;
  final String? excerpt;
  final String slug;
  final String authorId;
  final String? authorName;
  final ContentType type;
  final ContentStatus status;
  final ContentCategory category;
  final List<String> tags;
  final String? featuredImageId;
  final String? featuredImageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? publishedAt;
  final DateTime? scheduledAt;
  final Map<String, dynamic> seoMetadata;
  final Map<String, dynamic> metadata;

  const ContentItem({
    required this.id,
    required this.title,
    required this.content,
    this.excerpt,
    required this.slug,
    required this.authorId,
    this.authorName,
    required this.type,
    required this.status,
    required this.category,
    required this.tags,
    this.featuredImageId,
    this.featuredImageUrl,
    required this.createdAt,
    required this.updatedAt,
    this.publishedAt,
    this.scheduledAt,
    required this.seoMetadata,
    required this.metadata,
  });

  factory ContentItem.fromJson(Map<String, dynamic> json) {
    return ContentItem(
      id: json['id'] as String? ?? '',
      title: json['title'] as String? ?? '',
      content: json['content'] as String? ?? '',
      excerpt: json['excerpt'] as String?,
      slug: json['slug'] as String? ?? '',
      authorId: json['author_id'] as String? ?? '',
      authorName: json['author_name'] as String?,
      type: ContentType.fromString(json['content_type'] as String? ?? ''),
      status: ContentStatus.fromString(json['status'] as String? ?? ''),
      category: ContentCategory.fromString(json['category'] as String? ?? ''),
      tags: List<String>.from(json['tags'] as List? ?? []),
      featuredImageId: json['featured_image_id'] as String?,
      featuredImageUrl: json['featured_image_url'] as String?,
      createdAt: DateTime.tryParse(json['created'] as String? ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updated'] as String? ?? '') ?? DateTime.now(),
      publishedAt: json['published_at'] != null ? DateTime.tryParse(json['published_at'] as String) : null,
      scheduledAt: json['scheduled_at'] != null ? DateTime.tryParse(json['scheduled_at'] as String) : null,
      seoMetadata: Map<String, dynamic>.from(json['seo_metadata'] as Map? ?? {}),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'excerpt': excerpt,
      'slug': slug,
      'author_id': authorId,
      'author_name': authorName,
      'content_type': type.value,
      'status': status.value,
      'category': category.value,
      'tags': tags,
      'featured_image_id': featuredImageId,
      'featured_image_url': featuredImageUrl,
      'created': createdAt.toIso8601String(),
      'updated': updatedAt.toIso8601String(),
      'published_at': publishedAt?.toIso8601String(),
      'scheduled_at': scheduledAt?.toIso8601String(),
      'seo_metadata': seoMetadata,
      'metadata': metadata,
    };
  }

  bool get isPublished => status == ContentStatus.published;
  bool get isDraft => status == ContentStatus.draft;
  bool get isScheduled => status == ContentStatus.scheduled;

  String get formattedCreatedAt {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }

  String get formattedPublishedAt {
    if (publishedAt == null) return 'Not published';
    return '${publishedAt!.day}/${publishedAt!.month}/${publishedAt!.year}';
  }

  ContentItem copyWith({
    String? id,
    String? title,
    String? content,
    String? excerpt,
    String? slug,
    String? authorId,
    String? authorName,
    ContentType? type,
    ContentStatus? status,
    ContentCategory? category,
    List<String>? tags,
    String? featuredImageId,
    String? featuredImageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? publishedAt,
    DateTime? scheduledAt,
    Map<String, dynamic>? seoMetadata,
    Map<String, dynamic>? metadata,
  }) {
    return ContentItem(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      excerpt: excerpt ?? this.excerpt,
      slug: slug ?? this.slug,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      type: type ?? this.type,
      status: status ?? this.status,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      featuredImageId: featuredImageId ?? this.featuredImageId,
      featuredImageUrl: featuredImageUrl ?? this.featuredImageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      publishedAt: publishedAt ?? this.publishedAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      seoMetadata: seoMetadata ?? this.seoMetadata,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Blog post specific model
class BlogPost extends ContentItem {
  final String? metaDescription;
  final List<String> keywords;
  final String? canonicalUrl;
  final bool allowComments;
  final int readingTime;

  const BlogPost({
    required super.id,
    required super.title,
    required super.content,
    super.excerpt,
    required super.slug,
    required super.authorId,
    super.authorName,
    required super.type,
    required super.status,
    required super.category,
    required super.tags,
    super.featuredImageId,
    super.featuredImageUrl,
    required super.createdAt,
    required super.updatedAt,
    super.publishedAt,
    super.scheduledAt,
    required super.seoMetadata,
    required super.metadata,
    this.metaDescription,
    required this.keywords,
    this.canonicalUrl,
    this.allowComments = true,
    this.readingTime = 0,
  });

  factory BlogPost.fromContentItem(ContentItem item) {
    return BlogPost(
      id: item.id,
      title: item.title,
      content: item.content,
      excerpt: item.excerpt,
      slug: item.slug,
      authorId: item.authorId,
      authorName: item.authorName,
      type: item.type,
      status: item.status,
      category: item.category,
      tags: item.tags,
      featuredImageId: item.featuredImageId,
      featuredImageUrl: item.featuredImageUrl,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      publishedAt: item.publishedAt,
      scheduledAt: item.scheduledAt,
      seoMetadata: item.seoMetadata,
      metadata: item.metadata,
      metaDescription: item.seoMetadata['meta_description'] as String?,
      keywords: List<String>.from(item.seoMetadata['keywords'] as List? ?? []),
      canonicalUrl: item.seoMetadata['canonical_url'] as String?,
      allowComments: item.metadata['allow_comments'] as bool? ?? true,
      readingTime: item.metadata['reading_time'] as int? ?? 0,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json['seo_metadata'] = {
      ...seoMetadata,
      'meta_description': metaDescription,
      'keywords': keywords,
      'canonical_url': canonicalUrl,
    };
    json['metadata'] = {
      ...metadata,
      'allow_comments': allowComments,
      'reading_time': readingTime,
    };
    return json;
  }
}

/// Media asset model for content
class MediaAsset {
  final String id;
  final String fileName;
  final String originalName;
  final String fileType;
  final int fileSize;
  final String url;
  final String? thumbnailUrl;
  final String? altText;
  final String? caption;
  final String contentId;
  final DateTime uploadedAt;
  final Map<String, dynamic> metadata;

  const MediaAsset({
    required this.id,
    required this.fileName,
    required this.originalName,
    required this.fileType,
    required this.fileSize,
    required this.url,
    this.thumbnailUrl,
    this.altText,
    this.caption,
    required this.contentId,
    required this.uploadedAt,
    required this.metadata,
  });

  factory MediaAsset.fromJson(Map<String, dynamic> json) {
    return MediaAsset(
      id: json['id'] as String? ?? '',
      fileName: json['file_name'] as String? ?? '',
      originalName: json['original_name'] as String? ?? '',
      fileType: json['file_type'] as String? ?? '',
      fileSize: json['file_size'] as int? ?? 0,
      url: json['url'] as String? ?? '',
      thumbnailUrl: json['thumbnail_url'] as String?,
      altText: json['alt_text'] as String?,
      caption: json['caption'] as String?,
      contentId: json['content_id'] as String? ?? '',
      uploadedAt: DateTime.tryParse(json['uploaded_at'] as String? ?? '') ?? DateTime.now(),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'file_name': fileName,
      'original_name': originalName,
      'file_type': fileType,
      'file_size': fileSize,
      'url': url,
      'thumbnail_url': thumbnailUrl,
      'alt_text': altText,
      'caption': caption,
      'content_id': contentId,
      'uploaded_at': uploadedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  bool get isImage => fileType.startsWith('image/');
  bool get isVideo => fileType.startsWith('video/');
  bool get isAudio => fileType.startsWith('audio/');

  String get formattedFileSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}
