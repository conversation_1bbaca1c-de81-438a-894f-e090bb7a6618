/// Platform KPI categories for analytics
enum PlatformKPI {
  totalUsers('total_users', 'Total Users'),
  activeUsers('active_users', 'Active Users'),
  userGrowthRate('user_growth_rate', 'User Growth Rate'),
  totalClaims('total_claims', 'Total Claims'),
  activeClaims('active_claims', 'Active Claims'),
  claimSuccessRate('claim_success_rate', 'Claim Success Rate'),
  fundingVolume('funding_volume', 'Funding Volume'),
  averageFundingAmount('average_funding_amount', 'Average Funding Amount'),
  platformRevenue('platform_revenue', 'Platform Revenue'),
  userEngagement('user_engagement', 'User Engagement'),
  systemUptime('system_uptime', 'System Uptime'),
  responseTime('response_time', 'Response Time');

  const PlatformKPI(this.value, this.displayName);

  final String value;
  final String displayName;
}

/// Analytics time periods
enum AnalyticsTimePeriod {
  last24Hours('last_24_hours', 'Last 24 Hours'),
  last7Days('last_7_days', 'Last 7 Days'),
  last30Days('last_30_days', 'Last 30 Days'),
  last90Days('last_90_days', 'Last 90 Days'),
  lastYear('last_year', 'Last Year'),
  custom('custom', 'Custom Range');

  const AnalyticsTimePeriod(this.value, this.displayName);

  final String value;
  final String displayName;
}

/// Chart types for analytics visualization
enum ChartType {
  line('line', 'Line Chart'),
  bar('bar', 'Bar Chart'),
  pie('pie', 'Pie Chart'),
  area('area', 'Area Chart'),
  scatter('scatter', 'Scatter Plot'),
  heatmap('heatmap', 'Heat Map');

  const ChartType(this.value, this.displayName);

  final String value;
  final String displayName;
}

/// Chart data point model
class ChartDataPoint {
  final DateTime timestamp;
  final double value;
  final String? label;
  final Map<String, dynamic>? metadata;

  const ChartDataPoint({
    required this.timestamp,
    required this.value,
    this.label,
    this.metadata,
  });

  factory ChartDataPoint.fromJson(Map<String, dynamic> json) {
    return ChartDataPoint(
      timestamp:
          DateTime.tryParse(json['timestamp'] as String? ?? '') ??
          DateTime.now(),
      value: (json['value'] as num?)?.toDouble() ?? 0.0,
      label: json['label'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'value': value,
      'label': label,
      'metadata': metadata,
    };
  }

  ChartDataPoint copyWith({
    DateTime? timestamp,
    double? value,
    String? label,
    Map<String, dynamic>? metadata,
  }) {
    return ChartDataPoint(
      timestamp: timestamp ?? this.timestamp,
      value: value ?? this.value,
      label: label ?? this.label,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// User metrics model
class UserMetrics {
  final int totalUsers;
  final int solicitors;
  final int coFunders;
  final int claimants;
  final Map<DateTime, int> dailyRegistrations;
  final double growthRate;
  final Map<String, int> typeDistribution;

  const UserMetrics({
    required this.totalUsers,
    required this.solicitors,
    required this.coFunders,
    required this.claimants,
    required this.dailyRegistrations,
    required this.growthRate,
    required this.typeDistribution,
  });

  factory UserMetrics.fromJson(Map<String, dynamic> json) {
    return UserMetrics(
      totalUsers: json['total_users'] as int? ?? 0,
      solicitors: json['solicitors'] as int? ?? 0,
      coFunders: json['co_funders'] as int? ?? 0,
      claimants: json['claimants'] as int? ?? 0,
      dailyRegistrations: _parseTimeSeriesData(
        json['daily_registrations'] as Map?,
      ),
      growthRate: (json['growth_rate'] as num?)?.toDouble() ?? 0.0,
      typeDistribution: Map<String, int>.from(
        json['type_distribution'] as Map? ?? {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_users': totalUsers,
      'solicitors': solicitors,
      'co_funders': coFunders,
      'claimants': claimants,
      'daily_registrations': _serializeTimeSeriesData(dailyRegistrations),
      'growth_rate': growthRate,
      'type_distribution': typeDistribution,
    };
  }

  static Map<DateTime, int> _parseTimeSeriesData(Map? data) {
    if (data == null) return {};
    final result = <DateTime, int>{};
    data.forEach((key, value) {
      final date = DateTime.tryParse(key.toString());
      if (date != null) {
        result[date] = (value as num?)?.toInt() ?? 0;
      }
    });
    return result;
  }

  static Map<String, int> _serializeTimeSeriesData(Map<DateTime, int> data) {
    final result = <String, int>{};
    data.forEach((key, value) {
      result[key.toIso8601String()] = value;
    });
    return result;
  }
}

/// Claim metrics model
class ClaimMetrics {
  final int totalClaims;
  final int activeClaims;
  final int completedClaims;
  final double successRate;
  final Map<DateTime, int> dailySubmissions;
  final Map<String, int> statusDistribution;

  const ClaimMetrics({
    required this.totalClaims,
    required this.activeClaims,
    required this.completedClaims,
    required this.successRate,
    required this.dailySubmissions,
    required this.statusDistribution,
  });

  factory ClaimMetrics.fromJson(Map<String, dynamic> json) {
    return ClaimMetrics(
      totalClaims: json['total_claims'] as int? ?? 0,
      activeClaims: json['active_claims'] as int? ?? 0,
      completedClaims: json['completed_claims'] as int? ?? 0,
      successRate: (json['success_rate'] as num?)?.toDouble() ?? 0.0,
      dailySubmissions: UserMetrics._parseTimeSeriesData(
        json['daily_submissions'] as Map?,
      ),
      statusDistribution: Map<String, int>.from(
        json['status_distribution'] as Map? ?? {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_claims': totalClaims,
      'active_claims': activeClaims,
      'completed_claims': completedClaims,
      'success_rate': successRate,
      'daily_submissions': UserMetrics._serializeTimeSeriesData(
        dailySubmissions,
      ),
      'status_distribution': statusDistribution,
    };
  }
}

/// Funding metrics model
class FundingMetrics {
  final double totalVolume;
  final double averageAmount;
  final int totalInvestments;
  final Map<DateTime, double> dailyVolume;
  final Map<String, double> volumeByType;

  const FundingMetrics({
    required this.totalVolume,
    required this.averageAmount,
    required this.totalInvestments,
    required this.dailyVolume,
    required this.volumeByType,
  });

  factory FundingMetrics.fromJson(Map<String, dynamic> json) {
    return FundingMetrics(
      totalVolume: (json['total_volume'] as num?)?.toDouble() ?? 0.0,
      averageAmount: (json['average_amount'] as num?)?.toDouble() ?? 0.0,
      totalInvestments: json['total_investments'] as int? ?? 0,
      dailyVolume: _parseTimeSeriesDoubleData(json['daily_volume'] as Map?),
      volumeByType: Map<String, double>.from(
        json['volume_by_type'] as Map? ?? {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_volume': totalVolume,
      'average_amount': averageAmount,
      'total_investments': totalInvestments,
      'daily_volume': _serializeTimeSeriesDoubleData(dailyVolume),
      'volume_by_type': volumeByType,
    };
  }

  static Map<DateTime, double> _parseTimeSeriesDoubleData(Map? data) {
    if (data == null) return {};
    final result = <DateTime, double>{};
    data.forEach((key, value) {
      final date = DateTime.tryParse(key.toString());
      if (date != null) {
        result[date] = (value as num?)?.toDouble() ?? 0.0;
      }
    });
    return result;
  }

  static Map<String, double> _serializeTimeSeriesDoubleData(
    Map<DateTime, double> data,
  ) {
    final result = <String, double>{};
    data.forEach((key, value) {
      result[key.toIso8601String()] = value;
    });
    return result;
  }
}

/// Engagement metrics model
class EngagementMetrics {
  final double averageSessionDuration;
  final int totalSessions;
  final double bounceRate;
  final Map<String, int> pageViews;
  final Map<DateTime, int> dailyActiveUsers;

  const EngagementMetrics({
    required this.averageSessionDuration,
    required this.totalSessions,
    required this.bounceRate,
    required this.pageViews,
    required this.dailyActiveUsers,
  });

  factory EngagementMetrics.fromJson(Map<String, dynamic> json) {
    return EngagementMetrics(
      averageSessionDuration:
          (json['average_session_duration'] as num?)?.toDouble() ?? 0.0,
      totalSessions: json['total_sessions'] as int? ?? 0,
      bounceRate: (json['bounce_rate'] as num?)?.toDouble() ?? 0.0,
      pageViews: Map<String, int>.from(json['page_views'] as Map? ?? {}),
      dailyActiveUsers: UserMetrics._parseTimeSeriesData(
        json['daily_active_users'] as Map?,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'average_session_duration': averageSessionDuration,
      'total_sessions': totalSessions,
      'bounce_rate': bounceRate,
      'page_views': pageViews,
      'daily_active_users': UserMetrics._serializeTimeSeriesData(
        dailyActiveUsers,
      ),
    };
  }
}

/// System metrics model
class SystemMetrics {
  final double uptime;
  final double averageResponseTime;
  final int totalRequests;
  final int errorCount;
  final double errorRate;
  final Map<DateTime, double> responseTimeHistory;

  const SystemMetrics({
    required this.uptime,
    required this.averageResponseTime,
    required this.totalRequests,
    required this.errorCount,
    required this.errorRate,
    required this.responseTimeHistory,
  });

  factory SystemMetrics.fromJson(Map<String, dynamic> json) {
    return SystemMetrics(
      uptime: (json['uptime'] as num?)?.toDouble() ?? 0.0,
      averageResponseTime:
          (json['average_response_time'] as num?)?.toDouble() ?? 0.0,
      totalRequests: json['total_requests'] as int? ?? 0,
      errorCount: json['error_count'] as int? ?? 0,
      errorRate: (json['error_rate'] as num?)?.toDouble() ?? 0.0,
      responseTimeHistory: FundingMetrics._parseTimeSeriesDoubleData(
        json['response_time_history'] as Map?,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uptime': uptime,
      'average_response_time': averageResponseTime,
      'total_requests': totalRequests,
      'error_count': errorCount,
      'error_rate': errorRate,
      'response_time_history': FundingMetrics._serializeTimeSeriesDoubleData(
        responseTimeHistory,
      ),
    };
  }
}

/// Comprehensive platform metrics model
class PlatformMetrics {
  final UserMetrics userMetrics;
  final ClaimMetrics claimMetrics;
  final FundingMetrics fundingMetrics;
  final EngagementMetrics engagementMetrics;
  final SystemMetrics systemMetrics;
  final AnalyticsTimePeriod period;
  final DateTime startDate;
  final DateTime endDate;

  const PlatformMetrics({
    required this.userMetrics,
    required this.claimMetrics,
    required this.fundingMetrics,
    required this.engagementMetrics,
    required this.systemMetrics,
    required this.period,
    required this.startDate,
    required this.endDate,
  });

  factory PlatformMetrics.fromJson(Map<String, dynamic> json) {
    return PlatformMetrics(
      userMetrics: UserMetrics.fromJson(
        json['user_metrics'] as Map<String, dynamic>? ?? {},
      ),
      claimMetrics: ClaimMetrics.fromJson(
        json['claim_metrics'] as Map<String, dynamic>? ?? {},
      ),
      fundingMetrics: FundingMetrics.fromJson(
        json['funding_metrics'] as Map<String, dynamic>? ?? {},
      ),
      engagementMetrics: EngagementMetrics.fromJson(
        json['engagement_metrics'] as Map<String, dynamic>? ?? {},
      ),
      systemMetrics: SystemMetrics.fromJson(
        json['system_metrics'] as Map<String, dynamic>? ?? {},
      ),
      period: AnalyticsTimePeriod.values.firstWhere(
        (p) => p.value == json['period'],
        orElse: () => AnalyticsTimePeriod.last30Days,
      ),
      startDate:
          DateTime.tryParse(json['start_date'] as String? ?? '') ??
          DateTime.now(),
      endDate:
          DateTime.tryParse(json['end_date'] as String? ?? '') ??
          DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_metrics': userMetrics.toJson(),
      'claim_metrics': claimMetrics.toJson(),
      'funding_metrics': fundingMetrics.toJson(),
      'engagement_metrics': engagementMetrics.toJson(),
      'system_metrics': systemMetrics.toJson(),
      'period': period.value,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
    };
  }

  /// Check if metrics data is stale (older than 5 minutes)
  bool get isStale {
    return DateTime.now().difference(endDate).inMinutes > 5;
  }

  /// Get formatted date range string
  String get formattedDateRange {
    if (period == AnalyticsTimePeriod.custom) {
      return '${_formatDate(startDate)} - ${_formatDate(endDate)}';
    }
    return period.displayName;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  PlatformMetrics copyWith({
    UserMetrics? userMetrics,
    ClaimMetrics? claimMetrics,
    FundingMetrics? fundingMetrics,
    EngagementMetrics? engagementMetrics,
    SystemMetrics? systemMetrics,
    AnalyticsTimePeriod? period,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return PlatformMetrics(
      userMetrics: userMetrics ?? this.userMetrics,
      claimMetrics: claimMetrics ?? this.claimMetrics,
      fundingMetrics: fundingMetrics ?? this.fundingMetrics,
      engagementMetrics: engagementMetrics ?? this.engagementMetrics,
      systemMetrics: systemMetrics ?? this.systemMetrics,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }
}
