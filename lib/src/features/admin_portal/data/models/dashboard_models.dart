/// Dashboard KPI categories
enum DashboardKPI {
  totalUsers('total_users', 'Total Users'),
  activeUsers('active_users', 'Active Users'),
  newRegistrations('new_registrations', 'New Registrations'),
  totalClaims('total_claims', 'Total Claims'),
  activeClaims('active_claims', 'Active Claims'),
  fundingVolume('funding_volume', 'Funding Volume'),
  systemHealth('system_health', 'System Health'),
  recentActivity('recent_activity', 'Recent Activity');

  const DashboardKPI(this.value, this.displayName);

  final String value;
  final String displayName;
}

/// Trend indicator for KPI cards
enum TrendIndicator {
  up('up', 'Increasing'),
  down('down', 'Decreasing'),
  stable('stable', 'Stable');

  const TrendIndicator(this.value, this.displayName);

  final String value;
  final String displayName;
}

/// System health status
enum SystemHealthStatus {
  healthy('healthy', 'Healthy', '#10b981'),
  warning('warning', 'Warning', '#f59e0b'),
  critical('critical', 'Critical', '#ef4444'),
  maintenance('maintenance', 'Maintenance', '#6b7280');

  const SystemHealthStatus(this.value, this.displayName, this.color);

  final String value;
  final String displayName;
  final String color;

  static SystemHealthStatus fromString(String value) {
    return SystemHealthStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => SystemHealthStatus.healthy,
    );
  }
}

/// System health model
class SystemHealth {
  final SystemHealthStatus status;
  final String description;
  final double cpuUsage;
  final double memoryUsage;
  final double diskUsage;
  final int activeConnections;
  final DateTime lastChecked;

  const SystemHealth({
    required this.status,
    required this.description,
    required this.cpuUsage,
    required this.memoryUsage,
    required this.diskUsage,
    required this.activeConnections,
    required this.lastChecked,
  });

  factory SystemHealth.fromJson(Map<String, dynamic> json) {
    return SystemHealth(
      status: SystemHealthStatus.fromString(
        json['status'] as String? ?? 'healthy',
      ),
      description:
          json['description'] as String? ?? 'System is running normally',
      cpuUsage: (json['cpu_usage'] as num?)?.toDouble() ?? 0.0,
      memoryUsage: (json['memory_usage'] as num?)?.toDouble() ?? 0.0,
      diskUsage: (json['disk_usage'] as num?)?.toDouble() ?? 0.0,
      activeConnections: json['active_connections'] as int? ?? 0,
      lastChecked:
          DateTime.tryParse(json['last_checked'] as String? ?? '') ??
          DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status.value,
      'description': description,
      'cpu_usage': cpuUsage,
      'memory_usage': memoryUsage,
      'disk_usage': diskUsage,
      'active_connections': activeConnections,
      'last_checked': lastChecked.toIso8601String(),
    };
  }
}

/// Recent activity model
class RecentActivity {
  final String id;
  final String type;
  final String description;
  final String? userId;
  final String? userName;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const RecentActivity({
    required this.id,
    required this.type,
    required this.description,
    this.userId,
    this.userName,
    required this.timestamp,
    required this.metadata,
  });

  factory RecentActivity.fromJson(Map<String, dynamic> json) {
    return RecentActivity(
      id: json['id'] as String? ?? '',
      type: json['type'] as String? ?? '',
      description: json['description'] as String? ?? '',
      userId: json['user_id'] as String?,
      userName: json['user_name'] as String?,
      timestamp:
          DateTime.tryParse(json['timestamp'] as String? ?? '') ??
          DateTime.now(),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'description': description,
      'user_id': userId,
      'user_name': userName,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  String get formattedTimestamp {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}

/// Real-time event types
enum RealtimeEventType {
  userRegistration('user_registration', 'User Registration'),
  userStatusChange('user_status_change', 'User Status Change'),
  claimSubmission('claim_submission', 'Claim Submission'),
  claimStatusUpdate('claim_status_update', 'Claim Status Update'),
  systemAlert('system_alert', 'System Alert'),
  securityEvent('security_event', 'Security Event'),
  adminAction('admin_action', 'Admin Action'),
  bulkOperation('bulk_operation', 'Bulk Operation'),
  notification('notification', 'Notification');

  const RealtimeEventType(this.value, this.displayName);

  final String value;
  final String displayName;

  static RealtimeEventType fromString(String value) {
    return RealtimeEventType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => RealtimeEventType.systemAlert,
    );
  }
}

/// Connection status for real-time updates
enum ConnectionStatus {
  disconnected('disconnected', 'Disconnected'),
  connecting('connecting', 'Connecting'),
  connected('connected', 'Connected'),
  error('error', 'Error');

  const ConnectionStatus(this.value, this.displayName);

  final String value;
  final String displayName;
}

/// Real-time event model
class RealtimeEvent {
  final String id;
  final RealtimeEventType type;
  final String collection;
  final String action; // create, update, delete
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final String? userId;
  final String? userName;
  final String description;
  final Map<String, dynamic> metadata;

  const RealtimeEvent({
    required this.id,
    required this.type,
    required this.collection,
    required this.action,
    required this.data,
    required this.timestamp,
    this.userId,
    this.userName,
    required this.description,
    required this.metadata,
  });

  factory RealtimeEvent.fromJson(Map<String, dynamic> json) {
    return RealtimeEvent(
      id: json['id'] as String? ?? '',
      type: RealtimeEventType.fromString(json['type'] as String? ?? ''),
      collection: json['collection'] as String? ?? '',
      action: json['action'] as String? ?? '',
      data: Map<String, dynamic>.from(json['data'] as Map? ?? {}),
      timestamp:
          DateTime.tryParse(json['timestamp'] as String? ?? '') ??
          DateTime.now(),
      userId: json['user_id'] as String?,
      userName: json['user_name'] as String?,
      description: json['description'] as String? ?? '',
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.value,
      'collection': collection,
      'action': action,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'user_id': userId,
      'user_name': userName,
      'description': description,
      'metadata': metadata,
    };
  }

  String get formattedTimestamp {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  RealtimeEvent copyWith({
    String? id,
    RealtimeEventType? type,
    String? collection,
    String? action,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    String? userId,
    String? userName,
    String? description,
    Map<String, dynamic>? metadata,
  }) {
    return RealtimeEvent(
      id: id ?? this.id,
      type: type ?? this.type,
      collection: collection ?? this.collection,
      action: action ?? this.action,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      description: description ?? this.description,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Real-time update state
class RealtimeUpdateState {
  final ConnectionStatus connectionStatus;
  final List<RealtimeEvent> recentActivities;
  final String? lastError;
  final DateTime? lastConnected;
  final int reconnectAttempts;

  const RealtimeUpdateState({
    this.connectionStatus = ConnectionStatus.disconnected,
    this.recentActivities = const [],
    this.lastError,
    this.lastConnected,
    this.reconnectAttempts = 0,
  });

  RealtimeUpdateState copyWith({
    ConnectionStatus? connectionStatus,
    List<RealtimeEvent>? recentActivities,
    String? lastError,
    DateTime? lastConnected,
    int? reconnectAttempts,
  }) {
    return RealtimeUpdateState(
      connectionStatus: connectionStatus ?? this.connectionStatus,
      recentActivities: recentActivities ?? this.recentActivities,
      lastError: lastError,
      lastConnected: lastConnected ?? this.lastConnected,
      reconnectAttempts: reconnectAttempts ?? this.reconnectAttempts,
    );
  }

  bool get isConnected => connectionStatus == ConnectionStatus.connected;
  bool get isConnecting => connectionStatus == ConnectionStatus.connecting;
  bool get hasError => connectionStatus == ConnectionStatus.error;
}

/// Platform statistics model
class PlatformStatistics {
  final int totalUsers;
  final int activeSolicitors;
  final int activeCoFunders;
  final int activeClaimants;
  final int totalClaims;
  final int activeClaims;
  final double totalFundingVolume;
  final int newRegistrationsToday;
  final int newRegistrationsWeek;
  final int newRegistrationsMonth;
  final SystemHealth systemHealth;
  final List<RecentActivity> recentActivities;
  final Map<String, int> claimsByStatus;
  final Map<String, int> usersByType;
  final Map<String, double> fundingByMonth;
  final DateTime lastUpdated;

  const PlatformStatistics({
    required this.totalUsers,
    required this.activeSolicitors,
    required this.activeCoFunders,
    required this.activeClaimants,
    required this.totalClaims,
    required this.activeClaims,
    required this.totalFundingVolume,
    required this.newRegistrationsToday,
    required this.newRegistrationsWeek,
    required this.newRegistrationsMonth,
    required this.systemHealth,
    required this.recentActivities,
    required this.claimsByStatus,
    required this.usersByType,
    required this.fundingByMonth,
    required this.lastUpdated,
  });

  factory PlatformStatistics.fromJson(Map<String, dynamic> json) {
    return PlatformStatistics(
      totalUsers: json['total_users'] as int? ?? 0,
      activeSolicitors: json['active_solicitors'] as int? ?? 0,
      activeCoFunders: json['active_co_funders'] as int? ?? 0,
      activeClaimants: json['active_claimants'] as int? ?? 0,
      totalClaims: json['total_claims'] as int? ?? 0,
      activeClaims: json['active_claims'] as int? ?? 0,
      totalFundingVolume:
          (json['total_funding_volume'] as num?)?.toDouble() ?? 0.0,
      newRegistrationsToday: json['new_registrations_today'] as int? ?? 0,
      newRegistrationsWeek: json['new_registrations_week'] as int? ?? 0,
      newRegistrationsMonth: json['new_registrations_month'] as int? ?? 0,
      systemHealth: SystemHealth.fromJson(
        json['system_health'] as Map<String, dynamic>? ?? {},
      ),
      recentActivities:
          (json['recent_activities'] as List?)
              ?.map(
                (activity) =>
                    RecentActivity.fromJson(activity as Map<String, dynamic>),
              )
              .toList() ??
          [],
      claimsByStatus: Map<String, int>.from(
        json['claims_by_status'] as Map? ?? {},
      ),
      usersByType: Map<String, int>.from(json['users_by_type'] as Map? ?? {}),
      fundingByMonth: Map<String, double>.from(
        json['funding_by_month'] as Map? ?? {},
      ),
      lastUpdated:
          DateTime.tryParse(json['last_updated'] as String? ?? '') ??
          DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_users': totalUsers,
      'active_solicitors': activeSolicitors,
      'active_co_funders': activeCoFunders,
      'active_claimants': activeClaimants,
      'total_claims': totalClaims,
      'active_claims': activeClaims,
      'total_funding_volume': totalFundingVolume,
      'new_registrations_today': newRegistrationsToday,
      'new_registrations_week': newRegistrationsWeek,
      'new_registrations_month': newRegistrationsMonth,
      'system_health': systemHealth.toJson(),
      'recent_activities':
          recentActivities.map((activity) => activity.toJson()).toList(),
      'claims_by_status': claimsByStatus,
      'users_by_type': usersByType,
      'funding_by_month': fundingByMonth,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  /// Get growth percentage for new registrations
  double get weeklyGrowthPercentage {
    if (newRegistrationsWeek == 0) return 0.0;
    // This would need historical data to calculate properly
    // For now, return a placeholder calculation
    return (newRegistrationsWeek / totalUsers * 100).clamp(0.0, 100.0);
  }

  /// Get trend indicator for user registrations
  TrendIndicator get registrationTrend {
    if (newRegistrationsWeek > newRegistrationsToday * 7) {
      return TrendIndicator.up;
    } else if (newRegistrationsWeek < newRegistrationsToday * 5) {
      return TrendIndicator.down;
    }
    return TrendIndicator.stable;
  }

  /// Get trend indicator for claims
  TrendIndicator get claimsTrend {
    // This would need historical data to calculate properly
    // For now, return stable
    return TrendIndicator.stable;
  }

  /// Get formatted funding volume
  String get formattedFundingVolume {
    if (totalFundingVolume >= 1000000) {
      return '£${(totalFundingVolume / 1000000).toStringAsFixed(1)}M';
    } else if (totalFundingVolume >= 1000) {
      return '£${(totalFundingVolume / 1000).toStringAsFixed(1)}K';
    } else {
      return '£${totalFundingVolume.toStringAsFixed(0)}';
    }
  }

  /// Check if data is stale (older than 5 minutes)
  bool get isStale {
    return DateTime.now().difference(lastUpdated).inMinutes > 5;
  }

  PlatformStatistics copyWith({
    int? totalUsers,
    int? activeSolicitors,
    int? activeCoFunders,
    int? activeClaimants,
    int? totalClaims,
    int? activeClaims,
    double? totalFundingVolume,
    int? newRegistrationsToday,
    int? newRegistrationsWeek,
    int? newRegistrationsMonth,
    SystemHealth? systemHealth,
    List<RecentActivity>? recentActivities,
    Map<String, int>? claimsByStatus,
    Map<String, int>? usersByType,
    Map<String, double>? fundingByMonth,
    DateTime? lastUpdated,
  }) {
    return PlatformStatistics(
      totalUsers: totalUsers ?? this.totalUsers,
      activeSolicitors: activeSolicitors ?? this.activeSolicitors,
      activeCoFunders: activeCoFunders ?? this.activeCoFunders,
      activeClaimants: activeClaimants ?? this.activeClaimants,
      totalClaims: totalClaims ?? this.totalClaims,
      activeClaims: activeClaims ?? this.activeClaims,
      totalFundingVolume: totalFundingVolume ?? this.totalFundingVolume,
      newRegistrationsToday:
          newRegistrationsToday ?? this.newRegistrationsToday,
      newRegistrationsWeek: newRegistrationsWeek ?? this.newRegistrationsWeek,
      newRegistrationsMonth:
          newRegistrationsMonth ?? this.newRegistrationsMonth,
      systemHealth: systemHealth ?? this.systemHealth,
      recentActivities: recentActivities ?? this.recentActivities,
      claimsByStatus: claimsByStatus ?? this.claimsByStatus,
      usersByType: usersByType ?? this.usersByType,
      fundingByMonth: fundingByMonth ?? this.fundingByMonth,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  String toString() {
    return 'PlatformStatistics(totalUsers: $totalUsers, totalClaims: $totalClaims, fundingVolume: $formattedFundingVolume)';
  }
}
