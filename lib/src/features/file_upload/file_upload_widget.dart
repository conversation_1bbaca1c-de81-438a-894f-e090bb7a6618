import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:three_pay_group_litigation_platform/src/utils/custom_file_picker.dart';

class FileUploadWidget extends StatefulWidget {
  final Function(List<PlatformFile>)? onFilesSelected;
  final String? title;
  final String? buttonText;
  final FileType fileType;
  final List<String>? allowedExtensions;
  final bool allowMultiple;
  final double? maxFileSizeMB;

  const FileUploadWidget({
    Key? key,
    this.onFilesSelected,
    this.title,
    this.buttonText = 'Select Files',
    this.fileType = FileType.any,
    this.allowedExtensions,
    this.allowMultiple = false,
    this.maxFileSizeMB,
  }) : super(key: key);

  @override
  State<FileUploadWidget> createState() => _FileUploadWidgetState();
}

class _FileUploadWidgetState extends State<FileUploadWidget> {
  List<PlatformFile> _selectedFiles = [];
  bool _isLoading = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              widget.title!,
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ),
        ElevatedButton.icon(
          onPressed: _isLoading ? null : () => _pickFiles(context),
          icon: const Icon(Icons.upload_file),
          label: Text(widget.buttonText!),
        ),
        if (_isLoading)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: Center(child: CircularProgressIndicator()),
          ),
        if (_errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              _errorMessage!,
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ),
        if (_selectedFiles.isNotEmpty) ...[
          const SizedBox(height: 16),
          Text(
            'Selected Files:',
            style: Theme.of(context).textTheme.titleSmall,
          ),
          const SizedBox(height: 8),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _selectedFiles.length,
            itemBuilder: (context, index) {
              final file = _selectedFiles[index];
              return ListTile(
                leading: _getFileIcon(file),
                title: Text(file.name),
                subtitle: Text(_getFileInfo(file)),
                trailing: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => _removeFile(index),
                ),
              );
            },
          ),
        ],
      ],
    );
  }

  Future<void> _pickFiles(BuildContext context) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Use our custom file picker to handle platform-specific issues
      final result = await CustomFilePicker().pickFiles(
        context: context,
        type: widget.fileType,
        allowedExtensions: widget.allowedExtensions,
        allowMultiple: widget.allowMultiple,
        withData: true,
      );

      if (result != null) {
        // Validate file sizes if maxFileSizeMB is specified
        if (widget.maxFileSizeMB != null) {
          final maxSizeBytes = widget.maxFileSizeMB! * 1024 * 1024;
          final oversizedFiles = result.files.where((file) => file.size > maxSizeBytes).toList();
          
          if (oversizedFiles.isNotEmpty) {
            setState(() {
              _errorMessage = 'Some files exceed the maximum size of ${widget.maxFileSizeMB} MB';
            });
            return;
          }
        }

        setState(() {
          _selectedFiles = result.files;
        });

        if (widget.onFilesSelected != null) {
          widget.onFilesSelected!(result.files);
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error picking files: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _removeFile(int index) {
    setState(() {
      _selectedFiles.removeAt(index);
    });

    if (widget.onFilesSelected != null) {
      widget.onFilesSelected!(_selectedFiles);
    }
  }

  Widget _getFileIcon(PlatformFile file) {
    IconData iconData;
    Color iconColor;

    if (file.extension == 'pdf') {
      iconData = Icons.picture_as_pdf;
      iconColor = Colors.red;
    } else if (['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(file.extension?.toLowerCase())) {
      iconData = Icons.image;
      iconColor = Colors.blue;
    } else if (['doc', 'docx'].contains(file.extension?.toLowerCase())) {
      iconData = Icons.description;
      iconColor = Colors.blue;
    } else if (['xls', 'xlsx'].contains(file.extension?.toLowerCase())) {
      iconData = Icons.table_chart;
      iconColor = Colors.green;
    } else if (['ppt', 'pptx'].contains(file.extension?.toLowerCase())) {
      iconData = Icons.slideshow;
      iconColor = Colors.orange;
    } else {
      iconData = Icons.insert_drive_file;
      iconColor = Colors.grey;
    }

    return Icon(iconData, color: iconColor);
  }

  String _getFileInfo(PlatformFile file) {
    final sizeInKB = file.size / 1024;
    final sizeInMB = sizeInKB / 1024;
    
    String sizeText;
    if (sizeInMB >= 1) {
      sizeText = '${sizeInMB.toStringAsFixed(2)} MB';
    } else {
      sizeText = '${sizeInKB.toStringAsFixed(2)} KB';
    }
    
    return '${file.extension?.toUpperCase() ?? 'Unknown'} • $sizeText';
  }
}
