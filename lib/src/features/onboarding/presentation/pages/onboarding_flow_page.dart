import 'package:flutter/material.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/role_selection_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/onboarding/data/models/onboarding_page_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/onboarding/presentation/widgets/onboarding_screen_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/onboarding/presentation/widgets/page_indicator.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

// Keep static screens as fallbacks
import 'package:three_pay_group_litigation_platform/src/features/onboarding/presentation/pages/onboarding_screen_1.dart';
import 'package:three_pay_group_litigation_platform/src/features/onboarding/presentation/pages/onboarding_screen_2.dart';
import 'package:three_pay_group_litigation_platform/src/features/onboarding/presentation/pages/onboarding_screen_3.dart';

class OnboardingFlowPage extends StatefulWidget {
  static const String routeName = '/onboarding';
  const OnboardingFlowPage({super.key});

  @override
  State<OnboardingFlowPage> createState() => _OnboardingFlowPageState();
}

class _OnboardingFlowPageState extends State<OnboardingFlowPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  List<OnboardingPageModel> _dynamicOnboardingPages = []; // Holds fetched data
  bool _isLoading = true;
  String? _error;
  bool _useFallback = false; // Flag to indicate if using fallback

  // Static fallback pages list
  final List<Widget> _staticFallbackPages = [
    const OnboardingScreen1(),
    const OnboardingScreen2(),
    const OnboardingScreen3(),
  ];

  // The list of widgets currently being displayed (dynamic or fallback)
  List<Widget> get _currentPages => _useFallback || _dynamicOnboardingPages.isEmpty
      ? _staticFallbackPages
      : _dynamicOnboardingPages.map((pageData) => OnboardingScreenWidget(pageData: pageData)).toList();

  @override
  void initState() {
    super.initState();
    _fetchOnboardingPages();
    _pageController.addListener(() {
      if (!mounted) return;
      final newPage = _pageController.page?.round();
      if (newPage != null && newPage != _currentPage) {
        setState(() {
          _currentPage = newPage;
        });
      }
    });
  }

  Future<void> _fetchOnboardingPages() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _error = null;
      _useFallback = false;
    });
    try {
      final pb = PocketBaseService().client;
      final result = await pb.collection('onboarding_pages').getList(
            sort: '+page_order', // Ensure pages are in correct order
          );
      final items = result.items.map((record) => OnboardingPageModel.fromRecord(record)).toList();

      if (!mounted) return;

      if (items.isNotEmpty) {
        setState(() {
          _dynamicOnboardingPages = items;
          _isLoading = false;
        });
      } else {
        // If no pages fetched from PB, use static fallbacks
        setState(() {
          _useFallback = true;
          _isLoading = false;
          _error = "Using default onboarding screens."; // Optional: Inform user
        });
      }
    } catch (e) {
      print("Failed to fetch onboarding pages: $e"); // Log error
      if (!mounted) return;
      // Use static fallbacks on error
      setState(() {
        _useFallback = true;
        _isLoading = false;
        _error = "Couldn't load onboarding. Using defaults.";
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
     if (!mounted) return;
    setState(() {
      _currentPage = page;
    });
  }

  void _nextPage() {
    if (_currentPage < _currentPages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
      );
    } else {
      // Navigate to the next part of the app
      Navigator.of(context).pushReplacementNamed(RoleSelectionPage.routeName);
    }
  }

  void _skipOnboarding() {
    // Navigate directly to the next part of the app
    Navigator.of(context).pushReplacementNamed(RoleSelectionPage.routeName);
  }

  @override
  Widget build(BuildContext context) {
    final shadTheme = ShadTheme.of(context);
    final pagesToDisplay = _currentPages; // Get the list based on state

    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Stack(
              children: [
                PageView.builder(
                  controller: _pageController,
                  onPageChanged: _onPageChanged,
                  itemCount: pagesToDisplay.length,
                  itemBuilder: (context, index) {
                    // Apply animation logic if needed (similar to original file)
                    return AnimatedBuilder(
                      animation: _pageController,
                      builder: (context, child) {
                        double value = 1.0;
                        if (_pageController.position.haveDimensions) {
                          double pageOffset = (_pageController.page ?? 0.0) - index;
                          value = (1 - (pageOffset.abs() * 0.25)).clamp(0.75, 1.0);
                        } else if (index != _currentPage) {
                          value = 0.75;
                        }
                        return Transform.scale(
                          scale: value,
                          child: pagesToDisplay[index],
                        );
                      },
                    );
                  },
                ),
                // Positioned controls at the bottom
                Positioned(
                  bottom: 30,
                  left: 20,
                  right: 20,
                  child: Column(
                    children: [
                      if (_error != null) // Display error/fallback message
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: Text(
                            _error!,
                            style: shadTheme.textTheme.small.copyWith(color: shadTheme.colorScheme.destructive),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Skip Button - Larger size
                          ShadButton.ghost(
                            size: ShadButtonSize.lg, // Make button larger
                            onPressed: _skipOnboarding,
                            child: Text(
                              'Skip',
                              style: shadTheme.textTheme.p.copyWith(
                                color: shadTheme.colorScheme.mutedForeground,
                                fontWeight: FontWeight.bold // Make text bolder
                              ),
                            ),
                          ),
                          // Page Indicator Row
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center, // Center the dots
                            children: List.generate(
                              pagesToDisplay.length, // Use dynamic length
                              (index) => PageIndicator(
                                isActive: index == _currentPage, // Check if current dot is active
                                activeColor: shadTheme.colorScheme.primary,
                                inactiveColor: shadTheme.colorScheme.border,
                                // Optional: Adjust size/spacing if needed
                                // size: 10,
                                // spacing: 10,
                              ),
                            ),
                          ),
                          // Next/Done Button - Larger size and bolder text
                          ShadButton(
                            size: ShadButtonSize.lg, // Make button larger
                            onPressed: _nextPage,
                            child: Text(
                              _currentPage == pagesToDisplay.length - 1 ? 'Done' : 'Next',
                              style: shadTheme.textTheme.p.copyWith(fontWeight: FontWeight.bold) // Make text bolder
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }
}