import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/onboarding/data/models/onboarding_page_model.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart'; // To get PB client for image URL

class OnboardingScreenWidget extends StatefulWidget {
  final OnboardingPageModel pageData;

  const OnboardingScreenWidget({super.key, required this.pageData});

  @override
  State<OnboardingScreenWidget> createState() => _OnboardingScreenWidgetState();
}

class _OnboardingScreenWidgetState extends State<OnboardingScreenWidget> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _slideAnimation = Tween<Offset>(begin: const Offset(0, 0.2), end: Offset.zero).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    // Start animations when the screen is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final shadTheme = ShadTheme.of(context);
    final imageUrl = widget.pageData.getImageUrl(PocketBaseService().client);
    final bool hasImage = imageUrl != null;

    return Container(
      padding: const EdgeInsets.all(24.0),
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SizedBox( // Image container - Further increased size
                height: 250,
                width: 250,
                child: ClipRRect(
                  borderRadius: shadTheme.radius,
                  child: hasImage
                      ? Image.network(
                          imageUrl!,
                          fit: BoxFit.contain, // Or BoxFit.cover depending on design
                          loadingBuilder: (context, child, progress) =>
                              progress == null ? child : const Center(child: CircularProgressIndicator()),
                          errorBuilder: (context, error, stackTrace) =>
                              Container(color: shadTheme.colorScheme.muted, alignment: Alignment.center, child: Icon(LucideIcons.imageOff, size: 100, color: shadTheme.colorScheme.mutedForeground)), // Further increased icon size
                    )
                  : Container( // Placeholder if no image
                      color: shadTheme.colorScheme.muted,
                      alignment: Alignment.center,
                      child: Icon(LucideIcons.image, size: 100, color: shadTheme.colorScheme.mutedForeground), // Further increased icon size
                    ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 40),
          FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Text(
                widget.pageData.title,
                textAlign: TextAlign.center,
                style: shadTheme.textTheme.h1.copyWith( // Using h1 for bigger title
                  fontWeight: FontWeight.bold,
                  color: shadTheme.colorScheme.foreground,
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
          FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Text(
                widget.pageData.body,
                textAlign: TextAlign.center,
                style: shadTheme.textTheme.p.copyWith( // Using Shadcn styles
                  color: shadTheme.colorScheme.mutedForeground,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}