import 'package:flutter/material.dart';

class PageIndicator extends StatelessWidget {
  final bool isActive;
  final Color activeColor;
  final Color inactiveColor;
  final double size;
  final double spacing;

  const PageIndicator({
    super.key,
    required this.isActive,
    required this.activeColor,
    required this.inactiveColor,
    this.size = 8.0,
    this.spacing = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 150),
      margin: EdgeInsets.symmetric(horizontal: spacing / 2),
      height: size,
      width: isActive ? size * 2.5 : size, // Active indicator is wider
      decoration: BoxDecoration(
        color: isActive ? activeColor : inactiveColor,
        borderRadius: BorderRadius.circular(size / 2),
        boxShadow: isActive
            ? [
                BoxShadow(
                  color: activeColor.withOpacity(0.5),
                  blurRadius: 4.0,
                  spreadRadius: 1.0,
                  offset: const Offset(0, 1),
                ),
              ]
            : null,
      ),
    );
  }
}