import 'package:pocketbase/pocketbase.dart';

class OnboardingPageModel {
  final String id;
  final String collectionId;
  final String collectionName;
  final DateTime created;
  final DateTime updated;
  final int pageOrder;
  final String title;
  final String body;
  final String? image; // Filename from PocketBase

  OnboardingPageModel({
    required this.id,
    required this.collectionId,
    required this.collectionName,
    required this.created,
    required this.updated,
    required this.pageOrder,
    required this.title,
    required this.body,
    this.image,
  });

  factory OnboardingPageModel.fromJson(Map<String, dynamic> json) {
    return OnboardingPageModel(
      id: json['id'] as String,
      collectionId: json['collectionId'] as String,
      collectionName: json['collectionName'] as String,
      created: DateTime.parse(json['created'] as String),
      updated: DateTime.parse(json['updated'] as String),
      pageOrder: (json['page_order'] as num?)?.toInt() ?? 0, // Handle potential null/double
      title: json['title'] as String,
      body: json['body'] as String,
      image: json['image'] as String?,
    );
  }

  factory OnboardingPageModel.fromRecord(RecordModel record) {
    return OnboardingPageModel.fromJson(record.toJson());
  }

  // Helper to get the full image URL
  String? getImageUrl(PocketBase pb) {
    if (image == null || image!.isEmpty) {
      return null;
    }
    return Uri.parse('${pb.baseUrl}/api/files/$collectionId/$id/$image').toString();
  }
}