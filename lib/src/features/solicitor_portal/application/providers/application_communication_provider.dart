import 'dart:math';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/application_message_model.dart';
import 'package:pocketbase/pocketbase.dart';

// State for communication
class ApplicationCommunicationState {
  final List<ApplicationMessageModel> messages;
  final bool isLoading;
  final String? error;
  final bool isSending;
  final DateTime? lastCheckedAgentMessageTimestamp; // For potential UI use

  ApplicationCommunicationState({
    this.messages = const [],
    this.isLoading = false,
    this.error,
    this.isSending = false,
    this.lastCheckedAgentMessageTimestamp,
  });

  ApplicationCommunicationState copyWith({
    List<ApplicationMessageModel>? messages,
    bool? isLoading,
    String? error,
    bool? clearError,
    bool? isSending,
    DateTime? lastCheckedAgentMessageTimestamp,
  }) {
    return ApplicationCommunicationState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      error: clearError == true ? null : error ?? this.error,
      isSending: isSending ?? this.isSending,
      lastCheckedAgentMessageTimestamp:
          lastCheckedAgentMessageTimestamp ??
          this.lastCheckedAgentMessageTimestamp,
    );
  }
}

// StateNotifier
class ApplicationCommunicationNotifier
    extends StateNotifier<ApplicationCommunicationState> {
  final PocketBaseService _pbService;
  final String _applicationId; // Changed parameter name
  // In-memory store for the timestamp of the last agent message that triggered a notification for this applicationId
  static final Map<String, DateTime> _lastProcessedAgentMessageTimestamp = {};

  ApplicationCommunicationNotifier(
    this._pbService,
    this._applicationId,
  ) // Changed constructor name and parameter
  : super(ApplicationCommunicationState()) {
    fetchMessages();
  }

  Future<void> fetchMessages() async {
    state = state.copyWith(isLoading: true, clearError: true);
    try {
      final records = await _pbService.client
          .collection('application_communications')
          .getFullList(
            // Changed collection name
            filter:
                'application_id = "$_applicationId"', // Changed filter field and variable
            sort: 'created',
            expand: 'sender_id',
          );
      final messages =
          records
              .map((record) => ApplicationMessageModel.fromRecord(record))
              .toList();

      // New message detection logic (adapted for application context)
      final currentSolicitorId = _pbService.client.authStore.record?.id;
      if (messages.isNotEmpty && currentSolicitorId != null) {
        final latestMessage = messages.first;
        // Assuming 'admin' user_type for 3Pay Global Agents
        if (latestMessage.senderId != currentSolicitorId &&
            latestMessage.senderUserType == 'admin') {
          final lastProcessedTime =
              _lastProcessedAgentMessageTimestamp[_applicationId];
          if (latestMessage.created.isAfter(
            lastProcessedTime ?? DateTime.fromMillisecondsSinceEpoch(0),
          )) {
            // This is a new, unprocessed message from an agent
            try {
              final notificationBody = {
                'recipientId': [
                  currentSolicitorId,
                ], // Use array format for recipientId
                'type':
                    'new_message_application', // Differentiate notification type
                'title':
                    'New Message: Application $_applicationId', // Consider fetching application title/name
                'message':
                    'New message from ${latestMessage.senderName ?? '3Pay Agent'}: ${latestMessage.messageContent.substring(0, min(50, latestMessage.messageContent.length))}${latestMessage.messageContent.length > 50 ? "..." : ""}',
                'related_item_id': _applicationId,
                'isRead': false, // Use isRead instead of is_read
              };
              await _pbService.client
                  .collection('notifications')
                  .create(body: notificationBody);
              _lastProcessedAgentMessageTimestamp[_applicationId] =
                  latestMessage.created;
              // TODO: Trigger UI update to show toast.
              LoggerService.info(
                'New agent message notification created for application $_applicationId',
              );
            } catch (e) {
              // Log or handle notification creation error
              LoggerService.error(
                'Error creating notification record for application',
                e,
              );
            }
          }
        }
      }
      state = state.copyWith(
        messages: messages,
        isLoading: false,
        lastCheckedAgentMessageTimestamp: DateTime.now(),
      );
    } on ClientException catch (e) {
      state = state.copyWith(
        error:
            'Failed to fetch messages: ${e.response['message'] ?? e.toString()}',
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: 'An unexpected error occurred: ${e.toString()}',
        isLoading: false,
      );
    }
  }

  Future<bool> sendMessage({
    required String messageContent,
    required String senderId,
    String? recipientId,
    String? recipientGroup,
  }) async {
    if (messageContent.trim().isEmpty) {
      state = state.copyWith(error: "Message cannot be empty");
      return false;
    }
    state = state.copyWith(isSending: true, clearError: true);
    try {
      final body = <String, dynamic>{
        'application_id': _applicationId, // Changed field name and variable
        'sender_id': senderId,
        'message_content': messageContent,
      };
      if (recipientId != null) {
        body['recipient_id'] = recipientId;
      } else if (recipientGroup != null) {
        body['recipient_group'] = recipientGroup;
      } else {
        if (recipientId == null && recipientGroup == null) {
          state = state.copyWith(
            error: "Recipient not specified.",
            isSending: false,
          );
          return false;
        }
      }

      await _pbService.client
          .collection('application_communications')
          .create(body: body); // Changed collection name
      state = state.copyWith(isSending: false);
      await fetchMessages(); // Refresh messages after sending
      return true;
    } on ClientException catch (e) {
      state = state.copyWith(
        error:
            'Failed to send message: ${e.response['message'] ?? e.toString()}',
        isSending: false,
      );
      return false;
    } catch (e) {
      state = state.copyWith(
        error: 'An unexpected error occurred while sending: ${e.toString()}',
        isSending: false,
      );
      return false;
    }
  }
}

// Provider
final applicationCommunicationProvider = StateNotifierProvider.family<
  ApplicationCommunicationNotifier,
  ApplicationCommunicationState,
  String
>((ref, applicationId) {
  // Changed parameter name
  final pbService = ref.watch(
    pocketBaseClientProvider,
  ); // This line might need adjustment if pocketBaseClientProvider is not global or if PocketBaseService instantiation is preferred.
  // Assuming PocketBaseService() is the correct way to get an instance as per the original file.
  return ApplicationCommunicationNotifier(
    PocketBaseService(),
    applicationId,
  ); // Changed notifier class name and parameter
});
