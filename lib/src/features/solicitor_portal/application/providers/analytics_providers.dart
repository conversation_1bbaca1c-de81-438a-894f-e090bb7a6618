import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart'; // Assuming this service exists

// Data Model for Application Analytics
class ApplicationAnalyticsData {
  final int totalSubmitted;
  final Map<String, int>
  statusBreakdown; // e.g., {'submitted': 10, 'approved': 5, ...}
  final double successRate; // (approved / (approved + rejected))
  final Duration? averageTimeToDecision; // Optional

  ApplicationAnalyticsData({
    required this.totalSubmitted,
    required this.statusBreakdown,
    required this.successRate,
    this.averageTimeToDecision,
  });

  // Consider adding a factory constructor for error/loading states or an empty state
  factory ApplicationAnalyticsData.empty() {
    return ApplicationAnalyticsData(
      totalSubmitted: 0,
      statusBreakdown: {},
      successRate: 0.0,
      averageTimeToDecision: null,
    );
  }
}

// Riverpod Provider for Application Analytics
// We'll use a StateNotifierProvider as the data fetching is asynchronous and involves state management (loading, error, data)
final applicationAnalyticsProvider = StateNotifierProvider.autoDispose.family<
  ApplicationAnalyticsNotifier,
  AsyncValue<ApplicationAnalyticsData>,
  ApplicationAnalyticsFilter
>((ref, filter) {
  final pbClient = ref.watch(pocketBaseClientProvider); // Corrected provider
  return ApplicationAnalyticsNotifier(
    pbClient,
    filter,
  ); // Pass the PocketBase client directly
});

// Filter class for date range
class ApplicationAnalyticsFilter {
  final DateTime? startDate;
  final DateTime? endDate;
  // Potentially add solicitorFirmId if not globally available or if admins can view other firms
  // final String? solicitorFirmId;

  ApplicationAnalyticsFilter({this.startDate, this.endDate});

  // For equitable comparison if used in provider family
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ApplicationAnalyticsFilter &&
        other.startDate == startDate &&
        other.endDate == endDate;
  }

  @override
  int get hashCode => startDate.hashCode ^ endDate.hashCode;
}

class ApplicationAnalyticsNotifier
    extends StateNotifier<AsyncValue<ApplicationAnalyticsData>> {
  final PocketBase _pb;
  final ApplicationAnalyticsFilter _filter;
  // Assuming 'funding_applications' is the collection name
  final String _collectionName = 'funding_applications';

  ApplicationAnalyticsNotifier(this._pb, this._filter)
    : super(const AsyncValue.loading()) {
    _fetchApplicationAnalytics();
  }

  Future<void> _fetchApplicationAnalytics() async {
    state = const AsyncValue.loading();
    try {
      // Get current user
      final currentUser = _pb.authStore.record;
      if (currentUser == null) {
        state = AsyncValue.error('User not authenticated.', StackTrace.current);
        return;
      }

      // Get solicitor profile for current user
      final solicitorProfileRecords = await _pb
          .collection('solicitor_profiles')
          .getList(filter: 'user_id = "${currentUser.id}"', perPage: 1);

      if (solicitorProfileRecords.items.isEmpty) {
        state = AsyncValue.error(
          'Solicitor profile not found. Please ensure your profile is set up correctly.',
          StackTrace.current,
        );
        return;
      }

      final solicitorProfileId = solicitorProfileRecords.items.first.id;

      // Filter by solicitor profile ID instead of firm ID
      List<String> filterParts = [
        "solicitor_profile_id ~ '$solicitorProfileId'",
      ]; // Use ~ for relation field matching

      if (_filter.startDate != null) {
        // Ensure date is in PocketBase format: 'YYYY-MM-DD HH:MM:SS.mmmZ' or similar
        filterParts.add(
          "submission_date >= '${_filter.startDate!.toUtc().toIso8601String()}'",
        );
      }
      if (_filter.endDate != null) {
        // Add 1 day to endDate to make it inclusive of the whole day, then format
        final inclusiveEndDate = _filter.endDate!.add(const Duration(days: 1));
        filterParts.add(
          "submission_date < '${inclusiveEndDate.toUtc().toIso8601String()}'",
        );
      }

      final filterString = filterParts.join(' && ');

      // 2. Fetch all relevant records (consider pagination for very large datasets, though aggregation might be better done by a view or backend script if possible)
      // For simplicity, fetching all matching records here. PocketBase default limit is 30, max is 500 per request.
      // If more than 500, will need to implement paginated fetching.
      final List<RecordModel> records = await _pb
          .collection(_collectionName)
          .getFullList(
            filter: filterString,
            // batch: 500, // if using getFullList with batching
          );

      // 3. Calculate KPIs
      final int totalSubmitted = records.length;

      final Map<String, int> statusBreakdown = {};
      int approvedCount = 0;
      int rejectedCount = 0;
      List<Duration> decisionDurations = [];

      for (var record in records) {
        final status = record.getStringValue(
          'application_status',
        ); // Adjust field name if different
        statusBreakdown[status] = (statusBreakdown[status] ?? 0) + 1;

        if (status == 'approved') {
          // Assuming 'approved' is the status string
          approvedCount++;
        } else if (status == 'rejected') {
          // Assuming 'rejected' is the status string
          rejectedCount++;
        }

        // Calculate time to decision
        if ((status == 'approved' || status == 'rejected')) {
          final submissionDateString = record.getStringValue(
            'submission_date',
          ); // Adjust field name
          final decisionDateString = record.getStringValue(
            'decision_date',
          ); // Adjust field name

          if (submissionDateString.isNotEmpty &&
              decisionDateString.isNotEmpty) {
            try {
              final submissionDate = DateTime.parse(submissionDateString);
              final decisionDate = DateTime.parse(decisionDateString);
              if (decisionDate.isAfter(submissionDate)) {
                decisionDurations.add(decisionDate.difference(submissionDate));
              }
            } catch (e) {
              // Log parsing error or handle as needed
              LoggerService.error(
                'Error parsing dates for record ${record.id}',
                e,
              );
            }
          }
        }
      }

      final double successRate =
          (approvedCount + rejectedCount) == 0
              ? 0.0
              : approvedCount / (approvedCount + rejectedCount);

      Duration? averageTimeToDecision;
      if (decisionDurations.isNotEmpty) {
        final totalDecisionTimeMicroseconds = decisionDurations.fold<int>(
          0,
          (sum, duration) => sum + duration.inMicroseconds,
        );
        averageTimeToDecision = Duration(
          microseconds:
              totalDecisionTimeMicroseconds ~/ decisionDurations.length,
        );
      }

      // 4. Create ApplicationAnalyticsData instance
      final analyticsData = ApplicationAnalyticsData(
        totalSubmitted: totalSubmitted,
        statusBreakdown: statusBreakdown,
        successRate: successRate,
        averageTimeToDecision: averageTimeToDecision,
      );

      state = AsyncValue.data(analyticsData);
    } catch (e, s) {
      // Log the error and stack trace
      LoggerService.error('Error fetching application analytics', e);
      state = AsyncValue.error(e, s);
    }
  }

  // Optional: Method to refresh data
  Future<void> refresh() async {
    await _fetchApplicationAnalytics();
  }
}

// --- Claim Analytics ---

// Data Model for Claim Analytics
class ClaimAnalyticsData {
  final int totalActiveClaims;
  final Map<String, int>
  statusBreakdown; // e.g., {'pre_action': 5, 'discovery': 3, ...}
  final double totalFundingSecured;
  final Duration? averageClaimDuration; // Optional

  ClaimAnalyticsData({
    required this.totalActiveClaims,
    required this.statusBreakdown,
    required this.totalFundingSecured,
    this.averageClaimDuration,
  });

  factory ClaimAnalyticsData.empty() {
    return ClaimAnalyticsData(
      totalActiveClaims: 0,
      statusBreakdown: {},
      totalFundingSecured: 0.0,
      averageClaimDuration: null,
    );
  }
}

// Filter class for Claim Analytics, including date range and solicitor ID
class ClaimAnalyticsFilter {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? solicitorId; // For filtering by individual solicitor
  // Potentially add solicitorFirmId if not globally available or if admins can view other firms
  // final String? solicitorFirmId;

  ClaimAnalyticsFilter({this.startDate, this.endDate, this.solicitorId});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ClaimAnalyticsFilter &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.solicitorId == solicitorId;
  }

  @override
  int get hashCode =>
      startDate.hashCode ^ endDate.hashCode ^ solicitorId.hashCode;
}

// Riverpod Provider for Claim Analytics
final claimAnalyticsProvider = StateNotifierProvider.autoDispose.family<
  ClaimAnalyticsNotifier,
  AsyncValue<ClaimAnalyticsData>,
  ClaimAnalyticsFilter
>((ref, filter) {
  final pbClient = ref.watch(pocketBaseClientProvider);
  return ClaimAnalyticsNotifier(pbClient, filter);
});

class ClaimAnalyticsNotifier
    extends StateNotifier<AsyncValue<ClaimAnalyticsData>> {
  final PocketBase _pb;
  final ClaimAnalyticsFilter _filter;
  final String _collectionName = 'funding_applications'; // Collection name for claims

  ClaimAnalyticsNotifier(this._pb, this._filter)
    : super(const AsyncValue.loading()) {
    _fetchClaimAnalytics();
  }

  Future<void> _fetchClaimAnalytics() async {
    state = const AsyncValue.loading();
    try {
      // Get current user
      final currentUser = _pb.authStore.record;
      if (currentUser == null) {
        state = AsyncValue.error('User not authenticated.', StackTrace.current);
        return;
      }

      // Get solicitor profile for current user
      final solicitorProfileRecords = await _pb
          .collection('solicitor_profiles')
          .getList(filter: 'user_id = "${currentUser.id}"', perPage: 1);

      if (solicitorProfileRecords.items.isEmpty) {
        state = AsyncValue.error(
          'Solicitor profile not found. Please ensure your profile is set up correctly.',
          StackTrace.current,
        );
        return;
      }

      final solicitorProfileId = solicitorProfileRecords.items.first.id;

      // Filter by solicitor profile ID - claims collection uses claim_admins and associated_solicitors
      // We need to check if the solicitor is either a claim admin or associated solicitor
      List<String> filterParts = [
        "(claim_admins ~ '$solicitorProfileId' || associated_solicitors ~ '$solicitorProfileId')",
      ]; // Use ~ for relation field matching

      // Date range filtering - applied to 'created' field since claims collection doesn't have start_date
      if (_filter.startDate != null) {
        filterParts.add(
          "created >= '${_filter.startDate!.toUtc().toIso8601String()}'",
        );
      }
      if (_filter.endDate != null) {
        final inclusiveEndDate = _filter.endDate!.add(const Duration(days: 1));
        filterParts.add(
          "created < '${inclusiveEndDate.toUtc().toIso8601String()}'",
        );
      }

      // Filter by individual solicitor if solicitorId is provided
      if (_filter.solicitorId != null && _filter.solicitorId!.isNotEmpty) {
        filterParts.add(
          "(claim_admins ~ '${_filter.solicitorId}' || associated_solicitors ~ '${_filter.solicitorId}')",
        ); // Check both claim_admins and associated_solicitors
      }

      final filterString = filterParts.join(' && ');

      final List<RecordModel> records = await _pb
          .collection(_collectionName)
          .getFullList(
            filter: filterString,
            // Consider expanding fields if not all are returned by default, e.g. expand: 'lead_solicitor_id'
          );

      int totalActiveClaims = 0;
      final Map<String, int> statusBreakdown = {};
      double totalFundingSecured = 0.0;
      List<Duration> claimDurations = [];

      // Define what constitutes a "closed" or "settled" status.
      // These should align with the actual status values in your 'claims.current_status' field.
      const Set<String> nonActiveStatuses = {
        'closed',
        'settled',
      }; // Example statuses

      for (var record in records) {
        final status = record.getStringValue(
          'current_status',
        ); // Adjust field name if different
        final isClaimActive = !nonActiveStatuses.contains(status.toLowerCase());

        if (isClaimActive) {
          totalActiveClaims++;
          statusBreakdown[status] = (statusBreakdown[status] ?? 0) + 1;
        }

        // Total funding secured: sum for ALL relevant claims (active or not, based on filter)
        // As per SOLICITOR_FEATKILO.md: "Total Funding Secured across all active/past claims."
        // This means we sum it for all records fetched by the filter, not just active ones.
        totalFundingSecured += record.getDoubleValue(
          'total_funding_secured',
        ); // Adjust field name

        // Calculate average claim duration for closed/settled claims
        if (!isClaimActive) {
          final startDateString = record.getStringValue(
            'created',
          ); // Use created field as start date
          // Assuming a 'resolution_date' field exists for when a claim was closed/settled.
          // If not, this logic needs adjustment or the field needs to be added to the 'claims' collection.
          final resolutionDateString = record.getStringValue(
            'updated',
          ); // Use updated as resolution date for now

          if (startDateString.isNotEmpty && resolutionDateString.isNotEmpty) {
            try {
              final startDate = DateTime.parse(startDateString);
              final resolutionDate = DateTime.parse(resolutionDateString);
              if (resolutionDate.isAfter(startDate)) {
                claimDurations.add(resolutionDate.difference(startDate));
              }
            } catch (e) {
              LoggerService.error(
                'Error parsing dates for claim duration calculation (record ${record.id})',
                e,
              );
            }
          }
        }
      }

      Duration? averageClaimDuration;
      if (claimDurations.isNotEmpty) {
        final totalDurationMicroseconds = claimDurations.fold<int>(
          0,
          (sum, duration) => sum + duration.inMicroseconds,
        );
        averageClaimDuration = Duration(
          microseconds: totalDurationMicroseconds ~/ claimDurations.length,
        );
      }

      final analyticsData = ClaimAnalyticsData(
        totalActiveClaims: totalActiveClaims,
        statusBreakdown: statusBreakdown,
        totalFundingSecured: totalFundingSecured,
        averageClaimDuration: averageClaimDuration,
      );

      state = AsyncValue.data(analyticsData);
    } catch (e, s) {
      LoggerService.error('Error fetching claim analytics', e);
      state = AsyncValue.error(e, s);
    }
  }

  Future<void> refresh() async {
    await _fetchClaimAnalytics();
  }
}
