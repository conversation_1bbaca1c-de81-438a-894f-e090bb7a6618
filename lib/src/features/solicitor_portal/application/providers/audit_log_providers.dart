import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pocketbase/pocketbase.dart';
// Ensure this path is correct and pocketBaseProvider is exported from here.
// If pocketbase_service.dart is in a different location or the provider has a different name, adjust accordingly.
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

// Define the AuditLogEntry model
class AuditLogEntry {
  final String id;
  final DateTime timestamp;
  final String userName;
  final String userRole;
  final String action;
  final dynamic details; // Can be String or Map<String, dynamic>
  final String entityType;
  final String entityId;

  AuditLogEntry({
    required this.id,
    required this.timestamp,
    required this.userName,
    required this.userRole,
    required this.action,
    this.details,
    required this.entityType,
    required this.entityId,
  });

  factory AuditLogEntry.fromRecordModel(RecordModel record) {
    final expandedUser = record.get<RecordModel?>('expand.user_id');
    String userName = 'Unknown User';
    String userRole = 'User';

    if (expandedUser != null) {
      userName = expandedUser.data['name'] as String? ?? 'N/A';
      // Assuming 'user_type' field exists in the users collection
      // and might store roles like 'solicitor', 'agent', 'system'
      userRole = expandedUser.data['user_type'] as String? ?? 'User';
    }

    return AuditLogEntry(
      id: record.id,
      timestamp: DateTime.parse(record.get<String>('created')),
      userName: userName,
      userRole: userRole,
      action:
          record.data['action_type'] as String? ??
          record.data['action'] as String? ??
          'No action specified',
      details: record.data['details'],
      entityType: record.data['entity_type'] as String? ?? 'unknown',
      entityId: record.data['entity_id'] as String? ?? 'unknown',
    );
  }

  @override
  String toString() {
    return 'AuditLogEntry(id: $id, timestamp: $timestamp, userName: $userName, userRole: $userRole, action: $action, details: $details, entityType: $entityType, entityId: $entityId)';
  }
}

// Provider to fetch audit logs for a specific application
final applicationAuditLogProvider = FutureProvider.family<
  List<AuditLogEntry>,
  String
>((ref, applicationId) async {
  final pocketBase = ref.watch(
    pocketBaseClientProvider,
  ); // Corrected provider name

  try {
    final records = await pocketBase
        .collection('user_activity_logs')
        .getList(
          filter: "entity_id = '$applicationId' && entity_type = 'application'",
          sort: '-created',
          expand:
              'user_id', // Assuming 'user_id' is the field linking to the users collection
        );

    return records.items
        .map((record) => AuditLogEntry.fromRecordModel(record))
        .toList();
  } catch (e) {
    // Log the error or handle it as per your app's error handling strategy
    LoggerService.error('Error fetching application audit logs', e);
    // Consider rethrowing a more specific error or returning an empty list
    // For now, rethrow to let the UI handle the error state
    rethrow;
  }
});

// Provider to fetch audit logs for a specific claim
final claimAuditLogProvider = FutureProvider.family<
  List<AuditLogEntry>,
  String
>((ref, claimId) async {
  final pocketBase = ref.watch(
    pocketBaseClientProvider,
  ); // Corrected provider name

  try {
    final records = await pocketBase
        .collection('user_activity_logs')
        .getList(
          filter:
              "entity_id = '$claimId' && entity_type = 'application'", // Adjusted filter for funding applications
          sort: '-created',
          expand:
              'user_id', // Assuming 'user_id' is the field linking to the users collection
        );

    return records.items
        .map((record) => AuditLogEntry.fromRecordModel(record))
        .toList();
  } catch (e) {
    // Log the error or handle it as per your app's error handling strategy
    LoggerService.error('Error fetching claim audit logs', e);
    // Consider rethrowing a more specific error or returning an empty list
    // For now, rethrow to let the UI handle the error state
    rethrow;
  }
});

// You might also want a StreamProvider if you need real-time updates,
// but for an audit log, FutureProvider is often sufficient.
// Example of a StreamProvider (if needed later):
/*
final applicationAuditLogStreamProvider = StreamProvider.family<List<AuditLogEntry>, String>((ref, applicationId) {
  final pocketBase = ref.watch(pocketBaseProvider);
  final controller = StreamController<List<AuditLogEntry>>();

  Future<void> fetchAndEmit() async {
    try {
      final records = await pocketBase.collection('user_activity_logs').getList(
        filter: "entity_id = '$applicationId' && entity_type = 'application'",
        sort: '-created',
        expand: 'user_id',
      );
      controller.add(records.items.map((record) => AuditLogEntry.fromRecordModel(record)).toList());
    } catch (e) {
      controller.addError(e);
    }
  }

  fetchAndEmit(); // Initial fetch

  // Optional: Set up a subscription for real-time updates if your backend supports it
  // and if it's a requirement for audit logs (usually not critical for real-time).
  // For PocketBase, you might subscribe to changes on the 'user_activity_logs' collection.
  // Example:
  // final subscription = pocketBase.collection('user_activity_logs').subscribe('*', (e) {
  //   if (e.record != null && e.record!.data['entity_id'] == applicationId && e.record!.data['entity_type'] == 'application') {
  //     fetchAndEmit(); // Re-fetch when relevant changes occur
  //   }
  // });

  // controller.onCancel = () {
  //   subscription.then((s) => pocketBase.collection('user_activity_logs').unsubscribe(s.id));
  //   controller.close();
  // };

  return controller.stream;
});
*/
