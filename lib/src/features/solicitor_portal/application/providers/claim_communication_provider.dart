import 'dart:math';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/local_notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/claim_message_model.dart'; // Changed import
import 'package:pocketbase/pocketbase.dart';

// State for communication
class ClaimCommunicationState {
  // Changed class name
  final List<ClaimMessageModel> messages; // Changed model type
  final bool isLoading;
  final String? error;
  final bool isSending;
  final DateTime? lastCheckedAgentMessageTimestamp; // For potential UI use

  ClaimCommunicationState({
    // Changed constructor name
    this.messages = const [],
    this.isLoading = false,
    this.error,
    this.isSending = false,
    this.lastCheckedAgentMessageTimestamp,
  });

  ClaimCommunicationState copyWith({
    // Changed method name
    List<ClaimMessageModel>? messages, // Changed model type
    bool? isLoading,
    String? error,
    bool? clearError,
    bool? isSending,
    DateTime? lastCheckedAgentMessageTimestamp,
  }) {
    return ClaimCommunicationState(
      // Changed class name
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      error: clearError == true ? null : error ?? this.error,
      isSending: isSending ?? this.isSending,
      lastCheckedAgentMessageTimestamp:
          lastCheckedAgentMessageTimestamp ??
          this.lastCheckedAgentMessageTimestamp,
    );
  }
}

// StateNotifier
class ClaimCommunicationNotifier
    extends StateNotifier<ClaimCommunicationState> {
  // Changed class name
  final PocketBaseService _pbService;
  final String _claimId; // Changed from _applicationId
  // In-memory store for the timestamp of the last agent message that triggered a notification for this claimId
  static final Map<String, DateTime> _lastProcessedAgentMessageTimestamp = {};

  ClaimCommunicationNotifier(
    this._pbService,
    this._claimId,
  ) // Changed constructor name and parameter
  : super(ClaimCommunicationState()) {
    // Changed initial state
    fetchMessages();
  }

  Future<void> fetchMessages() async {
    state = state.copyWith(isLoading: true, clearError: true);
    try {
      final records = await _pbService.client
          .collection('funding_application_communications')
          .getFullList(
            // Changed collection name
            filter:
                'funding_application_id = "$_claimId"', // Changed filter field and variable
            sort: 'created',
            expand: 'sender_id',
          );
      final messages =
          records
              .map((record) => ClaimMessageModel.fromRecord(record))
              .toList(); // Changed model type

      // New message detection logic
      final currentSolicitorId = _pbService.client.authStore.record?.id;
      if (messages.isNotEmpty && currentSolicitorId != null) {
        final latestMessage = messages.first;
        // Assuming 'admin' user_type for 3Pay Global Agents
        if (latestMessage.senderId != currentSolicitorId &&
            latestMessage.senderUserType == 'admin') {
          final lastProcessedTime =
              _lastProcessedAgentMessageTimestamp[_claimId];
          if (latestMessage.created.isAfter(
            lastProcessedTime ?? DateTime.fromMillisecondsSinceEpoch(0),
          )) {
            // This is a new, unprocessed message from an agent
            try {
              final notificationBody = {
                'recipientId': [
                  currentSolicitorId,
                ], // Use array format for recipientId
                'type': 'new_message_claim', // Differentiate notification type
                'title':
                    'New Message: Claim $_claimId', // Consider fetching claim title/name
                'message':
                    'New message from ${latestMessage.senderName ?? '3Pay Agent'}: ${latestMessage.messageContent.substring(0, min(50, latestMessage.messageContent.length))}${latestMessage.messageContent.length > 50 ? "..." : ""}',
                'related_item_id': _claimId,
                'isRead': false, // Use isRead instead of is_read
              };
              await _pbService.client
                  .collection('notifications')
                  .create(body: notificationBody);
              _lastProcessedAgentMessageTimestamp[_claimId] =
                  latestMessage.created;

              // Trigger local notification for new agent message
              try {
                final notificationId =
                    LocalNotificationService.generateNotificationId();
                final payload =
                    LocalNotificationService.createNotificationPayload(
                      type: 'agent_message',
                      id: _claimId,
                      route: '/claims/$_claimId/messages',
                      additionalData: {
                        'senderId': latestMessage.senderId,
                        'senderName': latestMessage.senderName ?? '3Pay Agent',
                        'messagePreview': latestMessage.messageContent
                            .substring(
                              0,
                              min(100, latestMessage.messageContent.length),
                            ),
                      },
                    );

                await LocalNotificationService.showMessageNotification(
                  id: notificationId,
                  title: 'New Message: Claim $_claimId',
                  body:
                      'New message from ${latestMessage.senderName ?? '3Pay Agent'}: ${latestMessage.messageContent.substring(0, min(50, latestMessage.messageContent.length))}${latestMessage.messageContent.length > 50 ? "..." : ""}',
                  payload: payload,
                );

                LoggerService.info(
                  'Local notification shown for new agent message on claim $_claimId',
                );
              } catch (e) {
                LoggerService.error(
                  'Error showing local notification for agent message',
                  e,
                );
              }

              LoggerService.info(
                'New agent message notification created for claim $_claimId',
              );
            } catch (e) {
              // Log or handle notification creation error
              LoggerService.error(
                'Error creating notification record for claim',
                e,
              );
            }
          }
        }
      }
      state = state.copyWith(
        messages: messages,
        isLoading: false,
        lastCheckedAgentMessageTimestamp: DateTime.now(),
      );
    } on ClientException catch (e) {
      state = state.copyWith(
        error:
            'Failed to fetch messages: ${e.response['message'] ?? e.toString()}',
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: 'An unexpected error occurred: ${e.toString()}',
        isLoading: false,
      );
    }
  }

  Future<bool> sendMessage({
    required String messageContent,
    required String senderId,
    String? recipientId,
    String? recipientGroup,
  }) async {
    if (messageContent.trim().isEmpty) {
      state = state.copyWith(error: "Message cannot be empty");
      return false;
    }
    state = state.copyWith(isSending: true, clearError: true);
    try {
      final body = <String, dynamic>{
        'funding_application_id': _claimId, // Changed field name and variable
        'sender_id': senderId,
        'message_content': messageContent,
      };
      if (recipientId != null) {
        body['recipient_id'] = recipientId;
      } else if (recipientGroup != null) {
        body['recipient_group'] = recipientGroup;
      } else {
        if (recipientId == null && recipientGroup == null) {
          state = state.copyWith(
            error: "Recipient not specified.",
            isSending: false,
          );
          return false;
        }
      }

      await _pbService.client
          .collection('claim_communications')
          .create(body: body); // Changed collection name
      state = state.copyWith(isSending: false);
      await fetchMessages(); // Refresh messages after sending
      return true;
    } on ClientException catch (e) {
      state = state.copyWith(
        error:
            'Failed to send message: ${e.response['message'] ?? e.toString()}',
        isSending: false,
      );
      return false;
    } catch (e) {
      state = state.copyWith(
        error: 'An unexpected error occurred while sending: ${e.toString()}',
        isSending: false,
      );
      return false;
    }
  }
}

// Provider
final claimCommunicationProvider = StateNotifierProvider.family<
  // Changed provider name
  ClaimCommunicationNotifier,
  ClaimCommunicationState,
  String
>(
  // Changed notifier and state class names
  (ref, claimId) {
    // Changed parameter name
    final pbService = ref.watch(
      pocketBaseClientProvider,
    ); // This line might need adjustment if pocketBaseClientProvider is not global or if PocketBaseService instantiation is preferred.
    // Assuming PocketBaseService() is the correct way to get an instance as per the original file.
    return ClaimCommunicationNotifier(
      PocketBaseService(),
      claimId,
    ); // Changed notifier class name and parameter
  },
);
