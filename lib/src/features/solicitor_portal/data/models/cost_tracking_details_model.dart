import 'dart:convert';

enum CostType {
  legalFees,
  barristerFees,
  expertFees,
  courtFees,
  mediationCosts,
  translationServices,
  documentManagement,
  travelExpenses,
  insurancePremiums, // ATE/LFI
  otherDisbursements;

  String get displayName {
    switch (this) {
      case CostType.legalFees:
        return 'Legal Fees (Solicitor)';
      case CostType.barristerFees:
        return 'Barrister Fees';
      case CostType.expertFees:
        return 'Expert Witness Fees';
      case CostType.courtFees:
        return 'Court Fees';
      case CostType.mediationCosts:
        return 'Mediation Costs';
      case CostType.translationServices:
        return 'Translation Services';
      case CostType.documentManagement:
        return 'Document Management/Copying';
      case CostType.travelExpenses:
        return 'Travel Expenses';
      case CostType.insurancePremiums:
        return 'Insurance Premiums (ATE/LFI)';
      case CostType.otherDisbursements:
        return 'Other Disbursements';
      default:
        return toString().split('.').last;
    }
  }
}

class CostTrackingDetails { // Reverted to original name for consistency if used elsewhere
  final String id; // Added ID
  final CostType costType;
  final String description;
  final double amount;
  final DateTime dateIncurred;
  final String? invoiceFileId;
  final String? invoiceFilename;
  final String loggedByUserId;
  final String loggedByName;
  final DateTime loggedAt;

  CostTrackingDetails({ // Reverted to original name
    required this.id,
    required this.costType,
    required this.description,
    required this.amount,
    required this.dateIncurred,
    this.invoiceFileId,
    this.invoiceFilename,
    required this.loggedByUserId,
    required this.loggedByName,
    required this.loggedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cost_type': costType.name, // Storing enum by name
      'description': description,
      'amount': amount,
      'date_incurred': dateIncurred.toIso8601String(),
      'invoice_file_id': invoiceFileId,
      'invoice_filename': invoiceFilename,
      'logged_by_user_id': loggedByUserId,
      'logged_by_name': loggedByName,
      'logged_at': loggedAt.toIso8601String(),
    };
  }

  factory CostTrackingDetails.fromJson(Map<String, dynamic> map) { // Reverted
    return CostTrackingDetails( // Reverted
      id: map['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(), // Provide default if missing
      costType: CostType.values.firstWhere(
            (e) => e.name == map['cost_type'],
            orElse: () => CostType.otherDisbursements, // Default if parsing fails
          ),
      description: map['description'] ?? '',
      amount: (map['amount'] as num?)?.toDouble() ?? 0.0,
      dateIncurred: map['date_incurred'] != null
          ? DateTime.parse(map['date_incurred'])
          : DateTime.now(),
      invoiceFileId: map['invoice_file_id'],
      invoiceFilename: map['invoice_filename'],
      loggedByUserId: map['logged_by_user_id'] ?? '',
      loggedByName: map['logged_by_name'] ?? '',
      loggedAt: map['logged_at'] != null
          ? DateTime.parse(map['logged_at'])
          : DateTime.now(),
    );
  }
}


// The old CostTrackingDetailsModel can be removed or kept if it serves a different purpose for overall budget summary
// For individual entries, CostTrackingDetails is more appropriate.
// I will comment out the old model for now.


