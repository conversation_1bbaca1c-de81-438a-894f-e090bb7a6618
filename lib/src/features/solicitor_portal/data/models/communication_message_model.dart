import 'package:pocketbase/pocketbase.dart';

class CommunicationMessage {
  final String id;
  final String? applicationId;
  final String? claimId;
  final String senderId;
  final String senderName; // Assuming this will be populated by expanding senderId
  final String recipientId;
  final String messageContent;
  final DateTime timestamp;
  final bool isRead;

  CommunicationMessage({
    required this.id,
    this.applicationId,
    this.claimId,
    required this.senderId,
    required this.senderName,
    required this.recipientId,
    required this.messageContent,
    required this.timestamp,
    this.isRead = false,
  });

  factory CommunicationMessage.fromJson(Map<String, dynamic> json, String currentUserId) {
    final expand = json['expand'] as Map<String, dynamic>? ?? {};
    final senderData = expand['sender_id'] as Map<String, dynamic>? ?? {};
    
    String name = senderData['name'] as String? ?? 'Unknown Sender';
    if (json['sender_id'] == currentUserId) {
      name = 'You';
    } else if (name == 'Unknown Sender') {
      // Potentially fetch agent name if it's a known agent ID structure
      // For now, if not 'You' and not expanded, use a generic agent name
      name = '3Pay Global Agent';
    }


    return CommunicationMessage(
      id: json['id'] as String,
      applicationId: json['application_id'] as String?,
      claimId: json['claim_id'] as String?,
      senderId: json['sender_id'] as String,
      senderName: name,
      recipientId: json['recipient_id'] as String,
      messageContent: json['message_content'] as String,
      timestamp: DateTime.tryParse(json['created'] as String? ?? '') ?? DateTime.now(),
      isRead: json['is_read'] as bool? ?? false,
    );
  }

  factory CommunicationMessage.fromRecord(RecordModel record, String currentUserId) {
    final expand = record.expand;
    final senderData = expand['sender_id']?.firstOrNull?.data;

    String name = senderData?['name'] as String? ?? 'Unknown Sender';
    if (record.getStringValue('sender_id') == currentUserId) {
      name = 'You';
    } else if (name == 'Unknown Sender') {
      name = '3Pay Global Agent'; // Placeholder if not expanded or specific agent name
    }
    
    return CommunicationMessage(
      id: record.id,
      applicationId: record.data['application_id'] as String?,
      claimId: record.data['claim_id'] as String?,
      senderId: record.getStringValue('sender_id'),
      senderName: name,
      recipientId: record.getStringValue('recipient_id'),
      messageContent: record.getStringValue('message_content'),
      timestamp: DateTime.tryParse(record.created) ?? DateTime.now(),
      isRead: record.data['is_read'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (applicationId != null) 'application_id': applicationId,
      if (claimId != null) 'claim_id': claimId,
      'sender_id': senderId,
      'recipient_id': recipientId,
      'message_content': messageContent,
      // 'timestamp' is handled by PocketBase 'created'
      // 'is_read' might be updated separately
    };
  }
}