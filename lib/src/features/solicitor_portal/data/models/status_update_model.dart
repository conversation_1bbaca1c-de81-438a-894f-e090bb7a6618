import 'package:intl/intl.dart';

class StatusUpdateModel {
  final DateTime date;
  final String description;
  final String? updatedByUserId;
  final String? updatedByName;

  StatusUpdateModel({
    required this.date,
    required this.description,
    this.updatedByUserId,
    this.updatedByName,
  });

  factory StatusUpdateModel.fromJson(Map<String, dynamic> json) {
    return StatusUpdateModel(
      date: DateTime.parse(json['date'] as String),
      description: json['description'] as String,
      updatedByUserId: json['updated_by_user_id'] as String?,
      updatedByName: json['updated_by_name'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'description': description,
      'updated_by_user_id': updatedByUserId,
      'updated_by_name': updatedByName,
    };
  }

  String get formattedDate {
    return DateFormat('dd MMM yyyy, HH:mm').format(date);
  }
}