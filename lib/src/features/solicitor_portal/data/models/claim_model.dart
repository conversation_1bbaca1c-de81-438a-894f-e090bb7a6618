import 'dart:convert'; // For jsonDecode
import 'package:flutter/foundation.dart'; // For debugPrint
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/professional_model.dart'; // For Barrister, Expert
import 'package:three_pay_group_litigation_platform/src/features/auth/data/models/user_model.dart'; // Assuming a generic User model for firm members
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart'; // For UploadedDocumentCategory
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/cost_tracking_details_model.dart'; // Added for CostTrackingDetails
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/solicitor_profile_model.dart'; // Added for SolicitorProfileModel

class StatusUpdate {
  final DateTime date;
  final String description;
  final String? updatedByUserId;
  final String? updatedByName;

  StatusUpdate({
    required this.date,
    required this.description,
    this.updatedByUserId,
    this.updatedByName,
  });

  factory StatusUpdate.fromJson(Map<String, dynamic> json) {
    return StatusUpdate(
      date: DateTime.tryParse(json['date'] as String? ?? '') ?? DateTime.now(),
      description: json['description'] as String? ?? '',
      updatedByUserId: json['updated_by_user_id'] as String?,
      updatedByName: json['updated_by_name'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'description': description,
      'updated_by_user_id': updatedByUserId,
      'updated_by_name': updatedByName,
    };
  }
}

class Claim {
  final String id;
  final String fundingApplicationId; // Relation
  final String caseTitle;
  final String? caseSummaryPublic;
  final String? solicitorDetailsPublic;
  final String? barristerDetailsPublic;
  final String? currentStatus;
  final List<StatusUpdate> statusUpdates;
  final List<UploadedDocumentCategory>
  documentRepository; // Changed to UploadedDocumentCategory
  final List<CostTrackingDetails>? costTracking; // Changed type
  final List<String> associatedCoFunderIds; // Relation IDs
  final double? totalFundingSecured;
  final double? requiredFundingAmount; // From funding_applications
  final double? minimumValueClaim; // From funding_applications
  final String? claimantType; // From funding_applications
  final String? claimIndustry; // From funding_applications
  final String? claimType; // From funding_applications
  final String? expectedFrfrSchedule; // Could be JSON string or plain text
  final List<String> linkedBarristerIds; // Relation IDs
  final List<Barrister>? linkedBarristersExpanded; // For display
  final List<String> linkedExpertIds; // Relation IDs
  final List<Expert>? linkedExpertsExpanded; // For display
  final List<String> assignedFirmMemberIds; // Relation IDs
  final List<User>? assignedFirmMembersExpanded; // For display
  final List<String> claimAdminIds; // New: Relation IDs for claim_admins
  final List<SolicitorProfileModel>? claimAdminsExpanded; // New: For display
  final List<String>
  associatedSolicitorIds; // New: Relation IDs for associated_solicitors
  final List<SolicitorProfileModel>?
  associatedSolicitorsExpanded; // New: For display
  final DateTime created;
  final DateTime updated;

  Claim({
    required this.id,
    required this.fundingApplicationId,
    required this.caseTitle,
    this.caseSummaryPublic,
    this.solicitorDetailsPublic,
    this.barristerDetailsPublic,
    this.currentStatus,
    required this.statusUpdates,
    required this.documentRepository,
    this.costTracking,
    required this.associatedCoFunderIds,
    this.totalFundingSecured,
    this.requiredFundingAmount,
    this.minimumValueClaim,
    this.claimantType,
    this.claimIndustry,
    this.claimType,
    this.expectedFrfrSchedule,
    required this.linkedBarristerIds,
    this.linkedBarristersExpanded,
    required this.linkedExpertIds,
    this.linkedExpertsExpanded,
    required this.assignedFirmMemberIds,
    this.assignedFirmMembersExpanded,
    required this.claimAdminIds, // New
    this.claimAdminsExpanded, // New
    required this.associatedSolicitorIds, // New
    this.associatedSolicitorsExpanded, // New
    required this.created,
    required this.updated,
  });

  // Helper method to parse relation fields that can be either a single string or a list
  static List<String> _parseRelationField(dynamic field) {
    if (field == null) return [];
    if (field is List) {
      return field.map((item) => item.toString()).toList();
    } else if (field is String) {
      return field.isEmpty ? [] : [field];
    }
    return [];
  }

  // Helper method to parse expanded fields that can be either a single object or a list
  static List<T>? _parseExpandedList<T>(
    dynamic field,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    if (field == null) return null;

    if (field is List) {
      // Handle array of objects
      return field
          .map((item) => fromJson(item as Map<String, dynamic>))
          .toList();
    } else if (field is Map<String, dynamic>) {
      // Handle single object - wrap it in a list
      return [fromJson(field)];
    }

    return null;
  }

  factory Claim.fromJson(Map<String, dynamic> record) {
    // Renamed from fromPocketBase
    final expand = record['expand'] as Map<String, dynamic>? ?? {};

    List<StatusUpdate> parseStatusUpdates(dynamic jsonField) {
      if (jsonField is List) {
        return jsonField
            .map((item) => StatusUpdate.fromJson(item as Map<String, dynamic>))
            .toList();
      } else if (jsonField is String) {
        try {
          final decoded = jsonDecode(jsonField);
          if (decoded is List) {
            return decoded
                .map(
                  (item) => StatusUpdate.fromJson(item as Map<String, dynamic>),
                )
                .toList();
          }
        } catch (e) {
          // If string is not valid JSON list, return empty
          debugPrint('Error parsing status_updates string: $e');
          return [];
        }
        return [];
      }
      return [];
    }

    List<UploadedDocumentCategory> parseDocumentRepository(dynamic jsonField) {
      // Changed to UploadedDocumentCategory
      debugPrint(
        'Claim.fromJson: parseDocumentRepository called with: $jsonField (type: ${jsonField.runtimeType})',
      );

      if (jsonField is List) {
        debugPrint(
          'Claim.fromJson: Processing List with ${jsonField.length} items',
        );
        final result =
            jsonField.map((item) {
              debugPrint('Claim.fromJson: Processing document item: $item');
              return UploadedDocumentCategory.fromJson(
                item as Map<String, dynamic>,
              );
            }).toList(); // Uses UploadedDocumentCategory.fromJson
        debugPrint(
          'Claim.fromJson: Parsed ${result.length} document categories',
        );
        return result;
      } else if (jsonField is String) {
        try {
          final decoded = jsonDecode(jsonField);
          if (decoded is List) {
            debugPrint(
              'Claim.fromJson: Processing decoded JSON List with ${decoded.length} items',
            );
            final result =
                decoded
                    .map(
                      (item) => UploadedDocumentCategory.fromJson(
                        item as Map<String, dynamic>,
                      ),
                    )
                    .toList();
            debugPrint(
              'Claim.fromJson: Parsed ${result.length} document categories from JSON string',
            );
            return result;
          }
        } catch (e) {
          debugPrint('Error parsing document_repository string: $e');
          return [];
        }
        return [];
      }
      debugPrint(
        'Claim.fromJson: No valid document repository data found, returning empty list',
      );
      return [];
    }

    List<CostTrackingDetails> parseCostTracking(dynamic jsonField) {
      if (jsonField is List) {
        return jsonField
            .map(
              (item) =>
                  CostTrackingDetails.fromJson(item as Map<String, dynamic>),
            )
            .toList();
      } else if (jsonField is String) {
        try {
          final decoded = jsonDecode(jsonField);
          if (decoded is List) {
            return decoded
                .map(
                  (item) => CostTrackingDetails.fromJson(
                    item as Map<String, dynamic>,
                  ),
                )
                .toList();
          }
        } catch (e) {
          debugPrint('Error parsing cost_tracking string: $e');
          return [];
        }
        return [];
      }
      return [];
    }

    return Claim(
      id: record['id'] as String,
      fundingApplicationId:
          record['funding_application_id'] as String? ?? record['id'] as String,
      caseTitle:
          record['claim_title'] as String? ??
          record['case_title'] as String? ??
          'N/A',
      caseSummaryPublic: record['case_summary_public'] as String?,
      solicitorDetailsPublic: record['solicitor_details_public'] as String?,
      barristerDetailsPublic: record['barrister_details_public'] as String?,
      currentStatus:
          record['application_status'] as String? ??
          record['current_status'] as String?,
      statusUpdates: parseStatusUpdates(record['status_updates']),
      documentRepository: parseDocumentRepository(
        record['document_repository'],
      ),
      costTracking: parseCostTracking(
        record['cost_tracking'],
      ), // Updated parsing
      associatedCoFunderIds: _parseRelationField(
        record['associated_co_funder_ids'],
      ),
      totalFundingSecured:
          (record['total_funding_secured'] as num?)?.toDouble(),
      requiredFundingAmount:
          (record['required_funding_amount'] as num?)?.toDouble(),
      minimumValueClaim: (record['minimum_value_claim'] as num?)?.toDouble(),
      claimantType: record['claimant_type'] as String?,
      claimIndustry: record['claim_industry'] as String?,
      claimType: record['claim_type'] as String?,
      expectedFrfrSchedule:
          record['expected_frfr_schedule'] as String?, // Assuming text for now
      linkedBarristerIds: _parseRelationField(
        record['linked_barristers'] ?? record['barristers'],
      ), // Try both field names
      linkedBarristersExpanded: _parseExpandedList<Barrister>(
        expand['barristers'],
        (data) => Barrister.fromJson(data as Map<String, dynamic>),
      ),
      linkedExpertIds: _parseRelationField(
        record['linked_experts'] ?? record['experts'],
      ), // Try both field names
      linkedExpertsExpanded: _parseExpandedList<Expert>(
        expand['experts'],
        (data) => Expert.fromJson(data as Map<String, dynamic>),
      ),
      assignedFirmMemberIds: _parseRelationField(
        record['assigned_firm_members'],
      ), // Field name in PB might be assigned_firm_member_ids
      assignedFirmMembersExpanded: _parseExpandedList<User>(
        expand['assigned_firm_member_ids'],
        (data) => User.fromJson(data as Map<String, dynamic>),
      ),
      claimAdminIds: _parseRelationField(record['claim_admins']), // New
      claimAdminsExpanded: _parseExpandedList<SolicitorProfileModel>(
        expand['claim_admins'],
        (data) => SolicitorProfileModel.fromJson(data as Map<String, dynamic>),
      ),
      associatedSolicitorIds: _parseRelationField(
        record['associated_solicitors'],
      ), // New
      associatedSolicitorsExpanded: _parseExpandedList<SolicitorProfileModel>(
        expand['associated_solicitors'],
        (data) => SolicitorProfileModel.fromJson(data as Map<String, dynamic>),
      ),
      created:
          DateTime.tryParse(record['created'] as String? ?? '') ??
          DateTime.now(),
      updated:
          DateTime.tryParse(record['updated'] as String? ?? '') ??
          DateTime.now(),
    );
  }

  Map<String, dynamic> toJsonForUpdate() {
    return {
      'case_title': caseTitle,
      'case_summary_public': caseSummaryPublic,
      'current_status': currentStatus,
      'status_updates': statusUpdates.map((e) => e.toJson()).toList(),
      'document_repository': documentRepository.map((e) => e.toJson()).toList(),
      'cost_tracking':
          costTracking
              ?.map((e) => e.toJson())
              .toList(), // Updated serialization
      'expected_frfr_schedule': expectedFrfrSchedule,
      'linked_barrister_ids': linkedBarristerIds,
      'linked_expert_ids': linkedExpertIds,
      'assigned_firm_member_ids': assignedFirmMemberIds,
      'claim_admins': claimAdminIds, // New
      'associated_solicitors': associatedSolicitorIds, // New
    };
  }

  Claim copyWith({
    String? id,
    String? fundingApplicationId,
    String? caseTitle,
    String? caseSummaryPublic,
    String? solicitorDetailsPublic,
    String? barristerDetailsPublic,
    String? currentStatus,
    List<StatusUpdate>? statusUpdates,
    List<UploadedDocumentCategory>? documentRepository,
    List<CostTrackingDetails>? costTracking,
    List<String>? associatedCoFunderIds,
    double? totalFundingSecured,
    double? requiredFundingAmount,
    double? minimumValueClaim,
    String? claimantType,
    String? claimIndustry,
    String? claimType,
    String? expectedFrfrSchedule,
    List<String>? linkedBarristerIds,
    List<Barrister>? linkedBarristersExpanded,
    List<String>? linkedExpertIds,
    List<Expert>? linkedExpertsExpanded,
    List<String>? assignedFirmMemberIds,
    List<User>? assignedFirmMembersExpanded,
    List<String>? claimAdminIds, // New
    List<SolicitorProfileModel>? claimAdminsExpanded, // New
    List<String>? associatedSolicitorIds, // New
    List<SolicitorProfileModel>? associatedSolicitorsExpanded, // New
    DateTime? created,
    DateTime? updated,
  }) {
    return Claim(
      id: id ?? this.id,
      fundingApplicationId: fundingApplicationId ?? this.fundingApplicationId,
      caseTitle: caseTitle ?? this.caseTitle,
      caseSummaryPublic: caseSummaryPublic ?? this.caseSummaryPublic,
      solicitorDetailsPublic:
          solicitorDetailsPublic ?? this.solicitorDetailsPublic,
      barristerDetailsPublic:
          barristerDetailsPublic ?? this.barristerDetailsPublic,
      currentStatus: currentStatus ?? this.currentStatus,
      statusUpdates: statusUpdates ?? this.statusUpdates,
      documentRepository: documentRepository ?? this.documentRepository,
      costTracking: costTracking ?? this.costTracking,
      associatedCoFunderIds:
          associatedCoFunderIds ?? this.associatedCoFunderIds,
      totalFundingSecured: totalFundingSecured ?? this.totalFundingSecured,
      requiredFundingAmount:
          requiredFundingAmount ?? this.requiredFundingAmount,
      minimumValueClaim: minimumValueClaim ?? this.minimumValueClaim,
      claimantType: claimantType ?? this.claimantType,
      claimIndustry: claimIndustry ?? this.claimIndustry,
      claimType: claimType ?? this.claimType,
      expectedFrfrSchedule: expectedFrfrSchedule ?? this.expectedFrfrSchedule,
      linkedBarristerIds: linkedBarristerIds ?? this.linkedBarristerIds,
      linkedBarristersExpanded:
          linkedBarristersExpanded ?? this.linkedBarristersExpanded,
      linkedExpertIds: linkedExpertIds ?? this.linkedExpertIds,
      linkedExpertsExpanded:
          linkedExpertsExpanded ?? this.linkedExpertsExpanded,
      assignedFirmMemberIds:
          assignedFirmMemberIds ?? this.assignedFirmMemberIds,
      assignedFirmMembersExpanded:
          assignedFirmMembersExpanded ?? this.assignedFirmMembersExpanded,
      claimAdminIds: claimAdminIds ?? this.claimAdminIds, // New
      claimAdminsExpanded:
          claimAdminsExpanded ?? this.claimAdminsExpanded, // New
      associatedSolicitorIds:
          associatedSolicitorIds ?? this.associatedSolicitorIds, // New
      associatedSolicitorsExpanded:
          associatedSolicitorsExpanded ??
          this.associatedSolicitorsExpanded, // New
      created: created ?? this.created,
      updated: updated ?? this.updated,
    );
  }
}
