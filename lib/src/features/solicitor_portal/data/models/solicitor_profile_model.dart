import 'package:flutter/foundation.dart';

class SolicitorProfileModel {
  final String id;
  final String userId; // Links to the 'users' collection record
  final String solicitorName;
  final String lawFirmName;
  final String sraNumber;
  final String firmAddress;
  final String? contactNumber;
  final String? positionInFirm;
  final String? puStatus; // e.g., 'Pending', 'Approved', 'Rejected'
  final String? puApplicationDate;
  final String? puDecisionDate;
  final List<String> additionalUsers; // List of user IDs
  final String created;
  final String updated;
  final String collectionId;
  final String collectionName;
  final String? userEmail; // New: for expanded user data
  final String?
  userName; // New: for expanded user data (e.g. full name from users table)

  SolicitorProfileModel({
    required this.id,
    required this.userId,
    required this.solicitorName, // This is likely the solicitor's display name from their profile
    required this.lawFirmName,
    required this.sraNumber,
    required this.firmAddress,
    this.contactNumber,
    this.positionInFirm,
    this.puStatus,
    this.puApplicationDate,
    this.puDecisionDate,
    List<String>? additionalUsers,
    required this.created,
    required this.updated,
    required this.collectionId,
    required this.collectionName,
    this.userEmail, // New
    this.userName, // New
  }) : additionalUsers = additionalUsers ?? [];

  factory SolicitorProfileModel.fromJson(Map<String, dynamic> json) {
    // Debug: Print the raw JSON data
    debugPrint('SolicitorProfileModel.fromJson raw data: $json');

    // Safely extract user data from expand
    String? userEmail;
    String? userName;

    try {
      // Debug: Check if 'expand' exists and what type it is
      if (json.containsKey('expand')) {
        debugPrint('Expand exists, type: ${json['expand'].runtimeType}');
        debugPrint('Expand value: ${json['expand']}');

        if (json['expand'] != null) {
          // Handle different types of 'expand' data
          if (json['expand'] is Map<String, dynamic>) {
            final expand = json['expand'] as Map<String, dynamic>;
            debugPrint('Expand is a Map');

            if (expand.containsKey('user_id') && expand['user_id'] != null) {
              debugPrint(
                'user_id exists in expand, type: ${expand['user_id'].runtimeType}',
              );

              if (expand['user_id'] is Map<String, dynamic>) {
                final userData = expand['user_id'] as Map<String, dynamic>;
                debugPrint('userData: $userData');

                if (userData.containsKey('email')) {
                  userEmail = userData['email'] as String?;
                  debugPrint('Found email: $userEmail');
                }

                if (userData.containsKey('name')) {
                  userName = userData['name'] as String?;
                  debugPrint('Found name: $userName');
                }
              } else {
                debugPrint('user_id is not a Map: ${expand['user_id']}');
              }
            } else {
              debugPrint('user_id not found in expand or is null');
            }
          } else if (json['expand'] is String) {
            debugPrint('Expand is a String: ${json['expand']}');
            // Handle the case where 'expand' is a String
          } else {
            debugPrint(
              'Expand is neither a Map nor a String: ${json['expand']}',
            );
          }
        }
      }

      // Also check for directly added userEmail and userName fields
      if (json.containsKey('userEmail')) {
        debugPrint('userEmail exists directly in json: ${json['userEmail']}');
        userEmail = json['userEmail'] as String?;
      }

      if (json.containsKey('userName')) {
        debugPrint('userName exists directly in json: ${json['userName']}');
        userName = json['userName'] as String?;
      }
    } catch (e) {
      // Ignore errors in expanded data
      debugPrint('Error extracting user data from expand: $e');
    }

    return SolicitorProfileModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      solicitorName: json['solicitor_name'] as String,
      lawFirmName: json['law_firm_name'] as String,
      sraNumber:
          json['sra_number'] as String? ??
          '', // Made sraNumber nullable in PB, ensure model matches
      firmAddress: json['firm_address'] as String,
      contactNumber: json['contact_number'] as String?,
      positionInFirm:
          json['position'] as String?, // Corrected field name from PB
      puStatus: json['pu_status'] as String?,
      puApplicationDate: json['pu_application_date'] as String?,
      puDecisionDate: json['pu_decision_date'] as String?,
      additionalUsers: _safelyExtractAdditionalUsers(json),
      created: json['created'] as String,
      updated: json['updated'] as String,
      collectionId:
          json['collectionId'] as String? ??
          '', // PocketBase might not always send this
      collectionName:
          json['collectionName'] as String? ??
          '', // PocketBase might not always send this
      userEmail: userEmail,
      userName: userName,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'solicitor_name': solicitorName,
      'law_firm_name': lawFirmName,
      'sra_number': sraNumber,
      'firm_address': firmAddress,
      'contact_number': contactNumber,
      'position': positionInFirm, // Corrected field name for PB
      'pu_status': puStatus,
      'pu_application_date': puApplicationDate,
      'pu_decision_date': puDecisionDate,
      'additional_users': additionalUsers,
      // userEmail and userName are not part of the solicitor_profiles collection schema,
      // so they are not included in toJson for updates.
    };
  }

  SolicitorProfileModel copyWith({
    String? id,
    String? userId,
    String? solicitorName,
    String? lawFirmName,
    String? sraNumber,
    String? firmAddress,
    String? contactNumber,
    String? positionInFirm,
    String? puStatus,
    String? puApplicationDate,
    String? puDecisionDate,
    List<String>? additionalUsers,
    String? created,
    String? updated,
    String? collectionId,
    String? collectionName,
    String? userEmail, // New
    String? userName, // New
  }) {
    return SolicitorProfileModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      solicitorName: solicitorName ?? this.solicitorName,
      lawFirmName: lawFirmName ?? this.lawFirmName,
      sraNumber: sraNumber ?? this.sraNumber,
      firmAddress: firmAddress ?? this.firmAddress,
      contactNumber: contactNumber ?? this.contactNumber,
      positionInFirm: positionInFirm ?? this.positionInFirm,
      puStatus: puStatus ?? this.puStatus,
      puApplicationDate: puApplicationDate ?? this.puApplicationDate,
      puDecisionDate: puDecisionDate ?? this.puDecisionDate,
      additionalUsers: additionalUsers ?? this.additionalUsers,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      collectionId: collectionId ?? this.collectionId,
      collectionName: collectionName ?? this.collectionName,
      userEmail: userEmail ?? this.userEmail, // New
      userName: userName ?? this.userName, // New
    );
  }

  /// Check if profile is complete
  bool get isProfileComplete {
    return id.isNotEmpty &&
        userId.isNotEmpty &&
        solicitorName.isNotEmpty &&
        lawFirmName.isNotEmpty &&
        firmAddress.isNotEmpty &&
        sraNumber.isNotEmpty;
  }

  @override
  String toString() {
    return 'SolicitorProfileModel(id: $id, userId: $userId, solicitorName: $solicitorName, lawFirmName: $lawFirmName, sraNumber: $sraNumber, firmAddress: $firmAddress, contactNumber: $contactNumber, positionInFirm: $positionInFirm, puStatus: $puStatus, puApplicationDate: $puApplicationDate, puDecisionDate: $puDecisionDate, additionalUsers: $additionalUsers, created: $created, updated: $updated, collectionId: $collectionId, collectionName: $collectionName, userEmail: $userEmail, userName: $userName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is SolicitorProfileModel &&
        other.id == id &&
        other.userId == userId &&
        other.solicitorName == solicitorName &&
        other.lawFirmName == lawFirmName &&
        other.sraNumber == sraNumber &&
        other.firmAddress == firmAddress &&
        other.contactNumber == contactNumber &&
        other.positionInFirm == positionInFirm &&
        other.puStatus == puStatus &&
        other.puApplicationDate == puApplicationDate &&
        other.puDecisionDate == puDecisionDate &&
        listEquals(other.additionalUsers, additionalUsers) &&
        other.created == created &&
        other.updated == updated &&
        other.collectionId == collectionId &&
        other.collectionName == collectionName &&
        other.userEmail == userEmail && // New
        other.userName == userName; // New
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        solicitorName.hashCode ^
        lawFirmName.hashCode ^
        sraNumber.hashCode ^
        firmAddress.hashCode ^
        contactNumber.hashCode ^
        positionInFirm.hashCode ^
        puStatus.hashCode ^
        puApplicationDate.hashCode ^
        puDecisionDate.hashCode ^
        additionalUsers.hashCode ^
        created.hashCode ^
        updated.hashCode ^
        collectionId.hashCode ^
        collectionName.hashCode ^
        userEmail.hashCode ^ // New
        userName.hashCode; // New
  }

  // Helper method to safely extract additional users
  static List<String> _safelyExtractAdditionalUsers(Map<String, dynamic> json) {
    try {
      // Debug the additional_users field
      debugPrint('additional_users field: ${json['additional_users']}');
      debugPrint(
        'additional_users type: ${json['additional_users']?.runtimeType}',
      );

      if (json['additional_users'] == null) {
        debugPrint('additional_users is null, returning empty list');
        return [];
      }

      if (json['additional_users'] is List) {
        final list = json['additional_users'] as List;
        debugPrint('additional_users is a List with ${list.length} items');

        // Convert all items to String
        final result =
            list.map((e) {
              debugPrint('Item type: ${e.runtimeType}, value: $e');
              return e.toString();
            }).toList();

        return result;
      } else if (json['additional_users'] is String) {
        // Handle the case where additional_users is a String
        debugPrint('additional_users is a String: ${json['additional_users']}');

        // If it's a comma-separated string, split it
        if ((json['additional_users'] as String).contains(',')) {
          return (json['additional_users'] as String).split(',');
        }

        // Otherwise, return it as a single-item list
        return [json['additional_users'] as String];
      } else {
        debugPrint(
          'additional_users is neither a List nor a String, returning empty list',
        );
        return [];
      }
    } catch (e) {
      debugPrint('Error extracting additional_users: $e');
      return [];
    }
  }
}
