// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'application_message_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

ApplicationMessageModel _$ApplicationMessageModelFromJson(
  Map<String, dynamic> json,
) {
  return _ApplicationMessageModel.fromJson(json);
}

/// @nodoc
mixin _$ApplicationMessageModel {
  String get id => throw _privateConstructorUsedError;
  String get applicationId => throw _privateConstructorUsedError;
  String get senderId => throw _privateConstructorUsedError;
  String? get recipientId => throw _privateConstructorUsedError;
  String? get recipientGroup => throw _privateConstructorUsedError;
  String get messageContent => throw _privateConstructorUsedError;
  String? get attachmentUrl => throw _privateConstructorUsedError;
  DateTime get created => throw _privateConstructorUsedError; // Expanded fields
  String? get senderName => throw _privateConstructorUsedError;
  String? get senderUserType => throw _privateConstructorUsedError;

  /// Serializes this ApplicationMessageModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ApplicationMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ApplicationMessageModelCopyWith<ApplicationMessageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ApplicationMessageModelCopyWith<$Res> {
  factory $ApplicationMessageModelCopyWith(
    ApplicationMessageModel value,
    $Res Function(ApplicationMessageModel) then,
  ) = _$ApplicationMessageModelCopyWithImpl<$Res, ApplicationMessageModel>;
  @useResult
  $Res call({
    String id,
    String applicationId,
    String senderId,
    String? recipientId,
    String? recipientGroup,
    String messageContent,
    String? attachmentUrl,
    DateTime created,
    String? senderName,
    String? senderUserType,
  });
}

/// @nodoc
class _$ApplicationMessageModelCopyWithImpl<
  $Res,
  $Val extends ApplicationMessageModel
>
    implements $ApplicationMessageModelCopyWith<$Res> {
  _$ApplicationMessageModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ApplicationMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? applicationId = null,
    Object? senderId = null,
    Object? recipientId = freezed,
    Object? recipientGroup = freezed,
    Object? messageContent = null,
    Object? attachmentUrl = freezed,
    Object? created = null,
    Object? senderName = freezed,
    Object? senderUserType = freezed,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as String,
            applicationId:
                null == applicationId
                    ? _value.applicationId
                    : applicationId // ignore: cast_nullable_to_non_nullable
                        as String,
            senderId:
                null == senderId
                    ? _value.senderId
                    : senderId // ignore: cast_nullable_to_non_nullable
                        as String,
            recipientId:
                freezed == recipientId
                    ? _value.recipientId
                    : recipientId // ignore: cast_nullable_to_non_nullable
                        as String?,
            recipientGroup:
                freezed == recipientGroup
                    ? _value.recipientGroup
                    : recipientGroup // ignore: cast_nullable_to_non_nullable
                        as String?,
            messageContent:
                null == messageContent
                    ? _value.messageContent
                    : messageContent // ignore: cast_nullable_to_non_nullable
                        as String,
            attachmentUrl:
                freezed == attachmentUrl
                    ? _value.attachmentUrl
                    : attachmentUrl // ignore: cast_nullable_to_non_nullable
                        as String?,
            created:
                null == created
                    ? _value.created
                    : created // ignore: cast_nullable_to_non_nullable
                        as DateTime,
            senderName:
                freezed == senderName
                    ? _value.senderName
                    : senderName // ignore: cast_nullable_to_non_nullable
                        as String?,
            senderUserType:
                freezed == senderUserType
                    ? _value.senderUserType
                    : senderUserType // ignore: cast_nullable_to_non_nullable
                        as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ApplicationMessageModelImplCopyWith<$Res>
    implements $ApplicationMessageModelCopyWith<$Res> {
  factory _$$ApplicationMessageModelImplCopyWith(
    _$ApplicationMessageModelImpl value,
    $Res Function(_$ApplicationMessageModelImpl) then,
  ) = __$$ApplicationMessageModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String applicationId,
    String senderId,
    String? recipientId,
    String? recipientGroup,
    String messageContent,
    String? attachmentUrl,
    DateTime created,
    String? senderName,
    String? senderUserType,
  });
}

/// @nodoc
class __$$ApplicationMessageModelImplCopyWithImpl<$Res>
    extends
        _$ApplicationMessageModelCopyWithImpl<
          $Res,
          _$ApplicationMessageModelImpl
        >
    implements _$$ApplicationMessageModelImplCopyWith<$Res> {
  __$$ApplicationMessageModelImplCopyWithImpl(
    _$ApplicationMessageModelImpl _value,
    $Res Function(_$ApplicationMessageModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ApplicationMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? applicationId = null,
    Object? senderId = null,
    Object? recipientId = freezed,
    Object? recipientGroup = freezed,
    Object? messageContent = null,
    Object? attachmentUrl = freezed,
    Object? created = null,
    Object? senderName = freezed,
    Object? senderUserType = freezed,
  }) {
    return _then(
      _$ApplicationMessageModelImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as String,
        applicationId:
            null == applicationId
                ? _value.applicationId
                : applicationId // ignore: cast_nullable_to_non_nullable
                    as String,
        senderId:
            null == senderId
                ? _value.senderId
                : senderId // ignore: cast_nullable_to_non_nullable
                    as String,
        recipientId:
            freezed == recipientId
                ? _value.recipientId
                : recipientId // ignore: cast_nullable_to_non_nullable
                    as String?,
        recipientGroup:
            freezed == recipientGroup
                ? _value.recipientGroup
                : recipientGroup // ignore: cast_nullable_to_non_nullable
                    as String?,
        messageContent:
            null == messageContent
                ? _value.messageContent
                : messageContent // ignore: cast_nullable_to_non_nullable
                    as String,
        attachmentUrl:
            freezed == attachmentUrl
                ? _value.attachmentUrl
                : attachmentUrl // ignore: cast_nullable_to_non_nullable
                    as String?,
        created:
            null == created
                ? _value.created
                : created // ignore: cast_nullable_to_non_nullable
                    as DateTime,
        senderName:
            freezed == senderName
                ? _value.senderName
                : senderName // ignore: cast_nullable_to_non_nullable
                    as String?,
        senderUserType:
            freezed == senderUserType
                ? _value.senderUserType
                : senderUserType // ignore: cast_nullable_to_non_nullable
                    as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ApplicationMessageModelImpl implements _ApplicationMessageModel {
  const _$ApplicationMessageModelImpl({
    required this.id,
    required this.applicationId,
    required this.senderId,
    this.recipientId,
    this.recipientGroup,
    required this.messageContent,
    this.attachmentUrl,
    required this.created,
    this.senderName,
    this.senderUserType,
  });

  factory _$ApplicationMessageModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ApplicationMessageModelImplFromJson(json);

  @override
  final String id;
  @override
  final String applicationId;
  @override
  final String senderId;
  @override
  final String? recipientId;
  @override
  final String? recipientGroup;
  @override
  final String messageContent;
  @override
  final String? attachmentUrl;
  @override
  final DateTime created;
  // Expanded fields
  @override
  final String? senderName;
  @override
  final String? senderUserType;

  @override
  String toString() {
    return 'ApplicationMessageModel(id: $id, applicationId: $applicationId, senderId: $senderId, recipientId: $recipientId, recipientGroup: $recipientGroup, messageContent: $messageContent, attachmentUrl: $attachmentUrl, created: $created, senderName: $senderName, senderUserType: $senderUserType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApplicationMessageModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.applicationId, applicationId) ||
                other.applicationId == applicationId) &&
            (identical(other.senderId, senderId) ||
                other.senderId == senderId) &&
            (identical(other.recipientId, recipientId) ||
                other.recipientId == recipientId) &&
            (identical(other.recipientGroup, recipientGroup) ||
                other.recipientGroup == recipientGroup) &&
            (identical(other.messageContent, messageContent) ||
                other.messageContent == messageContent) &&
            (identical(other.attachmentUrl, attachmentUrl) ||
                other.attachmentUrl == attachmentUrl) &&
            (identical(other.created, created) || other.created == created) &&
            (identical(other.senderName, senderName) ||
                other.senderName == senderName) &&
            (identical(other.senderUserType, senderUserType) ||
                other.senderUserType == senderUserType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    applicationId,
    senderId,
    recipientId,
    recipientGroup,
    messageContent,
    attachmentUrl,
    created,
    senderName,
    senderUserType,
  );

  /// Create a copy of ApplicationMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApplicationMessageModelImplCopyWith<_$ApplicationMessageModelImpl>
  get copyWith => __$$ApplicationMessageModelImplCopyWithImpl<
    _$ApplicationMessageModelImpl
  >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ApplicationMessageModelImplToJson(this);
  }
}

abstract class _ApplicationMessageModel implements ApplicationMessageModel {
  const factory _ApplicationMessageModel({
    required final String id,
    required final String applicationId,
    required final String senderId,
    final String? recipientId,
    final String? recipientGroup,
    required final String messageContent,
    final String? attachmentUrl,
    required final DateTime created,
    final String? senderName,
    final String? senderUserType,
  }) = _$ApplicationMessageModelImpl;

  factory _ApplicationMessageModel.fromJson(Map<String, dynamic> json) =
      _$ApplicationMessageModelImpl.fromJson;

  @override
  String get id;
  @override
  String get applicationId;
  @override
  String get senderId;
  @override
  String? get recipientId;
  @override
  String? get recipientGroup;
  @override
  String get messageContent;
  @override
  String? get attachmentUrl;
  @override
  DateTime get created; // Expanded fields
  @override
  String? get senderName;
  @override
  String? get senderUserType;

  /// Create a copy of ApplicationMessageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApplicationMessageModelImplCopyWith<_$ApplicationMessageModelImpl>
  get copyWith => throw _privateConstructorUsedError;
}
