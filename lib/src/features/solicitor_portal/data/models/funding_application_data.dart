// lib/src/features/solicitor_portal/data/models/funding_application_data.dart

import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/barrister.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/expert.dart';

class FundingApplicationData {
  final String? id; // For updating drafts
  final List<String>?
  solicitorProfileId; // ID of the solicitor profile (as a relation field)
  final String? claimTitle;
  final double? minimumValueClaim;
  final double? requiredFundingAmount;
  final String? claimantType;
  final String? claimIndustry;
  final String? claimType;
  final String? stageOfClaim; // Corresponds to funding_applications.stage

  // Step 2
  final bool? conditionalFeeAgreementConfirmed;
  final int? legalOpinionSuccessProspects;
  final List<UploadedDocumentCategory>? uploadedDocuments;

  // Step 3 - Four Pillars (placeholders, assuming boolean or string responses)
  final String? pillarClaimantSolicitorOk;
  final String? pillarLeadingCounselOk;
  final String? pillarDefendantOk;
  final String? pillarEnforcementOk;

  // Step 4
  final bool? finalDeclarationConfirmed;

  final String applicationStatus; // e.g., 'draft', 'submitted'
  final String? submissionDate; // ISO 8601 format
  final String? reviewNotes;
  final String? decisionDate; // ISO 8601 format
  final String? lastUpdated; // ISO 8601 format, from record.updated

  // Barristers and experts
  final List<String>? barristers; // IDs of assigned barristers
  final List<String>? experts; // IDs of assigned experts
  final List<Barrister>?
  barristersData; // Full barrister objects (for UI display)
  final List<Expert>? expertsData; // Full expert objects (for UI display)

  FundingApplicationData({
    this.id,
    this.solicitorProfileId,
    this.claimTitle,
    this.minimumValueClaim,
    this.requiredFundingAmount,
    this.claimantType,
    this.claimIndustry,
    this.claimType,
    this.stageOfClaim,
    this.conditionalFeeAgreementConfirmed,
    this.legalOpinionSuccessProspects,
    this.uploadedDocuments,
    this.pillarClaimantSolicitorOk,
    this.pillarLeadingCounselOk,
    this.pillarDefendantOk,
    this.pillarEnforcementOk,
    this.finalDeclarationConfirmed,
    this.applicationStatus = 'draft',
    this.submissionDate,
    this.reviewNotes,
    this.decisionDate,
    this.lastUpdated,
    this.barristers,
    this.experts,
    this.barristersData,
    this.expertsData,
  });

  FundingApplicationData copyWith({
    String? id,
    List<String>? solicitorProfileId,
    String? claimTitle,
    double? minimumValueClaim,
    double? requiredFundingAmount,
    String? claimantType,
    String? claimIndustry,
    String? claimType,
    String? stageOfClaim,
    bool? conditionalFeeAgreementConfirmed,
    int? legalOpinionSuccessProspects,
    List<UploadedDocumentCategory>? uploadedDocuments,
    String? pillarClaimantSolicitorOk,
    String? pillarLeadingCounselOk,
    String? pillarDefendantOk,
    String? pillarEnforcementOk,
    bool? finalDeclarationConfirmed,
    String? applicationStatus,
    String? submissionDate,
    String? reviewNotes,
    String? decisionDate,
    String? lastUpdated,
    List<String>? barristers,
    List<String>? experts,
    List<Barrister>? barristersData,
    List<Expert>? expertsData,
  }) {
    return FundingApplicationData(
      id: id ?? this.id,
      solicitorProfileId: solicitorProfileId ?? this.solicitorProfileId,
      claimTitle: claimTitle ?? this.claimTitle,
      minimumValueClaim: minimumValueClaim ?? this.minimumValueClaim,
      requiredFundingAmount:
          requiredFundingAmount ?? this.requiredFundingAmount,
      claimantType: claimantType ?? this.claimantType,
      claimIndustry: claimIndustry ?? this.claimIndustry,
      claimType: claimType ?? this.claimType,
      stageOfClaim: stageOfClaim ?? this.stageOfClaim,
      conditionalFeeAgreementConfirmed:
          conditionalFeeAgreementConfirmed ??
          this.conditionalFeeAgreementConfirmed,
      legalOpinionSuccessProspects:
          legalOpinionSuccessProspects ?? this.legalOpinionSuccessProspects,
      uploadedDocuments: uploadedDocuments ?? this.uploadedDocuments,
      pillarClaimantSolicitorOk:
          pillarClaimantSolicitorOk ?? this.pillarClaimantSolicitorOk,
      pillarLeadingCounselOk:
          pillarLeadingCounselOk ?? this.pillarLeadingCounselOk,
      pillarDefendantOk: pillarDefendantOk ?? this.pillarDefendantOk,
      pillarEnforcementOk: pillarEnforcementOk ?? this.pillarEnforcementOk,
      finalDeclarationConfirmed:
          finalDeclarationConfirmed ?? this.finalDeclarationConfirmed,
      applicationStatus: applicationStatus ?? this.applicationStatus,
      submissionDate: submissionDate ?? this.submissionDate,
      reviewNotes: reviewNotes ?? this.reviewNotes,
      decisionDate: decisionDate ?? this.decisionDate,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      barristers: barristers ?? this.barristers,
      experts: experts ?? this.experts,
      barristersData: barristersData ?? this.barristersData,
      expertsData: expertsData ?? this.expertsData,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    // Only include non-null fields to avoid overwriting existing data in PocketBase with nulls
    // when saving a partial draft, unless the field is explicitly being cleared.
    // For 'Save Draft', we send what we have.
    if (solicitorProfileId != null && solicitorProfileId!.isNotEmpty) {
      data['solicitor_profile_id'] = solicitorProfileId;
    }
    if (claimTitle != null) {
      data['claim_title'] = claimTitle;
    }
    if (minimumValueClaim != null) {
      data['minimum_value_claim'] = minimumValueClaim;
    }
    if (requiredFundingAmount != null) {
      data['required_funding_amount'] = requiredFundingAmount;
    }
    if (claimantType != null) {
      data['claimant_type'] = claimantType;
    }
    if (claimIndustry != null) {
      data['claim_industry'] = claimIndustry;
    }
    if (claimType != null) {
      data['claim_type'] = claimType;
    }
    if (stageOfClaim != null) {
      data['stage'] = stageOfClaim; // PocketBase field is 'stage'
    }

    if (conditionalFeeAgreementConfirmed != null) {
      data['conditional_fee_agreement_confirmed'] =
          conditionalFeeAgreementConfirmed;
    }
    if (legalOpinionSuccessProspects != null) {
      data['legal_opinion_success_prospects'] = legalOpinionSuccessProspects;
    }

    if (uploadedDocuments != null) {
      data['uploaded_documents'] =
          uploadedDocuments!.map((v) => v.toJson()).toList();
    }

    if (pillarClaimantSolicitorOk != null) {
      data['pillar_claimant_solicitor_ok'] = pillarClaimantSolicitorOk;
    }
    if (pillarLeadingCounselOk != null) {
      data['pillar_leading_counsel_ok'] = pillarLeadingCounselOk;
    }
    if (pillarDefendantOk != null) {
      data['pillar_defendant_ok'] = pillarDefendantOk;
    }
    if (pillarEnforcementOk != null) {
      data['pillar_enforcement_ok'] = pillarEnforcementOk;
    }

    if (finalDeclarationConfirmed != null) {
      data['final_declaration_confirmed'] = finalDeclarationConfirmed;
    }

    data['application_status'] = applicationStatus;
    if (submissionDate != null) {
      data['submission_date'] = submissionDate;
    }
    if (reviewNotes != null) {
      data['review_notes'] = reviewNotes;
    }
    if (decisionDate != null) {
      data['decision_date'] = decisionDate;
    }

    // Add barristers and experts relation fields
    if (barristers != null && barristers!.isNotEmpty) {
      data['barristers'] = barristers;
    }

    if (experts != null && experts!.isNotEmpty) {
      data['experts'] = experts;
    }

    // lastUpdated is not typically sent in toJson as it's a record metadata from PocketBase
    // barristersData and expertsData are not sent to PocketBase as they're only for UI display
    return data;
  }

  /// Creates JSON data specifically for new record creation
  /// This ensures no ID field is included, which would cause PocketBase to reject the request
  /// Also removes created/updated fields as they are managed by PocketBase autodate fields
  Map<String, dynamic> toJsonForCreate() {
    final data = toJson();
    // Explicitly remove any id field that might have been included
    data.remove('id');
    // Also remove any potential variations of id field
    data.remove('ID');
    data.remove('Id');
    // Remove created/updated fields as they are managed by PocketBase autodate fields
    data.remove('created');
    data.remove('updated');
    // Remove any empty string values, null values, or whitespace-only strings that might cause validation issues
    data.removeWhere(
      (key, value) =>
          value == '' ||
          value == null ||
          (value is String && value.trim().isEmpty) ||
          key.toLowerCase() == 'id', // Extra safety for any id-related keys
    );
    return data;
  }

  // Helper method to parse a field that could be a String, List<dynamic>, or null
  // Returns a List<String> or null
  static List<String>? _parseStringList(dynamic value) {
    if (value == null) {
      return null;
    }

    if (value is String) {
      // If it's a single string, return a list with that string
      return [value];
    }

    if (value is List) {
      // If it's a list, convert all elements to strings
      return value.map((e) => e.toString()).toList();
    }

    // If it's neither a string nor a list, return null
    return null;
  }

  factory FundingApplicationData.fromJson(
    String id,
    Map<String, dynamic> json, {
    String? recordUpdatedTimestamp,
  }) {
    // Use our helper method to parse solicitorProfileId

    return FundingApplicationData(
      id: id,
      solicitorProfileId: _parseStringList(json['solicitor_profile_id']),
      claimTitle: json['claim_title'] as String?,
      minimumValueClaim: (json['minimum_value_claim'] as num?)?.toDouble(),
      requiredFundingAmount:
          (json['required_funding_amount'] as num?)?.toDouble(),
      claimantType: json['claimant_type'] as String?,
      claimIndustry: json['claim_industry'] as String?,
      claimType: json['claim_type'] as String?,
      stageOfClaim: json['stage'] as String?,
      conditionalFeeAgreementConfirmed:
          json['conditional_fee_agreement_confirmed'] as bool?,
      legalOpinionSuccessProspects:
          (json['legal_opinion_success_prospects'] as num?)?.toInt(),
      uploadedDocuments:
          json['uploaded_documents'] != null
              ? (json['uploaded_documents'] is List
                  ? (json['uploaded_documents'] as List<dynamic>)
                      .map((e) {
                        try {
                          return UploadedDocumentCategory.fromJson(
                            e as Map<String, dynamic>,
                          );
                        } catch (e) {
                          // If there's an error parsing, return null and filter it out
                          return null;
                        }
                      })
                      .where((e) => e != null)
                      .cast<UploadedDocumentCategory>()
                      .toList()
                  : null)
              : null,
      pillarClaimantSolicitorOk:
          json['pillar_claimant_solicitor_ok'] as String?,
      pillarLeadingCounselOk: json['pillar_leading_counsel_ok'] as String?,
      pillarDefendantOk: json['pillar_defendant_ok'] as String?,
      pillarEnforcementOk: json['pillar_enforcement_ok'] as String?,
      finalDeclarationConfirmed: json['final_declaration_confirmed'] as bool?,
      applicationStatus: json['application_status'] as String? ?? 'draft',
      submissionDate: json['submission_date'] as String?,
      reviewNotes: json['review_notes'] as String?,
      decisionDate: json['decision_date'] as String?,
      lastUpdated: recordUpdatedTimestamp, // Pass record.updated here
      barristers: _parseStringList(json['barristers']),
      experts: _parseStringList(json['experts']),
      // barristersData and expertsData are not populated here as they require separate API calls
    );
  }
}

class UploadedDocumentCategory {
  final String logicalName;
  final List<DocumentVersion> versions;
  final String currentVersionFileId;

  UploadedDocumentCategory({
    required this.logicalName,
    required this.versions,
    required this.currentVersionFileId,
  });

  Map<String, dynamic> toJson() {
    return {
      'logical_name': logicalName,
      'versions': versions.map((v) => v.toJson()).toList(),
      'current_version_file_id': currentVersionFileId,
    };
  }

  // Optional: fromJson factory for easier state management if needed later
  factory UploadedDocumentCategory.fromJson(Map<String, dynamic> json) {
    return UploadedDocumentCategory(
      logicalName: json['logical_name'] as String,
      versions:
          (json['versions'] as List<dynamic>)
              .map((v) => DocumentVersion.fromJson(v as Map<String, dynamic>))
              .toList(),
      currentVersionFileId: json['current_version_file_id'] as String,
    );
  }

  UploadedDocumentCategory copyWith({
    String? logicalName,
    List<DocumentVersion>? versions,
    String? currentVersionFileId,
    String? lastUpdatedAt, // Added for consistency if needed elsewhere
  }) {
    return UploadedDocumentCategory(
      logicalName: logicalName ?? this.logicalName,
      versions: versions ?? this.versions,
      currentVersionFileId: currentVersionFileId ?? this.currentVersionFileId,
      // lastUpdatedAt: lastUpdatedAt ?? this.lastUpdatedAt, // Uncomment if field is added
    );
  }
}

class DocumentVersion {
  final String fileId;
  final String filename;
  final String uploadedAt; // ISO 8601 format
  final String uploadedBy; // User ID
  final String? notes;

  DocumentVersion({
    required this.fileId,
    required this.filename,
    required this.uploadedAt,
    required this.uploadedBy,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    final map = {
      'file_id': fileId,
      'filename': filename,
      'uploaded_at': uploadedAt,
      'uploaded_by': uploadedBy,
    };
    if (notes != null) {
      map['notes'] = notes!;
    }
    return map;
  }

  // Optional: fromJson factory
  factory DocumentVersion.fromJson(Map<String, dynamic> json) {
    return DocumentVersion(
      fileId: json['file_id'] as String,
      filename: json['filename'] as String,
      uploadedAt: json['uploaded_at'] as String,
      uploadedBy: json['uploaded_by'] as String,
      notes: json['notes'] as String?,
    );
  }

  DocumentVersion copyWith({
    String? fileId,
    String? filename,
    String? uploadedAt,
    String? uploadedBy,
    String? notes,
  }) {
    return DocumentVersion(
      fileId: fileId ?? this.fileId,
      filename: filename ?? this.filename,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      notes: notes ?? this.notes,
    );
  }
}
