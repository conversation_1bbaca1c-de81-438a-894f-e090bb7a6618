// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'claim_message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ClaimMessageModelImpl _$$ClaimMessageModelImplFromJson(
  Map<String, dynamic> json,
) => _$ClaimMessageModelImpl(
  id: json['id'] as String,
  claimId: json['claimId'] as String,
  senderId: json['senderId'] as String,
  recipientId: json['recipientId'] as String?,
  recipientGroup: json['recipientGroup'] as String?,
  messageContent: json['messageContent'] as String,
  attachmentUrl: json['attachmentUrl'] as String?,
  created: DateTime.parse(json['created'] as String),
  senderName: json['senderName'] as String?,
  senderUserType: json['senderUserType'] as String?,
);

Map<String, dynamic> _$$ClaimMessageModelImplToJson(
  _$ClaimMessageModelImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'claimId': instance.claimId,
  'senderId': instance.senderId,
  'recipientId': instance.recipientId,
  'recipientGroup': instance.recipientGroup,
  'messageContent': instance.messageContent,
  'attachmentUrl': instance.attachmentUrl,
  'created': instance.created.toIso8601String(),
  'senderName': instance.senderName,
  'senderUserType': instance.senderUserType,
};
