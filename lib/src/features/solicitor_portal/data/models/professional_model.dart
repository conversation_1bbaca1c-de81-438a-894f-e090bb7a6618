// Helper method to parse relation fields that can be either a single string or a list
List<String> _parseRelationField(dynamic field) {
  if (field == null) return [];
  if (field is List) {
    return field.map((item) => item.toString()).toList();
  } else if (field is String) {
    return field.isEmpty ? [] : [field];
  }
  return [];
}

// Common Professional Model
class ProfessionalModel {
  final String id;
  final String name;
  final String? chambers; // Specific to Barristers
  final String? specialty; // Specific to Experts
  final String? email;
  final String? firmName; // Specific to Experts
  final DateTime? created;
  final DateTime? updated;

  ProfessionalModel({
    required this.id,
    required this.name,
    this.chambers,
    this.specialty,
    this.email,
    this.firmName,
    this.created,
    this.updated,
  });

  factory ProfessionalModel.fromJson(Map<String, dynamic> json) {
    // Attempt to intelligently parse based on typical fields from 'barristers' or 'experts' collections
    return ProfessionalModel(
      id: json['id'] as String,
      name:
          json['name'] as String? ??
          json['barrister_with_conduct'] as String? ??
          'N/A',
      chambers: json['barrister_chambers'] as String?,
      specialty:
          json['type'] as String? ??
          json['specialty']
              as String?, // 'type' is often used for expert's specialty
      email: json['email'] as String?,
      firmName: json['firm_name'] as String?,
      created:
          json['created'] != null
              ? DateTime.tryParse(json['created'] as String)
              : null,
      updated:
          json['updated'] != null
              ? DateTime.tryParse(json['updated'] as String)
              : null,
    );
  }

  // Helper to get a display subtitle, useful for UI
  String get displaySubtitle {
    if (chambers != null && chambers!.isNotEmpty) return chambers!;
    if (specialty != null && specialty!.isNotEmpty) return specialty!;
    if (firmName != null && firmName!.isNotEmpty) return firmName!;
    return 'N/A';
  }
}

class Barrister {
  final String id;
  final String name; // from 'barrister_with_conduct'
  final String? chambers; // from 'barrister_chambers'
  final String? email;
  final String?
  specialty; // Not directly in PB schema, but good for UI. Could be derived or added.
  final List<String> claimIds; // Relation to claims
  final DateTime created;
  final DateTime updated;

  Barrister({
    required this.id,
    required this.name,
    this.chambers,
    this.email,
    this.specialty,
    required this.claimIds,
    required this.created,
    required this.updated,
  });

  factory Barrister.fromJson(Map<String, dynamic> record) {
    // Renamed from fromPocketBase
    return Barrister(
      id: record['id'] as String,
      name: record['barrister_with_conduct'] as String? ?? 'N/A',
      chambers: record['barrister_chambers'] as String?,
      email: record['email'] as String?,
      // specialty: record['specialty'] as String?, // If added to PB
      claimIds: _parseRelationField(record['claims']),
      created:
          DateTime.tryParse(record['created'] as String? ?? '') ??
          DateTime.now(),
      updated:
          DateTime.tryParse(record['updated'] as String? ?? '') ??
          DateTime.now(),
    );
  }

  Map<String, dynamic> toJsonForCreate() {
    return {
      'barrister_with_conduct': name,
      'barrister_chambers': chambers,
      'email': email,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
      // 'specialty': specialty, // If added to PB
      // 'claims': claimIds, // Usually linking is done by updating the other side or a join table
    };
  }

  Map<String, dynamic> toJsonForUpdate() {
    return {
      'barrister_with_conduct': name,
      'barrister_chambers': chambers,
      'email': email,
      'updated':
          DateTime.now()
              .toIso8601String(), // Always update the updated timestamp
      // 'specialty': specialty,
    };
  }
}

class Expert {
  final String id;
  final String name;
  final String? firmName;
  final String? email;
  final String? specialty; // from 'type' in PB
  final List<String> claimIds; // Relation to claims
  final DateTime created;
  final DateTime updated;

  Expert({
    required this.id,
    required this.name,
    this.firmName,
    this.email,
    this.specialty,
    required this.claimIds,
    required this.created,
    required this.updated,
  });

  factory Expert.fromJson(Map<String, dynamic> record) {
    // Renamed from fromPocketBase
    return Expert(
      id: record['id'] as String,
      name: record['name'] as String? ?? 'N/A',
      firmName: record['firm_name'] as String?,
      email: record['email'] as String?,
      specialty: record['type'] as String?, // Maps from 'type' field in PB
      claimIds: _parseRelationField(record['claims']),
      created:
          DateTime.tryParse(record['created'] as String? ?? '') ??
          DateTime.now(),
      updated:
          DateTime.tryParse(record['updated'] as String? ?? '') ??
          DateTime.now(),
    );
  }

  Map<String, dynamic> toJsonForCreate() {
    return {
      'name': name,
      'firm_name': firmName,
      'email': email,
      'type': specialty, // Maps to 'type' field in PB
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  Map<String, dynamic> toJsonForUpdate() {
    return {
      'name': name,
      'firm_name': firmName,
      'email': email,
      'type': specialty,
      'updated':
          DateTime.now()
              .toIso8601String(), // Always update the updated timestamp
    };
  }
}
