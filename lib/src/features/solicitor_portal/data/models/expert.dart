// lib/src/features/solicitor_portal/data/models/expert.dart

class Expert {
  final String? id;
  final String name;
  final String firmName;
  final String email;
  final String type; // legal, financial, engineer, construction, other
  final List<String>? claims;
  final String? phone;
  final double? hourlyRate;
  final Map<String, dynamic>? reports;
  final Map<String, dynamic>? costEstimates;
  final String? notes;
  final double? performanceRating;
  final String? lastEngagementDate;

  Expert({
    this.id,
    required this.name,
    required this.firmName,
    required this.email,
    required this.type,
    this.claims,
    this.phone,
    this.hourlyRate,
    this.reports,
    this.costEstimates,
    this.notes,
    this.performanceRating,
    this.lastEngagementDate,
  });

  Expert copyWith({
    String? id,
    String? name,
    String? firmName,
    String? email,
    String? type,
    List<String>? claims,
    String? phone,
    double? hourlyRate,
    Map<String, dynamic>? reports,
    Map<String, dynamic>? costEstimates,
    String? notes,
    double? performanceRating,
    String? lastEngagementDate,
  }) {
    return Expert(
      id: id ?? this.id,
      name: name ?? this.name,
      firmName: firmName ?? this.firmName,
      email: email ?? this.email,
      type: type ?? this.type,
      claims: claims ?? this.claims,
      phone: phone ?? this.phone,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      reports: reports ?? this.reports,
      costEstimates: costEstimates ?? this.costEstimates,
      notes: notes ?? this.notes,
      performanceRating: performanceRating ?? this.performanceRating,
      lastEngagementDate: lastEngagementDate ?? this.lastEngagementDate,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    
    // DO NOT include 'id' in the JSON for create operations.
    // PocketBase generates it. For updates, it's in the URL.
    // if (id != null) {
    //   data['id'] = id;
    // }
    
    data['name'] = name;
    data['firm_name'] = firmName;
    data['email'] = email;
    data['type'] = type;
    
    if (claims != null && claims!.isNotEmpty) {
      data['claims'] = claims;
    }
    
    if (phone != null) {
      data['phone'] = phone;
    }
    
    if (hourlyRate != null) {
      data['hourly_rate'] = hourlyRate;
    }
    
    if (reports != null) {
      data['reports'] = reports;
    }
    
    if (costEstimates != null) {
      data['cost_estimates'] = costEstimates;
    }
    
    if (notes != null) {
      data['notes'] = notes;
    }
    
    if (performanceRating != null) {
      data['performance_rating'] = performanceRating;
    }
    
    if (lastEngagementDate != null) {
      data['last_engagement_date'] = lastEngagementDate;
    }
    
    return data;
  }

  factory Expert.fromJson(Map<String, dynamic> json) {
    return Expert(
      id: json['id'] as String?,
      name: json['name'] as String,
      firmName: json['firm_name'] as String,
      email: json['email'] as String,
      type: json['type'] as String,
      claims: (json['claims'] as List<dynamic>?)?.map((e) => e as String).toList(),
      phone: json['phone'] as String?,
      hourlyRate: (json['hourly_rate'] as num?)?.toDouble(),
      reports: json['reports'] as Map<String, dynamic>?,
      costEstimates: json['cost_estimates'] as Map<String, dynamic>?,
      notes: json['notes'] as String?,
      performanceRating: (json['performance_rating'] as num?)?.toDouble(),
      lastEngagementDate: json['last_engagement_date'] as String?,
    );
  }

  @override
  String toString() {
    return name;
  }
}
