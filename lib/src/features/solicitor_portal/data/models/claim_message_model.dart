import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

part 'claim_message_model.freezed.dart';
part 'claim_message_model.g.dart';

@freezed
class ClaimMessageModel with _$ClaimMessageModel {
  const factory ClaimMessageModel({
    required String id,
    required String claimId, // Changed from applicationId
    required String senderId,
    String? recipientId,
    String? recipientGroup,
    required String messageContent,
    String? attachmentUrl,
    required DateTime created,
    // Expanded fields
    String? senderName,
    String? senderUserType,
  }) = _ClaimMessageModel;

  factory ClaimMessageModel.fromJson(Map<String, dynamic> json) =>
      _$ClaimMessageModelFromJson(json);

  factory ClaimMessageModel.fromRecord(RecordModel record) {
    final data = record.toJson();
    String? attachment;
    if (data['attachment'] != null &&
        data['attachment'].toString().isNotEmpty) {
      try {
        attachment =
            PocketBaseService().pb.files
                .getURL(record, data['attachment'] as String)
                .toString();
      } catch (e) {
        // Log error but don't fail the entire message parsing
        LoggerService.error('Error constructing attachment URL', e);
        attachment = null;
      }
    }

    String? sName;
    String? sUserType;

    if (data['expand'] != null && data['expand']['sender_id'] != null) {
      final senderData = data['expand']['sender_id'] as Map<String, dynamic>;
      sName = senderData['name'] as String? ?? 'Unknown Sender';
      sUserType = senderData['user_type'] as String? ?? 'unknown';
    }

    return ClaimMessageModel(
      id: record.id,
      claimId: data['claim_id'] as String, // Changed from application_id
      senderId: data['sender_id'] as String,
      recipientId: data['recipient_id'] as String?,
      recipientGroup: data['recipient_group'] as String?,
      messageContent: data['message_content'] as String,
      attachmentUrl: attachment,
      created: DateTime.parse(data['created'] as String),
      senderName: sName,
      senderUserType: sUserType,
    );
  }
}
