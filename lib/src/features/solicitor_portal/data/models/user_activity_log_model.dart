import 'package:three_pay_group_litigation_platform/src/features/auth/data/models/user_model.dart'
    as auth_user;

class UserActivityLog {
  final String id;
  final DateTime created;
  final String action;
  final Map<String, dynamic> details;
  final String userId;
  final auth_user.User? user; // Expanded user details

  UserActivityLog({
    required this.id,
    required this.created,
    required this.action,
    required this.details,
    required this.userId,
    this.user,
  });

  factory UserActivityLog.fromJson(Map<String, dynamic> json) {
    auth_user.User? expandedUser;
    if (json['expand'] != null && json['expand']['user'] != null) {
      expandedUser = auth_user.User.fromJson(
        json['expand']['user'] as Map<String, dynamic>,
      );
    } else if (json['user'] != null && json['user'] is Map<String, dynamic>) {
      // Fallback if user is directly nested and not in expand (less common for PB queries)
      expandedUser = auth_user.User.fromJson(
        json['user'] as Map<String, dynamic>,
      );
    }

    return UserActivityLog(
      id: json['id'] as String,
      created:
          DateTime.tryParse(json['created'] as String? ?? '') ?? DateTime.now(),
      action: json['action'] as String,
      details:
          json['details'] is String
              ? {
                'message': json['details'],
              } // Handle if details is a simple string
              : Map<String, dynamic>.from(json['details'] as Map? ?? {}),
      userId:
          json['user_id'] as String? ??
          json['user'] as String? ??
          '', // user_id or user field
      user: expandedUser,
    );
  }

  String get detailsSummary {
    if (details.containsKey('message')) {
      return details['message'].toString();
    }
    if (details.containsKey('status_change')) {
      return 'Status changed: ${details['status_change']['from']} -> ${details['status_change']['to']}';
    }
    if (details.containsKey('document_uploaded')) {
      return 'Document uploaded: ${details['document_uploaded']['filename']}';
    }
    if (details.containsKey('field_updated')) {
      return 'Field updated: ${details['field_updated']['field_name']}';
    }
    // Add more specific summaries as needed
    return details.entries.map((e) => '${e.key}: ${e.value}').join(', ');
  }

  String get userNameOrEmail {
    if (user != null) {
      if (user!.name != null && user!.name!.isNotEmpty) {
        return user!.name!;
      }
      if (user!.email != null && user!.email!.isNotEmpty) {
        return user!.email!;
      }
    }
    return 'System/Unknown User';
  }
}
