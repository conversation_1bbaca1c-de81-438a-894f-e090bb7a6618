import 'package:pocketbase/pocketbase.dart';
import 'package:http/http.dart' as http;
import 'package:three_pay_group_litigation_platform/src/core/services/enhanced_notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/solicitor_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/data/models/enhanced_notification_model.dart';

/// Base service class for solicitor portal operations
/// Provides common functionality and follows existing codebase patterns
class SolicitorBaseService {
  final PocketBase _pb;
  final PocketBaseService _pocketBaseService;
  late final EnhancedNotificationService _notificationService;

  SolicitorBaseService()
    : _pb = PocketBaseService().pb,
      _pocketBaseService = PocketBaseService() {
    _notificationService = EnhancedNotificationService(_pocketBaseService);
  }

  /// Get the current authenticated user
  RecordModel? get currentUser => _pb.authStore.record;

  /// Check if user is authenticated
  bool get isAuthenticated => _pb.authStore.isValid;

  /// Check if current user is a solicitor
  bool get isSolicitor {
    final user = currentUser;
    return user != null && user.data['user_type'] == 'solicitor';
  }

  /// Get current user ID
  String? get currentUserId => currentUser?.id;

  /// Verify authentication and solicitor role
  void _verifySolicitorAuth() {
    if (!isAuthenticated) {
      throw Exception('User not authenticated');
    }
    if (!isSolicitor) {
      throw Exception('User is not a solicitor');
    }
  }

  /// Get solicitor profile for current user
  Future<SolicitorProfileModel?> getCurrentSolicitorProfile() async {
    try {
      _verifySolicitorAuth();

      final userId = currentUserId!;
      final records = await _pb
          .collection('solicitor_profiles')
          .getList(filter: 'user_id = "$userId"', expand: 'user_id');

      if (records.items.isEmpty) {
        LoggerService.info('No solicitor profile found for user: $userId');
        return null;
      }

      return SolicitorProfileModel.fromJson(records.items.first.toJson());
    } catch (e) {
      LoggerService.error('Error getting solicitor profile', e);
      rethrow;
    }
  }

  /// Create solicitor profile for current user
  Future<SolicitorProfileModel> createSolicitorProfile({
    required String lawFirmName,
    required String solicitorName,
    required String position,
    required String contactNumber,
    required String firmAddress,
    required String firmRegistrationNumber,
    Map<String, dynamic>? notificationPreferences,
  }) async {
    try {
      _verifySolicitorAuth();

      final userId = currentUserId!;

      final profileData = {
        'user_id': userId,
        'solicitor_name': solicitorName,
        'law_firm_name': lawFirmName,
        'sra_number': firmRegistrationNumber,
        'firm_address': firmAddress,
        'contact_number': contactNumber,
        'position': position,
        'pu_status': 'pending',
        'additional_users': <String>[],
      };

      final record = await _pb
          .collection('solicitor_profiles')
          .create(body: profileData, expand: 'user_id');

      LoggerService.info('Created solicitor profile for user: $userId');
      return SolicitorProfileModel.fromJson(record.toJson());
    } catch (e) {
      LoggerService.error('Error creating solicitor profile', e);
      rethrow;
    }
  }

  /// Update solicitor profile
  Future<SolicitorProfileModel> updateSolicitorProfile({
    String? solicitorName,
    String? lawFirmName,
    String? sraNumber,
    String? firmAddress,
    String? contactNumber,
    String? positionInFirm,
    Map<String, dynamic>? notificationPreferences,
  }) async {
    try {
      _verifySolicitorAuth();

      final profile = await getCurrentSolicitorProfile();
      if (profile == null) {
        throw Exception('Solicitor profile not found');
      }

      final updateData = <String, dynamic>{};
      if (solicitorName != null) updateData['solicitor_name'] = solicitorName;
      if (lawFirmName != null) updateData['law_firm_name'] = lawFirmName;
      if (sraNumber != null) updateData['sra_number'] = sraNumber;
      if (firmAddress != null) updateData['firm_address'] = firmAddress;
      if (contactNumber != null) updateData['contact_number'] = contactNumber;
      if (positionInFirm != null) updateData['position'] = positionInFirm;

      final record = await _pb
          .collection('solicitor_profiles')
          .update(profile.id, body: updateData, expand: 'user_id');

      LoggerService.info(
        'Updated solicitor profile for user: $currentUserId',
      );
      return SolicitorProfileModel.fromJson(record.toJson());
    } catch (e) {
      LoggerService.error('Error updating solicitor profile', e);
      rethrow;
    }
  }

  /// Upload profile picture
  Future<void> uploadProfilePicture(
    List<int> fileBytes,
    String fileName,
  ) async {
    try {
      _verifySolicitorAuth();

      final userId = currentUserId!;

      final multipartFile = http.MultipartFile.fromBytes(
        'avatar',
        fileBytes,
        filename: fileName,
      );

      await _pb.collection('users').update(userId, files: [multipartFile]);

      LoggerService.info('Uploaded profile picture for user: $userId');
    } catch (e) {
      LoggerService.error('Error uploading profile picture', e);
      rethrow;
    }
  }

  /// Get profile picture URL
  String? getProfilePictureUrl() {
    try {
      final user = currentUser;
      if (user == null) return null;

      final avatarField = user.getStringValue('avatar');
      if (avatarField.isEmpty) return null;

      return _pb.files.getURL(user, avatarField).toString();
    } catch (e) {
      LoggerService.error('Error getting profile picture URL', e);
      return null;
    }
  }

  /// Get profile picture URL from user record
  String? getProfilePictureUrlFromRecord(RecordModel userRecord) {
    try {
      final avatarField = userRecord.getStringValue('avatar');
      if (avatarField.isEmpty) return null;

      return _pb.files.getURL(userRecord, avatarField).toString();
    } catch (e) {
      LoggerService.error('Error getting profile picture URL from record', e);
      return null;
    }
  }

  /// Get user record by ID (for URL generation)
  Future<RecordModel> getUserRecord(String userId) async {
    try {
      return await _pb.collection('users').getOne(userId);
    } catch (e) {
      LoggerService.error('Error getting user record', e);
      rethrow;
    }
  }

  /// Get notifications for current solicitor
  Future<List<EnhancedNotificationModel>> getSolicitorNotifications({
    int page = 1,
    int perPage = 20,
    bool unreadOnly = false,
  }) async {
    try {
      _verifySolicitorAuth();

      await _notificationService.initialize();
      final allNotifications = _notificationService.notifications.value;

      LoggerService.info(
        'Enhanced notification service has ${allNotifications.length} total notifications',
      );

      // Filter notifications for solicitor if needed
      var filteredNotifications = allNotifications;
      if (unreadOnly) {
        filteredNotifications =
            allNotifications.where((n) => !n.isRead).toList();
      }

      // Apply pagination
      final startIndex = (page - 1) * perPage;
      final endIndex = startIndex + perPage;

      if (startIndex >= filteredNotifications.length) {
        return [];
      }

      final paginatedNotifications = filteredNotifications.sublist(
        startIndex,
        endIndex > filteredNotifications.length
            ? filteredNotifications.length
            : endIndex,
      );

      LoggerService.info(
        'Returning ${paginatedNotifications.length} notifications for solicitor (page $page)',
      );

      return paginatedNotifications;
    } catch (e) {
      LoggerService.error('Error getting solicitor notifications', e);
      rethrow;
    }
  }

  /// Get error message from exception
  String getErrorMessage(dynamic error) {
    if (error is ClientException) {
      final response = error.response;
      if (response.containsKey('message')) {
        return response['message'] as String;
      }
      if (response.containsKey('data')) {
        final data = response['data'] as Map<String, dynamic>;
        if (data.isNotEmpty) {
          final firstError = data.values.first;
          if (firstError is Map && firstError.containsKey('message')) {
            return firstError['message'] as String;
          }
        }
      }
    }
    return error.toString();
  }

  /// Dispose resources
  void dispose() {
    // Clean up any resources if needed
  }
}
