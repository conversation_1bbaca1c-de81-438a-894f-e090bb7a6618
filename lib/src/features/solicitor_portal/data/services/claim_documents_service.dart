import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:http/http.dart' as http;
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/google_drive_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/document_cache_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/performance_monitoring_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/storage_type.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/google_drive_file.dart';
import 'package:three_pay_group_litigation_platform/src/core/utils/storage_utils.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart';

/// Service for managing claim documents with Google Drive integration
class ClaimDocumentsService {
  final PocketBase _pb;
  final GoogleDriveService _driveService;
  final DocumentCacheService _cacheService;
  final StorageConfiguration _storageConfig;
  final PerformanceMonitoringService _performanceService;

  ClaimDocumentsService({
    GoogleDriveService? driveService,
    DocumentCacheService? cacheService,
    StorageConfiguration? storageConfig,
  }) : _pb = PocketBaseService().pb,
       _driveService = driveService ?? GoogleDriveService(),
       _cacheService = cacheService ?? DocumentCacheService(),
       _storageConfig = storageConfig ?? StorageConfiguration.production(),
       _performanceService = PerformanceMonitoringService();

  /// Fetch all document categories for a funding application
  /// Uses only the claim_documents collection as the data source
  Future<List<UploadedDocumentCategory>> getDocumentsForFundingApplication(
    String fundingApplicationId,
  ) async {
    try {
      debugPrint(
        'ClaimDocumentsService: Fetching documents for funding application: $fundingApplicationId',
      );

      // Query the claim_documents collection
      // Note: funding_application_id is a relation field, but we query by direct value
      final records = await _pb
          .collection('claim_documents')
          .getFullList(
            filter: 'funding_application_id = "$fundingApplicationId"',
          );

      debugPrint(
        'ClaimDocumentsService: Found ${records.length} document records in claim_documents collection',
      );

      // Debug: Print raw record data for troubleshooting
      for (int i = 0; i < records.length; i++) {
        final record = records[i];
        debugPrint('ClaimDocumentsService: Record $i - ID: ${record.id}');
        debugPrint('ClaimDocumentsService: Record $i - Data: ${record.data}');
        debugPrint(
          'ClaimDocumentsService: Record $i - logical_name: ${record.data['logical_name']}',
        );
        debugPrint(
          'ClaimDocumentsService: Record $i - current_version_file_id: ${record.data['current_version_file_id']}',
        );
        debugPrint(
          'ClaimDocumentsService: Record $i - versions field type: ${record.data['versions'].runtimeType}',
        );
        debugPrint(
          'ClaimDocumentsService: Record $i - versions field value: ${record.data['versions']}',
        );
      }

      final categories = <UploadedDocumentCategory>[];

      for (final record in records) {
        try {
          final logicalName = record.data['logical_name'] as String?;
          final currentVersionFileId =
              record.data['current_version_file_id'] as String?;

          if (logicalName == null || currentVersionFileId == null) {
            debugPrint(
              'ClaimDocumentsService: Skipping record ${record.id} - missing required fields (logical_name: $logicalName, current_version_file_id: $currentVersionFileId)',
            );
            continue;
          }

          final versions = _parseVersions(record.data['versions']);
          debugPrint(
            'ClaimDocumentsService: Processing record ${record.id}, logical_name: $logicalName, parsed ${versions.length} versions',
          );

          final category = UploadedDocumentCategory(
            logicalName: logicalName,
            currentVersionFileId: currentVersionFileId,
            versions: versions,
          );

          categories.add(category);

          debugPrint(
            'ClaimDocumentsService: Successfully created category: $logicalName with ${versions.length} versions',
          );
        } catch (e) {
          debugPrint(
            'ClaimDocumentsService: Error processing record ${record.id}: $e',
          );
          // Continue with other records
        }
      }

      debugPrint(
        'ClaimDocumentsService: Returning ${categories.length} document categories from claim_documents collection',
      );

      return categories;
    } catch (e) {
      debugPrint(
        'ClaimDocumentsService: Error fetching documents from claim_documents collection: $e',
      );
      return [];
    }
  }

  /// Create a new document category
  Future<String> createDocumentCategory({
    required String fundingApplicationId,
    required String logicalName,
    required String currentVersionFileId,
    required List<DocumentVersion> versions,
    String? uploadedBy,
  }) async {
    final body = {
      'funding_application_id': fundingApplicationId,
      'logical_name': logicalName,
      'current_version_file_id': currentVersionFileId,
      'versions': versions.map((v) => v.toJson()).toList(),
      'name': logicalName, // For display purposes
    };

    if (uploadedBy != null) {
      body['uploaded_by'] = uploadedBy;
    }

    final record = await _pb.collection('claim_documents').create(body: body);
    return record.id;
  }

  /// Update an existing document category (add new version)
  Future<void> updateDocumentCategory({
    required String fundingApplicationId,
    required String logicalName,
    required String newCurrentVersionFileId,
    required List<DocumentVersion> updatedVersions,
  }) async {
    // Find the existing document category
    final existingRecords = await _pb
        .collection('claim_documents')
        .getFullList(
          filter:
              'funding_application_id = "$fundingApplicationId" && logical_name = "$logicalName"',
        );

    if (existingRecords.isEmpty) {
      throw Exception('Document category not found: $logicalName');
    }

    final record = existingRecords.first;
    await _pb
        .collection('claim_documents')
        .update(
          record.id,
          body: {
            'current_version_file_id': newCurrentVersionFileId,
            'versions': updatedVersions.map((v) => v.toJson()).toList(),
            'updated': DateTime.now().toIso8601String(),
          },
        );
  }

  /// Add a new version to an existing document category
  Future<void> addVersionToCategory({
    required String fundingApplicationId,
    required String logicalName,
    required DocumentVersion newVersion,
  }) async {
    // Find the existing document category
    final existingRecords = await _pb
        .collection('claim_documents')
        .getFullList(
          filter:
              'funding_application_id = "$fundingApplicationId" && logical_name = "$logicalName"',
        );

    if (existingRecords.isEmpty) {
      throw Exception('Document category not found: $logicalName');
    }

    final record = existingRecords.first;
    final existingVersions = _parseVersions(record.data['versions']);
    debugPrint(
      'ClaimDocumentsService: Found ${existingVersions.length} existing versions for category: $logicalName',
    );

    final updatedVersions = [...existingVersions, newVersion];
    debugPrint(
      'ClaimDocumentsService: Updated versions list now has ${updatedVersions.length} versions',
    );

    final versionsJson = updatedVersions.map((v) => v.toJson()).toList();
    debugPrint(
      'ClaimDocumentsService: Versions JSON to be saved: $versionsJson',
    );

    await _pb
        .collection('claim_documents')
        .update(
          record.id,
          body: {
            'current_version_file_id': newVersion.fileId,
            'versions': versionsJson,
            'updated': DateTime.now().toIso8601String(),
          },
        );

    debugPrint(
      'ClaimDocumentsService: Successfully updated category with new version',
    );
  }

  /// Check if a document category exists for a funding application
  Future<bool> categoryExists({
    required String fundingApplicationId,
    required String logicalName,
  }) async {
    try {
      final records = await _pb
          .collection('claim_documents')
          .getFullList(
            filter:
                'funding_application_id = "$fundingApplicationId" && logical_name = "$logicalName"',
          );
      return records.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking if category exists: $e');
      return false;
    }
  }

  /// Migrate document repository data from funding_applications to claim_documents
  Future<void> migrateDocumentRepository({
    required String fundingApplicationId,
    required List<UploadedDocumentCategory> documentRepository,
  }) async {
    try {
      for (final category in documentRepository) {
        // Check if this category already exists in claim_documents
        final exists = await categoryExists(
          fundingApplicationId: fundingApplicationId,
          logicalName: category.logicalName,
        );

        if (!exists) {
          await createDocumentCategory(
            fundingApplicationId: fundingApplicationId,
            logicalName: category.logicalName,
            currentVersionFileId: category.currentVersionFileId,
            versions: category.versions,
          );
        }
      }
    } catch (e) {
      debugPrint('Error migrating document repository: $e');
      rethrow;
    }
  }

  /// Parse versions from JSON data
  List<DocumentVersion> _parseVersions(dynamic versionsData) {
    debugPrint(
      'ClaimDocumentsService: _parseVersions called with data type: ${versionsData.runtimeType}',
    );
    debugPrint('ClaimDocumentsService: _parseVersions raw data: $versionsData');

    if (versionsData == null) {
      debugPrint(
        'ClaimDocumentsService: _parseVersions - versionsData is null, returning empty list',
      );
      return [];
    }

    try {
      if (versionsData is List) {
        debugPrint(
          'ClaimDocumentsService: _parseVersions - versionsData is List with ${versionsData.length} items',
        );

        final List<DocumentVersion> parsedVersions = [];

        for (int i = 0; i < versionsData.length; i++) {
          try {
            final versionData = versionsData[i];
            debugPrint(
              'ClaimDocumentsService: _parseVersions - parsing version $i: $versionData (type: ${versionData.runtimeType})',
            );

            if (versionData is Map<String, dynamic>) {
              final version = DocumentVersion.fromJson(versionData);
              parsedVersions.add(version);
              debugPrint(
                'ClaimDocumentsService: _parseVersions - successfully parsed version $i: ${version.toJson()}',
              );
            } else {
              debugPrint(
                'ClaimDocumentsService: _parseVersions - version $i is not a Map<String, dynamic>, skipping',
              );
            }
          } catch (e) {
            debugPrint(
              'ClaimDocumentsService: _parseVersions - error parsing version $i: $e',
            );
          }
        }

        debugPrint(
          'ClaimDocumentsService: _parseVersions - successfully parsed ${parsedVersions.length} versions from List',
        );
        return parsedVersions;
      } else if (versionsData is String) {
        debugPrint(
          'ClaimDocumentsService: _parseVersions - versionsData is String, attempting to decode JSON',
        );

        final decoded = jsonDecode(versionsData);
        debugPrint(
          'ClaimDocumentsService: _parseVersions - decoded JSON type: ${decoded.runtimeType}',
        );
        debugPrint(
          'ClaimDocumentsService: _parseVersions - decoded JSON value: $decoded',
        );

        if (decoded is List) {
          debugPrint(
            'ClaimDocumentsService: _parseVersions - decoded JSON is List with ${decoded.length} items',
          );

          final List<DocumentVersion> parsedVersions = [];

          for (int i = 0; i < decoded.length; i++) {
            try {
              final versionData = decoded[i];
              debugPrint(
                'ClaimDocumentsService: _parseVersions - parsing decoded version $i: $versionData',
              );

              if (versionData is Map<String, dynamic>) {
                final version = DocumentVersion.fromJson(versionData);
                parsedVersions.add(version);
                debugPrint(
                  'ClaimDocumentsService: _parseVersions - successfully parsed decoded version $i',
                );
              }
            } catch (e) {
              debugPrint(
                'ClaimDocumentsService: _parseVersions - error parsing decoded version $i: $e',
              );
            }
          }

          debugPrint(
            'ClaimDocumentsService: _parseVersions - successfully parsed ${parsedVersions.length} versions from JSON String',
          );
          return parsedVersions;
        } else {
          debugPrint(
            'ClaimDocumentsService: _parseVersions - decoded JSON is not a List, it is: ${decoded.runtimeType}',
          );
        }
      } else {
        debugPrint(
          'ClaimDocumentsService: _parseVersions - versionsData is neither List nor String, it is: ${versionsData.runtimeType}',
        );
      }
    } catch (e) {
      debugPrint('ClaimDocumentsService: _parseVersions - error: $e');
      debugPrint(
        'ClaimDocumentsService: _parseVersions - error stack trace: ${StackTrace.current}',
      );
    }

    debugPrint('ClaimDocumentsService: _parseVersions - returning empty list');
    return [];
  }

  /// Update a specific version's comment/notes
  Future<void> updateVersionComment({
    required String fundingApplicationId,
    required String logicalName,
    required String versionFileId,
    required String? newComment,
  }) async {
    // Find the existing document category
    final existingRecords = await _pb
        .collection('claim_documents')
        .getFullList(
          filter:
              'funding_application_id = "$fundingApplicationId" && logical_name = "$logicalName"',
        );

    if (existingRecords.isEmpty) {
      throw Exception('Document category not found: $logicalName');
    }

    final record = existingRecords.first;
    final existingVersions = _parseVersions(record.data['versions']);

    // Update the specific version's comment
    final updatedVersions =
        existingVersions.map((version) {
          if (version.fileId == versionFileId) {
            return DocumentVersion(
              fileId: version.fileId,
              filename: version.filename,
              uploadedAt: version.uploadedAt,
              uploadedBy: version.uploadedBy,
              notes: newComment,
            );
          }
          return version;
        }).toList();

    await _pb
        .collection('claim_documents')
        .update(
          record.id,
          body: {
            'versions': updatedVersions.map((v) => v.toJson()).toList(),
            'updated': DateTime.now().toIso8601String(),
          },
        );

    // Note: No longer syncing with document_repository field as it has been removed
  }

  /// Delete a document category
  Future<void> deleteDocumentCategory({
    required String fundingApplicationId,
    required String logicalName,
  }) async {
    final records = await _pb
        .collection('claim_documents')
        .getFullList(
          filter:
              'funding_application_id = "$fundingApplicationId" && logical_name = "$logicalName"',
        );

    for (final record in records) {
      await _pb.collection('claim_documents').delete(record.id);
    }

    // Note: No longer syncing with document_repository field as it has been removed
  }

  /// Upload multiple files and create a new document category with Google Drive integration
  Future<String> uploadFilesAndCreateCategory({
    required String fundingApplicationId,
    required String logicalName,
    required List<http.MultipartFile> files,
    required String uploadedBy,
    String? comment,
  }) async {
    if (files.isEmpty) {
      throw Exception('No files provided for upload');
    }

    // Calculate total file size for performance tracking
    final totalFileSize = files.fold<int>(
      0,
      (sum, file) => sum + (file.length),
    );

    return await _performanceService.trackFileOperation(
      'upload_multiple_files',
      () => _uploadFilesAndCreateCategoryInternal(
        fundingApplicationId: fundingApplicationId,
        logicalName: logicalName,
        files: files,
        uploadedBy: uploadedBy,
        comment: comment,
      ),
      fileSizeBytes: totalFileSize,
      metadata: {
        'fileCount': files.length,
        'logicalName': logicalName,
        'storageType': _storageConfig.primaryStorage.name,
      },
    );
  }

  /// Internal implementation of upload with performance optimizations
  Future<String> _uploadFilesAndCreateCategoryInternal({
    required String fundingApplicationId,
    required String logicalName,
    required List<http.MultipartFile> files,
    required String uploadedBy,
    String? comment,
  }) async {
    LoggerService.info(
      'Creating new category "$logicalName" with ${files.length} files',
    );

    try {
      // Use Google Drive as primary storage
      if (_storageConfig.primaryStorage == StorageType.googleDrive) {
        return await _uploadFilesToGoogleDrive(
          fundingApplicationId: fundingApplicationId,
          logicalName: logicalName,
          files: files,
          uploadedBy: uploadedBy,
          comment: comment,
        );
      } else {
        // Fallback to PocketBase storage
        return await _uploadFilesToPocketBase(
          fundingApplicationId: fundingApplicationId,
          logicalName: logicalName,
          files: files,
          uploadedBy: uploadedBy,
          comment: comment,
        );
      }
    } catch (e) {
      LoggerService.error('Failed to upload files and create category', e);

      // Try fallback storage if enabled
      if (_storageConfig.fallbackStorage != null &&
          _storageConfig.primaryStorage != _storageConfig.fallbackStorage) {
        LoggerService.info(
          'Attempting fallback storage: ${_storageConfig.fallbackStorage}',
        );

        try {
          if (_storageConfig.fallbackStorage == StorageType.pocketbase) {
            return await _uploadFilesToPocketBase(
              fundingApplicationId: fundingApplicationId,
              logicalName: logicalName,
              files: files,
              uploadedBy: uploadedBy,
              comment: comment,
            );
          }
        } catch (fallbackError) {
          LoggerService.error('Fallback storage also failed', fallbackError);
        }
      }

      rethrow;
    }
  }

  /// Upload files to Google Drive as primary storage
  Future<String> _uploadFilesToGoogleDrive({
    required String fundingApplicationId,
    required String logicalName,
    required List<http.MultipartFile> files,
    required String uploadedBy,
    String? comment,
  }) async {
    final now = DateTime.now().toIso8601String();

    LoggerService.info(
      'Uploading ${files.length} files to Google Drive for category: $logicalName',
    );

    // Step 1: Create folder structure in Google Drive
    final folderId = await _ensureGoogleDriveFolder(
      fundingApplicationId,
      logicalName,
    );

    LoggerService.debug('Created/found Google Drive folder: $folderId');

    // Step 2: Upload files to Google Drive and collect file IDs
    final List<String> googleDriveFileIds = [];
    final List<DocumentVersion> versions = [];

    for (int i = 0; i < files.length; i++) {
      final file = files[i];
      final versionNumber = i + 1;

      // Validate file before upload
      final validation = StorageUtils.validateMultipartFile(
        file,
        StorageType.googleDrive,
      );
      if (!validation.success) {
        throw Exception('File validation failed: ${validation.error}');
      }

      // Generate Google Drive file name
      final driveFileName = StorageUtils.generateGoogleDriveFileName(
        logicalName,
        file.filename ?? 'unknown',
        versionNumber,
      );

      // Generate metadata for Google Drive
      final metadata = StorageUtils.generateDocumentMetadata(
        fundingApplicationId: fundingApplicationId,
        logicalName: logicalName,
        uploadedBy: uploadedBy,
        versionNumber: versionNumber,
        notes: i == 0 ? comment : null,
        originalFilename: file.filename,
      );

      LoggerService.debug(
        'Uploading file ${i + 1}/${files.length}: $driveFileName',
      );

      // Convert MultipartFile to temporary File for upload
      final tempFile = await _createTempFileFromMultipart(file, driveFileName);

      try {
        // Upload to Google Drive
        final driveFile = await _driveService.uploadFile(
          file: tempFile,
          fileName: driveFileName,
          folderId: folderId,
          metadata: metadata,
        );

        googleDriveFileIds.add(driveFile.id);
      } finally {
        // Clean up temporary file
        if (await tempFile.exists()) {
          await tempFile.delete();
        }
      }

      // Create version file ID for this Google Drive file
      final versionFileId = StorageUtils.generateGoogleDriveFileId(
        fundingApplicationId,
        logicalName,
        versionNumber,
      );

      // Create document version
      final version = DocumentVersion(
        fileId: versionFileId,
        filename: file.filename ?? 'unknown',
        uploadedAt: now,
        uploadedBy: uploadedBy,
        notes: i == 0 ? comment : null,
      );

      versions.add(version);
      LoggerService.debug('Created version ${i + 1}: ${version.toJson()}');
    }

    // Step 3: Create PocketBase record with Google Drive metadata
    final body = {
      'funding_application_id': fundingApplicationId,
      'logical_name': logicalName,
      'name': logicalName,
      'uploaded_by': uploadedBy,
      'original_filename': files.first.filename ?? 'unknown',
      'current_version_file_id':
          versions.isNotEmpty ? versions.last.fileId : 'temp',
      'google_drive_file_ids':
          googleDriveFileIds, // Store all Google Drive file IDs
      'google_drive_folder_id': folderId,
      'storage_type': StorageType.googleDrive.value,
      'versions': versions.map((v) => v.toJson()).toList(),
      // Note: 'created' and 'updated' are automatically managed by PocketBase autodate fields
    };

    final record = await _pb.collection('claim_documents').create(body: body);

    LoggerService.info(
      'Created PocketBase record ${record.id} with ${versions.length} Google Drive files',
    );

    // Step 4: Cache the file URLs for quick access
    if (_storageConfig.enableCaching) {
      for (int i = 0; i < versions.length; i++) {
        final version = versions[i];
        final driveFileId = googleDriveFileIds[i];

        try {
          final url = await _driveService.getFileViewUrl(driveFileId);
          await _cacheService.cacheUrl(version.fileId, url);

          // Also cache metadata
          final metadata = await _driveService.getFileMetadata(driveFileId);
          await _cacheService.cacheMetadata(version.fileId, metadata.toJson());

          LoggerService.debug(
            'Cached URL and metadata for version: ${version.fileId}',
          );
        } catch (e) {
          LoggerService.warning(
            'Failed to cache data for version ${version.fileId}: $e',
          );
          // Don't fail the upload if caching fails
        }
      }
    }

    // Step 5: Create proper document access logs for each uploaded file
    await _createDocumentAccessLogs(
      documentId: record.id,
      googleDriveFileIds: googleDriveFileIds,
      action: 'upload',
      success: true,
      uploadedBy: uploadedBy,
    );

    return record.id;
  }

  /// Create document access logs for Google Drive operations
  Future<void> _createDocumentAccessLogs({
    required String documentId,
    required List<String> googleDriveFileIds,
    required String action,
    required bool success,
    required String uploadedBy,
    String? errorMessage,
  }) async {
    try {
      for (final googleDriveFileId in googleDriveFileIds) {
        final logData = {
          'document_id': documentId,
          'user_id': uploadedBy,
          'action': action,
          'timestamp': DateTime.now().toIso8601String(),
          'success': success,
          'error_message': errorMessage,
          'storage_type': 'google_drive',
          'google_drive_file_id': googleDriveFileId,
        };

        await _pb.collection('document_access_logs').create(body: logData);
        LoggerService.debug(
          'Created document access log for $action: documentId=$documentId, googleDriveFileId=$googleDriveFileId',
        );
      }
    } catch (e) {
      LoggerService.warning('Failed to create document access logs: $e');
      // Don't fail the main operation if logging fails
    }
  }

  /// Upload files to PocketBase as fallback storage
  Future<String> _uploadFilesToPocketBase({
    required String fundingApplicationId,
    required String logicalName,
    required List<http.MultipartFile> files,
    required String uploadedBy,
    String? comment,
  }) async {
    final now = DateTime.now().toIso8601String();

    LoggerService.info(
      'Uploading ${files.length} files to PocketBase for category: $logicalName',
    );

    // IMPORTANT: Ensure all files use the same field name 'document_file'
    // so PocketBase can store them as an array in the document_file field
    // According to PocketBase docs, all files must use the same field name
    final normalizedFiles = <http.MultipartFile>[];
    for (int i = 0; i < files.length; i++) {
      final file = files[i];

      // Validate file
      final validation = StorageUtils.validateMultipartFile(
        file,
        StorageType.pocketbase,
      );
      if (!validation.success) {
        throw Exception('File validation failed: ${validation.error}');
      }

      // Ensure all files use the same field name
      final normalizedFile = http.MultipartFile.fromBytes(
        'document_file', // Same field name for all files
        await file.finalize().toBytes(),
        filename: file.filename,
        contentType: file.contentType,
      );
      normalizedFiles.add(normalizedFile);
    }

    // Create the document record with all files
    final body = {
      'funding_application_id': fundingApplicationId,
      'logical_name': logicalName,
      'name': logicalName,
      'uploaded_by': uploadedBy,
      'original_filename': files.first.filename ?? 'unknown',
      'current_version_file_id': 'temp', // Temporary value, will be updated
      'storage_type': StorageType.pocketbase.value,
      // Note: 'created' and 'updated' are automatically managed by PocketBase autodate fields
    };

    final record = await _pb
        .collection('claim_documents')
        .create(body: body, files: normalizedFiles);

    LoggerService.debug('Created PocketBase record: ${record.id}');

    // Get the uploaded file names from the record
    final uploadedFiles = record.data['document_file'];
    final List<String> uploadedFileNames = [];

    if (uploadedFiles is List) {
      uploadedFileNames.addAll(uploadedFiles.cast<String>());
    } else if (uploadedFiles is String && uploadedFiles.isNotEmpty) {
      uploadedFileNames.add(uploadedFiles);
    }

    LoggerService.debug('Uploaded files: $uploadedFileNames');

    // Create document versions for each uploaded file
    final List<DocumentVersion> versions = [];
    for (int i = 0; i < uploadedFileNames.length; i++) {
      final versionFileId = '${record.id}_v${i + 1}';
      final version = DocumentVersion(
        fileId: versionFileId,
        filename: uploadedFileNames[i],
        uploadedAt: now,
        uploadedBy: uploadedBy,
        notes: i == 0 ? comment : null, // Only add comment to first file
      );
      versions.add(version);

      LoggerService.debug('Created version ${i + 1}: ${version.toJson()}');
    }

    // Update the record with version information
    final currentVersionFileId =
        versions.isNotEmpty ? versions.last.fileId : 'temp';
    await _pb
        .collection('claim_documents')
        .update(
          record.id,
          body: {
            'current_version_file_id': currentVersionFileId,
            'versions': versions.map((v) => v.toJson()).toList(),
            'updated': now,
          },
        );

    LoggerService.info(
      'PocketBase record updated with ${versions.length} versions',
    );

    return record.id;
  }

  /// Upload a single file and create a new document category (legacy method)
  Future<String> uploadFileAndCreateCategory({
    required String fundingApplicationId,
    required String logicalName,
    required http.MultipartFile file,
    required String uploadedBy,
    String? comment,
  }) async {
    // Delegate to the multiple files method
    return uploadFilesAndCreateCategory(
      fundingApplicationId: fundingApplicationId,
      logicalName: logicalName,
      files: [file],
      uploadedBy: uploadedBy,
      comment: comment,
    );
  }

  /// Upload a file and add it as a new version to an existing category
  Future<String> uploadFileAndAddVersion({
    required String fundingApplicationId,
    required String logicalName,
    required http.MultipartFile file,
    required String uploadedBy,
    String? comment,
  }) async {
    final now = DateTime.now().toIso8601String();

    // Find the existing document category
    final existingRecords = await _pb
        .collection('claim_documents')
        .getFullList(
          filter:
              'funding_application_id = "$fundingApplicationId" && logical_name = "$logicalName"',
        );

    if (existingRecords.isEmpty) {
      throw Exception('Document category not found: $logicalName');
    }

    final record = existingRecords.first;
    final existingVersions = _parseVersions(record.data['versions']);

    debugPrint(
      'ClaimDocumentsService: Found existing category with ${existingVersions.length} versions',
    );

    // Get current files array to understand the existing state
    final currentFilesData = record.data['document_file'];
    List<String> currentFilesList = [];

    if (currentFilesData is List) {
      currentFilesList = currentFilesData.cast<String>();
    } else if (currentFilesData is String && currentFilesData.isNotEmpty) {
      currentFilesList = [currentFilesData];
    }

    debugPrint(
      'ClaimDocumentsService: Current files array has ${currentFilesList.length} files: $currentFilesData',
    );

    // IMPORTANT: Even with maxSelect > 1, PocketBase might still replace files
    // when using the standard files parameter. We need to use the explicit
    // append syntax with the '+' modifier for the field name.

    // Strategy: Use PocketBase's explicit file append syntax
    // This ensures files are appended rather than replaced

    // Step 1: Upload the new file using append syntax
    debugPrint(
      'ClaimDocumentsService: Uploading new file to PocketBase using explicit append syntax.',
    );

    // Create a multipart file with the append field name
    final appendFile = http.MultipartFile.fromBytes(
      'document_file+', // Use '+' suffix to explicitly append
      await file.finalize().toBytes(),
      filename: file.filename,
      contentType: file.contentType,
    );

    // Use the append syntax to ensure files are added to the array
    final uploadRecord = await _pb
        .collection('claim_documents')
        .update(
          record.id,
          body: {'updated': now},
          files: [appendFile], // This should append using the '+' modifier
        );

    // Step 2: Get the new filename from the upload result
    final uploadedFiles = uploadRecord.data['document_file'];
    String newFileName = file.filename ?? 'unknown';

    if (uploadedFiles is List && uploadedFiles.isNotEmpty) {
      // When appending, the new file should be the last one in the array
      newFileName = uploadedFiles.last as String;
      debugPrint(
        'ClaimDocumentsService: Extracted new filename from array (last): $newFileName',
      );
    } else if (uploadedFiles is String && uploadedFiles.isNotEmpty) {
      newFileName = uploadedFiles;
      debugPrint(
        'ClaimDocumentsService: Extracted new filename from string: $newFileName',
      );
    } else {
      debugPrint(
        'ClaimDocumentsService: Could not extract filename from upload result, using original: $newFileName',
      );
    }

    debugPrint(
      'ClaimDocumentsService: File uploaded successfully. New filename: $newFileName',
    );
    debugPrint(
      'ClaimDocumentsService: Used explicit append syntax (document_file+) to ensure file is appended.',
    );
    debugPrint(
      'ClaimDocumentsService: Verifying that files were properly appended...',
    );

    // The uploadRecord is our final result
    final updatedRecord = uploadRecord;

    // Step 6: Verify the update was successful
    final updatedFiles = updatedRecord.data['document_file'];

    if (updatedFiles is List) {
      debugPrint(
        'ClaimDocumentsService: Files array now has ${updatedFiles.length} files: $updatedFiles',
      );

      // Verify that files were properly appended
      if (updatedFiles.length == currentFilesList.length + 1) {
        debugPrint(
          'SUCCESS: File array properly appended. Expected ${currentFilesList.length + 1}, got ${updatedFiles.length}',
        );
      } else {
        debugPrint(
          'ERROR: File array was NOT properly appended! Expected ${currentFilesList.length + 1}, got ${updatedFiles.length}',
        );
        debugPrint('Previous files: $currentFilesList');
        debugPrint('Updated files: $updatedFiles');
        debugPrint(
          'DIAGNOSIS: Files are still being replaced despite using explicit append syntax (document_file+).',
        );
        debugPrint(
          'POSSIBLE CAUSES: 1) PocketBase version incompatibility, 2) Different append syntax needed, 3) Collection schema issue.',
        );

        // Call the schema check method to provide detailed instructions
        await checkCollectionSchema();

        // Also inspect the record to understand the exact issue
        debugPrint(
          'Inspecting record ${record.id} to understand the file structure...',
        );
        await inspectRecord(record.id);
      }
    } else {
      debugPrint('ClaimDocumentsService: Updated files result: $updatedFiles');
    }

    debugPrint(
      'ClaimDocumentsService: Successfully appended new file to category. New filename: $newFileName',
    );

    // Create the new version with a unique file ID
    final versionFileId = '${record.id}_v${existingVersions.length + 1}';
    final newVersion = DocumentVersion(
      fileId: versionFileId,
      filename: newFileName,
      uploadedAt: now,
      uploadedBy: uploadedBy,
      notes: comment,
    );

    debugPrint(
      'ClaimDocumentsService: Created new version object: ${newVersion.toJson()}',
    );

    // Update the versions array and current version
    final updatedVersions = [...existingVersions, newVersion];
    await _pb
        .collection('claim_documents')
        .update(
          record.id,
          body: {
            'current_version_file_id': versionFileId,
            'versions': updatedVersions.map((v) => v.toJson()).toList(),
            'updated': now,
          },
        );

    debugPrint(
      'ClaimDocumentsService: Updated category with new version. Total versions: ${updatedVersions.length}',
    );

    // Note: No longer syncing with document_repository field as it has been removed

    return versionFileId;
  }

  /// Get file URL for a document version with cache-first strategy
  Future<String> getFileUrl(String versionFileId) async {
    final startTime = DateTime.now();

    try {
      LoggerService.info('Getting file URL for version: $versionFileId');

      // Step 1: Check cache first
      if (_storageConfig.enableCaching) {
        final cachedUrl = await _cacheService.getCachedUrl(versionFileId);
        if (cachedUrl != null) {
          LoggerService.debug('Cache hit for file URL: $versionFileId');
          return cachedUrl;
        }
        LoggerService.debug('Cache miss for file URL: $versionFileId');
      }

      // Step 2: Determine storage type from file ID
      final storageType = StorageUtils.getStorageTypeFromFileId(versionFileId);
      LoggerService.debug(
        'Detected storage type: $storageType for file: $versionFileId',
      );

      // Step 3: Get URL based on storage type
      String url;
      if (storageType == StorageType.googleDrive) {
        url = await _getGoogleDriveUrl(versionFileId);
      } else {
        url = await _getPocketBaseUrl(versionFileId);
      }

      // Step 4: Verify URL accessibility before returning
      try {
        await _verifyUrlAccessibility(url);
      } catch (e) {
        LoggerService.warning(
          'URL verification failed for $versionFileId: $e. Attempting to fix...',
        );
        // Try to fix the issue and get a new URL
        url = await _attemptUrlFix(versionFileId, storageType);
      }

      // Step 5: Cache the URL if caching is enabled
      if (_storageConfig.enableCaching) {
        await _cacheService.cacheUrl(versionFileId, url);
        LoggerService.debug('Cached file URL: $versionFileId');
      }

      final duration = DateTime.now().difference(startTime);
      LoggerService.info(
        'Successfully got file URL for $versionFileId in ${duration.inMilliseconds}ms',
      );

      return url;
    } catch (e) {
      final duration = DateTime.now().difference(startTime);
      LoggerService.error(
        'Failed to get file URL for $versionFileId after ${duration.inMilliseconds}ms',
        e,
      );
      rethrow;
    }
  }

  /// Get Google Drive URL for a document version
  Future<String> _getGoogleDriveUrl(String versionFileId) async {
    try {
      // Parse Google Drive file ID from version file ID
      final parsed = StorageUtils.parseVersionFileId(versionFileId);
      if (parsed == null) {
        throw Exception('Invalid Google Drive file ID format: $versionFileId');
      }

      // Get the document record to find the Google Drive file ID
      final fundingApplicationId = parsed['funding_application_id'];
      final logicalName = parsed['logical_name'];
      final version = parsed['version'];

      if (fundingApplicationId == null ||
          logicalName == null ||
          version == null) {
        throw Exception(
          'Missing required components in file ID: $versionFileId',
        );
      }

      // Find the document record
      final records = await _pb
          .collection('claim_documents')
          .getFullList(
            filter:
                'funding_application_id = "$fundingApplicationId" && logical_name = "$logicalName"',
          );

      if (records.isEmpty) {
        throw Exception('Document not found for file ID: $versionFileId');
      }

      final record = records.first;
      final versions = _parseVersions(record.data['versions']);

      // Find the specific version
      versions.firstWhere(
        (v) => v.fileId == versionFileId,
        orElse: () => throw Exception('Version not found: $versionFileId'),
      );

      // Check if we have a Google Drive file ID stored in metadata
      String? googleDriveFileId;
      if (record.data['google_drive_file_id'] != null) {
        googleDriveFileId = record.data['google_drive_file_id'] as String;
      } else {
        // For new implementations, the Google Drive file ID should be stored
        // For now, we'll fall back to PocketBase URL
        LoggerService.warning(
          'No Google Drive file ID found for $versionFileId, falling back to PocketBase',
        );
        return await _getPocketBaseUrl(versionFileId);
      }

      // Get download URL from Google Drive service for direct file access
      // This is needed for PDF viewers and other direct file access
      final url = await _driveService.getFileDownloadUrl(googleDriveFileId);
      LoggerService.debug(
        'Got Google Drive download URL for file: $versionFileId',
      );
      LoggerService.debug('Google Drive URL: $url');

      // Ensure we have a proper download URL, not a view URL
      String finalUrl = url;
      if (url.contains('/view') && url.contains('drive.google.com/file/d/')) {
        final fileIdMatch = RegExp(r'/file/d/([a-zA-Z0-9_-]+)').firstMatch(url);
        if (fileIdMatch != null) {
          final fileId = fileIdMatch.group(1);
          finalUrl = 'https://drive.google.com/uc?export=download&id=$fileId';
          LoggerService.debug('Converted view URL to download URL: $finalUrl');
        }
      }

      return finalUrl;
    } catch (e) {
      LoggerService.error(
        'Failed to get Google Drive URL for $versionFileId',
        e,
      );
      rethrow;
    }
  }

  /// Get PocketBase URL for a document version (fallback method)
  Future<String> _getPocketBaseUrl(String versionFileId) async {
    try {
      // Parse the version file ID to get the record ID
      // Format: recordId_v1, recordId_v2, etc.
      String recordId;

      if (versionFileId.contains('_v')) {
        final parts = versionFileId.split('_v');
        recordId = parts[0];
      } else {
        // Fallback for legacy file IDs - try to find the record that contains this version
        recordId = versionFileId;
      }

      final record = await _pb.collection('claim_documents').getOne(recordId);
      final files = record.data['document_file'];
      final versions = _parseVersions(record.data['versions']);

      LoggerService.debug(
        'Getting PocketBase URL for version $versionFileId in record $recordId',
      );
      debugPrint(
        'ClaimDocumentsService: Record has ${files is List ? files.length : (files != null ? 1 : 0)} files and ${versions.length} versions',
      );

      String filename;

      // First, try to find the version in the versions array to get the correct index
      int fileIndex = -1;
      for (int i = 0; i < versions.length; i++) {
        if (versions[i].fileId == versionFileId) {
          fileIndex = i;
          filename = versions[i].filename;
          debugPrint(
            'ClaimDocumentsService: Found version at index $i with filename: $filename',
          );
          break;
        }
      }

      // If we found the version, try to get the corresponding file from the array
      if (fileIndex >= 0 && files is List && fileIndex < files.length) {
        final arrayFilename = files[fileIndex] as String;
        debugPrint(
          'ClaimDocumentsService: Using file from array at index $fileIndex: $arrayFilename',
        );
        filename = arrayFilename;
      } else if (files is List && files.isNotEmpty) {
        // Fallback: use the last file in the array
        filename = files.last as String;
        debugPrint(
          'ClaimDocumentsService: Fallback to last file in array: $filename',
        );
      } else if (files is String && files.isNotEmpty) {
        filename = files;
        debugPrint('ClaimDocumentsService: Using single file: $filename');
      } else {
        throw Exception('No files found for version: $versionFileId');
      }

      debugPrint(
        'ClaimDocumentsService: Final filename for URL generation: $filename',
      );

      return _pb.files.getURL(record, filename).toString();
    } catch (e) {
      LoggerService.error('Failed to get PocketBase URL for $versionFileId', e);
      rethrow;
    }
  }

  /// Verify if a URL is accessible by making a HEAD request
  Future<void> _verifyUrlAccessibility(String url) async {
    try {
      final response = await http.head(Uri.parse(url));
      if (response.statusCode != 200) {
        throw Exception('URL returned status code: ${response.statusCode}');
      }
      LoggerService.debug('URL verification successful: $url');
    } catch (e) {
      LoggerService.warning('URL verification failed: $url - $e');
      rethrow;
    }
  }

  /// Attempt to fix URL issues by refreshing record data and regenerating URL
  Future<String> _attemptUrlFix(
    String versionFileId,
    StorageType storageType,
  ) async {
    try {
      LoggerService.info('Attempting to fix URL for version: $versionFileId');

      // Clear any cached data for this file
      if (_storageConfig.enableCaching) {
        await _cacheService.invalidateCache(versionFileId);
      }

      // For PocketBase files, try to inspect and fix the record
      if (storageType == StorageType.pocketbase) {
        final recordId =
            versionFileId.contains('_v')
                ? versionFileId.split('_v')[0]
                : versionFileId;

        // Inspect the record to understand the issue
        await inspectRecord(recordId);

        // Try to fix filename duplication issues
        await fixFilenameDuplication(recordId);

        // Regenerate the URL after fixing
        return await _getPocketBaseUrl(versionFileId);
      } else {
        // For Google Drive files, try to regenerate the URL
        return await _getGoogleDriveUrl(versionFileId);
      }
    } catch (e) {
      LoggerService.error('Failed to fix URL for $versionFileId', e);
      rethrow;
    }
  }

  /// Debug method to check what documents exist in the claim_documents collection
  Future<void> debugClaimDocumentsCollection(
    String fundingApplicationId,
  ) async {
    try {
      debugPrint('=== DEBUGGING CLAIM_DOCUMENTS COLLECTION ===');
      debugPrint('Funding Application ID: $fundingApplicationId');

      // First, check if the collection exists and is accessible
      try {
        final allRecords =
            await _pb.collection('claim_documents').getFullList();
        debugPrint(
          'Total records in claim_documents collection: ${allRecords.length}',
        );
      } catch (e) {
        debugPrint('Error accessing claim_documents collection: $e');
        return;
      }

      // Now check for records matching this funding application
      final records = await _pb
          .collection('claim_documents')
          .getFullList(
            filter: 'funding_application_id = "$fundingApplicationId"',
          );

      debugPrint(
        'Records found for funding application $fundingApplicationId: ${records.length}',
      );

      if (records.isEmpty) {
        debugPrint(
          'No documents found for this funding application in claim_documents collection',
        );

        // Check if there are any records with similar IDs (debugging potential ID mismatch)
        try {
          final allRecords =
              await _pb.collection('claim_documents').getFullList();
          debugPrint('All funding_application_id values in collection:');
          for (final record in allRecords) {
            debugPrint(
              '  - Record ${record.id}: funding_application_id = "${record.data['funding_application_id']}"',
            );
          }
        } catch (e) {
          debugPrint('Error listing all records: $e');
        }
      } else {
        for (int i = 0; i < records.length; i++) {
          final record = records[i];
          debugPrint('=== RECORD $i ===');
          debugPrint('ID: ${record.id}');
          debugPrint('Created: ${record.data['created']}');
          debugPrint('Updated: ${record.data['updated']}');
          debugPrint(
            'Funding Application ID: ${record.data['funding_application_id']}',
          );
          debugPrint('Logical Name: ${record.data['logical_name']}');
          debugPrint(
            'Current Version File ID: ${record.data['current_version_file_id']}',
          );
          debugPrint(
            'Versions field type: ${record.data['versions'].runtimeType}',
          );
          debugPrint('Versions field value: ${record.data['versions']}');
          debugPrint('Document File field: ${record.data['document_file']}');
          debugPrint('=== END RECORD $i ===');
        }
      }

      debugPrint('=== END DEBUGGING CLAIM_DOCUMENTS COLLECTION ===');
    } catch (e) {
      debugPrint('Error in debugClaimDocumentsCollection: $e');
      debugPrint('Stack trace: ${StackTrace.current}');
    }
  }

  /// Check for and report duplicate logical names in claim_documents collection
  Future<void> checkForDuplicateLogicalNames(
    String fundingApplicationId,
  ) async {
    try {
      debugPrint('=== CHECKING FOR DUPLICATE LOGICAL NAMES ===');

      final records = await _pb
          .collection('claim_documents')
          .getFullList(
            filter: 'funding_application_id = "$fundingApplicationId"',
          );

      final Map<String, List<String>> logicalNameMap = {};

      for (final record in records) {
        final logicalName = record.data['logical_name'] as String;
        if (!logicalNameMap.containsKey(logicalName)) {
          logicalNameMap[logicalName] = [];
        }
        logicalNameMap[logicalName]!.add(record.id);
      }

      bool foundDuplicates = false;
      for (final entry in logicalNameMap.entries) {
        if (entry.value.length > 1) {
          foundDuplicates = true;
          debugPrint(
            'WARNING: Duplicate logical name "${entry.key}" found in records: ${entry.value.join(", ")}',
          );
        }
      }

      if (!foundDuplicates) {
        debugPrint('No duplicate logical names found.');
      }

      debugPrint('=== DUPLICATE CHECK COMPLETE ===');
    } catch (e) {
      debugPrint('Error checking for duplicate logical names: $e');
    }
  }

  /// Fix file array data for existing documents that may have incomplete file arrays
  Future<void> fixFileArrayData(String fundingApplicationId) async {
    try {
      debugPrint('=== FIXING FILE ARRAY DATA ===');

      final records = await _pb
          .collection('claim_documents')
          .getFullList(
            filter: 'funding_application_id = "$fundingApplicationId"',
          );

      for (final record in records) {
        final files = record.data['document_file'];
        final versions = _parseVersions(record.data['versions']);

        debugPrint(
          'Processing ${record.data['logical_name']}: ${files is List ? files.length : (files != null ? 1 : 0)} files, ${versions.length} versions',
        );

        // If we have fewer files than versions, this indicates a data inconsistency
        final fileCount =
            files is List ? files.length : (files != null ? 1 : 0);
        if (fileCount < versions.length) {
          debugPrint(
            'WARNING: File array mismatch for ${record.data['logical_name']} - $fileCount files but ${versions.length} versions',
          );

          // For now, we'll log this but not automatically fix it
          // as it requires careful handling of legacy data
        }
      }

      debugPrint('=== FILE ARRAY DATA CHECK COMPLETE ===');
    } catch (e) {
      debugPrint('Error fixing file array data: $e');
    }
  }

  /// Check the claim_documents collection schema to verify document_file field configuration
  Future<void> checkCollectionSchema() async {
    try {
      debugPrint('=== CHECKING CLAIM_DOCUMENTS COLLECTION SCHEMA ===');

      // Note: This requires admin access to check collection schema
      // In a production app, this should be done via admin interface or migration scripts
      debugPrint(
        'IMPORTANT: Please verify that the claim_documents collection has:',
      );
      debugPrint('1. document_file field with type: "file"');
      debugPrint(
        '2. document_file field with maxSelect > 1 (e.g., maxSelect: 20)',
      );
      debugPrint(
        '3. If maxSelect is 1 or not set, files will be REPLACED instead of APPENDED',
      );
      debugPrint('');
      debugPrint('To fix this issue:');
      debugPrint('1. Open PocketBase Admin UI');
      debugPrint('2. Go to Collections > claim_documents');
      debugPrint('3. Edit the document_file field');
      debugPrint('4. Set "Max select" to a value > 1 (e.g., 20)');
      debugPrint('5. Save the collection');
      debugPrint('=== SCHEMA CHECK COMPLETE ===');
    } catch (e) {
      debugPrint('Error checking collection schema: $e');
    }
  }

  /// Inspect a specific record to understand its structure and identify issues
  Future<void> inspectRecord(String recordId) async {
    try {
      debugPrint('=== INSPECTING RECORD $recordId ===');

      final record = await _pb.collection('claim_documents').getOne(recordId);

      debugPrint('Record ID: ${record.id}');
      debugPrint('Logical Name: ${record.data['logical_name']}');
      debugPrint(
        'Current Version File ID: ${record.data['current_version_file_id']}',
      );

      // Inspect document_file field
      final files = record.data['document_file'];
      debugPrint('Document File field type: ${files.runtimeType}');
      debugPrint('Document File field value: $files');

      if (files is List) {
        debugPrint('Document File array length: ${files.length}');
        for (int i = 0; i < files.length; i++) {
          debugPrint('  File $i: ${files[i]}');
        }
      }

      // Inspect versions field
      final versionsRaw = record.data['versions'];
      debugPrint('Versions field type: ${versionsRaw.runtimeType}');
      debugPrint('Versions field raw value: $versionsRaw');

      final versions = _parseVersions(versionsRaw);
      debugPrint('Parsed versions count: ${versions.length}');

      for (int i = 0; i < versions.length; i++) {
        final version = versions[i];
        debugPrint('  Version $i:');
        debugPrint('    File ID: ${version.fileId}');
        debugPrint('    Filename: ${version.filename}');
        debugPrint('    Uploaded At: ${version.uploadedAt}');
        debugPrint('    Uploaded By: ${version.uploadedBy}');
        debugPrint('    Notes: ${version.notes}');
      }

      // Check for filename duplications
      final filenames = versions.map((v) => v.filename).toList();
      final uniqueFilenames = filenames.toSet().toList();

      if (filenames.length != uniqueFilenames.length) {
        debugPrint('WARNING: Duplicate filenames detected!');
        debugPrint('Total filenames: ${filenames.length}');
        debugPrint('Unique filenames: ${uniqueFilenames.length}');
        debugPrint('All filenames: $filenames');
        debugPrint('Unique filenames: $uniqueFilenames');

        // Find duplicates
        final duplicates = <String>[];
        for (final filename in filenames) {
          if (filenames.where((f) => f == filename).length > 1 &&
              !duplicates.contains(filename)) {
            duplicates.add(filename);
          }
        }
        debugPrint('Duplicate filenames: $duplicates');
      } else {
        debugPrint('No duplicate filenames found.');
      }

      debugPrint('=== END RECORD INSPECTION ===');
    } catch (e) {
      debugPrint('Error inspecting record $recordId: $e');
    }
  }

  /// Fix filename duplication issues in versions array
  Future<void> fixFilenameDuplication(String recordId) async {
    try {
      debugPrint('=== FIXING FILENAME DUPLICATION FOR RECORD $recordId ===');

      final record = await _pb.collection('claim_documents').getOne(recordId);
      final files = record.data['document_file'];
      final versions = _parseVersions(record.data['versions']);

      if (files is! List || versions.isEmpty) {
        debugPrint('No files or versions to fix');
        return;
      }

      debugPrint('Current files array: $files');
      debugPrint('Current versions count: ${versions.length}');

      // Check if we need to fix the versions array
      final filenames = versions.map((v) => v.filename).toList();
      final uniqueFilenames = filenames.toSet().toList();

      if (filenames.length == uniqueFilenames.length &&
          files.length == versions.length) {
        debugPrint('No duplication issues found');
        return;
      }

      debugPrint('Found duplication issues - fixing...');

      // Create corrected versions array based on actual files
      final List<DocumentVersion> correctedVersions = [];

      for (int i = 0; i < files.length; i++) {
        final filename = files[i] as String;
        final versionFileId = '${record.id}_v${i + 1}';

        // Try to find existing version with this filename to preserve metadata
        DocumentVersion? existingVersion;
        for (final version in versions) {
          if (version.filename == filename) {
            existingVersion = version;
            break;
          }
        }

        final correctedVersion = DocumentVersion(
          fileId: versionFileId,
          filename: filename,
          uploadedAt:
              existingVersion?.uploadedAt ?? DateTime.now().toIso8601String(),
          uploadedBy: existingVersion?.uploadedBy ?? 'system',
          notes: existingVersion?.notes,
        );

        correctedVersions.add(correctedVersion);
        debugPrint(
          'Created corrected version ${i + 1}: ${correctedVersion.toJson()}',
        );
      }

      // Update the record with corrected versions
      final currentVersionFileId =
          correctedVersions.isNotEmpty
              ? correctedVersions.last.fileId
              : record.data['current_version_file_id'];

      await _pb
          .collection('claim_documents')
          .update(
            record.id,
            body: {
              'current_version_file_id': currentVersionFileId,
              'versions': correctedVersions.map((v) => v.toJson()).toList(),
              'updated': DateTime.now().toIso8601String(),
            },
          );

      debugPrint('Successfully fixed filename duplication issues');
      debugPrint('Updated versions count: ${correctedVersions.length}');
      debugPrint('=== FILENAME DUPLICATION FIX COMPLETE ===');
    } catch (e) {
      debugPrint('Error fixing filename duplication for record $recordId: $e');
    }
  }

  /// Debug method to inspect the specific problematic record
  Future<void> debugProblematicRecord() async {
    const recordId = '6f3t72d75s6zf2i';
    debugPrint('=== DEBUGGING PROBLEMATIC RECORD $recordId ===');

    try {
      await inspectRecord(recordId);

      // Also try to fix any issues found
      debugPrint('Attempting to fix any filename duplication issues...');
      await fixFilenameDuplication(recordId);

      // Inspect again to verify the fix
      debugPrint('Re-inspecting record after fix attempt...');
      await inspectRecord(recordId);
    } catch (e) {
      debugPrint('Error debugging problematic record: $e');
    }

    debugPrint('=== END PROBLEMATIC RECORD DEBUG ===');
  }

  /// Debug and fix URL issues for a specific file ID
  Future<String?> debugAndFixFileUrl(String versionFileId) async {
    try {
      LoggerService.info('=== DEBUGGING FILE URL: $versionFileId ===');

      // Step 1: Parse the file ID to understand its structure
      final parsed = StorageUtils.parseVersionFileId(versionFileId);
      LoggerService.info('Parsed file ID: $parsed');

      // Step 2: Determine storage type
      final storageType = StorageUtils.getStorageTypeFromFileId(versionFileId);
      LoggerService.info('Detected storage type: $storageType');

      // Step 3: Try to get the URL normally first
      try {
        final url = await getFileUrl(versionFileId);
        LoggerService.info('Successfully got URL: $url');
        return url;
      } catch (e) {
        LoggerService.warning('Normal URL retrieval failed: $e');
      }

      // Step 4: If normal retrieval failed, try to fix the issue
      LoggerService.info('Attempting to fix URL issue...');
      final fixedUrl = await _attemptUrlFix(versionFileId, storageType);
      LoggerService.info('Fixed URL: $fixedUrl');

      return fixedUrl;
    } catch (e) {
      LoggerService.error(
        'Failed to debug and fix file URL: $versionFileId',
        e,
      );
      return null;
    }
  }

  /// Test method to verify multiple file upload functionality
  /// This creates test MultipartFile objects and attempts to upload them
  Future<String?> testMultipleFileUpload({
    required String fundingApplicationId,
    required String uploadedBy,
  }) async {
    try {
      debugPrint('=== TESTING MULTIPLE FILE UPLOAD ===');

      // Create test files with the same field name
      final testFiles = [
        http.MultipartFile.fromString(
          'document_file',
          'Test content for file 1',
          filename: 'test_file_1.txt',
        ),
        http.MultipartFile.fromString(
          'document_file',
          'Test content for file 2',
          filename: 'test_file_2.txt',
        ),
        http.MultipartFile.fromString(
          'document_file',
          'Test content for file 3',
          filename: 'test_file_3.txt',
        ),
      ];

      debugPrint('Created ${testFiles.length} test files');

      // Upload using the multiple files method
      final recordId = await uploadFilesAndCreateCategory(
        fundingApplicationId: fundingApplicationId,
        logicalName: 'TEST_MULTIPLE_UPLOAD',
        files: testFiles,
        uploadedBy: uploadedBy,
        comment: 'Test upload for multiple file functionality',
      );

      debugPrint('Test upload completed. Record ID: $recordId');

      // Inspect the created record
      await inspectRecord(recordId);

      debugPrint('=== MULTIPLE FILE UPLOAD TEST COMPLETE ===');
      return recordId;
    } catch (e) {
      debugPrint('Error in multiple file upload test: $e');
      debugPrint('Stack trace: ${StackTrace.current}');
      return null;
    }
  }

  /// Ensure Google Drive folder exists for a funding application
  Future<String> _ensureGoogleDriveFolder(
    String fundingApplicationId,
    String logicalName,
  ) async {
    try {
      // Create folder structure: 3PayGlobal/Claims/{fundingApplicationId}/Documents
      final folderPath = StorageUtils.generateGoogleDriveFolderPath(
        fundingApplicationId,
      );
      final pathParts = folderPath.split('/');

      String? currentFolderId;

      // Create each folder in the path if it doesn't exist
      for (final folderName in pathParts) {
        if (folderName.isEmpty) continue;

        // Check if folder already exists
        final existingFolder = await _findFolderByName(
          folderName,
          currentFolderId,
        );

        if (existingFolder != null) {
          currentFolderId = existingFolder.id;
        } else {
          // Create the folder
          final newFolder = await _driveService.createFolder(
            folderName: folderName,
            parentFolderId: currentFolderId,
            metadata: {
              'funding_application_id': fundingApplicationId,
              'logical_name': logicalName,
              'created_by': '3PayGlobal',
            },
          );
          currentFolderId = newFolder.id;
        }
      }

      return currentFolderId!;
    } catch (e) {
      LoggerService.error('Failed to ensure Google Drive folder', e);
      rethrow;
    }
  }

  /// Find folder by name in parent folder
  Future<GoogleDriveFile?> _findFolderByName(
    String folderName,
    String? parentFolderId,
  ) async {
    try {
      final files = await _driveService.listFilesInFolder(
        folderId: parentFolderId,
        query: "mimeType = 'application/vnd.google-apps.folder'",
      );

      for (final file in files) {
        if (file.name == folderName && file.isFolder) {
          return file;
        }
      }

      return null;
    } catch (e) {
      LoggerService.warning('Failed to find folder by name: $folderName: $e');
      return null;
    }
  }

  /// Create temporary file from MultipartFile
  Future<File> _createTempFileFromMultipart(
    http.MultipartFile multipartFile,
    String fileName,
  ) async {
    try {
      final tempDir = Directory.systemTemp;
      final tempFile = File(
        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_$fileName',
      );

      final bytes = await StorageUtils.multipartFileToBytes(multipartFile);
      await tempFile.writeAsBytes(bytes);

      return tempFile;
    } catch (e) {
      LoggerService.error('Failed to create temporary file', e);
      rethrow;
    }
  }

  /// Invalidate cache for a document and all its versions
  Future<void> invalidateDocumentCache(String documentId) async {
    try {
      LoggerService.info('Invalidating cache for document: $documentId');

      // Get the document record to find all version file IDs
      final record = await _pb.collection('claim_documents').getOne(documentId);
      final versions = _parseVersions(record.data['versions']);

      // Invalidate cache for each version
      for (final version in versions) {
        await _cacheService.invalidateCache(version.fileId);
        LoggerService.debug('Invalidated cache for version: ${version.fileId}');
      }

      // Also invalidate any Google Drive file IDs if present
      if (record.data['google_drive_file_ids'] != null) {
        final googleDriveFileIds = record.data['google_drive_file_ids'] as List;
        for (final fileId in googleDriveFileIds) {
          await _cacheService.invalidateCache(fileId.toString());
          LoggerService.debug(
            'Invalidated cache for Google Drive file: $fileId',
          );
        }
      }

      LoggerService.info(
        'Successfully invalidated cache for document: $documentId',
      );
    } catch (e) {
      LoggerService.error(
        'Failed to invalidate cache for document: $documentId',
        e,
      );
      // Don't rethrow - cache invalidation failure shouldn't break the main operation
    }
  }

  /// Get cached metadata for a document version
  Future<Map<String, dynamic>?> getCachedMetadata(String versionFileId) async {
    if (!_storageConfig.enableCaching) return null;

    try {
      return await _cacheService.getCachedMetadata(versionFileId);
    } catch (e) {
      LoggerService.warning(
        'Failed to get cached metadata for $versionFileId: $e',
      );
      return null;
    }
  }

  /// Cache metadata for a document version
  Future<void> cacheMetadata(
    String versionFileId,
    Map<String, dynamic> metadata,
  ) async {
    if (!_storageConfig.enableCaching) return;

    try {
      await _cacheService.cacheMetadata(versionFileId, metadata);
      LoggerService.debug('Cached metadata for version: $versionFileId');
    } catch (e) {
      LoggerService.warning('Failed to cache metadata for $versionFileId: $e');
      // Don't rethrow - caching failure shouldn't break the main operation
    }
  }

  /// Get storage configuration
  StorageConfiguration get storageConfiguration => _storageConfig;

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStatistics() async {
    try {
      final stats = _cacheService.getStatistics();
      return {
        'cache_enabled': _storageConfig.enableCaching,
        'primary_storage': _storageConfig.primaryStorage.value,
        'fallback_storage': _storageConfig.fallbackStorage?.value,
        'total_hits': stats.totalHits,
        'total_misses': stats.totalMisses,
        'hit_ratio': stats.hitRatioPercentage,
        'total_entries': stats.totalEntries,
        'total_size_mb': stats.totalSizeMB,
        'performance_grade': stats.performanceGrade,
        'average_access_time_ms': stats.averageAccessTimeMs,
      };
    } catch (e) {
      LoggerService.error('Failed to get cache statistics', e);
      return {
        'cache_enabled': _storageConfig.enableCaching,
        'error': e.toString(),
      };
    }
  }

  /// Clear all document cache
  Future<void> clearDocumentCache() async {
    try {
      LoggerService.info('Clearing all document cache...');
      await _cacheService.clearCache();
      LoggerService.info('Successfully cleared all document cache');
    } catch (e) {
      LoggerService.error('Failed to clear document cache', e);
      rethrow;
    }
  }

  /// Preload cache for frequently accessed documents
  Future<void> preloadCache(List<String> documentIds) async {
    if (!_storageConfig.enableCaching) return;

    LoggerService.info(
      'Preloading cache for ${documentIds.length} documents...',
    );

    for (final documentId in documentIds) {
      try {
        final record = await _pb
            .collection('claim_documents')
            .getOne(documentId);
        final versions = _parseVersions(record.data['versions']);

        for (final version in versions) {
          // Preload URL
          try {
            await getFileUrl(version.fileId);
          } catch (e) {
            LoggerService.warning(
              'Failed to preload URL for ${version.fileId}: $e',
            );
          }

          // Preload metadata if it's a Google Drive file
          if (StorageUtils.getStorageTypeFromFileId(version.fileId) ==
              StorageType.googleDrive) {
            try {
              final metadata = await getCachedMetadata(version.fileId);
              if (metadata == null) {
                // Load metadata from Google Drive and cache it
                final parsed = StorageUtils.parseVersionFileId(version.fileId);
                if (parsed != null &&
                    record.data['google_drive_file_ids'] != null) {
                  final googleDriveFileIds =
                      record.data['google_drive_file_ids'] as List;
                  if (googleDriveFileIds.isNotEmpty) {
                    final driveMetadata = await _driveService.getFileMetadata(
                      googleDriveFileIds.first.toString(),
                    );
                    await cacheMetadata(version.fileId, driveMetadata.toJson());
                  }
                }
              }
            } catch (e) {
              LoggerService.warning(
                'Failed to preload metadata for ${version.fileId}: $e',
              );
            }
          }
        }
      } catch (e) {
        LoggerService.warning(
          'Failed to preload cache for document $documentId: $e',
        );
      }
    }

    LoggerService.info('Completed cache preloading');
  }

  /// Batch upload multiple files to different categories
  Future<Map<String, String>> batchUploadFiles({
    required String fundingApplicationId,
    required Map<String, List<http.MultipartFile>> categoryFiles,
    required String uploadedBy,
    Map<String, String>? comments,
  }) async {
    LoggerService.info(
      'Starting batch upload for ${categoryFiles.length} categories',
    );

    final results = <String, String>{};
    final errors = <String, String>{};

    // Process uploads in parallel for better performance
    final futures = categoryFiles.entries.map((entry) async {
      final logicalName = entry.key;
      final files = entry.value;
      final comment = comments?[logicalName];

      try {
        final documentId = await uploadFilesAndCreateCategory(
          fundingApplicationId: fundingApplicationId,
          logicalName: logicalName,
          files: files,
          uploadedBy: uploadedBy,
          comment: comment,
        );
        results[logicalName] = documentId;
        LoggerService.debug('Batch upload success for category: $logicalName');
      } catch (e) {
        errors[logicalName] = e.toString();
        LoggerService.error(
          'Batch upload failed for category: $logicalName',
          e,
        );
      }
    });

    await Future.wait(futures);

    if (errors.isNotEmpty) {
      LoggerService.warning(
        'Batch upload completed with ${errors.length} errors: $errors',
      );
    }

    LoggerService.info(
      'Batch upload completed: ${results.length} successful, ${errors.length} failed',
    );

    return results;
  }

  /// Check service health and connectivity
  Future<Map<String, dynamic>> checkServiceHealth() async {
    final healthCheck = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'overall_status': 'healthy',
      'services': <String, dynamic>{},
    };

    try {
      // Check PocketBase connectivity
      try {
        // Try a simple operation to check PocketBase health
        await _pb.collection('_health').getList(page: 1, perPage: 1);
        healthCheck['services']['pocketbase'] = {
          'status': 'healthy',
          'response_time_ms': 0, // Would need to measure actual response time
        };
      } catch (e) {
        // If health collection doesn't exist, try a different approach
        try {
          await _pb.collection('users').getList(page: 1, perPage: 1);
          healthCheck['services']['pocketbase'] = {
            'status': 'healthy',
            'response_time_ms': 0,
          };
        } catch (e2) {
          healthCheck['services']['pocketbase'] = {
            'status': 'unhealthy',
            'error': e2.toString(),
          };
          healthCheck['overall_status'] = 'degraded';
        }
      }

      // Check Google Drive service
      try {
        final driveHealthData = await _driveService.checkServiceHealth();
        final isHealthy =
            driveHealthData['service_initialized'] == true &&
            driveHealthData['authentication_valid'] == true &&
            driveHealthData['api_accessible'] == true;

        healthCheck['services']['google_drive'] = {
          'status': isHealthy ? 'healthy' : 'unhealthy',
          'details': driveHealthData,
        };

        if (!isHealthy) {
          healthCheck['overall_status'] = 'degraded';
        }
      } catch (e) {
        healthCheck['services']['google_drive'] = {
          'status': 'unhealthy',
          'error': e.toString(),
        };
        healthCheck['overall_status'] = 'degraded';
      }

      // Check cache service
      try {
        final cacheStats = _cacheService.getStatistics();
        healthCheck['services']['cache'] = {
          'status': 'healthy',
          'hit_ratio': cacheStats.hitRatioPercentage,
          'total_entries': cacheStats.totalEntries,
          'performance_grade': cacheStats.performanceGrade,
        };
      } catch (e) {
        healthCheck['services']['cache'] = {
          'status': 'unhealthy',
          'error': e.toString(),
        };
        // Cache issues don't affect overall status as much
      }

      // Add storage configuration info
      healthCheck['configuration'] = {
        'primary_storage': _storageConfig.primaryStorage.value,
        'fallback_storage': _storageConfig.fallbackStorage?.value,
        'caching_enabled': _storageConfig.enableCaching,
        'max_retry_attempts': _storageConfig.maxRetryAttempts,
      };

      LoggerService.info(
        'Service health check completed: ${healthCheck['overall_status']}',
      );
      return healthCheck;
    } catch (e) {
      LoggerService.error('Service health check failed', e);
      return {
        'timestamp': DateTime.now().toIso8601String(),
        'overall_status': 'unhealthy',
        'error': e.toString(),
      };
    }
  }

  /// Optimize API usage by batching operations where possible
  Future<List<String>> batchGetFileUrls(List<String> versionFileIds) async {
    LoggerService.info('Batch getting URLs for ${versionFileIds.length} files');

    final urls = <String>[];
    final cacheHits = <String>[];
    final cacheMisses = <String>[];

    // Step 1: Check cache for all URLs
    if (_storageConfig.enableCaching) {
      for (final fileId in versionFileIds) {
        final cachedUrl = await _cacheService.getCachedUrl(fileId);
        if (cachedUrl != null) {
          urls.add(cachedUrl);
          cacheHits.add(fileId);
        } else {
          cacheMisses.add(fileId);
        }
      }
    } else {
      cacheMisses.addAll(versionFileIds);
    }

    LoggerService.debug(
      'Cache results: ${cacheHits.length} hits, ${cacheMisses.length} misses',
    );

    // Step 2: Fetch URLs for cache misses in parallel
    if (cacheMisses.isNotEmpty) {
      final futures = cacheMisses.map((fileId) async {
        try {
          final url = await getFileUrl(fileId);
          return url;
        } catch (e) {
          LoggerService.warning('Failed to get URL for $fileId: $e');
          return null;
        }
      });

      final fetchedUrls = await Future.wait(futures);

      // Add non-null URLs to results
      for (final url in fetchedUrls) {
        if (url != null) {
          urls.add(url);
        }
      }
    }

    LoggerService.info(
      'Batch URL retrieval completed: ${urls.length} URLs obtained',
    );
    return urls;
  }

  /// Get performance metrics for the service
  Future<Map<String, dynamic>> getPerformanceMetrics() async {
    try {
      final cacheStats = _cacheService.getStatistics();

      return {
        'timestamp': DateTime.now().toIso8601String(),
        'cache_performance': {
          'hit_ratio_percentage': cacheStats.hitRatioPercentage,
          'total_hits': cacheStats.totalHits,
          'total_misses': cacheStats.totalMisses,
          'average_access_time_ms': cacheStats.averageAccessTimeMs,
          'performance_grade': cacheStats.performanceGrade,
        },
        'storage_configuration': {
          'primary_storage': _storageConfig.primaryStorage.value,
          'fallback_storage': _storageConfig.fallbackStorage?.value,
          'caching_enabled': _storageConfig.enableCaching,
          'max_file_size_mb':
              _storageConfig.primaryStorage.maxFileSize / (1024 * 1024),
        },
        'service_info': {
          'total_cache_entries': cacheStats.totalEntries,
          'total_cache_size_mb': cacheStats.totalSizeMB,
        },
      };
    } catch (e) {
      LoggerService.error('Failed to get performance metrics', e);
      return {
        'timestamp': DateTime.now().toIso8601String(),
        'error': e.toString(),
      };
    }
  }

  /// Download file with progress tracking
  Future<Uint8List> downloadFile(
    String versionFileId, {
    void Function(int received, int total)? onProgress,
  }) async {
    LoggerService.info('Downloading file: $versionFileId');

    try {
      // Step 1: Determine storage type
      final storageType = StorageUtils.getStorageTypeFromFileId(versionFileId);

      if (storageType == StorageType.googleDrive) {
        return await _downloadFromGoogleDrive(versionFileId, onProgress);
      } else {
        return await _downloadFromPocketBase(versionFileId, onProgress);
      }
    } catch (e) {
      LoggerService.error('Failed to download file: $versionFileId', e);
      rethrow;
    }
  }

  /// Download file from Google Drive
  Future<Uint8List> _downloadFromGoogleDrive(
    String versionFileId,
    void Function(int received, int total)? onProgress,
  ) async {
    try {
      // Find the document record to get Google Drive file ID
      final parsed = StorageUtils.parseVersionFileId(versionFileId);
      if (parsed == null) {
        throw Exception('Invalid version file ID: $versionFileId');
      }

      final fundingApplicationId = parsed['funding_application_id'];
      final logicalName = parsed['logical_name'];

      if (fundingApplicationId == null || logicalName == null) {
        throw Exception(
          'Cannot extract funding application ID or logical name from: $versionFileId',
        );
      }

      // Find the document record
      final records = await _pb
          .collection('claim_documents')
          .getFullList(
            filter:
                'funding_application_id = "$fundingApplicationId" && logical_name = "$logicalName"',
          );

      if (records.isEmpty) {
        throw Exception('Document not found for version: $versionFileId');
      }

      final record = records.first;
      final googleDriveFileIds = record.data['google_drive_file_ids'] as List?;

      if (googleDriveFileIds == null || googleDriveFileIds.isEmpty) {
        throw Exception('No Google Drive file IDs found for: $versionFileId');
      }

      // Use the first Google Drive file ID (could be enhanced to match version)
      final driveFileId = googleDriveFileIds.first.toString();

      // Download from Google Drive
      final bytes = await _driveService.downloadFile(driveFileId);

      // Call progress callback with final result
      onProgress?.call(bytes.length, bytes.length);

      return bytes;
    } catch (e) {
      LoggerService.error(
        'Failed to download from Google Drive: $versionFileId',
        e,
      );
      rethrow;
    }
  }

  /// Download file from PocketBase
  Future<Uint8List> _downloadFromPocketBase(
    String versionFileId,
    void Function(int received, int total)? onProgress,
  ) async {
    try {
      // Parse the version file ID to get record ID and version
      final parts = versionFileId.split('_v');
      if (parts.length != 2) {
        throw Exception('Invalid PocketBase version file ID: $versionFileId');
      }

      final recordId = parts[0];
      final versionIndex = int.parse(parts[1]) - 1; // Convert to 0-based index

      // Get the document record
      final record = await _pb.collection('claim_documents').getOne(recordId);
      final documentFiles = record.data['document_file'];

      List<String> fileNames = [];
      if (documentFiles is List) {
        fileNames = documentFiles.cast<String>();
      } else if (documentFiles is String && documentFiles.isNotEmpty) {
        fileNames = [documentFiles];
      }

      if (versionIndex >= fileNames.length) {
        throw Exception(
          'Version index $versionIndex out of range for record $recordId',
        );
      }

      final fileName = fileNames[versionIndex];
      final fileUrl = _pb.files.getUrl(record, fileName);

      // Download the file with progress tracking
      final response = await http.get(fileUrl);

      if (response.statusCode == 200) {
        // Simple progress callback (since we get the full response at once)
        final bytes = response.bodyBytes;
        onProgress?.call(bytes.length, bytes.length);
        return bytes;
      } else {
        throw Exception('Failed to download file: HTTP ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error(
        'Failed to download from PocketBase: $versionFileId',
        e,
      );
      rethrow;
    }
  }

  /// Enhanced search functionality with Google Drive integration
  Future<List<UploadedDocumentCategory>> searchDocuments({
    required String fundingApplicationId,
    String? searchQuery,
    List<String>? logicalNames,
    DateTime? uploadedAfter,
    DateTime? uploadedBefore,
    String? uploadedBy,
  }) async {
    try {
      LoggerService.info(
        'Searching documents for funding application: $fundingApplicationId',
      );

      // Build filter query
      final filters = <String>[
        'funding_application_id = "$fundingApplicationId"',
      ];

      if (logicalNames != null && logicalNames.isNotEmpty) {
        final nameFilters = logicalNames
            .map((name) => 'logical_name = "$name"')
            .join(' || ');
        filters.add('($nameFilters)');
      }

      if (uploadedBy != null && uploadedBy.isNotEmpty) {
        filters.add('uploaded_by = "$uploadedBy"');
      }

      if (uploadedAfter != null) {
        filters.add('created >= "${uploadedAfter.toIso8601String()}"');
      }

      if (uploadedBefore != null) {
        filters.add('created <= "${uploadedBefore.toIso8601String()}"');
      }

      final filterQuery = filters.join(' && ');

      // Get documents from PocketBase
      final records = await _pb
          .collection('claim_documents')
          .getFullList(filter: filterQuery);

      final categories = <UploadedDocumentCategory>[];

      for (final record in records) {
        try {
          final logicalName = record.data['logical_name'] as String?;
          final currentVersionFileId =
              record.data['current_version_file_id'] as String?;

          if (logicalName == null || currentVersionFileId == null) {
            continue;
          }

          // Apply text search if provided
          if (searchQuery != null && searchQuery.isNotEmpty) {
            final searchLower = searchQuery.toLowerCase();
            final nameMatch = logicalName.toLowerCase().contains(searchLower);

            // Also search in version filenames and notes
            final versions = _parseVersions(record.data['versions']);
            final versionMatch = versions.any(
              (version) =>
                  version.filename.toLowerCase().contains(searchLower) ||
                  (version.notes?.toLowerCase().contains(searchLower) ?? false),
            );

            if (!nameMatch && !versionMatch) {
              continue; // Skip this document if no match found
            }
          }

          final versions = _parseVersions(record.data['versions']);
          final category = UploadedDocumentCategory(
            logicalName: logicalName,
            currentVersionFileId: currentVersionFileId,
            versions: versions,
          );

          categories.add(category);
        } catch (e) {
          LoggerService.warning(
            'Error processing search result for record ${record.id}: $e',
          );
        }
      }

      LoggerService.info(
        'Search completed: found ${categories.length} matching documents',
      );
      return categories;
    } catch (e) {
      LoggerService.error('Document search failed', e);
      return [];
    }
  }

  /// Get document statistics for a funding application
  Future<Map<String, dynamic>> getDocumentStatistics(
    String fundingApplicationId,
  ) async {
    try {
      LoggerService.info(
        'Getting document statistics for funding application: $fundingApplicationId',
      );

      final documents = await getDocumentsForFundingApplication(
        fundingApplicationId,
      );

      final stats = <String, dynamic>{
        'total_categories': documents.length,
        'total_versions': 0,
        'total_files': 0,
        'categories_by_type': <String, int>{},
        'uploads_by_user': <String, int>{},
        'file_extensions': <String, int>{},
        'storage_types': <String, int>{},
        'upload_timeline': <String, int>{},
      };

      for (final category in documents) {
        // Count versions
        stats['total_versions'] += category.versions.length;
        stats['total_files'] += category.versions.length;

        // Count by category type
        final categoryType = category.logicalName;
        stats['categories_by_type'][categoryType] =
            (stats['categories_by_type'][categoryType] ?? 0) + 1;

        for (final version in category.versions) {
          // Count by uploader
          final uploader = version.uploadedBy;
          stats['uploads_by_user'][uploader] =
              (stats['uploads_by_user'][uploader] ?? 0) + 1;

          // Count by file extension
          final extension = version.filename.split('.').last.toLowerCase();
          stats['file_extensions'][extension] =
              (stats['file_extensions'][extension] ?? 0) + 1;

          // Count by storage type
          final storageType =
              StorageUtils.getStorageTypeFromFileId(version.fileId).value;
          stats['storage_types'][storageType] =
              (stats['storage_types'][storageType] ?? 0) + 1;

          // Count by upload date (by month)
          try {
            final uploadDate = DateTime.parse(version.uploadedAt);
            final monthKey =
                '${uploadDate.year}-${uploadDate.month.toString().padLeft(2, '0')}';
            stats['upload_timeline'][monthKey] =
                (stats['upload_timeline'][monthKey] ?? 0) + 1;
          } catch (e) {
            LoggerService.warning(
              'Failed to parse upload date: ${version.uploadedAt}',
            );
          }
        }
      }

      LoggerService.info('Document statistics generated successfully');
      return stats;
    } catch (e) {
      LoggerService.error('Failed to get document statistics', e);
      return {
        'error': e.toString(),
        'total_categories': 0,
        'total_versions': 0,
        'total_files': 0,
      };
    }
  }
}
