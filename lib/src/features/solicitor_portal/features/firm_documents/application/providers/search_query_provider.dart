import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'search_query_provider.g.dart';

/// Provider to hold the current search query string for firm documents.
@riverpod
class SearchQuery extends _$SearchQuery {
  @override
  String build() {
    return ''; // Initial search query is empty
  }

  void setSearchQuery(String query) {
    state = query;
  }

  void clearSearchQuery() {
    state = '';
  }
}