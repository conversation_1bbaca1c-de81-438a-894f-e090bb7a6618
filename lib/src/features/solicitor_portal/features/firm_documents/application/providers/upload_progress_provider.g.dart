// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_progress_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$uploadProgressNotifierHash() =>
    r'8211133c37e9a3ef9166ed5f10f992ca3bbfcc79';

/// Provider for managing the progress of multiple file uploads.
/// The state is a map where keys are file IDs and values are UploadProgressModel.
///
/// Copied from [UploadProgressNotifier].
@ProviderFor(UploadProgressNotifier)
final uploadProgressNotifierProvider = AutoDisposeNotifierProvider<
  UploadProgressNotifier,
  Map<String, UploadProgressModel>
>.internal(
  UploadProgressNotifier.new,
  name: r'uploadProgressNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$uploadProgressNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UploadProgressNotifier =
    AutoDisposeNotifier<Map<String, UploadProgressModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
