import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/upload_progress_model.dart';

part 'upload_progress_provider.g.dart';

/// Provider for managing the progress of multiple file uploads.
/// The state is a map where keys are file IDs and values are UploadProgressModel.
@riverpod
class UploadProgressNotifier extends _$UploadProgressNotifier {
  @override
  Map<String, UploadProgressModel> build() {
    return <String, UploadProgressModel>{};
  }

  /// Initiates an upload or updates its progress.
  void updateUpload(UploadProgressModel upload) {
    state = {
      ...state,
      upload.fileId: upload,
    };
  }

  /// Sets an upload to completed.
  void setUploadCompleted(String fileId) {
    if (state.containsKey(fileId)) {
      state = {
        ...state,
        fileId: state[fileId]!.copyWith(status: UploadStatus.completed, progress: 1.0),
      };
    }
  }

  /// Sets an upload to failed.
  void setUploadFailed(String fileId, String errorMessage) {
    if (state.containsKey(fileId)) {
      state = {
        ...state,
        fileId: state[fileId]!.copyWith(status: UploadStatus.failed, errorMessage: errorMessage),
      };
    }
  }

  /// Sets an upload to cancelled.
  void setUploadCancelled(String fileId) {
    if (state.containsKey(fileId)) {
      state = {
        ...state,
        fileId: state[fileId]!.copyWith(status: UploadStatus.cancelled),
      };
    }
  }

  /// Removes an upload from tracking (e.g., after completion or cancellation acknowledgment).
  void removeUpload(String fileId) {
    state = Map.from(state)..remove(fileId);
  }

  /// Clears all upload tracking.
  void clearAllUploads() {
    state = <String, UploadProgressModel>{};
  }

  /// Gets the progress for a specific file.
  UploadProgressModel? getUploadProgress(String fileId) {
    return state[fileId];
  }
}