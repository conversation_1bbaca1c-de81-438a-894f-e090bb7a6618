import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'selected_documents_provider.g.dart';

/// Provider for managing the set of selected document IDs for bulk actions.
@riverpod
class SelectedDocumentsNotifier extends _$SelectedDocumentsNotifier {
  @override
  Set<String> build() {
    // Initially, no documents are selected
    return <String>{};
  }

  void selectDocument(String documentId) {
    state = {...state, documentId};
  }

  void deselectDocument(String documentId) {
    state = state.where((id) => id != documentId).toSet();
  }

  void toggleDocumentSelection(String documentId) {
    if (state.contains(documentId)) {
      deselectDocument(documentId);
    } else {
      selectDocument(documentId);
    }
  }

  void selectAllDocuments(List<String> allDocumentIds) {
    state = allDocumentIds.toSet();
  }

  void clearSelection() {
    state = <String>{};
  }

  bool isSelected(String documentId) {
    return state.contains(documentId);
  }
}