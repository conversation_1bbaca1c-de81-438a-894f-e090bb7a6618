// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'active_filters_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$activeFiltersNotifierHash() =>
    r'39b267eb8bcd7dff0d0c064e75d528d5cd1509af';

/// Provider for managing the active filters for the firm documents list.
///
/// Copied from [ActiveFiltersNotifier].
@ProviderFor(ActiveFiltersNotifier)
final activeFiltersNotifierProvider =
    AutoDisposeNotifierProvider<ActiveFiltersNotifier, FiltersModel>.internal(
      ActiveFiltersNotifier.new,
      name: r'activeFiltersNotifierProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$activeFiltersNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ActiveFiltersNotifier = AutoDisposeNotifier<FiltersModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
