import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:pocketbase/pocketbase.dart';
import '../../../../../../core/services/pocketbase_service.dart';
import '../models/document_model.dart';

import 'search_query_provider.dart';
import 'active_filters_provider.dart';
import '../models/filters_model.dart';

part 'firm_documents_list_provider.g.dart';

const _itemsPerPage = 15;

/// Provider for managing the list of firm documents, including fetching,
/// pagination, searching, and filtering.
@riverpod
class FirmDocumentsNotifier extends _$FirmDocumentsNotifier {
  int _currentPage = 1;
  bool _isFetching = false;
  bool _hasMore = true;

  // Store the last used query and filters to prevent unnecessary fetches
  // if only the page changes for fetchNextPage.
  String? _lastQuery;
  FiltersModel? _lastFilters;

  @override
  Future<List<DocumentModel>> build() async {
    final searchQuery = ref.watch(searchQueryProvider);
    final activeFilters = ref.watch(activeFiltersNotifierProvider);

    // Reset pagination if query or filters change
    if (searchQuery != _lastQuery || activeFilters != _lastFilters) {
      _currentPage = 1;
      _hasMore = true;
      _lastQuery = searchQuery;
      _lastFilters = activeFilters;
    }
    
    return _fetchPage(_currentPage, searchQuery, activeFilters);
  }

  Future<List<DocumentModel>> _fetchPage(int page, String searchQuery, FiltersModel filters) async {
    // This check is important if _fetchPage is called directly by fetchNextPage
    // build() will handle the _isFetching for initial/filter-changed fetches.
    // if (_isFetching || !_hasMore) return state.value ?? [];
    
    // If called from build, state might not be loaded yet, so can't return state.value
    // For subsequent pages in fetchNextPage, this check is fine.
    if (page > 1 && (_isFetching || !_hasMore)) return state.value ?? [];

    _isFetching = true;

    final pb = ref.read(pocketBaseClientProvider);
    final filterParts = <String>[];
    final Map<String, dynamic> queryParams = {};

    if (searchQuery.isNotEmpty) {
      // Assuming 'name' or 'description' field for search. Adjust as needed.
      // Using OR condition for multiple fields: (field1 ~ {:query} || field2 ~ {:query})
      filterParts.add('(name ~ {:searchQuery} || original_filename ~ {:searchQuery})');
      queryParams['searchQuery'] = searchQuery;
    }

    if (filters.documentType != null && filters.documentType!.isNotEmpty) {
      filterParts.add('type = {:documentType}');
      queryParams['documentType'] = filters.documentType;
    }

    if (filters.status != null && filters.status!.isNotEmpty) {
      filterParts.add('status = {:status}');
      queryParams['status'] = filters.status;
    }

    if (filters.dateFrom != null) {
      filterParts.add('created >= {:dateFrom}');
      queryParams['dateFrom'] = filters.dateFrom!.toIso8601String();
    }

    if (filters.dateTo != null) {
      // Adjust to end of day for inclusive range
      final dateToInclusive = DateTime(filters.dateTo!.year, filters.dateTo!.month, filters.dateTo!.day, 23, 59, 59);
      filterParts.add('created <= {:dateTo}');
      queryParams['dateTo'] = dateToInclusive.toIso8601String();
    }

    String filterString = filterParts.join(' && ');

    try {
      final result = await pb.collection('firm_documents').getList(
            page: page,
            perPage: _itemsPerPage,
            filter: filterString.isNotEmpty ? filterString : null,
            query: queryParams,
            sort: '-created', // Example sort, adjust as needed
          );

      final newDocuments = result.items.map((record) => DocumentModel.fromJson(record.toJson(), record.collectionId)).toList();

      if (newDocuments.length < _itemsPerPage) {
        _hasMore = false;
      }
      return newDocuments;
    } finally {
      _isFetching = false;
    }
  }

  /// Fetches documents, potentially with pagination, search, and filters.
  /// This method might become less central as `build` handles reactive updates.
  /// Kept for explicit refresh or pagination calls if needed outside of widget tree.
  Future<void> fetchDocuments({bool nextPage = false}) async {
    if (nextPage) {
      await fetchNextPage();
    } else {
      await refresh(); // This will trigger build to re-fetch with current filters
    }
  }

  Future<void> fetchNextPage() async {
    if (_isFetching || !_hasMore) return;
    
    _isFetching = true;
    _currentPage++;

    // Read current search and filters for the next page fetch
    final searchQuery = ref.read(searchQueryProvider);
    final activeFilters = ref.read(activeFiltersNotifierProvider);

    try {
      final newItems = await _fetchPage(_currentPage, searchQuery, activeFilters);
      state = AsyncValue.data([...state.value ?? [], ...newItems]);
    } catch (e, s) {
      _currentPage--;
      state = AsyncValue.error(e, s);
    } finally {
      _isFetching = false;
    }
  }

  /// Refreshes the document list.
  Future<void> refresh() async {
    // Resetting these ensures build method fetches fresh from page 1
    _currentPage = 1;
    _hasMore = true;
    // No need to reset _lastQuery and _lastFilters here, build will handle it.
    ref.invalidateSelf(); // This will re-run the build method.
  }

  // Placeholder methods for actions that might be added later
  Future<void> uploadDocument(String filePath) async {
    // TODO: Implement document upload logic
  }

  Future<void> deleteDocument(String documentId) async {
    // TODO: Implement document deletion logic
    // After deletion, refresh the list
    // state = AsyncValue.data(state.value!.where((doc) => doc.id != documentId).toList());
  }

  Future<void> archiveDocument(String documentId) async {
    // TODO: Implement document archiving logic
  }
}