// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'selected_documents_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selectedDocumentsNotifierHash() =>
    r'98b56042ac75e1b14883a3da41da2057e8c4452f';

/// Provider for managing the set of selected document IDs for bulk actions.
///
/// Copied from [SelectedDocumentsNotifier].
@ProviderFor(SelectedDocumentsNotifier)
final selectedDocumentsNotifierProvider = AutoDisposeNotifierProvider<
  SelectedDocumentsNotifier,
  Set<String>
>.internal(
  SelectedDocumentsNotifier.new,
  name: r'selectedDocumentsNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$selectedDocumentsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedDocumentsNotifier = AutoDisposeNotifier<Set<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
