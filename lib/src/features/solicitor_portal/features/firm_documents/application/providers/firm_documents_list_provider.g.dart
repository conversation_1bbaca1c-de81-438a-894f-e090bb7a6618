// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'firm_documents_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$firmDocumentsNotifierHash() =>
    r'c4a3f94db7a126e7a277b219c14f3b426cfb540a';

/// Provider for managing the list of firm documents, including fetching,
/// pagination, searching, and filtering.
///
/// Copied from [FirmDocumentsNotifier].
@ProviderFor(FirmDocumentsNotifier)
final firmDocumentsNotifierProvider = AutoDisposeAsyncNotifierProvider<
  FirmDocumentsNotifier,
  List<DocumentModel>
>.internal(
  FirmDocumentsNotifier.new,
  name: r'firmDocumentsNotifierProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$firmDocumentsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FirmDocumentsNotifier = AutoDisposeAsyncNotifier<List<DocumentModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
