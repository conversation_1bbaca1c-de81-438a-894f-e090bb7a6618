import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/filters_model.dart';

part 'active_filters_provider.g.dart';

/// Provider for managing the active filters for the firm documents list.
@riverpod
class ActiveFiltersNotifier extends _$ActiveFiltersNotifier {
  @override
  FiltersModel build() {
    // Initialize with default (empty) filters
    return FiltersModel();
  }

  /// Updates the entire filter set.
  void setFilters(FiltersModel newFilters) {
    state = newFilters;
  }

  /// Updates a specific filter field.
  void updateFilter({
    String? documentType,
    DateTime? dateFrom,
    DateTime? dateTo,
    String? status,
    bool clearDocumentType = false,
    bool clearDateFrom = false,
    bool clearDateTo = false,
    bool clearStatus = false,
  }) {
    state = state.copyWith(
      documentType: clearDocumentType ? null : documentType ?? state.documentType,
      dateFrom: clearDateFrom ? null : dateFrom ?? state.dateFrom,
      dateTo: clearDateTo ? null : dateTo ?? state.dateTo,
      status: clearStatus ? null : status ?? state.status,
    );
  }

  /// Clears all active filters.
  void clearFilters() {
    state = FiltersModel();
  }
}