import 'dart:io';
import 'package:dio/dio.dart' as dio_package; // Aliasing dio to avoid conflict if http also has a Dio
import 'package:file_picker/file_picker.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/models/document_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/models/upload_progress_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/providers/firm_documents_list_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/providers/upload_progress_provider.dart';
import 'package:http/http.dart' as http; // For http.MultipartFile
import 'package:http_parser/http_parser.dart' show MediaType;


final documentManagementServiceProvider = Provider<DocumentManagementService>((ref) {
  final pocketBase = ref.watch(pocketBaseClientProvider);
  return DocumentManagementService(pocketBase, ref);
});

class DocumentManagementService {
  final PocketBase _pb;
  final Ref _ref;

  DocumentManagementService(this._pb, this._ref);

  Future<void> uploadFile(PlatformFile file, String solicitorProfileId, String? lawFirmName) async {
    final uploadNotifier = _ref.read(uploadProgressNotifierProvider.notifier);
    
    final initialProgress = UploadProgressModel(
      fileId: file.name, // Using filename as a temporary unique ID for progress tracking during initiation
      fileName: file.name,
      status: UploadStatus.uploading,
      progress: 0.0,
    );
    uploadNotifier.updateUpload(initialProgress);

    final userId = _pb.authStore.model?.id;

    try {
      final body = <String, dynamic>{
        'firm': solicitorProfileId, // This is the relation to solicitor_profiles collection
        'firm_name_text': lawFirmName, // New text field for the firm's actual name
        'name': file.name, // Assuming the document name should be the file name
        'type': 'file', // Explicitly setting type as 'file' for uploads
        // 'uploaded_by_user_id': _pb.authStore.model?.id, // Example: if you store who uploaded
      };
      if (userId != null) {
        body['uploaded_by'] = userId;
      }


      final record = await _pb.collection('firm_documents').create(
        body: body,
        files: [
          http.MultipartFile.fromBytes(
            'document_file', // This 'document_file' MUST match your PocketBase collection's file field name
            file.bytes!,
            filename: file.name,
            // contentType: file.extension != null ? MediaType.parse('application/${file.extension}') : null, // http.MultipartFile infers content type
          ),
        ],
      );

      final completedProgress = UploadProgressModel(
        fileId: record.id, // Use actual record ID once created
        fileName: file.name,
        status: UploadStatus.completed,
        progress: 1.0,
      );
      uploadNotifier.updateUpload(completedProgress);
      _ref.invalidate(firmDocumentsNotifierProvider);
    } on ClientException catch (e) {
      final errorProgress = UploadProgressModel(
        fileId: file.name, // Use the initial fileId for error reporting
        fileName: file.name,
        status: UploadStatus.failed,
        progress: 0.0,
        errorMessage: 'Upload failed: ${e.response['message'] ?? e.toString()}',
      );
      uploadNotifier.updateUpload(errorProgress);
      print('PocketBase ClientException during upload: ${e.response}');
    } catch (e) {
      final errorProgress = UploadProgressModel(
        fileId: file.name, // Use the initial fileId for error reporting
        fileName: file.name,
        status: UploadStatus.failed,
        progress: 0.0,
        errorMessage: 'An unexpected error occurred during upload: $e',
      );
      uploadNotifier.updateUpload(errorProgress);
      print('Upload error: $e');
    }
  }

  Future<void> downloadFile(DocumentModel document) async {
    if (document.id.isEmpty || document.collectionId.isEmpty || document.name.isEmpty) {
      print('Error: Document information is incomplete for download. Doc ID: ${document.id}, Collection ID: ${document.collectionId}, Name: ${document.name}');
      return;
    }

    try {
      final dir = await getApplicationDocumentsDirectory();
      final filePath = '${dir.path}/${document.name}';
      
      // Fetch the record to ensure we have the latest data, though getFileUrl can often work with just IDs.
      // However, PocketBase's getFileUrl method expects a RecordModel as the first argument.
      final recordModel = await _pb.collection(document.collectionId).getOne(document.id);
      final fileUrl = _pb.getFileUrl(recordModel, document.name).toString();

      final dio = dio_package.Dio(); // Use the aliased dio
      print('Downloading from: $fileUrl to $filePath');
      await dio.download(
        fileUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            print('Download progress: ${(received / total * 100).toStringAsFixed(0)}% for ${document.name}');
          }
        },
      );

      print('File downloaded to: $filePath');
      final result = await OpenFilex.open(filePath);
      print('OpenFilex result for ${document.name}: ${result.message}, type: ${result.type}');

      if (result.type != ResultType.done) {
        print('Error opening file ${document.name}: ${result.message}');
      }

    } on ClientException catch (e) {
      print('PocketBase download error for ${document.name}: ${e.toString()} / Response: ${e.response}');
    } catch (e) {
      print('Error downloading or opening file ${document.name}: $e');
    }
  }

  Future<void> deleteSelectedDocuments(List<String> documentIds) async {
    // Note: PocketBase does not support bulk delete via a single API call through the SDK directly
    // for arbitrary record IDs. You typically delete one by one or use a custom API endpoint.
    // This implementation will delete them sequentially.
    // Consider backend optimizations (e.g., a custom route) for very large bulk deletions if performance becomes an issue.

    List<String> successfullyDeletedIds = [];
    List<String> failedToDeleteIds = [];

    for (String id in documentIds) {
      try {
        // Before deleting a document, check if it's a folder.
        // If it is, we might need to recursively delete its contents first,
        // or ensure the backend handles cascading deletes if configured.
        // For simplicity, this example assumes direct deletion is sufficient
        // or that folders are handled/prevented from bulk deletion at the UI level if they require special handling.
        
        // To get the document type, we would ideally fetch the record first.
        // However, to avoid multiple reads in a loop for a delete operation,
        // this example proceeds with direct deletion.
        // If distinguishing between file/folder for deletion logic is critical here,
        // the document objects (with their types) should be passed instead of just IDs,
        // or each ID should be fetched.

        await _pb.collection('firm_documents').delete(id);
        successfullyDeletedIds.add(id);
        print('Successfully deleted document: $id');
      } catch (e) {
        failedToDeleteIds.add(id);
        print('Failed to delete document $id: $e');
        // Optionally, collect all errors and throw a composite error at the end,
        // or handle them individually (e.g., log to a more persistent store).
      }
    }

    if (failedToDeleteIds.isNotEmpty) {
      // Throw an error summarizing which documents failed, so the UI can inform the user.
      throw Exception('Failed to delete the following documents: ${failedToDeleteIds.join(', ')}. Please try again or check permissions.');
    }

    // Invalidate relevant providers to refresh UI lists
    // _ref.invalidate(firmDocumentsNotifierProvider); // This was in upload, might be relevant
    // _ref.invalidate(childItemsProvider); // This is from FirmDocumentsPage, likely needs invalidation
    // The actual invalidation should happen in the widget after this service call succeeds.
  }
}