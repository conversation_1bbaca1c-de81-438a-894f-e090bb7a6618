// Placeholder for DocumentModel
// In a real scenario, this would have fields like id, name, type, size, uploadDate, status, etc.
class DocumentModel {
  final String id;
  final String name; // Will be used as fileName
  final DateTime uploadDate;
  final String status; // e.g., "uploaded", "processing", "archived"
  final String type; // e.g., "pdf", "docx"
  final int size; // in bytes
  final String collectionId; // Added for PocketBase interactions
  final DateTime created; // For compatibility with UI
  final DateTime updated; // For compatibility with UI

  DocumentModel({
    required this.id,
    required this.name,
    required this.uploadDate,
    required this.status,
    required this.type,
    required this.size,
    required this.collectionId,
    DateTime? created,
    DateTime? updated,
  }) : created = created ?? uploadDate,
       updated = updated ?? uploadDate;

  factory DocumentModel.fromJson(
    Map<String, dynamic> json,
    String collectionId,
  ) {
    final createdDate =
        DateTime.tryParse(json['created'] as String? ?? '') ?? DateTime.now();
    final updatedDate =
        DateTime.tryParse(json['updated'] as String? ?? '') ?? createdDate;

    return DocumentModel(
      id: json['id'] as String,
      name:
          json['document_file'] as String? ??
          json['name'] as String? ??
          '', // PocketBase file field often named 'file' or specific like 'document_file'
      uploadDate: createdDate,
      status: json['status'] as String? ?? 'uploaded', // Default status
      type:
          (json['document_file'] as String?)?.split('.').last ??
          json['type'] as String? ??
          '', // Infer type from filename
      size:
          json['size'] as int? ??
          0, // PocketBase might not always provide size directly in this context
      collectionId: collectionId,
      created: createdDate,
      updated: updatedDate,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'uploadDate': uploadDate.toIso8601String(),
    'status': status,
    'type': type,
    'size': size,
    'collectionId': collectionId,
  };
}
