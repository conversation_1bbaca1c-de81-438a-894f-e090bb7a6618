enum UploadStatus {
  pending,
  uploading,
  completed,
  failed,
  cancelled,
}

class UploadProgressModel {
  final String fileId; // Unique ID for the file being uploaded
  final String fileName;
  final double progress; // 0.0 to 1.0
  final UploadStatus status;
  final String? errorMessage; // For failed uploads

  UploadProgressModel({
    required this.fileId,
    required this.fileName,
    this.progress = 0.0,
    this.status = UploadStatus.pending,
    this.errorMessage,
  });

  UploadProgressModel copyWith({
    String? fileId,
    String? fileName,
    double? progress,
    UploadStatus? status,
    String? errorMessage,
    bool clearErrorMessage = false,
  }) {
    return UploadProgressModel(
      fileId: fileId ?? this.fileId,
      fileName: fileName ?? this.fileName,
      progress: progress ?? this.progress,
      status: status ?? this.status,
      errorMessage: clearErrorMessage ? null : errorMessage ?? this.errorMessage,
    );
  }

  @override
  String toString() {
    return 'UploadProgressModel(fileId: $fileId, fileName: $fileName, progress: $progress, status: $status, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is UploadProgressModel &&
        other.fileId == fileId &&
        other.fileName == fileName &&
        other.progress == progress &&
        other.status == status &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return fileId.hashCode ^
        fileName.hashCode ^
        progress.hashCode ^
        status.hashCode ^
        errorMessage.hashCode;
  }
}