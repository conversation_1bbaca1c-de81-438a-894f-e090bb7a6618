// Represents the state of active filters
class FiltersModel {
  final String? documentType; // e.g., "pdf", "docx", "image"
  final DateTime? dateFrom;
  final DateTime? dateTo;
  final String? status; // e.g., "uploaded", "processing", "archived"

  FiltersModel({
    this.documentType,
    this.dateFrom,
    this.dateTo,
    this.status,
  });

  FiltersModel copyWith({
    String? documentType,
    DateTime? dateFrom,
    DateTime? dateTo,
    String? status,
  }) {
    return FiltersModel(
      documentType: documentType ?? this.documentType,
      dateFrom: dateFrom ?? this.dateFrom,
      dateTo: dateTo ?? this.dateTo,
      status: status ?? this.status,
    );
  }

  // For easier debugging or logging
  @override
  String toString() {
    return 'FiltersModel(documentType: $documentType, dateFrom: $dateFrom, dateTo: $dateTo, status: $status)';
  }

  // Optional: Add equality and hashCode for comparison if needed
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is FiltersModel &&
        other.documentType == documentType &&
        other.dateFrom == dateFrom &&
        other.dateTo == dateTo &&
        other.status == status;
  }

  @override
  int get hashCode {
    return documentType.hashCode ^
        dateFrom.hashCode ^
        dateTo.hashCode ^
        status.hashCode;
  }
}