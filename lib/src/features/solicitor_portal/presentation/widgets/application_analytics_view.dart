import 'package:flutter/foundation.dart' show kIsWeb; // Added for kIsWeb
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/application/providers/analytics_providers.dart';
import 'package:csv/csv.dart'; // Added for CSV generation
// Conditional import for html, using a stub for non-web platforms
import 'dart:html'
    if (dart.library.io) 'package:three_pay_group_litigation_platform/src/core/utils/html_stub.dart'
    as html;

class ApplicationAnalyticsView extends ConsumerStatefulWidget {
  const ApplicationAnalyticsView({super.key});

  @override
  ConsumerState<ApplicationAnalyticsView> createState() =>
      _ApplicationAnalyticsViewState();
}

class _ApplicationAnalyticsViewState
    extends ConsumerState<ApplicationAnalyticsView> {
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _endDate = DateTime.now();
    _startDate = _endDate!.subtract(const Duration(days: 30));
    // Trigger initial data fetch with default dates
    // Ensure the provider is read after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Check if the widget is still in the tree
        ref.read(applicationAnalyticsProvider(_getAnalyticsFilter()).notifier);
      }
    });
  }

  ApplicationAnalyticsFilter _getAnalyticsFilter() {
    // Renamed for clarity from PRD
    return ApplicationAnalyticsFilter(startDate: _startDate, endDate: _endDate);
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: (isStartDate ? _startDate : _endDate) ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
        // Refresh data when date changes
        ref.refresh(applicationAnalyticsProvider(_getAnalyticsFilter()));
      });
    }
  }

  String _formatDuration(Duration? duration) {
    if (duration == null) return 'N/A';
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    if (duration.inHours > 0) {
      return "${twoDigits(duration.inHours)}h ${twoDigitMinutes}m";
    } else if (duration.inMinutes > 0) {
      return "${twoDigitMinutes}m ${twoDigitSeconds}s";
    } else {
      return "${twoDigitSeconds}s";
    }
  }

  @override
  Widget build(BuildContext context) {
    final analyticsDataAsync = ref.watch(
      applicationAnalyticsProvider(_getAnalyticsFilter()),
    );
    final theme = ShadTheme.of(context);

    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Application Analytics', style: theme.textTheme.h4),
            const SizedBox(height: 16),
            _buildDateRangePicker(context),
            const SizedBox(height: 16), // Adjusted spacing
            analyticsDataAsync.maybeWhen(
              data:
                  (data) => ShadButton(
                    onPressed: () => _exportToCsv(data),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(LucideIcons.download, size: 16),
                        const SizedBox(width: 8),
                        const Text('Export CSV'),
                      ],
                    ),
                  ),
              orElse:
                  () => const ShadButton(
                    enabled: false,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(LucideIcons.download, size: 16),
                        SizedBox(width: 8),
                        Text('Export CSV'),
                      ],
                    ),
                  ),
            ),
            const SizedBox(height: 24),
            Expanded(
              // Allow content to scroll if it overflows
              child: analyticsDataAsync.when(
                data: (data) => _buildAnalyticsContent(context, data),
                loading: () => const Center(child: CircularProgressIndicator()),
                error:
                    (error, stackTrace) => Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Error fetching analytics: $error',
                            style: theme.textTheme.p.copyWith(
                              color: theme.colorScheme.destructive,
                            ),
                          ),
                          const SizedBox(height: 8),
                          ShadButton(
                            child: const Text('Retry'),
                            onPressed:
                                () => ref.refresh(
                                  applicationAnalyticsProvider(
                                    _getAnalyticsFilter(),
                                  ),
                                ),
                          ),
                        ],
                      ),
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _exportToCsv(ApplicationAnalyticsData data) {
    if (kIsWeb) {
      final List<List<dynamic>> rows = [];

      // Header row for KPIs
      rows.add(['KPI', 'Value']);
      rows.add(['Total Submitted', data.totalSubmitted]);
      rows.add([
        'Success Rate',
        '${(data.successRate * 100).toStringAsFixed(1)}%',
      ]);
      rows.add([
        'Avg. Time to Decision',
        _formatDuration(data.averageTimeToDecision),
      ]);
      rows.add([]); // Empty row for separation

      // Header row for Status Breakdown
      rows.add(['Status Breakdown - Status', 'Status Breakdown - Count']);
      data.statusBreakdown.forEach((status, count) {
        rows.add([status, count]);
      });

      final String csvData = const ListToCsvConverter().convert(rows);
      final blob = html.Blob([csvData], 'text/csv;charset=utf-8;');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor =
          html.AnchorElement(href: url)
            ..setAttribute("download", "application_analytics_export.csv")
            ..click();
      html.Url.revokeObjectUrl(url);
    } else {
      // Handle non-web export: e.g., show a message or use a different saving mechanism
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Export Not Available'),
          description: const Text(
            'CSV export is currently only available on web.',
          ),
        ),
      );
      // Alternatively, you could use a package like `file_saver` for cross-platform saving.
      // For now, just showing a toast.
    }
  }

  Widget _buildDateRangePicker(BuildContext context) {
    final dateFormat = DateFormat.yMMMd();
    return Row(
      children: [
        Expanded(
          child: ShadButton.outline(
            child: Text(
              _startDate != null
                  ? dateFormat.format(_startDate!)
                  : 'Select Start Date',
            ),
            onPressed: () => _selectDate(context, true),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ShadButton.outline(
            child: Text(
              _endDate != null
                  ? dateFormat.format(_endDate!)
                  : 'Select End Date',
            ),
            onPressed: () => _selectDate(context, false),
          ),
        ),
      ],
    );
  }

  Widget _buildAnalyticsContent(
    BuildContext context,
    ApplicationAnalyticsData data,
  ) {
    final theme = ShadTheme.of(context);
    return ListView(
      // Changed from Expanded to ListView for direct scrollability
      children: [
        _buildKpiSection(context, data),
        const SizedBox(height: 24),
        Text(
          'Application Status Breakdown',
          style: theme.textTheme.large,
        ), // Using a more generic style
        const SizedBox(height: 16),
        _buildStatusBreakdownChart(context, data.statusBreakdown),
        // Optional: Trends Chart
        // const SizedBox(height: 24),
        // Text('Application Trends', style: theme.textTheme.large),
        // const SizedBox(height: 16),
        // _buildTrendsChart(context, data.trendsData), // Assuming trendsData exists
      ],
    );
  }

  Widget _buildKpiSection(BuildContext context, ApplicationAnalyticsData data) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final crossAxisCount =
            constraints.maxWidth > 700
                ? 3
                : (constraints.maxWidth > 400 ? 2 : 1); // Adjusted breakpoints
        return GridView.count(
          crossAxisCount: crossAxisCount,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio:
              crossAxisCount == 3
                  ? 2.2
                  : (crossAxisCount == 2 ? 2.5 : 3), // Adjusted aspect ratio
          children: [
            _buildKpiCard(
              context,
              'Total Submitted',
              data.totalSubmitted.toString(),
            ),
            _buildKpiCard(
              context,
              'Success Rate',
              '${(data.successRate * 100).toStringAsFixed(1)}%',
            ),
            _buildKpiCard(
              context,
              'Avg. Time to Decision',
              _formatDuration(data.averageTimeToDecision),
            ),
          ],
        );
      },
    );
  }

  Widget _buildKpiCard(BuildContext context, String title, String value) {
    final theme = ShadTheme.of(context);
    return ShadCard(
      title: Text(
        title,
        style: theme.textTheme.muted.copyWith(fontSize: 14),
      ), // Smaller title
      description: Text(
        value,
        style: theme.textTheme.h4.copyWith(
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ), // Slightly smaller value
      // Removed content parameter as it was causing an error and is not needed here
    );
  }

  Widget _buildStatusBreakdownChart(
    BuildContext context,
    Map<String, int> statusBreakdown,
  ) {
    final theme = ShadTheme.of(context);
    final List<PieChartSectionData> sections = [];

    // Predefined list of colors from the theme or default Flutter colors
    final List<Color> chartColors = [
      theme.colorScheme.primary,
      theme.colorScheme.secondary,
      Colors.green.shade400, // Using Material colors as fallback
      Colors.orange.shade400,
      Colors.purple.shade400,
      Colors.teal.shade400,
      theme.colorScheme.destructive, // For destructive states if any
    ];

    int colorIndex = 0;
    for (var entry in statusBreakdown.entries) {
      final isTouched = false; // Placeholder for interactivity
      final fontSize = isTouched ? 15.0 : 13.0; // Adjusted font size
      final radius = isTouched ? 65.0 : 55.0; // Adjusted radius

      sections.add(
        PieChartSectionData(
          color: chartColors[colorIndex % chartColors.length],
          value: entry.value.toDouble(),
          title: '${entry.key}\n(${entry.value})',
          radius: radius,
          titleStyle: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.foreground.withOpacity(
              0.8,
            ), // Ensure contrast
            shadows: const [Shadow(color: Colors.black12, blurRadius: 1)],
          ),
          titlePositionPercentageOffset: 0.6, // Adjusted position
        ),
      );
      colorIndex++;
    }

    if (sections.isEmpty) {
      return const Center(child: Text('No status data available.'));
    }

    return SizedBox(
      height: 280, // Increased height for better visibility
      child: PieChart(
        PieChartData(
          sections: sections,
          borderData: FlBorderData(show: false),
          sectionsSpace: 2,
          centerSpaceRadius: 45, // Adjusted center space
          pieTouchData: PieTouchData(
            touchCallback: (FlTouchEvent event, pieTouchResponse) {
              // Handle touch events if needed for interactivity
            },
          ),
        ),
      ),
    );
  }

  // Placeholder for Trends Chart - implement if time-series data is available
  // Widget _buildTrendsChart(BuildContext context, Map<DateTime, int> trendsData) {
  //   // Implement LineChart using fl_chart
  //   return const SizedBox(height: 200, child: Center(child: Text('Trends Chart Placeholder')));
  // }
}
