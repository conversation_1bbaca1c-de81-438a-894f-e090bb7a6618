import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/solicitor_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/providers/solicitor_profile_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/widgets/solicitor_profile_picture_widget.dart';

/// Widget for displaying solicitor profile information
class SolicitorProfileInfoWidget extends ConsumerWidget {
  final VoidCallback? onEditPressed;

  const SolicitorProfileInfoWidget({super.key, this.onEditPressed});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(solicitorProfileProvider);

    if (profileState.isLoading) {
      return const Center(child: CircularProgressIndicator.adaptive());
    }

    if (profileState.error != null) {
      return ShadCard(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Text(
            'Error loading profile: ${profileState.error}',
            style: theme.textTheme.p.copyWith(
              color: theme.colorScheme.destructive,
            ),
          ),
        ),
      );
    }

    final profile = profileState.profile;
    if (profile == null) {
      return ShadCard(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Text(
            'No profile information available',
            style: theme.textTheme.p,
          ),
        ),
      );
    }

    return ShadCard(
      title: Text('Profile Information', style: theme.textTheme.h4),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile picture and basic info
            LayoutBuilder(
              builder: (context, constraints) {
                // Use column layout for narrow screens
                if (constraints.maxWidth < 400) {
                  return Column(
                    children: [
                      // Profile picture centered
                      const SolicitorProfilePictureWidget(
                        size: 80,
                        showEditButton: false,
                      ),
                      const SizedBox(height: 16),
                      // Basic info
                      _buildBasicInfo(profile, theme),
                    ],
                  );
                } else {
                  // Use row layout for wider screens
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Profile picture
                      const SolicitorProfilePictureWidget(
                        size: 100,
                        showEditButton: false,
                      ),
                      const SizedBox(width: 24),
                      // Basic info
                      Expanded(child: _buildBasicInfo(profile, theme)),
                    ],
                  );
                }
              },
            ),

            const SizedBox(height: 32),

            // Edit button
            if (onEditPressed != null)
              ShadButton(
                onPressed: onEditPressed,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(LucideIcons.pencil, size: 16),
                    const SizedBox(width: 8),
                    const Text('Edit Profile'),
                  ],
                ),
              ),

            const SizedBox(height: 32),

            // Professional Information Section
            _buildSection('Professional Information', [
              _buildInfoRow(
                'Solicitor Name',
                profile.solicitorName,
                LucideIcons.user,
              ),
              _buildInfoRow(
                'Law Firm',
                profile.lawFirmName,
                LucideIcons.building,
              ),
              _buildInfoRow(
                'Position',
                profile.positionInFirm ?? 'Not provided',
                LucideIcons.briefcase,
              ),
              _buildInfoRow(
                'SRA Number',
                profile.sraNumber.isNotEmpty
                    ? profile.sraNumber
                    : 'Not provided',
                LucideIcons.fileText,
              ),
            ], theme),

            const SizedBox(height: 24),

            // Contact Information Section
            _buildSection('Contact Information', [
              _buildInfoRow(
                'Email',
                profile.userEmail ?? 'Not provided',
                LucideIcons.mail,
              ),
              _buildInfoRow(
                'Phone Number',
                profile.contactNumber ?? 'Not provided',
                LucideIcons.phone,
              ),
              _buildInfoRow(
                'Firm Address',
                profile.firmAddress,
                LucideIcons.mapPin,
              ),
            ], theme),

            const SizedBox(height: 24),

            // Account Information Section
            _buildSection('Account Information', [
              // Only show PU Status if not approved (hide for approved solicitors)
              if (profile.puStatus?.toLowerCase() != 'approved')
                _buildInfoRow(
                  'PU Status',
                  profile.puStatus ?? 'Not provided',
                  LucideIcons.shield,
                ),
              _buildInfoRow(
                'Member Since',
                _formatDate(profile.created),
                LucideIcons.calendar,
              ),
              _buildInfoRow(
                'Last Updated',
                _formatDate(profile.updated),
                LucideIcons.clock,
              ),
            ], theme),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfo(SolicitorProfileModel profile, ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(profile.solicitorName, style: theme.textTheme.h3),
        const SizedBox(height: 4),
        Text(
          profile.lawFirmName,
          style: theme.textTheme.large.copyWith(
            color: theme.colorScheme.mutedForeground,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          profile.positionInFirm ?? 'Solicitor',
          style: theme.textTheme.p.copyWith(
            color: theme.colorScheme.mutedForeground,
          ),
        ),
      ],
    );
  }

  Widget _buildSection(
    String title,
    List<Widget> children,
    ShadThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: theme.textTheme.h4),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Builder(
      builder: (context) {
        final theme = ShadTheme.of(context);
        return Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(icon, size: 16, color: theme.colorScheme.mutedForeground),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(value, style: theme.textTheme.p),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.tryParse(dateString);
      if (date != null) {
        return DateFormat('MMM dd, yyyy').format(date);
      }
      return dateString;
    } catch (e) {
      return dateString;
    }
  }
}
