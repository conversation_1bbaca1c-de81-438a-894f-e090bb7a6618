import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as li;
import 'package:file_picker/file_picker.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/toast_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/storage_type.dart';
import 'package:three_pay_group_litigation_platform/src/shared/presentation/widgets/storage_indicator_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/providers/upload_progress_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/models/upload_progress_model.dart';

/// Enhanced document upload widget with Google Drive integration support
class DocumentUploadWidget extends ConsumerStatefulWidget {
  final String? claimId;
  final String? folderId;
  final List<String> allowedExtensions;
  final int maxFileSizeMB;
  final bool allowMultiple;
  final Function(List<PlatformFile>)? onFilesSelected;
  final Function(String fileId, double progress)? onUploadProgress;
  final Function(String fileId, String? error)? onUploadComplete;

  const DocumentUploadWidget({
    super.key,
    this.claimId,
    this.folderId,
    this.allowedExtensions = const ['pdf', 'jpg', 'jpeg', 'png', 'gif'],
    this.maxFileSizeMB = 10,
    this.allowMultiple = true,
    this.onFilesSelected,
    this.onUploadProgress,
    this.onUploadComplete,
  });

  @override
  ConsumerState<DocumentUploadWidget> createState() =>
      _DocumentUploadWidgetState();
}

class _DocumentUploadWidgetState extends ConsumerState<DocumentUploadWidget> {
  bool _isDragOver = false;
  bool _isUploading = false;

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final uploadProgress = ref.watch(uploadProgressNotifierProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildUploadArea(theme),
        const SizedBox(height: 16),
        _buildUploadGuidelines(theme),
        if (uploadProgress.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildActiveUploads(theme, uploadProgress),
        ],
      ],
    );
  }

  Widget _buildUploadArea(ShadThemeData theme) {
    return GestureDetector(
      onTap: _pickFiles,
      child: DragTarget<List<PlatformFile>>(
        onWillAccept: (data) => data != null,
        onAccept: (files) => _handleFilesSelected(files),
        onMove: (details) => setState(() => _isDragOver = true),
        onLeave: (data) => setState(() => _isDragOver = false),
        builder: (context, candidateData, rejectedData) {
          return Container(
            height: 120,
            decoration: BoxDecoration(
              border: Border.all(
                color:
                    _isDragOver
                        ? theme.colorScheme.primary
                        : theme.colorScheme.border,
                width: _isDragOver ? 2 : 1,
                style: BorderStyle.solid,
              ),
              borderRadius: theme.radius,
              color:
                  _isDragOver
                      ? theme.colorScheme.primary.withValues(alpha: 0.05)
                      : theme.colorScheme.card,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _isUploading
                      ? li.LucideIcons.loader2
                      : li.LucideIcons.uploadCloud,
                  size: 32,
                  color:
                      _isDragOver
                          ? theme.colorScheme.primary
                          : theme.colorScheme.mutedForeground,
                ),
                const SizedBox(height: 8),
                Text(
                  _isUploading
                      ? 'Uploading to Google Drive...'
                      : 'Drop files here or click to browse',
                  style: theme.textTheme.large.copyWith(
                    color:
                        _isDragOver
                            ? theme.colorScheme.primary
                            : theme.colorScheme.foreground,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const StorageIndicatorWidget(
                      storageType: StorageType.googleDrive,
                      showLabel: true,
                      iconSize: 14,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Primary storage',
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildUploadGuidelines(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.3),
        borderRadius: theme.radius,
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                li.LucideIcons.info,
                size: 16,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Upload Guidelines',
                style: theme.textTheme.small.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.foreground,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildGuideline(
            theme,
            'Supported formats: ${widget.allowedExtensions.join(', ').toUpperCase()}',
          ),
          _buildGuideline(
            theme,
            'Maximum file size: ${widget.maxFileSizeMB}MB per file',
          ),
          _buildGuideline(theme, 'Files are securely stored in Google Drive'),
          _buildGuideline(theme, 'Upload progress is tracked in real-time'),
        ],
      ),
    );
  }

  Widget _buildGuideline(ShadThemeData theme, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            li.LucideIcons.check,
            size: 12,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: theme.textTheme.small.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveUploads(
    ShadThemeData theme,
    Map<String, UploadProgressModel> uploads,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: theme.radius,
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                li.LucideIcons.activity,
                size: 16,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Upload Progress',
                style: theme.textTheme.small.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.foreground,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...uploads.values.map((upload) => _buildUploadItem(theme, upload)),
        ],
      ),
    );
  }

  Widget _buildUploadItem(ShadThemeData theme, UploadProgressModel upload) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          StorageIndicatorWidget(
            storageType: StorageType.googleDrive,
            isUploading: upload.status == UploadStatus.uploading,
            uploadProgress: upload.progress,
            errorMessage: upload.errorMessage,
            showLabel: false,
            iconSize: 14,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  upload.fileName,
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.foreground,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                if (upload.status == UploadStatus.uploading) ...[
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: upload.progress,
                    backgroundColor: theme.colorScheme.muted,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.primary,
                    ),
                    minHeight: 4,
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(width: 8),
          _buildUploadStatusIcon(theme, upload),
        ],
      ),
    );
  }

  Widget _buildUploadStatusIcon(
    ShadThemeData theme,
    UploadProgressModel upload,
  ) {
    IconData icon;
    Color color;

    switch (upload.status) {
      case UploadStatus.pending:
        icon = li.LucideIcons.clock;
        color = theme.colorScheme.mutedForeground;
        break;
      case UploadStatus.uploading:
        icon = li.LucideIcons.loader2;
        color = theme.colorScheme.primary;
        break;
      case UploadStatus.completed:
        icon = li.LucideIcons.checkCircle2;
        color = Colors.green;
        break;
      case UploadStatus.failed:
        icon = li.LucideIcons.xCircle;
        color = theme.colorScheme.destructive;
        break;
      case UploadStatus.cancelled:
        icon = li.LucideIcons.ban;
        color = theme.colorScheme.mutedForeground;
        break;
    }

    return Icon(icon, size: 16, color: color);
  }

  Future<void> _pickFiles() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: widget.allowedExtensions,
        allowMultiple: widget.allowMultiple,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        await _handleFilesSelected(result.files);
      }
    } catch (e) {
      LoggerService.error('Error picking files', e);
      if (mounted) {
        ToastService.showError(
          context,
          'Failed to select files: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _handleFilesSelected(List<PlatformFile> files) async {
    // Validate files
    final validFiles = <PlatformFile>[];

    for (final file in files) {
      if (_validateFile(file)) {
        validFiles.add(file);
      }
    }

    if (validFiles.isEmpty) return;

    // Notify parent about selected files
    widget.onFilesSelected?.call(validFiles);

    // Start upload process
    setState(() => _isUploading = true);

    try {
      for (final file in validFiles) {
        await _uploadFile(file);
      }
    } finally {
      setState(() => _isUploading = false);
    }
  }

  bool _validateFile(PlatformFile file) {
    // Check file size
    if (file.size > widget.maxFileSizeMB * 1024 * 1024) {
      ToastService.showError(
        context,
        'File "${file.name}" exceeds ${widget.maxFileSizeMB}MB limit',
      );
      return false;
    }

    // Check file extension
    final extension = file.extension?.toLowerCase();
    if (extension == null || !widget.allowedExtensions.contains(extension)) {
      ToastService.showError(
        context,
        'File "${file.name}" has unsupported format. Only PDF and image files (PDF, JPG, JPEG, PNG, GIF) are allowed.',
      );
      return false;
    }

    return true;
  }

  Future<void> _uploadFile(PlatformFile file) async {
    final uploadNotifier = ref.read(uploadProgressNotifierProvider.notifier);

    // Initialize upload progress
    final progressModel = UploadProgressModel(
      fileId: file.name, // Temporary ID
      fileName: file.name,
      status: UploadStatus.uploading,
      progress: 0.0,
    );

    uploadNotifier.updateUpload(progressModel);

    try {
      // Simulate upload progress (replace with actual Google Drive upload)
      for (int i = 0; i <= 100; i += 10) {
        await Future.delayed(const Duration(milliseconds: 200));

        uploadNotifier.updateUpload(
          progressModel.copyWith(progress: i / 100.0),
        );

        widget.onUploadProgress?.call(file.name, i / 100.0);
      }

      // Mark as completed
      uploadNotifier.setUploadCompleted(file.name);
      widget.onUploadComplete?.call(file.name, null);

      if (mounted) {
        ToastService.showSuccess(
          context,
          'Successfully uploaded "${file.name}" to Google Drive',
        );
      }
    } catch (e) {
      LoggerService.error('Upload failed for ${file.name}', e);

      uploadNotifier.setUploadFailed(file.name, e.toString());
      widget.onUploadComplete?.call(file.name, e.toString());

      if (mounted) {
        ToastService.showError(
          context,
          'Failed to upload "${file.name}": ${e.toString()}',
        );
      }
    }
  }
}
