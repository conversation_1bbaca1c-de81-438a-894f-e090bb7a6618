import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as li;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/toast_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/storage_type.dart';
import 'package:three_pay_group_litigation_platform/src/shared/presentation/widgets/storage_indicator_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/models/document_model.dart';

/// Enhanced documents list widget with Google Drive integration support
class DocumentsListWidget extends ConsumerStatefulWidget {
  final List<DocumentModel> documents;
  final bool isLoading;
  final String? errorMessage;
  final Function(DocumentModel)? onDocumentTap;
  final Function(DocumentModel)? onDownload;
  final Function(DocumentModel)? onDelete;
  final Function(DocumentModel)? onShare;
  final Function()? onRefresh;
  final bool showStorageInfo;
  final bool allowSelection;

  const DocumentsListWidget({
    super.key,
    required this.documents,
    this.isLoading = false,
    this.errorMessage,
    this.onDocumentTap,
    this.onDownload,
    this.onDelete,
    this.onShare,
    this.onRefresh,
    this.showStorageInfo = true,
    this.allowSelection = false,
  });

  @override
  ConsumerState<DocumentsListWidget> createState() =>
      _DocumentsListWidgetState();
}

class _DocumentsListWidgetState extends ConsumerState<DocumentsListWidget> {
  final Set<String> _selectedDocuments = <String>{};
  final Map<String, bool> _downloadingDocuments = <String, bool>{};

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    if (widget.isLoading && widget.documents.isEmpty) {
      return _buildLoadingState(theme);
    }

    if (widget.errorMessage != null) {
      return _buildErrorState(theme);
    }

    if (widget.documents.isEmpty) {
      return _buildEmptyState(theme);
    }

    return RefreshIndicator(
      onRefresh: () async {
        widget.onRefresh?.call();
      },
      child: ListView.builder(
        itemCount: widget.documents.length,
        itemBuilder: (context, index) {
          final document = widget.documents[index];
          return _buildDocumentItem(theme, document);
        },
      ),
    );
  }

  Widget _buildLoadingState(ShadThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Loading documents from Google Drive...',
            style: theme.textTheme.large.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(ShadThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            li.LucideIcons.alertCircle,
            size: 48,
            color: theme.colorScheme.destructive,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading documents',
            style: theme.textTheme.h4.copyWith(
              color: theme.colorScheme.foreground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.errorMessage!,
            style: theme.textTheme.large.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ShadButton(onPressed: widget.onRefresh, child: const Text('Retry')),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ShadThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            li.LucideIcons.fileText,
            size: 48,
            color: theme.colorScheme.mutedForeground,
          ),
          const SizedBox(height: 16),
          Text(
            'No documents found',
            style: theme.textTheme.h4.copyWith(
              color: theme.colorScheme.foreground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Upload your first document to get started',
            style: theme.textTheme.large.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentItem(ShadThemeData theme, DocumentModel document) {
    final isSelected = _selectedDocuments.contains(document.id);
    final isDownloading = _downloadingDocuments[document.id] ?? false;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: ListTile(
        leading: _buildDocumentLeading(theme, document, isSelected),
        title: _buildDocumentTitle(theme, document),
        subtitle: _buildDocumentSubtitle(theme, document),
        trailing: _buildDocumentActions(theme, document, isDownloading),
        onTap: () => _handleDocumentTap(document),
        selected: isSelected,
      ),
    );
  }

  Widget _buildDocumentLeading(
    ShadThemeData theme,
    DocumentModel document,
    bool isSelected,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.allowSelection) ...[
          Checkbox(
            value: isSelected,
            onChanged: (value) => _toggleSelection(document.id),
          ),
          const SizedBox(width: 8),
        ],
        Icon(
          _getDocumentIcon(document),
          size: 32,
          color: theme.colorScheme.primary,
        ),
      ],
    );
  }

  Widget _buildDocumentTitle(ShadThemeData theme, DocumentModel document) {
    return Row(
      children: [
        Expanded(
          child: Text(
            document.name,
            style: theme.textTheme.large.copyWith(fontWeight: FontWeight.w500),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (widget.showStorageInfo) ...[
          const SizedBox(width: 8),
          CompactStorageIndicatorWidget(
            storageType: StorageType.googleDrive,
            isProcessing: _downloadingDocuments[document.id] ?? false,
          ),
        ],
      ],
    );
  }

  Widget _buildDocumentSubtitle(ShadThemeData theme, DocumentModel document) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Uploaded: ${_formatDate(document.created)} | Size: ${_formatFileSize(document.size)}',
          style: theme.textTheme.small.copyWith(
            color: theme.colorScheme.mutedForeground,
          ),
        ),
        if (widget.showStorageInfo) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(li.LucideIcons.cloud, size: 12, color: Colors.blue),
              const SizedBox(width: 4),
              Text(
                'Google Drive',
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                  fontSize: 11,
                ),
              ),
              const SizedBox(width: 12),
              Icon(
                li.LucideIcons.clock,
                size: 12,
                color: theme.colorScheme.mutedForeground,
              ),
              const SizedBox(width: 4),
              Text(
                'Modified: ${_formatDate(document.updated)}',
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildDocumentActions(
    ShadThemeData theme,
    DocumentModel document,
    bool isDownloading,
  ) {
    return PopupMenuButton<String>(
      icon: Icon(
        isDownloading ? li.LucideIcons.loader2 : li.LucideIcons.moreVertical,
        size: 16,
      ),
      onSelected: (value) => _handleAction(value, document),
      itemBuilder:
          (context) => [
            PopupMenuItem(
              value: 'download',
              child: Row(
                children: [
                  Icon(
                    li.LucideIcons.download,
                    size: 16,
                    color: theme.colorScheme.foreground,
                  ),
                  const SizedBox(width: 8),
                  const Text('Download'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(
                    li.LucideIcons.share2,
                    size: 16,
                    color: theme.colorScheme.foreground,
                  ),
                  const SizedBox(width: 8),
                  const Text('Share'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'view_in_drive',
              child: Row(
                children: [
                  Icon(
                    li.LucideIcons.externalLink,
                    size: 16,
                    color: theme.colorScheme.foreground,
                  ),
                  const SizedBox(width: 8),
                  const Text('View in Google Drive'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(
                    li.LucideIcons.trash2,
                    size: 16,
                    color: theme.colorScheme.destructive,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Delete',
                    style: TextStyle(color: theme.colorScheme.destructive),
                  ),
                ],
              ),
            ),
          ],
    );
  }

  IconData _getDocumentIcon(DocumentModel document) {
    final extension = document.name.split('.').last.toLowerCase();

    switch (extension) {
      case 'pdf':
        return li.LucideIcons.fileText;
      case 'doc':
      case 'docx':
        return li.LucideIcons.fileText;
      case 'xls':
      case 'xlsx':
        return li.LucideIcons.fileSpreadsheet;
      case 'ppt':
      case 'pptx':
        return li.LucideIcons.presentation;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return li.LucideIcons.image;
      default:
        return li.LucideIcons.file;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';

    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 7) {
      return '${date.day}/${date.month}/${date.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else {
      return 'Just now';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  void _toggleSelection(String documentId) {
    setState(() {
      if (_selectedDocuments.contains(documentId)) {
        _selectedDocuments.remove(documentId);
      } else {
        _selectedDocuments.add(documentId);
      }
    });
  }

  void _handleDocumentTap(DocumentModel document) {
    if (widget.allowSelection) {
      _toggleSelection(document.id);
    } else {
      widget.onDocumentTap?.call(document);
    }
  }

  void _handleAction(String action, DocumentModel document) {
    switch (action) {
      case 'download':
        _downloadDocument(document);
        break;
      case 'share':
        widget.onShare?.call(document);
        break;
      case 'view_in_drive':
        _viewInGoogleDrive(document);
        break;
      case 'delete':
        _confirmDelete(document);
        break;
    }
  }

  void _downloadDocument(DocumentModel document) {
    setState(() {
      _downloadingDocuments[document.id] = true;
    });

    widget.onDownload?.call(document);

    // Simulate download completion (replace with actual implementation)
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _downloadingDocuments[document.id] = false;
        });

        ToastService.showSuccess(
          context,
          'Downloaded "${document.name}" from Google Drive',
        );
      }
    });
  }

  void _viewInGoogleDrive(DocumentModel document) {
    // TODO: Implement Google Drive URL opening
    ToastService.showInfo(
      context,
      'Opening "${document.name}" in Google Drive...',
    );
  }

  void _confirmDelete(DocumentModel document) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Document'),
            content: Text(
              'Are you sure you want to delete "${document.name}"?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  widget.onDelete?.call(document);
                },
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }
}
