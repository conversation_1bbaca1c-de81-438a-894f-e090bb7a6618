import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/utils/solicitor_profile_validation.dart';
import '../providers/solicitor_profile_provider.dart';
import 'solicitor_profile_picture_widget.dart';

/// Form widget for editing solicitor profile information
class SolicitorProfileEditForm extends ConsumerStatefulWidget {
  final VoidCallback? onSaved;
  final VoidCallback? onCancelled;

  const SolicitorProfileEditForm({super.key, this.onSaved, this.onCancelled});

  @override
  ConsumerState<SolicitorProfileEditForm> createState() =>
      _SolicitorProfileEditFormState();
}

class _SolicitorProfileEditFormState
    extends ConsumerState<SolicitorProfileEditForm> {
  final _formKey = GlobalKey<ShadFormState>();

  // Form controllers
  late final TextEditingController _nameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  late final TextEditingController _solicitorNameController;
  late final TextEditingController _lawFirmNameController;
  late final TextEditingController _sraNumberController;
  late final TextEditingController _firmAddressController;
  late final TextEditingController _contactNumberController;
  late final TextEditingController _positionController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadProfileData();
  }

  void _initializeControllers() {
    _nameController = TextEditingController();
    _emailController = TextEditingController();
    _phoneController = TextEditingController();
    _solicitorNameController = TextEditingController();
    _lawFirmNameController = TextEditingController();
    _sraNumberController = TextEditingController();
    _firmAddressController = TextEditingController();
    _contactNumberController = TextEditingController();
    _positionController = TextEditingController();
  }

  void _loadProfileData() {
    final profileState = ref.read(solicitorProfileProvider);
    final profile = profileState.profile;

    if (profile != null) {
      // Load professional information (editable)
      _solicitorNameController.text = profile.solicitorName;
      _lawFirmNameController.text = profile.lawFirmName;
      _sraNumberController.text = profile.sraNumber;
      _firmAddressController.text = profile.firmAddress;
      _contactNumberController.text = profile.contactNumber ?? '';
      _positionController.text = profile.positionInFirm ?? '';

      // Load personal information (read-only) from expanded user data
      _nameController.text = profile.userName ?? '';
      _emailController.text = profile.userEmail ?? '';
      _phoneController.text =
          ''; // Phone number not available in solicitor model
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _solicitorNameController.dispose();
    _lawFirmNameController.dispose();
    _sraNumberController.dispose();
    _firmAddressController.dispose();
    _contactNumberController.dispose();
    _positionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(solicitorProfileProvider);

    return ShadCard(
      title: Text('Edit Profile', style: theme.textTheme.h4),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: ShadForm(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Picture Section
              Center(
                child: SolicitorProfilePictureWidget(
                  size: 120,
                  showEditButton: true,
                  // Don't override onEditPressed - let the widget handle it internally
                ),
              ),

              const SizedBox(height: 32),

              // Personal Information Section
              Text('Personal Information', style: theme.textTheme.h4),
              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'name',
                controller: _nameController,
                label: const Text('Full Name'),
                placeholder: const Text('Enter your full name'),
                validator:
                    (value) => SolicitorProfileValidation.validateName(value),
                enabled: false, // Personal information is read-only
                decoration: ShadDecoration(color: theme.colorScheme.muted),
              ),

              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'email',
                controller: _emailController,
                label: const Text('Email Address'),
                placeholder: const Text('Enter your email address'),
                keyboardType: TextInputType.emailAddress,
                validator: SolicitorProfileValidation.validateEmail,
                enabled: false, // Personal information is read-only
                decoration: ShadDecoration(color: theme.colorScheme.muted),
              ),

              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'phone',
                controller: _phoneController,
                label: const Text('Phone Number'),
                placeholder: const Text('Enter your phone number'),
                keyboardType: TextInputType.phone,
                validator: SolicitorProfileValidation.validatePhoneNumber,
                enabled: false, // Personal information is read-only
                decoration: ShadDecoration(color: theme.colorScheme.muted),
              ),

              const SizedBox(height: 24),

              // Professional Information Section
              Text('Professional Information', style: theme.textTheme.h4),
              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'solicitorName',
                controller: _solicitorNameController,
                label: const Text('Solicitor Name'),
                placeholder: const Text('Enter solicitor name'),
                validator: SolicitorProfileValidation.validateSolicitorName,
              ),

              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'lawFirmName',
                controller: _lawFirmNameController,
                label: const Text('Law Firm Name'),
                placeholder: const Text('Enter law firm name'),
                validator: SolicitorProfileValidation.validateLawFirmName,
              ),

              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'sraNumber',
                controller: _sraNumberController,
                label: const Text('SRA Number'),
                placeholder: const Text('Enter SRA number (optional)'),
                validator: SolicitorProfileValidation.validateSraNumber,
              ),

              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'position',
                controller: _positionController,
                label: const Text('Position in Firm'),
                placeholder: const Text('Enter your position (optional)'),
                validator: SolicitorProfileValidation.validatePositionInFirm,
              ),

              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'contactNumber',
                controller: _contactNumberController,
                label: const Text('Contact Number'),
                placeholder: const Text('Enter contact number (optional)'),
                keyboardType: TextInputType.phone,
                validator: SolicitorProfileValidation.validateContactNumber,
              ),

              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'firmAddress',
                controller: _firmAddressController,
                label: const Text('Firm Address'),
                placeholder: const Text('Enter firm address'),
                maxLines: 3,
                validator: SolicitorProfileValidation.validateFirmAddress,
              ),

              const SizedBox(height: 32),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: ShadButton.outline(
                      onPressed: profileState.isUpdating ? null : _handleCancel,
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ShadButton(
                      onPressed: profileState.isUpdating ? null : _handleSave,
                      child:
                          profileState.isUpdating
                              ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text('Saving...'),
                                ],
                              )
                              : const Text('Save Changes'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleCancel() {
    if (widget.onCancelled != null) {
      widget.onCancelled!();
    } else {
      Navigator.of(context).pop();
    }
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Personal information fields are read-only, so we only update the solicitor profile

    // Update solicitor profile
    final success = await ref
        .read(solicitorProfileProvider.notifier)
        .updateProfile(
          solicitorName: _solicitorNameController.text.trim(),
          lawFirmName: _lawFirmNameController.text.trim(),
          sraNumber: _sraNumberController.text.trim(),
          firmAddress: _firmAddressController.text.trim(),
          contactNumber: _contactNumberController.text.trim(),
          positionInFirm: _positionController.text.trim(),
        );

    if (mounted && success) {
      ShadToaster.of(context).show(
        const ShadToast(
          title: Text('Success'),
          description: Text('Profile updated successfully'),
        ),
      );

      if (widget.onSaved != null) {
        widget.onSaved!();
      } else {
        Navigator.of(context).pop();
      }
    }
  }
}
