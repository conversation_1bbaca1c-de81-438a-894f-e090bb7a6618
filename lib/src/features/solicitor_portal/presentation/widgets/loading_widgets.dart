import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../utils/responsive_layout.dart';

/// Loading widgets for the solicitor portal
/// Provides consistent loading states and skeleton screens
class SolicitorLoadingWidgets {
  /// Dashboard stats loading skeleton
  static Widget dashboardStatsLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isDesktop = SolicitorResponsiveLayout.isDesktop(context);
    final isTablet = SolicitorResponsiveLayout.isTablet(context);
    
    final columns = SolicitorResponsiveLayout.getStatCardColumns(context);
    final spacing = SolicitorResponsiveLayout.getCardSpacing(context);

    return Padding(
      padding: SolicitorResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome text skeleton
          _buildShimmerContainer(
            width: isDesktop ? 300 : 250,
            height: 32,
            theme: theme,
          ),
          SizedBox(height: SolicitorResponsiveLayout.getSectionSpacing(context)),
          
          // Stats cards skeleton
          if (isDesktop || isTablet)
            Row(
              children: List.generate(
                columns,
                (index) => Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(
                      right: index < columns - 1 ? spacing : 0,
                    ),
                    child: _buildStatCardSkeleton(context, theme),
                  ),
                ),
              ),
            )
          else
            Column(
              children: List.generate(
                4,
                (index) => Padding(
                  padding: EdgeInsets.only(bottom: spacing),
                  child: _buildStatCardSkeleton(context, theme),
                ),
              ),
            ),
          
          SizedBox(height: SolicitorResponsiveLayout.getSectionSpacing(context)),
          
          // Content sections skeleton
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: Column(
                  children: [
                    _buildContentCardSkeleton(context, theme),
                    SizedBox(height: spacing),
                    _buildContentCardSkeleton(context, theme),
                  ],
                ),
              ),
              if (isDesktop) ...[
                SizedBox(width: spacing * 2),
                Expanded(
                  child: _buildContentCardSkeleton(context, theme),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// Application list loading skeleton
  static Widget applicationListLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final itemHeight = SolicitorResponsiveLayout.getListItemHeight(context);
    
    return ListView.builder(
      padding: SolicitorResponsiveLayout.getResponsivePadding(context),
      itemCount: 8,
      itemBuilder: (context, index) => Padding(
        padding: EdgeInsets.only(
          bottom: SolicitorResponsiveLayout.getCardSpacing(context),
        ),
        child: ShadCard(
          child: Container(
            height: itemHeight,
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildShimmerContainer(
                        width: double.infinity,
                        height: 20,
                        theme: theme,
                      ),
                      const SizedBox(height: 8),
                      _buildShimmerContainer(
                        width: 200,
                        height: 16,
                        theme: theme,
                      ),
                      const SizedBox(height: 8),
                      _buildShimmerContainer(
                        width: 150,
                        height: 14,
                        theme: theme,
                      ),
                    ],
                  ),
                ),
                _buildShimmerContainer(
                  width: 100,
                  height: 32,
                  theme: theme,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Claim details loading skeleton
  static Widget claimDetailsLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isDesktop = SolicitorResponsiveLayout.isDesktop(context);
    final spacing = SolicitorResponsiveLayout.getCardSpacing(context);

    return Padding(
      padding: SolicitorResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header skeleton
          _buildShimmerContainer(
            width: isDesktop ? 400 : double.infinity,
            height: 40,
            theme: theme,
          ),
          SizedBox(height: spacing * 2),
          
          // Tab content skeleton
          if (isDesktop)
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: _buildContentCardSkeleton(context, theme),
                ),
                SizedBox(width: spacing * 2),
                Expanded(
                  child: _buildContentCardSkeleton(context, theme),
                ),
              ],
            )
          else
            Column(
              children: [
                _buildContentCardSkeleton(context, theme),
                SizedBox(height: spacing),
                _buildContentCardSkeleton(context, theme),
              ],
            ),
        ],
      ),
    );
  }

  /// Profile loading skeleton
  static Widget profileLoading(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isDesktop = SolicitorResponsiveLayout.isDesktop(context);
    final spacing = SolicitorResponsiveLayout.getCardSpacing(context);

    return Padding(
      padding: SolicitorResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile header skeleton
          Row(
            children: [
              _buildShimmerContainer(
                width: 80,
                height: 80,
                theme: theme,
                borderRadius: 40,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildShimmerContainer(
                      width: isDesktop ? 300 : double.infinity,
                      height: 24,
                      theme: theme,
                    ),
                    const SizedBox(height: 8),
                    _buildShimmerContainer(
                      width: isDesktop ? 200 : 150,
                      height: 16,
                      theme: theme,
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          SizedBox(height: spacing * 2),
          
          // Profile sections skeleton
          if (isDesktop)
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      _buildContentCardSkeleton(context, theme),
                      SizedBox(height: spacing),
                      _buildContentCardSkeleton(context, theme),
                    ],
                  ),
                ),
                SizedBox(width: spacing * 2),
                Expanded(
                  child: _buildContentCardSkeleton(context, theme),
                ),
              ],
            )
          else
            Column(
              children: List.generate(
                3,
                (index) => Padding(
                  padding: EdgeInsets.only(bottom: spacing),
                  child: _buildContentCardSkeleton(context, theme),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Generic loading spinner
  static Widget loadingSpinner(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator.adaptive(),
    );
  }

  /// Loading overlay
  static Widget loadingOverlay(BuildContext context, {String? message}) {
    final theme = ShadTheme.of(context);
    
    return Container(
      color: theme.colorScheme.background.withValues(alpha: 0.8),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator.adaptive(),
            if (message != null) ...[
              const SizedBox(height: 16),
              Text(
                message,
                style: theme.textTheme.p,
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Helper methods
  static Widget _buildStatCardSkeleton(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Container(
        height: 120,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildShimmerContainer(
              width: 40,
              height: 40,
              theme: theme,
              borderRadius: 8,
            ),
            const SizedBox(height: 12),
            _buildShimmerContainer(
              width: double.infinity,
              height: 20,
              theme: theme,
            ),
            const SizedBox(height: 8),
            _buildShimmerContainer(
              width: 80,
              height: 16,
              theme: theme,
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildContentCardSkeleton(BuildContext context, ShadThemeData theme) {
    return ShadCard(
      child: Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildShimmerContainer(
              width: double.infinity,
              height: 24,
              theme: theme,
            ),
            const SizedBox(height: 16),
            _buildShimmerContainer(
              width: double.infinity,
              height: 16,
              theme: theme,
            ),
            const SizedBox(height: 8),
            _buildShimmerContainer(
              width: 200,
              height: 16,
              theme: theme,
            ),
            const SizedBox(height: 8),
            _buildShimmerContainer(
              width: 150,
              height: 16,
              theme: theme,
            ),
            const Spacer(),
            _buildShimmerContainer(
              width: 100,
              height: 32,
              theme: theme,
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildShimmerContainer({
    required double width,
    required double height,
    required ShadThemeData theme,
    double? borderRadius,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(borderRadius ?? 4),
      ),
    );
  }
}
