import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/providers/solicitor_profile_provider.dart';

/// Widget for managing solicitor preferences
class SolicitorPreferencesWidget extends ConsumerStatefulWidget {
  const SolicitorPreferencesWidget({super.key});

  @override
  ConsumerState<SolicitorPreferencesWidget> createState() => _SolicitorPreferencesWidgetState();
}

class _SolicitorPreferencesWidgetState extends ConsumerState<SolicitorPreferencesWidget> {
  late Map<String, dynamic> _preferences;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _preferences = _getDefaultPreferences();
  }

  Map<String, dynamic> _getDefaultPreferences() {
    return {
      'email_notifications': true,
      'push_notifications': true,
      'sms_notifications': false,
      'claim_updates': true,
      'funding_updates': true,
      'document_updates': true,
      'system_announcements': true,
      'marketing_emails': false,
      'weekly_digest': true,
      'language': 'en',
      'timezone': 'Europe/London',
      'privacy_public_profile': false,
      'privacy_share_data': false,
      'auto_assign_claims': true,
      'client_communication_alerts': true,
      'deadline_reminders': true,
    };
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(solicitorProfileProvider);

    return ShadCard(
      title: Text('Preferences', style: theme.textTheme.h4),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Notification Preferences
            _buildSection('Notification Preferences', [
              _buildSwitchTile(
                'Email Notifications',
                'Receive notifications via email',
                'email_notifications',
                LucideIcons.mail,
              ),
              _buildSwitchTile(
                'Push Notifications',
                'Receive push notifications on your device',
                'push_notifications',
                LucideIcons.bell,
              ),
              _buildSwitchTile(
                'SMS Notifications',
                'Receive important updates via SMS',
                'sms_notifications',
                LucideIcons.messageSquare,
              ),
            ], theme),

            const SizedBox(height: 24),

            // Content Preferences
            _buildSection('Content Preferences', [
              _buildSwitchTile(
                'Claim Updates',
                'Notifications about claim status changes',
                'claim_updates',
                LucideIcons.fileText,
              ),
              _buildSwitchTile(
                'Funding Updates',
                'Notifications about funding decisions',
                'funding_updates',
                LucideIcons.dollarSign,
              ),
              _buildSwitchTile(
                'Document Updates',
                'Notifications when documents are added or updated',
                'document_updates',
                LucideIcons.file,
              ),
              _buildSwitchTile(
                'System Announcements',
                'Important platform updates and announcements',
                'system_announcements',
                LucideIcons.megaphone,
              ),
              _buildSwitchTile(
                'Weekly Digest',
                'Weekly summary of your account activity',
                'weekly_digest',
                LucideIcons.calendar,
              ),
            ], theme),

            const SizedBox(height: 24),

            // Solicitor-Specific Preferences
            _buildSection('Professional Preferences', [
              _buildSwitchTile(
                'Auto-Assign Claims',
                'Automatically assign new claims to your firm',
                'auto_assign_claims',
                LucideIcons.userPlus,
              ),
              _buildSwitchTile(
                'Client Communication Alerts',
                'Notifications for client messages and updates',
                'client_communication_alerts',
                LucideIcons.messageCircle,
              ),
              _buildSwitchTile(
                'Deadline Reminders',
                'Reminders for important case deadlines',
                'deadline_reminders',
                LucideIcons.clock,
              ),
            ], theme),

            const SizedBox(height: 24),

            // Privacy Preferences
            _buildSection('Privacy Preferences', [
              _buildSwitchTile(
                'Public Profile',
                'Make your profile visible to other users',
                'privacy_public_profile',
                LucideIcons.eye,
              ),
              _buildSwitchTile(
                'Share Data for Analytics',
                'Help improve our services with anonymous usage data',
                'privacy_share_data',
                LucideIcons.activity,
              ),
              _buildSwitchTile(
                'Marketing Emails',
                'Receive promotional emails and updates',
                'marketing_emails',
                LucideIcons.mail,
              ),
            ], theme),

            const SizedBox(height: 32),

            // Save button
            if (_hasChanges)
              ShadButton(
                onPressed: profileState.isUpdating ? null : _savePreferences,
                child: profileState.isUpdating
                    ? const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator.adaptive(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Saving...'),
                        ],
                      )
                    : const Text('Save Preferences'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children, ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: theme.textTheme.h4),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    String key,
    IconData icon,
  ) {
    return Builder(
      builder: (context) {
        final theme = ShadTheme.of(context);
        return Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: theme.colorScheme.mutedForeground,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, style: theme.textTheme.p),
                    Text(
                      subtitle,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ),
              Switch.adaptive(
                value: _preferences[key] ?? false,
                onChanged: (value) {
                  setState(() {
                    _preferences[key] = value;
                    _hasChanges = true;
                  });
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _savePreferences() async {
    // TODO: Implement saving preferences to the profile
    // This would typically update the solicitor profile with the new preferences
    final success = await ref
        .read(solicitorProfileProvider.notifier)
        .updateProfile(
          // Add preferences to the update call when the service supports it
        );

    if (mounted && success) {
      setState(() {
        _hasChanges = false;
      });
      
      ShadToaster.of(context).show(
        const ShadToast(
          title: Text('Success'),
          description: Text('Preferences saved successfully'),
        ),
      );
    }
  }
}
