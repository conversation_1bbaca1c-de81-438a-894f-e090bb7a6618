import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as li;

import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/toast_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/storage_type.dart';
import 'package:three_pay_group_litigation_platform/src/shared/presentation/widgets/storage_indicator_widget.dart';

/// Download status enumeration
enum DownloadStatus {
  idle,
  preparing,
  downloading,
  completed,
  failed,
  cancelled,
  paused,
}

/// Download progress model
class DownloadProgressModel {
  final String fileId;
  final String fileName;
  final double progress;
  final DownloadStatus status;
  final String? errorMessage;
  final int? downloadSpeed; // bytes per second
  final Duration? estimatedTimeRemaining;
  final int? totalBytes;
  final int? downloadedBytes;

  const DownloadProgressModel({
    required this.fileId,
    required this.fileName,
    this.progress = 0.0,
    this.status = DownloadStatus.idle,
    this.errorMessage,
    this.downloadSpeed,
    this.estimatedTimeRemaining,
    this.totalBytes,
    this.downloadedBytes,
  });

  DownloadProgressModel copyWith({
    String? fileId,
    String? fileName,
    double? progress,
    DownloadStatus? status,
    String? errorMessage,
    int? downloadSpeed,
    Duration? estimatedTimeRemaining,
    int? totalBytes,
    int? downloadedBytes,
  }) {
    return DownloadProgressModel(
      fileId: fileId ?? this.fileId,
      fileName: fileName ?? this.fileName,
      progress: progress ?? this.progress,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      downloadSpeed: downloadSpeed ?? this.downloadSpeed,
      estimatedTimeRemaining:
          estimatedTimeRemaining ?? this.estimatedTimeRemaining,
      totalBytes: totalBytes ?? this.totalBytes,
      downloadedBytes: downloadedBytes ?? this.downloadedBytes,
    );
  }
}

/// Enhanced document download widget with Google Drive integration support
class DocumentDownloadWidget extends ConsumerStatefulWidget {
  final String documentId;
  final String fileName;
  final int? fileSize;
  final Function(String fileId)? onDownloadStart;
  final Function(String fileId, double progress)? onDownloadProgress;
  final Function(String fileId, String? filePath)? onDownloadComplete;
  final Function(String fileId, String error)? onDownloadError;
  final bool showProgress;
  final bool allowCancel;
  final bool allowPause;

  const DocumentDownloadWidget({
    super.key,
    required this.documentId,
    required this.fileName,
    this.fileSize,
    this.onDownloadStart,
    this.onDownloadProgress,
    this.onDownloadComplete,
    this.onDownloadError,
    this.showProgress = true,
    this.allowCancel = true,
    this.allowPause = false,
  });

  @override
  ConsumerState<DocumentDownloadWidget> createState() =>
      _DocumentDownloadWidgetState();
}

class _DocumentDownloadWidgetState
    extends ConsumerState<DocumentDownloadWidget> {
  DownloadProgressModel? _downloadProgress;
  bool _isRetrying = false;

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    if (_downloadProgress == null) {
      return _buildIdleState(theme);
    }

    return _buildDownloadingState(theme);
  }

  Widget _buildIdleState(ShadThemeData theme) {
    return ShadButton(
      onPressed: _startDownload,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            li.LucideIcons.download,
            size: 16,
            color: theme.colorScheme.primaryForeground,
          ),
          const SizedBox(width: 8),
          const Text('Download'),
        ],
      ),
    );
  }

  Widget _buildDownloadingState(ShadThemeData theme) {
    final progress = _downloadProgress!;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: theme.radius,
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDownloadHeader(theme, progress),
          if (widget.showProgress) ...[
            const SizedBox(height: 12),
            _buildProgressIndicator(theme, progress),
            const SizedBox(height: 8),
            _buildProgressDetails(theme, progress),
          ],
          if (progress.status == DownloadStatus.failed) ...[
            const SizedBox(height: 8),
            _buildErrorMessage(theme, progress),
          ],
          const SizedBox(height: 12),
          _buildActionButtons(theme, progress),
        ],
      ),
    );
  }

  Widget _buildDownloadHeader(
    ShadThemeData theme,
    DownloadProgressModel progress,
  ) {
    return Row(
      children: [
        StorageIndicatorWidget(
          storageType: StorageType.googleDrive,
          isDownloading: progress.status == DownloadStatus.downloading,
          downloadProgress: progress.progress,
          errorMessage: progress.errorMessage,
          showLabel: false,
          iconSize: 16,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                progress.fileName,
                style: theme.textTheme.large.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                _getStatusText(progress),
                style: theme.textTheme.small.copyWith(
                  color: _getStatusColor(theme, progress),
                ),
              ),
            ],
          ),
        ),
        _buildStatusIcon(theme, progress),
      ],
    );
  }

  Widget _buildProgressIndicator(
    ShadThemeData theme,
    DownloadProgressModel progress,
  ) {
    return Column(
      children: [
        LinearProgressIndicator(
          value:
              progress.status == DownloadStatus.downloading
                  ? progress.progress
                  : null,
          backgroundColor: theme.colorScheme.muted,
          valueColor: AlwaysStoppedAnimation<Color>(
            _getProgressColor(theme, progress),
          ),
          minHeight: 6,
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${(progress.progress * 100).toStringAsFixed(1)}%',
              style: theme.textTheme.small.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
            ),
            if (progress.downloadedBytes != null && progress.totalBytes != null)
              Text(
                '${_formatBytes(progress.downloadedBytes!)} / ${_formatBytes(progress.totalBytes!)}',
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildProgressDetails(
    ShadThemeData theme,
    DownloadProgressModel progress,
  ) {
    return Row(
      children: [
        if (progress.downloadSpeed != null) ...[
          Icon(
            li.LucideIcons.gauge,
            size: 12,
            color: theme.colorScheme.mutedForeground,
          ),
          const SizedBox(width: 4),
          Text(
            '${_formatBytes(progress.downloadSpeed!)}/s',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
        if (progress.estimatedTimeRemaining != null) ...[
          const SizedBox(width: 16),
          Icon(
            li.LucideIcons.clock,
            size: 12,
            color: theme.colorScheme.mutedForeground,
          ),
          const SizedBox(width: 4),
          Text(
            _formatDuration(progress.estimatedTimeRemaining!),
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildErrorMessage(
    ShadThemeData theme,
    DownloadProgressModel progress,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.colorScheme.destructive.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: theme.colorScheme.destructive.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            li.LucideIcons.alertCircle,
            size: 16,
            color: theme.colorScheme.destructive,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              progress.errorMessage ?? 'Download failed',
              style: theme.textTheme.small.copyWith(
                color: theme.colorScheme.destructive,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(
    ShadThemeData theme,
    DownloadProgressModel progress,
  ) {
    return Row(
      children: [
        if (progress.status == DownloadStatus.downloading && widget.allowPause)
          ShadButton.outline(
            onPressed: _pauseDownload,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(li.LucideIcons.pause, size: 14),
                const SizedBox(width: 4),
                const Text('Pause'),
              ],
            ),
          ),
        if (progress.status == DownloadStatus.paused)
          ShadButton.outline(
            onPressed: _resumeDownload,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(li.LucideIcons.play, size: 14),
                const SizedBox(width: 4),
                const Text('Resume'),
              ],
            ),
          ),
        if (progress.status == DownloadStatus.failed)
          ShadButton.outline(
            onPressed: _isRetrying ? null : _retryDownload,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_isRetrying)
                  const SizedBox(
                    width: 14,
                    height: 14,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                else
                  Icon(li.LucideIcons.refreshCw, size: 14),
                const SizedBox(width: 4),
                Text(_isRetrying ? 'Retrying...' : 'Retry'),
              ],
            ),
          ),
        const Spacer(),
        if ((progress.status == DownloadStatus.downloading ||
                progress.status == DownloadStatus.paused) &&
            widget.allowCancel)
          ShadButton.outline(
            onPressed: _cancelDownload,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(li.LucideIcons.x, size: 14),
                const SizedBox(width: 4),
                const Text('Cancel'),
              ],
            ),
          ),
        if (progress.status == DownloadStatus.completed ||
            progress.status == DownloadStatus.cancelled)
          ShadButton.outline(
            onPressed: _clearDownload,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(li.LucideIcons.trash2, size: 14),
                const SizedBox(width: 4),
                const Text('Clear'),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildStatusIcon(ShadThemeData theme, DownloadProgressModel progress) {
    IconData icon;
    Color color;

    switch (progress.status) {
      case DownloadStatus.idle:
        icon = li.LucideIcons.download;
        color = theme.colorScheme.primary;
        break;
      case DownloadStatus.preparing:
        icon = li.LucideIcons.loader2;
        color = theme.colorScheme.primary;
        break;
      case DownloadStatus.downloading:
        icon = li.LucideIcons.downloadCloud;
        color = Colors.blue;
        break;
      case DownloadStatus.completed:
        icon = li.LucideIcons.checkCircle2;
        color = Colors.green;
        break;
      case DownloadStatus.failed:
        icon = li.LucideIcons.xCircle;
        color = theme.colorScheme.destructive;
        break;
      case DownloadStatus.cancelled:
        icon = li.LucideIcons.ban;
        color = theme.colorScheme.mutedForeground;
        break;
      case DownloadStatus.paused:
        icon = li.LucideIcons.pauseCircle;
        color = Colors.orange;
        break;
    }

    return Icon(icon, size: 20, color: color);
  }

  String _getStatusText(DownloadProgressModel progress) {
    switch (progress.status) {
      case DownloadStatus.idle:
        return 'Ready to download';
      case DownloadStatus.preparing:
        return 'Preparing download...';
      case DownloadStatus.downloading:
        return 'Downloading from Google Drive...';
      case DownloadStatus.completed:
        return 'Download completed';
      case DownloadStatus.failed:
        return 'Download failed';
      case DownloadStatus.cancelled:
        return 'Download cancelled';
      case DownloadStatus.paused:
        return 'Download paused';
    }
  }

  Color _getStatusColor(ShadThemeData theme, DownloadProgressModel progress) {
    switch (progress.status) {
      case DownloadStatus.idle:
      case DownloadStatus.preparing:
      case DownloadStatus.downloading:
        return theme.colorScheme.primary;
      case DownloadStatus.completed:
        return Colors.green;
      case DownloadStatus.failed:
        return theme.colorScheme.destructive;
      case DownloadStatus.cancelled:
      case DownloadStatus.paused:
        return theme.colorScheme.mutedForeground;
    }
  }

  Color _getProgressColor(ShadThemeData theme, DownloadProgressModel progress) {
    switch (progress.status) {
      case DownloadStatus.downloading:
        return Colors.blue;
      case DownloadStatus.completed:
        return Colors.green;
      case DownloadStatus.failed:
        return theme.colorScheme.destructive;
      case DownloadStatus.paused:
        return Colors.orange;
      default:
        return theme.colorScheme.primary;
    }
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes.remainder(60)}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds.remainder(60)}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  void _startDownload() {
    setState(() {
      _downloadProgress = DownloadProgressModel(
        fileId: widget.documentId,
        fileName: widget.fileName,
        status: DownloadStatus.preparing,
      );
    });

    widget.onDownloadStart?.call(widget.documentId);
    _simulateDownload();
  }

  void _pauseDownload() {
    if (_downloadProgress != null) {
      setState(() {
        _downloadProgress = _downloadProgress!.copyWith(
          status: DownloadStatus.paused,
        );
      });
    }
  }

  void _resumeDownload() {
    if (_downloadProgress != null) {
      setState(() {
        _downloadProgress = _downloadProgress!.copyWith(
          status: DownloadStatus.downloading,
        );
      });
      _simulateDownload();
    }
  }

  void _cancelDownload() {
    if (_downloadProgress != null) {
      setState(() {
        _downloadProgress = _downloadProgress!.copyWith(
          status: DownloadStatus.cancelled,
        );
      });
    }
  }

  void _retryDownload() {
    setState(() {
      _isRetrying = true;
    });

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isRetrying = false;
        });
        _startDownload();
      }
    });
  }

  void _clearDownload() {
    setState(() {
      _downloadProgress = null;
    });
  }

  void _simulateDownload() async {
    // Simulate download progress (replace with actual Google Drive download)
    final totalBytes = widget.fileSize ?? 1024 * 1024; // Default 1MB

    for (int i = 0; i <= 100; i += 5) {
      if (_downloadProgress?.status != DownloadStatus.downloading) break;

      await Future.delayed(const Duration(milliseconds: 200));

      if (mounted && _downloadProgress?.status == DownloadStatus.downloading) {
        final downloadedBytes = (totalBytes * i / 100).round();
        final speed = 50 * 1024; // 50 KB/s
        final remaining = Duration(
          seconds: ((totalBytes - downloadedBytes) / speed).round(),
        );

        setState(() {
          _downloadProgress = _downloadProgress!.copyWith(
            progress: i / 100.0,
            downloadedBytes: downloadedBytes,
            totalBytes: totalBytes,
            downloadSpeed: speed,
            estimatedTimeRemaining: remaining,
          );
        });

        widget.onDownloadProgress?.call(widget.documentId, i / 100.0);
      }
    }

    if (mounted && _downloadProgress?.status == DownloadStatus.downloading) {
      setState(() {
        _downloadProgress = _downloadProgress!.copyWith(
          status: DownloadStatus.completed,
          progress: 1.0,
        );
      });

      widget.onDownloadComplete?.call(
        widget.documentId,
        '/path/to/downloaded/file',
      );

      ToastService.showSuccess(
        context,
        'Successfully downloaded "${widget.fileName}" from Google Drive',
      );
    }
  }
}
