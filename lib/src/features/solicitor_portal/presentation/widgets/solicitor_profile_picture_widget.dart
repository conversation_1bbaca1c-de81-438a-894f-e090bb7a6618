import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../providers/solicitor_profile_provider.dart';

/// Widget for displaying and managing solicitor profile picture
class SolicitorProfilePictureWidget extends ConsumerWidget {
  final double size;
  final bool showEditButton;
  final VoidCallback? onEditPressed;

  const SolicitorProfilePictureWidget({
    super.key,
    this.size = 120,
    this.showEditButton = true,
    this.onEditPressed,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(solicitorProfileProvider);
    final profileNotifier = ref.read(solicitorProfileProvider.notifier);

    return Column(
      children: [
        Stack(
          children: [
            // Profile picture or avatar
            Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: theme.colorScheme.border, width: 2),
              ),
              child: ClipOval(
                child:
                    profileState.profilePictureUrl != null
                        ? Image.network(
                          profileState.profilePictureUrl!,
                          width: size,
                          height: size,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildDefaultAvatar(theme);
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return _buildLoadingAvatar(theme);
                          },
                        )
                        : _buildDefaultAvatar(theme),
              ),
            ),

            // Edit button
            if (showEditButton)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: theme.colorScheme.background,
                      width: 2,
                    ),
                  ),
                  child: ShadButton.ghost(
                    size: ShadButtonSize.sm,
                    width: 32,
                    height: 32,
                    padding: EdgeInsets.zero,
                    onPressed:
                        profileState.isUploadingImage
                            ? null
                            : () async {
                              if (onEditPressed != null) {
                                onEditPressed!();
                              } else {
                                await _handleImageUpload(
                                  context,
                                  profileNotifier,
                                );
                              }
                            },
                    child:
                        profileState.isUploadingImage
                            ? SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  theme.colorScheme.primaryForeground,
                                ),
                              ),
                            )
                            : Icon(
                              LucideIcons.camera,
                              size: 16,
                              color: theme.colorScheme.primaryForeground,
                            ),
                  ),
                ),
              ),
          ],
        ),

        // Upload status and instructions
        if (profileState.isUploadingImage) ...[
          const SizedBox(height: 8),
          Text(
            'Uploading...',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ] else if (showEditButton) ...[
          const SizedBox(height: 8),
          Text(
            'Click the edit button to change the profile picture',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            'Supported: JPG, PNG (max 5MB)',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
              fontSize: 11,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  Widget _buildDefaultAvatar(ShadThemeData theme) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        shape: BoxShape.circle,
      ),
      child: Icon(
        LucideIcons.user,
        size: size * 0.4,
        color: theme.colorScheme.mutedForeground,
      ),
    );
  }

  Widget _buildLoadingAvatar(ShadThemeData theme) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
        ),
      ),
    );
  }

  Future<void> _handleImageUpload(
    BuildContext context,
    SolicitorProfileNotifier notifier,
  ) async {
    final success = await notifier.uploadProfilePicture();

    if (context.mounted) {
      if (success) {
        ShadToaster.of(context).show(
          const ShadToast(
            title: Text('Success'),
            description: Text('Profile picture updated successfully'),
          ),
        );
      } else {
        // Error is handled by the provider state
        // Show help dialog for any upload failure to assist users
        Future.delayed(const Duration(milliseconds: 500), () {
          if (context.mounted) {
            _showImageUploadHelp(context);
          }
        });
      }
    }
  }

  void _showImageUploadHelp(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Image Upload Help'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Having trouble uploading your profile picture?'),
                SizedBox(height: 16),
                Text('Tips:'),
                SizedBox(height: 8),
                Text('• Use JPG or PNG format'),
                Text('• Keep file size under 5MB'),
                Text('• Try taking a new photo'),
                Text('• Check your internet connection'),
                SizedBox(height: 16),
                Text(
                  'If the problem persists, please contact support.',
                  style: TextStyle(fontStyle: FontStyle.italic),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }
}

/// Compact version of solicitor profile picture for smaller spaces
class CompactSolicitorProfilePictureWidget extends ConsumerWidget {
  final double size;

  const CompactSolicitorProfilePictureWidget({super.key, this.size = 40});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(solicitorProfileProvider);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: theme.colorScheme.border, width: 1),
      ),
      child: ClipOval(
        child:
            profileState.profilePictureUrl != null
                ? Image.network(
                  profileState.profilePictureUrl!,
                  width: size,
                  height: size,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildDefaultAvatar(theme);
                  },
                )
                : _buildDefaultAvatar(theme),
      ),
    );
  }

  Widget _buildDefaultAvatar(ShadThemeData theme) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        shape: BoxShape.circle,
      ),
      child: Icon(
        LucideIcons.user,
        size: size * 0.5,
        color: theme.colorScheme.mutedForeground,
      ),
    );
  }
}
