import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart'; // For date formatting
import 'package:lucide_icons/lucide_icons.dart' as li;
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/toast_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/services/document_management_service.dart';
import '../../../features/firm_documents/application/models/document_model.dart';
import '../../../features/firm_documents/application/providers/selected_documents_provider.dart'; // Reverted to original correct path

/// A widget to display a single document item in a list.
class DocumentListItem extends ConsumerWidget {
  // Changed to ConsumerWidget
  const DocumentListItem({super.key, required this.document});

  final DocumentModel document;

  IconData _getIconForType(String type) {
    switch (type.toLowerCase()) {
      case 'pdf':
        return li.LucideIcons.fileText;
      case 'docx':
      case 'doc':
        return li.LucideIcons.fileText; // Changed to fileText
      case 'xlsx':
      case 'xls':
        return li.LucideIcons.fileSpreadsheet;
      case 'pptx':
      case 'ppt':
        return li.LucideIcons.file; // Changed to generic file icon
      case 'zip':
      case 'rar':
        return li.LucideIcons.fileArchive;
      case 'txt':
        return li.LucideIcons.fileType;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'bmp':
      case 'webp':
        return li.LucideIcons.image;
      default:
        return li.LucideIcons.file;
    }
  }

  String _formatSize(int sizeInBytes) {
    if (sizeInBytes <= 0) return '0 B'; // Handle zero or negative size
    if (sizeInBytes < 1024) {
      return '$sizeInBytes B';
    } else if (sizeInBytes < 1024 * 1024) {
      return '${(sizeInBytes / 1024).toStringAsFixed(1)} KB';
    } else if (sizeInBytes < 1024 * 1024 * 1024) {
      return '${(sizeInBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(sizeInBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Added WidgetRef
    final formattedDate = DateFormat('MMM d, yyyy').format(document.uploadDate);
    final formattedSize = _formatSize(document.size);
    final theme = Theme.of(context); // Standard Flutter theme for now

    final selectedDocuments = ref.watch(
      selectedDocumentsNotifierProvider,
    ); // Corrected provider name
    final isSelected = selectedDocuments.contains(document.id);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      child: ListTile(
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Checkbox(
              value: isSelected,
              onChanged: (bool? value) {
                final notifier = ref.read(
                  selectedDocumentsNotifierProvider.notifier,
                ); // Corrected provider name
                if (value == true) {
                  notifier.selectDocument(document.id); // Corrected method name
                } else {
                  notifier.deselectDocument(
                    document.id,
                  ); // Corrected method name
                }
              },
            ),
            const SizedBox(width: 8),
            Icon(
              _getIconForType(document.type),
              size: 36,
              color: theme.colorScheme.primary,
            ),
          ],
        ),
        title: Text(
          document.name,
          style: const TextStyle(fontWeight: FontWeight.w500),
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          'Uploaded: $formattedDate | Size: $formattedSize',
          overflow: TextOverflow.ellipsis,
        ), // Removed status for brevity
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (String value) async {
            if (value == 'download') {
              final docService = ref.read(documentManagementServiceProvider);
              try {
                NotificationService.showInfo(
                  context,
                  'Starting download for ${document.name}...',
                );
                await docService.downloadFile(document);
                // Success/failure is handled by service (OpenFilex result, console logs)
                // Optionally show a success toast here if OpenFilex was successful,
                // but that would require the service to return a status.
              } catch (e) {
                if (context.mounted)
                  NotificationService.showError(
                    context,
                    'Download failed: ${e.toString()}',
                  );
              }
            }
            // TODO: Implement other actions like rename, delete from here
          },
          itemBuilder:
              (BuildContext context) => <PopupMenuEntry<String>>[
                const PopupMenuItem<String>(
                  value: 'download',
                  child: ListTile(
                    leading: Icon(li.LucideIcons.downloadCloud),
                    title: Text('Download'),
                  ),
                ),
                // Add other actions here
                // const PopupMenuItem<String>(
                //   value: 'rename',
                //   child: ListTile(
                //     leading: Icon(li.LucideIcons.edit3),
                //     title: Text('Rename'),
                //   ),
                // ),
                // const PopupMenuItem<String>(
                //   value: 'delete',
                //   child: ListTile(
                //     leading: Icon(li.LucideIcons.trash2, color: Colors.red),
                //     title: Text('Delete', style: TextStyle(color: Colors.red)),
                //   ),
                // ),
              ],
        ),
        onTap: () {
          // For now, tapping also initiates download as a primary action
          final docService = ref.read(documentManagementServiceProvider);
          try {
            NotificationService.showInfo(
              context,
              'Starting download for ${document.name}...',
            );
            docService.downloadFile(document);
          } catch (e) {
            if (context.mounted)
              NotificationService.showError(
                context,
                'Download failed: ${e.toString()}',
              );
          }
        },
      ),
    );
  }
}
