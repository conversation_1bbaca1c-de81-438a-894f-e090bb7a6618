import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../../features/firm_documents/application/providers/active_filters_provider.dart';
import '../../../features/firm_documents/application/models/filters_model.dart';

/// A widget to display filtering options for documents.
class FilterPanelWidget extends ConsumerWidget {
  const FilterPanelWidget({super.key});

  // Placeholder options for select fields
  static const List<String> _documentTypes = ['all', 'pdf', 'docx', 'xlsx', 'image', 'other'];
  static const List<String> _documentStatuses = ['all', 'uploaded', 'processing', 'reviewed', 'archived'];


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activeFilters = ref.watch(activeFiltersNotifierProvider);
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.border),
        borderRadius: theme.radius, // Use theme.radius directly
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('Filter Options', style: theme.textTheme.h4),
          const SizedBox(height: 24),
          _buildFilterSectionTitle(context, 'Document Type'),
          ShadSelect<String>(
            placeholder: const Text('Select type...'),
            initialValue: activeFilters.documentType == null || activeFilters.documentType!.isEmpty || activeFilters.documentType == 'all'
                ? null
                : activeFilters.documentType,
            selectedOptionBuilder: (context, value) => Text(
              value == 'all' ? 'All Types' : (value.isNotEmpty ? value[0].toUpperCase() + value.substring(1) : 'Select type...')
            ),
            options: _documentTypes
                .map((type) => ShadOption(value: type, child: Text(type == 'all' ? 'All Types' : type.toUpperCase())))
                .toList(),
            onChanged: (value) {
              ref.read(activeFiltersNotifierProvider.notifier).updateFilter(
                documentType: value == 'all' ? null : value,
                clearDocumentType: value == 'all',
              );
            },
          ),
          const SizedBox(height: 16),
          _buildFilterSectionTitle(context, 'Status'),
          ShadSelect<String>(
            placeholder: const Text('Select status...'),
            initialValue: activeFilters.status == null || activeFilters.status!.isEmpty || activeFilters.status == 'all'
                ? null
                : activeFilters.status,
            selectedOptionBuilder: (context, value) => Text(
              value == 'all' ? 'All Statuses' : (value.isNotEmpty ? value[0].toUpperCase() + value.substring(1) : 'Select status...')
            ),
            options: _documentStatuses
                .map((status) => ShadOption(value: status, child: Text(status == 'all' ? 'All Statuses' : status[0].toUpperCase() + status.substring(1))))
                .toList(),
            onChanged: (value) {
              ref.read(activeFiltersNotifierProvider.notifier).updateFilter(
                status: value == 'all' ? null : value,
                clearStatus: value == 'all',
              );
            },
          ),
          const SizedBox(height: 16),
          _buildFilterSectionTitle(context, 'Date From'),
          ShadButton.outline(
            width: double.infinity,
            child: Text(activeFilters.dateFrom != null
                ? '${activeFilters.dateFrom!.toLocal()}'.split(' ')[0]
                : 'Select start date...'),
            onPressed: () async {
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: activeFilters.dateFrom ?? DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime(2101),
              );
              if (picked != null) {
                ref.read(activeFiltersNotifierProvider.notifier).updateFilter(dateFrom: picked);
              }
            },
          ),
          const SizedBox(height: 16),
          _buildFilterSectionTitle(context, 'Date To'),
          ShadButton.outline(
            width: double.infinity,
            child: Text(activeFilters.dateTo != null
                ? '${activeFilters.dateTo!.toLocal()}'.split(' ')[0]
                : 'Select end date...'),
            onPressed: () async {
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: activeFilters.dateTo ?? activeFilters.dateFrom ?? DateTime.now(),
                firstDate: activeFilters.dateFrom ?? DateTime(2000),
                lastDate: DateTime(2101),
              );
              if (picked != null) {
                ref.read(activeFiltersNotifierProvider.notifier).updateFilter(dateTo: picked);
              }
            },
          ),
          const SizedBox(height: 24),
          ShadButton(
            child: const Text('Clear All Filters'),
            onPressed: () {
              ref.read(activeFiltersNotifierProvider.notifier).clearFilters();
            },
            width: double.infinity,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(title, style: ShadTheme.of(context).textTheme.small),
    );
  }
}