import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../features/firm_documents/application/providers/firm_documents_list_provider.dart';
import 'document_list_item.dart';
import '../../../../../core/ui/widgets/loading_spinner_widget.dart';
import '../../../../../core/ui/widgets/empty_state_widget.dart';

/// A widget to display a list of documents with infinite scrolling.
class DocumentListView extends ConsumerStatefulWidget {
  const DocumentListView({super.key});

  @override
  ConsumerState<DocumentListView> createState() => _DocumentListViewState();
}

class _DocumentListViewState extends ConsumerState<DocumentListView> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) { // Trigger load 200px before end
      // Check if provider is not in loading state before fetching more
      final notifier = ref.read(firmDocumentsNotifierProvider.notifier);
      final state = ref.watch(firmDocumentsNotifierProvider);
      if (!state.isLoading && !state.hasError) { // Ensure not already loading or in error
         notifier.fetchNextPage();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final documentsAsyncValue = ref.watch(firmDocumentsNotifierProvider);

    return documentsAsyncValue.when(
      data: (documents) {
        if (documents.isEmpty) {
          return const EmptyStateWidget(
            message: 'No documents found. Upload your first document to get started.',
            icon: Icons.cloud_upload_outlined,
          );
        }
        return RefreshIndicator(
          onRefresh: () => ref.read(firmDocumentsNotifierProvider.notifier).refresh(),
          child: ListView.builder(
            controller: _scrollController,
            itemCount: documents.length + (documentsAsyncValue.isLoading && documents.isNotEmpty ? 1 : 0), // Add space for loader
            itemBuilder: (context, index) {
              if (index == documents.length && documentsAsyncValue.isLoading) {
                return const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Center(child: LoadingSpinnerWidget(size: 24)),
                );
              }
              if (index >= documents.length) return const SizedBox.shrink(); // Should not happen if logic is correct

              final document = documents[index];
              return DocumentListItem(document: document);
            },
          ),
        );
      },
      loading: () => const Center(child: LoadingSpinnerWidget()),
      error: (error, stackTrace) {
        // Simple error display, can be enhanced
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('Error loading documents: ${error.toString()}'),
              const SizedBox(height: 10),
              ElevatedButton(
                onPressed: () => ref.read(firmDocumentsNotifierProvider.notifier).refresh(),
                child: const Text('Retry'),
              )
            ],
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }
}