import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/toast_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/providers/selected_documents_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/services/document_management_service.dart';
import 'package:lucide_icons/lucide_icons.dart' as li;
// Import for childItemsProvider
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/firm_documents_page.dart'
    show childItemsProvider;

/// A toolbar for performing bulk actions on selected documents.
class BulkActionToolbarWidget extends ConsumerWidget {
  const BulkActionToolbarWidget({super.key});

  /// Shows a confirmation dialog and deletes the selected documents if confirmed
  Future<void> _confirmAndDelete(
    BuildContext context,
    WidgetRef ref,
    int selectedCount,
    Set<String> selectedDocuments,
  ) async {
    final confirmed = await showShadDialog<bool>(
      context: context,
      builder:
          (dialogContext) => ShadDialog.alert(
            title: Text(
              'Delete $selectedCount Document${selectedCount == 1 ? '' : 's'}?',
            ),
            description: Text(
              'Are you sure you want to delete the selected document${selectedCount == 1 ? '' : 's'}? This action cannot be undone.',
            ),
            actions: [
              ShadButton.outline(
                onPressed: () => Navigator.of(dialogContext).pop(false),
                child: const Text('Cancel'),
              ),
              ShadButton.destructive(
                onPressed: () => Navigator.of(dialogContext).pop(true),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final docService = ref.read(documentManagementServiceProvider);
      final selectedIds = selectedDocuments.toList();
      try {
        await docService.deleteSelectedDocuments(selectedIds);
        if (context.mounted) {
          NotificationService.showSuccess(
            context,
            '$selectedCount document${selectedCount == 1 ? '' : 's'} deleted successfully.',
          );
        }
        ref.read(selectedDocumentsNotifierProvider.notifier).clearSelection();
        ref.invalidate(childItemsProvider);
      } catch (e) {
        if (context.mounted) {
          NotificationService.showError(
            context,
            'Failed to delete documents: ${e.toString()}',
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDocuments = ref.watch(selectedDocumentsNotifierProvider);
    final selectedCount = selectedDocuments.length;
    final shadTheme = ShadTheme.of(context);

    if (selectedCount == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: shadTheme.colorScheme.secondary.withAlpha(
          26,
        ), // 0.1 opacity is approximately alpha 26
        border: Border(top: BorderSide(color: shadTheme.colorScheme.border)),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Define width thresholds for responsive layout
          final bool isNarrow = constraints.maxWidth < 400;
          final bool isVeryNarrow = constraints.maxWidth < 250;

          // For very narrow widths, use a compact layout with a menu
          if (isVeryNarrow) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    '$selectedCount selected',
                    style: shadTheme.textTheme.small.copyWith(
                      color: shadTheme.colorScheme.foreground,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                ShadIconButton.ghost(
                  icon: const Icon(li.LucideIcons.moreVertical, size: 18),
                  onPressed: () {
                    // Show a popup menu with all actions
                    showMenu(
                      context: context,
                      position: RelativeRect.fromLTRB(
                        constraints.maxWidth,
                        0,
                        0,
                        0,
                      ),
                      items: [
                        PopupMenuItem(
                          onTap: () {
                            NotificationService.showInfo(
                              context,
                              'Bulk download not yet implemented.',
                            );
                          },
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(li.LucideIcons.downloadCloud, size: 16),
                              SizedBox(width: 8),
                              Text('Download'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          onTap: () {
                            NotificationService.showInfo(
                              context,
                              'Bulk move not yet implemented.',
                            );
                          },
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(li.LucideIcons.move, size: 16),
                              SizedBox(width: 8),
                              Text('Move'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          onTap:
                              () => _confirmAndDelete(
                                context,
                                ref,
                                selectedCount,
                                selectedDocuments,
                              ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                li.LucideIcons.trash2,
                                size: 16,
                                color: Colors.red,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Delete',
                                style: TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ],
            );
          }

          // For narrow widths, show icon-only buttons
          if (isNarrow) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    '$selectedCount item${selectedCount == 1 ? '' : 's'} selected',
                    style: shadTheme.textTheme.small.copyWith(
                      color: shadTheme.colorScheme.foreground,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ShadTooltip(
                      builder: (context) => const Text('Download'),
                      child: ShadIconButton.ghost(
                        icon: const Icon(
                          li.LucideIcons.downloadCloud,
                          size: 18,
                        ),
                        onPressed: () {
                          NotificationService.showInfo(
                            context,
                            'Bulk download not yet implemented.',
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 4),
                    ShadTooltip(
                      builder: (context) => const Text('Move'),
                      child: ShadIconButton.ghost(
                        icon: const Icon(li.LucideIcons.move, size: 18),
                        onPressed: () {
                          NotificationService.showInfo(
                            context,
                            'Bulk move not yet implemented.',
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 4),
                    ShadTooltip(
                      builder: (context) => const Text('Delete Selected'),
                      child: ShadIconButton.destructive(
                        icon: const Icon(li.LucideIcons.trash2, size: 18),
                        onPressed:
                            () => _confirmAndDelete(
                              context,
                              ref,
                              selectedCount,
                              selectedDocuments,
                            ),
                      ),
                    ),
                  ],
                ),
              ],
            );
          }

          // For wider layouts, use the original design with buttons and text
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  '$selectedCount item${selectedCount == 1 ? '' : 's'} selected',
                  style: shadTheme.textTheme.small.copyWith(
                    color: shadTheme.colorScheme.foreground,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShadButton.ghost(
                    leading: const Icon(li.LucideIcons.downloadCloud, size: 18),
                    onPressed: () {
                      NotificationService.showInfo(
                        context,
                        'Bulk download not yet implemented.',
                      );
                    },
                    child: const Text('Download'),
                  ),
                  const SizedBox(width: 8),
                  ShadButton.ghost(
                    leading: const Icon(li.LucideIcons.move, size: 18),
                    onPressed: () {
                      NotificationService.showInfo(
                        context,
                        'Bulk move not yet implemented.',
                      );
                    },
                    child: const Text('Move'),
                  ),
                  const SizedBox(width: 8),
                  ShadButton.destructive(
                    leading: const Icon(li.LucideIcons.trash2, size: 18),
                    onPressed:
                        () => _confirmAndDelete(
                          context,
                          ref,
                          selectedCount,
                          selectedDocuments,
                        ),
                    child: const Text('Delete'),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }
}
