import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart' as li; // Import with prefix
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/models/upload_progress_model.dart';

/// A widget to display file upload progress.
class UploadProgressIndicatorWidget extends StatelessWidget {
  const UploadProgressIndicatorWidget({
    super.key,
    required this.progressModel,
    this.onCancel,
    this.onClear,
  });

  final UploadProgressModel progressModel;
  final VoidCallback? onCancel;
  final VoidCallback? onClear;

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    IconData statusIcon;
    Color statusColor;
    String statusText = progressModel.fileName;

    switch (progressModel.status) {
      case UploadStatus.pending:
        statusIcon = li.LucideIcons.loader2; // Use prefixed icon
        statusColor = theme.colorScheme.foreground.withOpacity(0.7);
        statusText = 'Pending: ${progressModel.fileName}';
        break;
      case UploadStatus.uploading:
        statusIcon = li.LucideIcons.uploadCloud; // Use prefixed icon
        statusColor = theme.colorScheme.primary;
        statusText =
            'Uploading: ${progressModel.fileName} (${(progressModel.progress * 100).toStringAsFixed(0)}%)';
        break;
      case UploadStatus.completed:
        statusIcon = li.LucideIcons.checkCircle2; // Use prefixed icon
        statusColor = theme.colorScheme.primary;
        statusText = 'Completed: ${progressModel.fileName}';
        break;
      case UploadStatus.failed:
        statusIcon = li.LucideIcons.xCircle; // Use prefixed icon
        statusColor = theme.colorScheme.destructive;
        statusText = 'Failed: ${progressModel.fileName}';
        break;
      case UploadStatus.cancelled:
        statusIcon = li.LucideIcons.ban; // Use prefixed icon
        statusColor = theme.colorScheme.mutedForeground;
        statusText = 'Cancelled: ${progressModel.fileName}';
        break;
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      elevation: 0,
      color: theme.colorScheme.card,
      shape: RoundedRectangleBorder(
        borderRadius: theme.radius, // theme.radius is BorderRadius
        side: BorderSide(color: theme.colorScheme.border),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(statusIcon, color: statusColor, size: 20),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    statusText,
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.foreground,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (progressModel.status == UploadStatus.uploading &&
                    onCancel != null)
                  ShadButton.ghost(
                    onPressed: onCancel,
                    padding: EdgeInsets.zero,
                    width: 24,
                    height: 24,
                    child: const Icon(
                      li.LucideIcons.x,
                      size: 16,
                    ), // Use prefixed icon
                  )
                else if ((progressModel.status == UploadStatus.completed ||
                        progressModel.status == UploadStatus.failed ||
                        progressModel.status == UploadStatus.cancelled) &&
                    onClear != null)
                  ShadButton.ghost(
                    onPressed: onClear,
                    padding: EdgeInsets.zero,
                    width: 24,
                    height: 24,
                    child: const Icon(
                      li.LucideIcons.trash2,
                      size: 16,
                    ), // Use prefixed icon
                  ),
              ],
            ),
            if (progressModel.status == UploadStatus.uploading) ...[
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: progressModel.progress,
                backgroundColor: theme.colorScheme.muted,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
                minHeight: 6,
                // borderRadius: theme.radius, // LinearProgressIndicator doesn't have borderRadius directly
              ),
            ],
            if (progressModel.status == UploadStatus.failed &&
                progressModel.errorMessage != null) ...[
              const SizedBox(height: 6),
              Text(
                progressModel.errorMessage!,
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.destructive,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
