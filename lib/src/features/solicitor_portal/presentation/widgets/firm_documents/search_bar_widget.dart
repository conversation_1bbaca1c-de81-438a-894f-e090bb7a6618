import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../../features/firm_documents/application/providers/search_query_provider.dart';

/// A widget for search input with debouncing.
class SearchBarWidget extends ConsumerStatefulWidget {
  const SearchBarWidget({super.key});

  @override
  ConsumerState<SearchBarWidget> createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends ConsumerState<SearchBarWidget> {
  final _controller = TextEditingController();
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    // Initialize the controller with the current search query from the provider
    // This ensures that if the user navigates away and back, the search bar
    // reflects the current search state.
    final currentQuery = ref.read(searchQueryProvider);
    _controller.text = currentQuery;
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _controller.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      ref.read(searchQueryProvider.notifier).setSearchQuery(query);
    });
  }

  @override
  Widget build(BuildContext context) {
    // Listen to the provider to update the text field if the query is cleared externally
    ref.listen<String>(searchQueryProvider, (previous, next) {
      if (next.isEmpty && _controller.text.isNotEmpty) {
        _controller.clear();
      } else if (next != _controller.text) {
        _controller.text = next;
        // Move cursor to the end
        _controller.selection = TextSelection.fromPosition(
          TextPosition(offset: _controller.text.length),
        );
      }
    });

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: ShadInput(
        controller: _controller,
        placeholder: const Text('Search documents...'),
        leading: const Padding(
          padding: EdgeInsets.only(left: 8.0, right: 4.0),
          child: Icon(Icons.search, size: 16),
        ),
        onChanged: _onSearchChanged,
      ),
    );
  }
}
