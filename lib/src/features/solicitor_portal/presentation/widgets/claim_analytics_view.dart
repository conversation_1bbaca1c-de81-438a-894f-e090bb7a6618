import 'package:flutter/foundation.dart' show kIsWeb; // Added for kIsWeb
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart'; // For date formatting
import 'package:fl_chart/fl_chart.dart'; // For charts
import 'package:csv/csv.dart'; // Added for CSV generation
// Conditional import for html, using a stub for non-web platforms
import 'dart:html'
    if (dart.library.io) 'package:three_pay_group_litigation_platform/src/core/utils/html_stub.dart'
    as html;
import 'package:shadcn_ui/shadcn_ui.dart'; // Added for ShadButton and LucideIcons
// import 'package:shadcn_flutter/shadcn_flutter.dart' as shad; // Removed problematic import

import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/data/models/user_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/solicitor_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/application/providers/analytics_providers.dart'
    show ClaimAnalyticsData, ClaimAnalyticsFilter, claimAnalyticsProvider;

// State providers for filters
final _startDateProvider = StateProvider.autoDispose<DateTime?>((ref) => null);
final _endDateProvider = StateProvider.autoDispose<DateTime?>((ref) => null);
final _selectedSolicitorIdProvider = StateProvider.autoDispose<String?>(
  (ref) => null,
);

// Provider to check if the current user is a firm admin
final isFirmAdminProvider = Provider.autoDispose<bool>((ref) {
  final pbClient = ref.watch(pocketBaseClientProvider); // Corrected provider
  final currentUser = pbClient.authStore.model;
  if (currentUser == null) return false;

  final solicitorProfileAsync = ref.watch(currentSolicitorProfileProvider);
  return solicitorProfileAsync.when(
    data: (profile) => profile?.puStatus == 'approved',
    loading: () => false,
    error: (_, __) => false,
  );
});

// Provider to fetch the current solicitor's profile
final currentSolicitorProfileProvider =
    FutureProvider.autoDispose<SolicitorProfileModel?>((ref) async {
      final pbClient = ref.watch(pocketBaseClientProvider);
      final currentUser = pbClient.authStore.model;
      if (currentUser == null) return null;
      try {
        final profileRecords = await pbClient
            .collection('solicitor_profiles')
            .getList(filter: 'user_id = "${currentUser.id}"', perPage: 1);
        if (profileRecords.items.isNotEmpty) {
          return SolicitorProfileModel.fromJson(
            profileRecords.items.first.toJson(),
          );
        }
      } catch (e) {
        print('Error fetching current solicitor profile: $e');
        return null;
      }
      return null;
    });

// Provider to fetch firm's solicitors (main PU + additional users)
final firmSolicitorsProvider = FutureProvider.autoDispose<List<User>>((
  ref,
) async {
  final pbClient = ref.watch(pocketBaseClientProvider);
  final currentProfile = await ref.watch(
    currentSolicitorProfileProvider.future,
  );

  if (currentProfile == null) return [];

  List<String> userIdsToFetch = [];
  userIdsToFetch.add(currentProfile.userId);
  userIdsToFetch.addAll(currentProfile.additionalUsers);

  if (userIdsToFetch.isEmpty) return [];

  final filterString = userIdsToFetch.map((id) => 'id="$id"').join(' || ');

  try {
    final userRecords = await pbClient
        .collection('users')
        .getFullList(filter: filterString);
    return userRecords.map((record) => User.fromJson(record.toJson())).toList();
  } catch (e) {
    print('Error fetching firm solicitors: $e');
    return [];
  }
});

// Combined filter provider that depends on individual filter states
final _currentClaimAnalyticsFilterProvider =
    Provider.autoDispose<ClaimAnalyticsFilter>((ref) {
      final startDate = ref.watch(_startDateProvider);
      final endDate = ref.watch(_endDateProvider);
      final solicitorId = ref.watch(_selectedSolicitorIdProvider);
      return ClaimAnalyticsFilter(
        startDate: startDate,
        endDate: endDate,
        solicitorId: solicitorId,
      );
    });

class ClaimAnalyticsView extends ConsumerWidget {
  const ClaimAnalyticsView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentFilter = ref.watch(_currentClaimAnalyticsFilterProvider);
    final claimAnalyticsAsync = ref.watch(
      claimAnalyticsProvider(currentFilter),
    );

    return Scaffold(
      body: claimAnalyticsAsync.when(
        data: (data) {
          return RefreshIndicator(
            onRefresh: () async {
              ref.invalidate(claimAnalyticsProvider(currentFilter));
            },
            child: ListView(
              padding: const EdgeInsets.all(16.0),
              children: <Widget>[
                _buildFilters(context, ref),
                const SizedBox(height: 16), // Adjusted spacing
                ShadButton(
                  onPressed: () => _exportToCsv(data, ref),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(LucideIcons.download, size: 16),
                      const SizedBox(width: 8),
                      const Text('Export CSV'),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                _buildKpiCards(context, ref, data),
                const SizedBox(height: 24),
                _buildCharts(context, ref, data),
                const SizedBox(height: 24),
                // TODO: Add optional Data Table
              ],
            ),
          );
        },
        loading:
            () => Column(
              // Wrap in column to add disabled button
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Center(child: CircularProgressIndicator()),
                const SizedBox(height: 16),
                const ShadButton(
                  enabled: false,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(LucideIcons.download, size: 16),
                      SizedBox(width: 8),
                      Text('Export CSV'),
                    ],
                  ),
                ),
              ],
            ),
        error:
            (error, stackTrace) => Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Error loading claim analytics: $error',
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ShadButton(
                      // Changed to ShadButton
                      onPressed: () {
                        ref.invalidate(claimAnalyticsProvider(currentFilter));
                      },
                      child: const Text('Retry'),
                    ),
                    const SizedBox(height: 16),
                    const ShadButton(
                      // Disabled export button on error
                      enabled: false,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(LucideIcons.download, size: 16),
                          SizedBox(width: 8),
                          Text('Export CSV'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
      ),
    );
  }

  void _exportToCsv(ClaimAnalyticsData data, WidgetRef ref) {
    if (kIsWeb) {
      final List<List<dynamic>> rows = [];

      // Header row for KPIs
      rows.add(['KPI', 'Value']);
      rows.add(['Total Active Claims', data.totalActiveClaims]);
      rows.add([
        'Total Funding Secured',
        '£${data.totalFundingSecured.toStringAsFixed(2)}',
      ]);
      rows.add([
        'Avg. Claim Duration',
        data.averageClaimDuration != null
            ? '${data.averageClaimDuration!.inDays} days'
            : 'N/A',
      ]);
      rows.add([]); // Empty row for separation

      // Header row for Status Breakdown
      rows.add(['Status Breakdown - Status', 'Status Breakdown - Count']);
      data.statusBreakdown.forEach((status, count) {
        rows.add([status, count]);
      });

      final String csvData = const ListToCsvConverter().convert(rows);
      final blob = html.Blob([csvData], 'text/csv;charset=utf-8;');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor =
          html.AnchorElement(href: url)
            ..setAttribute("download", "claim_analytics_export.csv")
            ..click();
      html.Url.revokeObjectUrl(url);
    } else {
      // Handle non-web export
      ShadToaster.of(ref.context).show(
        // Use ref.context as this is a ConsumerWidget
        ShadToast.destructive(
          title: const Text('Export Not Available'),
          description: const Text(
            'CSV export is currently only available on web.',
          ),
        ),
      );
    }
  }

  Widget _buildFilters(BuildContext context, WidgetRef ref) {
    final startDate = ref.watch(_startDateProvider);
    final endDate = ref.watch(_endDateProvider);
    final selectedSolicitorId = ref.watch(_selectedSolicitorIdProvider);
    final isFirmAdmin = ref.watch(isFirmAdminProvider);
    final firmSolicitorsAsync = ref.watch(firmSolicitorsProvider);

    Future<void> selectDate(
      BuildContext context,
      StateController<DateTime?> dateController, {
      DateTime? initialDate,
      DateTime? firstDate,
      DateTime? lastDate,
    }) async {
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: initialDate ?? DateTime.now(),
        firstDate: firstDate ?? DateTime(2000),
        lastDate: lastDate ?? DateTime(2101),
      );
      if (picked != null) {
        dateController.state = picked;
      }
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Filters', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextButton.icon(
                    icon: const Icon(Icons.calendar_today),
                    label: Text(
                      startDate == null
                          ? 'Start Date'
                          : DateFormat.yMd().format(startDate),
                    ),
                    onPressed:
                        () => selectDate(
                          context,
                          ref.read(_startDateProvider.notifier),
                          initialDate: startDate,
                          lastDate: endDate ?? DateTime.now(),
                        ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextButton.icon(
                    icon: const Icon(Icons.calendar_today),
                    label: Text(
                      endDate == null
                          ? 'End Date'
                          : DateFormat.yMd().format(endDate),
                    ),
                    onPressed:
                        () => selectDate(
                          context,
                          ref.read(_endDateProvider.notifier),
                          initialDate: endDate,
                          firstDate: startDate,
                          lastDate: DateTime.now(),
                        ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (isFirmAdmin)
              firmSolicitorsAsync.when(
                data: (solicitors) {
                  if (solicitors.isEmpty) {
                    return const Text('No solicitors found for this firm.');
                  }
                  List<DropdownMenuItem<String?>> items = [
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('All Solicitors'),
                    ),
                    ...solicitors.map((solicitor) {
                      return DropdownMenuItem<String?>(
                        value: solicitor.id,
                        child: Text(solicitor.displayName),
                      );
                    }).toList(),
                  ];

                  final currentSolicitorInList =
                      selectedSolicitorId == null ||
                      solicitors.any((s) => s.id == selectedSolicitorId);

                  return DropdownButtonFormField<String?>(
                    decoration: const InputDecoration(
                      labelText: 'Filter by Solicitor',
                      border: OutlineInputBorder(),
                    ),
                    value: currentSolicitorInList ? selectedSolicitorId : null,
                    items: items,
                    onChanged: (String? newValue) {
                      ref.read(_selectedSolicitorIdProvider.notifier).state =
                          newValue;
                    },
                    hint: const Text('Select a Solicitor'),
                  );
                },
                loading:
                    () => const Row(
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 8),
                        Text('Loading solicitors...'),
                      ],
                    ),
                error: (err, stack) => Text('Error loading solicitors: $err'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildKpiCards(
    BuildContext context,
    WidgetRef ref,
    ClaimAnalyticsData data,
  ) {
    final totalActiveClaims = data.totalActiveClaims;
    final totalFundingSecured = data.totalFundingSecured;
    final averageClaimDuration = data.averageClaimDuration;

    String formatCurrency(double amount) {
      return '£${amount.toStringAsFixed(2)}';
    }

    String formatDuration(Duration? duration) {
      if (duration == null) return 'N/A';
      return '${duration.inDays} days';
    }

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Total Active Claims',
                        style: Theme.of(context).textTheme.titleMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '$totalActiveClaims',
                        style: Theme.of(context).textTheme.headlineMedium,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Total Funding Secured',
                        style: Theme.of(context).textTheme.titleMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        formatCurrency(totalFundingSecured),
                        style: Theme.of(context).textTheme.headlineMedium,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Text(
                  'Average Claim Duration',
                  style: Theme.of(context).textTheme.titleMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  formatDuration(averageClaimDuration),
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCharts(
    BuildContext context,
    WidgetRef ref,
    ClaimAnalyticsData data,
  ) {
    final statusBreakdown = data.statusBreakdown;

    final List<Color> pieColors = [
      Colors.blue.shade400,
      Colors.green.shade400,
      Colors.orange.shade400,
      Colors.red.shade400,
      Colors.purple.shade400,
      Colors.yellow.shade700,
      Colors.teal.shade400,
      Colors.pink.shade300,
      Colors.indigo.shade400,
      Colors.lime.shade600,
    ];

    List<PieChartSectionData> generateSections() {
      if (statusBreakdown.isEmpty) {
        return [
          PieChartSectionData(
            color: Colors.grey[300],
            value: 1,
            title: 'No Data',
            radius: 60,
            titleStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black54,
            ),
          ),
        ];
      }

      final List<PieChartSectionData> sections = [];
      int colorIndex = 0;
      double totalValue = statusBreakdown.values.fold(
        0,
        (sum, item) => sum + item.toDouble(),
      );

      if (totalValue == 0) {
        return [
          PieChartSectionData(
            color: Colors.grey[300],
            value: 1,
            title: 'No Claims',
            radius: 60,
            titleStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black54,
            ),
          ),
        ];
      }

      statusBreakdown.forEach((status, count) {
        final percentage = (count / totalValue) * 100;
        sections.add(
          PieChartSectionData(
            color: pieColors[colorIndex % pieColors.length],
            value: count.toDouble(),
            title: '${status}\n(${percentage.toStringAsFixed(0)}%)',
            radius: 80,
            titleStyle: const TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: [Shadow(color: Colors.black54, blurRadius: 2)],
            ),
            titlePositionPercentageOffset: 0.65,
          ),
        );
        colorIndex++;
      });
      return sections;
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Claim Status Breakdown',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              height: 280,
              child:
                  statusBreakdown.isEmpty && data.totalActiveClaims == 0
                      ? const Center(
                        child: Text(
                          'No claim data available to display chart.',
                        ),
                      )
                      : Row(
                        children: [
                          Expanded(
                            flex: 2,
                            child: PieChart(
                              PieChartData(
                                sections: generateSections(),
                                borderData: FlBorderData(show: false),
                                sectionsSpace: 2,
                                centerSpaceRadius: 50,
                                pieTouchData: PieTouchData(
                                  touchCallback: (
                                    FlTouchEvent event,
                                    pieTouchResponse,
                                  ) {
                                    // TODO: Implement touch interactions if needed
                                  },
                                ),
                              ),
                            ),
                          ),
                          if (statusBreakdown.isNotEmpty &&
                              data.totalActiveClaims > 0)
                            Expanded(
                              flex: 1,
                              child: ListView.builder(
                                itemCount: statusBreakdown.length,
                                itemBuilder: (context, index) {
                                  final status = statusBreakdown.keys.elementAt(
                                    index,
                                  );
                                  final color =
                                      pieColors[index % pieColors.length];
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 2.0,
                                    ),
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 12,
                                          height: 12,
                                          color: color,
                                        ),
                                        const SizedBox(width: 6),
                                        Text(
                                          status,
                                          style: const TextStyle(fontSize: 12),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                        ],
                      ),
            ),
          ],
        ),
      ),
    );
  }
}
