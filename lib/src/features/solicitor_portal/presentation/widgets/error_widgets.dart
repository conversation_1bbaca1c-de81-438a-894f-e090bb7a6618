import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../utils/responsive_layout.dart';

/// Error widgets for the solicitor portal
/// Provides consistent error states and user-friendly error messages
class SolicitorErrorWidgets {
  /// Generic error state widget
  static Widget errorState({
    required BuildContext context,
    required String message,
    String? title,
    VoidCallback? onRetry,
    IconData? icon,
    String? retryButtonText,
  }) {
    final theme = ShadTheme.of(context);
    final isDesktop = SolicitorResponsiveLayout.isDesktop(context);

    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isDesktop ? 500 : double.infinity,
        ),
        padding: SolicitorResponsiveLayout.getResponsivePadding(context),
        child: ShadCard(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon ?? LucideIcons.circleAlert,
                  size: SolicitorResponsiveLayout.getIconSize(
                    context,
                    baseSize: 48,
                  ),
                  color: theme.colorScheme.destructive,
                ),
                const SizedBox(height: 16),
                if (title != null) ...[
                  Text(
                    title,
                    style: theme.textTheme.h3.copyWith(
                      color: theme.colorScheme.foreground,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                ],
                Text(
                  message,
                  style: theme.textTheme.p.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (onRetry != null) ...[
                  const SizedBox(height: 24),
                  ShadButton(
                    onPressed: onRetry,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          LucideIcons.refreshCw,
                          size: 16,
                          color: theme.colorScheme.primaryForeground,
                        ),
                        const SizedBox(width: 8),
                        Text(retryButtonText ?? 'Try Again'),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Network error widget
  static Widget networkError({
    required BuildContext context,
    VoidCallback? onRetry,
  }) {
    return errorState(
      context: context,
      title: 'Connection Error',
      message:
          'Unable to connect to the server. Please check your internet connection and try again.',
      icon: LucideIcons.wifiOff,
      onRetry: onRetry,
      retryButtonText: 'Retry Connection',
    );
  }

  /// Not found error widget
  static Widget notFound({
    required BuildContext context,
    String? message,
    VoidCallback? onGoBack,
  }) {
    return errorState(
      context: context,
      title: 'Not Found',
      message: message ?? 'The requested resource could not be found.',
      icon: LucideIcons.search,
      onRetry: onGoBack,
      retryButtonText: 'Go Back',
    );
  }

  /// Permission denied error widget
  static Widget permissionDenied({
    required BuildContext context,
    String? message,
    VoidCallback? onGoBack,
  }) {
    return errorState(
      context: context,
      title: 'Access Denied',
      message: message ?? 'You don\'t have permission to access this resource.',
      icon: LucideIcons.lock,
      onRetry: onGoBack,
      retryButtonText: 'Go Back',
    );
  }

  /// Empty state widget
  static Widget emptyState({
    required BuildContext context,
    required String message,
    String? title,
    VoidCallback? onAction,
    String? actionButtonText,
    IconData? icon,
  }) {
    final theme = ShadTheme.of(context);
    final isDesktop = SolicitorResponsiveLayout.isDesktop(context);

    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isDesktop ? 500 : double.infinity,
        ),
        padding: SolicitorResponsiveLayout.getResponsivePadding(context),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon ?? LucideIcons.inbox,
              size: SolicitorResponsiveLayout.getIconSize(
                context,
                baseSize: 64,
              ),
              color: theme.colorScheme.mutedForeground,
            ),
            const SizedBox(height: 16),
            if (title != null) ...[
              Text(
                title,
                style: theme.textTheme.h3.copyWith(
                  color: theme.colorScheme.foreground,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
            ],
            Text(
              message,
              style: theme.textTheme.p.copyWith(
                color: theme.colorScheme.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
            if (onAction != null) ...[
              const SizedBox(height: 24),
              ShadButton(
                onPressed: onAction,
                child: Text(actionButtonText ?? 'Get Started'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// No applications empty state
  static Widget noApplications({
    required BuildContext context,
    VoidCallback? onCreateNew,
  }) {
    return emptyState(
      context: context,
      title: 'No Applications Yet',
      message:
          'You haven\'t submitted any funding applications. Create your first application to get started.',
      icon: LucideIcons.fileText,
      onAction: onCreateNew,
      actionButtonText: 'Create Application',
    );
  }

  /// No claims empty state
  static Widget noClaims({
    required BuildContext context,
    VoidCallback? onCreateNew,
  }) {
    return emptyState(
      context: context,
      title: 'No Claims Found',
      message:
          'No claims match your current filters. Try adjusting your search criteria.',
      icon: LucideIcons.search,
      onAction: onCreateNew,
      actionButtonText: 'Clear Filters',
    );
  }

  /// Offline state widget
  static Widget offlineState({
    required BuildContext context,
    VoidCallback? onRetry,
  }) {
    return errorState(
      context: context,
      title: 'You\'re Offline',
      message: 'Please check your internet connection and try again.',
      icon: LucideIcons.cloudOff,
      onRetry: onRetry,
      retryButtonText: 'Try Again',
    );
  }

  /// Server error widget
  static Widget serverError({
    required BuildContext context,
    VoidCallback? onRetry,
  }) {
    return errorState(
      context: context,
      title: 'Server Error',
      message:
          'Something went wrong on our end. Please try again in a few moments.',
      icon: LucideIcons.server,
      onRetry: onRetry,
      retryButtonText: 'Try Again',
    );
  }

  /// Validation error widget
  static Widget validationError({
    required BuildContext context,
    required String message,
    VoidCallback? onDismiss,
  }) {
    final theme = ShadTheme.of(context);

    return Container(
      margin: SolicitorResponsiveLayout.getResponsivePadding(context),
      child: ShadCard(
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color: theme.colorScheme.destructive.withValues(alpha: 0.3),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                LucideIcons.triangleAlert,
                color: theme.colorScheme.destructive,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  message,
                  style: theme.textTheme.p.copyWith(
                    color: theme.colorScheme.destructive,
                  ),
                ),
              ),
              if (onDismiss != null) ...[
                const SizedBox(width: 12),
                ShadButton.ghost(
                  onPressed: onDismiss,
                  child: Icon(
                    LucideIcons.x,
                    size: 16,
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Loading error snackbar
  static void showErrorSnackBar({
    required BuildContext context,
    required String message,
    VoidCallback? onRetry,
  }) {
    final theme = ShadTheme.of(context);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              LucideIcons.circleAlert,
              color: theme.colorScheme.destructiveForeground,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  color: theme.colorScheme.destructiveForeground,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: theme.colorScheme.destructive,
        action:
            onRetry != null
                ? SnackBarAction(
                  label: 'Retry',
                  textColor: theme.colorScheme.destructiveForeground,
                  onPressed: onRetry,
                )
                : null,
        behavior: SnackBarBehavior.floating,
        margin: SolicitorResponsiveLayout.getResponsivePadding(context),
      ),
    );
  }

  /// Success snackbar
  static void showSuccessSnackBar({
    required BuildContext context,
    required String message,
  }) {
    final theme = ShadTheme.of(context);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              LucideIcons.circleCheck,
              color: theme.colorScheme.primaryForeground,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: TextStyle(color: theme.colorScheme.primaryForeground),
              ),
            ),
          ],
        ),
        backgroundColor: theme.colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        margin: SolicitorResponsiveLayout.getResponsivePadding(context),
      ),
    );
  }
}
