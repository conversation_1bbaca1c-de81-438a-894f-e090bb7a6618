import 'dart:typed_data'; // For Uint8List
import 'package:file_picker/file_picker.dart'; // For file picking
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart' as li; // For icons, with prefix
import 'package:pocketbase/pocketbase.dart';
import 'package:shadcn_ui/shadcn_ui.dart'; // Exports LucideIcons from lucide_icons_flutter
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/toast_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:intl/intl.dart'; // Added for DateFormat

// Import DocumentManagementService
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/services/document_management_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/providers/upload_progress_provider.dart'; // To watch upload progress
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/widgets/firm_documents/upload_progress_indicator_widget.dart'; // Import the progress widget
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/models/upload_progress_model.dart'; // Ensure this import is present

// Import new widget shells
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/widgets/firm_documents/document_list_view.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/widgets/firm_documents/search_bar_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/widgets/firm_documents/bulk_action_toolbar_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/widgets/firm_documents/filter_panel_widget.dart'; // Though not explicitly placed, good for context
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/providers/selected_documents_provider.dart'; // Added import

// --- Models (Simplified for now) ---
// These would typically be in their own files
class FirmDocument {
  final String id;
  final String name;
  final String type; // 'folder' or 'file'
  final String? parentFolder;
  final String firm;
  final String uploadedBy;
  final DateTime created;
  final DateTime updated;
  final String? uploaderName; // Added for expanded data

  FirmDocument({
    required this.id,
    required this.name,
    required this.type,
    this.parentFolder,
    required this.firm,
    required this.uploadedBy,
    required this.created,
    required this.updated,
    this.uploaderName, // Added
  });

  factory FirmDocument.fromRecord(RecordModel record) {
    String? uploaderName;
    if (record.expand.containsKey('uploaded_by') &&
        record.expand['uploaded_by'] != null &&
        record.expand['uploaded_by']!.isNotEmpty) {
      final uploaderRecord = record.expand['uploaded_by']!.first;
      uploaderName = uploaderRecord.data['name'] as String?;
    }

    return FirmDocument(
      id: record.id,
      name: record.data['name'] ?? '',
      type: record.data['type'] ?? 'file',
      parentFolder: record.data['parent_folder'],
      firm: record.data['firm'] ?? '',
      uploadedBy: record.data['uploaded_by'] ?? '',
      created: DateTime.tryParse(record.created) ?? DateTime.now(),
      updated: DateTime.tryParse(record.updated) ?? DateTime.now(),
      uploaderName: uploaderName,
    );
  }
}

// --- Helper Class for Solicitor Info ---
class SolicitorFirmInfo {
  final String? solicitorProfileId;
  final String? lawFirmName;

  SolicitorFirmInfo({this.solicitorProfileId, this.lawFirmName});
}

// --- Providers ---

final solicitorFirmInfoProvider = FutureProvider<SolicitorFirmInfo?>((
  ref,
) async {
  final pb = ref.watch(pocketBaseClientProvider);
  final currentUser = pb.authStore.model;

  if (currentUser == null) {
    debugPrint('solicitorFirmInfoProvider: User not authenticated.');
    return null;
  }

  try {
    debugPrint(
      'solicitorFirmInfoProvider: Fetching solicitor profile for user ID: ${currentUser.id}',
    );
    // Ensure the filter uses the correct field name for the user relation in 'solicitor_profiles'.
    // Based on schema inspection, it's 'user_id'.
    final solicitorProfileRecord = await pb
        .collection('solicitor_profiles')
        .getFirstListItem('user_id="${currentUser.id}"');

    final solicitorProfileId = solicitorProfileRecord.id;
    final lawFirmName = solicitorProfileRecord.data['law_firm_name'] as String?;

    debugPrint(
      'solicitorFirmInfoProvider: Fetched solicitorProfileId "$solicitorProfileId" and lawFirmName "$lawFirmName".',
    );

    if (solicitorProfileId.isEmpty) {
      debugPrint('solicitorFirmInfoProvider: Solicitor Profile ID is empty.');
      return null;
    }
    if (lawFirmName == null || lawFirmName.isEmpty) {
      debugPrint('solicitorFirmInfoProvider: Law Firm Name is null or empty.');
      // Decide if this is an error or if we can proceed without it for some operations.
      // For creating firm_documents, both are likely needed.
    }

    return SolicitorFirmInfo(
      solicitorProfileId: solicitorProfileId,
      lawFirmName: lawFirmName,
    );
  } catch (e) {
    debugPrint(
      'solicitorFirmInfoProvider: Error fetching solicitor profile: ${e.toString()}',
    );
    debugPrint(
      'solicitorFirmInfoProvider: Ensure a "solicitor_profiles" record exists for user ID "${currentUser.id}" and the "user_id" field is correctly queryable.',
    );
    return null;
  }
});

final selectedFolderIdProvider = StateProvider<String?>((ref) => null);

final allFirmFoldersProvider = FutureProvider<List<FirmDocument>>((ref) async {
  final pb = ref.watch(pocketBaseClientProvider);
  final firmInfo = await ref.watch(solicitorFirmInfoProvider.future);

  if (firmInfo?.solicitorProfileId == null) {
    debugPrint(
      'allFirmFoldersProvider: Solicitor profile ID is null, cannot fetch folders.',
    );
    return [];
  }
  final solicitorProfileId = firmInfo!.solicitorProfileId!;

  try {
    // Documents are filtered by the solicitor's profile ID (which acts as the 'firm' context)
    final records = await pb
        .collection('firm_documents')
        .getFullList(
          filter: 'type="folder" && firm="$solicitorProfileId"',
          sort: '+name',
        );
    return records.map((record) => FirmDocument.fromRecord(record)).toList();
  } catch (e) {
    debugPrint('Error fetching all firm folders: $e');
    return [];
  }
});

final documentFilterTypeProvider = StateProvider<String?>((ref) => null);
final documentFilterDateRangeProvider = StateProvider<DateTimeRange?>(
  (ref) => null,
);
final documentSortOptionProvider = StateProvider<String>((ref) => 'name');
final documentSortAscendingProvider = StateProvider<bool>((ref) => true);

final childItemsProvider = FutureProvider<List<FirmDocument>>((ref) async {
  final pb = ref.watch(pocketBaseClientProvider);
  final firmInfo = await ref.watch(solicitorFirmInfoProvider.future);
  final parentId = ref.watch(selectedFolderIdProvider);

  if (firmInfo?.solicitorProfileId == null) {
    debugPrint(
      'childItemsProvider: Solicitor profile ID is null, cannot fetch child items.',
    );
    return [];
  }
  final solicitorProfileId = firmInfo!.solicitorProfileId!;

  final filterFileType = ref.watch(documentFilterTypeProvider);
  final filterDateRange = ref.watch(documentFilterDateRangeProvider);
  final sortOption = ref.watch(documentSortOptionProvider);
  final sortAscending = ref.watch(documentSortAscendingProvider);

  if (solicitorProfileId == null) {
    return []; // Should be caught by the check above
  }

  List<String> filterParts = ['firm="$solicitorProfileId"'];
  if (parentId == null) {
    filterParts.add('parent_folder=null');
  } else {
    filterParts.add('parent_folder="$parentId"');
  }

  if (filterFileType != null &&
      filterFileType.isNotEmpty &&
      filterFileType != 'All') {
    filterParts.add('name ~ "%.$filterFileType" && type="file"');
  }

  if (filterDateRange != null) {
    final startDate = DateFormat(
      "yyyy-MM-dd HH:mm:ss.SSS'Z'",
    ).format(filterDateRange.start.toUtc());
    final endDate = DateFormat(
      "yyyy-MM-dd HH:mm:ss.SSS'Z'",
    ).format(filterDateRange.end.toUtc().add(const Duration(days: 1)));
    filterParts.add('(created >= "$startDate" && created < "$endDate")');
  }

  String filterString = filterParts.join(' && ');

  String sortPrefix = sortAscending ? '+' : '-';
  String sortField = sortOption;

  String finalSortString = '-type,${sortPrefix}${sortField}';

  if (sortOption == 'type') {
    finalSortString = '${sortPrefix}type,${sortPrefix}name';
  }

  try {
    final records = await pb
        .collection('firm_documents')
        .getFullList(
          filter: filterString,
          sort: finalSortString,
          expand: 'uploaded_by',
        );
    return records.map((record) => FirmDocument.fromRecord(record)).toList();
  } catch (e) {
    debugPrint('Error fetching child items: $e');
    return [];
  }
});

class FirmDocumentsPage extends ConsumerStatefulWidget {
  static const routeName = '/firm-documents';

  const FirmDocumentsPage({super.key});

  @override
  ConsumerState<FirmDocumentsPage> createState() => _FirmDocumentsPageState();
}

class _FirmDocumentsPageState extends ConsumerState<FirmDocumentsPage> {
  final _folderNameController = TextEditingController();
  final _renameNameController = TextEditingController();
  final _formKey = GlobalKey<ShadFormState>();
  final _renameFormKey = GlobalKey<ShadFormState>();

  bool _isRenaming = false;
  bool _isDeleting = false;

  bool _isLeftPaneVisible = true;
  bool _isRightPaneVisible = true;

  final List<String> _fileTypeOptions = [
    'All',
    'pdf',
    'jpg',
    'jpeg',
    'png',
    'gif',
  ];

  final Map<String, String> _sortOptionsMap = {
    'name': 'Name',
    'updated': 'Last Modified Date',
    'type': 'Type (Folder/File)',
  };

  @override
  void dispose() {
    _folderNameController.dispose();
    _renameNameController.dispose();
    super.dispose();
  }

  Future<void> _renameItem(
    BuildContext context,
    String itemId,
    String currentName,
  ) async {
    _renameNameController.text = currentName;
    final selectedFolderId = ref.read(selectedFolderIdProvider);

    showShadDialog(
      context: context,
      builder:
          (dialogContext) => ShadDialog(
            title: Text(
              'Rename ${currentName.length > 20 ? "${currentName.substring(0, 17)}..." : currentName}',
            ),
            description: const Text('Enter the new name for the item.'),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 400),
              child: ShadForm(
                key: _renameFormKey,
                child: ShadInputFormField(
                  id: 'newItemName',
                  controller: _renameNameController,
                  label: const Text('New Name'),
                  placeholder: const Text('Enter new name'),
                  validator: (v) {
                    if (v == null || v.isEmpty) {
                      return 'Name cannot be empty.';
                    }
                    if (v.trim() == currentName) {
                      return 'New name must be different from the current name.';
                    }
                    return null;
                  },
                ),
              ),
            ),
            actions: [
              ShadButton.outline(
                child: const Text('Cancel'),
                onPressed: () => Navigator.of(dialogContext).pop(),
              ),
              ShadButton(
                child: const Text('Rename'),
                onPressed: () async {
                  if (_renameFormKey.currentState!.saveAndValidate()) {
                    final newName = _renameNameController.text.trim();
                    Navigator.of(dialogContext).pop();

                    if (!mounted) return;
                    setState(() => _isRenaming = true);
                    final pb = ref.read(pocketBaseClientProvider);
                    try {
                      await pb
                          .collection('firm_documents')
                          .update(itemId, body: {'name': newName});
                      if (!mounted) return;
                      NotificationService.showSuccess(
                        context,
                        '"$currentName" renamed to "$newName" successfully.',
                      );
                      ref.invalidate(allFirmFoldersProvider);
                      ref.invalidate(childItemsProvider);
                    } catch (e) {
                      if (!mounted) return;
                      NotificationService.showError(
                        context,
                        'Failed to rename item: ${e.toString()}',
                      );
                      debugPrint('Error renaming item $itemId: $e');
                    } finally {
                      if (mounted) {
                        setState(() => _isRenaming = false);
                      }
                    }
                  }
                },
              ),
            ],
          ),
    );
  }

  Future<void> _deleteItem(BuildContext context, FirmDocument item) async {
    final confirmDelete = await showShadDialog<bool>(
      context: context,
      builder:
          (dialogContext) => ShadDialog.alert(
            title: Text('Delete ${item.type == 'folder' ? 'Folder' : 'File'}?'),
            description: Text(
              item.type == 'folder'
                  ? 'Are you sure you want to delete the folder "${item.name}" and all its contents? This action cannot be undone.'
                  : 'Are you sure you want to delete the file "${item.name}"? This action cannot be undone.',
            ),
            actions: [
              ShadButton.outline(
                child: const Text('Cancel'),
                onPressed: () => Navigator.of(dialogContext).pop(false),
              ),
              ShadButton.destructive(
                child: const Text('Delete'),
                onPressed: () => Navigator.of(dialogContext).pop(true),
              ),
            ],
          ),
    );

    if (confirmDelete == true) {
      if (!mounted) return;
      setState(() => _isDeleting = true);
      final pb = ref.read(pocketBaseClientProvider);

      try {
        await _performDeletion(pb, item);
        if (!mounted) return;
        NotificationService.showSuccess(
          context,
          '${item.type == 'folder' ? 'Folder' : 'File'} "${item.name}" deleted successfully.',
        );
        ref.invalidate(allFirmFoldersProvider);
        ref.invalidate(childItemsProvider);
        if (item.type == 'folder' &&
            ref.read(selectedFolderIdProvider) == item.id) {
          ref.read(selectedFolderIdProvider.notifier).state = item.parentFolder;
        }
      } catch (e) {
        if (!mounted) return;
        NotificationService.showError(
          context,
          'Failed to delete ${item.type}: ${e.toString()}',
        );
        debugPrint('Error deleting item ${item.id}: $e');
      } finally {
        if (mounted) {
          setState(() => _isDeleting = false);
        }
      }
    }
  }

  Future<void> _performDeletion(PocketBase pb, FirmDocument item) async {
    if (item.type == 'folder') {
      final children = await pb
          .collection('firm_documents')
          .getFullList(filter: 'parent_folder="${item.id}"');

      for (final childRecord in children) {
        final childDoc = FirmDocument.fromRecord(childRecord);
        await _performDeletion(pb, childDoc);
      }
    }
    await pb.collection('firm_documents').delete(item.id);
  }

  Future<void> _createNewFolder(
    String folderName,
    String? parentFolderId,
  ) async {
    final pb = ref.read(pocketBaseClientProvider);
    final firmInfo = await ref.read(solicitorFirmInfoProvider.future);

    final authModel = pb.authStore.model;
    if (authModel == null || authModel.id == null) {
      if (!mounted) return;
      NotificationService.showError(
        context,
        'User not authenticated. Please log in.',
      );
      return;
    }
    final currentUserId = authModel.id;

    if (firmInfo == null || firmInfo.solicitorProfileId == null) {
      // lawFirmName can be null if not set
      if (!mounted) return;
      NotificationService.showError(
        context,
        'Could not determine firm information. Please ensure your solicitor profile is complete and linked.',
      );
      debugPrint(
        '_createNewFolder: firmInfo is null or solicitorProfileId is null. ProfileID: ${firmInfo?.solicitorProfileId}, FirmName: ${firmInfo?.lawFirmName}',
      );
      return;
    }

    final solicitorProfileIdForFirmRelation = firmInfo.solicitorProfileId!;
    // lawFirmName can be null, so provide a default or handle it if your DB schema for firm_name_text requires a value
    final lawFirmNameForText = firmInfo.lawFirmName ?? 'Unknown Firm Name';

    debugPrint(
      '_createNewFolder: Attempting with solicitorProfileId (for firm relation): "$solicitorProfileIdForFirmRelation", lawFirmName (for firm_name_text): "$lawFirmNameForText"',
    );

    try {
      await pb
          .collection('firm_documents')
          .create(
            body: {
              'name': folderName,
              'type': 'folder',
              'parent_folder': parentFolderId,
              'firm':
                  solicitorProfileIdForFirmRelation, // Relation to solicitor_profiles.id
              'firm_name_text': lawFirmNameForText, // New text field
              'uploaded_by': currentUserId,
            },
          );
      if (!mounted) return;
      NotificationService.showSuccess(
        context,
        'Folder "$folderName" created successfully.',
      );
      ref.invalidate(allFirmFoldersProvider);
      ref.invalidate(childItemsProvider);
      if (mounted) Navigator.of(context).pop();
    } catch (e) {
      if (!mounted) return;
      NotificationService.showError(
        context,
        'Failed to create folder: ${e.toString()}',
      );
      debugPrint(
        'Error creating folder: $e. Payload attempted: name: $folderName, type: folder, parent_folder: $parentFolderId, firm: $solicitorProfileIdForFirmRelation, firm_name_text: $lawFirmNameForText, uploaded_by: $currentUserId',
      );
    }
  }

  // _uploadDocument method was here, it's now removed as its logic is in DocumentManagementService.

  Future<void> _pickAndUploadFile() async {
    final firmInfoAsyncValue = ref.watch(
      solicitorFirmInfoProvider,
    ); // Use new provider
    final firmInfo = firmInfoAsyncValue.asData?.value;

    if (firmInfo?.solicitorProfileId == null) {
      if (mounted) {
        NotificationService.showError(
          context,
          'Cannot upload file: Firm information not available. Please ensure your solicitor profile is complete.',
        );
      }
      debugPrint(
        '_pickAndUploadFile: Solicitor profile ID is null. Cannot proceed with upload.',
      );
      return;
    }
    final solicitorProfileIdForUpload = firmInfo!.solicitorProfileId!;

    final result = await FilePicker.platform.pickFiles(
      type: FileType.any,
      allowMultiple: false,
    );

    if (result != null && result.files.single.bytes != null) {
      final platformFile = result.files.single;

      if (!mounted) return;

      final docService = ref.read(documentManagementServiceProvider);
      try {
        // Call the service to upload the file. Progress and status will be handled by the provider.
        // Pass the solicitorProfileId to the uploadFile service method.
        // The DocumentManagementService.uploadFile expects a 'firmId' which, in our context,
        // is the solicitor_profiles.id for the 'firm' relation in 'firm_documents'.
        // It will also need to be updated to populate 'firm_name_text'.
        docService.uploadFile(
          platformFile,
          solicitorProfileIdForUpload,
          firmInfo.lawFirmName,
        );
      } catch (e) {
        // Fallback error handling, though the service should manage this via the provider.
        if (mounted) {
          NotificationService.showError(
            context,
            'Upload initiation failed: ${e.toString()}',
          );
        }
        final uploadNotifier = ref.read(
          uploadProgressNotifierProvider.notifier,
        );
        uploadNotifier.updateUpload(
          UploadProgressModel(
            fileId: platformFile.name,
            fileName: platformFile.name,
            status: UploadStatus.failed,
            errorMessage: 'Upload initiation failed: ${e.toString()}',
          ),
        );
        debugPrint('Error calling upload service from page: $e');
      }
    } else {
      // User cancelled picker or file was invalid. No notification needed usually.
      if (mounted) {
        // print('File selection cancelled or file is invalid.');
      }
    }
  }

  void _showNewFolderDialog() {
    _folderNameController.clear();
    final selectedFolder = ref.read(selectedFolderIdProvider);

    showShadDialog(
      context: context,
      builder:
          (context) => ShadDialog(
            title: const Text('Create New Folder'),
            description: Text(
              selectedFolder == null
                  ? 'Create a new folder in the root.'
                  : 'Create a new subfolder.',
            ),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 400),
              child: ShadForm(
                key: _formKey,
                child: ShadInputFormField(
                  id: 'folderName',
                  controller: _folderNameController,
                  label: const Text('Folder Name'),
                  placeholder: const Text('Enter folder name'),
                  validator: (v) {
                    if (v == null || v.isEmpty) {
                      return 'Folder name cannot be empty.';
                    }
                    return null;
                  },
                ),
              ),
            ),
            actions: [
              ShadButton.outline(
                child: const Text('Cancel'),
                onPressed: () => Navigator.of(context).pop(),
              ),
              ShadButton(
                child: const Text('Create'),
                onPressed: () {
                  if (_formKey.currentState!.saveAndValidate()) {
                    _createNewFolder(
                      _folderNameController.text.trim(),
                      selectedFolder,
                    );
                  }
                },
              ),
            ],
          ),
    );
  }

  List<Widget> _buildFolderTree(
    List<FirmDocument> folders,
    String? parentId,
    int depth,
  ) {
    final shadTheme = ShadTheme.of(context); // Explicitly use ShadTheme
    return folders.where((folder) => folder.parentFolder == parentId).expand((
      folder,
    ) {
      final isSelected = ref.watch(selectedFolderIdProvider) == folder.id;
      return [
        Padding(
          padding: EdgeInsets.only(left: depth * 20.0),
          child: InkWell(
            onTap: () {
              ref.read(selectedFolderIdProvider.notifier).state = folder.id;
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? shadTheme.colorScheme.primary.withOpacity(0.1)
                        : Colors.transparent,
                borderRadius: shadTheme.radius, // Use ShadTheme's radius
              ),
              child: Row(
                children: [
                  Icon(
                    li.LucideIcons.folder,
                    size: 18,
                    color:
                        isSelected
                            ? shadTheme.colorScheme.primary
                            : shadTheme.colorScheme.foreground.withOpacity(0.7),
                  ), // Use ShadTheme's colorScheme
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      folder.name,
                      style: shadTheme.textTheme.small.copyWith(
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ), // Changed to small
                ],
              ),
            ),
          ),
        ),
        ..._buildFolderTree(folders, folder.id, depth + 1),
      ];
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final shadTheme = ShadTheme.of(
      context,
    ); // Use shadTheme consistently in build method
    final allFoldersAsync = ref.watch(allFirmFoldersProvider);
    final childItemsAsync = ref.watch(childItemsProvider);
    final selectedDocuments = ref.watch(
      selectedDocumentsNotifierProvider,
    ); // Watch selected documents
    final showBulkActions =
        selectedDocuments.isNotEmpty; // Determine if toolbar should be shown
    // final selectedFolderId = ref.watch(selectedFolderIdProvider); // For context if needed

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(li.LucideIcons.arrowLeft),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text('Firm Documents Management'),
        elevation: 0,
        backgroundColor: shadTheme.colorScheme.background, // Use shadTheme
        foregroundColor: shadTheme.colorScheme.foreground, // Use shadTheme
        actions: [
          // Placeholder for global actions like settings or help
        ],
      ),
      body: allFoldersAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error:
            (err, stack) => Center(child: Text('Error loading folders: $err')),
        data: (allFolders) {
          return childItemsAsync.when(
            loading:
                () => Row(
                  children: [
                    if (_isLeftPaneVisible)
                      Container(
                        width: 280,
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(
                              color: shadTheme.colorScheme.border,
                            ),
                          ),
                        ), // Use shadTheme
                        child: const Center(child: Text("Loading folders...")),
                      ),
                    const Expanded(
                      child: Center(child: CircularProgressIndicator()),
                    ),
                  ],
                ),
            error:
                (err, stack) =>
                    Center(child: Text('Error loading documents: $err')),
            data: (childItems) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left Pane (Folder Tree)
                  if (_isLeftPaneVisible)
                    Container(
                      width: 280,
                      decoration: BoxDecoration(
                        border: Border(
                          right: BorderSide(
                            color: shadTheme.colorScheme.border,
                          ), // Use shadTheme
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Folders',
                                  style: shadTheme.textTheme.h4,
                                ), // Use shadTheme
                                ShadTooltip(
                                  builder:
                                      (context) =>
                                          const Text('Create New Folder'),
                                  child: ShadIconButton.ghost(
                                    icon: const Icon(
                                      li.LucideIcons.plusCircle,
                                      size: 20,
                                    ),
                                    onPressed: _showNewFolderDialog,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          ShadSeparator.horizontal(), // Assuming horizontal separator
                          Expanded(
                            child:
                                allFolders.isEmpty
                                    ? const Center(
                                      child: Text(
                                        'No folders yet.',
                                        style: TextStyle(color: Colors.grey),
                                      ),
                                    )
                                    : ListView(
                                      children: _buildFolderTree(
                                        allFolders,
                                        null,
                                        0,
                                      ),
                                    ),
                          ),
                        ],
                      ),
                    ),

                  // Center/Right Pane (Document List and Details)
                  Expanded(
                    child: Column(
                      children: [
                        // Top Bar: Search, Filters, View Toggle
                        Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              // More conservative threshold for narrow width
                              final bool isNarrow = constraints.maxWidth < 400;
                              final bool isVeryNarrow =
                                  constraints.maxWidth < 200;

                              // For extremely narrow widths, use a more compact layout
                              if (isVeryNarrow) {
                                return Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // Only show the most important controls
                                    ShadIconButton.ghost(
                                      icon: Icon(
                                        _isLeftPaneVisible
                                            ? li.LucideIcons.panelLeftClose
                                            : li.LucideIcons.panelLeftOpen,
                                        size: 16,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isLeftPaneVisible =
                                              !_isLeftPaneVisible;
                                        });
                                      },
                                    ),
                                    Expanded(child: Container()), // Spacer
                                    ShadIconButton.ghost(
                                      icon: const Icon(
                                        li.LucideIcons.moreVertical,
                                        size: 16,
                                      ),
                                      onPressed: () {
                                        // Show a popup menu with all actions
                                        final RenderBox button =
                                            context.findRenderObject()
                                                as RenderBox;
                                        final position = button.localToGlobal(
                                          Offset.zero,
                                        );

                                        showMenu(
                                          context: context,
                                          position: RelativeRect.fromLTRB(
                                            position.dx,
                                            position.dy + button.size.height,
                                            position.dx + button.size.width,
                                            position.dy,
                                          ),
                                          items: [
                                            PopupMenuItem(
                                              onTap: () {
                                                NotificationService.showInfo(
                                                  context,
                                                  'Search functionality available in expanded view.',
                                                );
                                              },
                                              child: const Text('Search'),
                                            ),
                                            PopupMenuItem(
                                              onTap: _pickAndUploadFile,
                                              child: const Text('Upload File'),
                                            ),
                                            PopupMenuItem(
                                              onTap: () {
                                                NotificationService.showInfo(
                                                  context,
                                                  'Filter panel not yet implemented.',
                                                );
                                              },
                                              child: const Text('Filter'),
                                            ),
                                          ],
                                        );
                                      },
                                    ),
                                  ],
                                );
                              }

                              // For narrow but not extremely narrow widths
                              if (isNarrow) {
                                return Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    ShadTooltip(
                                      builder:
                                          (context) => Text(
                                            _isLeftPaneVisible
                                                ? 'Hide Folder Tree'
                                                : 'Show Folder Tree',
                                          ),
                                      child: ShadIconButton.ghost(
                                        icon: Icon(
                                          _isLeftPaneVisible
                                              ? li.LucideIcons.panelLeftClose
                                              : li.LucideIcons.panelLeftOpen,
                                          size: 16,
                                        ),
                                        onPressed: () {
                                          setState(() {
                                            _isLeftPaneVisible =
                                                !_isLeftPaneVisible;
                                          });
                                        },
                                      ),
                                    ),
                                    Flexible(
                                      child: IconButton(
                                        icon: const Icon(
                                          li.LucideIcons.search,
                                          size: 16,
                                        ),
                                        onPressed: () {
                                          NotificationService.showInfo(
                                            context,
                                            'Search functionality available in expanded view.',
                                          );
                                        },
                                      ),
                                    ),
                                    // Combine upload and filter into a single menu button
                                    ShadIconButton.ghost(
                                      icon: const Icon(
                                        li.LucideIcons.moreHorizontal,
                                        size: 16,
                                      ),
                                      onPressed: () {
                                        showModalBottomSheet(
                                          context: context,
                                          builder:
                                              (context) => Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  ListTile(
                                                    leading: const Icon(
                                                      li
                                                          .LucideIcons
                                                          .uploadCloud,
                                                    ),
                                                    title: const Text(
                                                      'Upload File',
                                                    ),
                                                    onTap: () {
                                                      Navigator.pop(context);
                                                      _pickAndUploadFile();
                                                    },
                                                  ),
                                                  ListTile(
                                                    leading: const Icon(
                                                      li.LucideIcons.filter,
                                                    ),
                                                    title: const Text(
                                                      'Filter Documents',
                                                    ),
                                                    onTap: () {
                                                      Navigator.pop(context);
                                                      NotificationService.showInfo(
                                                        context,
                                                        'Filter panel not yet implemented.',
                                                      );
                                                    },
                                                  ),
                                                ],
                                              ),
                                        );
                                      },
                                    ),
                                  ],
                                );
                              }

                              // For wider layouts, use the original design with some adjustments
                              return Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  ShadTooltip(
                                    builder:
                                        (context) => Text(
                                          _isLeftPaneVisible
                                              ? 'Hide Folder Tree'
                                              : 'Show Folder Tree',
                                        ),
                                    child: ShadIconButton.ghost(
                                      icon: Icon(
                                        _isLeftPaneVisible
                                            ? li.LucideIcons.panelLeftClose
                                            : li.LucideIcons.panelLeftOpen,
                                        size: 18,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isLeftPaneVisible =
                                              !_isLeftPaneVisible;
                                        });
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 2),
                                  Flexible(child: const SearchBarWidget()),
                                  const SizedBox(width: 2),
                                  // Wrap these buttons in a Row with mainAxisSize.min to prevent overflow
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      ShadTooltip(
                                        builder:
                                            (context) =>
                                                const Text('Upload a new file'),
                                        child: ShadIconButton.ghost(
                                          icon: const Icon(
                                            li.LucideIcons.uploadCloud,
                                            size: 18,
                                          ),
                                          onPressed: _pickAndUploadFile,
                                        ),
                                      ),
                                      const SizedBox(width: 2),
                                      ShadTooltip(
                                        builder:
                                            (context) =>
                                                const Text('Filter documents'),
                                        child: ShadIconButton.ghost(
                                          icon: const Icon(
                                            li.LucideIcons.filter,
                                            size: 18,
                                          ),
                                          onPressed: () {
                                            NotificationService.showInfo(
                                              context,
                                              'Filter panel not yet implemented.',
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                        ShadSeparator.horizontal(), // Assuming horizontal separator
                        // Upload Progress Indicators Area
                        Consumer(
                          builder: (context, ref, child) {
                            final uploadsMap = ref.watch(
                              uploadProgressNotifierProvider,
                            );
                            if (uploadsMap.isEmpty) {
                              return const SizedBox.shrink();
                            }

                            final relevantUploads =
                                uploadsMap.values.where((p) {
                                  bool show = false;
                                  switch (p.status) {
                                    case UploadStatus.pending:
                                    case UploadStatus.uploading:
                                      show = true;
                                      break;
                                    case UploadStatus.completed:
                                    case UploadStatus.failed:
                                    case UploadStatus.cancelled:
                                      show = true;
                                      break;
                                  }
                                  return show;
                                }).toList();

                            if (relevantUploads.isEmpty) {
                              return const SizedBox.shrink();
                            }

                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12.0,
                                vertical: 8.0,
                              ),
                              child: Column(
                                children:
                                    relevantUploads.map((progressModel) {
                                      return UploadProgressIndicatorWidget(
                                        key: ValueKey(progressModel.fileId),
                                        progressModel: progressModel,
                                        onCancel: () {
                                          ref
                                              .read(
                                                uploadProgressNotifierProvider
                                                    .notifier,
                                              )
                                              .setUploadCancelled(
                                                progressModel.fileId,
                                              );
                                        },
                                        onClear: () {
                                          ref
                                              .read(
                                                uploadProgressNotifierProvider
                                                    .notifier,
                                              )
                                              .removeUpload(
                                                progressModel.fileId,
                                              );
                                        },
                                      );
                                    }).toList(),
                              ),
                            );
                          },
                        ),
                        // Bulk Action Toolbar (conditionally shown)
                        if (showBulkActions) const BulkActionToolbarWidget(),

                        // Document List Area
                        const Expanded(child: DocumentListView()),
                      ],
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}
