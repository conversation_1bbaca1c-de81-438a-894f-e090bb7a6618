// lib/src/features/solicitor_portal/presentation/pages/funding_application_form_page.dart
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart'; // Added for file picking
import 'package:intl/intl.dart'; // For date formatting
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/toast_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart';
// TODO: Import shadcn_ui components if available and appropriate, or use styled Material/Cupertino widgets.
// For now, using Material widgets.
// Import for the new page (will be created in a subsequent step)
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/solicitor_application_list_page.dart';

// Define logical names for documents
const String scheduleOfCostsLogicalName = 'schedule_of_costs';
const String legalOpinionLogicalName = 'legal_opinion';
const String riskCommitteeApprovalLogicalName = 'risk_committee_approval';

class FundingApplicationFormPage extends StatefulWidget {
  static const String routeName = '/solicitor/funding-application-form';

  // Optional: if resuming a draft, pass the existing data
  final FundingApplicationData? existingApplicationData;
  final int? initialStep;

  const FundingApplicationFormPage({
    super.key,
    this.existingApplicationData,
    this.initialStep,
  });

  @override
  State<FundingApplicationFormPage> createState() =>
      _FundingApplicationFormPageState();
}

class _FundingApplicationFormPageState
    extends State<FundingApplicationFormPage> {
  int _currentStep = 0;
  late FundingApplicationData _formData;
  final _formKeys = [
    GlobalKey<FormState>(), // Step 1: Claim Details
    GlobalKey<FormState>(), // Step 2: Document Provision
    GlobalKey<FormState>(), // Step 3: Four Pillars
    GlobalKey<FormState>(), // Step 4: Review & Submit
  ];

  // Controllers for Step 1
  final _claimTitleController = TextEditingController();
  final _minimumValueClaimController = TextEditingController();
  final _requiredFundingAmountController = TextEditingController();
  String? _selectedClaimantType;
  String? _selectedClaimIndustry;
  String? _selectedClaimType;
  String? _selectedStageOfClaim;

  // Controllers/State for Step 2
  bool _conditionalFeeAgreementConfirmed = false;
  final _legalOpinionSuccessProspectsController = TextEditingController();

  // State for document uploads (Step 2)
  Map<String, PlatformFile?> _selectedFiles = {};
  Map<String, TextEditingController> _documentNotesControllers = {};
  Map<String, bool> _isUploading = {
    scheduleOfCostsLogicalName: false,
    legalOpinionLogicalName: false,
    riskCommitteeApprovalLogicalName: false,
  };
  Map<String, String?> _uploadErrorMessages = {};

  // Controllers/State for Step 3 (Placeholders)
  // Example: Map<String, bool> _fourPillarsSelection = {};

  // Controllers/State for Step 4
  bool _finalDeclarationConfirmed = false;

  String?
  _currentSolicitorProfileId; // To store the logged-in solicitor's profile ID
  bool _isLoading = false;

  // TODO: Replace with actual options from PocketBase schema or constants file
  final List<String> _claimantTypeOptions = [
    'Individual',
    'Single Claimants',
    'Group Claimants',
    'Other Legal Entity',
    'Other',
  ];
  final List<String> _claimIndustryOptions = [
    'Aerospace', 'Automotive', 'Aviation', 'Banking', 'Construction',
    'Consumer Goods', 'Energy', 'Financial Services', 'Healthcare',
    'Insurance', 'IT & Telecoms', 'Manufacturing', 'Media & Entertainment',
    'Pharmaceuticals', 'Professional Services', 'Public Sector',
    'Real Estate', 'Retail', 'Shipping & Maritime', 'Sports',
    'Technology',
    'Transport & Logistics',
    'Travel & Leisure',
    'Veterinary Sector',
    // Add all from SOLICITOR_FEATKILO.md
  ];
  final List<String> _claimTypeOptions = [
    'Construction - Property Developer', 'Construction - Insurer',
    'Competition - Cartel Damages', 'Competition - Abuse of Dominance',
    'Financial Services - Mis-selling',
    'Financial Services - Regulatory Breach',
    'Insolvency - Preference Claims', 'Insolvency - Fraudulent Trading',
    'Intellectual Property - Patent Infringement',
    'Intellectual Property - Copyright Infringement',
    'Professional Negligence - Legal Sector',
    'Professional Negligence - Accounting Sector',
    'Professional Negligence - Financial Advisors',
    'Professional Negligence - Other',
    // Add all from SOLICITOR_FEATKILO.md
  ];
  final List<String> _stageOfClaimOptions = [
    // From DESIGN_DOCUMENT.md (funding_applications.stage)
    'Pre-Action', 'Proceedings Issued', 'Disclosure', 'Witness Statements',
    'Expert Evidence', 'Trial Preparation', 'Trial', 'Post-Trial/Appeal',
  ];

  @override
  void initState() {
    super.initState();
    _fetchCurrentSolicitorProfileId(); // Fetch at init
    _formData =
        widget.existingApplicationData ??
        FundingApplicationData(uploadedDocuments: []);
    if (widget.initialStep != null &&
        widget.initialStep! >= 0 &&
        widget.initialStep! < _formKeys.length) {
      _currentStep = widget.initialStep!;
    }

    // Initialize notes controllers
    _documentNotesControllers = {
      scheduleOfCostsLogicalName: TextEditingController(),
      legalOpinionLogicalName: TextEditingController(),
      riskCommitteeApprovalLogicalName: TextEditingController(),
    };

    _populateFormFieldsFromData();
  }

  void _populateFormFieldsFromData() {
    if (widget.existingApplicationData != null) {
      _claimTitleController.text = _formData.claimTitle ?? '';
      _minimumValueClaimController.text =
          _formData.minimumValueClaim?.toString() ?? '';
      _requiredFundingAmountController.text =
          _formData.requiredFundingAmount?.toString() ?? '';
      _selectedClaimantType = _formData.claimantType;
      _selectedClaimIndustry = _formData.claimIndustry;
      _selectedClaimType = _formData.claimType;
      _selectedStageOfClaim = _formData.stageOfClaim;

      _conditionalFeeAgreementConfirmed =
          _formData.conditionalFeeAgreementConfirmed ?? false;
      _legalOpinionSuccessProspectsController.text =
          _formData.legalOpinionSuccessProspects?.toString() ?? '';

      _finalDeclarationConfirmed = _formData.finalDeclarationConfirmed ?? false;
      // Populate other steps as they are implemented
      // Populate notes from existing draft if available
      _formData.uploadedDocuments?.forEach((category) {
        if (_documentNotesControllers.containsKey(category.logicalName) &&
            category.versions.isNotEmpty) {
          // For simplicity, let's assume we might want to prefill notes if editing the latest version.
          // However, the task is about new uploads. This part might need refinement based on UX for "editing" an upload.
          // For now, if a document category exists, we don't prefill notes as new uploads get new notes.
        }
      });
    }
  }

  @override
  void dispose() {
    _claimTitleController.dispose();
    _minimumValueClaimController.dispose();
    _requiredFundingAmountController.dispose();
    _legalOpinionSuccessProspectsController.dispose();
    _documentNotesControllers.forEach((_, controller) => controller.dispose());
    super.dispose();
  }

  Future<void> _fetchCurrentSolicitorProfileId() async {
    final pbClient = PocketBaseService().pb;
    final currentUser = pbClient.authStore.record;
    if (currentUser == null) {
      debugPrint('User not authenticated, cannot fetch solicitor profile ID.');
      return;
    }

    try {
      final profileRecords = await pbClient
          .collection('solicitor_profiles')
          .getList(filter: 'user_id = "${currentUser.id}"', perPage: 1);
      if (profileRecords.items.isNotEmpty) {
        if (!mounted) return;
        setState(() {
          _currentSolicitorProfileId = profileRecords.items.first.id;
          debugPrint(
            'Current solicitor profile ID: $_currentSolicitorProfileId',
          );
        });
      } else {
        debugPrint('No solicitor profile found for user ID: ${currentUser.id}');
      }
    } catch (e) {
      debugPrint('Error fetching current solicitor profile ID: $e');
      if (mounted) {
        NotificationService.showError(
          context,
          'Error fetching your solicitor profile: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _pickAndUploadFile(String logicalName) async {
    setState(() {
      _isUploading[logicalName] = true;
      _uploadErrorMessages[logicalName] = null;
    });

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png', 'gif'],
      );

      if (result != null && result.files.single.path != null) {
        PlatformFile file = result.files.first;
        setState(() {
          _selectedFiles[logicalName] = file;
        });

        final pocketBaseService = PocketBaseService();
        final String currentUserId =
            pocketBaseService.client.authStore.model?.id ?? 'unknown_user_id';

        if (currentUserId == 'unknown_user_id') {
          NotificationService.showError(
            context,
            'User not authenticated. Cannot upload file.',
            title: 'Error',
          );
          setState(() {
            _isUploading[logicalName] = false;
            _uploadErrorMessages[logicalName] = 'User not authenticated.';
          });
          return;
        }

        // SIMULATING FILE UPLOAD AND GETTING AN ID for now.
        // In a real scenario, you would upload to PocketBase here and get the actual file ID.
        // For example:
        // String pbFileId = await pocketBaseService.uploadFileToStorage(file, 'funding_application_documents');
        // For this task, we'll use a placeholder ID.
        final pbFileId =
            'pb_file_${DateTime.now().millisecondsSinceEpoch}_${file.name}';
        NotificationService.showSuccess(
          context,
          '${file.name} selected. It will be saved with the draft.',
          title: 'File Ready',
        );

        final String notes =
            _documentNotesControllers[logicalName]?.text.trim() ?? '';
        final newVersion = DocumentVersion(
          fileId: pbFileId,
          filename: file.name,
          uploadedAt: DateFormat(
            "yyyy-MM-ddTHH:mm:ss'Z'",
          ).format(DateTime.now().toUtc()),
          uploadedBy: currentUserId,
          notes: notes.isNotEmpty ? notes : null,
        );

        List<UploadedDocumentCategory> updatedDocuments = List.from(
          _formData.uploadedDocuments ?? [],
        );
        int existingCategoryIndex = updatedDocuments.indexWhere(
          (doc) => doc.logicalName == logicalName,
        );

        if (existingCategoryIndex != -1) {
          UploadedDocumentCategory category =
              updatedDocuments[existingCategoryIndex];
          List<DocumentVersion> updatedVersions = List.from(category.versions)
            ..add(newVersion);
          updatedDocuments[existingCategoryIndex] = UploadedDocumentCategory(
            logicalName: category.logicalName,
            versions: updatedVersions,
            currentVersionFileId: newVersion.fileId,
          );
        } else {
          updatedDocuments.add(
            UploadedDocumentCategory(
              logicalName: logicalName,
              versions: [newVersion],
              currentVersionFileId: newVersion.fileId,
            ),
          );
        }

        _formData = _formData.copyWith(uploadedDocuments: updatedDocuments);

        // Trigger a UI update to show the newly added document information
        setState(() {});
      } else {
        // User canceled the picker
        NotificationService.showInfo(
          context,
          'No file was selected for $logicalName.',
          title: 'File Selection Cancelled',
        );
      }
    } catch (e) {
      NotificationService.showError(
        context,
        'Failed to process file for $logicalName: ${e.toString()}',
        title: 'File Processing Error',
      );
      setState(() {
        _uploadErrorMessages[logicalName] =
            'File processing failed: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isUploading[logicalName] = false;
      });
    }
  }

  Future<void> _saveDraft() async {
    if (_isLoading) return;
    setState(() => _isLoading = true);

    // Consolidate data from controllers/state into _formData
    _formData = _formData.copyWith(
      claimTitle:
          _claimTitleController.text.trim().isNotEmpty
              ? _claimTitleController.text.trim()
              : null,
      minimumValueClaim: double.tryParse(
        _minimumValueClaimController.text.trim(),
      ),
      requiredFundingAmount: double.tryParse(
        _requiredFundingAmountController.text.trim(),
      ),
      claimantType: _selectedClaimantType,
      claimIndustry: _selectedClaimIndustry,
      claimType: _selectedClaimType,
      stageOfClaim: _selectedStageOfClaim,
      conditionalFeeAgreementConfirmed: _conditionalFeeAgreementConfirmed,
      legalOpinionSuccessProspects: int.tryParse(
        _legalOpinionSuccessProspectsController.text.trim(),
      ),
      // pillarClaimantSolicitorOk: ...
      // pillarLeadingCounselOk: ...
      // pillarDefendantOk: ...
      // pillarEnforcementOk: ...
      finalDeclarationConfirmed: _finalDeclarationConfirmed,
      applicationStatus: 'draft',
      // uploadedDocuments is already part of _formData through _pickAndUploadFile updates
      // So, no need to explicitly pass it to copyWith here if _formData is the source of truth.
      // However, if we were building _formData from scratch here, we would include:
      // uploadedDocuments: _formData.uploadedDocuments,
    );

    try {
      final pocketBaseService = PocketBaseService();
      Map<String, dynamic> jsonData = _formData.toJson();

      // Ensure uploaded_documents is part of the JSON if it exists
      // The toJson method in FundingApplicationData should handle this.

      if (_formData.id != null && _formData.id!.isNotEmpty) {
        // Update existing draft
        // For updates, we don't modify claim_admins here as it's managed in the Edit page.
        await pocketBaseService.updateRecord(
          collectionName: 'funding_applications',
          recordId: _formData.id!,
          data: jsonData,
        );
      } else {
        // Create new draft
        if (_currentSolicitorProfileId != null) {
          jsonData['claim_admins'] = [_currentSolicitorProfileId];
          // Also ensure solicitor_profile_id is set for the application itself
          jsonData['solicitor_profile_id'] = [_currentSolicitorProfileId];
        } else {
          NotificationService.showError(
            context,
            'Could not identify current solicitor. Cannot set claim admin.',
          );
          setState(() => _isLoading = false);
          return;
        }

        final newRecord = await pocketBaseService.createRecord(
          collectionName: 'funding_applications',
          data: jsonData,
        );
        // IMPORTANT: Update the local _formData.id with the newRecord.id
        // And also ensure the _formData instance itself is updated if copyWith creates a new one.
        _formData = _formData.copyWith(id: newRecord.id);
      }
      NotificationService.showSuccess(
        context,
        'Draft saved successfully!',
        title: 'Success',
      );
    } catch (e) {
      NotificationService.showError(
        context,
        'Error saving draft: ${e.toString()}',
        title: 'Error',
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _submitApplication() async {
    if (_isLoading) return;
    if (!_finalDeclarationConfirmed) {
      NotificationService.showError(
        context,
        'Please confirm the final declaration before submitting.',
        title: 'Submission Error',
      );
      return;
    }
    if (_formData.id == null || _formData.id!.isEmpty) {
      NotificationService.showError(
        context,
        'Application ID is missing. Please save as draft first.',
        title: 'Submission Error',
      );
      // Optionally, call _saveDraft here and then proceed, or guide the user.
      // For now, we'll require a draft to be saved first to have an ID.
      return;
    }

    setState(() => _isLoading = true);

    try {
      final pocketBaseService = PocketBaseService();
      final String submissionTimestamp = DateFormat(
        "yyyy-MM-ddTHH:mm:ss.SSS'Z'",
      ).format(DateTime.now().toUtc());

      // Update _formData with submission details
      // We only need to update status and submission_date for the existing record.
      // Other fields should have been set by _saveDraft or previous steps.
      Map<String, dynamic> submissionData = {
        'application_status': 'submitted',
        'submission_date': submissionTimestamp,
        'final_declaration_confirmed': true, // Ensure this is also persisted
      };

      await pocketBaseService.updateRecord(
        collectionName: 'funding_applications',
        recordId: _formData.id!,
        data: submissionData,
      );

      // Update local _formData state as well
      _formData = _formData.copyWith(
        applicationStatus: 'submitted',
        submissionDate: submissionTimestamp,
        finalDeclarationConfirmed: true,
      );

      NotificationService.showSuccess(
        context,
        'Application submitted successfully!',
        title: 'Success',
      );

      // Navigate to Application Tracking page
      Navigator.pushNamedAndRemoveUntil(
        context,
        SolicitorApplicationListPage
            .routeName, // Assuming routeName is defined in the new page
        (Route<dynamic> route) => false, // Remove all routes below
      );
    } catch (e) {
      NotificationService.showError(
        context,
        'Error submitting application: ${e.toString()}',
        title: 'Submission Error',
      );
    } finally {
      // Only set isLoading to false if not navigating away or if navigation might fail
      // If navigation is successful, this widget might be disposed.
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  List<Step> _getSteps() {
    return [
      Step(
        title: const Text('Claim Details'),
        isActive: _currentStep >= 0,
        state: _currentStep > 0 ? StepState.complete : StepState.indexed,
        content: Form(
          key: _formKeys[0],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              TextFormField(
                controller: _claimTitleController,
                decoration: const InputDecoration(labelText: 'Claim Title*'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Claim title is required.';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _minimumValueClaimController,
                decoration: const InputDecoration(
                  labelText: 'Minimum Value of Claim (£)*',
                  prefixText: '£',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Minimum value is required.';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number.';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _requiredFundingAmountController,
                decoration: const InputDecoration(
                  labelText: 'Required Funding Amount (£)*',
                  prefixText: '£',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Required funding amount is required.';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number.';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedClaimantType,
                decoration: const InputDecoration(labelText: 'Claimant Type*'),
                items:
                    _claimantTypeOptions.map((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                onChanged: (newValue) {
                  setState(() {
                    _selectedClaimantType = newValue;
                  });
                },
                validator:
                    (value) =>
                        value == null ? 'Claimant type is required.' : null,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedClaimIndustry,
                decoration: const InputDecoration(labelText: 'Claim Industry*'),
                isExpanded: true,
                items:
                    _claimIndustryOptions.map((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value, overflow: TextOverflow.ellipsis),
                      );
                    }).toList(),
                onChanged: (newValue) {
                  setState(() {
                    _selectedClaimIndustry = newValue;
                  });
                },
                validator:
                    (value) =>
                        value == null ? 'Claim industry is required.' : null,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedClaimType,
                decoration: const InputDecoration(labelText: 'Claim Type*'),
                isExpanded: true,
                items:
                    _claimTypeOptions.map((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value, overflow: TextOverflow.ellipsis),
                      );
                    }).toList(),
                onChanged: (newValue) {
                  setState(() {
                    _selectedClaimType = newValue;
                  });
                },
                validator:
                    (value) => value == null ? 'Claim type is required.' : null,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedStageOfClaim,
                decoration: const InputDecoration(labelText: 'Stage of Claim*'),
                isExpanded: true,
                items:
                    _stageOfClaimOptions.map((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value, overflow: TextOverflow.ellipsis),
                      );
                    }).toList(),
                onChanged: (newValue) {
                  setState(() {
                    _selectedStageOfClaim = newValue;
                  });
                },
                validator:
                    (value) =>
                        value == null ? 'Stage of claim is required.' : null,
              ),
            ],
          ),
        ),
      ),
      Step(
        title: const Text('Document Provision'),
        isActive: _currentStep >= 1,
        state: _currentStep > 1 ? StepState.complete : StepState.indexed,
        content: Form(
          key: _formKeys[1],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const Text(
                'Required Documents:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              _buildDocumentSection(
                'Schedule of Costs',
                scheduleOfCostsLogicalName,
              ),
              _buildDocumentSection('Legal Opinion', legalOpinionLogicalName),
              _buildDocumentSection(
                'Risk Committee Approval (if applicable)',
                riskCommitteeApprovalLogicalName,
              ),
              const SizedBox(height: 16),
              CheckboxListTile(
                title: const Text('Conditional Fee Agreement Confirmed*'),
                value: _conditionalFeeAgreementConfirmed,
                onChanged: (bool? value) {
                  setState(() {
                    _conditionalFeeAgreementConfirmed = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
                // validator removed, handled in onStepContinue
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _legalOpinionSuccessProspectsController,
                decoration: const InputDecoration(
                  labelText: 'Legal Opinion Success Prospects (%)*',
                  hintText: '0-100',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Success prospects are required.';
                  }
                  final prospects = int.tryParse(value);
                  if (prospects == null || prospects < 0 || prospects > 100) {
                    return 'Please enter a valid percentage (0-100).';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      Step(
        title: const Text('Four Pillars Assessment'),
        isActive: _currentStep >= 2,
        state: _currentStep > 2 ? StepState.complete : StepState.indexed,
        content: Form(
          key: _formKeys[2],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const <Widget>[
              Text('This step is for the Four Pillars assessment.'),
              Text(
                'UI placeholders for checkboxes/dropdowns if solicitor input is needed:',
              ),
              SizedBox(height: 8),
              Text(' - Claimant & Solicitor OK? (Placeholder)'),
              Text(' - Leading Counsel OK? (Placeholder)'),
              Text(' - Defendant OK? (Placeholder)'),
              Text(' - Enforcement OK? (Placeholder)'),
              // Actual implementation of these fields will depend on specific requirements
            ],
          ),
        ),
      ),
      Step(
        title: const Text('Review & Submit'),
        isActive: _currentStep >= 3,
        state: _currentStep > 3 ? StepState.complete : StepState.indexed,
        content: Form(
          key: _formKeys[3],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const Text('Review your application details before submitting:'),
              const SizedBox(height: 16),
              // Read-only summary of data from _formData
              Text('Claim Title: ${_claimTitleController.text}'),
              Text(
                'Minimum Value of Claim: ${_minimumValueClaimController.text}',
              ),
              // ... Add all other fields from _formData
              const SizedBox(height: 16),
              const Text('Uploaded Documents (Placeholders):'),
              // Placeholder links to view/download
              const SizedBox(height: 16),
              CheckboxListTile(
                title: const Text(
                  'I confirm all information provided is accurate to the best of my knowledge.*',
                ),
                value: _finalDeclarationConfirmed,
                onChanged: (bool? value) {
                  setState(() {
                    _finalDeclarationConfirmed = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
                // validator removed, handled in onStepContinue
              ),
            ],
          ),
        ),
      ),
    ];
  }

  Widget _buildDocumentSection(String title, String logicalName) {
    final selectedFile = _selectedFiles[logicalName];
    final noteController = _documentNotesControllers[logicalName];
    final uploading = _isUploading[logicalName] ?? false;
    final errorMessage = _uploadErrorMessages[logicalName];

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          // Text input for comments/notes
          TextFormField(
            controller: noteController,
            decoration: InputDecoration(
              labelText: 'Text Comment / Version Notes for $title',
              border: const OutlineInputBorder(),
            ),
            maxLines: 2,
          ),
          const SizedBox(height: 8),
          // File picker button and status
          Row(
            children: [
              ElevatedButton.icon(
                icon:
                    uploading
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.upload_file_outlined),
                label: Text(
                  uploading
                      ? 'Uploading...'
                      : selectedFile != null
                      ? 'Change File'
                      : 'Select File',
                ),
                onPressed:
                    uploading ? null : () => _pickAndUploadFile(logicalName),
              ),
              const SizedBox(width: 16),
              if (selectedFile != null)
                Expanded(
                  child: Text(
                    'Selected: ${selectedFile.name}',
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
            ],
          ),
          if (errorMessage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                errorMessage,
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ),
          const SizedBox(height: 4),
          const Text(
            'Supported types: PDF, JPG, JPEG, PNG, GIF.',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),

          // Display already uploaded documents for this logical name from _formData
          // This part is more complex if we need to show version history.
          // For now, let's show the current version if it exists in _formData.
          // This requires _formData to be accurately populated.
          _buildUploadedDocumentInfo(logicalName),

          const Divider(height: 24, thickness: 1),
        ],
      ),
    );
  }

  Widget _buildUploadedDocumentInfo(String logicalName) {
    final category = _formData.uploadedDocuments?.firstWhere(
      (cat) => cat.logicalName == logicalName,
      orElse:
          () => UploadedDocumentCategory(
            logicalName: logicalName,
            versions: [],
            currentVersionFileId: '',
          ), // Return a dummy to avoid null issues
    );

    if (category != null && category.versions.isNotEmpty) {
      final currentVersion = category.versions.firstWhere(
        (ver) => ver.fileId == category.currentVersionFileId,
        orElse:
            () =>
                category.versions.last, // Fallback to last if current not found
      );
      return Padding(
        padding: const EdgeInsets.only(top: 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Uploaded File:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            Text('  Name: ${currentVersion.filename}'),
            Text(
              '  Uploaded: ${DateFormat.yMd().add_jm().format(DateTime.parse(currentVersion.uploadedAt).toLocal())} by ${currentVersion.uploadedBy}',
            ),
            if (currentVersion.notes != null &&
                currentVersion.notes!.isNotEmpty)
              Text('  Notes: ${currentVersion.notes}'),
            // Add button to view/download (placeholder for now)
            // TextButton(onPressed: () {}, child: Text("View/Download Current Version")),
          ],
        ),
      );
    }
    return const SizedBox.shrink(); // No document uploaded for this category yet
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('New Funding Application'),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveDraft,
              child:
                  _isLoading
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Text('Save Draft'),
            ),
          ),
        ],
      ),
      body: Stepper(
        steps: _getSteps(),
        currentStep: _currentStep,
        onStepTapped: (step) => setState(() => _currentStep = step),
        onStepContinue: () {
          final isLastStep = _currentStep == _getSteps().length - 1;
          // Validate current step's form
          if (_formKeys[_currentStep].currentState!.validate()) {
            // Specific validation for checkboxes not part of Form
            if (_currentStep == 1 && !_conditionalFeeAgreementConfirmed) {
              NotificationService.showError(
                context,
                'Please confirm the Conditional Fee Agreement.',
                title: 'Validation Error',
              );
              return;
            }
            if (isLastStep && !_finalDeclarationConfirmed) {
              NotificationService.showError(
                context,
                'Please confirm the final declaration.',
                title: 'Validation Error',
              );
              return;
            }

            if (isLastStep) {
              // This is the "Submit" action for the last step
              _submitApplication();
            } else {
              setState(() => _currentStep += 1);
            }
          }
        },
        onStepCancel: () {
          if (_currentStep > 0) {
            setState(() => _currentStep -= 1);
          }
        },
        controlsBuilder: (BuildContext context, ControlsDetails details) {
          final isLastStep = _currentStep == _getSteps().length - 1;
          return Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                if (_currentStep > 0)
                  TextButton(
                    onPressed: details.onStepCancel,
                    child: const Text('Previous'),
                  ),
                const Spacer(),
                ElevatedButton(
                  onPressed: details.onStepContinue,
                  child: Text(isLastStep ? 'Submit Application' : 'Next'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
