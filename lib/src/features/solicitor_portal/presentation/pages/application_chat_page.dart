import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/theme/app_theme.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/loading_spinner_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/application/providers/application_communication_provider.dart';
import 'package:three_pay_group_litigation_platform/src/shared/adapters/message_adapter.dart';

import 'package:three_pay_group_litigation_platform/src/shared/widgets/chat/message_bubble_widget.dart';
import 'package:three_pay_group_litigation_platform/src/shared/widgets/chat/message_input_widget.dart';

class ApplicationChatPage extends ConsumerStatefulWidget {
  static const String routeName = '/solicitor/application-chat';
  final String applicationId;

  const ApplicationChatPage({super.key, required this.applicationId});

  @override
  ConsumerState<ApplicationChatPage> createState() =>
      _ApplicationChatPageState();
}

class _ApplicationChatPageState extends ConsumerState<ApplicationChatPage> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final communicationState = ref.watch(
      applicationCommunicationProvider(widget.applicationId),
    );

    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      appBar: _buildModernAppBar(context, communicationState),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryColor, // 3Pay brand primary
              AppTheme.secondaryColor, // 3Pay brand secondary
            ],
          ),
        ),
        child: Column(
          children: [
            // Add top padding for status bar and app bar
            SizedBox(
              height: MediaQuery.of(context).padding.top + kToolbarHeight,
            ),
            // Chat Container with rounded top corners
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                ),
                child: Column(
                  children: [
                    // Chat Header inside white container
                    _buildChatHeader(context),
                    // Messages List
                    Expanded(
                      child: _buildMessagesList(context, communicationState),
                    ),
                    // Message Input
                    MessageInputWidget(
                      controller: _messageController,
                      onSendMessage: (content) => _sendMessage(content),
                      isSending: communicationState.isSending,
                      placeholder: 'Message 3Pay Global...',
                      showFileButton: false,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildModernAppBar(
    BuildContext context,
    ApplicationCommunicationState state,
  ) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      title: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.support_agent,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '3Pay Global',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  state.isSending ? 'Typing...' : 'Your intelligent assistant',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatHeader(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.border.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Claim ${widget.applicationId}',
            style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.w600),
          ),
          Row(
            children: [
              IconButton(
                icon: Icon(
                  Icons.content_copy,
                  size: 20,
                  color: theme.colorScheme.mutedForeground,
                ),
                onPressed: () => _copyClaimIdToClipboard(context),
              ),
              // IconButton(
              //   icon: Icon(
              //     Icons.more_horiz,
              //     size: 20,
              //     color: theme.colorScheme.mutedForeground,
              //   ),
              //   onPressed: () {
              //     // TODO: Add more options
              //   },
              // ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeMessage(BuildContext context, ShadThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Time stamp
          const SizedBox(height: 16),
          Text(
            'Today, ${_formatCurrentTime()}',
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
          const SizedBox(height: 24),

          // Welcome message from AI
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor, // 3Pay brand primary
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.lightbulb_outline,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.muted.withValues(alpha: 0.5),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                      bottomLeft: Radius.circular(4),
                      bottomRight: Radius.circular(16),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Hello! I\'m your 3Pay Global assistant. How can I help you today?',
                        style: theme.textTheme.p.copyWith(
                          color: theme.colorScheme.foreground,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Just now',
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Suggested actions
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildSuggestionChip(
                context,
                theme,
                'Ask about my claim',
                Icons.gavel,
              ),
              _buildSuggestionChip(
                context,
                theme,
                'Document upload',
                Icons.upload_file,
              ),
              _buildSuggestionChip(
                context,
                theme,
                'Contact support',
                Icons.support_agent,
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildSuggestionChip(
    BuildContext context,
    ShadThemeData theme,
    String label,
    IconData icon,
  ) {
    return InkWell(
      onTap: () {
        // Set the suggestion text in the input field
        setState(() {
          _messageController.text = label;
          _messageController.selection = TextSelection.fromPosition(
            TextPosition(offset: _messageController.text.length),
          );
        });
      },
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: theme.colorScheme.muted.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: theme.colorScheme.border.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: theme.colorScheme.mutedForeground),
            const SizedBox(width: 6),
            Text(
              label,
              style: theme.textTheme.small.copyWith(
                color: theme.colorScheme.foreground,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatCurrentTime() {
    final now = DateTime.now();
    final hour = now.hour;
    final minute = now.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '$displayHour:$minute $period';
  }

  Future<void> _copyClaimIdToClipboard(BuildContext context) async {
    try {
      await Clipboard.setData(ClipboardData(text: widget.applicationId));
      if (mounted && context.mounted) {
        ShadToaster.of(context).show(
          ShadToast(
            title: const Text('Copied'),
            description: Text(
              'Claim ID ${widget.applicationId} copied to clipboard',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted && context.mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Error'),
            description: const Text('Failed to copy claim ID to clipboard'),
          ),
        );
      }
    }
  }

  Widget _buildMessagesList(
    BuildContext context,
    ApplicationCommunicationState state,
  ) {
    final theme = ShadTheme.of(context);

    if (state.isLoading && state.messages.isEmpty) {
      return const Center(child: LoadingSpinnerWidget());
    }

    if (state.error != null && state.messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: theme.colorScheme.destructive,
            ),
            const SizedBox(height: 16),
            Text('Error loading messages', style: theme.textTheme.h4),
            const SizedBox(height: 8),
            Text(
              state.error!,
              style: theme.textTheme.muted,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ShadButton.outline(
              child: const Text('Retry'),
              onPressed: () {
                ref
                    .read(
                      applicationCommunicationProvider(
                        widget.applicationId,
                      ).notifier,
                    )
                    .fetchMessages();
              },
            ),
          ],
        ),
      );
    }

    if (state.messages.isEmpty) {
      return _buildWelcomeMessage(context, theme);
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: state.messages.length,
      itemBuilder: (context, index) {
        final message = state.messages[index];
        final chatMessage = MessageAdapter.fromApplicationMessage(message);
        return MessageBubbleWidget(message: chatMessage);
      },
    );
  }

  Future<bool> _sendMessage(String content) async {
    if (content.trim().isEmpty) return false;

    final currentUserId = PocketBaseService().currentUser?.id;
    if (currentUserId == null) {
      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Authentication Error'),
            description: const Text('User not authenticated. Please log in.'),
          ),
        );
      }
      return false;
    }

    final success = await ref
        .read(applicationCommunicationProvider(widget.applicationId).notifier)
        .sendMessage(
          messageContent: content.trim(),
          senderId: currentUserId,
          recipientGroup: 'agent', // Send to 3Pay Global agents
        );

    if (success) {
      // Scroll to bottom after sending
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    } else {
      final error =
          ref
              .read(applicationCommunicationProvider(widget.applicationId))
              .error;
      if (error != null && mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Error'),
            description: Text(error.toString()),
          ),
        );
      }
    }

    return success;
  }
}
