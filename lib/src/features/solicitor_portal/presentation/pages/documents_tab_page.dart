import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as li;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/toast_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/storage_type.dart';
import 'package:three_pay_group_litigation_platform/src/shared/presentation/widgets/storage_indicator_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/widgets/document_upload_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/widgets/documents_list_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/models/document_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/features/firm_documents/application/providers/firm_documents_list_provider.dart';

/// Enhanced documents tab page with Google Drive integration
class DocumentsTabPage extends ConsumerStatefulWidget {
  final String? claimId;
  final String? folderId;

  const DocumentsTabPage({super.key, this.claimId, this.folderId});

  @override
  ConsumerState<DocumentsTabPage> createState() => _DocumentsTabPageState();
}

class _DocumentsTabPageState extends ConsumerState<DocumentsTabPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _showUploadPanel = false;
  String _searchQuery = '';
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      body: Column(
        children: [
          _buildHeader(theme),
          _buildTabBar(theme),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllDocumentsTab(),
                _buildRecentDocumentsTab(),
                _buildSharedDocumentsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(theme),
    );
  }

  Widget _buildHeader(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(bottom: BorderSide(color: theme.colorScheme.border)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                li.LucideIcons.fileText,
                size: 24,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Documents',
                      style: theme.textTheme.h3.copyWith(
                        color: theme.colorScheme.foreground,
                      ),
                    ),
                    Row(
                      children: [
                        const StorageIndicatorWidget(
                          storageType: StorageType.googleDrive,
                          showLabel: true,
                          iconSize: 14,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Secure cloud storage',
                          style: theme.textTheme.small.copyWith(
                            color: theme.colorScheme.mutedForeground,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              _buildHeaderActions(theme),
            ],
          ),
          if (_showUploadPanel) ...[
            const SizedBox(height: 16),
            _buildUploadPanel(),
          ],
        ],
      ),
    );
  }

  Widget _buildHeaderActions(ShadThemeData theme) {
    return Row(
      children: [
        ShadTooltip(
          builder: (context) => const Text('Search documents'),
          child: ShadIconButton.ghost(
            icon: const Icon(li.LucideIcons.search, size: 18),
            onPressed: _showSearchDialog,
          ),
        ),
        const SizedBox(width: 8),
        ShadTooltip(
          builder: (context) => const Text('Filter documents'),
          child: ShadIconButton.ghost(
            icon: const Icon(li.LucideIcons.filter, size: 18),
            onPressed: _showFilterDialog,
          ),
        ),
        const SizedBox(width: 8),
        ShadTooltip(
          builder: (context) => const Text('Storage settings'),
          child: ShadIconButton.ghost(
            icon: const Icon(li.LucideIcons.settings, size: 18),
            onPressed: _showStorageSettings,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar(ShadThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(bottom: BorderSide(color: theme.colorScheme.border)),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(icon: Icon(li.LucideIcons.files), text: 'All Documents'),
          Tab(icon: Icon(li.LucideIcons.clock), text: 'Recent'),
          Tab(icon: Icon(li.LucideIcons.share2), text: 'Shared'),
        ],
      ),
    );
  }

  Widget _buildUploadPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.2)),
      ),
      child: DocumentUploadWidget(
        claimId: widget.claimId,
        folderId: widget.folderId,
        onFilesSelected: (files) {
          LoggerService.info('Files selected for upload: ${files.length}');
        },
        onUploadProgress: (fileId, progress) {
          LoggerService.debug(
            'Upload progress for $fileId: ${(progress * 100).toStringAsFixed(1)}%',
          );
        },
        onUploadComplete: (fileId, error) {
          if (error == null) {
            LoggerService.info('Upload completed for $fileId');
            _refreshDocuments();
          } else {
            LoggerService.error('Upload failed for $fileId', error);
          }
        },
      ),
    );
  }

  Widget _buildAllDocumentsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final documentsAsync = ref.watch(firmDocumentsNotifierProvider);

        return documentsAsync.when(
          data:
              (documents) => DocumentsListWidget(
                documents: documents,
                onDocumentTap: _handleDocumentTap,
                onDownload: _handleDocumentDownload,
                onDelete: _handleDocumentDelete,
                onShare: _handleDocumentShare,
                onRefresh: _refreshDocuments,
                showStorageInfo: true,
                allowSelection: true,
              ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildRecentDocumentsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final documentsAsync = ref.watch(firmDocumentsNotifierProvider);

        return documentsAsync.when(
          data: (documents) {
            // Filter recent documents (last 7 days)
            final recentDocuments =
                documents.where((doc) {
                  final daysDiff =
                      DateTime.now().difference(doc.created).inDays;
                  return daysDiff <= 7;
                }).toList();

            return DocumentsListWidget(
              documents: recentDocuments,
              onDocumentTap: _handleDocumentTap,
              onDownload: _handleDocumentDownload,
              onDelete: _handleDocumentDelete,
              onShare: _handleDocumentShare,
              onRefresh: _refreshDocuments,
              showStorageInfo: true,
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildSharedDocumentsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final documentsAsync = ref.watch(firmDocumentsNotifierProvider);

        return documentsAsync.when(
          data: (documents) {
            // Filter shared documents (placeholder logic)
            final sharedDocuments =
                documents.where((doc) {
                  // TODO: Implement actual shared document filtering
                  return doc.name.toLowerCase().contains('shared');
                }).toList();

            return DocumentsListWidget(
              documents: sharedDocuments,
              onDocumentTap: _handleDocumentTap,
              onDownload: _handleDocumentDownload,
              onDelete: _handleDocumentDelete,
              onShare: _handleDocumentShare,
              onRefresh: _refreshDocuments,
              showStorageInfo: true,
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error.toString()),
        );
      },
    );
  }

  Widget _buildErrorState(String error) {
    final theme = ShadTheme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            li.LucideIcons.cloudOff,
            size: 48,
            color: theme.colorScheme.destructive,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load documents',
            style: theme.textTheme.h4.copyWith(
              color: theme.colorScheme.foreground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: theme.textTheme.large.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ShadButton(onPressed: _refreshDocuments, child: const Text('Retry')),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton(ShadThemeData theme) {
    return FloatingActionButton.extended(
      onPressed: _toggleUploadPanel,
      icon: Icon(
        _showUploadPanel ? li.LucideIcons.x : li.LucideIcons.uploadCloud,
      ),
      label: Text(_showUploadPanel ? 'Close' : 'Upload'),
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: theme.colorScheme.primaryForeground,
    );
  }

  void _toggleUploadPanel() {
    setState(() {
      _showUploadPanel = !_showUploadPanel;
    });
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Search Documents'),
            content: TextField(
              decoration: const InputDecoration(
                hintText: 'Enter search terms...',
                prefixIcon: Icon(li.LucideIcons.search),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _performSearch();
                },
                child: const Text('Search'),
              ),
            ],
          ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Filter Documents'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('All Documents'),
                  value: 'all',
                  groupValue: _selectedFilter,
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value!;
                    });
                  },
                ),
                RadioListTile<String>(
                  title: const Text('PDF Files'),
                  value: 'pdf',
                  groupValue: _selectedFilter,
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value!;
                    });
                  },
                ),
                RadioListTile<String>(
                  title: const Text('Word Documents'),
                  value: 'docx',
                  groupValue: _selectedFilter,
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value!;
                    });
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _applyFilter();
                },
                child: const Text('Apply'),
              ),
            ],
          ),
    );
  }

  void _showStorageSettings() {
    // TODO: Navigate to storage settings page
    ToastService.showInfo(context, 'Storage settings will be available soon');
  }

  void _performSearch() {
    LoggerService.info('Performing search: $_searchQuery');
    // TODO: Implement search functionality
    ToastService.showInfo(context, 'Searching for: $_searchQuery');
  }

  void _applyFilter() {
    LoggerService.info('Applying filter: $_selectedFilter');
    // TODO: Implement filter functionality
    ToastService.showInfo(context, 'Filter applied: $_selectedFilter');
  }

  void _refreshDocuments() {
    ref.read(firmDocumentsNotifierProvider.notifier).refresh();
  }

  void _handleDocumentTap(DocumentModel document) {
    LoggerService.info('Document tapped: ${document.name}');
    // TODO: Implement document preview/open
    ToastService.showInfo(context, 'Opening document: ${document.name}');
  }

  void _handleDocumentDownload(DocumentModel document) {
    LoggerService.info('Downloading document: ${document.name}');
    ToastService.showInfo(context, 'Starting download: ${document.name}');
  }

  void _handleDocumentDelete(DocumentModel document) {
    LoggerService.info('Deleting document: ${document.name}');
    ToastService.showSuccess(context, 'Document deleted: ${document.name}');
    _refreshDocuments();
  }

  void _handleDocumentShare(DocumentModel document) {
    LoggerService.info('Sharing document: ${document.name}');
    ToastService.showInfo(context, 'Sharing document: ${document.name}');
  }
}
