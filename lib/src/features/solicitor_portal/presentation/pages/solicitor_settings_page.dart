import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qr_flutter/qr_flutter.dart'; // Added for QR code generation
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/data/models/user_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/solicitor_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/toast_service.dart'; // For user feedback
import 'package:pocketbase/pocketbase.dart' as pbsdk; // Aliased import

// Provider for the current user's data (fetched from PocketBase)
final userProvider = StateProvider<User?>((ref) => null);
// Provider for the solicitor profile data (fetched from PocketBase)
final solicitorProfileProvider = StateProvider<SolicitorProfileModel?>(
  (ref) => null,
);
// Provider for loading state
final isLoadingProvider = StateProvider<bool>((ref) => false);
// Provider for error messages
final errorProvider = StateProvider<String?>((ref) => null);
// Provider for the list of firm users
final firmUsersProvider = StateProvider<List<User>>((ref) => []);
// Provider for firm users loading state
final isFirmUsersLoadingProvider = StateProvider<bool>((ref) => false);
// Provider for firm users error messages
final firmUsersErrorProvider = StateProvider<String?>((ref) => null);
// Provider for password change loading state
final isPasswordChangingProvider = StateProvider<bool>((ref) => false);
// Provider for saving notification preferences
final isSavingNotificationPreferencesProvider = StateProvider<bool>(
  (ref) => false,
);

// Provider for fetched notification preferences
final notificationPreferencesProvider = StateProvider<Map<String, dynamic>?>(
  (ref) => null,
);
// Provider for loading state of notification preferences
final isFetchingNotificationPreferencesProvider = StateProvider<bool>(
  (ref) => false,
);
// Provider for error messages for notification preferences
final notificationPreferencesErrorProvider = StateProvider<String?>(
  (ref) => null,
);

// Providers for 2FA
final is2faLoadingProvider = StateProvider<bool>((ref) => false);
final tfaProvisioningUriProvider = StateProvider<String?>((ref) => null);
final tfaSecretProvider = StateProvider<String?>((ref) => null);
final show2faEnableSetupUIProvider = StateProvider<bool>(
  (ref) => false,
); // Controls visibility of QR/OTP input for enabling
final show2faDisableConfirmUIProvider = StateProvider<bool>(
  (ref) => false,
); // Controls visibility of OTP input for disabling

class SolicitorSettingsPage extends ConsumerStatefulWidget {
  const SolicitorSettingsPage({super.key});

  static const routeName = '/solicitor-profile';

  @override
  ConsumerState<SolicitorSettingsPage> createState() =>
      _SolicitorSettingsPageState();
}

class _SolicitorSettingsPageState extends ConsumerState<SolicitorSettingsPage> {
  final _personalDetailsFormKey = GlobalKey<ShadFormState>();
  late TextEditingController _nameController = TextEditingController();
  late TextEditingController _contactNumberController = TextEditingController();
  late TextEditingController _positionController = TextEditingController();
  String _emailDisplay = ''; // For read-only email field

  final _firmProfileFormKey = GlobalKey<ShadFormState>();
  late TextEditingController _lawFirmNameController = TextEditingController();
  late TextEditingController _firmAddressController = TextEditingController();
  late TextEditingController _sraNumberController = TextEditingController();

  final _securityFormKey = GlobalKey<ShadFormState>();
  late TextEditingController _currentPasswordController =
      TextEditingController();
  late TextEditingController _newPasswordController = TextEditingController();
  late TextEditingController _confirmPasswordController =
      TextEditingController();

  // Controller for 2FA OTP input
  late TextEditingController _tfaOtpController;

  // Notification Preferences State
  bool _inAppNewMessage = true;
  bool _emailNewMessage = true;
  bool _inAppStatusChange = true;
  bool _emailStatusChange = true;
  bool _inAppClaimUpdate = true;
  bool _emailClaimUpdate = true;
  bool _inAppTaskAssigned = true;
  bool _emailTaskAssigned = true;
  bool _inAppAnnouncements = true;
  bool _emailAnnouncements = true;
  String _emailDigestFrequency =
      'Daily'; // Options: "Immediate", "Daily", "Weekly"
  final _notificationPreferencesFormKey = GlobalKey<ShadFormState>();

  // State variables for Firm Users tab
  String _firmUserStatusFilter = 'All'; // 'All', 'Active', 'Invited'
  String _firmUserSortOption = 'name'; // 'name', 'email', 'status'
  bool _firmUserSortAscending = true;

  // Placeholder for firm admin check logic
  bool _isFirmAdmin() {
    final solicitorProfile = ref.watch(solicitorProfileProvider);
    // The primary solicitor associated with an approved PU firm can edit.
    // _fetchPersonalDetails ensures that the loaded solicitorProfile is for the current user.
    // A null check for solicitorProfile is important before accessing its properties.
    return solicitorProfile != null && solicitorProfile.puStatus == 'approved';
  }

  Future<void> _fetchPersonalDetails() async {
    ref.read(isLoadingProvider.notifier).state = true;
    ref.read(errorProvider.notifier).state = null;

    try {
      final pbService = PocketBaseService();
      final currentPbUser = pbService.pb.authStore.model;

      if (currentPbUser == null) {
        throw Exception('User not authenticated.');
      }

      // Fetch the full User record
      final userRecord = await pbService.pb
          .collection('users')
          .getOne(currentPbUser.id);
      final user = User.fromJson(
        userRecord.toJson(),
      ); // Assuming User.fromJson can take RecordModel.toJson()
      ref.read(userProvider.notifier).state = user;
      _emailDisplay = user.email ?? 'N/A';

      // Fetch the linked SolicitorProfile record
      // Assuming 'solicitor_profiles' has a 'user_id' field linking to the 'users' collection.
      final profileRecords = await pbService.pb
          .collection('solicitor_profiles')
          .getList(filter: 'user_id = "${user.id}"');

      if (profileRecords.items.isNotEmpty) {
        final solicitorProfile = SolicitorProfileModel.fromJson(
          profileRecords.items.first.toJson(),
        );
        ref.read(solicitorProfileProvider.notifier).state = solicitorProfile;
        _nameController.text = solicitorProfile.solicitorName;
        _contactNumberController.text = solicitorProfile.contactNumber ?? '';
        _positionController.text = solicitorProfile.positionInFirm ?? '';

        // Initialize firm profile controllers if data is available
        _lawFirmNameController.text = solicitorProfile.lawFirmName;
        _firmAddressController.text = solicitorProfile.firmAddress;
        _sraNumberController.text = solicitorProfile.sraNumber;
      } else {
        // Handle case where solicitor profile doesn't exist, though for settings it should.
        // For now, initialize controllers with empty or default values if profile is null.
        _nameController.text =
            user.name ??
            ''; // Fallback to user.name if profile specific name not found
        _lawFirmNameController.text = '';
        _firmAddressController.text = '';
        _sraNumberController.text = '';
        ref.read(errorProvider.notifier).state =
            'Solicitor profile not found. Please complete your profile.';
      }

      // After fetching solicitor profile, fetch firm users if the profile is available
      if (ref.read(solicitorProfileProvider) != null) {
        await _fetchFirmUsers(pbService, ref.read(solicitorProfileProvider)!);
      }

      // Fetch notification preferences after user and solicitor profile are loaded
      await _fetchNotificationPreferences();
    } on pbsdk.ClientException catch (e) {
      ref.read(errorProvider.notifier).state =
          'Failed to load details: ${e.response['message'] ?? e.toString()}';
      NotificationService.showError(
        context,
        'Error: ${e.response['message'] ?? e.toString()}',
      );
    } catch (e) {
      ref.read(errorProvider.notifier).state =
          'An unexpected error occurred: ${e.toString()}';
      NotificationService.showError(context, 'Error: ${e.toString()}');
    } finally {
      ref.read(isLoadingProvider.notifier).state = false;
      if (mounted) {
        setState(() {}); // Ensure UI updates with fetched data or error
      }
    }
  }

  Future<void> _fetchFirmUsers(
    PocketBaseService pbService,
    SolicitorProfileModel solicitorProfile,
  ) async {
    ref.read(isFirmUsersLoadingProvider.notifier).state = true;
    ref.read(firmUsersErrorProvider.notifier).state = null;
    // Keep existing users while loading if applying filters, or clear if it's a full refresh
    // For simplicity here, we'll refetch all and then filter/sort client-side for now,
    // as filtering by 'verified' status of related users in a single PB query is complex.
    // A more optimized approach might involve backend views or multiple queries.
    // ref.read(firmUsersProvider.notifier).state = [];

    try {
      if (solicitorProfile.additionalUsers.isEmpty) {
        // No additional users to fetch
        ref.read(isFirmUsersLoadingProvider.notifier).state = false;
        return;
      }

      List<User> fetchedUsers = [];
      for (String userId in solicitorProfile.additionalUsers) {
        try {
          final userRecord = await pbService.pb
              .collection('users')
              .getOne(userId);
          // It's important that User.fromJson can handle the structure of userRecord.toJson()
          // and that userRecord itself contains a 'verified' field from PocketBase.
          final user = User.fromJson(userRecord.toJson());
          // Manually add verified status if not directly in User model but available in RecordModel
          // This is a common pattern if the model doesn't map all PB fields.
          // For this example, we'll assume User.fromJson handles it or we'll access it in the UI.
          fetchedUsers.add(user);
        } catch (e) {
          // Log or handle error for individual user fetch if necessary
          print('Error fetching user $userId: $e');
          // Optionally add a placeholder or skip the user
        }
      }
      // Client-side sorting and filtering
      // Filter by status
      List<User> filteredUsers = fetchedUsers;
      if (_firmUserStatusFilter == 'Active') {
        filteredUsers = fetchedUsers.where((user) => user.verified).toList();
      } else if (_firmUserStatusFilter == 'Invited') {
        filteredUsers = fetchedUsers.where((user) => !user.verified).toList();
      }

      // Sort
      filteredUsers.sort((a, b) {
        int comparison;
        switch (_firmUserSortOption) {
          case 'name':
            comparison = (a.name ?? '').compareTo(b.name ?? '');
            break;
          case 'email':
            comparison = (a.email ?? '').compareTo(b.email ?? '');
            break;
          case 'status': // Sort by verified status
            comparison = (a.verified ? 0 : 1).compareTo(b.verified ? 0 : 1);
            break;
          default:
            comparison = 0;
        }
        return _firmUserSortAscending ? comparison : -comparison;
      });

      ref.read(firmUsersProvider.notifier).state = filteredUsers;
    } on pbsdk.ClientException catch (e) {
      ref.read(firmUsersErrorProvider.notifier).state =
          'Failed to load firm users: ${e.response['message'] ?? e.toString()}';
      NotificationService.showError(
        context,
        'Error loading firm users: ${e.response['message'] ?? e.toString()}',
      );
    } catch (e) {
      ref.read(firmUsersErrorProvider.notifier).state =
          'An unexpected error occurred while loading firm users: ${e.toString()}';
      NotificationService.showError(
        context,
        'Error loading firm users: ${e.toString()}',
      );
    } finally {
      ref.read(isFirmUsersLoadingProvider.notifier).state = false;
      if (mounted) {
        setState(() {}); // Ensure UI updates
      }
    }
  }

  Future<void> _fetchNotificationPreferences() async {
    ref.read(isFetchingNotificationPreferencesProvider.notifier).state = true;
    ref.read(notificationPreferencesErrorProvider.notifier).state = null;

    final Map<String, dynamic> defaultPreferences = {
      "in_app": <String, bool>{
        "new_message": true,
        "application_status_change": true,
        "claim_status_update": true,
        "task_assigned": true,
        "platform_announcements": true,
      },
      "email": <String, bool>{
        "new_message": false,
        "application_status_change": false,
        "claim_status_update": false,
        "task_assigned": false,
        "platform_announcements": false,
      },
      "email_digest_frequency": "daily" as String,
    };

    try {
      final pbService = PocketBaseService();
      final currentPbUser = pbService.pb.authStore.model;

      if (currentPbUser == null) {
        throw Exception(
          'User not authenticated. Cannot fetch notification preferences.',
        );
      }

      final userRecord = await pbService.pb
          .collection('users')
          .getOne(currentPbUser.id);
      final preferencesData =
          userRecord.data['notification_preferences'] as Map<String, dynamic>?;

      Map<String, dynamic> resolvedPreferences;
      if (preferencesData != null) {
        final Map<String, bool> defaultInApp =
            defaultPreferences['in_app'] as Map<String, bool>;
        final Map<String, bool> defaultEmail =
            defaultPreferences['email'] as Map<String, bool>;
        final String defaultDigest =
            defaultPreferences['email_digest_frequency'] as String;

        final Map<String, dynamic>? prefsInAppRaw =
            preferencesData["in_app"] as Map<String, dynamic>?;
        final Map<String, dynamic>? prefsEmailRaw =
            preferencesData["email"] as Map<String, dynamic>?;

        resolvedPreferences = {
          "in_app": <String, bool>{
            "new_message":
                prefsInAppRaw?["new_message"] as bool? ??
                defaultInApp["new_message"]!,
            "application_status_change":
                prefsInAppRaw?["application_status_change"] as bool? ??
                defaultInApp["application_status_change"]!,
            "claim_status_update":
                prefsInAppRaw?["claim_status_update"] as bool? ??
                defaultInApp["claim_status_update"]!,
            "task_assigned":
                prefsInAppRaw?["task_assigned"] as bool? ??
                defaultInApp["task_assigned"]!,
            "platform_announcements":
                prefsInAppRaw?["platform_announcements"] as bool? ??
                defaultInApp["platform_announcements"]!,
          },
          "email": <String, bool>{
            "new_message":
                prefsEmailRaw?["new_message"] as bool? ??
                defaultEmail["new_message"]!,
            "application_status_change":
                prefsEmailRaw?["application_status_change"] as bool? ??
                defaultEmail["application_status_change"]!,
            "claim_status_update":
                prefsEmailRaw?["claim_status_update"] as bool? ??
                defaultEmail["claim_status_update"]!,
            "task_assigned":
                prefsEmailRaw?["task_assigned"] as bool? ??
                defaultEmail["task_assigned"]!,
            "platform_announcements":
                prefsEmailRaw?["platform_announcements"] as bool? ??
                defaultEmail["platform_announcements"]!,
          },
          "email_digest_frequency":
              preferencesData["email_digest_frequency"] as String? ??
              defaultDigest,
        };
      } else {
        resolvedPreferences = Map<String, dynamic>.from(defaultPreferences);
      }

      ref.read(notificationPreferencesProvider.notifier).state =
          resolvedPreferences;

      final Map<String, bool> inAppPrefs =
          resolvedPreferences['in_app'] as Map<String, bool>;
      final Map<String, bool> emailPrefs =
          resolvedPreferences['email'] as Map<String, bool>;
      final String emailDigest =
          resolvedPreferences['email_digest_frequency'] as String;

      _inAppNewMessage = inAppPrefs['new_message']!;
      _inAppStatusChange = inAppPrefs['application_status_change']!;
      _inAppClaimUpdate = inAppPrefs['claim_status_update']!;
      _inAppTaskAssigned = inAppPrefs['task_assigned']!;
      _inAppAnnouncements = inAppPrefs['platform_announcements']!;

      _emailNewMessage = emailPrefs['new_message']!;
      _emailStatusChange = emailPrefs['application_status_change']!;
      _emailClaimUpdate = emailPrefs['claim_status_update']!;
      _emailTaskAssigned = emailPrefs['task_assigned']!;
      _emailAnnouncements = emailPrefs['platform_announcements']!;

      _emailDigestFrequency = emailDigest;
    } on pbsdk.ClientException catch (e) {
      ref.read(notificationPreferencesErrorProvider.notifier).state =
          'Failed to load notification preferences: ${e.response['message'] ?? e.toString()}';
      ref
          .read(notificationPreferencesProvider.notifier)
          .state = Map<String, dynamic>.from(defaultPreferences);

      final Map<String, bool> defaultInApp =
          defaultPreferences['in_app'] as Map<String, bool>;
      final Map<String, bool> defaultEmail =
          defaultPreferences['email'] as Map<String, bool>;
      final String defaultDigest =
          defaultPreferences['email_digest_frequency'] as String;

      _inAppNewMessage = defaultInApp['new_message']!;
      _inAppStatusChange = defaultInApp['application_status_change']!;
      _inAppClaimUpdate = defaultInApp['claim_status_update']!;
      _inAppTaskAssigned = defaultInApp['task_assigned']!;
      _inAppAnnouncements = defaultInApp['platform_announcements']!;
      _emailNewMessage = defaultEmail['new_message']!;
      _emailStatusChange = defaultEmail['application_status_change']!;
      _emailClaimUpdate = defaultEmail['claim_status_update']!;
      _emailTaskAssigned = defaultEmail['task_assigned']!;
      _emailAnnouncements = defaultEmail['platform_announcements']!;
      _emailDigestFrequency = defaultDigest;
      NotificationService.showError(
        context,
        'Error loading notification preferences: ${e.response['message'] ?? e.toString()}',
      );
    } catch (e) {
      ref.read(notificationPreferencesErrorProvider.notifier).state =
          'An unexpected error occurred: ${e.toString()}';
      ref
          .read(notificationPreferencesProvider.notifier)
          .state = Map<String, dynamic>.from(defaultPreferences);

      final Map<String, bool> defaultInApp =
          defaultPreferences['in_app'] as Map<String, bool>;
      final Map<String, bool> defaultEmail =
          defaultPreferences['email'] as Map<String, bool>;
      final String defaultDigest =
          defaultPreferences['email_digest_frequency'] as String;

      _inAppNewMessage = defaultInApp['new_message']!;
      _inAppStatusChange = defaultInApp['application_status_change']!;
      _inAppClaimUpdate = defaultInApp['claim_status_update']!;
      _inAppTaskAssigned = defaultInApp['task_assigned']!;
      _inAppAnnouncements = defaultInApp['platform_announcements']!;
      _emailNewMessage = defaultEmail['new_message']!;
      _emailStatusChange = defaultEmail['application_status_change']!;
      _emailClaimUpdate = defaultEmail['claim_status_update']!;
      _emailTaskAssigned = defaultEmail['task_assigned']!;
      _emailAnnouncements = defaultEmail['platform_announcements']!;
      _emailDigestFrequency = defaultDigest;
      NotificationService.showError(
        context,
        'Error loading notification preferences: ${e.toString()}',
      );
    } finally {
      ref.read(isFetchingNotificationPreferencesProvider.notifier).state =
          false;
      if (mounted) {
        setState(
          () {},
        ); // Ensure UI updates with fetched preferences or defaults
      }
    }
  }

  final tabs = [
    'Personal Details',
    'Firm Profile',
    'Firm Users',
    'Security',
    'Notification Preferences',
  ];

  String selectedTab = 'Personal Details';

  @override
  void initState() {
    super.initState();
    // Initialize controllers, then fetch data.
    // Controllers are already initialized at declaration.
    // _nameController = TextEditingController();
    // _contactNumberController = TextEditingController();
    // _positionController = TextEditingController();
    // _lawFirmNameController = TextEditingController();
    // _firmAddressController = TextEditingController();
    // _sraNumberController = TextEditingController();
    // _currentPasswordController = TextEditingController();
    // _newPasswordController = TextEditingController();
    // _confirmPasswordController = TextEditingController();
    _tfaOtpController = TextEditingController();

    // Initialize notification preferences - these will be loaded from backend in a later task
    _inAppNewMessage = true;
    _emailNewMessage = true;
    _inAppStatusChange = true;
    _emailStatusChange = true;
    _inAppClaimUpdate = true;
    _emailClaimUpdate = true;
    _inAppTaskAssigned = true;
    _emailTaskAssigned = true;
    _inAppAnnouncements = true;
    _emailAnnouncements = true;
    _emailDigestFrequency = 'Daily';

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchPersonalDetails();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _contactNumberController.dispose();
    _positionController.dispose();

    _lawFirmNameController.dispose();
    _firmAddressController.dispose();
    _sraNumberController.dispose();

    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    _tfaOtpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings & Profile'),
        centerTitle: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShadTabs(
              value: selectedTab,
              onChanged: (value) {
                setState(() {
                  selectedTab = value;
                });
              },
              tabs:
                  tabs
                      .map((tab) => ShadTab(value: tab, child: Text(tab)))
                      .toList(),
            ),
            const SizedBox(height: 24),
            Expanded(child: _buildCurrentTabContent(selectedTab)),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentTabContent(String tabValue) {
    switch (tabValue) {
      case 'Personal Details':
        return _buildPersonalDetailsTab(context);
      case 'Firm Profile':
        return _buildFirmProfileTab(context);
      case 'Firm Users':
        return _buildFirmUsersTab(context);
      case 'Security':
        return _buildSecurityTab(context);
      case 'Notification Preferences':
        return _buildNotificationPreferencesTab(context);
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildPersonalDetailsTab(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isLoading = ref.watch(isLoadingProvider);
    final error = ref.watch(errorProvider);
    // final user = ref.watch(userProvider); // Not directly used here, emailDisplay is set
    // final solicitorProfile = ref.watch(solicitorProfileProvider); // Data is in controllers

    if (isLoading && _nameController.text.isEmpty) {
      // Show loading only on initial load
      return const Center(child: CircularProgressIndicator.adaptive());
    }

    if (error != null && _nameController.text.isEmpty) {
      // Show error only if initial load failed
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Error: $error',
              style: TextStyle(color: theme.colorScheme.destructive),
            ),
            const SizedBox(height: 16),
            ShadButton(
              child: const Text('Retry'),
              onPressed: _fetchPersonalDetails,
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      child: ShadForm(
        key: _personalDetailsFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Manage your personal information.',
              style: theme.textTheme.muted,
            ),
            const SizedBox(height: 24),
            ShadInputFormField(
              id: 'name',
              controller: _nameController,
              label: const Text('Full Name'),
              placeholder: const Text('Enter your full name'),
              validator: (value) {
                if (value.isEmpty) {
                  return 'Name cannot be empty.';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            ShadInputFormField(
              id: 'email',
              initialValue: _emailDisplay, // Use state variable
              label: const Text('Email Address'),
              readOnly: true,
            ),
            const SizedBox(height: 16),
            ShadInputFormField(
              id: 'contact_number',
              controller: _contactNumberController,
              label: const Text('Contact Number'),
              placeholder: const Text('Enter your contact number'),
            ),
            const SizedBox(height: 16),
            ShadInputFormField(
              id: 'position',
              controller: _positionController,
              label: const Text('Position in Firm'),
              placeholder: const Text('E.g., Partner, Associate'),
            ),
            const SizedBox(height: 24),
            ShadButton(
              child:
                  isLoading
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Text('Save Changes'),
              onPressed:
                  isLoading
                      ? null
                      : () async {
                        if (_personalDetailsFormKey.currentState!
                            .saveAndValidate()) {
                          ref.read(isLoadingProvider.notifier).state = true;
                          ref.read(errorProvider.notifier).state = null;

                          final currentUserId =
                              PocketBaseService().pb.authStore.model?.id;
                          final currentSolicitorProfile = ref.read(
                            solicitorProfileProvider,
                          );

                          if (currentUserId == null ||
                              currentSolicitorProfile == null) {
                            NotificationService.showError(
                              context,
                              'Error: User or profile data not available.',
                            );
                            ref.read(isLoadingProvider.notifier).state = false;
                            return;
                          }

                          try {
                            final pbService = PocketBaseService();
                            final newName = _nameController.text;
                            final newContact = _contactNumberController.text;
                            final newPosition = _positionController.text;

                            // 1. Update 'users' collection
                            Map<String, dynamic> userDataToUpdate = {};
                            if (newName != ref.read(userProvider)?.name) {
                              userDataToUpdate['name'] = newName;
                            }
                            // Potentially add other user fields if they are editable here
                            // For example, if User model had firstName, lastName and form had separate fields:
                            // if (_firstNameController.text != ref.read(userProvider)?.firstName) userDataToUpdate['first_name'] = _firstNameController.text;
                            // if (_lastNameController.text != ref.read(userProvider)?.lastName) userDataToUpdate['last_name'] = _lastNameController.text;

                            if (userDataToUpdate.isNotEmpty) {
                              await pbService.pb
                                  .collection('users')
                                  .update(
                                    currentUserId,
                                    body: userDataToUpdate,
                                  );
                            }

                            // 2. Update 'solicitor_profiles' collection
                            Map<String, dynamic> profileDataToUpdate = {};
                            if (newName !=
                                currentSolicitorProfile.solicitorName) {
                              profileDataToUpdate['solicitor_name'] = newName;
                            }
                            if (newContact !=
                                currentSolicitorProfile.contactNumber) {
                              profileDataToUpdate['contact_number'] =
                                  newContact;
                            }
                            if (newPosition !=
                                currentSolicitorProfile.positionInFirm) {
                              profileDataToUpdate['position_in_firm'] =
                                  newPosition;
                            }

                            if (profileDataToUpdate.isNotEmpty) {
                              await pbService.pb
                                  .collection('solicitor_profiles')
                                  .update(
                                    currentSolicitorProfile.id,
                                    body: profileDataToUpdate,
                                  );
                            }

                            NotificationService.showSuccess(
                              context,
                              'Personal details updated successfully!',
                            );
                            await _fetchPersonalDetails(); // Refresh data
                          } on pbsdk.ClientException catch (e) {
                            NotificationService.showError(
                              context,
                              'Update failed: ${e.response['message'] ?? e.toString()}',
                            );
                            ref.read(errorProvider.notifier).state =
                                'Update failed: ${e.response['message'] ?? e.toString()}';
                          } catch (e) {
                            NotificationService.showError(
                              context,
                              'An unexpected error occurred: ${e.toString()}',
                            );
                            ref.read(errorProvider.notifier).state =
                                'An unexpected error occurred: ${e.toString()}';
                          } finally {
                            ref.read(isLoadingProvider.notifier).state = false;
                          }
                        } else {
                          NotificationService.showError(
                            context,
                            'Please correct the errors in the form.',
                          );
                        }
                      },
            ),
            if (error != null &&
                !isLoading) // Show error message below button if save fails
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  error,
                  style: TextStyle(color: theme.colorScheme.destructive),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFirmProfileTab(BuildContext context) {
    final theme = ShadTheme.of(context);
    // final solicitorProfile = ref.watch(solicitorProfileProvider); // Data is in controllers
    final isLoading = ref.watch(isLoadingProvider);
    final error = ref.watch(errorProvider);
    final solicitorProfile = ref.watch(
      solicitorProfileProvider,
    ); // Watch for PU status fields

    if (isLoading && _lawFirmNameController.text.isEmpty) {
      // Show loading only on initial load for this tab's distinct fields
      return const Center(child: CircularProgressIndicator.adaptive());
    }
    if (error != null &&
        _lawFirmNameController.text.isEmpty &&
        ref.watch(userProvider) == null) {
      // Show error only if initial load failed
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Error: $error',
              style: TextStyle(color: theme.colorScheme.destructive),
            ),
            const SizedBox(height: 16),
            ShadButton(
              child: const Text('Retry'),
              onPressed: _fetchPersonalDetails,
            ),
          ],
        ),
      );
    }
    final bool canEditFirmProfile = _isFirmAdmin();

    return SingleChildScrollView(
      child: ShadForm(
        key: _firmProfileFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Manage your firm\'s profile information.',
              style: theme.textTheme.muted,
            ),
            if (!canEditFirmProfile) ...[
              const SizedBox(height: 16),
              ShadAlert(
                icon: Icon(
                  LucideIcons.shieldAlert,
                  size: 16,
                ), // Replaced ShadImage.square
                title: const Text('Admin Access Required'),
                description: const Text(
                  'You do not have permission to edit firm profile details.',
                ),
              ),
            ],
            const SizedBox(height: 24),
            ShadInputFormField(
              id: 'law_firm_name',
              controller: _lawFirmNameController,
              label: const Text('Law Firm Name'),
              placeholder: const Text('Enter the law firm name'),
              readOnly: !canEditFirmProfile,
              validator:
                  canEditFirmProfile
                      ? (value) {
                        if (value.isEmpty) {
                          return 'Law firm name cannot be empty.';
                        }
                        return null;
                      }
                      : null,
            ),
            const SizedBox(height: 16),
            ShadInputFormField(
              id: 'firm_address',
              controller: _firmAddressController,
              label: const Text('Firm Address'),
              placeholder: const Text('Enter the firm address'),
              maxLines: 3,
              readOnly: !canEditFirmProfile,
            ),
            const SizedBox(height: 16),
            ShadInputFormField(
              id: 'sra_number',
              controller: _sraNumberController,
              label: const Text('Firm Registration / SRA Number'),
              placeholder: const Text('Enter SRA number or registration ID'),
              readOnly: !canEditFirmProfile,
            ),
            const SizedBox(height: 16),
            ShadInputFormField(
              id: 'pu_status',
              initialValue: solicitorProfile?.puStatus ?? 'N/A',
              label: const Text('PU Status'),
              readOnly: true,
            ),
            const SizedBox(height: 8),
            ShadInputFormField(
              id: 'pu_application_date',
              initialValue: solicitorProfile?.puApplicationDate ?? 'N/A',
              label: const Text('PU Application Date'),
              readOnly: true,
            ),
            const SizedBox(height: 8),
            ShadInputFormField(
              id: 'pu_decision_date',
              initialValue: solicitorProfile?.puDecisionDate ?? 'N/A',
              label: const Text('PU Decision Date'),
              readOnly: true,
            ),
            if (canEditFirmProfile) ...[
              const SizedBox(height: 24),
              ShadButton(
                child: const Text('Save Firm Profile'),
                onPressed: () async {
                  if (_firmProfileFormKey.currentState!.saveAndValidate()) {
                    ref.read(isLoadingProvider.notifier).state = true;
                    ref.read(errorProvider.notifier).state = null;
                    final currentSolicitorProfile = ref.read(
                      solicitorProfileProvider,
                    );

                    if (currentSolicitorProfile == null) {
                      NotificationService.showError(
                        context,
                        'Error: Solicitor profile data not available.',
                      );
                      ref.read(isLoadingProvider.notifier).state = false;
                      return;
                    }

                    try {
                      final pbService = PocketBaseService();
                      final firmName = _lawFirmNameController.text;
                      final firmAddress = _firmAddressController.text;
                      final sraNumber = _sraNumberController.text;

                      Map<String, dynamic> firmProfileDataToUpdate = {};
                      if (firmName != currentSolicitorProfile.lawFirmName) {
                        firmProfileDataToUpdate['law_firm_name'] = firmName;
                      }
                      if (firmAddress != currentSolicitorProfile.firmAddress) {
                        firmProfileDataToUpdate['firm_address'] = firmAddress;
                      }
                      if (sraNumber != currentSolicitorProfile.sraNumber) {
                        firmProfileDataToUpdate['sra_number'] = sraNumber;
                      }

                      if (firmProfileDataToUpdate.isNotEmpty) {
                        await pbService.pb
                            .collection('solicitor_profiles')
                            .update(
                              currentSolicitorProfile.id,
                              body: firmProfileDataToUpdate,
                            );
                      }
                      NotificationService.showSuccess(
                        context,
                        'Firm profile updated successfully!',
                      );
                      await _fetchPersonalDetails(); // Refresh data
                    } on pbsdk.ClientException catch (e) {
                      NotificationService.showError(
                        context,
                        'Update failed: ${e.response['message'] ?? e.toString()}',
                      );
                      ref.read(errorProvider.notifier).state =
                          'Update failed: ${e.response['message'] ?? e.toString()}';
                    } catch (e) {
                      NotificationService.showError(
                        context,
                        'An unexpected error occurred: ${e.toString()}',
                      );
                      ref.read(errorProvider.notifier).state =
                          'An unexpected error occurred: ${e.toString()}';
                    } finally {
                      ref.read(isLoadingProvider.notifier).state = false;
                    }
                  } else {
                    NotificationService.showError(
                      context,
                      'Please correct the errors in the form.',
                    );
                  }
                },
              ),
              if (error != null &&
                  !isLoading &&
                  _lawFirmNameController
                      .text
                      .isNotEmpty) // Show error message below button if save fails
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    error,
                    style: TextStyle(color: theme.colorScheme.destructive),
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFirmUsersTab(BuildContext context) {
    final theme = ShadTheme.of(context);
    final bool canManageFirmUsers = _isFirmAdmin();
    final firmUsers = ref.watch(firmUsersProvider);
    final isFirmUsersLoading = ref.watch(isFirmUsersLoadingProvider);
    final firmUsersError = ref.watch(firmUsersErrorProvider);
    // final solicitorProfile = ref.watch(solicitorProfileProvider); // Already available if needed for other checks
    // final currentUser = ref.watch(userProvider); // Already available if needed

    if (isFirmUsersLoading && firmUsers.isEmpty) {
      return const Center(child: CircularProgressIndicator.adaptive());
    }

    if (firmUsersError != null && firmUsers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Error: $firmUsersError',
              style: TextStyle(color: theme.colorScheme.destructive),
            ),
            const SizedBox(height: 16),
            ShadButton(
              child: const Text('Retry'),
              onPressed: () {
                final pbService = PocketBaseService();
                final currentProfile = ref.read(solicitorProfileProvider);
                if (currentProfile != null) {
                  _fetchFirmUsers(pbService, currentProfile);
                } else {
                  _fetchPersonalDetails(); // Fallback to refetch all if profile is missing
                }
              },
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Manage users associated with your firm.',
            style: theme.textTheme.muted,
          ),
          if (!canManageFirmUsers) ...[
            const SizedBox(height: 16),
            ShadAlert(
              icon: Icon(
                LucideIcons.shieldAlert,
                size: 16,
              ), // Replaced ShadImage.square
              title: const Text('Admin Access Required'),
              description: const Text(
                'You do not have permission to manage firm users.',
              ),
            ),
            const SizedBox(height: 24),
          ] else ...[
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ShadButton(
                  child: const Text('Invite New User'),
                  onPressed: () {
                    _showInviteUserDialog(context);
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text('Current Firm Users:', style: theme.textTheme.large),
            const SizedBox(height: 8),
            if (firmUsers.isEmpty)
              const Text('No users found for this firm yet.')
            else
              ListView.builder(
                shrinkWrap: true,
                physics:
                    const NeverScrollableScrollPhysics(), // To use within SingleChildScrollView
                itemCount: firmUsers.length,
                itemBuilder: (context, index) {
                  final user = firmUsers[index];
                  // The User model now includes the 'verified' field.
                  String status = user.verified ? 'Active' : 'Invited';
                  String role = user.userType; // From User model

                  return Card(
                    margin: const EdgeInsets.symmetric(vertical: 4),
                    child: ListTile(
                      title: Text(user.displayName), // Use displayName getter
                      subtitle: Text(
                        '${user.email ?? "No email"} - Role: $role - Status: $status',
                      ),
                      trailing: PopupMenuButton<String>(
                        // Reverting to standard PopupMenuButton
                        icon: const Icon(
                          LucideIcons.ellipsisVertical,
                          size: 16,
                        ),
                        onSelected: (String action) {
                          if (action == 'disable_reenable') {
                            if (user.verified) {
                              _disableUser(context, user);
                            } else {
                              _reenableUser(context, user);
                            }
                          } else if (action == 'remove') {
                            _removeUserFromFirm(context, user);
                          }
                        },
                        itemBuilder:
                            (BuildContext context) => <PopupMenuEntry<String>>[
                              PopupMenuItem<String>(
                                value: 'disable_reenable',
                                child: Text(
                                  user.verified
                                      ? 'Disable User'
                                      : 'Re-enable User',
                                ),
                              ),
                              PopupMenuItem<String>(
                                value: 'remove',
                                child: const Text('Remove from Firm'),
                              ),
                            ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ],
      ),
    );
  }

  Future<void> _showConfirmationDialog({
    required BuildContext context,
    required String title,
    required String content,
    required VoidCallback onConfirm,
    bool isDestructive = false,
  }) async {
    return showShadDialog(
      context: context,
      builder:
          (context) => ShadDialog(
            title: Text(title),
            description: Text(content),
            actions: [
              ShadButton.outline(
                child: const Text('Cancel'),
                onPressed: () => Navigator.of(context).pop(),
              ),
              isDestructive
                  ? ShadButton.destructive(
                    child: const Text('Confirm'),
                    onPressed: () {
                      Navigator.of(context).pop();
                      onConfirm();
                    },
                  )
                  : ShadButton(
                    child: const Text('Confirm'),
                    onPressed: () {
                      Navigator.of(context).pop();
                      onConfirm();
                    },
                  ),
            ],
          ),
    );
  }

  Future<void> _disableUser(BuildContext context, User userToDisable) async {
    await _showConfirmationDialog(
      context: context,
      title: 'Disable User',
      content:
          'Are you sure you want to disable ${userToDisable.displayName}? They will lose access to the platform.',
      isDestructive: true,
      onConfirm: () async {
        ref.read(isFirmUsersLoadingProvider.notifier).state = true;
        try {
          final pbService = PocketBaseService();
          await pbService.pb
              .collection('users')
              .update(userToDisable.id, body: {'verified': false});
          NotificationService.showSuccess(
            context,
            '${userToDisable.displayName} has been disabled.',
          );
          final currentProfile = ref.read(solicitorProfileProvider);
          if (currentProfile != null) {
            await _fetchFirmUsers(pbService, currentProfile);
          }
        } on pbsdk.ClientException catch (e) {
          NotificationService.showError(
            context,
            'Failed to disable user: ${e.response['message'] ?? e.toString()}',
          );
        } catch (e) {
          NotificationService.showError(
            context,
            'An unexpected error occurred: ${e.toString()}',
          );
        } finally {
          ref.read(isFirmUsersLoadingProvider.notifier).state = false;
        }
      },
    );
  }

  Future<void> _reenableUser(BuildContext context, User userToReenable) async {
    await _showConfirmationDialog(
      context: context,
      title: 'Re-enable User',
      content:
          'Are you sure you want to re-enable ${userToReenable.displayName}? They will regain access to the platform.',
      onConfirm: () async {
        ref.read(isFirmUsersLoadingProvider.notifier).state = true;
        try {
          final pbService = PocketBaseService();
          await pbService.pb
              .collection('users')
              .update(userToReenable.id, body: {'verified': true});
          NotificationService.showSuccess(
            context,
            '${userToReenable.displayName} has been re-enabled.',
          );
          final currentProfile = ref.read(solicitorProfileProvider);
          if (currentProfile != null) {
            await _fetchFirmUsers(pbService, currentProfile);
          }
        } on pbsdk.ClientException catch (e) {
          NotificationService.showError(
            context,
            'Failed to re-enable user: ${e.response['message'] ?? e.toString()}',
          );
        } catch (e) {
          NotificationService.showError(
            context,
            'An unexpected error occurred: ${e.toString()}',
          );
        } finally {
          ref.read(isFirmUsersLoadingProvider.notifier).state = false;
        }
      },
    );
  }

  Future<void> _removeUserFromFirm(
    BuildContext context,
    User userToRemove,
  ) async {
    await _showConfirmationDialog(
      context: context,
      title: 'Remove User from Firm',
      content:
          'Are you sure you want to remove ${userToRemove.displayName} from the firm? This will not delete their account but will remove their association with this firm.',
      isDestructive: true,
      onConfirm: () async {
        ref.read(isFirmUsersLoadingProvider.notifier).state = true;
        final currentSolicitorProfile = ref.read(solicitorProfileProvider);

        if (currentSolicitorProfile == null) {
          NotificationService.showError(
            context,
            'Error: Current solicitor profile not found.',
          );
          ref.read(isFirmUsersLoadingProvider.notifier).state = false;
          return;
        }

        try {
          final pbService = PocketBaseService();
          List<String> updatedAdditionalUsers = List<String>.from(
            currentSolicitorProfile.additionalUsers,
          );
          updatedAdditionalUsers.remove(userToRemove.id);

          await pbService.pb
              .collection('solicitor_profiles')
              .update(
                currentSolicitorProfile.id,
                body: {'additional_users': updatedAdditionalUsers},
              );
          NotificationService.showSuccess(
            context,
            '${userToRemove.displayName} has been removed from the firm.',
          );
          // Refresh firm users list by re-fetching the solicitor profile which in turn calls _fetchFirmUsers
          await _fetchPersonalDetails(); // This will re-fetch solicitor profile and then firm users.
          // Alternatively, call _fetchFirmUsers directly if _fetchPersonalDetails is too broad.
          // For simplicity and to ensure all related states are current:
          // final updatedProfile = await pbService.pb.collection('solicitor_profiles').getOne(currentSolicitorProfile.id);
          // ref.read(solicitorProfileProvider.notifier).state = SolicitorProfileModel.fromJson(updatedProfile.toJson());
          // await _fetchFirmUsers(pbService, ref.read(solicitorProfileProvider)!);
        } on pbsdk.ClientException catch (e) {
          NotificationService.showError(
            context,
            'Failed to remove user: ${e.response['message'] ?? e.toString()}',
          );
        } catch (e) {
          NotificationService.showError(
            context,
            'An unexpected error occurred: ${e.toString()}',
          );
        } finally {
          ref.read(isFirmUsersLoadingProvider.notifier).state = false;
        }
      },
    );
  }

  void _showInviteUserDialog(BuildContext context) {
    final theme = ShadTheme.of(context);
    final inviteFormKey = GlobalKey<ShadFormState>();
    final emailController = TextEditingController();
    final nameController = TextEditingController();

    showShadDialog(
      context: context,
      builder: (context) {
        // Use a StatefulWidget builder to manage loading state within the dialog
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {
            bool isDialogLoading = false;

            return ShadDialog(
              title: const Text('Invite New User to Firm'),
              description: const Text(
                'Enter the email and name of the user you want to invite.',
              ),
              child: ShadForm(
                key: inviteFormKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ShadInputFormField(
                      id: 'invite_email',
                      controller: emailController,
                      label: const Text('Email Address'),
                      placeholder: const Text('<EMAIL>'),
                      validator: (value) {
                        if (value == null ||
                            value.isEmpty ||
                            !value.contains('@')) {
                          return 'Please enter a valid email address.';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    ShadInputFormField(
                      id: 'invite_name',
                      controller: nameController,
                      label: const Text('Full Name (Optional)'),
                      placeholder: const Text('John Doe'),
                    ),
                  ],
                ),
              ),
              actions: [
                ShadButton.outline(
                  child: const Text('Cancel'),
                  onPressed:
                      isDialogLoading
                          ? null
                          : () => Navigator.of(context).pop(),
                ),
                ShadButton(
                  child:
                      isDialogLoading
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : const Text('Send Invitation'),
                  onPressed:
                      isDialogLoading
                          ? null
                          : () async {
                            if (inviteFormKey.currentState!.saveAndValidate()) {
                              setDialogState(() {
                                isDialogLoading = true;
                              });

                              final email = emailController.text;
                              final name = nameController.text;
                              final pbService = PocketBaseService();
                              final currentSolicitorProfile = ref.read(
                                solicitorProfileProvider,
                              );

                              if (currentSolicitorProfile == null) {
                                NotificationService.showError(
                                  context,
                                  'Error: Current solicitor profile not found.',
                                );
                                setDialogState(() {
                                  isDialogLoading = false;
                                });
                                return;
                              }

                              try {
                                // 1. Create a new user record
                                final newUserData = {
                                  'email': email,
                                  'name':
                                      name.isNotEmpty
                                          ? name
                                          : email.split('@').first,
                                  'user_type': 'solicitor',
                                  'verified': false,
                                  // PocketBase handles password setting via verification email
                                };
                                final newUserRecord = await pbService.pb
                                    .collection('users')
                                    .create(body: newUserData);
                                final newUserId = newUserRecord.id;

                                // 2. Link the new user to the current solicitor's firm
                                // Ensure additionalUsers is treated as a list of strings
                                List<String> additionalUsersIds =
                                    List<String>.from(
                                      currentSolicitorProfile.additionalUsers,
                                    );
                                if (!additionalUsersIds.contains(newUserId)) {
                                  additionalUsersIds.add(newUserId);
                                }

                                await pbService.pb
                                    .collection('solicitor_profiles')
                                    .update(
                                      currentSolicitorProfile.id,
                                      body: {
                                        'additional_users': additionalUsersIds,
                                      },
                                    );

                                // 3. Trigger Pocketbase's email verification process
                                await pbService.pb
                                    .collection('users')
                                    .requestVerification(email);

                                NotificationService.showSuccess(
                                  context,
                                  'Invitation sent successfully to $email.',
                                );
                                Navigator.of(context).pop();
                                // Refresh firm users list
                                await _fetchFirmUsers(
                                  pbService,
                                  ref.read(solicitorProfileProvider)!,
                                );
                              } on pbsdk.ClientException catch (e) {
                                String errorMessage =
                                    'Failed to send invitation: ${e.response['message'] ?? e.toString()}';
                                if (e.response['data']?['email']?['code'] ==
                                    'validation_not_unique') {
                                  errorMessage =
                                      'This email address is already registered.';
                                }
                                NotificationService.showError(
                                  context,
                                  errorMessage,
                                );
                              } catch (e) {
                                NotificationService.showError(
                                  context,
                                  'An unexpected error occurred: ${e.toString()}',
                                );
                              } finally {
                                setDialogState(() {
                                  isDialogLoading = false;
                                });
                              }
                            }
                          },
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildSecurityTab(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isPasswordChanging = ref.watch(isPasswordChangingProvider);
    final currentUser = ref.watch(userProvider);
    final is2faEnabled = currentUser?.tfaEnabled ?? false;
    final is2faCurrentlyLoading = ref.watch(is2faLoadingProvider);
    final provisioningUri = ref.watch(tfaProvisioningUriProvider);
    final tfaSecret = ref.watch(tfaSecretProvider);
    final showEnableUI = ref.watch(show2faEnableSetupUIProvider);
    final showDisableConfirmUI = ref.watch(show2faDisableConfirmUIProvider);

    return SingleChildScrollView(
      child: ShadForm(
        key:
            _securityFormKey, // This key might need to be split if 2FA has its own form elements
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Manage your account security settings.',
              style: theme.textTheme.muted,
            ),
            const SizedBox(height: 24),
            Text('Change Password', style: theme.textTheme.large),
            const SizedBox(height: 16),
            ShadInputFormField(
              id: 'current_password',
              controller: _currentPasswordController,
              label: const Text('Current Password'),
              obscureText: true,
              validator: (value) {
                if (value.isEmpty) {
                  return 'Current password cannot be empty.';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            ShadInputFormField(
              id: 'new_password',
              controller: _newPasswordController,
              label: const Text('New Password'),
              obscureText: true,
              validator: (value) {
                if (value.isEmpty) {
                  return 'New password cannot be empty.';
                }
                if (value.length < 8) {
                  return 'Password must be at least 8 characters long.';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            ShadInputFormField(
              id: 'confirm_password',
              controller: _confirmPasswordController,
              label: const Text('Confirm New Password'),
              obscureText: true,
              validator: (value) {
                if (value.isEmpty) {
                  return 'Please confirm your new password.';
                }
                if (value != _newPasswordController.text) {
                  return 'Passwords do not match.';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            ShadButton(
              child:
                  isPasswordChanging
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Text('Change Password'),
              onPressed:
                  isPasswordChanging
                      ? null
                      : () async {
                        // Using a different form key for password change if _securityFormKey is shared
                        // For now, assuming _securityFormKey covers password fields.
                        // If _securityFormKey.currentState is null, it means the form is not yet built or visible.
                        if (_securityFormKey.currentState?.saveAndValidate() ??
                            false) {
                          ref.read(isPasswordChangingProvider.notifier).state =
                              true;
                          final currentPassword =
                              _currentPasswordController.text;
                          final newPassword = _newPasswordController.text;
                          final confirmNewPassword =
                              _confirmPasswordController.text;

                          final pbService = PocketBaseService();
                          final userId = pbService.pb.authStore.model?.id;

                          if (userId == null) {
                            NotificationService.showError(
                              context,
                              'Error: User not authenticated.',
                            );
                            ref
                                .read(isPasswordChangingProvider.notifier)
                                .state = false;
                            return;
                          }

                          try {
                            await pbService.pb
                                .collection('users')
                                .update(
                                  userId,
                                  body: {
                                    'oldPassword': currentPassword,
                                    'password': newPassword,
                                    'passwordConfirm': confirmNewPassword,
                                  },
                                );
                            NotificationService.showSuccess(
                              context,
                              'Password changed successfully!',
                            );
                            _currentPasswordController.clear();
                            _newPasswordController.clear();
                            _confirmPasswordController.clear();
                            _securityFormKey.currentState?.reset();
                          } on pbsdk.ClientException catch (e) {
                            String errorMessage =
                                e.response['message'] as String? ??
                                e.toString();
                            if (e.response['data'] != null &&
                                e.response['data'] is Map) {
                              final data =
                                  e.response['data'] as Map<String, dynamic>;
                              if (data.containsKey('oldPassword') &&
                                  data['oldPassword']?['code'] ==
                                      'validation_invalid_password') {
                                errorMessage = 'Incorrect current password.';
                              } else if (data.containsKey('password') &&
                                  data['password']?['code'] ==
                                      'validation_password_too_short') {
                                errorMessage = 'New password is too short.';
                              }
                            }
                            NotificationService.showError(
                              context,
                              'Failed to change password: $errorMessage',
                            );
                          } catch (e) {
                            NotificationService.showError(
                              context,
                              'An unexpected error occurred: ${e.toString()}',
                            );
                          } finally {
                            ref
                                .read(isPasswordChangingProvider.notifier)
                                .state = false;
                          }
                        } else {
                          NotificationService.showError(
                            context,
                            'Please correct the errors in the password form.',
                          );
                        }
                      },
            ),
            const SizedBox(height: 32),
            Text(
              'Two-Factor Authentication (2FA)',
              style: theme.textTheme.large,
            ),
            const SizedBox(height: 8),
            if (is2faCurrentlyLoading && !showEnableUI && !showDisableConfirmUI)
              const Center(child: CircularProgressIndicator.adaptive())
            else if (showEnableUI)
              _buildEnable2FASetupUI(context)
            else if (showDisableConfirmUI)
              _buildDisable2FAConfirmUI(context)
            else
              _build2FAManagementUI(context, is2faEnabled),
          ],
        ),
      ),
    );
  }

  Widget _build2FAManagementUI(BuildContext context, bool is2faEnabled) {
    final theme = ShadTheme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          is2faEnabled
              ? 'Status: Two-Factor Authentication is Enabled.'
              : 'Status: Two-Factor Authentication is Disabled.',
          style: theme.textTheme.muted,
        ),
        const SizedBox(height: 16),
        if (is2faEnabled)
          ShadButton.destructive(
            child: const Text('Disable 2FA'),
            onPressed: _initiateDisable2FA,
          )
        else
          ShadButton(
            child: const Text('Enable 2FA'),
            onPressed: _initiateEnable2FA,
          ),
      ],
    );
  }

  Widget _buildEnable2FASetupUI(BuildContext context) {
    final theme = ShadTheme.of(context);
    final provisioningUri = ref.watch(tfaProvisioningUriProvider);
    final tfaSecret = ref.watch(tfaSecretProvider);
    final is2faCurrentlyLoading = ref.watch(is2faLoadingProvider);

    if (is2faCurrentlyLoading && provisioningUri == null) {
      return const Center(child: CircularProgressIndicator.adaptive());
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Setup Two-Factor Authentication',
              style: theme.textTheme.large,
            ),
            const SizedBox(height: 16),
            const Text(
              '1. Scan the QR code with your authenticator app (e.g., Google Authenticator, Authy).',
            ),
            if (provisioningUri != null)
              Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: QrImageView(
                    data: provisioningUri,
                    version: QrVersions.auto,
                    size: 200.0,
                  ),
                ),
              ),
            const SizedBox(height: 16),
            const Text('2. Alternatively, manually enter the secret key:'),
            if (tfaSecret != null)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: SelectableText(
                  tfaSecret,
                  style: theme.textTheme.p.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            const SizedBox(height: 16),
            const Text('3. Enter the OTP generated by your app below:'),
            const SizedBox(height: 8),
            ShadInputFormField(
              id: 'tfa_otp',
              controller: _tfaOtpController,
              label: const Text('One-Time Password (OTP)'),
              placeholder: const Text('Enter 6-digit code'),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'OTP cannot be empty.';
                }
                if (value.length != 6 || int.tryParse(value) == null) {
                  return 'Please enter a valid 6-digit OTP.';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ShadButton.outline(
                  child: const Text('Cancel'),
                  onPressed: () {
                    ref.read(show2faEnableSetupUIProvider.notifier).state =
                        false;
                    _tfaOtpController.clear();
                    // Clear URI and secret if needed
                    ref.read(tfaProvisioningUriProvider.notifier).state = null;
                    ref.read(tfaSecretProvider.notifier).state = null;
                  },
                ),
                const SizedBox(width: 8),
                ShadButton(
                  child:
                      is2faCurrentlyLoading
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : const Text('Confirm & Enable 2FA'),
                  onPressed: is2faCurrentlyLoading ? null : _confirmEnable2FA,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDisable2FAConfirmUI(BuildContext context) {
    final theme = ShadTheme.of(context);
    final is2faCurrentlyLoading = ref.watch(is2faLoadingProvider);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Confirm Disabling 2FA', style: theme.textTheme.large),
            const SizedBox(height: 16),
            const Text(
              'To disable Two-Factor Authentication, please enter a one-time password from your authenticator app.',
            ),
            const SizedBox(height: 16),
            ShadInputFormField(
              id: 'disable_tfa_otp',
              controller: _tfaOtpController,
              label: const Text('One-Time Password (OTP)'),
              placeholder: const Text('Enter 6-digit code'),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'OTP cannot be empty.';
                }
                if (value.length != 6 || int.tryParse(value) == null) {
                  return 'Please enter a valid 6-digit OTP.';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ShadButton.outline(
                  child: const Text('Cancel'),
                  onPressed: () {
                    ref.read(show2faDisableConfirmUIProvider.notifier).state =
                        false;
                    _tfaOtpController.clear();
                  },
                ),
                const SizedBox(width: 8),
                ShadButton.destructive(
                  child:
                      is2faCurrentlyLoading
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : const Text('Confirm & Disable 2FA'),
                  onPressed: is2faCurrentlyLoading ? null : _confirmDisable2FA,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _initiateEnable2FA() async {
    ref.read(is2faLoadingProvider.notifier).state = true;
    ref.read(show2faEnableSetupUIProvider.notifier).state = true;
    ref.read(show2faDisableConfirmUIProvider.notifier).state =
        false; // Ensure disable UI is hidden
    _tfaOtpController.clear();

    final pbService = PocketBaseService();
    try {
      // Use aliased SDK and direct users service
      final currentUser = pbService.authStore.model;
      if (currentUser == null) throw Exception("User not authenticated.");
      final resultData = await pbService.pb.send(
        '/api/collections/users/records/${currentUser.id}/totp',
        method: 'POST',
      );
      // Manually construct a UserTotp-like structure or extract directly
      final String secret = resultData['secret'] as String;
      final String provisioningUri = resultData['uri'] as String;
      // The result object from PocketBase SDK for requestTotpEnable contains:
      // - secret: The TOTP secret.
      // - qrCode: A data URL of the QR code image (e.g., "data:image/png;base64,iVBORw0KGgo...").
      // - uri: The otpauth:// URI.
      // We need the URI for qr_flutter.

      // PocketBase Dart SDK `client.users.requestTotpEnable()` returns a `UserTotp` model
      // which has `secret` and `uri` (which is the otpauth:// URI).
      // It does not directly return a base64 qrCode string like some other PB SDKs might.
      // qr_flutter uses the otpauth:// URI directly.

      ref.read(tfaSecretProvider.notifier).state = secret;
      ref.read(tfaProvisioningUriProvider.notifier).state =
          provisioningUri; // Use the otpauth URI
    } on pbsdk.ClientException catch (e) {
      NotificationService.showError(
        context,
        'Failed to start 2FA setup: ${e.response['message'] ?? e.toString()}',
      );
      ref.read(show2faEnableSetupUIProvider.notifier).state =
          false; // Hide setup UI on error
    } catch (e) {
      NotificationService.showError(
        context,
        'An unexpected error occurred: ${e.toString()}',
      );
      ref.read(show2faEnableSetupUIProvider.notifier).state =
          false; // Hide setup UI on error
    } finally {
      ref.read(is2faLoadingProvider.notifier).state = false;
    }
  }

  Future<void> _confirmEnable2FA() async {
    // It's good practice to use a specific form key for 2FA OTP if it's complex
    // For now, we assume the _tfaOtpController is validated directly or via a simple check.
    final otpCode = _tfaOtpController.text;
    if (otpCode.isEmpty ||
        otpCode.length != 6 ||
        int.tryParse(otpCode) == null) {
      NotificationService.showError(
        context,
        'Please enter a valid 6-digit OTP.',
      );
      return;
    }

    ref.read(is2faLoadingProvider.notifier).state = true;
    final pbService = PocketBaseService();
    final userId = pbService.pb.authStore.model?.id;
    final secret = ref.read(tfaSecretProvider);

    if (userId == null || secret == null) {
      NotificationService.showError(
        context,
        'Error: User session or 2FA secret is missing.',
      );
      ref.read(is2faLoadingProvider.notifier).state = false;
      return;
    }

    try {
      // Use aliased SDK and direct users service
      final currentUser = pbService.authStore.model;
      if (currentUser == null) throw Exception("User not authenticated.");
      if (secret == null)
        throw Exception(
          "2FA secret not available.",
        ); // Already have this check, but good to be sure
      final authRecord = await pbService.pb.send(
        '/api/collections/users/records/${currentUser.id}/totp/confirm',
        method: 'POST',
        body: {'token': secret, 'code': otpCode},
      );
      NotificationService.showSuccess(
        context,
        'Two-Factor Authentication enabled successfully!',
      );

      // Refresh user data to reflect tfa_enabled status
      // _fetchPersonalDetails will update the userProvider, which includes the new User model with backupCodes
      await _fetchPersonalDetails();

      ref.read(show2faEnableSetupUIProvider.notifier).state = false;
      _tfaOtpController.clear();
      ref.read(tfaProvisioningUriProvider.notifier).state = null;
      ref.read(tfaSecretProvider.notifier).state = null;

      // Process backup codes from the updatedUserRecord
      // The User.fromJson factory now handles 'backup_codes'
      final updatedPbUser = User.fromJson(authRecord.record!.toJson());
      if (updatedPbUser.backupCodes != null &&
          updatedPbUser.backupCodes!.isNotEmpty) {
        // Small delay to ensure the main success message is seen first, then show recovery codes.
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            // Check if the widget is still in the tree
            _showRecoveryCodesDialog(context, updatedPbUser.backupCodes!);
          }
        });
      }
    } on pbsdk.ClientException catch (e) {
      String errMsg = e.response['message'] ?? e.toString();
      if (e.statusCode == 400 &&
          e.response['data']?['code']?['code'] == 'validation_invalid_token') {
        errMsg = 'Invalid OTP. Please try again.';
      }
      NotificationService.showError(context, 'Failed to enable 2FA: $errMsg');
    } catch (e) {
      NotificationService.showError(
        context,
        'An unexpected error occurred: ${e.toString()}',
      );
    } finally {
      ref.read(is2faLoadingProvider.notifier).state = false;
    }
  }

  Future<void> _initiateDisable2FA() async {
    // Show UI for OTP confirmation
    ref.read(show2faDisableConfirmUIProvider.notifier).state = true;
    ref.read(show2faEnableSetupUIProvider.notifier).state =
        false; // Ensure enable UI is hidden
    _tfaOtpController.clear();
  }

  Future<void> _confirmDisable2FA() async {
    final otpCode = _tfaOtpController.text;
    if (otpCode.isEmpty ||
        otpCode.length != 6 ||
        int.tryParse(otpCode) == null) {
      NotificationService.showError(
        context,
        'Please enter a valid 6-digit OTP to disable 2FA.',
      );
      return;
    }

    ref.read(is2faLoadingProvider.notifier).state = true;
    final pbService = PocketBaseService();
    final userId = pbService.pb.authStore.model?.id;

    if (userId == null) {
      NotificationService.showError(context, 'Error: User not authenticated.');
      ref.read(is2faLoadingProvider.notifier).state = false;
      return;
    }
    try {
      final pbClient =
          pbService.pb; // Get the PocketBase client from PocketBaseService
      final currentUser = pbClient.authStore.model;
      if (currentUser == null) throw Exception("User not authenticated.");
      await pbClient.send(
        '/api/collections/users/records/${currentUser.id}/totp_disable',
        method: 'POST',
        body: {'code': otpCode},
      );
      NotificationService.showSuccess(
        context,
        'Two-Factor Authentication disabled successfully!',
      );

      // Refresh user data
      await _fetchPersonalDetails();

      ref.read(show2faDisableConfirmUIProvider.notifier).state = false;
      _tfaOtpController.clear();
    } on pbsdk.ClientException catch (e) {
      String errMsg = e.response['message'] ?? e.toString();
      if (e.statusCode == 400 &&
          e.response['data']?['code']?['code'] == 'validation_invalid_token') {
        errMsg = 'Invalid OTP. Please try again.';
      }
      NotificationService.showError(context, 'Failed to disable 2FA: $errMsg');
    } catch (e) {
      NotificationService.showError(
        context,
        'An unexpected error occurred: ${e.toString()}',
      );
    } finally {
      ref.read(is2faLoadingProvider.notifier).state = false;
    }
  }

  // Placeholder for showing recovery codes if PocketBase provides them
  Future<void> _showRecoveryCodesDialog(
    BuildContext context,
    List<String> recoveryCodes,
  ) async {
    return showShadDialog(
      context: context,
      builder:
          (context) => ShadDialog.alert(
            title: const Text('Save Your Recovery Codes!'),
            description: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'These codes can be used to access your account if you lose access to your authenticator app. Store them securely.',
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: ShadTheme.of(context).colorScheme.border,
                    ),
                    // Assuming ShadTheme.of(context).radius is already a BorderRadius object or similar.
                    // If ShadTheme.of(context).radius is a double, then BorderRadius.circular(ShadTheme.of(context).radius) was correct.
                    // Given the error, it's likely ShadTheme.of(context).radius is what's expected directly or is a BorderRadius itself.
                    // Let's assume it's a BorderRadius object directly for now based on the error.
                    // If it's a double, the original code was fine and the error is strange.
                    // For now, trying to use it directly as if it's a BorderRadius.
                    // If `ShadTheme.of(context).radius` is a `double`, then the original `BorderRadius.circular(ShadTheme.of(context).radius)` was correct.
                    // The error "The argument type 'BorderRadius' can't be assigned to the parameter type 'double'" for `BorderRadius.circular(ShadTheme.of(context).radius)`
                    // implies that `ShadTheme.of(context).radius` is NOT a double, but perhaps a BorderRadius itself.
                    // Let's assume `ShadTheme.of(context).radius` is a `BorderRadius` object.
                    borderRadius: ShadTheme.of(context).radius,
                  ),
                  child: Column(
                    children:
                        recoveryCodes
                            .map(
                              (code) => SelectableText(
                                code,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            )
                            .toList(),
                  ),
                ),
                const SizedBox(height: 8),
                const Text('Each code can only be used once.'),
              ],
            ),
            actions: [
              ShadButton(
                child: const Text('I have saved them securely'),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
    );
  }

  Widget _buildNotificationPreferencesTab(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isFetching = ref.watch(isFetchingNotificationPreferencesProvider);
    final fetchError = ref.watch(notificationPreferencesErrorProvider);
    // The local state variables (_inAppNewMessage, _emailDigestFrequency, etc.)
    // are updated by _fetchNotificationPreferences, so the UI will reflect
    // the fetched or default data once loading is complete.

    if (isFetching) {
      return const Center(child: CircularProgressIndicator.adaptive());
    }

    if (fetchError != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Error: $fetchError',
              style: TextStyle(color: theme.colorScheme.destructive),
            ),
            const SizedBox(height: 16),
            ShadButton(
              child: const Text('Retry'),
              onPressed: _fetchNotificationPreferences,
            ),
          ],
        ),
      );
    }

    // Use a key for ShadSelect to ensure it rebuilds when _emailDigestFrequency changes programmatically
    final emailDigestSelectKey = ValueKey<String>(_emailDigestFrequency);

    return SingleChildScrollView(
      child: ShadForm(
        key: _notificationPreferencesFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Manage your notification settings.',
              style: theme.textTheme.muted,
            ),
            const SizedBox(height: 24),

            // In-App Notifications Section
            Text('In-App Notifications', style: theme.textTheme.large),
            const SizedBox(height: 16),
            _buildNotificationSwitch(
              title:
                  'New message from 3Pay Global Agent regarding Application/Claim',
              value: _inAppNewMessage,
              onChanged: (val) => setState(() => _inAppNewMessage = val),
            ),
            _buildNotificationSwitch(
              title:
                  'Application status change (e.g., Submitted, Requires Info, Approved, Rejected)',
              value: _inAppStatusChange,
              onChanged: (val) => setState(() => _inAppStatusChange = val),
            ),
            _buildNotificationSwitch(
              title: 'Claim status update',
              value: _inAppClaimUpdate,
              onChanged: (val) => setState(() => _inAppClaimUpdate = val),
            ),
            _buildNotificationSwitch(
              title: 'Task assigned / Action required',
              value: _inAppTaskAssigned,
              onChanged: (val) => setState(() => _inAppTaskAssigned = val),
            ),
            _buildNotificationSwitch(
              title: 'General platform announcements',
              value: _inAppAnnouncements,
              onChanged: (val) => setState(() => _inAppAnnouncements = val),
            ),
            const SizedBox(height: 24),

            // Email Notifications Section
            Text('Email Notifications', style: theme.textTheme.large),
            const SizedBox(height: 16),
            _buildNotificationSwitch(
              title:
                  'New message from 3Pay Global Agent regarding Application/Claim',
              value: _emailNewMessage,
              onChanged: (val) => setState(() => _emailNewMessage = val),
            ),
            _buildNotificationSwitch(
              title:
                  'Application status change (e.g., Submitted, Requires Info, Approved, Rejected)',
              value: _emailStatusChange,
              onChanged: (val) => setState(() => _emailStatusChange = val),
            ),
            _buildNotificationSwitch(
              title: 'Claim status update',
              value: _emailClaimUpdate,
              onChanged: (val) => setState(() => _emailClaimUpdate = val),
            ),
            _buildNotificationSwitch(
              title: 'Task assigned / Action required',
              value: _emailTaskAssigned,
              onChanged: (val) => setState(() => _emailTaskAssigned = val),
            ),
            _buildNotificationSwitch(
              title: 'General platform announcements',
              value: _emailAnnouncements,
              onChanged: (val) => setState(() => _emailAnnouncements = val),
            ),
            const SizedBox(height: 24),

            // Email Digest Section
            Text('Email Digest Frequency', style: theme.textTheme.large),
            const SizedBox(height: 16),
            ShadSelect<String>(
              key:
                  emailDigestSelectKey, // Ensure ShadSelect updates with _emailDigestFrequency
              placeholder: const Text('Select frequency'),
              initialValue:
                  _emailDigestFrequency, // This will be correctly set by _fetchNotificationPreferences
              options: const [
                ShadOption(value: 'Immediate', child: Text('Immediate')),
                ShadOption(value: 'Daily', child: Text('Daily')),
                ShadOption(value: 'Weekly', child: Text('Weekly')),
              ],
              selectedOptionBuilder: (context, value) => Text(value),
              onChanged: (value) {
                setState(() {
                  if (value != null) {
                    _emailDigestFrequency = value;
                  }
                });
              },
            ),
            const SizedBox(height: 32),

            Consumer(
              builder: (context, ref, child) {
                final isSaving = ref.watch(
                  isSavingNotificationPreferencesProvider,
                );
                return ShadButton(
                  child:
                      isSaving
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : const Text('Save Preferences'),
                  onPressed:
                      isSaving
                          ? null
                          : () async {
                            if (_notificationPreferencesFormKey.currentState!
                                .saveAndValidate()) {
                              ref
                                  .read(
                                    isSavingNotificationPreferencesProvider
                                        .notifier,
                                  )
                                  .state = true;
                              final pbService = PocketBaseService();
                              final userId = pbService.pb.authStore.model?.id;

                              if (userId == null) {
                                NotificationService.showError(
                                  context,
                                  'Error: User not authenticated.',
                                );
                                ref
                                    .read(
                                      isSavingNotificationPreferencesProvider
                                          .notifier,
                                    )
                                    .state = false;
                                return;
                              }

                              final preferencesJson = {
                                "in_app": {
                                  "new_message": _inAppNewMessage,
                                  "application_status_change":
                                      _inAppStatusChange,
                                  "claim_status_update": _inAppClaimUpdate,
                                  "task_assigned": _inAppTaskAssigned,
                                  "platform_announcements": _inAppAnnouncements,
                                },
                                "email": {
                                  "new_message": _emailNewMessage,
                                  "application_status_change":
                                      _emailStatusChange,
                                  "claim_status_update": _emailClaimUpdate,
                                  "task_assigned": _emailTaskAssigned,
                                  "platform_announcements": _emailAnnouncements,
                                },
                                "email_digest_frequency":
                                    _emailDigestFrequency
                                        .toLowerCase(), // "daily", "weekly", "immediate"
                              };

                              try {
                                await pbService.pb
                                    .collection('users')
                                    .update(
                                      userId,
                                      body: {
                                        'notification_preferences':
                                            preferencesJson,
                                      },
                                    );
                                NotificationService.showSuccess(
                                  context,
                                  'Notification preferences saved successfully!',
                                );
                                // Re-fetch preferences to ensure UI consistency and reflect saved state
                                await _fetchNotificationPreferences();
                              } on pbsdk.ClientException catch (e) {
                                NotificationService.showError(
                                  context,
                                  'Failed to save preferences: ${e.response['message'] ?? e.toString()}',
                                );
                              } catch (e) {
                                NotificationService.showError(
                                  context,
                                  'An unexpected error occurred: ${e.toString()}',
                                );
                              } finally {
                                ref
                                    .read(
                                      isSavingNotificationPreferencesProvider
                                          .notifier,
                                    )
                                    .state = false;
                              }
                            } else {
                              NotificationService.showError(
                                context,
                                'Please correct errors in the form.',
                              );
                            }
                          },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSwitch({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(child: Text(title)),
          ShadSwitch(value: value, onChanged: onChanged),
        ],
      ),
    );
  }
}
