import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/widgets/application_analytics_view.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/widgets/claim_analytics_view.dart'; // Added import

class ReportsPage extends ConsumerWidget {
  static const routeName = '/solicitor-reports';

  const ReportsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text('Reports & Analytics'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Application Analytics'),
              Tab(text: 'Claim Analytics'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            const ApplicationAnalyticsView(),
            const ClaimAnalyticsView(), // Replaced placeholder with ClaimAnalyticsView
          ],
        ),
      ),
    );
  }
}
