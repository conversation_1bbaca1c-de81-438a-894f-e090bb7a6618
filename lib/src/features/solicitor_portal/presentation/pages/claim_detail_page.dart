import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/document_preview_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/application/providers/audit_log_providers.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/loading_spinner_widget.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For FilteringTextInputFormatter
import 'package:flutter_riverpod/flutter_riverpod.dart'; // For ConsumerStatefulWidget and ConsumerState
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/professional_model.dart';
import 'dart:async'; // For Timer (debouncing search)
import 'dart:ui' as ui; // For TextDirection
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:pocketbase/pocketbase.dart'; // Import PocketBase

import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/user_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/data/models/user_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/claim_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/cost_tracking_details_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/services/claim_documents_service.dart'; // Import ClaimDocumentsService
import 'dart:convert'; // For json operations
import 'package:file_picker/file_picker.dart'; // For file selection
import 'package:intl/intl.dart'; // For date formatting
import 'package:http/http.dart' as http; // For MultipartFile
import 'package:url_launcher/url_launcher.dart'; // For launching URLs
import 'package:flutter/gestures.dart'; // For TapGestureRecognizer in ExpandableText
import 'package:pocketbase/pocketbase.dart' as pb;
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/application/providers/claim_communication_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/claim_message_model.dart';

class ClaimDetailPage extends ConsumerStatefulWidget {
  static const String routeName = '/solicitor/claim-detail'; // Added routeName
  final String claimId;

  const ClaimDetailPage({super.key, required this.claimId});

  @override
  ConsumerState<ClaimDetailPage> createState() => _ClaimDetailPageState();
}

class _ClaimDetailPageState extends ConsumerState<ClaimDetailPage> {
  Claim? _claimDetails;
  List<ClaimantProfile> _linkedClaimantProfiles = [];
  bool _isLoading = true;
  String? _errorMessage;
  final _newStatusUpdateController = TextEditingController();

  // Document service
  late final ClaimDocumentsService _claimDocumentsService;

  Timer? _debounce;
  List<ProfessionalModel> _searchResults = [];
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();

  List<User> _firmMembersSearchResults = [];
  List<User> _allFirmMembers = [];
  Set<String> _selectedFirmMemberIds = {};
  bool _isFetchingFirmMembers = false;
  final TextEditingController _firmMemberSearchController =
      TextEditingController();
  Timer? _firmMemberSearchDebounce;

  // User name resolution cache
  final Map<String, String> _resolvedUserNames = {};

  @override
  void initState() {
    super.initState();
    _claimDocumentsService = ClaimDocumentsService();
    _fetchClaimDetails();
  }

  /// Resolve user ID to display name
  Future<String> _resolveUserName(String userId) async {
    // Check cache first
    if (_resolvedUserNames.containsKey(userId)) {
      return _resolvedUserNames[userId]!;
    }

    try {
      final displayName = await UserService.getUserDisplayName(userId);
      _resolvedUserNames[userId] = displayName;
      return displayName;
    } catch (e) {
      LoggerService.warning('Failed to resolve user name for $userId: $e');
      final fallbackName = 'User ($userId)';
      _resolvedUserNames[userId] = fallbackName;
      return fallbackName;
    }
  }

  @override
  void dispose() {
    _newStatusUpdateController.dispose();
    _searchController.dispose();
    _firmMemberSearchController.dispose();
    _debounce?.cancel();
    _firmMemberSearchDebounce?.cancel();
    super.dispose();
  }

  Future<void> _fetchClaimDetails() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final pbClient = PocketBaseService().pb;

      final record = await pbClient
          .collection('funding_applications')
          .getOne(
            widget.claimId,
            expand:
                'solicitor_profile_id,barristers,experts,claim_admins,associated_solicitors',
          );

      // Fetch documents from the new claim_documents collection
      List<UploadedDocumentCategory> documentRepository = [];
      try {
        // First try to get documents from the new collection
        documentRepository = await _claimDocumentsService
            .getDocumentsForFundingApplication(widget.claimId);

        // If no documents found in new collection, try to migrate from old structure
        if (documentRepository.isEmpty) {
          final legacyDocuments = record.data['document_repository'];
          if (legacyDocuments != null &&
              legacyDocuments is List &&
              legacyDocuments.isNotEmpty) {
            // Convert legacy format to UploadedDocumentCategory objects
            final legacyCategories =
                legacyDocuments.map((doc) {
                  final versions =
                      (doc['versions'] as List<dynamic>)
                          .map(
                            (v) => DocumentVersion.fromJson(
                              v as Map<String, dynamic>,
                            ),
                          )
                          .toList();
                  return UploadedDocumentCategory(
                    logicalName: doc['logical_name'] as String,
                    currentVersionFileId:
                        doc['current_version_file_id'] as String,
                    versions: versions,
                  );
                }).toList();

            // Migrate to new collection
            await _claimDocumentsService.migrateDocumentRepository(
              fundingApplicationId: widget.claimId,
              documentRepository: legacyCategories,
            );

            documentRepository = legacyCategories;
          }
        }
      } catch (e) {
        LoggerService.error(
          'Error fetching documents from claim_documents collection',
          e,
        );
        // Fallback to legacy document repository
        final legacyDocuments = record.data['document_repository'];
        if (legacyDocuments != null && legacyDocuments is List) {
          documentRepository =
              legacyDocuments.map((doc) {
                final versions =
                    (doc['versions'] as List<dynamic>)
                        .map(
                          (v) => DocumentVersion.fromJson(
                            v as Map<String, dynamic>,
                          ),
                        )
                        .toList();
                return UploadedDocumentCategory(
                  logicalName: doc['logical_name'] as String,
                  currentVersionFileId:
                      doc['current_version_file_id'] as String,
                  versions: versions,
                );
              }).toList();
        }
      }

      // Create a modified record data with the fetched documents
      final recordData = Map<String, dynamic>.from(record.toJson());
      recordData['document_repository'] =
          documentRepository.map((c) => c.toJson()).toList();

      _claimDetails = Claim.fromJson(recordData);

      final claimantRecords = await pbClient
          .collection('claimant_profiles')
          .getFullList(filter: 'associated_claim_ids ~ "${widget.claimId}"');
      _linkedClaimantProfiles =
          claimantRecords
              .map((rec) => ClaimantProfile.fromJson(rec.toJson()))
              .toList();

      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
    } on ClientException catch (e, stackTrace) {
      if (!mounted) return;
      LoggerService.error('ClientException in _fetchClaimDetails', e);
      LoggerService.debug('StackTrace: ${stackTrace.toString()}');
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Load Error'),
          description: Text(
            'Failed to load claim details: ${e.response['message'] ?? e.toString()}',
          ),
        ),
      );
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error: ${e.response['message'] ?? e.toString()}';
      });
    } catch (e, stackTrace) {
      if (!mounted) return;
      LoggerService.error('Exception in _fetchClaimDetails', e);
      LoggerService.debug('StackTrace: ${stackTrace.toString()}');
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Error'),
          description: Text('An unexpected error occurred: ${e.toString()}'),
        ),
      );
      setState(() {
        _isLoading = false;
        _errorMessage = 'An unexpected error occurred: ${e.toString()}';
      });
    }
  }

  Future<void> _addStatusUpdate(String description) async {
    if (description.isEmpty || _claimDetails == null) {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Input Error'),
          description: const Text('Description cannot be empty.'),
        ),
      );
      return;
    }

    final pbClient = PocketBaseService().pb;
    final authModel = pbClient.authStore.model;
    String currentUserId;
    String currentUserName = 'Unknown User';

    if (authModel is RecordModel) {
      currentUserId = authModel.id;
      currentUserName =
          authModel.data['name'] as String? ?? 'User ($currentUserId)';
    } else if (authModel is User) {
      currentUserId = authModel.id;
      currentUserName = authModel.name ?? 'User ${authModel.id}';
    } else {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Auth Error'),
          description: const Text('User not authenticated. Cannot add update.'),
        ),
      );
      return;
    }

    final newUpdate = StatusUpdate(
      date: DateTime.now(),
      description: description,
      updatedByUserId: currentUserId,
      updatedByName: currentUserName,
    );

    List<dynamic> currentUpdatesJson = [];
    if (_claimDetails!.statusUpdates.isNotEmpty) {
      currentUpdatesJson =
          _claimDetails!.statusUpdates.map((su) => su.toJson()).toList();
    } else if (_claimDetails!.statusUpdates is String &&
        (_claimDetails!.statusUpdates as String).isNotEmpty) {
      try {
        final decoded = jsonDecode(_claimDetails!.statusUpdates as String);
        if (decoded is List) {
          currentUpdatesJson = decoded;
        }
      } catch (e) {
        LoggerService.error('Error decoding existing status_updates string', e);
      }
    }

    final updatedStatusUpdatesList = List<Map<String, dynamic>>.from(
      currentUpdatesJson,
    )..add(newUpdate.toJson());

    try {
      await pbClient
          .collection('funding_applications')
          .update(
            _claimDetails!.id,
            body: {'status_updates': updatedStatusUpdatesList},
          );

      ShadToaster.of(context).show(
        ShadToast(
          title: const Text('Success'),
          description: const Text('Status update added successfully.'),
        ),
      );
      _newStatusUpdateController.clear();
      await _fetchClaimDetails();
    } on ClientException catch (e) {
      LoggerService.error('ClientException in _addStatusUpdate', e);
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Update Error'),
          description: Text(
            'Failed to add status update: ${e.response['message'] ?? e.toString()}',
          ),
        ),
      );
    } catch (e) {
      LoggerService.error('Exception in _addStatusUpdate', e);
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Error'),
          description: Text('An unexpected error occurred: ${e.toString()}'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Loading Claim...')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_errorMessage != null || _claimDetails == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Text(_errorMessage ?? 'Could not load claim details.'),
        ),
      );
    }

    return DefaultTabController(
      length: 5,
      child: Scaffold(
        appBar: AppBar(
          title: Text(_claimDetails!.caseTitle),
          bottom: const TabBar(
            isScrollable: true,
            tabs: [
              Tab(text: 'Overview'),
              Tab(text: 'Status & Milestones'),
              Tab(text: 'Professionals'),
              Tab(text: 'Cost Tracking'),
              // Tab(text: 'FRFR Schedule'),
              Tab(text: 'Audit Trail'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            _buildOverviewTab(context, _claimDetails!),
            _buildStatusUpdatesTab(context, _claimDetails!),
            _buildLinkedProfessionalsTab(
              context,
              _claimDetails!,
              _linkedClaimantProfiles,
            ),
            _CostTrackingTab(claim: _claimDetails!),
            // _buildFRFRScheduleTab(context, _claimDetails!),
            _buildAuditTrailTab(context, _claimDetails!),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewTab(BuildContext context, Claim details) {
    final theme = ShadTheme.of(context);
    final currentUser = PocketBaseService().currentUser;
    final isCoFunder = currentUser?.data['user_type'] == 'co_funder';

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isDesktop = constraints.maxWidth >= 1200;
          final isTablet =
              constraints.maxWidth >= 768 && constraints.maxWidth < 1200;

          if (isDesktop) {
            return _buildDesktopOverviewLayout(context, details, theme);
          } else if (isTablet) {
            return _buildTabletOverviewLayout(context, details, theme);
          } else {
            return _buildMobileOverviewLayout(context, details, theme);
          }
        },
      ),
    );
  }

  Widget _buildDesktopOverviewLayout(
    BuildContext context,
    Claim details,
    ShadThemeData theme,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column - Main details
        Expanded(
          flex: 2,
          child: Column(
            children: [
              _buildClaimHeaderCard(context, details, theme),
              const SizedBox(height: 20),
              _buildFinancialDetailsCard(context, details, theme),
            ],
          ),
        ),
        const SizedBox(width: 20),
        // Right column - Summary and status
        Expanded(
          flex: 1,
          child: Column(
            children: [
              _buildStatusCard(context, details, theme),
              const SizedBox(height: 20),
              _buildSummaryCard(context, details, theme),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTabletOverviewLayout(
    BuildContext context,
    Claim details,
    ShadThemeData theme,
  ) {
    return Column(
      children: [
        _buildClaimHeaderCard(context, details, theme),
        const SizedBox(height: 20),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildFinancialDetailsCard(context, details, theme),
            ),
            const SizedBox(width: 16),
            Expanded(child: _buildStatusCard(context, details, theme)),
          ],
        ),
        const SizedBox(height: 20),
        _buildSummaryCard(context, details, theme),
      ],
    );
  }

  Widget _buildMobileOverviewLayout(
    BuildContext context,
    Claim details,
    ShadThemeData theme,
  ) {
    return Column(
      children: [
        _buildClaimHeaderCard(context, details, theme),
        const SizedBox(height: 16),
        _buildStatusCard(context, details, theme),
        const SizedBox(height: 16),
        _buildFinancialDetailsCard(context, details, theme),
        const SizedBox(height: 16),
        _buildSummaryCard(context, details, theme),
      ],
    );
  }

  Widget _buildClaimHeaderCard(
    BuildContext context,
    Claim details,
    ShadThemeData theme,
  ) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    LucideIcons.briefcase,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        details.caseTitle,
                        style: theme.textTheme.h3.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Claim ID: ${details.id}',
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Start Date',
                    DateFormat.yMMMd().format(details.created.toLocal()),
                    LucideIcons.calendar,
                    theme,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Stage',
                    details.currentStatus ?? "N/A",
                    LucideIcons.flag,
                    theme,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Claim Type',
                    details.claimType ?? "N/A",
                    LucideIcons.tag,
                    theme,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Industry',
                    details.claimIndustry ?? "N/A",
                    LucideIcons.building,
                    theme,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(
    BuildContext context,
    Claim details,
    ShadThemeData theme,
  ) {
    Color statusColor = theme.colorScheme.primary;
    IconData statusIcon = LucideIcons.clock;

    // Determine status color and icon based on current status
    switch (details.currentStatus?.toLowerCase()) {
      case 'approved':
      case 'approved_for_funding':
        statusColor = Colors.green;
        statusIcon = LucideIcons.check;
        break;
      case 'funded':
        statusColor = Colors.blue;
        statusIcon = LucideIcons.dollarSign;
        break;
      case 'rejected':
        statusColor = Colors.red;
        statusIcon = LucideIcons.x;
        break;
      case 'under review':
        statusColor = Colors.orange;
        statusIcon = LucideIcons.eye;
        break;
      default:
        statusColor = theme.colorScheme.primary;
        statusIcon = LucideIcons.clock;
    }

    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(statusIcon, color: statusColor, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Stage',
                        style: theme.textTheme.h4.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Case progression status',
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: statusColor.withValues(alpha: 0.3)),
              ),
              child: Text(
                details.currentStatus ?? "N/A",
                style: theme.textTheme.small.copyWith(
                  color: statusColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialDetailsCard(
    BuildContext context,
    Claim details,
    ShadThemeData theme,
  ) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    LucideIcons.dollarSign,
                    color: Colors.green.shade600,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Financial Details',
                        style: theme.textTheme.h4.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Funding requirements and rates',
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Total Funding Required',
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '£${(details.requiredFundingAmount ?? details.totalFundingSecured ?? 0).toStringAsFixed(2)}',
                        style: theme.textTheme.h4.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'FRFR Schedule',
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        details.expectedFrfrSchedule ?? "N/A",
                        style: theme.textTheme.p.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    Claim details,
    ShadThemeData theme,
  ) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    LucideIcons.fileText,
                    color: Colors.blue.shade600,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Public Summary',
                        style: theme.textTheme.h4.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Case overview and details',
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              details.caseSummaryPublic ?? "No public summary available.",
              style: theme.textTheme.p.copyWith(height: 1.5),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    ShadThemeData theme,
  ) {
    return Row(
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.mutedForeground),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: theme.textTheme.p.copyWith(fontWeight: FontWeight.w600),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusUpdatesTab(BuildContext context, Claim details) {
    final theme = ShadTheme.of(context);
    final sortedUpdates = List<StatusUpdate>.from(details.statusUpdates)
      ..sort((a, b) => b.date.compareTo(a.date));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  LucideIcons.activity,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status Updates & Milestones',
                      style: theme.textTheme.h3.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Track case progress and important milestones',
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Add new update card
          ShadCard(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    children: [
                      Icon(
                        LucideIcons.plus,
                        color: theme.colorScheme.primary,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Add New Update/Milestone',
                          style: theme.textTheme.h4.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ShadTextarea(
                    controller: _newStatusUpdateController,
                    placeholder: const Text(
                      'Enter detailed description of the update or milestone achieved...',
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ShadButton(
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                LucideIcons.save,
                                size: 16,
                                color: theme.colorScheme.primaryForeground,
                              ),
                              const SizedBox(width: 8),
                              const Text('Log Update'),
                            ],
                          ),
                          onPressed: () {
                            _addStatusUpdate(_newStatusUpdateController.text);
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // History section header
          Row(
            children: [
              Icon(
                LucideIcons.history,
                color: theme.colorScheme.primary,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'Update History',
                style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${sortedUpdates.length} updates',
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Updates list
          if (sortedUpdates.isEmpty)
            ShadCard(
              child: Padding(
                padding: const EdgeInsets.all(40),
                child: Column(
                  children: [
                    Icon(
                      LucideIcons.clock,
                      size: 48,
                      color: theme.colorScheme.mutedForeground,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No updates yet',
                      style: theme.textTheme.h4.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Status updates and milestones will appear here',
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            ...sortedUpdates
                .map((update) => _buildStatusUpdateCard(update, theme))
                .toList(),
        ],
      ),
    );
  }

  Widget _buildStatusUpdateCard(StatusUpdate update, ShadThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: ShadCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      LucideIcons.messageSquare,
                      color: theme.colorScheme.primary,
                      size: 14,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          DateFormat.yMMMd().add_jm().format(
                            update.date.toLocal(),
                          ),
                          style: theme.textTheme.small.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'by ${update.updatedByName ?? (update.updatedByUserId != null ? 'User ID: ${update.updatedByUserId}' : 'Unknown User')}',
                          style: theme.textTheme.small.copyWith(
                            color: theme.colorScheme.mutedForeground,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                update.description,
                style: theme.textTheme.p.copyWith(height: 1.5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentRepositoryTab(
    BuildContext context,
    Claim details,
    List<ClaimantProfile> claimantProfiles,
  ) {
    final theme = ShadTheme.of(context);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Document Repository', style: theme.textTheme.h4),
              ShadButton(
                child: const Text('+ Add Document'),
                onPressed: () {
                  _showUploadNewDocumentModal(context, details);
                },
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (details.documentRepository.isEmpty)
            const Expanded(
              child: Center(child: Text('No documents uploaded yet.')),
            )
          else
            Expanded(
              child: ListView.builder(
                itemCount: details.documentRepository.length,
                itemBuilder: (context, index) {
                  final category = details.documentRepository[index];
                  final currentVersion = category.versions.firstWhere(
                    (v) => v.fileId == category.currentVersionFileId,
                    orElse:
                        () =>
                            category.versions.isNotEmpty
                                ? category.versions.last
                                : DocumentVersion(
                                  fileId: '',
                                  filename: 'N/A',
                                  uploadedAt: DateTime.now().toIso8601String(),
                                  uploadedBy: 'System',
                                  notes: 'No current version found',
                                ),
                  );

                  return Card(
                    margin: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            category.logicalName,
                            style: theme.textTheme.large.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          _buildDocumentDetailRow(
                            context,
                            'Current Filename:',
                            currentVersion.filename,
                            isLink: true,
                            onLinkTap:
                                () => _previewDocument(context, currentVersion),
                          ),
                          _buildDocumentDetailRow(
                            context,
                            'Last Updated:',
                            DateFormat.yMMMd().add_jm().format(
                              DateTime.parse(
                                currentVersion.uploadedAt,
                              ).toLocal(),
                            ),
                          ),
                          _buildDocumentDetailRowWithUserName(
                            context,
                            'Uploaded By:',
                            currentVersion.uploadedBy,
                          ),
                          ExpandableText(
                            label: 'Comment:',
                            text: currentVersion.notes ?? 'No comment.',
                          ),
                          const SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              ShadButton.outline(
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.download_outlined,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    const Text('Download'),
                                  ],
                                ),
                                onPressed:
                                    () => _downloadDocument(
                                      context,
                                      currentVersion,
                                    ),
                                size: ShadButtonSize.sm,
                              ),
                              const SizedBox(width: 8),
                              ShadButton.outline(
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.visibility_outlined,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    const Text('Preview'),
                                  ],
                                ),
                                onPressed:
                                    () => _previewDocument(
                                      context,
                                      currentVersion,
                                    ),
                                size: ShadButtonSize.sm,
                              ),
                              const SizedBox(width: 8),
                              ShadButton.outline(
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.upload_file_outlined,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    const Text('New Version'),
                                  ],
                                ),
                                onPressed:
                                    () => _showUploadNewVersionModal(
                                      context,
                                      details,
                                      category,
                                    ),
                                size: ShadButtonSize.sm,
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              const SizedBox(width: 8),
                              ShadButton.outline(
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.edit_note_outlined,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    const Text('Edit Comment'),
                                  ],
                                ),
                                onPressed:
                                    () => _showEditCommentModal(
                                      context,
                                      details,
                                      category,
                                      currentVersion,
                                    ),
                                size: ShadButtonSize.sm,
                              ),
                              const SizedBox(width: 8),
                              ShadButton.outline(
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.history_outlined,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    const Text('History'),
                                  ],
                                ),
                                onPressed:
                                    () => _showVersionHistoryModal(
                                      context,
                                      category,
                                    ),
                                size: ShadButtonSize.sm,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDocumentDetailRow(
    BuildContext context,
    String label,
    String value, {
    bool isLink = false,
    VoidCallback? onLinkTap,
  }) {
    final theme = ShadTheme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: theme.textTheme.muted.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          Expanded(
            child:
                isLink
                    ? InkWell(
                      onTap: onLinkTap,
                      child: Text(
                        value,
                        style: theme.textTheme.p.copyWith(
                          color: theme.colorScheme.primary,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    )
                    : Text(value, style: theme.textTheme.p),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentDetailRowWithUserName(
    BuildContext context,
    String label,
    String userId,
  ) {
    final theme = ShadTheme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: theme.textTheme.muted.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: FutureBuilder<String>(
              future: _resolveUserName(userId),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Text(
                    'Loading...',
                    style: theme.textTheme.p.copyWith(
                      color: theme.colorScheme.muted,
                    ),
                  );
                } else if (snapshot.hasError) {
                  return Text('User ($userId)', style: theme.textTheme.p);
                } else {
                  return Text(
                    snapshot.data ?? 'Unknown User',
                    style: theme.textTheme.p,
                  );
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showUploadNewDocumentModal(BuildContext context, Claim claimDetails) {
    final theme = ShadTheme.of(context);
    final formKey = GlobalKey<FormState>();
    String? newCategoryName;
    String? comment;
    PlatformFile? pickedFile;
    StateSetter? dialogSetState;

    showShadDialog(
      context: context,
      builder:
          (context) => ShadDialog.alert(
            title: const Text('+ Add Document'),
            description: const Text(
              'Create a new document category and upload the first version.',
            ),
            actions: [
              ShadButton.ghost(
                child: const Text('Cancel'),
                onPressed: () => Navigator.of(context).pop(),
              ),
              ShadButton(
                child: const Text('Upload Document'),
                onPressed: () async {
                  if (formKey.currentState!.validate() && pickedFile != null) {
                    formKey.currentState!.save();
                    Navigator.of(context).pop();
                    await _handleNewDocumentUpload(
                      context,
                      claimDetails,
                      newCategoryName!,
                      pickedFile!,
                      comment,
                    );
                  } else if (pickedFile == null) {
                    ShadToaster.of(context).show(
                      ShadToast.destructive(
                        title: const Text('File Error'),
                        description: const Text(
                          'Please select a file to upload.',
                        ),
                      ),
                    );
                  }
                },
              ),
            ],
            child: StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
                dialogSetState = setState;
                return Form(
                  key: formKey,
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.8,
                    constraints: const BoxConstraints(maxWidth: 500),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        ShadInputFormField(
                          id: 'categoryName',
                          label: const Text('Document Category Name'),
                          placeholder: const Text(
                            'e.g., Witness Statement - John Doe',
                          ),
                          onSaved: (value) => newCategoryName = value,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Category name is required.';
                            }
                            if (claimDetails.documentRepository.any(
                              (cat) =>
                                  cat.logicalName.toLowerCase() ==
                                  value.toLowerCase(),
                            )) {
                              return 'This category name already exists.';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        ShadInputFormField(
                          id: 'comment',
                          label: const Text('Comment/Notes (Optional)'),
                          placeholder: const Text('Initial version notes...'),
                          onSaved: (value) => comment = value,
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            ShadButton.outline(
                              child: const Text('Select File'),
                              onPressed: () async {
                                FilePickerResult? result =
                                    await FilePicker.platform.pickFiles();
                                if (result != null) {
                                  dialogSetState?.call(() {
                                    pickedFile = result.files.first;
                                  });
                                }
                              },
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                pickedFile?.name ?? 'No file selected',
                                style: theme.textTheme.muted,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        if (pickedFile == null)
                          Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Text(
                              'Please select a file.',
                              style: TextStyle(
                                color: theme.colorScheme.destructive,
                                fontSize: 12,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
    );
  }

  Future<void> _handleNewDocumentUpload(
    BuildContext context,
    Claim claimDetails,
    String categoryName,
    PlatformFile file,
    String? comment,
  ) async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      final pbClient = PocketBaseService().pb;
      final authModel = pbClient.authStore.model;
      String currentUserId;
      String currentUserName = 'Unknown User';

      if (authModel is RecordModel) {
        currentUserId = authModel.id;
        currentUserName =
            authModel.data['name'] as String? ?? 'User ($currentUserId)';
      } else if (authModel is User) {
        currentUserId = authModel.id;
        currentUserName = authModel.name ?? 'User ${authModel.id}';
      } else {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Auth Error'),
            description: const Text('User not authenticated.'),
          ),
        );
        if (mounted) setState(() => _isLoading = false);
        return;
      }

      final multipartFile = http.MultipartFile.fromBytes(
        'document_file',
        file.bytes!,
        filename: file.name,
      );

      // Upload file and create new document category using the new service
      await _claimDocumentsService.uploadFileAndCreateCategory(
        fundingApplicationId: claimDetails.id,
        logicalName: categoryName,
        file: multipartFile,
        uploadedBy: currentUserId,
        comment: comment,
      );

      ShadToaster.of(context).show(
        ShadToast(
          title: const Text('Success'),
          description: const Text(
            'Document uploaded and category created successfully!',
          ),
        ),
      );
      _fetchClaimDetails();
    } catch (e) {
      debugPrint('Error uploading new document: $e');
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Upload Error'),
          description: Text('Failed to upload document: ${e.toString()}'),
        ),
      );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _showUploadNewVersionModal(
    BuildContext context,
    Claim claimDetails,
    UploadedDocumentCategory category,
  ) {
    final theme = ShadTheme.of(context);
    String? comment;
    PlatformFile? pickedFile;
    StateSetter? dialogSetState;

    showShadDialog(
      context: context,
      builder:
          (context) => ShadDialog.alert(
            title: Text('Upload New Version for ${category.logicalName}'),
            child: StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
                dialogSetState = setState;
                return Container(
                  width: MediaQuery.of(context).size.width * 0.8,
                  constraints: const BoxConstraints(maxWidth: 500),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      ShadInput(
                        // id: 'comment_new_version', // Removed invalid 'id' parameter
                        placeholder: const Text(
                          'Version Comment/Notes (Optional)',
                        ),
                        onChanged: (value) => comment = value,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          ShadButton.outline(
                            child: const Text('Select File'),
                            onPressed: () async {
                              FilePickerResult? result =
                                  await FilePicker.platform.pickFiles();
                              if (result != null) {
                                dialogSetState?.call(() {
                                  pickedFile = result.files.first;
                                });
                              }
                            },
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              pickedFile?.name ?? 'No file selected',
                              style: theme.textTheme.muted,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      if (pickedFile == null)
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Text(
                            'Please select a file.',
                            style: TextStyle(
                              color: theme.colorScheme.destructive,
                              fontSize: 12,
                            ),
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
            actions: [
              ShadButton.ghost(
                child: const Text('Cancel'),
                onPressed: () => Navigator.of(context).pop(),
              ),
              ShadButton(
                child: const Text('Upload Version'),
                onPressed: () async {
                  if (pickedFile != null) {
                    Navigator.of(context).pop();
                    await _handleNewVersionUpload(
                      context,
                      claimDetails,
                      category,
                      pickedFile!,
                      comment,
                    );
                  } else {
                    ShadToaster.of(context).show(
                      ShadToast.destructive(
                        title: const Text('File Error'),
                        description: const Text(
                          'Please select a file to upload.',
                        ),
                      ),
                    );
                  }
                },
              ),
            ],
          ),
    );
  }

  Future<void> _handleNewVersionUpload(
    BuildContext context,
    Claim claimDetails,
    UploadedDocumentCategory category,
    PlatformFile file,
    String? comment,
  ) async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      final pbClient = PocketBaseService().pb;
      final authModel = pbClient.authStore.model;
      String currentUserId;
      String currentUserName = 'Unknown User';

      if (authModel is RecordModel) {
        currentUserId = authModel.id;
        currentUserName =
            authModel.data['name'] as String? ?? 'User ($currentUserId)';
      } else if (authModel is User) {
        currentUserId = authModel.id;
        currentUserName = authModel.name ?? 'User ${authModel.id}';
      } else {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Auth Error'),
            description: const Text('User not authenticated.'),
          ),
        );
        if (mounted) setState(() => _isLoading = false);
        return;
      }

      final multipartFile = http.MultipartFile.fromBytes(
        'document_file',
        file.bytes!,
        filename: file.name,
      );

      // Upload file and add new version using the new service
      await _claimDocumentsService.uploadFileAndAddVersion(
        fundingApplicationId: claimDetails.id,
        logicalName: category.logicalName,
        file: multipartFile,
        uploadedBy: currentUserId,
        comment: comment,
      );

      ShadToaster.of(context).show(
        ShadToast(
          title: const Text('Success'),
          description: const Text('New version uploaded successfully!'),
        ),
      );
      _fetchClaimDetails();
    } catch (e) {
      debugPrint('Error uploading new version: $e');
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Upload Error'),
          description: Text('Failed to upload new version: ${e.toString()}'),
        ),
      );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _showEditCommentModal(
    BuildContext context,
    Claim claimDetails,
    UploadedDocumentCategory category,
    DocumentVersion versionToEdit,
  ) {
    final formKey = GlobalKey<FormState>();
    String? updatedComment = versionToEdit.notes;

    showShadDialog(
      context: context,
      builder:
          (context) => ShadDialog.alert(
            title: Text('Edit Comment for ${versionToEdit.filename}'),
            child: Form(
              key: formKey,
              child: Container(
                width: MediaQuery.of(context).size.width * 0.8,
                constraints: const BoxConstraints(maxWidth: 500),
                child: ShadInputFormField(
                  id: 'edit_comment',
                  label: const Text('Comment/Notes'),
                  initialValue: updatedComment,
                  placeholder: const Text('Enter comment...'),
                  maxLines: 3,
                  onSaved: (value) => updatedComment = value,
                ),
              ),
            ),
            actions: [
              ShadButton.ghost(
                child: const Text('Cancel'),
                onPressed: () => Navigator.of(context).pop(),
              ),
              ShadButton(
                child: const Text('Save Comment'),
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    formKey.currentState!.save();
                    Navigator.of(context).pop();
                    await _handleEditComment(
                      context,
                      claimDetails,
                      category,
                      versionToEdit,
                      updatedComment,
                    );
                  }
                },
              ),
            ],
          ),
    );
  }

  Future<void> _handleEditComment(
    BuildContext context,
    Claim claimDetails,
    UploadedDocumentCategory category,
    DocumentVersion versionToEdit,
    String? newComment,
  ) async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      // Update the version comment in the new collection
      await _claimDocumentsService.updateVersionComment(
        fundingApplicationId: claimDetails.id,
        logicalName: category.logicalName,
        versionFileId: versionToEdit.fileId,
        newComment: newComment,
      );

      ShadToaster.of(context).show(
        ShadToast(
          title: const Text('Success'),
          description: const Text('Comment updated successfully.'),
        ),
      );
      _fetchClaimDetails();
    } catch (e) {
      debugPrint('Error updating comment: $e');
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Error'),
          description: Text('Failed to update comment: ${e.toString()}'),
        ),
      );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _showVersionHistoryModal(
    BuildContext context,
    UploadedDocumentCategory category,
  ) {
    final theme = ShadTheme.of(context);
    final sortedVersions = List<DocumentVersion>.from(category.versions)..sort(
      (a, b) =>
          DateTime.parse(b.uploadedAt).compareTo(DateTime.parse(a.uploadedAt)),
    );

    showShadDialog(
      context: context,
      builder:
          (context) => ShadDialog.alert(
            title: Text('Version History for ${category.logicalName}'),
            child: SizedBox(
              width: double.maxFinite,
              height: MediaQuery.of(context).size.height * 0.6,
              child:
                  sortedVersions.isEmpty
                      ? const Center(
                        child: Text('No version history available.'),
                      )
                      : ListView.builder(
                        shrinkWrap: true,
                        itemCount: sortedVersions.length,
                        itemBuilder: (context, index) {
                          final version = sortedVersions[index];
                          final isCurrent =
                              version.fileId == category.currentVersionFileId;
                          return ListTile(
                            title: Text(
                              version.filename,
                              style: TextStyle(
                                fontWeight:
                                    isCurrent
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                FutureBuilder<String>(
                                  future: _resolveUserName(version.uploadedBy),
                                  builder: (context, snapshot) {
                                    final userName =
                                        snapshot.data ?? version.uploadedBy;
                                    return Text(
                                      'Uploaded: ${DateFormat.yMMMd().add_jm().format(DateTime.parse(version.uploadedAt).toLocal())} by $userName',
                                    );
                                  },
                                ),
                                if (version.notes != null &&
                                    version.notes!.isNotEmpty)
                                  Text('Notes: ${version.notes}'),
                              ],
                            ),
                            trailing:
                                isCurrent
                                    ? Chip(
                                      label: const Text('Current'),
                                      backgroundColor: Colors.greenAccent
                                          .withOpacity(0.3),
                                    )
                                    : null,
                            onTap: () {
                              Navigator.of(context).pop();
                              _previewDocument(context, version);
                            },
                          );
                        },
                      ),
            ),
            actions: [
              ShadButton.ghost(
                child: const Text('Close'),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
    );
  }

  Future<void> _previewDocument(
    BuildContext context,
    DocumentVersion document,
  ) async {
    try {
      // Get the file URL using the ClaimDocumentsService
      final fileUrl = await _claimDocumentsService.getFileUrl(document.fileId);

      // Navigate to document preview screen instead of launching browser
      Navigator.of(context).push(
        MaterialPageRoute(
          builder:
              (context) => DocumentPreviewWidget(
                url: fileUrl,
                fileName: document.filename,
              ),
        ),
      );
    } catch (e) {
      if (context.mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Preview Error'),
            description: Text('Failed to preview document: ${e.toString()}'),
          ),
        );
      }
    }
  }

  Future<void> _downloadDocument(
    BuildContext context,
    DocumentVersion version,
  ) async {
    try {
      final url = await _claimDocumentsService.getFileUrl(version.fileId);

      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } else {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Download Error'),
            description: const Text('Could not initiate document download.'),
          ),
        );
      }
    } catch (e) {
      debugPrint("Error downloading document: $e");
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Download Error'),
          description: Text('Failed to download document: ${e.toString()}'),
        ),
      );
    }
  }

  Widget _buildLinkedProfessionalsTab(
    BuildContext context,
    Claim details,
    List<ClaimantProfile> claimantProfiles,
  ) {
    final theme = ShadTheme.of(context);
    final currentUser = PocketBaseService().currentUser;
    final isSolicitor = currentUser?.data['user_type'] == 'solicitor';

    if (!isSolicitor) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Professional details are only available to Solicitors.',
            style: theme.textTheme.muted,
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    // Combine all solicitor-related profiles
    final allSolicitors = [
      ...(details.assignedFirmMembersExpanded ?? []).map(
        (user) => {
          'name': user.displayName,
          'type': 'Firm Member',
          'details': user.email ?? 'N/A',
        },
      ),
      ...(details.claimAdminsExpanded ?? []).map(
        (profile) => {
          'name': profile.solicitorName,
          'type': 'Claim Admin',
          'details': profile.lawFirmName,
        },
      ),
      ...(details.associatedSolicitorsExpanded ?? []).map(
        (profile) => {
          'name': profile.solicitorName,
          'type': 'Associated Solicitor',
          'details': profile.lawFirmName,
        },
      ),
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Linked Professionals', style: theme.textTheme.h4),
          const SizedBox(height: 16),

          // Barristers
          Text('Barristers', style: theme.textTheme.large),
          const SizedBox(height: 8),
          if (details.linkedBarristersExpanded?.isEmpty ?? true)
            Text('No barristers linked.', style: theme.textTheme.muted)
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: details.linkedBarristersExpanded!.length,
              itemBuilder: (context, index) {
                final barrister =
                    details.linkedBarristersExpanded![index] as Barrister;
                debugPrint('Barrister object type: ${barrister.runtimeType}');
                debugPrint('Barrister object: $barrister');
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          barrister.name,
                          style: theme.textTheme.p.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Chambers: ${barrister.chambers}',
                          style: theme.textTheme.muted,
                        ),
                        // Add other barrister details as needed
                      ],
                    ),
                  ),
                );
              },
            ),

          const SizedBox(height: 20),

          // Expert Witnesses
          Text('Expert Witnesses', style: theme.textTheme.large),
          const SizedBox(height: 8),
          if (details.linkedExpertsExpanded?.isEmpty ?? true)
            Text('No expert witnesses linked.', style: theme.textTheme.muted)
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: details.linkedExpertsExpanded!.length,
              itemBuilder: (context, index) {
                final expert = details.linkedExpertsExpanded![index] as Expert;
                debugPrint('Expert object type: ${expert.runtimeType}');
                debugPrint('Expert object: $expert');
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          expert.name,
                          style: theme.textTheme.p.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Expertise: ${expert.specialty}',
                          style: theme.textTheme.muted,
                        ),
                        Text(
                          'Firm: ${expert.firmName}',
                          style: theme.textTheme.muted,
                        ),
                        // Add other expert details as needed
                      ],
                    ),
                  ),
                );
              },
            ),

          const SizedBox(height: 20),

          // Solicitors (Firm Members, Claim Admins, Associated Solicitors)
          Text('Solicitors & Firm Members', style: theme.textTheme.large),
          const SizedBox(height: 8),
          if (allSolicitors.isEmpty)
            Text(
              'No solicitors or firm members linked.',
              style: theme.textTheme.muted,
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: allSolicitors.length,
              itemBuilder: (context, index) {
                final solicitor = allSolicitors[index];
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${solicitor['name']}',
                          style: theme.textTheme.p.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Role: ${solicitor['type']}',
                          style: theme.textTheme.muted,
                        ),
                        Text(
                          'Details: ${solicitor['details']}',
                          style: theme.textTheme.muted,
                        ),
                        // Add other solicitor details as needed
                      ],
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildFRFRScheduleTab(BuildContext context, Claim details) {
    return const Center(child: Text('FRFR Schedule - To be implemented'));
  }

  Widget _buildAuditTrailTab(BuildContext context, Claim claimDetails) {
    final theme = ShadTheme.of(context);
    final auditLogAsyncValue = ref.watch(
      claimAuditLogProvider(claimDetails.id),
    );

    return auditLogAsyncValue.when(
      data: (auditLogs) {
        if (auditLogs.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('No audit trail activity found for this claim yet.'),
            ),
          );
        }
        return ListView.separated(
          padding: const EdgeInsets.all(16.0),
          itemCount: auditLogs.length,
          separatorBuilder: (context, index) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final log = auditLogs[index];
            String userPrefix;
            String userNameToDisplay = log.userName;

            switch (log.userRole.toLowerCase()) {
              case 'solicitor':
                userPrefix = 'Solicitor: ';
                break;
              case 'admin':
                userPrefix = 'Admin: ';
                break;
              case 'claimant':
                userPrefix = 'Claimant: ';
                break;
              case 'co_funder':
                userPrefix = 'Co-Funder: ';
                break;
              default:
                userPrefix =
                    (log.userRole.isNotEmpty
                        ? log.userRole[0].toUpperCase() +
                            log.userRole.substring(1)
                        : 'User') +
                    ': ';
            }

            IconData actionIcon;
            Color iconColor;
            switch (log.action.toLowerCase()) {
              case 'create':
              case 'created':
              case 'submit':
              case 'submitted':
                actionIcon = Icons.add_circle_outline;
                iconColor = theme.colorScheme.primary;
                break;
              case 'update':
              case 'updated':
              case 'edit':
              case 'edited':
                actionIcon = Icons.edit_outlined;
                iconColor = Colors.orange;
                break;
              case 'delete':
              case 'deleted':
              case 'remove':
              case 'removed':
                actionIcon = Icons.delete_outline;
                iconColor = theme.colorScheme.destructive;
                break;
              case 'view':
              case 'viewed':
              case 'access':
              case 'accessed':
                actionIcon = Icons.visibility_outlined;
                iconColor = Colors.blue;
                break;
              case 'login':
                actionIcon = Icons.login;
                iconColor = Colors.green;
                break;
              case 'logout':
                actionIcon = Icons.logout;
                iconColor = Colors.grey;
                break;
              case 'upload':
                actionIcon = Icons.upload_file_outlined;
                iconColor = theme.colorScheme.secondary;
                break;
              case 'download':
                actionIcon = Icons.download_outlined;
                iconColor = theme.colorScheme.secondary;
                break;
              case 'status_change':
                actionIcon = Icons.published_with_changes_outlined;
                iconColor = Colors.purple;
                break;
              case 'communication_sent':
              case 'message_sent':
                actionIcon = Icons.send_outlined;
                iconColor = Colors.cyan;
                break;
              default:
                actionIcon = Icons.info_outline;
                iconColor = Colors.grey;
            }

            String detailsString = 'No additional details.';
            if (log.details != null) {
              if (log.details is Map) {
                detailsString = (log.details as Map).entries
                    .map((e) => '${e.key}: ${e.value}')
                    .join('\n');
              } else {
                detailsString = log.details.toString();
              }
            }

            final formattedTimestamp = DateFormat(
              'MMM dd, yyyy - hh:mm a',
            ).format(log.timestamp.toLocal());

            return Card(
              margin: const EdgeInsets.symmetric(vertical: 8.0),
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(actionIcon, color: iconColor, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            log.action,
                            style: theme.textTheme.large.copyWith(
                              fontWeight: FontWeight.bold,
                              color: iconColor,
                            ),
                          ),
                        ),
                        Text(
                          formattedTimestamp,
                          style: theme.textTheme.small.copyWith(
                            color: theme.colorScheme.mutedForeground,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '$userPrefix$userNameToDisplay',
                      style: theme.textTheme.small.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (log.entityType.isNotEmpty &&
                        log.entityId.isNotEmpty &&
                        (log.entityType != 'claim' ||
                            log.entityId != claimDetails.id))
                      Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Text(
                          'Related Entity: ${log.entityType} (ID: ${log.entityId})',
                          style: theme.textTheme.small.copyWith(
                            color: theme.colorScheme.mutedForeground,
                          ),
                        ),
                      ),
                    const SizedBox(height: 8),
                    Text(
                      'Details:',
                      style: theme.textTheme.small.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.all(8.0),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.secondary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: theme.colorScheme.border),
                      ),
                      child: Text(
                        detailsString,
                        style: theme.textTheme.small,
                        maxLines: 5,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) {
        print(
          'Error in claimAuditLogProvider: $error\nStackTrace: $stackTrace',
        );
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Error fetching audit logs: $error',
              style: TextStyle(color: theme.colorScheme.destructive),
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
    );
  }
}

class ExpandableText extends StatefulWidget {
  final String text;
  final String label;
  final int trimLines;

  const ExpandableText({
    super.key,
    required this.text,
    required this.label,
    this.trimLines = 2,
  });

  @override
  ExpandableTextState createState() => ExpandableTextState();
}

class ExpandableTextState extends State<ExpandableText> {
  bool _readMore = true;

  void _toggleReadMore() {
    setState(() => _readMore = !_readMore);
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final span = TextSpan(text: widget.text, style: theme.textTheme.p);
    final tp = TextPainter(
      text: span,
      maxLines: widget.trimLines,
      textDirection: ui.TextDirection.ltr,
    );
    tp.layout(maxWidth: MediaQuery.of(context).size.width - 32);

    final didExceedMaxLines = tp.didExceedMaxLines;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          widget.label,
          style: theme.textTheme.muted.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 4),
        AnimatedSize(
          duration: const Duration(milliseconds: 200),
          child: ConstrainedBox(
            constraints:
                _readMore && didExceedMaxLines
                    ? BoxConstraints(
                      maxHeight: tp.preferredLineHeight * widget.trimLines,
                    )
                    : const BoxConstraints(),
            child: Text(
              widget.text,
              style: theme.textTheme.p,
              softWrap: true,
              overflow: TextOverflow.fade,
            ),
          ),
        ),
        if (didExceedMaxLines)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: InkWell(
              child: Text(
                _readMore ? 'Read more' : 'Show less',
                style: TextStyle(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              onTap: _toggleReadMore,
            ),
          )
        else
          const SizedBox.shrink(),
      ],
    );
  }
}

class _CostTrackingTab extends ConsumerStatefulWidget {
  final Claim claim;
  const _CostTrackingTab({required this.claim});

  @override
  ConsumerState<_CostTrackingTab> createState() => _CostTrackingTabState();
}

class _CostTrackingTabState extends ConsumerState<_CostTrackingTab> {
  final _formKey = GlobalKey<FormState>();
  CostType _selectedCostType = CostType.legalFees;
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  DateTime _selectedDate = DateTime.now();
  PlatformFile? _invoiceFile;
  bool _isSubmittingCost = false;

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _pickDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _pickInvoiceFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf', 'doc', 'docx', 'png', 'jpg', 'jpeg'],
    );
    if (result != null) {
      setState(() {
        _invoiceFile = result.files.first;
      });
    }
  }

  Future<void> _submitCostEntry() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    _formKey.currentState!.save();
    setState(() => _isSubmittingCost = true);

    final pbClient = ref.read(pocketBaseClientProvider);
    final authModel = pbClient.authStore.model;
    String currentUserId;
    String currentUserName = 'Unknown User';

    if (authModel is RecordModel) {
      currentUserId = authModel.id;
      currentUserName =
          authModel.data['name'] as String? ?? 'User ($currentUserId)';
    } else if (authModel is User) {
      currentUserId = authModel.id;
      currentUserName = authModel.name ?? 'User ${authModel.id}';
    } else {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Auth Error'),
          description: const Text('User not authenticated.'),
        ),
      );
      setState(() => _isSubmittingCost = false);
      return;
    }

    String? uploadedInvoiceFileId;
    String? uploadedInvoiceFilename;

    if (_invoiceFile != null) {
      try {
        final multipartFile = http.MultipartFile.fromBytes(
          'invoice_file_upload',
          _invoiceFile!.bytes!,
          filename: _invoiceFile!.name,
        );
        final fileRecord = await pbClient
            .collection('cost_invoices')
            .create(
              files: [multipartFile],
              body: {
                'claim_id': widget.claim.id,
                'uploader_id': currentUserId,
                'original_filename': _invoiceFile!.name,
              },
            );
        uploadedInvoiceFileId = fileRecord.id;
        uploadedInvoiceFilename = fileRecord.getStringValue(
          'invoice_file_upload',
        );
        if (uploadedInvoiceFilename.isEmpty)
          uploadedInvoiceFilename = _invoiceFile!.name;
      } catch (e) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Upload Error'),
            description: Text('Failed to upload invoice: $e'),
          ),
        );
        setState(() => _isSubmittingCost = false);
        return;
      }
    }

    final newCostEntry = CostTrackingDetails(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      costType: _selectedCostType,
      description: _descriptionController.text,
      amount: double.tryParse(_amountController.text) ?? 0.0,
      dateIncurred: _selectedDate,
      invoiceFileId: uploadedInvoiceFileId,
      invoiceFilename: uploadedInvoiceFilename,
      loggedByUserId: currentUserId,
      loggedByName: currentUserName,
      loggedAt: DateTime.now(),
    );

    try {
      final currentCosts = List<CostTrackingDetails>.from(
        widget.claim.costTracking ?? [],
      );
      currentCosts.add(newCostEntry);

      await pbClient
          .collection('funding_applications')
          .update(
            widget.claim.id,
            body: {
              'cost_tracking': currentCosts.map((c) => c.toJson()).toList(),
              'updated': DateTime.now().toIso8601String(),
            },
          );
      ShadToaster.of(context).show(
        ShadToast(
          title: const Text('Success'),
          description: const Text('Cost entry added successfully.'),
        ),
      );
      _formKey.currentState?.reset();
      _descriptionController.clear();
      _amountController.clear();
      setState(() {
        _invoiceFile = null;
        _selectedDate = DateTime.now();
        _selectedCostType = CostType.legalFees;
      });
    } catch (e) {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Error'),
          description: Text('Failed to add cost entry: $e'),
        ),
      );
    } finally {
      if (mounted) setState(() => _isSubmittingCost = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final costs = widget.claim.costTracking ?? [];
    costs.sort((a, b) => b.dateIncurred.compareTo(a.dateIncurred));
    double totalCosts = costs.fold(0, (sum, item) => sum + item.amount);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Cost Tracking', style: theme.textTheme.h4),
          const SizedBox(height: 8),
          Text(
            'Total Costs Logged: £${totalCosts.toStringAsFixed(2)}',
            style: theme.textTheme.large,
          ),
          const SizedBox(height: 16),
          ShadCard(
            title: Text('Log New Cost Entry', style: theme.textTheme.large),
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  DropdownButtonFormField<CostType>(
                    value: _selectedCostType,
                    decoration: const InputDecoration(labelText: 'Cost Type'),
                    items:
                        CostType.values.map((CostType type) {
                          return DropdownMenuItem<CostType>(
                            value: type,
                            child: Text(type.displayName),
                          );
                        }).toList(),
                    onChanged: (CostType? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedCostType = newValue;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 12),
                  ShadInputFormField(
                    id: 'cost_description',
                    controller: _descriptionController,
                    label: const Text('Description'),
                    placeholder: const Text(
                      'e.g., Barrister fees for hearing prep',
                    ),
                    validator:
                        (value) =>
                            value == null || value.isEmpty
                                ? 'Description is required'
                                : null,
                  ),
                  const SizedBox(height: 12),
                  ShadInputFormField(
                    id: 'cost_amount',
                    controller: _amountController,
                    label: const Text('Amount (£)'),
                    placeholder: const Text('e.g., 1500.00'),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                        RegExp(r'^\d+\.?\d{0,2}'),
                      ),
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty)
                        return 'Amount is required';
                      if (double.tryParse(value) == null)
                        return 'Invalid amount';
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Date Incurred: ${DateFormat.yMMMd().format(_selectedDate)}',
                        ),
                      ),
                      ShadButton.outline(
                        child: const Text('Select Date'),
                        onPressed: () => _pickDate(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      ShadButton.outline(
                        child: const Text('Attach Invoice (Optional)'),
                        onPressed: _pickInvoiceFile,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _invoiceFile?.name ?? 'No file selected',
                          style: theme.textTheme.muted,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  ShadButton(
                    child:
                        _isSubmittingCost
                            ? const LoadingSpinnerWidget(size: 20)
                            : const Text('Log Cost'),
                    onPressed: _isSubmittingCost ? null : _submitCostEntry,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          Text('Logged Costs', style: theme.textTheme.large),
          const SizedBox(height: 10),
          if (costs.isEmpty)
            const Center(child: Text('No costs logged yet.'))
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: costs.length,
              itemBuilder: (context, index) {
                final cost = costs[index];
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 6.0),
                  child: ListTile(
                    title: Text(
                      cost.description,
                      style: theme.textTheme.p.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Type: ${cost.costType.displayName}'),
                        Text(
                          'Date: ${DateFormat.yMMMd().format(cost.dateIncurred)}',
                        ),
                        Text(
                          'Logged by: ${cost.loggedByName} on ${DateFormat.yMMMd().format(cost.loggedAt.toLocal())}',
                        ),
                        if (cost.invoiceFilename != null)
                          InkWell(
                            onTap: () async {
                              if (cost.invoiceFileId != null) {
                                final pbClient = ref.read(
                                  pocketBaseClientProvider,
                                );
                                try {
                                  final record = await pbClient
                                      .collection('cost_invoices')
                                      .getOne(cost.invoiceFileId!);
                                  final filename = record.getStringValue(
                                    'invoice_file_upload',
                                  );
                                  final url =
                                      pbClient
                                          .getFileUrl(record, filename)
                                          .toString();

                                  // Navigate to document preview screen instead of launching browser
                                  if (context.mounted) {
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder:
                                            (context) => DocumentPreviewWidget(
                                              url: url,
                                              fileName: filename,
                                            ),
                                      ),
                                    );
                                  }
                                } catch (e) {
                                  LoggerService.error(
                                    'Error opening invoice',
                                    e,
                                  );
                                  if (context.mounted) {
                                    ShadToaster.of(context).show(
                                      ShadToast.destructive(
                                        title: const Text('Error'),
                                        description: Text(
                                          'Error opening invoice: $e',
                                        ),
                                      ),
                                    );
                                  }
                                }
                              }
                            },
                            child: Text(
                              'Invoice: ${cost.invoiceFilename}',
                              style: TextStyle(
                                color: theme.colorScheme.primary,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                      ],
                    ),
                    trailing: Text(
                      '£${cost.amount.toStringAsFixed(2)}',
                      style: theme.textTheme.p.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }
}

class _CommunicationTabForClaim extends ConsumerStatefulWidget {
  final String claimId;
  const _CommunicationTabForClaim({required this.claimId, super.key});

  @override
  ConsumerState<_CommunicationTabForClaim> createState() =>
      __CommunicationTabForClaimState();
}

class __CommunicationTabForClaimState
    extends ConsumerState<_CommunicationTabForClaim> {
  final TextEditingController _messageController = TextEditingController();
  bool _isSending = false;

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  Future<void> _sendMessage() async {
    if (_messageController.text.isEmpty) return;

    setState(() {
      _isSending = true;
    });

    final pbService = ref.read(pocketBaseClientProvider);
    final authModel = pbService.authStore.model;
    String currentUserId;

    if (authModel is RecordModel) {
      currentUserId = authModel.id;
    } else if (authModel is User) {
      currentUserId = authModel.id;
    } else {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Auth Error'),
          description: const Text('User not authenticated.'),
        ),
      );
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
      return;
    }

    try {
      await ref
          .read(claimCommunicationProvider(widget.claimId).notifier)
          .sendMessage(
            messageContent: _messageController.text,
            senderId: currentUserId,
          );
      _messageController.clear();
      ShadToaster.of(context).show(
        ShadToast(
          title: const Text('Success'),
          description: const Text('Message sent!'),
        ),
      );
    } catch (e) {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Error'),
          description: Text('Failed to send message: $e'),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final communicationState = ref.watch(
      claimCommunicationProvider(widget.claimId),
    );

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Expanded(
            child: Builder(
              builder: (context) {
                if (communicationState.isLoading &&
                    communicationState.messages.isEmpty) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (communicationState.error != null &&
                    communicationState.messages.isEmpty) {
                  print(
                    'Error in claimCommunicationProvider UI: ${communicationState.error}',
                  );
                  return Center(
                    child: Text(
                      'Error loading messages: ${communicationState.error}',
                    ),
                  );
                }
                if (communicationState.messages.isEmpty) {
                  return const Center(child: Text('No messages yet.'));
                }

                final sortedMessages = List<ClaimMessageModel>.from(
                  communicationState.messages,
                )..sort((a, b) => a.created.compareTo(b.created));

                return ListView.builder(
                  itemCount: sortedMessages.length,
                  itemBuilder: (context, index) {
                    final message = sortedMessages[index];
                    final currentAuthModel =
                        ref.read(pocketBaseClientProvider).authStore.model;
                    bool isCurrentUser = false;
                    if (currentAuthModel is RecordModel) {
                      isCurrentUser = message.senderId == currentAuthModel.id;
                    } else if (currentAuthModel is User) {
                      isCurrentUser = message.senderId == currentAuthModel.id;
                    }

                    final senderDisplayName =
                        message.senderName ??
                        'User ${message.senderId.substring(0, 5)}';

                    return Align(
                      alignment:
                          isCurrentUser
                              ? Alignment.centerRight
                              : Alignment.centerLeft,
                      child: Container(
                        margin: const EdgeInsets.symmetric(
                          vertical: 4.0,
                          horizontal: 8.0,
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 10.0,
                          horizontal: 14.0,
                        ),
                        decoration: BoxDecoration(
                          color:
                              isCurrentUser
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.secondary,
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                        child: Column(
                          crossAxisAlignment:
                              isCurrentUser
                                  ? CrossAxisAlignment.end
                                  : CrossAxisAlignment.start,
                          children: [
                            Text(
                              senderDisplayName,
                              style: theme.textTheme.small.copyWith(
                                fontWeight: FontWeight.bold,
                                color:
                                    isCurrentUser
                                        ? theme.colorScheme.primaryForeground
                                        : theme.colorScheme.secondaryForeground,
                              ),
                            ),
                            const SizedBox(height: 4.0),
                            Text(
                              message.messageContent,
                              style: theme.textTheme.p.copyWith(
                                color:
                                    isCurrentUser
                                        ? theme.colorScheme.primaryForeground
                                        : theme.colorScheme.secondaryForeground,
                              ),
                            ),
                            const SizedBox(height: 4.0),
                            Text(
                              DateFormat(
                                'MMM dd, hh:mm a',
                              ).format(message.created.toLocal()),
                              style: theme.textTheme.small.copyWith(
                                fontSize: 10,
                                color: (isCurrentUser
                                        ? theme.colorScheme.primaryForeground
                                        : theme.colorScheme.secondaryForeground)
                                    .withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ShadInput(
                  controller: _messageController,
                  placeholder: const Text('Type your message...'),
                  enabled: !_isSending,
                ),
              ),
              const SizedBox(width: 8),
              ShadButton(
                child:
                    _isSending
                        ? const LoadingSpinnerWidget(size: 20)
                        : const Text('Send'),
                onPressed: _isSending ? null : _sendMessage,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
