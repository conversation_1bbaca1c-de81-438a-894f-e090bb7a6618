import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart'; // Assuming PocketBaseService is here
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/solicitor_dashboard_page.dart'; // Placeholder for dashboard

class SolicitorLoginPage extends StatefulWidget {
  const SolicitorLoginPage({super.key});

  static const String routeName = '/solicitor-login';

  @override
  State<SolicitorLoginPage> createState() => _SolicitorLoginPageState();
}

class _SolicitorLoginPageState extends State<SolicitorLoginPage> {
  final _formKey = GlobalKey<ShadFormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;

  // Instance of PocketBaseService to interact with PocketBase
  final PocketBaseService _pocketBaseService = PocketBaseService();

  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        final email = _emailController.text.trim();
        final password = _passwordController.text.trim();

        // Authenticate user
        final authData = await _pocketBaseService.client
            .collection('users')
            .authWithPassword(email, password);

        if (authData.record != null) {
          // Fetch the user record to get the solicitor_profile relation
          final userRecord = await _pocketBaseService.client
              .collection('users')
              .getOne(authData.record!.id, expand: 'solicitor_profile');

          final List<dynamic>? solicitorProfileRecords =
              userRecord.expand['solicitor_profile'];
          final solicitorProfileId =
              solicitorProfileRecords?.isNotEmpty == true
                  ? solicitorProfileRecords!.first.id
                  : null;

          if (solicitorProfileId != null) {
            // It's often better to use the expanded record if available, rather than fetching again.
            // However, if 'solicitor_profiles' collection needs specific fields not in expand, fetching is okay.
            // For this example, let's assume the expanded data is sufficient or refetch if necessary.
            // final solicitorProfileRecord = await _pocketBaseService.client.collection('solicitor_profiles').getOne(solicitorProfileId);
            // final puStatus = solicitorProfileRecord.getStringValue('pu_status');

            // Using the expanded record directly:
            final puStatus = solicitorProfileRecords!.first.getStringValue(
              'pu_status',
            );

            if (puStatus == 'approved') {
              if (mounted) {
                Navigator.of(
                  context,
                ).pushReplacementNamed(SolicitorDashboardPage.routeName);
              }
            } else {
              String message;
              switch (puStatus) {
                case 'pending':
                  message = 'Your Permitted User application is under review.';
                  break;
                case 'rejected':
                  message =
                      'Your Permitted User application has been rejected. Please contact support.';
                  break;
                case 'none':
                default:
                  message =
                      'You do not have Permitted User status. Please apply or contact support.';
                  break;
              }
              if (mounted) {
                ShadToaster.of(context).show(
                  ShadToast(
                    title: const Text('Access Denied'),
                    description: Text(message),
                    duration: const Duration(seconds: 5),
                  ),
                );
              }
            }
          } else {
            // Handle case where solicitor_profile relation is missing
            if (mounted) {
              ShadToaster.of(context).show(
                ShadToast(
                  title: const Text('Login Error'),
                  description: const Text(
                    'Solicitor profile not found. Please contact support.',
                  ),
                  duration: const Duration(seconds: 5),
                ),
              );
            }
            _errorMessage =
                'Solicitor profile not found. Please contact support.';
          }
        } else {
          if (mounted) {
            ShadToaster.of(context).show(
              ShadToast(
                title: const Text('Login Failed'),
                description: const Text('Invalid email or password.'),
                duration: const Duration(seconds: 5),
              ),
            );
          }
          _errorMessage = 'Invalid email or password.';
        }
      } catch (e) {
        if (mounted) {
          ShadToaster.of(context).show(
            ShadToast(
              title: const Text('Login Error'),
              description: Text('An error occurred: ${e.toString()}'),
              duration: const Duration(seconds: 5),
            ),
          );
        }
        _errorMessage = 'An error occurred: ${e.toString()}';
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(title: const Text('Solicitor Login'), centerTitle: true),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 400),
            child: ShadCard(
              title: Text('Welcome Back', style: theme.textTheme.h4),
              description: Text(
                'Enter your credentials to access your portal.',
                style: theme.textTheme.muted,
              ),
              child: ShadForm(
                // Changed 'content' to 'child'
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: 16),
                    ShadInputFormField(
                      id: 'email',
                      label: const Text('Email'),
                      controller: _emailController,
                      placeholder: const Text('Enter your email'),
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Email is required.';
                        }
                        if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                          return 'Enter a valid email address.';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    ShadInputFormField(
                      id: 'password',
                      label: const Text('Password'),
                      controller: _passwordController,
                      placeholder: const Text('Enter your password'),
                      obscureText: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Password is required.';
                        }
                        return null;
                      },
                    ),
                    if (_errorMessage != null) ...[
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: theme.textTheme.p.copyWith(
                          color: theme.colorScheme.destructive,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                    const SizedBox(height: 24),
                    ShadButton(
                      onPressed: _isLoading ? null : _login,
                      child:
                          _isLoading
                              ? Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Text('Logging in...'),
                                ],
                              )
                              : const Text('Login'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
