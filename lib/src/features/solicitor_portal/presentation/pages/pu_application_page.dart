import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
// TODO: Import AppTheme if needed for specific styling not covered by Shadcn.
// import 'package:three_pay_group_litigation_platform/src/core/theme/app_theme.dart';

class PUApplicationPage extends StatefulWidget {
  const PUApplicationPage({super.key});

  static const String routeName = '/pu-application';

  @override
  State<PUApplicationPage> createState() => _PUApplicationPageState();
}

class _PUApplicationPageState extends State<PUApplicationPage> {
  final _formKey = GlobalKey<FormState>();
  String? _selectedDocumentName;

  // TODO: Add TextEditingControllers for each field
  final _lawFirmNameController = TextEditingController();
  final _solicitorNameController = TextEditingController();
  final _positionController = TextEditingController();
  final _contactNumberController = TextEditingController();
  final _sraNumberController = TextEditingController();
  final _firmAddressController = TextEditingController();
  final _firmRegistrationNumberController = TextEditingController();

  @override
  void dispose() {
    _lawFirmNameController.dispose();
    _solicitorNameController.dispose();
    _positionController.dispose();
    _contactNumberController.dispose();
    _sraNumberController.dispose();
    _firmAddressController.dispose();
    _firmRegistrationNumberController.dispose();
    super.dispose();
  }

  void _handleDocumentUpload() {
    // Simulate file picking
    setState(() {
      _selectedDocumentName = 'mock_legal_practitioner_certificate.pdf';
    });
    ShadToaster.of(context).show(
      ShadToast(
        title: const Text('File Selected'),
        description: Text(_selectedDocumentName!),
      ),
    );
  }

  void _submitApplication() {
    if (_formKey.currentState!.validate()) {
      // Process data (mock for now)
      ShadToaster.of(context).show(
        // TODO: Style this as a success toast if a direct constructor is not available
        const ShadToast(
          title: Text('Application Submitted'),
          description: Text(
            'Your PU status application has been submitted for review.',
          ),
        ),
      );
      // Potentially navigate away or show success message
      // Navigator.of(context).pop();
    } else {
      ShadToaster.of(context).show(
        const ShadToast(
          // Assuming .destructive is available, if not, will use default
          title: Text('Validation Error'),
          description: Text('Please correct the errors in the form.'),
        ),
      );
    }
  }

  void _saveDraft() {
    // Mock save draft functionality
    ShadToaster.of(context).show(
      ShadToast(
        title: const Text('Draft Saved'),
        description: Text('Your application draft has been saved.'),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('PU Status Application'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text('Firm Information', style: theme.textTheme.h4),
              const SizedBox(height: 16),
              _buildTextField(
                controller: _lawFirmNameController,
                labelText: 'Law Firm Name',
                hintText: 'Enter your law firm name',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter law firm name';
                  }
                  return null;
                },
              ),
              _buildTextField(
                controller: _solicitorNameController,
                labelText: 'Solicitor Name',
                hintText: 'Enter solicitor name',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter solicitor name';
                  }
                  return null;
                },
              ),
              _buildTextField(
                controller: _positionController,
                labelText: 'Position',
                hintText: 'Enter your position',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your position';
                  }
                  return null;
                },
              ),
              _buildTextField(
                controller: _contactNumberController,
                labelText: 'Contact Number',
                hintText: 'Enter contact number',
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter contact number';
                  }
                  // Add more specific phone validation if needed
                  return null;
                },
              ),
              _buildTextField(
                controller: _sraNumberController,
                labelText: 'Firm\'s SRA Number',
                hintText: 'Enter SRA number',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter SRA number';
                  }
                  return null;
                },
              ),
              _buildTextField(
                controller: _firmAddressController,
                labelText: 'Firm\'s Address',
                hintText: 'Enter firm address',
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter firm address';
                  }
                  return null;
                },
              ),
              _buildTextField(
                controller: _firmRegistrationNumberController,
                labelText: 'Firm\'s Registration Number',
                hintText: 'Enter firm registration number',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter firm registration number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              Text('Document Uploads', style: theme.textTheme.h4),
              const SizedBox(height: 16),
              ShadButton.outline(
                onPressed: _handleDocumentUpload,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(LucideIcons.upload, size: 16),
                    const SizedBox(width: 8),
                    const Text('Upload Legal Practitioner\'s Certificate'),
                  ],
                ),
              ),
              if (_selectedDocumentName != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    'Selected: $_selectedDocumentName',
                    style: theme.textTheme.muted,
                  ),
                ),
              // TODO: Add more document upload fields if necessary
              const SizedBox(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ShadButton.outline(
                    onPressed: _saveDraft,
                    child: const Text('Save Draft'),
                  ),
                  const SizedBox(width: 16),
                  ShadButton(
                    onPressed: _submitApplication,
                    child: const Text('Submit Application'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    TextInputType? keyboardType,
    int? maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: ShadInputFormField(
        controller: controller,
        label: Text(labelText),
        placeholder: Text(hintText),
        keyboardType: keyboardType,
        maxLines: maxLines,
        validator: validator,
      ),
    );
  }
}
