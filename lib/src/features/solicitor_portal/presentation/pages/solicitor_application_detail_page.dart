// lib/src/features/solicitor_portal/presentation/pages/solicitor_application_detail_page.dart
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/loading_spinner_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/application/providers/application_communication_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/funding_application_form_page.dart';

class SolicitorApplicationDetailPage extends StatefulWidget {
  static const String routeName = '/solicitor/application-detail';
  final String applicationId;

  const SolicitorApplicationDetailPage({
    super.key,
    required this.applicationId,
  });

  @override
  State<SolicitorApplicationDetailPage> createState() =>
      _SolicitorApplicationDetailPageState();
}

class _SolicitorApplicationDetailPageState
    extends State<SolicitorApplicationDetailPage>
    with SingleTickerProviderStateMixin {
  final PocketBaseService _pocketBaseService = PocketBaseService();
  FundingApplicationData? _applicationData;
  bool _isLoading = true;
  String? _error;
  late TabController _tabController;

  final List<String> _tabValues = [
    'overview',
    'status_history',
    'documents',
    'communication',
    'linked_professionals',
    'audit_trail',
  ];

  String _currentShadTabValue = 'overview';

  List<Map<String, dynamic>> _statusHistory = [];

  @override
  void initState() {
    super.initState();
    _currentShadTabValue = _tabValues.first;
    _tabController = TabController(length: _tabValues.length, vsync: this);

    _tabController.addListener(() {
      if (_tabController.indexIsChanging ||
          _tabController.index != _tabValues.indexOf(_currentShadTabValue)) {
        setState(() {
          _currentShadTabValue = _tabValues[_tabController.index];
        });
      }
    });
    _fetchApplicationDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _fetchApplicationDetails() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });
    try {
      final record = await _pocketBaseService.getOne(
        collectionName: 'funding_applications',
        recordId: widget.applicationId,
      );

      _applicationData = FundingApplicationData.fromJson(
        record.id,
        record.data,
      );

      if (_applicationData?.applicationStatus != 'draft' &&
          _applicationData?.submissionDate != null) {
        _statusHistory = [
          {
            'date': _applicationData!.submissionDate!,
            'old_status': 'Draft',
            'new_status': _applicationData!.applicationStatus,
            'updated_by': 'System/Solicitor',
            'review_notes': 'Application submitted by solicitor.',
          },
        ];
      }
      if (record.data['review_notes'] != null &&
          record.data['review_notes'].isNotEmpty) {
        _statusHistory.add({
          'date': record.updated,
          'old_status': 'Previous Status',
          'new_status': _applicationData!.applicationStatus,
          'updated_by': 'Funder',
          'review_notes': record.data['review_notes'],
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to load application details: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildOverviewTab() {
    if (_applicationData == null) return const Text('No data available.');
    final app = _applicationData!;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Key Dates', style: ShadTheme.of(context).textTheme.h4),
          const SizedBox(height: 8),
          Text(
            'Submission Date: ${app.submissionDate != null ? DateFormat.yMd().add_jm().format(DateTime.parse(app.submissionDate!).toLocal()) : 'N/A'}',
          ),
          if (app.applicationStatus == 'Approved' ||
              app.applicationStatus == 'Rejected') ...[
            Text(
              'Decision Date: ${app.decisionDate != null ? DateFormat.yMd().add_jm().format(DateTime.parse(app.decisionDate!).toLocal()) : 'N/A'}',
            ),
          ],
          const SizedBox(height: 16),
          Text('Summary', style: ShadTheme.of(context).textTheme.h4),
          const SizedBox(height: 8),
          Text('Claim Title: ${app.claimTitle}'),
          Text('Application ID: ${app.id}'),
          Text('Current Status: ${app.applicationStatus}'),
          const SizedBox(height: 16),
          Text('Solicitor Firm: Placeholder Company Name'),
          Text('Lead Solicitor: Placeholder Solicitor Name'),

          if (app.applicationStatus == 'Approved' ||
              app.applicationStatus == 'Rejected') ...[
            const SizedBox(height: 24),
            Text(
              'Resolution Details',
              style: ShadTheme.of(context).textTheme.h4,
            ),
            const SizedBox(height: 8),
            Text(
              'Final Status: ${app.applicationStatus}',
              style: ShadTheme.of(
                context,
              ).textTheme.p.copyWith(fontWeight: FontWeight.bold),
            ),
            if (app.reviewNotes != null && app.reviewNotes!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  'Review Notes: ${app.reviewNotes}',
                  style: ShadTheme.of(context).textTheme.muted,
                ),
              ),
            if (app.applicationStatus == 'Approved') ...[
              const SizedBox(height: 16),
              ShadButton(
                child: const Text(
                  'View Claim Details (Pending Claim Creation)',
                ),
                onPressed: null,
              ),
            ],
          ],
          if (app.applicationStatus == 'Requires Info') ...[
            const SizedBox(height: 24),
            Text(
              'Action Required',
              style: ShadTheme.of(context).textTheme.h4.copyWith(
                color: ShadTheme.of(context).colorScheme.destructive,
              ),
            ),
            const SizedBox(height: 8),
            if (app.reviewNotes != null && app.reviewNotes!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: ShadAlert(
                  icon: Icon(
                    Icons.warning_amber_rounded,
                    color: ShadTheme.of(context).colorScheme.destructive,
                  ),
                  title: Text(
                    'Review Feedback',
                    style: ShadTheme.of(context).textTheme.large,
                  ),
                  description: Text(app.reviewNotes!),
                ),
              ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                ShadButton.outline(
                  child: const Text('Edit Application'),
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder:
                            (context) => FundingApplicationFormPage(
                              existingApplicationData: _applicationData,
                              initialStep: 0,
                            ),
                      ),
                    );
                  },
                ),
                const SizedBox(width: 8),
                ShadButton.outline(
                  child: const Text('Upload Supplementary Documents'),
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder:
                            (context) => FundingApplicationFormPage(
                              existingApplicationData: _applicationData,
                              initialStep: 1,
                            ),
                      ),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            ShadButton(
              child: const Text('Resubmit Application with Updates'),
              onPressed: _resubmitApplication,
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _resubmitApplication() async {
    if (_applicationData == null || _applicationData!.id == null) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final Map<String, dynamic> updatedData = {
        'application_status': 'submitted',
      };

      await _pocketBaseService.updateRecord(
        collectionName: 'funding_applications',
        recordId: _applicationData!.id!,
        data: updatedData,
      );

      if (mounted) {
        ShadToaster.of(context).show(
          const ShadToast(
            title: Text('Application Resubmitted'),
            description: Text(
              'Your application has been successfully resubmitted.',
            ),
          ),
        );
        _fetchApplicationDetails();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to resubmit application: ${e.toString()}';
        });
        ShadToaster.of(context).show(
          ShadToast(
            title: Text(
              'Error Resubmitting',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
            description: Text(_error!),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildStatusHistoryTab() {
    if (_statusHistory.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Text('No status history available yet.'),
      );
    }
    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: _statusHistory.length,
      itemBuilder: (context, index) {
        final entry = _statusHistory[index];
        return ShadCard(
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Date: ${DateFormat.yMd().add_jm().format(DateTime.parse(entry['date']).toLocal())}',
                  style: ShadTheme.of(context).textTheme.small,
                ),
                const SizedBox(height: 4),
                Text(
                  'Status Change: ${entry['old_status'] ?? 'Initial'} -> ${entry['new_status']}',
                  style: ShadTheme.of(
                    context,
                  ).textTheme.p.copyWith(fontWeight: FontWeight.bold),
                ),
                Text('Updated By: ${entry['updated_by']}'),
                if (entry['review_notes'] != null &&
                    entry['review_notes'].isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(
                      'Notes: ${entry['review_notes']}',
                      style: ShadTheme.of(context).textTheme.muted,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPlaceholderTab(String title) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          '$title tab content will be implemented in a future task.',
          style: ShadTheme.of(context).textTheme.p,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isLoading || _applicationData == null
              ? 'Application Details'
              : 'Details: ${_applicationData!.claimTitle ?? "N/A"}',
        ),
      ),
      body:
          _isLoading
              ? const LoadingSpinnerWidget()
              : _error != null
              ? Center(
                child: Text(
                  _error!,
                  style: TextStyle(color: Theme.of(context).colorScheme.error),
                ),
              )
              : _applicationData == null
              ? const Center(child: Text('Application not found.'))
              : Column(
                children: [
                  ShadTabs<String>(
                    value: _currentShadTabValue,
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _currentShadTabValue = value;
                        });
                        final tabIndex = _tabValues.indexOf(value);
                        if (tabIndex != -1 &&
                            _tabController.index != tabIndex) {
                          _tabController.animateTo(tabIndex);
                        }
                      }
                    },
                    tabs:
                        _tabValues.map((value) {
                          String title = '';
                          switch (value) {
                            case 'overview':
                              title = 'Overview';
                              break;
                            case 'status_history':
                              title = 'Status History & Notes';
                              break;
                            case 'documents':
                              title = 'Documents';
                              break;
                            case 'communication':
                              title = 'Communication';
                              break;
                            case 'linked_professionals':
                              title = 'Linked Professionals';
                              break;
                            case 'audit_trail':
                              title = 'Audit Trail';
                              break;
                          }
                          return ShadTab(value: value, child: Text(title));
                        }).toList(),
                  ),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildOverviewTab(),
                        _buildStatusHistoryTab(),
                        _buildPlaceholderTab('Documents'),
                        _CommunicationTabWidget(
                          applicationId: widget.applicationId,
                        ), // Use the new tab
                        _buildPlaceholderTab('Linked Professionals'),
                        _AuditTrailTabWidget(
                          applicationId: widget.applicationId,
                        ), // Replaced placeholder
                      ],
                    ),
                  ),
                ],
              ),
    );
  }

  // Placeholder for AuditTrailTabWidget
  Widget _AuditTrailTabWidget({required String applicationId}) {
    // TODO: Implement Audit Trail Tab
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          'Audit Trail for application $applicationId will be implemented here.',
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

// New Widget for Communication Tab
class _CommunicationTabWidget extends ConsumerStatefulWidget {
  final String applicationId;
  const _CommunicationTabWidget({required this.applicationId, super.key});

  @override
  ConsumerState<_CommunicationTabWidget> createState() =>
      _CommunicationTabWidgetState();
}

class _CommunicationTabWidgetState
    extends ConsumerState<_CommunicationTabWidget> {
  final TextEditingController _messageController = TextEditingController();
  final PocketBaseService _pocketBaseService = PocketBaseService();
  final String _defaultAgentRecipientId =
      'AGENT_USER_ID_PLACEHOLDER'; // Placeholder

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  Future<void> _handleSendMessage() async {
    final messageContent = _messageController.text.trim();
    if (messageContent.isEmpty) {
      ShadToaster.of(context).show(
        const ShadToast(
          title: Text('Empty Message'),
          description: Text('Please enter a message to send.'),
        ),
      );
      return;
    }

    final currentUser = _pocketBaseService.currentUser;
    if (currentUser == null) {
      ShadToaster.of(context).show(
        const ShadToast(
          title: Text('Error: Not Logged In'),
          description: Text('You must be logged in to send messages.'),
        ),
      );
      return;
    }

    final success = await ref
        .read(applicationCommunicationProvider(widget.applicationId).notifier)
        .sendMessage(
          messageContent: messageContent,
          senderId: currentUser.id,
          recipientId: _defaultAgentRecipientId,
        );

    if (success && mounted) {
      _messageController.clear();
      ShadToaster.of(
        context,
      ).show(const ShadToast(title: Text('Message Sent Successfully')));
    } else if (mounted) {
      final error =
          ref
              .read(applicationCommunicationProvider(widget.applicationId))
              .error;
      if (error != null) {
        ShadToaster.of(context).show(
          ShadToast(
            title: const Text('Failed to Send Message'),
            description: Text(
              error.length > 100 ? '${error.substring(0, 100)}...' : error,
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final communicationState = ref.watch(
      applicationCommunicationProvider(widget.applicationId),
    );
    final messages = communicationState.messages;
    final currentUserId = _pocketBaseService.currentUser?.id;
    final ScrollController scrollController = ScrollController();

    ref.listen<ApplicationCommunicationState>(
      applicationCommunicationProvider(widget.applicationId),
      (previous, next) {
        if (previous?.messages.length != next.messages.length &&
            next.messages.isNotEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (scrollController.hasClients) {
              scrollController.animateTo(
                0.0,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOut,
              );
            }
          });
        }
      },
    );

    return Column(
      children: [
        Expanded(
          child:
              communicationState.isLoading && messages.isEmpty
                  ? const LoadingSpinnerWidget()
                  : communicationState.error != null && messages.isEmpty
                  ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'Error loading messages: ${communicationState.error}',
                        style: theme.textTheme.p.copyWith(
                          color: theme.colorScheme.destructive,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )
                  : messages.isEmpty
                  ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'No messages yet. Start a conversation with your assigned agent.',
                        style: theme.textTheme.p,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )
                  : ListView.builder(
                    controller: scrollController,
                    reverse: true,
                    padding: const EdgeInsets.all(16.0),
                    itemCount: messages.length,
                    itemBuilder: (context, index) {
                      final message = messages[index];
                      final isSender = message.senderId == currentUserId;

                      String senderDisplayName = 'Agent';
                      if (isSender) {
                        senderDisplayName = 'You';
                      } else if (message.senderName != null &&
                          message.senderName!.isNotEmpty) {
                        senderDisplayName = message.senderName!;
                      } else if (message.senderUserType == 'admin' ||
                          message.senderUserType == 'agent_placeholder_type') {
                        senderDisplayName = '3Pay Agent';
                      }

                      return Align(
                        alignment:
                            isSender
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                        child: Padding(
                          // Added Padding for margin
                          padding: const EdgeInsets.symmetric(vertical: 4.0),
                          child: ShadCard(
                            backgroundColor:
                                isSender
                                    ? theme.colorScheme.primary.withOpacity(0.1)
                                    : theme.colorScheme.secondary.withOpacity(
                                      0.05,
                                    ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12.0,
                                vertical: 8.0,
                              ),
                              child: Column(
                                crossAxisAlignment:
                                    isSender
                                        ? CrossAxisAlignment.end
                                        : CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    senderDisplayName,
                                    style: theme.textTheme.small.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    message.messageContent,
                                    style: theme.textTheme.p,
                                  ),
                                  if (message.attachmentUrl != null &&
                                      message.attachmentUrl!.isNotEmpty)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 4.0),
                                      child: InkWell(
                                        onTap: () {
                                          // TODO: Implement attachment download/view
                                          ShadToaster.of(context).show(
                                            ShadToast(
                                              description: Text(
                                                'Attachment: ${message.attachmentUrl}',
                                              ),
                                            ),
                                          );
                                        },
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.attach_file,
                                              size: 16,
                                              color: theme.colorScheme.primary,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              'View Attachment',
                                              style: theme.textTheme.small
                                                  .copyWith(
                                                    color:
                                                        theme
                                                            .colorScheme
                                                            .primary,
                                                    decoration:
                                                        TextDecoration
                                                            .underline,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  const SizedBox(height: 4),
                                  Text(
                                    DateFormat.yMd().add_jm().format(
                                      message.created.toLocal(),
                                    ),
                                    style: theme.textTheme.small.copyWith(
                                      color: theme.colorScheme.mutedForeground,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ), // Added missing parenthesis
                      );
                    },
                  ),
        ),
        if (communicationState.error != null &&
            messages.isNotEmpty &&
            !communicationState.isLoading)
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 4.0,
            ),
            child: Text(
              'Error: ${communicationState.error}',
              style: theme.textTheme.small.copyWith(
                color: theme.colorScheme.destructive,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Expanded(
                child: ShadInput(
                  controller: _messageController,
                  placeholder: const Text('Type your message to the agent...'),
                  enabled: !communicationState.isSending,
                ),
              ),
              const SizedBox(width: 8),
              ShadButton(
                onPressed:
                    communicationState.isSending ? null : _handleSendMessage,
                child:
                    communicationState.isSending
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.send, size: 16),
                            SizedBox(width: 8),
                            Text('Send'),
                          ],
                        ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
// New Widget for Audit Trail Tab
// Removed extra closing brace

// Moved _AuditTrailTabWidget to top-level (end of file)
