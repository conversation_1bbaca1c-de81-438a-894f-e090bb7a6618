import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/theme/app_theme.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/services/claim_documents_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/solicitor_dashboard_page.dart';
import 'package:three_pay_group_litigation_platform/src/core/utils/document_debug_utils.dart';
import 'package:file_picker/file_picker.dart';
import 'package:http/http.dart' as http;

// Document upload state class
class DocumentUploadState {
  PlatformFile? file;
  bool isUploading = false;
  double uploadProgress = 0.0;
  String? errorMessage;
  String? uploadedFileId;
  String? uploadedFileName;

  bool get isUploaded => uploadedFileId != null && uploadedFileName != null;

  DocumentUploadState({this.file, this.uploadedFileId, this.uploadedFileName});

  void reset() {
    file = null;
    isUploading = false;
    uploadProgress = 0.0;
    errorMessage = null;
    uploadedFileId = null;
    uploadedFileName = null;
  }
}

class FundingApplicationDocumentsPage extends StatefulWidget {
  const FundingApplicationDocumentsPage({
    super.key,
    required this.applicationId,
  });

  final String applicationId;
  static const String routeName = '/funding-application-documents';

  @override
  State<FundingApplicationDocumentsPage> createState() =>
      _FundingApplicationDocumentsPageState();
}

class _FundingApplicationDocumentsPageState
    extends State<FundingApplicationDocumentsPage> {
  final _pocketBaseService = PocketBaseService();
  final _claimDocumentsService = ClaimDocumentsService();
  FundingApplicationData? _applicationData;
  bool _isLoading = true;
  bool _isSubmitting = false;
  String? _errorMessage;
  List<UploadedDocumentCategory> _existingDocuments = [];

  // Document upload state
  final Map<String, DocumentUploadState> _documentStates = {
    'Schedule of Costs': DocumentUploadState(),
    'Legal Opinion': DocumentUploadState(),
    'Risk Committee Approval': DocumentUploadState(),
  };

  // Legacy field names removed - now using claim_documents collection

  @override
  void initState() {
    super.initState();
    _fetchApplicationData();
  }

  Future<void> _fetchApplicationData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Fetch application data
      final record = await _pocketBaseService.getOne(
        collectionName: 'funding_applications',
        recordId: widget.applicationId,
      );

      final appData = FundingApplicationData.fromJson(
        record.id,
        record.data,
        recordUpdatedTimestamp: record.data['updated'] as String?,
      );

      // Fetch existing documents from claim_documents collection
      final existingDocuments = await _claimDocumentsService
          .getDocumentsForFundingApplication(widget.applicationId);

      // Initialize document states from claim_documents collection
      for (final category in existingDocuments) {
        final docState = _documentStates[category.logicalName];
        if (docState != null && category.versions.isNotEmpty) {
          // Use the current version (latest version)
          final currentVersion = category.versions.last;
          docState.uploadedFileId = currentVersion.fileId;
          docState.uploadedFileName = currentVersion.filename;
        }
      }

      setState(() {
        _applicationData = appData;
        _existingDocuments = existingDocuments;
        _isLoading = false;
      });
    } catch (e) {
      // Use centralized error mapping for user-friendly messages
      final friendlyError = PocketBaseService.mapPocketBaseError(e);

      setState(() {
        _errorMessage = friendlyError.message;
        _isLoading = false;
      });
    }
  }

  Future<void> _pickAndUploadFile(String docName) async {
    final docState = _documentStates[docName]!;

    setState(() {
      docState.isUploading = true;
      docState.errorMessage = null;
    });

    try {
      // Pick file
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png', 'gif'],
        allowMultiple: false,
        withData: true,
      );

      if (result == null || result.files.isEmpty) {
        setState(() {
          docState.isUploading = false;
        });
        return;
      }

      final file = result.files.first;

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setState(() {
          docState.isUploading = false;
          docState.errorMessage = 'File size exceeds 10MB limit';
        });

        if (mounted) {
          ShadToaster.of(context).show(
            ShadToast.destructive(
              title: const Text('File Too Large'),
              description: const Text('Maximum file size is 10MB'),
            ),
          );
        }
        return;
      }

      // Create multipart file for claim_documents collection
      final multipartFile = http.MultipartFile.fromBytes(
        'document_file', // Use the field name from claim_documents collection
        file.bytes!,
        filename: file.name,
      );

      // Upload using ClaimDocumentsService
      // This will either create a new category or add a version to existing one
      await _claimDocumentsService.uploadFilesAndCreateCategory(
        fundingApplicationId: widget.applicationId,
        logicalName: docName,
        files: [multipartFile],
        uploadedBy: _pocketBaseService.pb.authStore.model!.id,
      );

      // Update state with uploaded file info
      setState(() {
        docState.file = file;
        docState.isUploading = false;
        docState.uploadProgress = 1.0;
        docState.uploadedFileId = 'uploaded';
        docState.uploadedFileName = file.name;
      });

      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast(
            title: const Text('File Uploaded'),
            description: Text('${file.name} uploaded successfully'),
          ),
        );
      }
    } catch (e) {
      // Use centralized error mapping for user-friendly messages
      final friendlyError = PocketBaseService.mapPocketBaseError(e);

      setState(() {
        docState.isUploading = false;
        docState.errorMessage = friendlyError.message;
      });

      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Upload Failed'),
            description: Text(friendlyError.message),
          ),
        );
      }
    }
  }

  Future<void> _deleteUploadedFile(String docName) async {
    final docState = _documentStates[docName]!;

    if (!docState.isUploaded) return;

    setState(() {
      docState.isUploading = true;
    });

    try {
      // Delete document category from claim_documents collection
      await _claimDocumentsService.deleteDocumentCategory(
        fundingApplicationId: widget.applicationId,
        logicalName: docName,
      );

      setState(() {
        docState.reset();
      });

      if (mounted) {
        ShadToaster.of(context).show(
          const ShadToast(
            title: Text('File Removed'),
            description: Text('The file has been removed'),
          ),
        );
      }
    } catch (e) {
      // Use centralized error mapping for user-friendly messages
      final friendlyError = PocketBaseService.mapPocketBaseError(e);

      setState(() {
        docState.isUploading = false;
        docState.errorMessage = friendlyError.message;
      });

      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Error'),
            description: Text(friendlyError.message),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    // final appTheme = AppTheme.of(context); // AppTheme.secondaryColor can be accessed statically

    return Scaffold(
      appBar: AppBar(
        title: const Text('Funding Application - Documents'),
        centerTitle: true,
        backgroundColor: theme.colorScheme.primary,
        foregroundColor:
            theme.colorScheme.primaryForeground, // Changed from onPrimary
        actions: [
          // Debug button for development
          if (const bool.fromEnvironment('dart.vm.product') == false)
            IconButton(
              icon: const Icon(Icons.bug_report),
              tooltip: 'Debug Documents',
              onPressed: () => _debugDocuments(),
            ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    if (_errorMessage != null)
                      Container(
                        padding: const EdgeInsets.all(16),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.destructive.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: theme.colorScheme.destructive,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: theme.colorScheme.destructive,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(
                                  color: theme.colorScheme.destructive,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    Text(
                      'Upload Required Documents',
                      style: theme.textTheme.h4.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Please download the templates, complete them, and upload the signed documents.',
                      style: theme.textTheme.muted,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    _buildDocumentItem(
                      context,
                      'Schedule of Costs',
                      'Detailed breakdown of anticipated legal costs.',
                    ),
                    const SizedBox(height: 16),
                    _buildDocumentItem(
                      context,
                      'Legal Opinion',
                      'Solicitor’s professional opinion on the merits of the case.',
                    ),
                    const SizedBox(height: 16),
                    _buildDocumentItem(
                      context,
                      'Risk Committee Approval',
                      'Internal approval document from the risk committee.',
                    ),
                    const SizedBox(height: 32),
                    Row(
                      children: [
                        Expanded(
                          child: ShadButton.outline(
                            onPressed: () {
                              if (Navigator.canPop(context)) {
                                Navigator.pop(context);
                              }
                            },
                            leading: const Icon(
                              Icons.arrow_back_ios_new,
                              size: 16,
                            ),
                            child: const Text(
                              'Back',
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ShadButton(
                            onPressed: () async {
                              try {
                                // No need to do anything special here as the files are already
                                // uploaded to PocketBase directly when the user uploads them

                                if (!mounted) return;

                                // Use a local context variable to avoid the lint warning
                                final currentContext = context;
                                if (mounted) {
                                  ShadToaster.of(currentContext).show(
                                    ShadToast(
                                      title: Text('Draft Saved'),
                                      description: Text(
                                        'Your application draft has been saved.',
                                      ),
                                    ),
                                  );
                                }
                              } catch (e) {
                                if (!mounted) return;

                                setState(() {
                                  _errorMessage =
                                      'Failed to save draft: ${e.toString()}';
                                });

                                // Use a local context variable to avoid the lint warning
                                final currentContext = context;
                                if (mounted) {
                                  ShadToaster.of(currentContext).show(
                                    ShadToast.destructive(
                                      title: Text('Save Failed'),
                                      description: Text(
                                        _errorMessage ?? 'An error occurred',
                                      ),
                                    ),
                                  );
                                }
                              }
                            },
                            backgroundColor: AppTheme.secondaryColor,
                            child: const Text(
                              'Save Draft',
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ShadButton(
                      onPressed:
                          _isSubmitting
                              ? null
                              : () async {
                                // Check if the 'Legal Opinion' document is uploaded
                                final legalOpinionState =
                                    _documentStates['Legal Opinion'];
                                if (legalOpinionState == null ||
                                    !legalOpinionState.isUploaded) {
                                  ShadToaster.of(context).show(
                                    ShadToast.destructive(
                                      title: Text('Missing Legal Opinion'),
                                      description: Text(
                                        'Please upload the Legal Opinion document before submitting.',
                                      ),
                                    ),
                                  );
                                  return;
                                }

                                setState(() {
                                  _isSubmitting = true;
                                  _errorMessage = null;
                                });

                                try {
                                  // Update application status to submitted
                                  await _pocketBaseService.updateRecord(
                                    collectionName: 'funding_applications',
                                    recordId: widget.applicationId,
                                    data: {
                                      'application_status': 'submitted',
                                      'submission_date':
                                          DateTime.now()
                                              .toUtc()
                                              .toIso8601String(),
                                    },
                                  );

                                  if (!mounted) return;

                                  // Use a local context variable to avoid the lint warning
                                  final currentContext = context;
                                  if (mounted) {
                                    // Show success message and navigate to dashboard
                                    ShadToaster.of(currentContext).show(
                                      ShadToast(
                                        title: Text('Application Submitted'),
                                        description: Text(
                                          'Your funding application has been submitted for review.',
                                        ),
                                      ),
                                    );

                                    // Navigate to dashboard
                                    Navigator.of(
                                      currentContext,
                                    ).pushNamedAndRemoveUntil(
                                      SolicitorDashboardPage.routeName,
                                      (route) => false,
                                    );
                                  }
                                } catch (e) {
                                  if (!mounted) return;

                                  setState(() {
                                    _isSubmitting = false;
                                    _errorMessage =
                                        'Failed to submit application: ${e.toString()}';
                                  });

                                  // Use a local context variable to avoid the lint warning
                                  final currentContext = context;
                                  if (mounted) {
                                    ShadToaster.of(currentContext).show(
                                      ShadToast.destructive(
                                        title: Text('Submission Failed'),
                                        description: Text(
                                          _errorMessage ?? 'An error occurred',
                                        ),
                                      ),
                                    );
                                  }
                                }
                              },
                      width: double.infinity,
                      child:
                          _isSubmitting
                              ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : const Text(
                                'Submit Application',
                                overflow: TextOverflow.ellipsis,
                              ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildDocumentItem(
    BuildContext context,
    String docName,
    String description,
  ) {
    final theme = ShadTheme.of(context);
    final docState = _documentStates[docName]!;
    final bool isUploaded = docState.isUploaded;
    final bool isUploading = docState.isUploading;

    return ShadCard(
      title: Text(docName, style: theme.textTheme.large),
      description: Text(description),
      child: Padding(
        padding: const EdgeInsets.only(top: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (isUploaded)
                  Row(
                    children: [
                      Expanded(
                        child: ShadButton(
                          onPressed:
                              isUploading
                                  ? null
                                  : () => _pickAndUploadFile(docName),
                          leading: Icon(Icons.upload_file_outlined, size: 16),
                          child: Text(
                            'Replace File',
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ShadButton.destructive(
                        onPressed:
                            isUploading
                                ? null
                                : () => _deleteUploadedFile(docName),
                        leading: Icon(Icons.delete_outline, size: 16),
                        child: Text('Delete', overflow: TextOverflow.ellipsis),
                      ),
                    ],
                  )
                else
                  ShadButton(
                    onPressed:
                        isUploading ? null : () => _pickAndUploadFile(docName),
                    leading: Icon(Icons.upload_file_outlined, size: 16),
                    child: Text(
                      'Upload Document',
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            if (isUploading)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LinearProgressIndicator(
                    value:
                        docState.uploadProgress > 0
                            ? docState.uploadProgress
                            : null,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Uploading...',
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ],
              )
            else
              Row(
                children: [
                  Icon(
                    isUploaded
                        ? Icons.check_circle_outline
                        : Icons.info_outline,
                    color:
                        isUploaded
                            ? Colors.green.shade600
                            : theme.colorScheme.mutedForeground,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      isUploaded
                          ? '${docState.uploadedFileName} Uploaded'
                          : 'Status: Not Uploaded',
                      style: theme.textTheme.small.copyWith(
                        color:
                            isUploaded
                                ? Colors.green.shade700
                                : theme.colorScheme.mutedForeground,
                        fontWeight:
                            isUploaded ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ),
            if (docState.errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  docState.errorMessage!,
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.destructive,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Debug method to help identify and fix document issues
  Future<void> _debugDocuments() async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              content: Row(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('Debugging documents...'),
                ],
              ),
            ),
      );

      // Run debug operations
      await DocumentDebugUtils.testMetadataGeneration();
      await DocumentDebugUtils.testGoogleDriveUrlConversion();
      await DocumentDebugUtils.debugFundingApplicationDocuments(
        widget.applicationId,
      );
      await DocumentDebugUtils.debugProblematicFile();

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show success message
      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast(
            title: const Text('Debug Complete'),
            description: const Text(
              'Check console logs for debug information.',
            ),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) Navigator.of(context).pop();

      // Show error message
      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Debug Failed'),
            description: Text('Error: ${e.toString()}'),
          ),
        );
      }
    }
  }
}
