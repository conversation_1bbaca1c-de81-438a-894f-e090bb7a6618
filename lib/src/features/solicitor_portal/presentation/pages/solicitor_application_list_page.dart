// lib/src/features/solicitor_portal/presentation/pages/solicitor_application_list_page.dart
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/empty_state_widget.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/loading_spinner_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart'; // Assuming this model can be reused or adapted
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/solicitor_application_detail_page.dart';

class SolicitorApplicationListPage extends StatefulWidget {
  static const String routeName = '/solicitor/application-list';
  const SolicitorApplicationListPage({super.key});

  @override
  State<SolicitorApplicationListPage> createState() =>
      _SolicitorApplicationListPageState();
}

class _SolicitorApplicationListPageState
    extends State<SolicitorApplicationListPage> {
  final PocketBaseService _pocketBaseService = PocketBaseService();
  List<FundingApplicationData> _applications = [];
  bool _isLoading = true;
  String? _error;

  // Filter and sort state
  String _selectedStatusFilter = 'All';
  DateTimeRange? _selectedDateRangeFilter;
  String _searchKeyword = '';
  String _sortOption = 'submission_date'; // Default sort
  bool _sortAscending = false; // Default to descending for dates

  final Map<String, String> _sortOptionsMap = {
    'submission_date': 'Submission Date',
    'last_updated': 'Last Update Date',
    'application_status': 'Status',
  };
 
  final List<String> _statusOptions = [
    'All', 'Draft', 'Submitted', 'Under Review', 
    'Requires Info', 'Approved', 'Rejected', 'Funded', 'Active'
  ];

  @override
  void initState() {
    super.initState();
    _fetchApplications();
  }

  Future<void> _fetchApplications() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });
    try {
      final String currentUserId = _pocketBaseService.client.authStore.model?.id ?? '';
      if (currentUserId.isEmpty) {
        throw Exception('User not authenticated.');
      }

      List<String> filterParts = ['solicitor_id="$currentUserId"']; // Assuming solicitor_id links to users collection

      if (_selectedStatusFilter != 'All') {
        filterParts.add('application_status="${_selectedStatusFilter.toLowerCase()}"');
      }

      if (_selectedDateRangeFilter != null) {
        final startDate = DateFormat("yyyy-MM-dd HH:mm:ss.SSS'Z'").format(_selectedDateRangeFilter!.start.toUtc());
        final endDate = DateFormat("yyyy-MM-dd HH:mm:ss.SSS'Z'").format(_selectedDateRangeFilter!.end.toUtc().add(const Duration(days: 1))); // Ensure end date is inclusive
        filterParts.add('(submission_date >= "$startDate" && submission_date < "$endDate")');
      }

      if (_searchKeyword.isNotEmpty) {
        // PocketBase filter syntax for OR conditions and LIKE
        // This assumes 'id' is the application ID field in PocketBase.
        // Adjust field names if 'application_id_display' or similar is used for user-facing App ID.
        final keywordLower = _searchKeyword.toLowerCase();
        filterParts.add('(claim_title ~ "%$keywordLower%" || id ~ "%$keywordLower%")');
      }
      
      String filterString = filterParts.join(' && ');

      String sortPrefix = _sortAscending ? '' : '-';
      String sortField = _sortOption;
      if (_sortOption == 'last_updated') {
        sortField = 'updated'; // PocketBase uses 'updated' for record last modification
      }
      String sortString = '$sortPrefix$sortField';

      final records = await _pocketBaseService.getFullList(
        collectionName: 'funding_applications',
        filter: filterString.isNotEmpty ? filterString : null,
        sort: sortString,
      );

      _applications = records.map((record) {
        return FundingApplicationData.fromJson(record.id, record.data, recordUpdatedTimestamp: record.updated);
      }).toList();

    } catch (e) {
      setState(() {
        _error = 'Failed to load applications: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildFilterBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Wrap(
        spacing: 16,
        runSpacing: 16,
        children: [
          // Status Filter
          SizedBox(
            width: 200,
            child: ShadSelect<String>(
              placeholder: const Text('Filter by Status'),
              options: _statusOptions.map((status) => ShadOption(value: status, child: Text(status))).toList(),
              selectedOptionBuilder: (context, value) => Text(value),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedStatusFilter = value;
                    _fetchApplications(); // Refetch with new filter
                  });
                }
              },
            ),
          ),
          // Date Range Filter (Placeholder Button)
          ShadButton.outline(
            child: Text(_selectedDateRangeFilter != null
                ? '${DateFormat.yMd().format(_selectedDateRangeFilter!.start)} - ${DateFormat.yMd().format(_selectedDateRangeFilter!.end)}'
                : 'Filter by Date Range'),
            onPressed: () async {
              final picked = await showDateRangePicker(
                context: context,
                firstDate: DateTime(2020),
                lastDate: DateTime.now().add(const Duration(days: 365)),
                initialDateRange: _selectedDateRangeFilter,
              );
              if (picked != null) {
                setState(() {
                  _selectedDateRangeFilter = picked;
                  _fetchApplications(); // Refetch
                });
              }
            },
          ),
          // Keyword Search
          SizedBox(
            width: 250,
            child: ShadInput(
              placeholder: const Text('Search by Claim Title, App ID...'),
              onChanged: (value) {
                // Debounce this in a real app
                setState(() {
                  _searchKeyword = value;
                });
              },
              // Add a search button or trigger search on text change (debounced)
            ),
          ),
          ShadButton(
            child: const Text('Search'),
            onPressed: _fetchApplications, // Refetch with new keyword
          ),
          const SizedBox(width: 20),
          // Sorting Options
          SizedBox(
            width: 200,
            child: ShadSelect<String>(
              placeholder: const Text('Sort by'),
              options: _sortOptionsMap.entries
                  .map((entry) =>
                      ShadOption(value: entry.key, child: Text(entry.value)))
                  .toList(),
              selectedOptionBuilder: (context, value) =>
                  Text(_sortOptionsMap[value] ?? 'Sort by'),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _sortOption = value;
                    // Default to descending for dates, ascending for status
                    _sortAscending = value == 'application_status';
                    _fetchApplications();
                  });
                }
              },
            ),
          ),
          ShadIconButton.ghost(
            icon: Icon(
              _sortAscending ? LucideIcons.arrowUpAZ : LucideIcons.arrowDownAZ,
              size: 16,
            ),
            onPressed: () {
              setState(() {
                _sortAscending = !_sortAscending;
                _fetchApplications();
              });
            },
          ),
        ],
      ),
    );
  }
 
  Widget _buildApplicationList() {
    if (_applications.isEmpty) {
      return const EmptyStateWidget(
        icon: LucideIcons.folderOpen,
        message: 'You have no submitted applications yet.',
      );
    }

    // Using ShadCard for list items for now, ShadTable can be complex for initial setup
    return ListView.builder(
      itemCount: _applications.length,
      itemBuilder: (context, index) {
        final app = _applications[index];
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: ShadCard(
            title: Text(app.claimTitle ?? 'No Title', style: ShadTheme.of(context).textTheme.h4),
            description: Text('ID: ${app.id}'),
            child: Column( // Assuming 'child' is the correct parameter for content
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
               ShadBadge( // Removed variant
                 child: Text('Status: ${app.applicationStatus}'),
               ),
               const SizedBox(height: 4),
               Text('Submission Date: ${app.submissionDate != null && DateTime.tryParse(app.submissionDate!) != null ? DateFormat.yMd().format(DateTime.parse(app.submissionDate!)) : 'N/A'}'),
               Text('Last Updated: ${app.lastUpdated != null && DateTime.tryParse(app.lastUpdated!) != null ? DateFormat.yMd().format(DateTime.parse(app.lastUpdated!)) : 'N/A'}'),
               // Text('Action Required: Placeholder'), // TODO: Implement logic
             ],
           ),
            footer: ShadButton(
              child: const Text('View Details'),
              onPressed: () {
                Navigator.pushNamed(
                  context,
                  SolicitorApplicationDetailPage.routeName,
                  arguments: app.id, // Pass application ID
                );
              },
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Funding Applications'),
        // TODO: Add sorting options if needed in AppBar actions
      ),
      body: Column(
        children: [
          _buildFilterBar(),
          Expanded(
            child: _isLoading
                ? const LoadingSpinnerWidget()
                : _error != null
                    ? Center(child: Text(_error!, style: TextStyle(color: Theme.of(context).colorScheme.error)))
                    : _buildApplicationList(),
          ),
        ],
      ),
    );
  }
}