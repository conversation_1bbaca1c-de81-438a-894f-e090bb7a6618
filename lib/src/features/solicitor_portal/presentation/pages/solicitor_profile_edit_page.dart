import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/form_submission_error_alert_widget.dart';
import '../providers/solicitor_profile_provider.dart';
import '../widgets/solicitor_profile_edit_form.dart';

/// Page for editing solicitor profile information
class SolicitorProfileEditPage extends ConsumerStatefulWidget {
  static const String routeName = '/solicitor/profile/edit';

  const SolicitorProfileEditPage({super.key});

  @override
  ConsumerState<SolicitorProfileEditPage> createState() =>
      _SolicitorProfileEditPageState();
}

class _SolicitorProfileEditPageState
    extends ConsumerState<SolicitorProfileEditPage> {
  @override
  void initState() {
    super.initState();
    // Load profile data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(solicitorProfileProvider.notifier).loadProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(solicitorProfileProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Profile', style: theme.textTheme.h4),
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Error Alert
            if (profileState.error != null)
              FormSubmissionErrorAlertWidget(
                title: 'Error',
                description: profileState.error!,
              ),

            const SizedBox(height: 16),

            // Loading State
            if (profileState.isLoading)
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Loading profile...'),
                  ],
                ),
              )
            else if (profileState.profile == null)
              ShadCard(
                title: Text('Profile Not Found', style: theme.textTheme.h4),
                child: const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(LucideIcons.info, size: 48),
                      SizedBox(height: 16),
                      Text(
                        'Unable to load profile information. Please try again.',
                      ),
                    ],
                  ),
                ),
              )
            else
              // Edit Form
              SolicitorProfileEditForm(
                onSaved: _handleSaved,
                onCancelled: _handleCancelled,
              ),
          ],
        ),
      ),
    );
  }

  void _handleSaved() {
    // Clear any messages
    ref.read(solicitorProfileProvider.notifier).clearMessages();

    // Navigate back
    Navigator.of(context).pop();
  }

  void _handleCancelled() {
    // Clear any messages
    ref.read(solicitorProfileProvider.notifier).clearMessages();

    // Navigate back
    Navigator.of(context).pop();
  }
}

/// Responsive edit page for larger screens
class ResponsiveSolicitorProfileEditPage extends ConsumerStatefulWidget {
  static const String routeName = '/solicitor/profile/edit-responsive';

  const ResponsiveSolicitorProfileEditPage({super.key});

  @override
  ConsumerState<ResponsiveSolicitorProfileEditPage> createState() =>
      _ResponsiveSolicitorProfileEditPageState();
}

class _ResponsiveSolicitorProfileEditPageState
    extends ConsumerState<ResponsiveSolicitorProfileEditPage> {
  @override
  void initState() {
    super.initState();
    // Load profile data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(solicitorProfileProvider.notifier).loadProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final profileState = ref.watch(solicitorProfileProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Profile', style: theme.textTheme.h4),
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
      ),
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 800),
          padding: const EdgeInsets.all(24.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Error Alert
                if (profileState.error != null)
                  FormSubmissionErrorAlertWidget(
                    title: 'Error',
                    description: profileState.error!,
                  ),

                const SizedBox(height: 16),

                // Loading State
                if (profileState.isLoading)
                  const Center(
                    child: Column(
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading profile...'),
                      ],
                    ),
                  )
                else if (profileState.profile == null)
                  ShadCard(
                    title: Text('Profile Not Found', style: theme.textTheme.h4),
                    child: const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Icon(LucideIcons.info, size: 48),
                          SizedBox(height: 16),
                          Text(
                            'Unable to load profile information. Please try again.',
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  // Edit Form
                  SolicitorProfileEditForm(
                    onSaved: _handleSaved,
                    onCancelled: _handleCancelled,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleSaved() {
    // Clear any messages
    ref.read(solicitorProfileProvider.notifier).clearMessages();

    // Navigate back
    Navigator.of(context).pop();
  }

  void _handleCancelled() {
    // Clear any messages
    ref.read(solicitorProfileProvider.notifier).clearMessages();

    // Navigate back
    Navigator.of(context).pop();
  }
}
