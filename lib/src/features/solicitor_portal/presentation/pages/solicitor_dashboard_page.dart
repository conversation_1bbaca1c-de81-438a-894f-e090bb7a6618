import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/providers/notification_counter_provider.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/theme/app_theme.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/data/models/enhanced_notification_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/presentation/pages/notification_detail_page.dart'
    as detail;
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/funding_application_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/my_claims_list_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/pu_application_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/reports_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/solicitor_profile_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/utils/responsive_layout.dart';

class SolicitorDashboardPage extends ConsumerStatefulWidget {
  const SolicitorDashboardPage({super.key});

  static const String routeName = '/solicitor-dashboard';

  @override
  ConsumerState<SolicitorDashboardPage> createState() =>
      _SolicitorDashboardPageState();
}

class _SolicitorDashboardPageState
    extends ConsumerState<SolicitorDashboardPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final PocketBaseService _pbService = PocketBaseService();
  String _solicitorFirstName = ''; // To store the solicitor's first name
  bool _isLoading = true; // Loading state

  // Solicitor profile data
  String _puStatus = 'none'; // Default PU status

  // Application counts
  int _activeClaims = 0;
  int _pendingApplications = 0;
  bool _isLoadingCounts = true;

  // Status filter state
  String _selectedStatusFilter = 'All';
  // TODO: Populate with actual statuses from backend or a predefined list
  final List<String> _statusOptions = [
    'All',
    'Active - Pre-Action',
    'Active - Discovery',
    'Settled',
    'Closed',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    _fetchDashboardData();
  }

  // Combined method to fetch all dashboard data
  Future<void> _fetchDashboardData() async {
    setState(() {
      _isLoading = true;
      _isLoadingCounts = true;
    });

    await Future.wait([_fetchSolicitorName(), _fetchApplicationCounts()]);

    setState(() {
      _isLoading = false;
    });
  }

  // Fetch application counts from PocketBase
  Future<void> _fetchApplicationCounts() async {
    try {
      final currentUser = _pbService.currentUser;
      if (currentUser == null) {
        setState(() {
          _isLoadingCounts = false;
        });
        return;
      }

      LoggerService.debug('Current User ID: ${currentUser.id}');

      // Get the solicitor profile ID for the current user
      final solicitorProfilesResult = await _pbService.client
          .collection('solicitor_profiles')
          .getList(
            filter: 'user_id = "${currentUser.id}"',
            page: 1,
            perPage: 1,
          );

      if (solicitorProfilesResult.items.isEmpty) {
        LoggerService.warning(
          'No solicitor profile found for user ID: ${currentUser.id}',
        );
        setState(() {
          _isLoadingCounts = false;
        });
        return;
      }

      final solicitorProfile = solicitorProfilesResult.items[0];
      final solicitorProfileId = solicitorProfile.id;

      // Store the PU status
      setState(() {
        _puStatus = solicitorProfile.data['pu_status'] as String? ?? 'none';
      });

      // Get all funding applications for this solicitor
      final allApplicationsResult = await _pbService.client
          .collection('funding_applications')
          .getList(filter: 'solicitor_profile_id ~ "$solicitorProfileId"');

      // Count active claims (applications with status "approved", "approved_for_funding", or "funded")
      int activeClaimsCount = 0;

      // Count pending applications (applications with status "submitted", "under_review", or "requires_info")
      int pendingApplicationsCount = 0;

      // Process each application to count by status
      for (final app in allApplicationsResult.items) {
        final status = app.data['application_status'] as String?;

        if (status == 'approved' ||
            status == 'approved_for_funding' ||
            status == 'funded') {
          activeClaimsCount++;
        } else if (status == 'submitted' ||
            status == 'under_review' ||
            status == 'requires_info') {
          pendingApplicationsCount++;
        }
      }

      if (mounted) {
        setState(() {
          _activeClaims = activeClaimsCount;
          _pendingApplications = pendingApplicationsCount;
          _isLoadingCounts = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingCounts = false;
        });
      }
      LoggerService.error('Error fetching application counts', e);
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  // Fetch the solicitor's name from PocketBase
  Future<void> _fetchSolicitorName() async {
    try {
      final currentUser = _pbService.currentUser;
      if (currentUser != null) {
        // Fetch the user record to get the name
        final userRecord = await _pbService.client
            .collection('users')
            .getOne(currentUser.id);

        // Try to get the first name, or fall back to full name or email
        String firstName = userRecord.data['first_name'] as String? ?? '';
        if (firstName.isEmpty) {
          // If first_name is empty, try to extract first name from full name
          final fullName = userRecord.data['name'] as String? ?? '';
          if (fullName.isNotEmpty) {
            // Split the full name and take the first part as the first name
            firstName = fullName.split(' ').first;
          } else {
            // If name is also empty, use email or a default
            final email = userRecord.data['email'] as String? ?? '';
            firstName = email.isNotEmpty ? email.split('@').first : 'Solicitor';
          }
        }

        if (mounted) {
          setState(() {
            _solicitorFirstName = firstName;
            _isLoading = false;
          });
        }
      } else {
        // If no user is logged in, set a default name
        if (mounted) {
          setState(() {
            _solicitorFirstName = 'Solicitor';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      // On error, set a default name
      if (mounted) {
        setState(() {
          _solicitorFirstName = 'Solicitor';
          _isLoading = false;
        });
      }
      LoggerService.error('Error fetching solicitor name', e);
    }
  }

  // Navigation methods for web/desktop layout
  Widget _buildTopNavigationBar(
    BuildContext context,
    ShadThemeData theme,
    Color primaryColor,
  ) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32.0),
        child: Row(
          children: [
            // Quick Actions
            _buildNavButton(
              context,
              'Dashboard',
              LucideIcons.layoutDashboard,
              true,
              primaryColor,
              () {},
            ),
            const SizedBox(width: 24),
            _buildNavButton(
              context,
              'My Claims',
              LucideIcons.briefcase,
              false,
              primaryColor,
              () => Navigator.of(context).pushNamed(MyClaimsListPage.routeName),
            ),
            const SizedBox(width: 24),
            _buildNavButton(
              context,
              'Reports',
              LucideIcons.activity,
              false,
              primaryColor,
              () => Navigator.of(context).pushNamed(ReportsPage.routeName),
            ),
            const SizedBox(width: 24),

            const Spacer(),

            // New Application Button
            ShadButton(
              onPressed: () {
                if (_puStatus == 'approved') {
                  Navigator.of(
                    context,
                  ).pushNamed(FundingApplicationPage.routeName);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text(
                        'You need Permitted User (PU) approval to submit funding applications.',
                      ),
                      backgroundColor: theme.colorScheme.destructive,
                    ),
                  );
                }
              },
              backgroundColor:
                  _puStatus == 'approved'
                      ? primaryColor
                      : theme.colorScheme.muted,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    LucideIcons.plus,
                    size: 16,
                    color:
                        _puStatus == 'approved'
                            ? Colors.white
                            : theme.colorScheme.foreground,
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      'New Application',
                      style: TextStyle(
                        color:
                            _puStatus == 'approved'
                                ? Colors.white
                                : theme.colorScheme.foreground,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavButton(
    BuildContext context,
    String label,
    IconData icon,
    bool isActive,
    Color primaryColor,
    VoidCallback onTap,
  ) {
    final theme = ShadTheme.of(context);

    return ShadButton.ghost(
      onPressed: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 18,
            color: isActive ? primaryColor : theme.colorScheme.foreground,
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              label,
              style: TextStyle(
                color: isActive ? primaryColor : theme.colorScheme.foreground,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  // Sidebar removed for cleaner web/desktop experience

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final isDesktop = SolicitorResponsiveLayout.isDesktop(context);
    final isTablet = SolicitorResponsiveLayout.isTablet(context);

    // Using theme colors from AppTheme
    final primaryColor = AppTheme.primaryColor;
    final secondaryColor = AppTheme.secondaryColor;
    final tertiaryColor = AppTheme.tertiaryColor;
    final warningColor = Colors.amber[600]!;
    final dangerColor = theme.colorScheme.destructive;
    final neutralBgColor =
        theme.brightness == Brightness.light
            ? AppTheme.backgroundLight
            : AppTheme.backgroundDark;

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: neutralBgColor,
      floatingActionButton: SizedBox(
        width: 180, // Fixed width to prevent overflow
        child: FloatingActionButton.extended(
          onPressed: () {
            if (_puStatus == 'approved') {
              Navigator.of(context).pushNamed(FundingApplicationPage.routeName);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'You need Permitted User (PU) approval to submit funding applications.',
                  ),
                  backgroundColor: theme.colorScheme.destructive,
                ),
              );
            }
          },
          icon: Icon(
            LucideIcons.plus,
            color:
                _puStatus == 'approved'
                    ? Colors.white
                    : theme.colorScheme.background,
            size: 18, // Smaller icon
          ),
          label: Text(
            'New Application',
            style: TextStyle(
              color:
                  _puStatus == 'approved'
                      ? Colors.white
                      : theme.colorScheme.background,
              fontWeight: FontWeight.bold,
              fontSize: 14, // Smaller text
            ),
            overflow: TextOverflow.ellipsis, // Handle text overflow
          ),
          backgroundColor:
              _puStatus == 'approved'
                  ? primaryColor
                  : theme.colorScheme.muted.withAlpha(180),
          foregroundColor:
              _puStatus == 'approved'
                  ? Colors.white
                  : theme.colorScheme.background,
          tooltip:
              _puStatus == 'approved'
                  ? 'Create a new funding application'
                  : 'You need Permitted User (PU) approval to submit funding applications',
        ),
      ),
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(LucideIcons.scale, color: primaryColor),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Solicitor Portal',
                style: theme.textTheme.h4.copyWith(
                  color: theme.colorScheme.foreground,
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        elevation: 0,
        backgroundColor: theme.colorScheme.card,
        actions: [
          Consumer(
            builder: (context, ref, child) {
              final unreadCount = ref.watch(currentUnreadCountProvider);

              return Stack(
                clipBehavior: Clip.none,
                children: [
                  ShadButton.ghost(
                    onPressed: () {
                      Navigator.of(context).pushNamed('/notifications');
                    },
                    child: Icon(
                      LucideIcons.bell,
                      size: 20,
                      color: theme.colorScheme.foreground,
                    ),
                  ),
                  // Unread notification badge
                  if (unreadCount > 0)
                    Positioned(
                      right: 6,
                      top: 6,
                      child: Container(
                        width: 18,
                        height: 18,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.destructive,
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            unreadCount.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 9,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
          const SizedBox(width: 4),
          ShadIconButton.ghost(
            icon: Icon(
              LucideIcons.user,
              size: 20,
              color: theme.colorScheme.foreground,
            ),
            onPressed: () {
              Navigator.of(context).pushNamed(SolicitorProfilePage.routeName);
            },
          ),
          const SizedBox(width: 4),
          // ShadDropdownMenu( ... existing user profile dropdown ... )
          const SizedBox(width: 16),
        ],
      ),
      // Enhanced web/desktop layout with top navigation
      body: Column(
        children: [
          // Top navigation bar for web/desktop
          if (SolicitorResponsiveLayout.isDesktop(context))
            _buildTopNavigationBar(context, theme, primaryColor),

          // Main content
          Expanded(
            child: _buildMainContent(
              context,
              theme,
              primaryColor,
              secondaryColor,
              tertiaryColor,
              warningColor,
              dangerColor,
              neutralBgColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent(
    BuildContext context,
    ShadThemeData theme,
    Color primaryColor,
    Color secondaryColor,
    Color tertiaryColor,
    Color warningColor,
    Color dangerColor,
    Color neutralBgColor,
  ) {
    final isDesktop = SolicitorResponsiveLayout.isDesktop(context);
    final isTablet = SolicitorResponsiveLayout.isTablet(context);

    // Enhanced responsive padding for web/desktop
    final horizontalPadding = SolicitorResponsiveLayout.getHorizontalPadding(
      context,
    );
    final verticalPadding = SolicitorResponsiveLayout.getVerticalPadding(
      context,
    );

    return RefreshIndicator.adaptive(
      onRefresh: _fetchDashboardData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: verticalPadding,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Filter Dropdown

            // Enhanced welcome section with stats for web/desktop
            Container(
              padding: EdgeInsets.all(isDesktop ? 32 : 24),
              decoration: BoxDecoration(
                color: theme.colorScheme.card,
                borderRadius: BorderRadius.circular(isDesktop ? 16 : 12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: isDesktop ? 15 : 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _isLoading
                        ? 'Welcome back'
                        : 'Welcome back, $_solicitorFirstName',
                    style: theme.textTheme.h3.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.foreground,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Here\'s an overview of your claims',
                    style: theme.textTheme.p.copyWith(
                      color: theme.colorScheme.foreground,
                    ),
                  ),
                  const SizedBox(height: 24),
                  LayoutBuilder(
                    builder: (context, constraints) {
                      // Responsive stat cards layout
                      final isDesktopLayout =
                          SolicitorResponsiveLayout.isDesktop(context);
                      final isTabletLayout = SolicitorResponsiveLayout.isTablet(
                        context,
                      );

                      if (isDesktopLayout) {
                        // Desktop: Enhanced 2-card layout with better spacing
                        return Row(
                          children: [
                            Expanded(
                              child: _buildStatCard(
                                context,
                                _isLoadingCounts
                                    ? '...'
                                    : _activeClaims.toString(),
                                'Active Claims',
                                LucideIcons.briefcase,
                                primaryColor,
                                onTap:
                                    () => Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder:
                                            (context) => MyClaimsListPage(
                                              statusFilter: [
                                                'approved',
                                                'approved_for_funding',
                                                'funded',
                                              ],
                                              pageTitle: 'Active Claims',
                                            ),
                                      ),
                                    ),
                              ),
                            ),
                            const SizedBox(width: 32),
                            Expanded(
                              child: _buildStatCard(
                                context,
                                _isLoadingCounts
                                    ? '...'
                                    : _pendingApplications.toString(),
                                'Pending Applications',
                                LucideIcons.fileText,
                                warningColor,
                                onTap:
                                    () => Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder:
                                            (context) => MyClaimsListPage(
                                              statusFilter: [
                                                'submitted',
                                                'under_review',
                                                'requires_info',
                                              ],
                                              pageTitle: 'Pending Applications',
                                            ),
                                      ),
                                    ),
                              ),
                            ),
                          ],
                        );
                      } else if (isTabletLayout) {
                        // Tablet: 2 cards per row
                        return Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildStatCard(
                                    context,
                                    _isLoadingCounts
                                        ? '...'
                                        : _activeClaims.toString(),
                                    'Active Claims',
                                    LucideIcons.briefcase,
                                    primaryColor,
                                    onTap:
                                        () => Navigator.of(context).push(
                                          MaterialPageRoute(
                                            builder:
                                                (context) => MyClaimsListPage(
                                                  statusFilter: [
                                                    'approved',
                                                    'approved_for_funding',
                                                    'funded',
                                                  ],
                                                  pageTitle: 'Active Claims',
                                                ),
                                          ),
                                        ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: _buildStatCard(
                                    context,
                                    _isLoadingCounts
                                        ? '...'
                                        : _pendingApplications.toString(),
                                    'Pending Applications',
                                    LucideIcons.fileText,
                                    warningColor,
                                    onTap:
                                        () => Navigator.of(context).push(
                                          MaterialPageRoute(
                                            builder:
                                                (context) => MyClaimsListPage(
                                                  statusFilter: [
                                                    'submitted',
                                                    'under_review',
                                                    'requires_info',
                                                  ],
                                                  pageTitle:
                                                      'Pending Applications',
                                                ),
                                          ),
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        );
                      } else {
                        // Mobile: Single column
                        return Column(
                          children: [
                            _buildStatCard(
                              context,
                              _isLoadingCounts
                                  ? '...'
                                  : _activeClaims.toString(),
                              'Active Claims',
                              LucideIcons.briefcase,
                              primaryColor,
                              onTap:
                                  () => Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder:
                                          (context) => MyClaimsListPage(
                                            statusFilter: [
                                              'approved',
                                              'approved_for_funding',
                                              'funded',
                                            ],
                                            pageTitle: 'Active Claims',
                                          ),
                                    ),
                                  ),
                            ),
                            const SizedBox(height: 16),
                            _buildStatCard(
                              context,
                              _isLoadingCounts
                                  ? '...'
                                  : _pendingApplications.toString(),
                              'Pending Applications',
                              LucideIcons.fileText,
                              warningColor,
                              onTap:
                                  () => Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder:
                                          (context) => MyClaimsListPage(
                                            statusFilter: [
                                              'submitted',
                                              'under_review',
                                              'requires_info',
                                            ],
                                            pageTitle: 'Pending Applications',
                                          ),
                                    ),
                                  ),
                            ),
                          ],
                        );
                      }
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Responsive main content layout
            LayoutBuilder(
              builder: (context, constraints) {
                final isDesktopLayout = SolicitorResponsiveLayout.isDesktop(
                  context,
                );
                final isTabletLayout = SolicitorResponsiveLayout.isTablet(
                  context,
                );

                if (isDesktopLayout) {
                  // Desktop: Two-column layout with wider spacing
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        // flex: 3, // Flex is not needed if it's the only expanded child
                        child: _buildLeftColumnContent(
                          context,
                          theme,
                          primaryColor,
                          secondaryColor,
                          warningColor,
                          dangerColor,
                        ),
                      ),
                      // const SizedBox(width: 32), // No longer needed
                      // Expanded( // Right column is now empty
                      //   flex: 2,
                      //   child: _buildRightColumnContent(
                      //     context,
                      //     theme,
                      //     primaryColor,
                      //     secondaryColor,
                      //     warningColor,
                      //   ),
                      // ),
                    ],
                  );
                } else if (isTabletLayout) {
                  // Tablet: Two-column layout with medium spacing
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        // flex: 2, // Flex is not needed if it's the only expanded child
                        child: _buildLeftColumnContent(
                          context,
                          theme,
                          primaryColor,
                          secondaryColor,
                          warningColor,
                          dangerColor,
                        ),
                      ),
                      // const SizedBox(width: 24), // No longer needed
                      // Expanded( // Right column is now empty
                      //   flex: 1,
                      //   child: _buildRightColumnContent(
                      //     context,
                      //     theme,
                      //     primaryColor,
                      //     secondaryColor,
                      //     warningColor,
                      //   ),
                      // ),
                    ],
                  );
                } else {
                  // Mobile: Single column layout
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildLeftColumnContent(
                        context,
                        theme,
                        primaryColor,
                        secondaryColor,
                        warningColor,
                        dangerColor,
                      ),
                      // const SizedBox(height: 24), // No longer needed as right column is empty
                      // _buildRightColumnContent( // Right column is now empty
                      //   context,
                      //   theme,
                      //   primaryColor,
                      //   secondaryColor,
                      //   warningColor,
                      // ),
                    ],
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLeftColumnContent(
    BuildContext context,
    ShadThemeData theme,
    Color primaryColor,
    Color secondaryColor,
    Color warningColor,
    Color dangerColor,
  ) {
    final bool useRowLayout =
        SolicitorResponsiveLayout.isTablet(context) ||
        SolicitorResponsiveLayout.isDesktop(context);

    final reportsCard = ShadCard(
      title: _buildSectionTitle(
        context,
        'Reports & Analytics',
        LucideIcons.activity,
      ),
      description: Text(
        'View application and claim analytics',
        style: theme.textTheme.p,
      ),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ShadButton(
            onPressed: () {
              Navigator.of(context).pushNamed(ReportsPage.routeName);
            },
            backgroundColor: primaryColor,
            child: const Row(
              children: [
                Text('View Reports'),
                SizedBox(width: 4),
                Icon(LucideIcons.externalLink, size: 16),
              ],
            ),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Column(
          children: [
            _buildReportItem(
              context,
              'Monthly Case Summary',
              'Generated on May 1, 2023',
              LucideIcons.chartPie,
              primaryColor,
            ),
            const SizedBox(height: 12),
            _buildReportItem(
              context,
              'Application Success Rate',
              'Last 6 months analysis',
              LucideIcons.layoutList,
              secondaryColor,
            ),
            const SizedBox(height: 12),
            _buildReportItem(
              context,
              'Financial Performance',
              'Q2 2023 Report',
              LucideIcons.activity,
              warningColor,
            ),
          ],
        ),
      ),
    );

    final notificationsCard = ShadCard(
      title: _buildSectionTitle(context, 'Notifications', LucideIcons.bell),
      description: Text('Recent updates and alerts', style: theme.textTheme.p),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ShadButton.outline(
            onPressed: () {
              Navigator.of(context).pushNamed('/notifications');
            },
            child: const Row(
              children: [
                Text('View All'),
                SizedBox(width: 4),
                Icon(LucideIcons.arrowRight, size: 16),
              ],
            ),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16.0),
        child: Consumer(
          builder: (context, ref, child) {
            final notificationService = ref.watch(notificationServiceProvider);

            return FutureBuilder(
              future: notificationService.initialize(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(24.0),
                      child: CircularProgressIndicator.adaptive(),
                    ),
                  );
                }

                return ValueListenableBuilder(
                  valueListenable: notificationService.notifications,
                  builder: (context, notifications, child) {
                    final recentNotifications = notifications.take(3).toList();

                    if (recentNotifications.isEmpty) {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Column(
                            children: [
                              Icon(
                                LucideIcons.bellOff,
                                size: 48,
                                color: theme.colorScheme.muted,
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'No notifications yet',
                                style: theme.textTheme.p.copyWith(
                                  color: theme.colorScheme.muted,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    return Column(
                      children:
                          recentNotifications.asMap().entries.map((entry) {
                            final index = entry.key;
                            final notification = entry.value;
                            return Column(
                              children: [
                                _buildNotificationItem(
                                  context,
                                  notification.message,
                                  _formatNotificationDate(notification.created),
                                  _getNotificationIcon(notification.type),
                                  _getNotificationColor(
                                    notification.type,
                                    primaryColor,
                                    secondaryColor,
                                    warningColor,
                                  ),
                                  notificationId: notification.id,
                                ),
                                if (index < recentNotifications.length - 1)
                                  const SizedBox(height: 12),
                              ],
                            );
                          }).toList(),
                    );
                  },
                );
              },
            );
          },
        ),
      ),
    );

    Widget reportsAndNotificationsContent;
    if (useRowLayout) {
      reportsAndNotificationsContent = Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(child: reportsCard),
          const SizedBox(width: 24), // Adjust spacing as needed
          Expanded(child: notificationsCard),
        ],
      );
    } else {
      reportsAndNotificationsContent = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [reportsCard, const SizedBox(height: 24), notificationsCard],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // PU Status - Only show if not approved
        if (_puStatus != 'approved') ...[
          _buildPUStatusCard(
            context,
            primaryColor,
            theme,
            warningColor,
            _puStatus,
          ),
          const SizedBox(height: 24),
        ],
        reportsAndNotificationsContent, // Add the new content here
      ],
    );
  }

  Widget _buildRightColumnContent(
    BuildContext context,
    ShadThemeData theme,
    Color primaryColor,
    Color secondaryColor,
    Color warningColor,
  ) {
    return const SizedBox.shrink(); // Right column is now empty
  }

  Widget _buildNavItem(
    BuildContext context,
    String title,
    IconData icon,
    bool isActive,
    Color primaryColor,
    VoidCallback onTap,
  ) {
    final theme = ShadTheme.of(context);
    final textColor = theme.colorScheme.foreground;
    final mutedColor = theme.colorScheme.muted;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isActive ? primaryColor.withAlpha(25) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ShadButton.ghost(
        onPressed: onTap, // Use the passed onTap callback
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Row(
          mainAxisSize: MainAxisSize.min, // Take only needed space
          children: [
            Icon(icon, size: 18, color: isActive ? primaryColor : mutedColor),
            const SizedBox(width: 12),
            Flexible(
              child: Text(
                title,
                style: TextStyle(
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  color: isActive ? primaryColor : textColor,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String value,
    String label,
    IconData icon,
    Color color, {
    VoidCallback? onTap,
  }) {
    final theme = ShadTheme.of(context);
    final textColor = theme.colorScheme.foreground;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withAlpha(25), // Use withAlpha instead of withOpacity
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withAlpha(50)),
        ),
        child: Row(
          crossAxisAlignment:
              CrossAxisAlignment.start, // Align to top for better layout
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: color.withAlpha(
                  50,
                ), // Use withAlpha instead of withOpacity
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              // Ensure text column takes available space
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                    overflow: TextOverflow.ellipsis, // Handle overflow
                  ),
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 14,
                      color: textColor.withAlpha(
                        180,
                      ), // Use withAlpha instead of withOpacity
                    ),
                    overflow: TextOverflow.ellipsis, // Handle overflow
                    maxLines: 1,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to get the appropriate message based on PU status
  String _getPUStatusMessage(String status) {
    switch (status.toLowerCase()) {
      case 'none':
        return 'You need to apply for PU status to access all platform features';
      case 'pending':
        return 'Your application is being reviewed. This typically takes 2-3 business days';
      case 'approved':
        return 'Your application has been approved. You have full access to all platform features';
      case 'rejected':
        return 'Your application was not approved. Please contact support for more information';
      default:
        return 'You need to apply for PU status to access all platform features';
    }
  }

  // Helper method to get the appropriate button text based on PU status
  String _getPUStatusButtonText(String status) {
    switch (status.toLowerCase()) {
      case 'none':
        return 'Apply for PU Status';
      case 'pending':
        return 'View Application';
      case 'approved':
        return 'View Status Details';
      case 'rejected':
        return 'Contact Support';
      default:
        return 'Apply for PU Status';
    }
  }

  // Helper method to format notification date
  String _formatNotificationDate(DateTime? date) {
    if (date == null) return 'Unknown';

    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return DateFormat('MMM d, y').format(date);
    }
  }

  // Helper method to get notification icon based on type
  IconData _getNotificationIcon(String type) {
    switch (type.toLowerCase()) {
      case 'success':
        return LucideIcons.check;
      case 'warning':
        return LucideIcons.activity; // Using activity as a fallback
      case 'error':
        return LucideIcons.activity; // Using activity as a fallback
      case 'info':
      default:
        return LucideIcons.info;
    }
  }

  // Helper method to get notification color based on type
  Color _getNotificationColor(
    String type,
    Color primaryColor,
    Color secondaryColor,
    Color warningColor,
  ) {
    switch (type.toLowerCase()) {
      case 'success':
        return Colors.green;
      case 'warning':
        return warningColor;
      case 'error':
        return Colors.red;
      case 'info':
      default:
        return primaryColor;
    }
  }

  Widget _buildPUStatusCard(
    BuildContext context,
    Color primaryColor,
    ShadThemeData theme,
    Color warningColor,
    String puStatus,
  ) {
    return ShadCard(
      title: _buildSectionTitle(
        context,
        'Permitted User (PU) Status',
        LucideIcons.shieldCheck,
      ),
      description: Text(
        'Check your current PU status or apply if you haven\'t already',
        style: theme.textTheme.p,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: warningColor.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(LucideIcons.info, color: warningColor, size: 24),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'PU Status: ${_puStatus.substring(0, 1).toUpperCase() + _puStatus.substring(1)}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: warningColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getPUStatusMessage(_puStatus),
                          style: TextStyle(color: warningColor, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.muted.withOpacity(
                  0.1,
                ), // theme.colorScheme should now work
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.muted.withOpacity(0.2),
                ), // theme.colorScheme should now work
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    LucideIcons.info,
                    color: theme.colorScheme.muted,
                    size: 24,
                  ), // theme.colorScheme should now work
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'What is PU Status?',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color:
                                theme
                                    .colorScheme
                                    .foreground, // theme.colorScheme should now work
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Permitted User (PU) status allows solicitors to submit funding applications and access advanced platform features.',
                          style: TextStyle(
                            color: theme.colorScheme.foreground.withOpacity(
                              0.8,
                            ), // theme.colorScheme should now work
                            fontSize: 14,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ShadButton(
            onPressed: () {
              Navigator.of(context).pushNamed(PUApplicationPage.routeName);
            },
            child: SizedBox(
              width: 150, // Fixed width to prevent overflow
              child: Row(
                mainAxisSize: MainAxisSize.min, // Take only needed space
                children: [
                  Icon(LucideIcons.shieldCheck, size: 16),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      _getPUStatusButtonText(_puStatus),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            backgroundColor: primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title, IconData icon) {
    final theme = ShadTheme.of(context);
    return Row(
      children: [
        Icon(icon, size: 20, color: theme.colorScheme.foreground),
        const SizedBox(width: 8),
        Text(
          title,
          style: theme.textTheme.h4.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildApplicationCard(
    BuildContext context,
    String title,
    String status,
    String date,
    Color statusColor,
  ) {
    final theme = ShadTheme.of(context);
    final cardBgColor = theme.colorScheme.card;
    final borderColor = theme.colorScheme.border;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardBgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.large.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  status,
                  style: TextStyle(
                    color: statusColor,
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            date,
            style: TextStyle(color: theme.colorScheme.muted, fontSize: 14),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                // Ensure document info takes available space
                child: Row(
                  children: [
                    Icon(
                      LucideIcons.fileText,
                      size: 16,
                      color: theme.colorScheme.muted,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '5 documents',
                      style: TextStyle(
                        color: theme.colorScheme.muted,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              ShadButton.link(
                onPressed: () {
                  // View application details
                },
                child: Row(
                  children: [
                    const Text('View Details'),
                    const SizedBox(width: 4),
                    Icon(LucideIcons.arrowRight, size: 14),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedCaseCard(
    BuildContext context,
    String title,
    String status,
    String lastUpdated,
    double progressValue,
    Color progressColor,
  ) {
    final theme = ShadTheme.of(context);
    final cardBgColor = theme.colorScheme.card;
    final borderColor = theme.colorScheme.border;
    final mutedColor = theme.colorScheme.muted;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardBgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.large.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: progressColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  status,
                  style: TextStyle(
                    color: progressColor,
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Flexible(
                // Allow lastUpdated text to wrap or truncate
                child: Text(
                  lastUpdated,
                  style: TextStyle(color: mutedColor, fontSize: 12),
                ),
              ),
            ], // Closes children list of Row starting at line 992
          ), // Closes Row widget starting at line 991
          const SizedBox(height: 16),
          Row(
            children: [
              Text(
                'Case Progress',
                style: TextStyle(
                  color: theme.colorScheme.foreground.withOpacity(0.8),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${(progressValue * 100).toInt()}%',
                style: TextStyle(
                  color: progressColor,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: progressValue,
              backgroundColor: theme.colorScheme.muted.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
              minHeight: 8,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                // Ensure claimant info takes available space
                child: Row(
                  children: [
                    Icon(LucideIcons.users, size: 16, color: mutedColor),
                    const SizedBox(width: 4),
                    Text(
                      '12 claimants',
                      style: TextStyle(color: mutedColor, fontSize: 14),
                    ),
                  ],
                ),
              ),
              ShadButton.link(
                onPressed: () {
                  // View case details
                },
                child: Row(
                  children: [
                    const Text('View Details'),
                    const SizedBox(width: 4),
                    Icon(LucideIcons.arrowRight, size: 14),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentItem(
    BuildContext context,
    String title,
    String fileType,
    String fileSize,
    String uploadDate,
    IconData icon,
    Color iconColor,
  ) {
    final theme = ShadTheme.of(context);
    final mutedColor = theme.colorScheme.muted;
    final bgColor =
        theme.brightness == Brightness.light
            ? Colors.grey[50]
            : theme.colorScheme.background;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: iconColor, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.foreground,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.muted.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        fileType,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.muted,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      fileSize,
                      style: TextStyle(fontSize: 12, color: mutedColor),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '•',
                      style: TextStyle(color: mutedColor.withOpacity(0.5)),
                    ),
                    const SizedBox(width: 8),
                    Flexible(
                      // Allow uploadDate to wrap or truncate
                      child: Text(
                        uploadDate,
                        style: TextStyle(fontSize: 12, color: mutedColor),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          ShadButton.ghost(
            onPressed: () {
              // Download document
            },
            size: ShadButtonSize.sm,
            child: Icon(LucideIcons.download, size: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildReportItem(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color iconColor,
  ) {
    final theme = ShadTheme.of(context);
    final bgColor =
        theme.brightness == Brightness.light
            ? Colors.grey[50]
            : theme.colorScheme.background;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: iconColor, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.foreground,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.colorScheme.muted,
                  ),
                ),
              ],
            ),
          ),
          ShadButton.ghost(
            onPressed: () {
              // View report
            },
            size: ShadButtonSize.sm,
            child: Icon(LucideIcons.externalLink, size: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationItem(
    BuildContext context,
    String message,
    String timestamp,
    IconData icon,
    Color iconColor, {
    String? notificationId,
  }) {
    final theme = ShadTheme.of(context);
    final bgColor =
        theme.brightness == Brightness.light
            ? Colors.grey[50]
            : theme.colorScheme.background;

    return GestureDetector(
      onTap:
          notificationId != null
              ? () {
                Navigator.of(context).pushNamed(
                  detail.NotificationDetailPage.routeName,
                  arguments: notificationId,
                );
              }
              : null,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: theme.colorScheme.border),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: iconColor, size: 16),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: theme.colorScheme.foreground,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    timestamp,
                    style: TextStyle(
                      fontSize: 12,
                      color: theme.colorScheme.muted,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
