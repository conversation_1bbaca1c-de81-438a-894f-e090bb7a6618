import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/toast_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/ui/widgets/document_preview_widget.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/notification_integration_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/user_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/claim_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/professional_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/solicitor_profile_model.dart'; // Import SolicitorProfileModel
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart'; // Import for DocumentVersion and UploadedDocumentCategory
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/services/claim_documents_service.dart'; // Import ClaimDocumentsService
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/models/claimant_profile_model.dart'; // Import ClaimantProfile
import 'dart:async';
import 'package:file_picker/file_picker.dart'; // For file selection
import 'package:http/http.dart' as http; // For MultipartFile
import 'package:url_launcher/url_launcher.dart'; // For launching URLs
import 'package:flutter/gestures.dart'; // For TapGestureRecognizer in ExpandableText

class EditFundingApplicationPage extends ConsumerStatefulWidget {
  static const String routeName = '/solicitor/edit-funding-application';
  final String fundingApplicationId;

  const EditFundingApplicationPage({
    super.key,
    required this.fundingApplicationId,
  });

  @override
  ConsumerState<EditFundingApplicationPage> createState() =>
      _EditFundingApplicationPageState();
}

class _EditFundingApplicationPageState
    extends ConsumerState<EditFundingApplicationPage> {
  Claim? _claimDetails;
  bool _isLoading = true;
  String? _errorMessage;
  bool _isSaving = false;
  bool _showSuccessMessage = false;
  bool _isDownloading = false;

  // Document service
  late final ClaimDocumentsService _claimDocumentsService;

  // Form controllers
  final TextEditingController _caseTitleController = TextEditingController();
  final TextEditingController _caseSummaryController = TextEditingController();
  final TextEditingController _reviewNotesController = TextEditingController();

  // Stage dropdown value
  String? _selectedStage;

  // Professionals search
  Timer? _debounce;
  List<ProfessionalModel> _barristersSearchResults = [];
  List<ProfessionalModel> _expertsSearchResults = [];
  bool _isSearchingBarristers = false;
  bool _isSearchingExperts = false;
  final TextEditingController _barristersSearchController =
      TextEditingController();
  final TextEditingController _expertsSearchController =
      TextEditingController();

  // Selected professionals
  List<Barrister> _selectedBarristers = [];
  List<Expert> _selectedExperts = [];

  // Solicitors Tab State
  bool _isCurrentUserClaimAdmin = false;
  List<SolicitorProfileModel> _associatedSolicitors =
      []; // Will need to fetch email separately or create a combined model
  List<SolicitorProfileModel> _solicitorSearchResultsList = [];
  final TextEditingController _solicitorsSearchController =
      TextEditingController();
  bool _isSearchingSolicitors = false;
  String? _currentSolicitorProfileId;

  // Claimants Tab State
  final TextEditingController _claimantsEmailController =
      TextEditingController();
  List<ClaimantProfile> _linkedClaimants = [];
  bool _isAddingClaimant = false;

  // User name resolution cache
  final Map<String, String> _resolvedUserNames = {};

  @override
  void initState() {
    super.initState();
    _claimDocumentsService = ClaimDocumentsService();
    _fetchClaimDetails();
  }

  /// Resolve user ID to display name
  Future<String> _resolveUserName(String userId) async {
    // Check cache first
    if (_resolvedUserNames.containsKey(userId)) {
      return _resolvedUserNames[userId]!;
    }

    try {
      final displayName = await UserService.getUserDisplayName(userId);
      _resolvedUserNames[userId] = displayName;
      return displayName;
    } catch (e) {
      LoggerService.warning('Failed to resolve user name for $userId: $e');
      final fallbackName = 'User ($userId)';
      _resolvedUserNames[userId] = fallbackName;
      return fallbackName;
    }
  }

  @override
  void dispose() {
    _caseTitleController.dispose();
    _caseSummaryController.dispose();
    _reviewNotesController.dispose();
    _barristersSearchController.dispose();
    _expertsSearchController.dispose();
    _solicitorsSearchController.dispose(); // Dispose new controller
    _claimantsEmailController.dispose(); // Dispose claimants controller
    _debounce?.cancel();
    super.dispose();
  }

  Future<void> _fetchCurrentSolicitorProfileId() async {
    final pbClient = PocketBaseService().pb;
    final currentUser = pbClient.authStore.record;
    if (currentUser == null) return;

    try {
      final profileRecords = await pbClient
          .collection('solicitor_profiles')
          .getList(
            filter: 'user_id = "${currentUser.id}"',
            perPage: 1,
          ); // Corrected: maxItems to perPage
      if (profileRecords.items.isNotEmpty) {
        if (!mounted) return;
        setState(() {
          _currentSolicitorProfileId = profileRecords.items.first.id;
        });
      }
    } catch (e) {
      debugPrint('Error fetching current solicitor profile ID: $e');
    }
  }

  Future<void> _fetchClaimDetails() async {
    if (!mounted) return;
    await _fetchCurrentSolicitorProfileId(); // Fetch current solicitor profile ID first
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final pbClient = PocketBaseService().pb;

      final record = await pbClient
          .collection('funding_applications')
          .getOne(
            widget.fundingApplicationId,
            expand:
                'barristers,experts,claim_admins.user_id,associated_solicitors.user_id',
          );

      // Debug: Print raw record data
      debugPrint('Raw funding application record: ${record.data}');
      debugPrint('Expanded data: ${record.expand}');

      // Check if barristers field exists and has data
      if (record.data.containsKey('barristers') &&
          record.data['barristers'] != null) {
        debugPrint(
          'Barristers field exists with data: ${record.data['barristers']}',
        );

        // Try to directly fetch barristers to see if they exist in the database
        if (record.data['barristers'] is List &&
            (record.data['barristers'] as List).isNotEmpty) {
          try {
            // Get the first barrister ID to check if it exists
            final firstBarristerId =
                (record.data['barristers'] as List).first.toString();
            debugPrint(
              'Checking if barrister with ID $firstBarristerId exists...',
            );

            final barristerCheck = await pbClient
                .collection('barristers')
                .getOne(firstBarristerId);

            debugPrint('Barrister exists: ${barristerCheck.data}');
          } catch (e) {
            debugPrint('Error checking barrister: $e');
          }
        }
      } else {
        debugPrint('No barristers field found in the record data');
      }

      // Fetch documents from claim_documents collection only
      List<UploadedDocumentCategory> documentRepository = [];
      try {
        debugPrint(
          'EditFundingApplicationPage: Fetching documents for funding application: ${widget.fundingApplicationId}',
        );

        // Debug: Check what's in the claim_documents collection
        await _claimDocumentsService.debugClaimDocumentsCollection(
          widget.fundingApplicationId,
        );

        // Fetch documents exclusively from claim_documents collection
        documentRepository = await _claimDocumentsService
            .getDocumentsForFundingApplication(widget.fundingApplicationId);

        debugPrint(
          'EditFundingApplicationPage: Fetched ${documentRepository.length} document categories from claim_documents collection',
        );

        // Debug: Print details of each fetched category
        for (int i = 0; i < documentRepository.length; i++) {
          final category = documentRepository[i];
          debugPrint(
            'EditFundingApplicationPage: Category $i - logicalName: ${category.logicalName}',
          );
          debugPrint(
            'EditFundingApplicationPage: Category $i - currentVersionFileId: ${category.currentVersionFileId}',
          );
          debugPrint(
            'EditFundingApplicationPage: Category $i - versions count: ${category.versions.length}',
          );
          for (int j = 0; j < category.versions.length; j++) {
            final version = category.versions[j];
            debugPrint(
              'EditFundingApplicationPage: Category $i, Version $j - fileId: ${version.fileId}, filename: ${version.filename}',
            );
          }
        }
      } catch (e) {
        debugPrint(
          'EditFundingApplicationPage: Error fetching documents from claim_documents collection: $e',
        );
        debugPrint(
          'EditFundingApplicationPage: Error stack trace: ${StackTrace.current}',
        );
        // Continue with empty list if there's an error
        documentRepository = [];
      }

      // Convert funding application record to Claim model
      final Map<String, dynamic> claimData = {
        'id': record.id,
        'funding_application_id': record.id,
        'case_title': record.data['claim_title'] ?? 'N/A',
        'case_summary_public': record.data['case_summary_public'] ?? '',
        'current_status': record.data['application_status'],
        'status_updates': record.data['status_updates'] ?? [],
        'document_repository':
            documentRepository.map((c) => c.toJson()).toList(),
        'barristers': record.data['barristers'] ?? [],
        'experts': record.data['experts'] ?? [],
        'linked_barristers':
            record.data['barristers'] ??
            [], // Make sure we're using the correct field name
        'linked_experts':
            record.data['experts'] ??
            [], // Make sure we're using the correct field name
        'claim_admins': record.data['claim_admins'] ?? [], // New
        'associated_solicitors':
            record.data['associated_solicitors'] ?? [], // New
        'created': record.data['created'] ?? DateTime.now().toIso8601String(),
        'updated': record.data['updated'] ?? DateTime.now().toIso8601String(),
      };

      debugPrint(
        'EditFundingApplicationPage: Creating Claim object with document_repository containing ${(claimData['document_repository'] as List).length} items',
      );

      _claimDetails = Claim.fromJson(claimData);

      debugPrint(
        'EditFundingApplicationPage: Created Claim object with ${_claimDetails!.documentRepository.length} document categories',
      );

      // Determine if current user is a claim admin
      if (_currentSolicitorProfileId != null &&
          _claimDetails!.claimAdminIds.contains(_currentSolicitorProfileId)) {
        _isCurrentUserClaimAdmin = true;
      }
      debugPrint('Is current user claim admin: $_isCurrentUserClaimAdmin');

      // Fetch missing admin profiles if needed
      await _fetchMissingAdminProfiles();

      // Populate _associatedSolicitors with expanded data
      try {
        // Debug the associated_solicitors data
        debugPrint('Processing associated_solicitors data');

        // Get the associated_solicitors data
        final List<dynamic> associatedSolicitorsData = record.get<List>(
          'associated_solicitors',
        );
        debugPrint(
          'associatedSolicitorsData length: ${associatedSolicitorsData.length}',
        );

        // Process each item with careful type checking
        final List<SolicitorProfileModel> processedSolicitors = [];

        for (var data in associatedSolicitorsData) {
          try {
            debugPrint(
              'Processing solicitor data: $data (type: ${data.runtimeType})',
            );

            // Handle different data types
            Map<String, dynamic> solicitorProfileData;

            if (data is Map<String, dynamic>) {
              solicitorProfileData = data;
            } else if (data is String) {
              // If it's just an ID string, we need to fetch the full record
              debugPrint('Solicitor data is a String ID: $data');
              try {
                final solicitorRecord = await pbClient
                    .collection('solicitor_profiles')
                    .getOne(data);
                solicitorProfileData = solicitorRecord.toJson();
                debugPrint('Fetched solicitor profile: $solicitorProfileData');
              } catch (e) {
                debugPrint('Error fetching solicitor profile for ID $data: $e');
                continue; // Skip this item
              }
            } else {
              debugPrint(
                'Unsupported data type for solicitor: ${data.runtimeType}',
              );
              continue; // Skip this item
            }

            // Now process the expand data if it exists
            Map<String, dynamic>? userData;
            if (solicitorProfileData.containsKey('expand') &&
                solicitorProfileData['expand'] != null) {
              var expandData = solicitorProfileData['expand'];
              debugPrint('Expand data type: ${expandData.runtimeType}');

              if (expandData is Map<String, dynamic> &&
                  expandData.containsKey('user_id') &&
                  expandData['user_id'] != null) {
                if (expandData['user_id'] is Map<String, dynamic>) {
                  userData = expandData['user_id'] as Map<String, dynamic>;
                  debugPrint('Found user data: $userData');
                } else {
                  debugPrint('user_id is not a Map: ${expandData['user_id']}');
                }
              }
            }

            // Create a clean copy of the data for the model
            final Map<String, dynamic> cleanData = {...solicitorProfileData};

            // Add user email and name if available
            if (userData != null) {
              if (userData.containsKey('email')) {
                cleanData['userEmail'] = userData['email'].toString();
              }
              if (userData.containsKey('name')) {
                cleanData['userName'] = userData['name'].toString();
              }
            }

            // Create the model
            final solicitorModel = SolicitorProfileModel.fromJson(cleanData);
            processedSolicitors.add(solicitorModel);
          } catch (e) {
            debugPrint('Error processing solicitor data: $e');
            // Continue with the next item
          }
        }

        _associatedSolicitors = processedSolicitors;
      } catch (e) {
        debugPrint('Error processing associated solicitors: $e');
        _associatedSolicitors = []; // Use an empty list if there's an error
      }
      debugPrint(
        'Fetched associated solicitors: ${_associatedSolicitors.length}',
      );

      // Fetch barristers and experts if they exist
      debugPrint('Linked barrister IDs: ${_claimDetails!.linkedBarristerIds}');

      if (_claimDetails!.linkedBarristerIds.isNotEmpty) {
        try {
          // Try a different approach - fetch all barristers and filter in memory
          debugPrint('Fetching all barristers and filtering in memory');

          final allBarristersResult = await pbClient
              .collection('barristers')
              .getList(sort: 'barrister_with_conduct');

          debugPrint(
            'Total barristers in database: ${allBarristersResult.items.length}',
          );

          // Filter barristers that are linked to this claim
          final linkedBarristers =
              allBarristersResult.items
                  .where(
                    (item) =>
                        _claimDetails!.linkedBarristerIds.contains(item.id),
                  )
                  .toList();

          debugPrint('Filtered barristers: ${linkedBarristers.length}');

          _selectedBarristers =
              linkedBarristers
                  .map((item) => Barrister.fromJson(item.toJson()))
                  .toList();

          debugPrint('Selected barristers: ${_selectedBarristers.length}');
          for (var barrister in _selectedBarristers) {
            debugPrint('Barrister: ${barrister.name}, ID: ${barrister.id}');
          }

          // If no barristers were found, try the original approach with filter query
          if (_selectedBarristers.isEmpty) {
            debugPrint(
              'No barristers found with memory filtering, trying filter query',
            );

            final filterQuery = _claimDetails!.linkedBarristerIds
                .map((id) => 'id = "$id"')
                .join(' || ');

            debugPrint('Barrister filter query: $filterQuery');

            final barristersResult = await pbClient
                .collection('barristers')
                .getList(filter: filterQuery);

            debugPrint(
              'Barristers result: ${barristersResult.items.length} items found',
            );

            _selectedBarristers =
                barristersResult.items
                    .map((item) => Barrister.fromJson(item.toJson()))
                    .toList();

            debugPrint(
              'Selected barristers (after filter query): ${_selectedBarristers.length}',
            );
          }
        } catch (e) {
          debugPrint('Error fetching barristers: $e');
          // Continue with empty list if there's an error
          _selectedBarristers = [];
        }
      } else {
        debugPrint('No linked barristers found in the claim data');
      }

      if (_claimDetails!.linkedExpertIds.isNotEmpty) {
        try {
          debugPrint('Linked expert IDs: ${_claimDetails!.linkedExpertIds}');

          // Try a different approach - fetch all experts and filter in memory
          debugPrint('Fetching all experts and filtering in memory');

          final allExpertsResult = await pbClient
              .collection('experts')
              .getList(sort: 'name');

          debugPrint(
            'Total experts in database: ${allExpertsResult.items.length}',
          );

          // Filter experts that are linked to this claim
          final linkedExperts =
              allExpertsResult.items
                  .where(
                    (item) => _claimDetails!.linkedExpertIds.contains(item.id),
                  )
                  .toList();

          debugPrint('Filtered experts: ${linkedExperts.length}');

          _selectedExperts =
              linkedExperts
                  .map((item) => Expert.fromJson(item.toJson()))
                  .toList();

          debugPrint('Selected experts: ${_selectedExperts.length}');
          for (var expert in _selectedExperts) {
            debugPrint('Expert: ${expert.name}, ID: ${expert.id}');
          }

          // If no experts were found, try the original approach with filter query
          if (_selectedExperts.isEmpty) {
            debugPrint(
              'No experts found with memory filtering, trying filter query',
            );

            final filterQuery = _claimDetails!.linkedExpertIds
                .map((id) => 'id = "$id"')
                .join(' || ');

            debugPrint('Expert filter query: $filterQuery');

            final expertsResult = await pbClient
                .collection('experts')
                .getList(filter: filterQuery);

            debugPrint(
              'Experts result: ${expertsResult.items.length} items found',
            );

            _selectedExperts =
                expertsResult.items
                    .map((item) => Expert.fromJson(item.toJson()))
                    .toList();

            debugPrint(
              'Selected experts (after filter query): ${_selectedExperts.length}',
            );
          }
        } catch (e) {
          debugPrint('Error fetching experts: $e');
          // Continue with empty list if there's an error
          _selectedExperts = [];
        }
      } else {
        debugPrint('No linked experts found in the claim data');
      }

      // Fetch claimants associated with this funding application
      try {
        debugPrint(
          'Fetching claimants for funding application: ${widget.fundingApplicationId}',
        );

        final claimantsResult = await pbClient
            .collection('claimant_profiles')
            .getList(
              filter: 'associated_claim_ids ~ "${widget.fundingApplicationId}"',
              expand: 'user_id',
            );

        _linkedClaimants =
            claimantsResult.items
                .map((item) => ClaimantProfile.fromJson(item.toJson()))
                .toList();

        debugPrint('Fetched ${_linkedClaimants.length} claimants');
      } catch (e) {
        debugPrint('Error fetching claimants: $e');
        _linkedClaimants = [];
      }

      // Initialize form controllers
      _caseTitleController.text = _claimDetails!.caseTitle;
      _caseSummaryController.text = _claimDetails!.caseSummaryPublic ?? '';
      _selectedStage = record.data['stage'] as String?;
      _reviewNotesController.text =
          ''; // Initialize with empty string, will be updated when saving

      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
    } on ClientException catch (e, stackTrace) {
      if (!mounted) return;
      debugPrint('ClientException in _fetchClaimDetails: ${e.toString()}');
      debugPrint('StackTrace: ${stackTrace.toString()}');
      NotificationService.showError(
        context,
        'Failed to load claim details: ${e.response['message'] ?? e.toString()}',
      );
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error: ${e.response['message'] ?? e.toString()}';
      });
    } catch (e, stackTrace) {
      if (!mounted) return;
      debugPrint('Exception in _fetchClaimDetails: ${e.toString()}');
      debugPrint('StackTrace: ${stackTrace.toString()}');
      NotificationService.showError(
        context,
        'An unexpected error occurred: ${e.toString()}',
      );
      setState(() {
        _isLoading = false;
        _errorMessage = 'An unexpected error occurred: ${e.toString()}';
      });
    }
  }

  Future<void> _searchBarristers(String query) async {
    if (query.isEmpty) {
      setState(() {
        _barristersSearchResults = [];
        _isSearchingBarristers = false;
      });
      return;
    }

    setState(() {
      _isSearchingBarristers = true;
    });

    try {
      final pbClient = PocketBaseService().pb;
      final result = await pbClient
          .collection('barristers')
          .getList(
            filter:
                'barrister_with_conduct ~ "$query" || barrister_chambers ~ "$query" || email ~ "$query"',
            sort: 'barrister_with_conduct',
          );

      if (!mounted) return;
      setState(() {
        _barristersSearchResults =
            result.items
                .map((item) => ProfessionalModel.fromJson(item.toJson()))
                .toList();
        _isSearchingBarristers = false;
      });
    } catch (e) {
      debugPrint('Error searching barristers: $e');
      if (!mounted) return;
      setState(() {
        _barristersSearchResults = [];
        _isSearchingBarristers = false;
      });
    }
  }

  Future<void> _searchExperts(String query) async {
    if (query.isEmpty) {
      setState(() {
        _expertsSearchResults = [];
        _isSearchingExperts = false;
      });
      return;
    }

    setState(() {
      _isSearchingExperts = true;
    });

    try {
      final pbClient = PocketBaseService().pb;
      final result = await pbClient
          .collection('experts')
          .getList(
            filter:
                'name ~ "$query" || firm_name ~ "$query" || email ~ "$query" || type ~ "$query"',
            sort: 'name',
          );

      if (!mounted) return;
      setState(() {
        _expertsSearchResults =
            result.items
                .map((item) => ProfessionalModel.fromJson(item.toJson()))
                .toList();
        _isSearchingExperts = false;
      });
    } catch (e) {
      debugPrint('Error searching experts: $e');
      if (!mounted) return;
      setState(() {
        _expertsSearchResults = [];
        _isSearchingExperts = false;
      });
    }
  }

  // Update the claims field in a barrister record
  Future<void> _updateBarristerClaims(
    String barristerId,
    String fundingApplicationId,
  ) async {
    try {
      // Use PocketBase's special syntax for relation fields to append the new funding application ID
      await PocketBaseService().pb
          .collection('barristers')
          .update(barristerId, body: {'claims+': fundingApplicationId});
    } catch (e) {
      // Handle error - using a logger would be better in production
      debugPrint('Error updating barrister claims: ${e.toString()}');
      if (mounted) {
        NotificationService.showError(
          context,
          'Error updating barrister claims: ${e.toString()}',
        );
      }
    }
  }

  // Remove a funding application from a barrister's claims
  Future<void> _removeClaimFromBarrister(
    String barristerId,
    String fundingApplicationId,
  ) async {
    try {
      debugPrint(
        'Removing funding application $fundingApplicationId from barrister $barristerId',
      );

      // First, update the funding_applications collection to remove the barrister
      await PocketBaseService().pb
          .collection('funding_applications')
          .update(fundingApplicationId, body: {'barristers-': barristerId});

      // Then, update the barrister's claims field
      await PocketBaseService().pb
          .collection('barristers')
          .update(barristerId, body: {'-claims': fundingApplicationId});

      debugPrint('Successfully removed barrister-claim relationship');
    } catch (e) {
      // Handle error - using a logger would be better in production
      debugPrint('Error removing claim from barrister: ${e.toString()}');
      if (mounted) {
        NotificationService.showError(
          context,
          'Error removing claim from barrister: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _addBarrister(ProfessionalModel barrister) async {
    // Check if already selected
    if (_selectedBarristers.any((b) => b.id == barrister.id)) {
      NotificationService.showInfo(
        context,
        'This barrister is already added to the claim.',
      );
      return;
    }

    try {
      // First update the funding application to add the barrister
      final pbClient = PocketBaseService().pb;
      await pbClient
          .collection('funding_applications')
          .update(
            widget.fundingApplicationId,
            body: {
              'barristers+': [barrister.id],
            },
          );

      // Then update the barrister record to add the claim
      await _updateBarristerClaims(barrister.id, widget.fundingApplicationId);

      // Refresh the claim details to get the updated data
      await _fetchClaimDetails();

      // Show success notification
      if (mounted) {
        NotificationService.showSuccess(
          context,
          '${barrister.name} added to claim',
        );
      }
    } catch (e) {
      debugPrint('Error adding barrister: $e');
      if (mounted) {
        NotificationService.showError(context, 'Failed to add barrister: $e');
      }
    } finally {
      // Clear search results
      setState(() {
        _barristersSearchResults = [];
        _barristersSearchController.clear();
      });
    }
  }

  // Update the claims field in an expert record
  Future<void> _updateExpertClaims(
    String expertId,
    String fundingApplicationId,
  ) async {
    try {
      // Use PocketBase's special syntax for relation fields to append the new funding application ID
      await PocketBaseService().pb
          .collection('experts')
          .update(expertId, body: {'claims+': fundingApplicationId});
    } catch (e) {
      // Handle error - using a logger would be better in production
      debugPrint('Error updating expert claims: ${e.toString()}');
      if (mounted) {
        NotificationService.showError(
          context,
          'Error updating expert claims: ${e.toString()}',
        );
      }
    }
  }

  // Remove a funding application from an expert's claims
  Future<void> _removeClaimFromExpert(
    String expertId,
    String fundingApplicationId,
  ) async {
    try {
      debugPrint(
        'Removing funding application $fundingApplicationId from expert $expertId',
      );

      // First, update the funding_applications collection to remove the expert
      await PocketBaseService().pb
          .collection('funding_applications')
          .update(fundingApplicationId, body: {'experts-': expertId});

      // Then, update the expert's claims field
      await PocketBaseService().pb
          .collection('experts')
          .update(expertId, body: {'-claims': fundingApplicationId});

      debugPrint('Successfully removed expert-claim relationship');
    } catch (e) {
      // Handle error - using a logger would be better in production
      debugPrint('Error removing claim from expert: ${e.toString()}');
      if (mounted) {
        NotificationService.showError(
          context,
          'Error removing claim from expert: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _addExpert(ProfessionalModel expert) async {
    // Check if already selected
    if (_selectedExperts.any((e) => e.id == expert.id)) {
      NotificationService.showInfo(
        context,
        'This expert is already added to the claim.',
      );
      return;
    }

    try {
      // First update the funding application to add the expert
      final pbClient = PocketBaseService().pb;
      await pbClient
          .collection('funding_applications')
          .update(
            widget.fundingApplicationId,
            body: {
              'experts+': [expert.id],
            },
          );

      // Then update the expert record to add the claim
      await _updateExpertClaims(expert.id, widget.fundingApplicationId);

      // Refresh the claim details to get the updated data
      await _fetchClaimDetails();

      // Show success notification
      if (mounted) {
        NotificationService.showSuccess(
          context,
          '${expert.name} added to claim',
        );
      }
    } catch (e) {
      debugPrint('Error adding expert: $e');
      if (mounted) {
        NotificationService.showError(context, 'Failed to add expert: $e');
      }
    } finally {
      // Clear search results
      setState(() {
        _expertsSearchResults = [];
        _expertsSearchController.clear();
      });
    }
  }

  Future<void> _removeBarrister(String barristerId) async {
    // Find the barrister name before removing it from the list
    final barrister = _selectedBarristers.firstWhere(
      (b) => b.id == barristerId,
      orElse:
          () => Barrister(
            id: barristerId,
            name: 'Unknown Barrister',
            claimIds: [],
            created: DateTime.now(),
            updated: DateTime.now(),
          ),
    );

    // Store the name for later use
    final barristerName = barrister.name;

    try {
      // Remove the funding application from the barrister's claims
      await _removeClaimFromBarrister(barristerId, widget.fundingApplicationId);

      // Refresh the claim details to get the updated data
      await _fetchClaimDetails();

      // Show success notification if widget is still mounted
      if (mounted) {
        NotificationService.showSuccess(
          context,
          '$barristerName removed from claim',
        );
      }
    } catch (e) {
      debugPrint('Error in _removeBarrister: ${e.toString()}');

      if (mounted) {
        NotificationService.showError(
          context,
          'Failed to remove $barristerName from claim: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _removeExpert(String expertId) async {
    // Find the expert name before removing it from the list
    final expert = _selectedExperts.firstWhere(
      (e) => e.id == expertId,
      orElse:
          () => Expert(
            id: expertId,
            name: 'Unknown Expert',
            claimIds: [],
            created: DateTime.now(),
            updated: DateTime.now(),
          ),
    );

    // Store the name for later use
    final expertName = expert.name;

    try {
      // Remove the funding application from the expert's claims
      await _removeClaimFromExpert(expertId, widget.fundingApplicationId);

      // Refresh the claim details to get the updated data
      await _fetchClaimDetails();

      // Show success notification if widget is still mounted
      if (mounted) {
        NotificationService.showSuccess(
          context,
          '$expertName removed from claim',
        );
      }
    } catch (e) {
      debugPrint('Error in _removeExpert: ${e.toString()}');

      if (mounted) {
        NotificationService.showError(
          context,
          'Failed to remove $expertName from claim: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _saveChanges() async {
    debugPrint('=== _saveChanges method called ===');
    if (_claimDetails == null) return;

    setState(() {
      _isSaving = true;
    });

    debugPrint('=== Starting save process ===');
    try {
      final pbClient = PocketBaseService().pb;
      final authRecord = pbClient.authStore.record;
      String currentUserId;
      String currentUserName = 'Unknown User';

      if (authRecord is RecordModel) {
        currentUserId = authRecord.id;
        currentUserName =
            authRecord.data['name'] as String? ?? 'User ($currentUserId)';
      } else {
        NotificationService.showError(
          context,
          'User not authenticated. Cannot save changes.',
        );
        setState(() {
          _isSaving = false;
        });
        return;
      }

      // Create a status update for the edit
      final newUpdate = StatusUpdate(
        date: DateTime.now(),
        description: 'Claim details updated by solicitor',
        updatedByUserId: currentUserId,
        updatedByName: currentUserName,
      );

      // Handle existing status updates properly
      debugPrint('Starting status updates processing...');
      debugPrint(
        '_claimDetails!.statusUpdates type: ${_claimDetails!.statusUpdates.runtimeType}',
      );
      debugPrint(
        '_claimDetails!.statusUpdates length: ${_claimDetails!.statusUpdates.length}',
      );

      List<StatusUpdate> existingUpdates = [];
      try {
        if (_claimDetails!.statusUpdates.isNotEmpty) {
          debugPrint('Attempting to create List<StatusUpdate>.from()...');
          existingUpdates = List<StatusUpdate>.from(
            _claimDetails!.statusUpdates,
          );
          debugPrint('Successfully created existing updates list');
        }
      } catch (e) {
        debugPrint('Error creating existing updates list: $e');
        // If there's an error, just use an empty list
        existingUpdates = [];
      }

      List<StatusUpdate> updatedStatusUpdates = List<StatusUpdate>.from(
        existingUpdates,
      )..add(newUpdate);

      debugPrint('Existing status updates count: ${existingUpdates.length}');
      debugPrint(
        'Updated status updates count: ${updatedStatusUpdates.length}',
      );

      // We'll use the updated values directly in the update call

      // Get the current barristers and experts from the funding application
      final currentRecord = await pbClient
          .collection('funding_applications')
          .getOne(_claimDetails!.id);

      final List<String> currentBarristerIds = [];
      if (currentRecord.data.containsKey('barristers') &&
          currentRecord.data['barristers'] != null) {
        final barristersData = currentRecord.data['barristers'];
        if (barristersData is List) {
          currentBarristerIds.addAll(barristersData.map((e) => e.toString()));
        } else if (barristersData is String && barristersData.isNotEmpty) {
          currentBarristerIds.add(barristersData);
        }
      }

      final List<String> currentExpertIds = [];
      if (currentRecord.data.containsKey('experts') &&
          currentRecord.data['experts'] != null) {
        final expertsData = currentRecord.data['experts'];
        if (expertsData is List) {
          currentExpertIds.addAll(expertsData.map((e) => e.toString()));
        } else if (expertsData is String && expertsData.isNotEmpty) {
          currentExpertIds.add(expertsData);
        }
      }

      // Determine which barristers and experts to add and remove
      final Set<String> selectedBarristerIds =
          _selectedBarristers.map((b) => b.id).toSet();
      final Set<String> selectedExpertIds =
          _selectedExperts.map((e) => e.id).toSet();

      // Barristers to add and remove
      final barristersToAdd = selectedBarristerIds.difference(
        currentBarristerIds.toSet(),
      );
      final barristersToRemove = currentBarristerIds.toSet().difference(
        selectedBarristerIds,
      );

      // Experts to add and remove
      final expertsToAdd = selectedExpertIds.difference(
        currentExpertIds.toSet(),
      );
      final expertsToRemove = currentExpertIds.toSet().difference(
        selectedExpertIds,
      );

      // Determine which solicitors to add and remove
      final Set<String> currentAssociatedSolicitorIds =
          _claimDetails!.associatedSolicitorIds.toSet();
      final Set<String> newAssociatedSolicitorIds =
          _associatedSolicitors.map((s) => s.id).toSet();

      final solicitorsToAdd = newAssociatedSolicitorIds.difference(
        currentAssociatedSolicitorIds,
      );
      final solicitorsToRemove = currentAssociatedSolicitorIds.difference(
        newAssociatedSolicitorIds,
      );

      // Note: claim_admins are not directly editable in this UI flow yet,
      // but if they were, similar logic would apply.
      // For now, we only save what's editable.

      List<String> fieldsUpdated = [
        'claim_title',
        'case_summary_public',
        'stage',
      ];

      // Save to PocketBase funding_applications collection
      final Map<String, dynamic> updateBody = {
        'claim_title': _caseTitleController.text,
        'case_summary_public': _caseSummaryController.text,
        'stage': _selectedStage,
        'status_updates': updatedStatusUpdates.map((e) => e.toJson()).toList(),
      };

      debugPrint('Update body: $updateBody');

      await pbClient
          .collection('funding_applications')
          .update(_claimDetails!.id, body: updateBody);

      // Update barristers relation using special syntax
      if (barristersToAdd.isNotEmpty) {
        fieldsUpdated.add('barristers');
        debugPrint('Adding barristers: $barristersToAdd');
        for (final barristerId in barristersToAdd) {
          debugPrint('Adding barrister: $barristerId');
          await pbClient
              .collection('funding_applications')
              .update(_claimDetails!.id, body: {'+barristers': barristerId});
          await _updateBarristerClaims(barristerId, _claimDetails!.id);
        }
      }

      if (barristersToRemove.isNotEmpty) {
        fieldsUpdated.add('barristers');
        for (final barristerId in barristersToRemove) {
          await pbClient
              .collection('funding_applications')
              .update(_claimDetails!.id, body: {'-barristers': barristerId});
          await _removeClaimFromBarrister(barristerId, _claimDetails!.id);
        }
      }

      // Update experts relation using special syntax
      if (expertsToAdd.isNotEmpty) {
        fieldsUpdated.add('experts');
        for (final expertId in expertsToAdd) {
          await pbClient
              .collection('funding_applications')
              .update(_claimDetails!.id, body: {'+experts': expertId});
          await _updateExpertClaims(expertId, _claimDetails!.id);
        }
      }

      if (expertsToRemove.isNotEmpty) {
        fieldsUpdated.add('experts');
        for (final expertId in expertsToRemove) {
          await pbClient
              .collection('funding_applications')
              .update(_claimDetails!.id, body: {'-experts': expertId});
          await _removeClaimFromExpert(expertId, _claimDetails!.id);
        }
      }

      // Update associated_solicitors relation
      if (solicitorsToAdd.isNotEmpty) {
        fieldsUpdated.add('associated_solicitors');
        for (final solicitorId in solicitorsToAdd) {
          await pbClient
              .collection('funding_applications')
              .update(
                _claimDetails!.id,
                body: {'+associated_solicitors': solicitorId},
              );
        }
        // TODO: If solicitor_profiles needs to track associated claims, update them here.
      }
      if (solicitorsToRemove.isNotEmpty) {
        fieldsUpdated.add('associated_solicitors');
        for (final solicitorId in solicitorsToRemove) {
          await pbClient
              .collection('funding_applications')
              .update(
                _claimDetails!.id,
                body: {'-associated_solicitors': solicitorId},
              );
        }
        // TODO: If solicitor_profiles needs to track associated claims, update them here.
      }

      // Log the edit in the audit trail
      await pbClient
          .collection('user_activity_logs')
          .create(
            body: {
              'user_id': currentUserId,
              'action': 'update',
              'entity_type': 'funding_application',
              'entity_id': _claimDetails!.id,
              'details': {
                'fields_updated': fieldsUpdated, // Use dynamic list
                'timestamp': DateTime.now().toIso8601String(),
              },
            },
          );

      // Add review notes if provided
      if (_reviewNotesController.text.isNotEmpty) {
        await pbClient
            .collection('funding_application_review_notes')
            .create(
              body: {
                'funding_application_id': _claimDetails!.id,
                'note_text': _reviewNotesController.text,
                'created_by': currentUserId,
                'created_by_name': currentUserName,
              },
            );
      }

      if (mounted) {
        NotificationService.showSuccess(
          context,
          'Funding application updated successfully!',
        );
        // Navigate back to the previous screen
        Navigator.pop(context);
      }
    } catch (e) {
      debugPrint('Error saving claim changes: $e');
      if (mounted) {
        NotificationService.showError(context, 'Failed to save changes: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show success message if needed
    if (_showSuccessMessage) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Operation completed successfully'),
            backgroundColor: Colors.green,
          ),
        );
        setState(() {
          _showSuccessMessage = false;
        });
      });
    }

    // Show error message if needed
    if (_errorMessage != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(_errorMessage!), backgroundColor: Colors.red),
        );
        setState(() {
          _errorMessage = null;
        });
      });
    }

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Loading...')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_claimDetails == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: const Center(child: Text('Could not load claim details.')),
      );
    }

    final tabs = [
      const Tab(
        icon: Icon(Icons.info_outline, size: 18),
        text: 'Basic Details',
      ),
      const Tab(icon: Icon(Icons.gavel, size: 18), text: 'Barristers'),
      const Tab(icon: Icon(Icons.science, size: 18), text: 'Experts'),
      const Tab(icon: Icon(Icons.description, size: 18), text: 'Documents'),
      const Tab(icon: Icon(Icons.people, size: 18), text: 'Claimants'),
      const Tab(icon: Icon(Icons.rate_review, size: 18), text: 'Review Notes'),
    ];
    final tabViews = [
      _buildBasicDetailsTab(context),
      _buildBarristersTab(context),
      _buildExpertsTab(context),
      _buildDocumentsTab(context),
      _buildClaimantsTab(context),
      _buildReviewNotesTab(context),
    ];

    if (_isCurrentUserClaimAdmin) {
      tabs.add(
        const Tab(icon: Icon(Icons.business, size: 18), text: 'Solicitors'),
      );
      // Placeholder for now, will implement _buildSolicitorsTab later

      tabViews.add(_buildSolicitorsTab(context));
    } else {
      Container(
        alignment: Alignment.center,
        child: const Text('Solicitors Tab Content (To be implemented)'),
      );
    }

    return DefaultTabController(
      length: tabs.length, // Use dynamic length
      child: Scaffold(
        appBar: AppBar(
          title: Text('Edit: ${_claimDetails!.caseTitle}'),
          bottom: TabBar(isScrollable: true, tabs: tabs),
          actions: [
            _isSaving
                ? const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                )
                : IconButton(
                  icon: const Icon(Icons.save),
                  tooltip: 'Save Changes',
                  onPressed: _saveChanges,
                ),
          ],
        ),
        body: TabBarView(
          // Use dynamic tabViews list
          children: tabViews,
        ),
        bottomNavigationBar: BottomAppBar(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ShadButton.outline(
                  child: const Text('Cancel'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                ShadButton(
                  onPressed: _isSaving ? null : _saveChanges,
                  child:
                      _isSaving
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                          : const Text('Save Changes'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBasicDetailsTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShadCard(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Claim Title',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ShadInput(
                  controller: _caseTitleController,
                  placeholder: const Text('Enter claim title'),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Public Summary',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ShadTextarea(
                  controller: _caseSummaryController,
                  placeholder: const Text('Enter public summary of the claim'),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Stage',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ShadSelect<String>(
                  placeholder: const Text('Select claim stage'),
                  initialValue: _selectedStage,
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedStage = newValue;
                    });
                  },
                  selectedOptionBuilder: (BuildContext context, String value) {
                    // The builder now receives a non-nullable value when an option is selected.
                    // If no option is selected (e.g. initialValue is null), this builder might not be called
                    // or might be called with a default/placeholder. Handling null if `initialValue` can be null.
                    return Text(value, overflow: TextOverflow.ellipsis);
                  },
                  options:
                      [
                            'STAGE 1: PRE ACTION',
                            'STAGE 2: LETTER BEFORE ACTION',
                            'STAGE 3: CLAIM ISSUED AND SERVED',
                            'STAGE 4: PARTICULARS OF CLAIM SERVED',
                            'STAGE 5: DEFENCE RECEIVED',
                            'STAGE 6: CASE MANAGEMENT COURT HEARING',
                            'STAGE 7: DIRECTIONS HEARINGS COURT',
                            'STAGE 8: APPLICATIONS HEARINGS',
                            'STAGE 9: WITNESS STATEMENTS',
                            'STAGE 10: EXPERT REPORTS',
                            'STAGE 11: DISCLOSURE EVIDENCE',
                            'STAGE 12: INSPECTIONS',
                            'STAGE 13: PRE TRIAL REVIEW',
                            'STAGE 14: TRIAL PREPARATIONS',
                            'STAGE 15: PART 36 OFFERS MEDIATION',
                          ]
                          .map(
                            (stage) => ShadOption(
                              value: stage,
                              child: Text(
                                stage,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          )
                          .toList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarristersTab(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShadCard(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Search Barristers',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ShadInput(
                        controller: _barristersSearchController,
                        placeholder: const Text(
                          'Search by name, chambers, or email',
                        ),
                        onChanged: (value) {
                          if (_debounce?.isActive ?? false) _debounce!.cancel();
                          _debounce = Timer(
                            const Duration(milliseconds: 500),
                            () {
                              _searchBarristers(value);
                            },
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    ShadButton.outline(
                      child: const Text('Clear'),
                      onPressed: () {
                        _barristersSearchController.clear();
                        setState(() {
                          _barristersSearchResults = [];
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                    ShadButton(
                      child: const Text('Add New'),
                      onPressed: () => _showAddBarristerDialog(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (_isSearchingBarristers)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: CircularProgressIndicator(),
                    ),
                  )
                else if (_barristersSearchResults.isNotEmpty)
                  Container(
                    height: 250,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            'Search Results (${_barristersSearchResults.length})',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const Divider(height: 1),
                        Expanded(
                          child: ListView.separated(
                            itemCount: _barristersSearchResults.length,
                            separatorBuilder:
                                (context, index) => const Divider(height: 1),
                            itemBuilder: (context, index) {
                              final barrister = _barristersSearchResults[index];
                              // Check if this barrister is already selected
                              final bool isAlreadySelected = _selectedBarristers
                                  .any((b) => b.id == barrister.id);

                              return ListTile(
                                title: Text(
                                  barrister.name,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(barrister.chambers ?? 'No chambers'),
                                    if (barrister.email != null)
                                      Text(
                                        barrister.email!,
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade700,
                                        ),
                                      ),
                                  ],
                                ),
                                isThreeLine: barrister.email != null,
                                trailing:
                                    isAlreadySelected
                                        ? const Chip(
                                          label: Text('Added'),
                                          backgroundColor: Colors.green,
                                          labelStyle: TextStyle(
                                            color: Colors.white,
                                          ),
                                        )
                                        : IconButton(
                                          icon: const Icon(
                                            Icons.add_circle,
                                            color: Colors.blue,
                                          ),
                                          tooltip: 'Add to claim',
                                          onPressed:
                                              () => _addBarrister(barrister),
                                        ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Assigned Barristers',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
              if (_selectedBarristers.isNotEmpty)
                Text(
                  '${_selectedBarristers.length} barrister${_selectedBarristers.length > 1 ? 's' : ''}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          Expanded(
            child:
                _selectedBarristers.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.person_off_outlined,
                            size: 48,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'No barristers assigned to this claim.',
                            style: TextStyle(fontSize: 16, color: Colors.grey),
                          ),
                          const SizedBox(height: 16),
                          ShadButton(
                            onPressed: () => _showAddBarristerDialog(context),
                            child: const Text('Add a Barrister'),
                          ),
                        ],
                      ),
                    )
                    : ListView.separated(
                      itemCount: _selectedBarristers.length,
                      separatorBuilder:
                          (context, index) => const SizedBox(height: 12),
                      itemBuilder: (context, index) {
                        final barrister = _selectedBarristers[index];
                        return ShadCard(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(
                                    Icons.person,
                                    size: 20,
                                    color: Colors.blue,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      barrister.name,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(
                                      Icons.delete_outline,
                                      color: Colors.red,
                                    ),
                                    tooltip: 'Remove barrister',
                                    onPressed: () async {
                                      // Show confirmation dialog
                                      final confirmed = await showShadDialog<
                                        bool
                                      >(
                                        context: context,
                                        builder:
                                            (context) => ShadDialog(
                                              title: const Text(
                                                'Remove Barrister',
                                              ),
                                              description: Text(
                                                'Are you sure you want to remove ${barrister.name} from this claim?',
                                              ),
                                              actions: [
                                                ShadButton.outline(
                                                  child: const Text('Cancel'),
                                                  onPressed:
                                                      () => Navigator.of(
                                                        context,
                                                      ).pop(false),
                                                ),
                                                ShadButton.destructive(
                                                  child: const Text('Remove'),
                                                  onPressed:
                                                      () => Navigator.of(
                                                        context,
                                                      ).pop(true),
                                                ),
                                              ],
                                            ),
                                      );

                                      if (confirmed == true) {
                                        await _removeBarrister(barrister.id);
                                      }
                                    },
                                  ),
                                ],
                              ),
                              const Divider(),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.business,
                                    size: 16,
                                    color: Colors.grey,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      barrister.chambers ??
                                          'No chambers specified',
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.email,
                                    size: 16,
                                    color: Colors.grey,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      barrister.email ?? 'No email specified',
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpertsTab(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShadCard(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Search Experts',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ShadInput(
                        controller: _expertsSearchController,
                        placeholder: const Text(
                          'Search by name, firm, specialty, or email',
                        ),
                        onChanged: (value) {
                          if (_debounce?.isActive ?? false) _debounce!.cancel();
                          _debounce = Timer(
                            const Duration(milliseconds: 500),
                            () {
                              _searchExperts(value);
                            },
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    ShadButton.outline(
                      child: const Text('Clear'),
                      onPressed: () {
                        _expertsSearchController.clear();
                        setState(() {
                          _expertsSearchResults = [];
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                    ShadButton(
                      child: const Text('Add New'),
                      onPressed: () => _showAddExpertDialog(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (_isSearchingExperts)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: CircularProgressIndicator(),
                    ),
                  )
                else if (_expertsSearchResults.isNotEmpty)
                  Container(
                    height: 250,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            'Search Results (${_expertsSearchResults.length})',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const Divider(height: 1),
                        Expanded(
                          child: ListView.separated(
                            itemCount: _expertsSearchResults.length,
                            separatorBuilder:
                                (context, index) => const Divider(height: 1),
                            itemBuilder: (context, index) {
                              final expert = _expertsSearchResults[index];
                              // Check if this expert is already selected
                              final bool isAlreadySelected = _selectedExperts
                                  .any((e) => e.id == expert.id);

                              return ListTile(
                                title: Text(
                                  expert.name,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(expert.displaySubtitle),
                                    if (expert.email != null)
                                      Text(
                                        expert.email!,
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade700,
                                        ),
                                      ),
                                  ],
                                ),
                                isThreeLine: expert.email != null,
                                trailing:
                                    isAlreadySelected
                                        ? const Chip(
                                          label: Text('Added'),
                                          backgroundColor: Colors.green,
                                          labelStyle: TextStyle(
                                            color: Colors.white,
                                          ),
                                        )
                                        : IconButton(
                                          icon: const Icon(
                                            Icons.add_circle,
                                            color: Colors.blue,
                                          ),
                                          tooltip: 'Add to claim',
                                          onPressed: () => _addExpert(expert),
                                        ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Assigned Experts',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
              if (_selectedExperts.isNotEmpty)
                Text(
                  '${_selectedExperts.length} expert${_selectedExperts.length > 1 ? 's' : ''}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          Expanded(
            child:
                _selectedExperts.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.person_off,
                            size: 48,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'No expert witnesses assigned to this claim.',
                            style: TextStyle(fontSize: 16, color: Colors.grey),
                          ),
                          const SizedBox(height: 16),
                          ShadButton(
                            onPressed: () => _showAddExpertDialog(context),
                            child: const Text('Add an Expert Witness'),
                          ),
                        ],
                      ),
                    )
                    : ListView.separated(
                      itemCount: _selectedExperts.length,
                      separatorBuilder:
                          (context, index) => const SizedBox(height: 12),
                      itemBuilder: (context, index) {
                        final expert = _selectedExperts[index];
                        return ShadCard(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(
                                    Icons.person,
                                    size: 20,
                                    color: Colors.blue,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      expert.name,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(
                                      Icons.delete_outline,
                                      color: Colors.red,
                                    ),
                                    tooltip: 'Remove expert',
                                    onPressed: () async {
                                      // Show confirmation dialog
                                      final confirmed = await showShadDialog<
                                        bool
                                      >(
                                        context: context,
                                        builder:
                                            (context) => ShadDialog(
                                              title: const Text(
                                                'Remove Expert Witness',
                                              ),
                                              description: Text(
                                                'Are you sure you want to remove ${expert.name} from this claim?',
                                              ),
                                              actions: [
                                                ShadButton.outline(
                                                  child: const Text('Cancel'),
                                                  onPressed:
                                                      () => Navigator.of(
                                                        context,
                                                      ).pop(false),
                                                ),
                                                ShadButton.destructive(
                                                  child: const Text('Remove'),
                                                  onPressed:
                                                      () => Navigator.of(
                                                        context,
                                                      ).pop(true),
                                                ),
                                              ],
                                            ),
                                      );

                                      if (confirmed == true) {
                                        await _removeExpert(expert.id);
                                      }
                                    },
                                  ),
                                ],
                              ),
                              const Divider(),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.business,
                                    size: 16,
                                    color: Colors.grey,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      expert.firmName ?? 'No firm specified',
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.category,
                                    size: 16,
                                    color: Colors.grey,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      expert.specialty != null
                                          ? 'Specialty: ${expert.specialty}'
                                          : 'No specialty specified',
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.email,
                                    size: 16,
                                    color: Colors.grey,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      expert.email ?? 'No email specified',
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewNotesTab(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Add Review Notes',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          ShadCard(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'These notes will be visible to other solicitors and administrators.',
                ),
                const SizedBox(height: 16),
                ShadTextarea(
                  controller: _reviewNotesController,
                  placeholder: const Text(
                    'Enter review notes, observations, or internal comments about this claim...',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Dialog to add a new barrister
  void _showAddBarristerDialog(BuildContext context) {
    final nameController = TextEditingController();
    final chambersController = TextEditingController();
    final emailController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showShadDialog(
      context: context,
      builder: (context) {
        return ShadDialog(
          title: const Text('Add New Barrister'),
          description: Form(
            // Use description for the main content area
            key: formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Container(
              width: 350, // Adjust width as needed
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Name*'), // Label for Name
                  const SizedBox(height: 4),
                  ShadInput(
                    controller: nameController,
                    placeholder: const Text('Enter barrister name'),
                    // Validator is handled by the Form field wrapper if needed,
                    // or manually before submitting. For ShadInput, validation is typically
                    // done at the Form level.
                  ),
                  // We'll rely on the Form's validator, so we need to wrap ShadInput
                  // with TextFormField or use a custom validation approach with Form.
                  // For simplicity, let's assume Form validation handles it.
                  // If direct validation on ShadInput is needed, it's often custom.
                  // The original TextFormField had a validator, so we need to replicate that.
                  // A common way is to keep TextFormField for validation logic if ShadInput doesn't support it directly.
                  // However, to keep it Shadcn, we'll manage validation via the Form key.
                  const SizedBox(height: 16),
                  const Text('Chambers*'), // Label for Chambers
                  const SizedBox(height: 4),
                  ShadInput(
                    controller: chambersController,
                    placeholder: const Text('Enter chambers name'),
                  ),
                  const SizedBox(height: 16),
                  const Text('Email*'), // Label for Email
                  const SizedBox(height: 4),
                  ShadInput(
                    controller: emailController,
                    placeholder: const Text('Enter email address'),
                    keyboardType: TextInputType.emailAddress,
                  ),
                ],
              ),
            ),
          ),
          actions: [
            ShadButton.outline(
              child: const Text('Cancel'), // Corrected: use child
              onPressed: () => Navigator.of(context).pop(),
            ),
            ShadButton(
              child: const Text('Add'), // Corrected: use child
              onPressed: () async {
                // Manual validation for ShadInputs within the Form
                if (nameController.text.isEmpty) {
                  NotificationService.showError(context, 'Name is required.');
                  return;
                }
                if (nameController.text.length < 2) {
                  NotificationService.showError(
                    context,
                    'Name must be at least 2 characters.',
                  );
                  return;
                }
                if (chambersController.text.isEmpty) {
                  NotificationService.showError(
                    context,
                    'Chambers is required.',
                  );
                  return;
                }
                if (emailController.text.isEmpty) {
                  NotificationService.showError(context, 'Email is required.');
                  return;
                }
                final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                if (!emailRegex.hasMatch(emailController.text)) {
                  NotificationService.showError(
                    context,
                    'Please enter a valid email address.',
                  );
                  return;
                }

                // If formKey was used with TextFormFields, validation would be:
                // if (!formKey.currentState!.validate()) {
                //   return;
                // }
                final dialogContext = context;
                Navigator.of(dialogContext).pop();

                try {
                  final pbClient = PocketBaseService().pb;
                  final record = await pbClient
                      .collection('barristers')
                      .create(
                        body: {
                          'barrister_with_conduct': nameController.text.trim(),
                          'barrister_chambers': chambersController.text.trim(),
                          'email': emailController.text.trim(),
                          'claims': [widget.fundingApplicationId],
                        },
                      );

                  final newBarrister = Barrister(
                    id: record.id,
                    name: nameController.text.trim(),
                    chambers: chambersController.text.trim(),
                    email: emailController.text.trim(),
                    claimIds: [widget.fundingApplicationId],
                    created: DateTime.now(),
                    updated: DateTime.now(),
                  );

                  if (mounted) {
                    setState(() {
                      _selectedBarristers.add(newBarrister);
                      NotificationService.showSuccess(
                        context,
                        '${newBarrister.name} added successfully.',
                      );
                    });
                  }
                } catch (e) {
                  if (mounted) {
                    NotificationService.showError(
                      context,
                      'Failed to add barrister: ${e.toString()}',
                    );
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  // Dialog to add a new expert
  void _showAddExpertDialog(BuildContext context) {
    final nameController = TextEditingController();
    final firmController = TextEditingController();
    final emailController = TextEditingController();
    String selectedType = 'legal'; // Default value
    final formKey = GlobalKey<FormState>();

    showShadDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          // Keep StatefulBuilder for the dropdown state
          builder: (context, setDialogState) {
            return ShadDialog(
              title: const Text('Add New Expert Witness'),
              description: Form(
                key: formKey,
                // autovalidateMode: AutovalidateMode.onUserInteraction, // Manual validation for ShadInputs
                child: Container(
                  width: 350, // Adjust width as needed
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Name*'),
                      const SizedBox(height: 4),
                      ShadInput(
                        controller: nameController,
                        placeholder: const Text('Enter expert name'),
                      ),
                      const SizedBox(height: 16),
                      const Text('Firm*'),
                      const SizedBox(height: 4),
                      ShadInput(
                        controller: firmController,
                        placeholder: const Text('Enter firm name'),
                      ),
                      const SizedBox(height: 16),
                      const Text('Email*'),
                      const SizedBox(height: 4),
                      ShadInput(
                        controller: emailController,
                        placeholder: const Text('Enter email address'),
                        keyboardType: TextInputType.emailAddress,
                      ),
                      const SizedBox(height: 16),
                      const Text('Expertise Type*'),
                      const SizedBox(height: 4),
                      ShadSelect<String>(
                        placeholder: const Text('Select expertise type'),
                        initialValue: selectedType,
                        onChanged: (String? value) {
                          if (value != null) {
                            setDialogState(() {
                              // Use the StatefulBuilder's setState
                              selectedType = value;
                            });
                          }
                        },
                        selectedOptionBuilder: (
                          BuildContext context,
                          String value,
                        ) {
                          return Text(value);
                        },
                        options: const [
                          ShadOption(value: 'legal', child: Text('Legal')),
                          ShadOption(
                            value: 'financial',
                            child: Text('Financial'),
                          ),
                          ShadOption(
                            value: 'engineer',
                            child: Text('Engineer'),
                          ),
                          ShadOption(
                            value: 'construction',
                            child: Text('Construction'),
                          ),
                          ShadOption(value: 'other', child: Text('Other')),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                ShadButton.outline(
                  child: const Text('Cancel'),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                ShadButton(
                  child: const Text('Add'),
                  onPressed: () async {
                    // Manual validation
                    if (nameController.text.isEmpty) {
                      NotificationService.showError(
                        context,
                        'Name is required.',
                      );
                      return;
                    }
                    if (nameController.text.length < 2) {
                      NotificationService.showError(
                        context,
                        'Name must be at least 2 characters.',
                      );
                      return;
                    }
                    if (firmController.text.isEmpty) {
                      NotificationService.showError(
                        context,
                        'Firm is required.',
                      );
                      return;
                    }
                    if (emailController.text.isEmpty) {
                      NotificationService.showError(
                        context,
                        'Email is required.',
                      );
                      return;
                    }
                    final emailRegex = RegExp(
                      r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                    );
                    if (!emailRegex.hasMatch(emailController.text)) {
                      NotificationService.showError(
                        context,
                        'Please enter a valid email address.',
                      );
                      return;
                    }
                    if (selectedType.isEmpty) {
                      // Should not happen with initialValue but good practice
                      NotificationService.showError(
                        context,
                        'Expertise type is required.',
                      );
                      return;
                    }

                    final dialogContext = context;
                    Navigator.of(dialogContext).pop();

                    try {
                      final pbClient = PocketBaseService().pb;
                      final record = await pbClient
                          .collection('experts')
                          .create(
                            body: {
                              'name': nameController.text.trim(),
                              'firm_name': firmController.text.trim(),
                              'email': emailController.text.trim(),
                              'type': selectedType,
                              'claims': [widget.fundingApplicationId],
                            },
                          );

                      final newExpert = Expert(
                        id: record.id,
                        name: nameController.text.trim(),
                        firmName: firmController.text.trim(),
                        email: emailController.text.trim(),
                        specialty: selectedType,
                        claimIds: [widget.fundingApplicationId],
                        created: DateTime.now(),
                        updated: DateTime.now(),
                      );

                      if (mounted) {
                        setState(() {
                          _selectedExperts.add(newExpert);
                          NotificationService.showSuccess(
                            context,
                            '${newExpert.name} added successfully.',
                          );
                        });
                      }
                    } catch (e) {
                      if (mounted) {
                        NotificationService.showError(
                          context,
                          'Failed to add expert: ${e.toString()}',
                        );
                      }
                    }
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Helper method to get all solicitors to display, including claim admins
  List<SolicitorProfileModel> _getAllSolicitorsToDisplay() {
    if (_claimDetails == null) {
      return _associatedSolicitors;
    }

    // No need for a map here, we're just sorting

    // Sort the solicitors so that claim admins appear first
    final List<SolicitorProfileModel> sortedSolicitors =
        _associatedSolicitors.toList();
    sortedSolicitors.sort((a, b) {
      final aIsAdmin = _claimDetails!.claimAdminIds.contains(a.id);
      final bIsAdmin = _claimDetails!.claimAdminIds.contains(b.id);

      if (aIsAdmin && !bIsAdmin) {
        return -1; // a comes before b
      } else if (!aIsAdmin && bIsAdmin) {
        return 1; // b comes before a
      } else {
        // Both are admins or both are not admins, sort by name
        return a.solicitorName.compareTo(b.solicitorName);
      }
    });

    return sortedSolicitors;
  }

  // This method would be used to fetch missing admin profiles in a real implementation
  // For now, we'll just use the existing associated solicitors
  Future<void> _fetchMissingAdminProfiles() async {
    if (_claimDetails == null) return;

    try {
      // Create a map of solicitor ID to solicitor model for quick lookup
      final Map<String, SolicitorProfileModel> solicitorMap = {
        for (var solicitor in _associatedSolicitors) solicitor.id: solicitor,
      };

      // Find admin IDs that aren't in the associated solicitors list
      final List<String> missingAdminIds =
          _claimDetails!.claimAdminIds
              .where((id) => !solicitorMap.containsKey(id))
              .toList();

      if (missingAdminIds.isEmpty) return;

      // Fetch the missing admin profiles
      final pbClient = PocketBaseService().pb;
      final filterQuery = missingAdminIds
          .map((id) => 'id = "$id"')
          .join(' || ');

      final result = await pbClient
          .collection('solicitor_profiles')
          .getList(
            filter: filterQuery,
            expand: 'user_id', // Expand user_id to get email from users table
          );

      // Process the results and add them to _associatedSolicitors
      for (var item in result.items) {
        final Map<String, dynamic> data = item.toJson();

        // Add user email from expanded data if available
        try {
          // Check if expand data exists and contains user_id
          if (item.data.containsKey('expand') && item.data['expand'] != null) {
            final expandData = item.data['expand'] as Map<String, dynamic>;
            if (expandData.containsKey('user_id') &&
                expandData['user_id'] != null) {
              final userData = expandData['user_id'] as Map<String, dynamic>;
              data['userEmail'] = userData['email'];
              data['userName'] = userData['name'];
            }
          }
        } catch (e) {
          // If there's an error getting the expanded data, just continue
          debugPrint('Error getting expanded user data: $e');
        }

        final solicitorProfile = SolicitorProfileModel.fromJson(data);

        // Only add if not already in the list
        if (!_associatedSolicitors.any((s) => s.id == solicitorProfile.id)) {
          _associatedSolicitors.add(solicitorProfile);
        }
      }

      // Update the UI
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('Error fetching missing admin profiles: $e');
    }
  }

  // Method to search for solicitors (similar to _searchBarristers and _searchExperts)
  Future<void> _searchSolicitors(String query) async {
    if (query.isEmpty) {
      if (!mounted) return;
      setState(() {
        _solicitorSearchResultsList = [];
        _isSearchingSolicitors = false;
      });
      return;
    }
    if (!mounted) return;
    setState(() {
      _isSearchingSolicitors = true;
    });

    try {
      final pbClient = PocketBaseService().pb;

      // First, search for solicitor profiles by name or firm
      final result = await pbClient
          .collection('solicitor_profiles')
          .getList(
            filter: 'solicitor_name ~ "$query" || law_firm_name ~ "$query"',
            expand: 'user_id', // Expand user_id to get email from users table
          );

      // Debug: Print the raw data structure to understand what we're working with
      debugPrint('Raw solicitor profiles data structure:');
      for (var item in result.items) {
        debugPrint('Item ID: ${item.id}');
        debugPrint('Item data: ${item.data}');
        if (item.data.containsKey('expand')) {
          debugPrint('Expand data type: ${item.data['expand'].runtimeType}');
          debugPrint('Expand data: ${item.data['expand']}');
        } else {
          debugPrint('No expand data found');
        }
      }

      // Process the results to include user email
      final List<SolicitorProfileModel> solicitorProfiles = [];

      for (var item in result.items) {
        final Map<String, dynamic> data = item.toJson();

        // Add user email from expanded data if available
        try {
          // Check if expand data exists and contains user_id
          if (item.data.containsKey('expand') && item.data['expand'] != null) {
            final expandData = item.data['expand'] as Map<String, dynamic>;
            if (expandData.containsKey('user_id') &&
                expandData['user_id'] != null) {
              final userData = expandData['user_id'] as Map<String, dynamic>;
              data['userEmail'] = userData['email'];
              data['userName'] = userData['name'];
            }
          }
        } catch (e) {
          // If there's an error getting the expanded data, just continue
          debugPrint('Error getting expanded user data: $e');
        }

        solicitorProfiles.add(SolicitorProfileModel.fromJson(data));
      }

      // Now, search for users by email and then find their solicitor profiles
      if (query.contains('@')) {
        final userResults = await pbClient
            .collection('users')
            .getList(filter: 'email ~ "$query" && user_type = "solicitor"');

        if (userResults.items.isNotEmpty) {
          // For each user found, get their solicitor profile
          for (var user in userResults.items) {
            final profileResults = await pbClient
                .collection('solicitor_profiles')
                .getList(filter: 'user_id = "${user.id}"');

            if (profileResults.items.isNotEmpty) {
              for (var profile in profileResults.items) {
                final Map<String, dynamic> data = profile.toJson();
                // Safely add user email and name
                try {
                  // Debug the user data
                  debugPrint('User data: ${user.data}');

                  if (user.data.containsKey('email')) {
                    data['userEmail'] = user.data['email'].toString();
                    debugPrint('Added userEmail: ${data['userEmail']}');
                  }

                  if (user.data.containsKey('name')) {
                    data['userName'] = user.data['name'].toString();
                    debugPrint('Added userName: ${data['userName']}');
                  }
                } catch (e) {
                  debugPrint('Error adding user data to profile: $e');
                }

                // Check if this profile is already in the list
                final existingIndex = solicitorProfiles.indexWhere(
                  (p) => p.id == profile.id,
                );
                if (existingIndex >= 0) {
                  // Update existing entry
                  solicitorProfiles[existingIndex] =
                      SolicitorProfileModel.fromJson(data);
                } else {
                  // Add new entry
                  solicitorProfiles.add(SolicitorProfileModel.fromJson(data));
                }
              }
            }
          }
        }
      }

      if (!mounted) return;
      setState(() {
        _solicitorSearchResultsList = solicitorProfiles;
        _isSearchingSolicitors = false;
      });

      // Debug output
      debugPrint(
        'Found ${_solicitorSearchResultsList.length} solicitor profiles for query: $query',
      );
      for (var solicitor in _solicitorSearchResultsList) {
        debugPrint(
          'Solicitor: ${solicitor.solicitorName}, Email: ${solicitor.userEmail}, ID: ${solicitor.id}',
        );
      }
    } catch (e) {
      debugPrint('Error searching solicitors: $e');
      if (!mounted) return;
      setState(() {
        _solicitorSearchResultsList = [];
        _isSearchingSolicitors = false;
      });
      NotificationService.showError(
        context,
        'Error searching solicitors: ${e.toString()}',
      );
    }
  }

  // Add a solicitor to the claim
  Future<void> _addSolicitorToClaim(SolicitorProfileModel solicitor) async {
    if (_associatedSolicitors.any((s) => s.id == solicitor.id)) {
      if (mounted) {
        NotificationService.showInfo(
          context,
          '${solicitor.solicitorName} is already associated with this claim.',
        );
      }
      return;
    }

    try {
      final pbClient = PocketBaseService().pb;

      // Update the funding application to add the solicitor
      await pbClient
          .collection('funding_applications')
          .update(
            widget.fundingApplicationId,
            body: {
              'associated_solicitors+': [solicitor.id],
            },
          );

      // Refresh the claim details to get the updated data
      await _fetchClaimDetails();

      // Create a notification for the solicitor
      await _createSolicitorNotification(
        solicitor.userId,
        'Added to Claim',
        'You have been assigned to the claim: ${_claimDetails?.caseTitle ?? 'Unknown Claim'}',
      );

      // Show success notification
      if (mounted) {
        NotificationService.showSuccess(
          context,
          '${solicitor.solicitorName} added to claim',
        );
      }

      // Clear search results
      if (mounted) {
        setState(() {
          _solicitorsSearchController.clear();
          _solicitorSearchResultsList = [];
        });
      }
    } catch (e) {
      debugPrint('Error adding solicitor to claim: $e');
      if (mounted) {
        NotificationService.showError(
          context,
          'Failed to add solicitor to claim: $e',
        );
      }
    }
  }

  Future<void> _removeSolicitorFromClaim(String solicitorId) async {
    SolicitorProfileModel? solicitorToRemove;
    try {
      solicitorToRemove = _associatedSolicitors.firstWhere(
        (s) => s.id == solicitorId,
      );
    } catch (e) {
      // Solicitor not found in current list, should not happen if UI is consistent
      debugPrint('Solicitor to remove not found in local list: $solicitorId');
      if (mounted) {
        NotificationService.showError(
          context,
          'Could not find solicitor to remove.',
        );
      }
      return;
    }

    final tempSolicitor = solicitorToRemove; // Keep a reference for the name

    try {
      final pbClient = PocketBaseService().pb;

      // Update the funding application to remove the solicitor
      await pbClient
          .collection('funding_applications')
          .update(
            widget.fundingApplicationId,
            body: {
              'associated_solicitors-': [solicitorId],
            },
          );

      // Refresh the claim details to get the updated data
      await _fetchClaimDetails();

      // Show success notification
      if (mounted) {
        NotificationService.showSuccess(
          context,
          '${tempSolicitor.solicitorName} removed from claim',
        );
      }
    } catch (e) {
      debugPrint('Error removing solicitor from claim: $e');
      if (mounted) {
        NotificationService.showError(
          context,
          'Failed to remove solicitor from claim: $e',
        );
      }
    }
  }

  Widget _buildSolicitorsTab(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShadCard(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Search Solicitors',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ShadInput(
                        controller: _solicitorsSearchController,
                        placeholder: const Text(
                          'Search by name, firm, or email',
                        ),
                        onChanged: (value) {
                          if (_debounce?.isActive ?? false) _debounce!.cancel();
                          _debounce = Timer(
                            const Duration(milliseconds: 500),
                            () => _searchSolicitors(value),
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    ShadButton.outline(
                      child: const Text('Clear'),
                      onPressed: () {
                        _solicitorsSearchController.clear();
                        setState(() => _solicitorSearchResultsList = []);
                      },
                    ),
                    const SizedBox(width: 8),
                    ShadButton(
                      child: const Text('Add New'),
                      onPressed:
                          () => _showAddSolicitorDialog(
                            context,
                          ), // Will add this method
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (_isSearchingSolicitors)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: CircularProgressIndicator(),
                    ),
                  )
                else if (_solicitorSearchResultsList.isNotEmpty)
                  Container(
                    height: 250, // Similar to Barristers/Experts search results
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Colors.grey.shade300,
                      ), // Consistent styling
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            'Search Results (${_solicitorSearchResultsList.length})',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const Divider(height: 1),
                        Expanded(
                          child: ListView.separated(
                            itemCount: _solicitorSearchResultsList.length,
                            separatorBuilder:
                                (context, index) => const Divider(height: 1),
                            itemBuilder: (context, index) {
                              final solicitor =
                                  _solicitorSearchResultsList[index];
                              final isAlreadyAdded = _associatedSolicitors.any(
                                (s) => s.id == solicitor.id,
                              );

                              return ListTile(
                                title: Text(
                                  solicitor
                                      .solicitorName, // This should be the solicitor's actual name
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(solicitor.lawFirmName),
                                    if (solicitor.userEmail != null)
                                      Text(
                                        solicitor.userEmail!,
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade700,
                                        ),
                                      ),
                                  ],
                                ),
                                isThreeLine: solicitor.userEmail != null,
                                trailing:
                                    isAlreadyAdded
                                        ? const Chip(
                                          label: Text('Added'),
                                          backgroundColor: Colors.green,
                                          labelStyle: TextStyle(
                                            color: Colors.white,
                                          ),
                                        )
                                        : IconButton(
                                          icon: const Icon(
                                            Icons.add_circle,
                                            color: Colors.blue,
                                          ),
                                          tooltip: 'Add to claim',
                                          onPressed:
                                              () => _addSolicitorToClaim(
                                                solicitor,
                                              ),
                                        ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Associated Solicitors',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
              if (_associatedSolicitors.isNotEmpty)
                Text(
                  '${_associatedSolicitors.length} solicitor${_associatedSolicitors.length > 1 ? 's' : ''}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          Expanded(
            child:
                _associatedSolicitors.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.group_off_outlined,
                            size: 48,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'No solicitors associated with this claim.',
                            style: TextStyle(fontSize: 16, color: Colors.grey),
                          ),
                          // Optionally, a button to trigger search or add if no results are shown above
                        ],
                      ),
                    )
                    : ListView.builder(
                      itemCount: _getAllSolicitorsToDisplay().length,
                      itemBuilder: (context, index) {
                        final solicitor = _getAllSolicitorsToDisplay()[index];
                        final email =
                            solicitor.userEmail ?? 'No email available';

                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12.0),
                          child: ShadCard(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.person_pin_circle_outlined,
                                      size: 20,
                                      color: Colors.blue,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              solicitor.solicitorName,
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 16,
                                              ),
                                            ),
                                          ),
                                          // Display admin tag if this solicitor is a claim admin
                                          if (_claimDetails != null &&
                                              _claimDetails!.claimAdminIds
                                                  .contains(solicitor.id))
                                            Container(
                                              margin: const EdgeInsets.only(
                                                left: 8,
                                              ),
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    horizontal: 8,
                                                    vertical: 2,
                                                  ),
                                              decoration: BoxDecoration(
                                                color: Colors.purple.shade100,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                border: Border.all(
                                                  color: Colors.purple.shade300,
                                                ),
                                              ),
                                              child: const Text(
                                                'Admin',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.purple,
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                    // Only show delete button if the solicitor is not a claim admin
                                    if (_claimDetails == null ||
                                        !_claimDetails!.claimAdminIds.contains(
                                          solicitor.id,
                                        ))
                                      IconButton(
                                        icon: const Icon(
                                          Icons.delete_outline,
                                          color: Colors.red,
                                        ),
                                        tooltip: 'Remove solicitor',
                                        onPressed: () async {
                                          final confirmed = await showShadDialog<
                                            bool
                                          >(
                                            context: context,
                                            builder:
                                                (context) => ShadDialog(
                                                  title: const Text(
                                                    'Remove Solicitor',
                                                  ),
                                                  description: Text(
                                                    'Are you sure you want to remove ${solicitor.solicitorName} from this claim?',
                                                  ),
                                                  actions: [
                                                    ShadButton.outline(
                                                      child: const Text(
                                                        'Cancel',
                                                      ),
                                                      onPressed:
                                                          () => Navigator.of(
                                                            context,
                                                          ).pop(false),
                                                    ),
                                                    ShadButton.destructive(
                                                      child: const Text(
                                                        'Remove',
                                                      ),
                                                      onPressed:
                                                          () => Navigator.of(
                                                            context,
                                                          ).pop(true),
                                                    ),
                                                  ],
                                                ),
                                          );
                                          if (confirmed == true) {
                                            await _removeSolicitorFromClaim(
                                              solicitor.id,
                                            );
                                          }
                                        },
                                      ),
                                  ],
                                ),
                                const Divider(),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.business_center_outlined,
                                      size: 16,
                                      color: Colors.grey,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        solicitor.lawFirmName,
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.alternate_email_outlined,
                                      size: 16,
                                      color: Colors.grey,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        email,
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }

  // Dialog to add a new solicitor
  void _showAddSolicitorDialog(BuildContext context) {
    final nameController = TextEditingController();
    final firmNameController = TextEditingController();
    final emailController = TextEditingController();
    final positionController = TextEditingController();
    final contactNumberController = TextEditingController();
    final firmAddressController = TextEditingController();
    final sraNumberController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showShadDialog(
      context: context,
      builder: (context) {
        return ShadDialog(
          title: const Text('Add New Solicitor'),
          description: Form(
            key: formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Container(
              width: 400, // Wider dialog for more fields
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Solicitor Name*'),
                  const SizedBox(height: 4),
                  ShadInput(
                    controller: nameController,
                    placeholder: const Text('Enter solicitor name'),
                  ),
                  const SizedBox(height: 12),

                  const Text('Law Firm Name*'),
                  const SizedBox(height: 4),
                  ShadInput(
                    controller: firmNameController,
                    placeholder: const Text('Enter law firm name'),
                  ),
                  const SizedBox(height: 12),

                  const Text('Email*'),
                  const SizedBox(height: 4),
                  ShadInput(
                    controller: emailController,
                    placeholder: const Text('Enter email address'),
                  ),
                  const SizedBox(height: 12),

                  const Text('Position*'),
                  const SizedBox(height: 4),
                  ShadInput(
                    controller: positionController,
                    placeholder: const Text(
                      'Enter position (e.g., Partner, Associate)',
                    ),
                  ),
                  const SizedBox(height: 12),

                  const Text('Contact Number*'),
                  const SizedBox(height: 4),
                  ShadInput(
                    controller: contactNumberController,
                    placeholder: const Text('Enter contact number'),
                  ),
                  const SizedBox(height: 12),

                  const Text('Firm Address*'),
                  const SizedBox(height: 4),
                  ShadTextarea(
                    controller: firmAddressController,
                    placeholder: const Text('Enter firm address'),
                  ),
                  const SizedBox(height: 12),

                  const Text('SRA Number*'),
                  const SizedBox(height: 4),
                  ShadInput(
                    controller: sraNumberController,
                    placeholder: const Text('Enter SRA number'),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            ShadButton.outline(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            ShadButton(
              child: const Text('Add Solicitor'),
              onPressed: () async {
                // Basic validation
                if (nameController.text.isEmpty ||
                    firmNameController.text.isEmpty ||
                    emailController.text.isEmpty ||
                    positionController.text.isEmpty ||
                    contactNumberController.text.isEmpty ||
                    firmAddressController.text.isEmpty ||
                    sraNumberController.text.isEmpty) {
                  NotificationService.showError(
                    context,
                    'Please fill in all required fields',
                  );
                  return;
                }

                // Close the dialog
                Navigator.of(context).pop();

                try {
                  final pbClient = PocketBaseService().pb;

                  // First, check if a user with this email already exists
                  final userResults = await pbClient
                      .collection('users')
                      .getList(
                        filter: 'email = "${emailController.text.trim()}"',
                        perPage: 1,
                      );

                  String userId;

                  if (userResults.items.isEmpty) {
                    // Create a new user with a random password (they'll need to reset it)
                    final randomPassword =
                        DateTime.now().millisecondsSinceEpoch.toString();

                    final newUser = await pbClient
                        .collection('users')
                        .create(
                          body: {
                            'email': emailController.text.trim(),
                            'password': randomPassword,
                            'passwordConfirm': randomPassword,
                            'name': nameController.text.trim(),
                            'user_type': 'solicitor',
                            'status': 'pending', // They'll need to be approved
                            'emailVisibility': false,
                            'verified': false,
                          },
                        );

                    userId = newUser.id;

                    // TODO: Send email to the new user with password reset instructions
                    debugPrint('Created new user with ID: $userId');
                  } else {
                    // Use existing user
                    userId = userResults.items.first.id;
                    debugPrint('Using existing user with ID: $userId');
                  }

                  // Now create the solicitor profile
                  final newSolicitorProfile = await pbClient
                      .collection('solicitor_profiles')
                      .create(
                        body: {
                          'user_id': userId,
                          'solicitor_name': nameController.text.trim(),
                          'law_firm_name': firmNameController.text.trim(),
                          'position': positionController.text.trim(),
                          'contact_number': contactNumberController.text.trim(),
                          'firm_address': firmAddressController.text.trim(),
                          'firm_registration_number':
                              sraNumberController.text.trim(),
                          'pu_status': 'pending', // They'll need to be approved
                        },
                      );

                  // Create a SolicitorProfileModel from the response
                  final solicitorProfile = SolicitorProfileModel.fromJson({
                    ...newSolicitorProfile.data,
                    'userEmail': emailController.text.trim(),
                  });

                  // Add the solicitor to the claim
                  await _addSolicitorToClaim(solicitorProfile);

                  if (mounted) {
                    NotificationService.showSuccess(
                      context,
                      'Solicitor added successfully',
                    );
                  }
                } catch (e) {
                  debugPrint('Error adding solicitor: $e');
                  if (mounted) {
                    NotificationService.showError(
                      context,
                      'Failed to add solicitor: $e',
                    );
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildDocumentsTab(BuildContext context) {
    final theme = ShadTheme.of(context);

    debugPrint(
      'EditFundingApplicationPage: Building Documents tab with ${_claimDetails!.documentRepository.length} document categories (exclusively from claim_documents collection)',
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  'Document Repository',
                  style: theme.textTheme.h4,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              Flexible(
                child: ShadButton(
                  size: ShadButtonSize.sm,
                  child: const Text('+ Add Document'),
                  onPressed: () {
                    _showUploadNewDocumentModal(context, _claimDetails!);
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_isLoading)
            SizedBox(
              height: 300,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Loading documents...'),
                  ],
                ),
              ),
            )
          else if (_claimDetails!.documentRepository.isEmpty)
            Container(
              height: 300,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.folder_open_outlined,
                      size: 64,
                      color: theme.colorScheme.mutedForeground,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No documents uploaded yet',
                      style: theme.textTheme.h4,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Upload your first document to get started',
                      style: theme.textTheme.muted,
                    ),
                    const SizedBox(height: 16),
                    ShadButton(
                      child: const Text('Upload Document'),
                      onPressed: () {
                        _showUploadNewDocumentModal(context, _claimDetails!);
                      },
                    ),
                  ],
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _claimDetails!.documentRepository.length,
              itemBuilder: (context, index) {
                final category = _claimDetails!.documentRepository[index];
                final currentVersion = category.versions.firstWhere(
                  (v) => v.fileId == category.currentVersionFileId,
                  orElse:
                      () =>
                          category.versions.isNotEmpty
                              ? category.versions.last
                              : DocumentVersion(
                                fileId: '',
                                filename: 'N/A',
                                uploadedAt: DateTime.now().toIso8601String(),
                                uploadedBy: 'System',
                                notes: 'No current version found',
                              ),
                );

                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                category.logicalName,
                                style: theme.textTheme.large.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            if (category.versions.length > 1)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8.0,
                                  vertical: 4.0,
                                ),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.primary.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '${category.versions.length} versions',
                                  style: theme.textTheme.small.copyWith(
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        _buildDocumentDetailRow(
                          context,
                          'Current Filename:',
                          currentVersion.filename,
                          isLink: true,
                          onLinkTap:
                              () => _previewDocument(context, currentVersion),
                        ),
                        _buildDocumentDetailRow(
                          context,
                          'Last Updated:',
                          DateFormat.yMMMd().add_jm().format(
                            DateTime.parse(currentVersion.uploadedAt).toLocal(),
                          ),
                        ),
                        _buildDocumentDetailRowWithUserName(
                          context,
                          'Uploaded By:',
                          currentVersion.uploadedBy,
                        ),
                        _buildExpandableText(
                          label: 'Comment:',
                          text: currentVersion.notes ?? 'No comment.',
                        ),
                        const SizedBox(height: 12),
                        // Action buttons
                        Wrap(
                          spacing: 8.0,
                          runSpacing: 8.0,
                          children: [
                            ShadButton.outline(
                              leading: const Icon(
                                Icons.download_outlined,
                                size: 16,
                              ),
                              onPressed:
                                  _isDownloading
                                      ? null
                                      : () => _downloadDocument(
                                        context,
                                        currentVersion,
                                      ),
                              size: ShadButtonSize.sm,
                              child: const Text('Download'),
                            ),
                            ShadButton.outline(
                              leading: const Icon(
                                Icons.visibility_outlined,
                                size: 16,
                              ),
                              onPressed:
                                  () =>
                                      _previewDocument(context, currentVersion),
                              size: ShadButtonSize.sm,
                              child: const Text('Preview'),
                            ),
                            ShadButton.outline(
                              leading: const Icon(
                                Icons.upload_file_outlined,
                                size: 16,
                              ),
                              onPressed:
                                  () => _showUploadNewVersionModal(
                                    context,
                                    _claimDetails!,
                                    category,
                                  ),
                              size: ShadButtonSize.sm,
                              child: const Text('New Version'),
                            ),
                            ShadButton.outline(
                              leading: const Icon(
                                Icons.edit_note_outlined,
                                size: 16,
                              ),
                              onPressed:
                                  () => _showEditCommentModal(
                                    context,
                                    _claimDetails!,
                                    category,
                                    currentVersion,
                                  ),
                              size: ShadButtonSize.sm,
                              child: const Text('Edit Comment'),
                            ),
                            ShadButton.outline(
                              leading: const Icon(
                                Icons.history_outlined,
                                size: 16,
                              ),
                              onPressed:
                                  () => _showVersionHistoryModal(
                                    context,
                                    category,
                                  ),
                              size: ShadButtonSize.sm,
                              child: const Text('History'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildDocumentDetailRow(
    BuildContext context,
    String label,
    String value, {
    bool isLink = false,
    VoidCallback? onLinkTap,
  }) {
    final theme = ShadTheme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: theme.textTheme.muted.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          Expanded(
            child:
                isLink
                    ? InkWell(
                      onTap: onLinkTap,
                      child: Text(
                        value,
                        style: theme.textTheme.p.copyWith(
                          color: theme.colorScheme.primary,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    )
                    : Text(value, style: theme.textTheme.p),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentDetailRowWithUserName(
    BuildContext context,
    String label,
    String userId,
  ) {
    final theme = ShadTheme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: theme.textTheme.muted.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: FutureBuilder<String>(
              future: _resolveUserName(userId),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Text(
                    'Loading...',
                    style: theme.textTheme.p.copyWith(
                      color: theme.colorScheme.muted,
                    ),
                  );
                } else if (snapshot.hasError) {
                  return Text('User ($userId)', style: theme.textTheme.p);
                } else {
                  return Text(
                    snapshot.data ?? 'Unknown User',
                    style: theme.textTheme.p,
                  );
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _previewDocument(
    BuildContext context,
    DocumentVersion version,
  ) async {
    final pbClient = PocketBaseService().pb;
    try {
      String url;

      // Check if this is a legacy document
      if (version.fileId.startsWith('legacy_')) {
        // For legacy documents, construct URL directly from funding_applications collection
        final fundingAppRecord = await pbClient
            .collection('funding_applications')
            .getOne(widget.fundingApplicationId);

        url =
            pbClient.files
                .getURL(fundingAppRecord, version.filename)
                .toString();
      } else {
        // For new documents, use the claim_documents collection
        url = await _claimDocumentsService.getFileUrl(version.fileId);
      }

      // Navigate to document preview screen instead of launching browser
      if (mounted && context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) =>
                    DocumentPreviewWidget(url: url, fileName: version.filename),
          ),
        );
      }
    } catch (e) {
      LoggerService.error('Error previewing document', e);
      if (mounted) {
        NotificationService.showError(
          context,
          'Failed to preview document: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _downloadDocument(
    BuildContext context,
    DocumentVersion version,
  ) async {
    if (_isDownloading) return;

    setState(() {
      _isDownloading = true;
    });

    final pbClient = PocketBaseService().pb;
    try {
      String url;

      // Check if this is a legacy document
      if (version.fileId.startsWith('legacy_')) {
        // For legacy documents, construct URL directly from funding_applications collection
        final fundingAppRecord = await pbClient
            .collection('funding_applications')
            .getOne(widget.fundingApplicationId);

        url =
            pbClient.files
                .getURL(fundingAppRecord, version.filename)
                .toString();
      } else {
        // For new documents, use the claim_documents collection
        url = await _claimDocumentsService.getFileUrl(version.fileId);
      }

      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);

        if (mounted) {
          NotificationService.showSuccess(
            context,
            'Document download initiated successfully.',
          );
        }
      } else {
        if (mounted) {
          NotificationService.showError(
            context,
            'Could not initiate document download.',
          );
        }
      }
    } catch (e) {
      debugPrint("Error downloading document: $e");
      if (mounted) {
        NotificationService.showError(
          context,
          'Failed to download document: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  void _showUploadNewDocumentModal(BuildContext context, Claim claimDetails) {
    final theme = ShadTheme.of(context);
    final formKey = GlobalKey<FormState>();
    String? newCategoryName;
    String? comment;
    PlatformFile? pickedFile;
    List<PlatformFile>? pickedFiles;
    StateSetter? dialogSetState;

    showShadDialog(
      context: context,
      builder:
          (context) => ShadDialog.alert(
            title: const Text('+ Add Document'),
            description: const Text(
              'Create a new document category and upload the first version.',
            ),
            actions: [
              ShadButton.ghost(
                child: const Text('Cancel'),
                onPressed: () => Navigator.of(context).pop(),
              ),
              ShadButton(
                child: const Text('Upload Document(s)'),
                onPressed: () async {
                  if (formKey.currentState!.validate() &&
                      pickedFiles != null &&
                      pickedFiles!.isNotEmpty) {
                    formKey.currentState!.save();
                    Navigator.of(context).pop();

                    // Use multiple files upload if more than one file selected
                    if (pickedFiles!.length > 1) {
                      await _handleMultipleDocumentUpload(
                        context,
                        claimDetails,
                        newCategoryName!,
                        pickedFiles!,
                        comment,
                      );
                    } else {
                      // Use single file upload for backward compatibility
                      await _handleNewDocumentUpload(
                        context,
                        claimDetails,
                        newCategoryName!,
                        pickedFiles!.first,
                        comment,
                      );
                    }
                  } else if (pickedFiles == null || pickedFiles!.isEmpty) {
                    NotificationService.showError(
                      context,
                      'Please select at least one file to upload.',
                    );
                  }
                },
              ),
            ],
            child: StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
                dialogSetState = setState;
                return Form(
                  key: formKey,
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.8,
                    constraints: const BoxConstraints(maxWidth: 500),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        ShadInputFormField(
                          id: 'categoryName',
                          label: const Text('Document Category Name'),
                          placeholder: const Text(
                            'e.g., Witness Statement - John Doe',
                          ),
                          onSaved: (value) => newCategoryName = value,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Category name is required.';
                            }
                            if (claimDetails.documentRepository.any(
                              (cat) =>
                                  cat.logicalName.toLowerCase() ==
                                  value.toLowerCase(),
                            )) {
                              return 'This category name already exists.';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        ShadInputFormField(
                          id: 'comment',
                          label: const Text('Comment/Notes (Optional)'),
                          placeholder: const Text('Initial version notes...'),
                          onSaved: (value) => comment = value,
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            ShadButton.outline(
                              child: const Text('Select File'),
                              onPressed: () async {
                                FilePickerResult?
                                result = await FilePicker.platform.pickFiles(
                                  type: FileType.custom,
                                  allowedExtensions: [
                                    'pdf',
                                    'jpg',
                                    'jpeg',
                                    'png',
                                    'gif',
                                  ],
                                  withData: true,
                                  allowMultiple:
                                      true, // Enable multiple file selection
                                );
                                if (result != null && result.files.isNotEmpty) {
                                  dialogSetState?.call(() {
                                    pickedFile =
                                        result
                                            .files
                                            .first; // For now, use first file
                                    // Store all selected files for multiple upload
                                    pickedFiles = result.files;
                                  });
                                }
                              },
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    pickedFiles == null || pickedFiles!.isEmpty
                                        ? 'No files selected'
                                        : pickedFiles!.length == 1
                                        ? pickedFiles!.first.name
                                        : '${pickedFiles!.length} files selected',
                                    style: theme.textTheme.muted,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  if (pickedFiles != null &&
                                      pickedFiles!.length > 1)
                                    ...pickedFiles!
                                        .take(3)
                                        .map(
                                          (file) => Padding(
                                            padding: const EdgeInsets.only(
                                              left: 8.0,
                                              top: 2.0,
                                            ),
                                            child: Text(
                                              '• ${file.name}',
                                              style: theme.textTheme.muted
                                                  .copyWith(fontSize: 12),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                  if (pickedFiles != null &&
                                      pickedFiles!.length > 3)
                                    Padding(
                                      padding: const EdgeInsets.only(
                                        left: 8.0,
                                        top: 2.0,
                                      ),
                                      child: Text(
                                        '... and ${pickedFiles!.length - 3} more',
                                        style: theme.textTheme.muted.copyWith(
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        if (pickedFiles == null || pickedFiles!.isEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Text(
                              'Please select at least one file.',
                              style: TextStyle(
                                color: theme.colorScheme.destructive,
                                fontSize: 12,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
    );
  }

  Widget _buildExpandableText({required String label, required String text}) {
    final theme = ShadTheme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: theme.textTheme.muted.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: theme.textTheme.p,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  void _showUploadNewVersionModal(
    BuildContext context,
    Claim claimDetails,
    UploadedDocumentCategory category,
  ) {
    final theme = ShadTheme.of(context);
    String? comment;
    PlatformFile? pickedFile;
    StateSetter? dialogSetState;

    showShadDialog(
      context: context,
      builder:
          (context) => ShadDialog.alert(
            title: Text('Upload New Version for ${category.logicalName}'),
            actions: [
              ShadButton.ghost(
                child: const Text('Cancel'),
                onPressed: () => Navigator.of(context).pop(),
              ),
              ShadButton(
                child: const Text('Upload Version'),
                onPressed: () async {
                  if (pickedFile != null) {
                    Navigator.of(context).pop();
                    await _handleNewVersionUpload(
                      context,
                      claimDetails,
                      category,
                      pickedFile!,
                      comment,
                    );
                  } else {
                    NotificationService.showError(
                      context,
                      'Please select a file to upload.',
                    );
                  }
                },
              ),
            ],
            child: StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
                dialogSetState = setState;
                return Container(
                  width: MediaQuery.of(context).size.width * 0.8,
                  constraints: const BoxConstraints(maxWidth: 500),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      ShadInput(
                        placeholder: const Text(
                          'Version Comment/Notes (Optional)',
                        ),
                        onChanged: (value) => comment = value,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          ShadButton.outline(
                            child: const Text('Select File'),
                            onPressed: () async {
                              FilePickerResult? result = await FilePicker
                                  .platform
                                  .pickFiles(
                                    type: FileType.custom,
                                    allowedExtensions: ['pdf', 'docx'],
                                    withData: true,
                                  );
                              if (result != null) {
                                dialogSetState?.call(() {
                                  pickedFile = result.files.first;
                                });
                              }
                            },
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              pickedFile?.name ?? 'No file selected',
                              style: theme.textTheme.muted,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      if (pickedFile == null)
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Text(
                            'Please select a file.',
                            style: TextStyle(
                              color: theme.colorScheme.destructive,
                              fontSize: 12,
                            ),
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
          ),
    );
  }

  void _showEditCommentModal(
    BuildContext context,
    Claim claimDetails,
    UploadedDocumentCategory category,
    DocumentVersion versionToEdit,
  ) {
    final formKey = GlobalKey<FormState>();
    String? updatedComment = versionToEdit.notes;

    showShadDialog(
      context: context,
      builder:
          (context) => ShadDialog.alert(
            title: Text('Edit Comment for ${versionToEdit.filename}'),
            actions: [
              ShadButton.ghost(
                child: const Text('Cancel'),
                onPressed: () => Navigator.of(context).pop(),
              ),
              ShadButton(
                child: const Text('Save Comment'),
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    formKey.currentState!.save();
                    Navigator.of(context).pop();
                    await _handleEditComment(
                      context,
                      claimDetails,
                      category,
                      versionToEdit,
                      updatedComment,
                    );
                  }
                },
              ),
            ],
            child: Form(
              key: formKey,
              child: Container(
                width: MediaQuery.of(context).size.width * 0.8,
                constraints: const BoxConstraints(maxWidth: 500),
                child: ShadInputFormField(
                  id: 'edit_comment',
                  label: const Text('Comment/Notes'),
                  initialValue: updatedComment,
                  placeholder: const Text('Enter comment...'),
                  maxLines: 3,
                  onSaved: (value) => updatedComment = value,
                ),
              ),
            ),
          ),
    );
  }

  void _showVersionHistoryModal(
    BuildContext context,
    UploadedDocumentCategory category,
  ) {
    final sortedVersions = List<DocumentVersion>.from(category.versions)..sort(
      (a, b) =>
          DateTime.parse(b.uploadedAt).compareTo(DateTime.parse(a.uploadedAt)),
    );

    showShadDialog(
      context: context,
      builder:
          (context) => ShadDialog.alert(
            title: Text('Version History for ${category.logicalName}'),
            actions: [
              ShadButton.ghost(
                child: const Text('Close'),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
            child: SizedBox(
              width: double.maxFinite,
              height: MediaQuery.of(context).size.height * 0.6,
              child:
                  sortedVersions.isEmpty
                      ? const Center(
                        child: Text('No version history available.'),
                      )
                      : ListView.builder(
                        shrinkWrap: true,
                        itemCount: sortedVersions.length,
                        itemBuilder: (context, index) {
                          final version = sortedVersions[index];
                          final isCurrent =
                              version.fileId == category.currentVersionFileId;
                          return Card(
                            margin: const EdgeInsets.symmetric(vertical: 4.0),
                            child: Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          version.filename,
                                          style: TextStyle(
                                            fontWeight:
                                                isCurrent
                                                    ? FontWeight.bold
                                                    : FontWeight.w600,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ),
                                      if (isCurrent)
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8.0,
                                            vertical: 4.0,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colors.greenAccent
                                                .withValues(alpha: 0.3),
                                            borderRadius: BorderRadius.circular(
                                              12,
                                            ),
                                          ),
                                          child: const Text(
                                            'Current',
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Uploaded: ${DateFormat.yMMMd().add_jm().format(DateTime.parse(version.uploadedAt).toLocal())}',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  FutureBuilder<String>(
                                    future: _resolveUserName(
                                      version.uploadedBy,
                                    ),
                                    builder: (context, snapshot) {
                                      final userName =
                                          snapshot.data ?? version.uploadedBy;
                                      return Text(
                                        'By: $userName',
                                        style: const TextStyle(fontSize: 12),
                                      );
                                    },
                                  ),
                                  if (version.notes != null &&
                                      version.notes!.isNotEmpty)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 4.0),
                                      child: Text(
                                        'Notes: ${version.notes}',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontStyle: FontStyle.italic,
                                        ),
                                      ),
                                    ),
                                  const SizedBox(height: 8),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Flexible(
                                        child: ShadButton.outline(
                                          size: ShadButtonSize.sm,
                                          onPressed:
                                              () => _downloadDocument(
                                                context,
                                                version,
                                              ),
                                          child: const Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                Icons.download_outlined,
                                                size: 14,
                                              ),
                                              SizedBox(width: 4),
                                              Flexible(
                                                child: Text(
                                                  'Download',
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Flexible(
                                        child: ShadButton.outline(
                                          size: ShadButtonSize.sm,
                                          onPressed: () {
                                            Navigator.of(context).pop();
                                            _previewDocument(context, version);
                                          },
                                          child: const Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                Icons.visibility_outlined,
                                                size: 14,
                                              ),
                                              SizedBox(width: 4),
                                              Flexible(
                                                child: Text(
                                                  'Preview',
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
            ),
          ),
    );
  }

  Future<void> _handleNewDocumentUpload(
    BuildContext context,
    Claim claimDetails,
    String categoryName,
    PlatformFile file,
    String? comment,
  ) async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      final pbClient = PocketBaseService().pb;
      final authModel = pbClient.authStore.record;
      String currentUserId;
      String currentUserName = 'Unknown User';

      if (authModel != null) {
        currentUserId = authModel.id;
        currentUserName =
            authModel.data['name'] as String? ?? 'User ($currentUserId)';
      } else {
        NotificationService.showError(context, 'User not authenticated.');
        if (mounted) setState(() => _isLoading = false);
        return;
      }

      // Check if file bytes are available
      if (file.bytes == null) {
        NotificationService.showError(
          context,
          'File data is not available. Please try selecting the file again.',
        );
        if (mounted) setState(() => _isLoading = false);
        return;
      }

      final multipartFile = http.MultipartFile.fromBytes(
        'document_file',
        file.bytes!,
        filename: file.name,
      );

      // Upload file and create new document category
      await _claimDocumentsService.uploadFileAndCreateCategory(
        fundingApplicationId: claimDetails.id,
        logicalName: categoryName,
        file: multipartFile,
        uploadedBy: currentUserId,
        comment: comment,
      );

      if (mounted) {
        NotificationService.showSuccess(
          context,
          'Document uploaded and category created successfully!',
        );
        _fetchClaimDetails(); // Refresh to show updated data from claim_documents collection
      }
    } catch (e) {
      debugPrint('Error uploading new document: $e');
      if (mounted) {
        NotificationService.showError(
          context,
          'Failed to upload document: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _handleNewVersionUpload(
    BuildContext context,
    Claim claimDetails,
    UploadedDocumentCategory category,
    PlatformFile file,
    String? comment,
  ) async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      final pbClient = PocketBaseService().pb;
      final authModel = pbClient.authStore.record;
      String currentUserId;
      String currentUserName = 'Unknown User';

      if (authModel != null) {
        currentUserId = authModel.id;
        currentUserName =
            authModel.data['name'] as String? ?? 'User ($currentUserId)';
      } else {
        NotificationService.showError(context, 'User not authenticated.');
        if (mounted) setState(() => _isLoading = false);
        return;
      }

      // Check if file bytes are available
      if (file.bytes == null) {
        NotificationService.showError(
          context,
          'File data is not available. Please try selecting the file again.',
        );
        if (mounted) setState(() => _isLoading = false);
        return;
      }

      final multipartFile = http.MultipartFile.fromBytes(
        'document_file',
        file.bytes!,
        filename: file.name,
      );

      // Upload file and add new version
      await _claimDocumentsService.uploadFileAndAddVersion(
        fundingApplicationId: claimDetails.id,
        logicalName: category.logicalName,
        file: multipartFile,
        uploadedBy: currentUserId,
        comment: comment,
      );

      if (mounted) {
        NotificationService.showSuccess(
          context,
          'New version uploaded successfully!',
        );
        _fetchClaimDetails(); // Refresh to show updated data from claim_documents collection
      }
    } catch (e) {
      debugPrint('Error uploading new version: $e');
      if (mounted) {
        NotificationService.showError(
          context,
          'Failed to upload new version: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _handleMultipleDocumentUpload(
    BuildContext context,
    Claim claimDetails,
    String categoryName,
    List<PlatformFile> files,
    String? comment,
  ) async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      final pbClient = PocketBaseService().pb;
      final authModel = pbClient.authStore.record;
      String currentUserId;

      if (authModel != null) {
        currentUserId = authModel.id;
      } else {
        NotificationService.showError(context, 'User not authenticated.');
        if (mounted) setState(() => _isLoading = false);
        return;
      }

      // Check if all files have bytes available
      for (final file in files) {
        if (file.bytes == null) {
          NotificationService.showError(
            context,
            'File data is not available for ${file.name}. Please try selecting the files again.',
          );
          if (mounted) setState(() => _isLoading = false);
          return;
        }
      }

      // Create MultipartFile objects for all files
      final multipartFiles = <http.MultipartFile>[];
      for (final file in files) {
        final multipartFile = http.MultipartFile.fromBytes(
          'document_file', // All files use the same field name
          file.bytes!,
          filename: file.name,
        );
        multipartFiles.add(multipartFile);
      }

      // Upload all files and create new document category
      await _claimDocumentsService.uploadFilesAndCreateCategory(
        fundingApplicationId: claimDetails.id,
        logicalName: categoryName,
        files: multipartFiles,
        uploadedBy: currentUserId,
        comment: comment,
      );

      if (mounted) {
        NotificationService.showSuccess(
          context,
          '${files.length} documents uploaded and category created successfully!',
        );
        _fetchClaimDetails(); // Refresh to show updated data from claim_documents collection
      }
    } catch (e) {
      debugPrint('Error uploading multiple documents: $e');
      if (mounted) {
        NotificationService.showError(
          context,
          'Failed to upload documents: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _handleEditComment(
    BuildContext context,
    Claim claimDetails,
    UploadedDocumentCategory category,
    DocumentVersion versionToEdit,
    String? updatedComment,
  ) async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      // Update the version comment in the claim_documents collection
      await _claimDocumentsService.updateVersionComment(
        fundingApplicationId: claimDetails.id,
        logicalName: category.logicalName,
        versionFileId: versionToEdit.fileId,
        newComment: updatedComment,
      );

      if (mounted) {
        NotificationService.showSuccess(
          context,
          'Comment updated successfully.',
        );
        _fetchClaimDetails(); // Refresh to show updated data from claim_documents collection
      }
    } catch (e) {
      debugPrint('Error updating comment: $e');
      if (mounted) {
        NotificationService.showError(
          context,
          'Failed to update comment: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Widget _buildClaimantsTab(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShadCard(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Add Claimant by Email',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ShadInput(
                        controller: _claimantsEmailController,
                        placeholder: const Text('Enter claimant email address'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ShadButton(
                      onPressed: _isAddingClaimant ? null : _addClaimantByEmail,
                      child:
                          _isAddingClaimant
                              ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : const Text('Add Claimant'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Associated Claimants',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
              if (_linkedClaimants.isNotEmpty)
                Text(
                  '${_linkedClaimants.length} claimant${_linkedClaimants.length > 1 ? 's' : ''}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          Expanded(
            child:
                _linkedClaimants.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.people_outline,
                            size: 48,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'No claimants associated with this claim.',
                            style: TextStyle(fontSize: 16, color: Colors.grey),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Add claimants by entering their email address above.',
                            style: TextStyle(fontSize: 14, color: Colors.grey),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    )
                    : ListView.separated(
                      itemCount: _linkedClaimants.length,
                      separatorBuilder:
                          (context, index) => const SizedBox(height: 12),
                      itemBuilder: (context, index) {
                        final claimant = _linkedClaimants[index];
                        return ShadCard(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(
                                    Icons.person,
                                    size: 20,
                                    color: Colors.blue,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      claimant.displayName,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(
                                      Icons.delete_outline,
                                      color: Colors.red,
                                    ),
                                    tooltip: 'Remove claimant',
                                    onPressed:
                                        () => _removeClaimant(claimant.id),
                                  ),
                                ],
                              ),
                              const Divider(),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.email,
                                    size: 16,
                                    color: Colors.grey,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      claimant.displayEmail.isNotEmpty
                                          ? claimant.displayEmail
                                          : 'No email specified',
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Future<void> _addClaimantByEmail() async {
    final email = _claimantsEmailController.text.trim();
    if (email.isEmpty) {
      NotificationService.showError(context, 'Please enter an email address');
      return;
    }

    // Basic email validation
    if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email)) {
      NotificationService.showError(
        context,
        'Please enter a valid email address',
      );
      return;
    }

    setState(() {
      _isAddingClaimant = true;
    });

    try {
      final pbClient = PocketBaseService().pb;

      // Look up claimant by email address
      final claimantsResult = await pbClient
          .collection('claimant_profiles')
          .getList(filter: 'email_address = "$email"', perPage: 1);

      if (claimantsResult.items.isEmpty) {
        if (mounted) {
          NotificationService.showError(
            context,
            'No claimant found with email address: $email',
          );
        }
        return;
      }

      final claimantRecord = claimantsResult.items.first;
      final claimant = ClaimantProfile.fromJson(claimantRecord.toJson());

      // Check if claimant is already associated with this claim
      if (claimant.associatedClaimIds.contains(widget.fundingApplicationId)) {
        if (mounted) {
          NotificationService.showInfo(
            context,
            'This claimant is already associated with this claim',
          );
        }
        return;
      }

      // Add the funding application ID to the claimant's associated_claim_ids
      await pbClient
          .collection('claimant_profiles')
          .update(
            claimant.id,
            body: {'associated_claim_ids+': widget.fundingApplicationId},
          );

      // Create a notification for the claimant
      await _createClaimantNotification(
        claimant.userId ?? '',
        'Added to Claim',
        'You have been added to the claim: ${_claimDetails?.caseTitle ?? 'Unknown Claim'}',
      );

      // Refresh the claimants list
      await _fetchClaimDetails();

      if (mounted) {
        NotificationService.showSuccess(
          context,
          'Claimant ${claimant.displayName} added to claim',
        );
        _claimantsEmailController.clear();
      }
    } catch (e) {
      debugPrint('Error adding claimant: $e');
      if (mounted) {
        NotificationService.showError(
          context,
          'Failed to add claimant: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAddingClaimant = false;
        });
      }
    }
  }

  Future<void> _removeClaimant(String claimantId) async {
    // Show confirmation dialog
    final confirmed = await showShadDialog<bool>(
      context: context,
      builder:
          (context) => ShadDialog(
            title: const Text('Remove Claimant'),
            description: const Text(
              'Are you sure you want to remove this claimant from the claim?',
            ),
            actions: [
              ShadButton.outline(
                child: const Text('Cancel'),
                onPressed: () => Navigator.of(context).pop(false),
              ),
              ShadButton.destructive(
                child: const Text('Remove'),
                onPressed: () => Navigator.of(context).pop(true),
              ),
            ],
          ),
    );

    if (confirmed != true) return;

    try {
      final pbClient = PocketBaseService().pb;

      // Remove the funding application ID from the claimant's associated_claim_ids
      await pbClient
          .collection('claimant_profiles')
          .update(
            claimantId,
            body: {'associated_claim_ids-': widget.fundingApplicationId},
          );

      // Refresh the claimants list
      await _fetchClaimDetails();

      if (mounted) {
        NotificationService.showSuccess(context, 'Claimant removed from claim');
      }
    } catch (e) {
      debugPrint('Error removing claimant: $e');
      if (mounted) {
        NotificationService.showError(
          context,
          'Failed to remove claimant: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _createClaimantNotification(
    String userId,
    String title,
    String message,
  ) async {
    if (userId.isEmpty) return;

    try {
      // Use the new notification integration service for comprehensive notifications
      final notificationService = NotificationIntegrationService(
        PocketBaseService(),
      );

      await notificationService.createClaimNotification(
        claimId: widget.fundingApplicationId,
        title: title,
        message: message,
        recipientIds: [userId],
        claimStatus: _claimDetails?.currentStatus ?? 'unknown',
        claimData: {
          'application_id': widget.fundingApplicationId,
          'updated_by': PocketBaseService().pb.authStore.record?.id,
          'update_timestamp': DateTime.now().toIso8601String(),
        },
      );

      LoggerService.info(
        'Comprehensive claimant notification created for user: $userId',
      );
    } catch (e) {
      LoggerService.error('Failed to create claimant notification', e);
      // Non-critical, so we don't show an error to the user
    }
  }

  Future<void> _createSolicitorNotification(
    String userId,
    String title,
    String message,
  ) async {
    if (userId.isEmpty) return;

    try {
      await PocketBaseService().pb
          .collection('notifications')
          .create(
            body: {
              'recipientId': [userId],
              'title': title,
              'message': message,
              'type': 'claim_update',
              'isRead': false,
            },
          );
    } catch (e) {
      debugPrint('Failed to create solicitor notification: $e');
      // Non-critical, so we don't show an error to the user
    }
  }
}
