import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/barrister.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/expert.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/funding_application_documents_page.dart';
import 'package:uuid/uuid.dart';

// No dialog components needed as we're removing the ability to add new barristers and experts

class FundingApplicationPage extends StatefulWidget {
  const FundingApplicationPage({super.key});

  static const routeName = '/funding-application';

  @override
  State<FundingApplicationPage> createState() => _FundingApplicationPageState();
}

class _FundingApplicationPageState extends State<FundingApplicationPage> {
  final _formKey = GlobalKey<ShadFormState>();
  final _pocketBaseService = PocketBaseService();

  // Form data with required fields initialized
  FundingApplicationData _formData = FundingApplicationData(
    applicationStatus: 'draft',
    solicitorProfileId: [
      "1bllhet27rhaf58",
    ], // Using a known solicitor profile ID
    conditionalFeeAgreementConfirmed: true,
    legalOpinionSuccessProspects: 60,
    stageOfClaim: "STAGE 1: PRE ACTION", // Set initial stage to first stage
  );

  // Form field controllers
  final _claimTitleController = TextEditingController();
  final _minimumValueClaimController = TextEditingController();
  final _requiredFundingAmountController = TextEditingController();
  final _legalOpinionSuccessProspectsController = TextEditingController();

  // Selected dropdown values
  String? _selectedClaimantType;
  String? _selectedClaimIndustry;
  String? _selectedClaimType;
  bool _conditionalFeeAgreementConfirmed = false;

  // Loading and error states
  bool _isLoading = false;
  String? _errorMessage;
  bool _isSavingDraft = false;

  // Barristers and experts state
  List<Barrister> _availableBarristers = [];
  List<Expert> _availableExperts = [];
  List<Barrister> _selectedBarristers = [];
  List<Expert> _selectedExperts = [];
  bool _isLoadingBarristers = false;
  bool _isLoadingExperts = false;

  // Expert type options
  final List<String> _expertTypeOptions = [
    'legal',
    'financial',
    'engineer',
    'construction',
    'other',
  ];

  // Dropdown options
  final List<String> _claimantTypeOptions = [
    'Individual',
    'Single Claimants',
    'Group Claimants',
    'Other Legal Entity',
    'Other',
  ];

  final List<String> _claimIndustryOptions = [
    'Aerospace',
    'Automotive',
    'Aviation',
    'Banking, insurance and other financial services',
    'Chemicals',
    'Construction and Housing',
    'Construction Products',
    'Consumer Goods',
    'Creative, cultural, tourism and sport',
    'Data Protection',
    'Defence',
    'Digital, technology and computer services',
    'Drivers',
    'Ecommerce',
    'Education',
    'Electricity including Renewables',
    'Electronics, Machinery and Parts',
    'Energy',
    'Farming, food and drink sectors',
    'Fisheries',
    'Gas Markets',
    'Health and Care Sector',
    'Life Sciences',
    'Media and Broadcasting',
    'Mining and non-metal manufacturing',
    'Natural environment',
    'Nuclear',
    'Oil and Gas Production',
    'Parcel Delivery Services',
    'Pesticides',
    'Professional Business services',
    'Public Procurement Policy',
    'Retail',
    'Research & Innovation',
    'Space',
    'Steel and metal manufacturing',
    'Telecoms',
    'Transport and haulage',
    'Veterinary Sector',
  ];

  // Claim type options grouped by category
  final Map<String, List<String>> _claimTypeOptionsGrouped = {
    'Construction': [
      'Construction - Property Developer',
      'Construction - Insurer',
      'Construction - Other',
    ],
    'Professional Negligence': [
      'Professional Negligence - Solicitor',
      'Professional Negligence - Barrister',
      'Professional Negligence - Accountant',
      'Professional Negligence - Auditor',
      'Professional Negligence - Surveyor',
      'Professional Negligence - Architect',
      'Professional Negligence - Valuer',
      'Professional Negligence - Other',
    ],
  };

  // Flattened list for the actual dropdown
  late List<String> _claimTypeOptions;

  @override
  void initState() {
    super.initState();
    // Flatten the grouped options for the dropdown
    _claimTypeOptions = [
      ..._claimTypeOptionsGrouped['Construction']!,
      ..._claimTypeOptionsGrouped['Professional Negligence']!,
    ];

    // Initialize form field controllers with default values
    _claimTitleController.text = "Sample Claim Title";
    _minimumValueClaimController.text = "5000000";
    _requiredFundingAmountController.text = "1000000";
    _legalOpinionSuccessProspectsController.text = "60";

    // Initialize dropdown values
    _selectedClaimantType = _claimantTypeOptions.first;
    _selectedClaimIndustry = _claimIndustryOptions.first;
    _selectedClaimType = _claimTypeOptions.first;
    _conditionalFeeAgreementConfirmed = true;

    // Fetch barristers and experts
    _fetchBarristers();
    _fetchExperts();
  }

  // Fetch barristers from PocketBase
  Future<void> _fetchBarristers() async {
    setState(() {
      _isLoadingBarristers = true;
    });

    try {
      final records = await _pocketBaseService.getFullList(
        collectionName: 'barristers',
      );

      final barristers =
          records.map((record) {
            final data = Map<String, dynamic>.from(record.data);
            data['id'] = record.id;
            // Ensure all required fields for Barrister.fromJson are present
            data.putIfAbsent('funding_applications', () => <String>[]);
            return Barrister.fromJson(data);
          }).toList();

      if (!mounted) return;
      setState(() {
        _availableBarristers = barristers;
        _isLoadingBarristers = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingBarristers = false;
      });
    }
  }

  // Fetch experts from PocketBase
  Future<void> _fetchExperts() async {
    setState(() {
      _isLoadingExperts = true;
    });

    try {
      final records = await _pocketBaseService.getFullList(
        collectionName: 'experts',
      );

      final experts =
          records.map((record) {
            // Expert.fromJson(record.data..addAll({'id': record.id}));
            final data = Map<String, dynamic>.from(record.data);
            data['id'] = record.id;
            // Ensure all required fields for Barrister.fromJson are present
            data.putIfAbsent('funding_applications', () => <String>[]);
            return Expert.fromJson(data);
          }).toList();

      if (!mounted) return;
      setState(() {
        _availableExperts = experts;
        _isLoadingExperts = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingExperts = false;
      });
    }
  }

  // Methods for creating new barristers and experts removed as we're no longer allowing this in this page

  // Add a barrister to the selected list
  void _addBarrister(Barrister barrister) {
    if (!_selectedBarristers.any((b) => b.id == barrister.id)) {
      setState(() {
        _selectedBarristers.add(barrister);
      });

      // Update form data with the new barrister
      _updateBarristersInFormData();

      // If we have a funding application ID, update the barrister's claims
      if (_formData.id != null &&
          _formData.id!.isNotEmpty &&
          barrister.id != null) {
        _updateBarristerClaims(barrister.id!, _formData.id!);
      }
    }
  }

  // Add an expert to the selected list
  void _addExpert(Expert expert) {
    if (!_selectedExperts.any((e) => e.id == expert.id)) {
      setState(() {
        _selectedExperts.add(expert);
      });

      // Update form data with the new expert
      _updateExpertsInFormData();

      // If we have a funding application ID, update the expert's claims
      if (_formData.id != null &&
          _formData.id!.isNotEmpty &&
          expert.id != null) {
        _updateExpertClaims(expert.id!, _formData.id!);
      }
    }
  }

  // Remove a barrister from the selected list
  void _removeBarrister(Barrister barrister) {
    setState(() {
      _selectedBarristers.removeWhere((b) => b.id == barrister.id);
    });

    // Update form data with the removed barrister
    _updateBarristersInFormData();

    // If we have a funding application ID, remove it from the barrister's claims
    if (_formData.id != null &&
        _formData.id!.isNotEmpty &&
        barrister.id != null) {
      _removeClaimFromBarrister(barrister.id!, _formData.id!);
    }
  }

  // Remove an expert from the selected list
  void _removeExpert(Expert expert) {
    setState(() {
      _selectedExperts.removeWhere((e) => e.id == expert.id);
    });

    // Update form data with the removed expert
    _updateExpertsInFormData();

    // If we have a funding application ID, remove it from the expert's claims
    if (_formData.id != null && _formData.id!.isNotEmpty && expert.id != null) {
      _removeClaimFromExpert(expert.id!, _formData.id!);
    }
  }

  // Remove a funding application from a barrister's claims
  Future<void> _removeClaimFromBarrister(
    String barristerId,
    String fundingApplicationId,
  ) async {
    try {
      // Use PocketBase's special syntax for relation fields to remove the funding application ID
      await _pocketBaseService.pb
          .collection('barristers')
          .update(barristerId, body: {'-claims': fundingApplicationId});
    } catch (e) {
      // Handle error - using a logger would be better in production
      // ignore: avoid_print
      print('Error removing claim from barrister: ${e.toString()}');
    }
  }

  // Remove a funding application from an expert's claims
  Future<void> _removeClaimFromExpert(
    String expertId,
    String fundingApplicationId,
  ) async {
    try {
      // Use PocketBase's special syntax for relation fields to remove the funding application ID
      await _pocketBaseService.pb
          .collection('experts')
          .update(expertId, body: {'-claims': fundingApplicationId});
    } catch (e) {
      // Handle error - using a logger would be better in production
      // ignore: avoid_print
      print('Error removing claim from expert: ${e.toString()}');
    }
  }

  // Update form data with the selected barristers
  void _updateBarristersInFormData() {
    final barristerIds =
        _selectedBarristers
            .where((b) => b.id != null)
            .map((b) => b.id!)
            .toList();
    _formData = _formData.copyWith(
      barristers: barristerIds,
      barristersData: _selectedBarristers,
    );
  }

  // Update form data with the selected experts
  void _updateExpertsInFormData() {
    final expertIds =
        _selectedExperts.where((e) => e.id != null).map((e) => e.id!).toList();
    _formData = _formData.copyWith(
      experts: expertIds,
      expertsData: _selectedExperts,
    );
  }

  // Build the barristers section
  Widget _buildBarristersSection(BuildContext context) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      title: Text('Barristers', style: theme.textTheme.large),
      description: Text(
        'Select barristers for this claim (new barristers can be added in edit mode)',
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display selected barristers
          if (_selectedBarristers.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Assigned Barristers:',
              style: theme.textTheme.small.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...List.generate(_selectedBarristers.length, (index) {
              final barrister = _selectedBarristers[index];
              return ListTile(
                title: Text(barrister.barristerWithConduct),
                subtitle: Text(
                  '${barrister.barristerChambers} • ${barrister.email}',
                ),
                trailing: IconButton(
                  icon: const Icon(Icons.delete_outline, color: Colors.red),
                  onPressed: () => _removeBarrister(barrister),
                ),
              );
            }),
            const Divider(),
          ],

          const SizedBox(height: 16),

          // Add barrister section
          Builder(
            builder: (context) {
              // Get available barristers that aren't already selected
              final availableToSelect =
                  _availableBarristers
                      .where(
                        (b) => !_selectedBarristers.any((sb) => sb.id == b.id),
                      )
                      .toList();

              if (availableToSelect.isEmpty) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    'All available barristers have been selected.',
                    style: theme.textTheme.muted,
                  ),
                );
              }

              // Create dropdown items
              final dropdownItems =
                  availableToSelect.map((barrister) {
                    return DropdownMenuItem<String>(
                      value: barrister.id,
                      child: Text(
                        '${barrister.barristerWithConduct} (${barrister.barristerChambers})',
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }).toList();

              // Use a unique key to force rebuild
              return DropdownButtonFormField<String>(
                key: UniqueKey(),
                decoration: const InputDecoration(
                  labelText: 'Select Barrister',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 16,
                  ),
                ),
                isExpanded: true,
                hint: const Text('Select a barrister'),
                items: dropdownItems,
                onChanged: (value) {
                  if (value != null) {
                    final barrister = _availableBarristers.firstWhere(
                      (b) => b.id == value,
                    );
                    _addBarrister(barrister);
                  }
                },
              );
            },
          ),

          if (_isLoadingBarristers)
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
    );
  }

  // Build the experts section
  Widget _buildExpertsSection(BuildContext context) {
    final theme = ShadTheme.of(context);

    return ShadCard(
      title: Text('Expert Witnesses', style: theme.textTheme.large),
      description: Text(
        'Select expert witnesses for this claim (new experts can be added in edit mode)',
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display selected experts
          if (_selectedExperts.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'Assigned Expert Witnesses:',
              style: theme.textTheme.small.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...List.generate(_selectedExperts.length, (index) {
              final expert = _selectedExperts[index];
              return ListTile(
                title: Text(expert.name),
                subtitle: Text(
                  '${expert.firmName} • ${expert.type} • ${expert.email}',
                ),
                trailing: IconButton(
                  icon: const Icon(Icons.delete_outline, color: Colors.red),
                  onPressed: () => _removeExpert(expert),
                ),
              );
            }),
            const Divider(),
          ],

          const SizedBox(height: 16),

          // Add expert section
          Builder(
            builder: (context) {
              // Get available experts that aren't already selected
              final availableToSelect =
                  _availableExperts
                      .where(
                        (e) => !_selectedExperts.any((se) => se.id == e.id),
                      )
                      .toList();

              if (availableToSelect.isEmpty) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    'All available expert witnesses have been selected.',
                    style: theme.textTheme.muted,
                  ),
                );
              }

              // Create dropdown items
              final dropdownItems =
                  availableToSelect.map((expert) {
                    return DropdownMenuItem<String>(
                      value: expert.id,
                      child: Text(
                        '${expert.name} (${expert.type})',
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }).toList();

              // Use a unique key to force rebuild
              return DropdownButtonFormField<String>(
                key: UniqueKey(),
                decoration: const InputDecoration(
                  labelText: 'Select Expert Witness',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 16,
                  ),
                ),
                isExpanded: true,
                hint: const Text('Select an expert witness'),
                items: dropdownItems,
                onChanged: (value) {
                  if (value != null) {
                    final expert = _availableExperts.firstWhere(
                      (e) => e.id == value,
                    );
                    _addExpert(expert);
                  }
                },
              );
            },
          ),

          if (_isLoadingExperts)
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
    );
  }

  // Dialog methods removed as we're no longer allowing adding new barristers/experts in this page

  @override
  void dispose() {
    _claimTitleController.dispose();
    _minimumValueClaimController.dispose();
    _requiredFundingAmountController.dispose();
    _legalOpinionSuccessProspectsController.dispose();
    super.dispose();
  }

  // Update the claims field in a barrister record
  Future<void> _updateBarristerClaims(
    String barristerId,
    String fundingApplicationId,
  ) async {
    try {
      // Use PocketBase's special syntax for relation fields to append the new funding application ID
      await _pocketBaseService.pb
          .collection('barristers')
          .update(barristerId, body: {'claims+': fundingApplicationId});
    } catch (e) {
      // Handle error - using a logger would be better in production
      // ignore: avoid_print
      print('Error updating barrister claims: ${e.toString()}');
    }
  }

  // Update the claims field in an expert record
  Future<void> _updateExpertClaims(
    String expertId,
    String fundingApplicationId,
  ) async {
    try {
      // Use PocketBase's special syntax for relation fields to append the new funding application ID
      await _pocketBaseService.pb
          .collection('experts')
          .update(expertId, body: {'claims+': fundingApplicationId});
    } catch (e) {
      // Handle error - using a logger would be better in production
      // ignore: avoid_print
      print('Error updating expert claims: ${e.toString()}');
    }
  }

  // Save draft to PocketBase and prepare for document management
  Future<bool> _saveDraft() async {
    if (_isSavingDraft) return false;

    setState(() {
      _isSavingDraft = true;
      _errorMessage = null;
    });

    try {
      // Collect form data
      _updateFormDataFromFields();

      // Determine if this is an update or create operation
      final bool isUpdate = _formData.id != null && _formData.id!.isNotEmpty;

      // Convert to JSON for PocketBase - use appropriate method based on operation
      final Map<String, dynamic> jsonData =
          isUpdate
              ? _formData.toJson()
              : _formData
                  .toJsonForCreate(); // This ensures no ID field for new records

      // Remove deprecated document_repository field if it exists
      jsonData.remove('document_repository');

      // Remove legacy document upload fields as we're using claim_documents collection
      jsonData.remove('schedule_of_costs_doc_uploaded');
      jsonData.remove('legal_opinion_doc_uploaded');
      jsonData.remove('risk_committee_approval_doc_uploaded');

      // Additional safety: Explicitly remove any id-related fields for new records
      if (!isUpdate) {
        jsonData.remove('id');
        jsonData.remove('ID');
        jsonData.remove('Id');
        // Remove any fields with empty string values that might cause validation issues
        jsonData.removeWhere(
          (key, value) =>
              value == '' ||
              value == null ||
              (value is String && value.trim().isEmpty),
        );
      }

      // Debug: Log the JSON data being sent using proper logging
      LoggerService.debug('JSON data being sent to PocketBase');
      LoggerService.debug(
        '_formData.id: "${_formData.id}" (null: ${_formData.id == null})',
      );
      LoggerService.debug('isUpdate: $isUpdate');
      LoggerService.debug('jsonData keys: ${jsonData.keys.toList()}');
      LoggerService.debug('jsonData values: ${jsonData.values.toList()}');
      LoggerService.debug(
        'jsonData contains id key: ${jsonData.containsKey('id')}',
      );
      if (jsonData.containsKey('id')) {
        LoggerService.debug(
          'id value: "${jsonData['id']}" (type: ${jsonData['id'].runtimeType})',
        );
      }
      LoggerService.debug('Full jsonData: $jsonData');

      // Save to PocketBase
      if (isUpdate) {
        // Update existing draft
        await _pocketBaseService.updateRecord(
          collectionName: 'funding_applications',
          recordId: _formData.id!,
          data: jsonData,
        );

        // Update the claims field in barristers and experts records
        if (_formData.barristers != null && _formData.barristers!.isNotEmpty) {
          for (final barristerId in _formData.barristers!) {
            await _updateBarristerClaims(barristerId, _formData.id!);
          }
        }

        if (_formData.experts != null && _formData.experts!.isNotEmpty) {
          for (final expertId in _formData.experts!) {
            await _updateExpertClaims(expertId, _formData.id!);
          }
        }
      } else {
        // Create new draft - this will generate a valid claim ID for document management
        final uuid = Uuid(); // Instantiate Uuid
        final String newCustomId =
            uuid.v4().replaceAll('-', '').toLowerCase();
        jsonData['id'] = newCustomId; // Add the custom ID to the payload

        final newRecord = await _pocketBaseService.createRecord(
          collectionName: 'funding_applications',
          data: jsonData,
        );

        // Update local form data with the new CUSTOM ID we generated
        _formData = _formData.copyWith(id: newCustomId);

        // Update the claims field in barristers and experts records
        if (_formData.barristers != null && _formData.barristers!.isNotEmpty) {
          for (final barristerId in _formData.barristers!) {
            await _updateBarristerClaims(barristerId, _formData.id!);
          }
        }

        if (_formData.experts != null && _formData.experts!.isNotEmpty) {
          for (final expertId in _formData.experts!) {
            await _updateExpertClaims(expertId, _formData.id!);
          }
        }
      }

      setState(() {
        _isSavingDraft = false;
      });

      return true;
    } catch (e) {
      // Use centralized error mapping for user-friendly messages
      final friendlyError = PocketBaseService.mapPocketBaseError(e);

      setState(() {
        _isSavingDraft = false;
        _errorMessage = friendlyError.message;
      });

      return false;
    }
  }

  // Update form data from field values
  void _updateFormDataFromFields() {
    // Parse numeric values
    double? minimumValueClaim;
    if (_minimumValueClaimController.text.isNotEmpty) {
      minimumValueClaim = double.tryParse(_minimumValueClaimController.text);
    }

    double? requiredFundingAmount;
    if (_requiredFundingAmountController.text.isNotEmpty) {
      requiredFundingAmount = double.tryParse(
        _requiredFundingAmountController.text,
      );
    }

    int? legalOpinionSuccessProspects;
    if (_legalOpinionSuccessProspectsController.text.isNotEmpty) {
      legalOpinionSuccessProspects = int.tryParse(
        _legalOpinionSuccessProspectsController.text,
      );
    }

    // We're using the solicitorProfileId that was set in the constructor
    // No need to update it here as it's already set

    // Update form data
    _formData = _formData.copyWith(
      claimTitle:
          _claimTitleController.text.isNotEmpty
              ? _claimTitleController.text
              : null,
      minimumValueClaim: minimumValueClaim,
      requiredFundingAmount: requiredFundingAmount,
      claimantType: _selectedClaimantType,
      claimIndustry: _selectedClaimIndustry,
      claimType: _selectedClaimType,
      conditionalFeeAgreementConfirmed: _conditionalFeeAgreementConfirmed,
      legalOpinionSuccessProspects: legalOpinionSuccessProspects,
      // Always ensure the stage is set to the first stage for new applications
      stageOfClaim: _formData.stageOfClaim ?? "STAGE 1: PRE ACTION",
      // Include barristers and experts
      // Always persist the *current* selection – an empty list means
      // “clear the relation” rather than “leave unchanged”.
      barristers: _selectedBarristers.map((b) => b.id!).toList(),
      barristersData: List<Barrister>.from(_selectedBarristers),
      experts: _selectedExperts.map((e) => e.id!).toList(),
      expertsData: List<Expert>.from(_selectedExperts),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('New Funding Application'),
        // Potentially use Shadcn app bar if available and desired
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: ShadForm(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Claim Details', style: theme.textTheme.h4),
              const SizedBox(height: 20),

              ShadInputFormField(
                id: 'claim_title',
                label: const Text('Claim Title*'),
                placeholder: const Text('e.g., Breach of Contract Case XYZ'),
                controller: _claimTitleController,
                validator: (v) {
                  if (v.isEmpty) {
                    return 'Claim title is required.';
                  }
                  if (v.length < 5) {
                    return 'Claim title must be at least 5 characters.';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Claim Classification Section
              Container(
                padding: const EdgeInsets.all(16),
                width: double.infinity, // Ensure container takes full width
                decoration: BoxDecoration(
                  color: theme.colorScheme.muted.withAlpha(20),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment:
                      CrossAxisAlignment
                          .stretch, // Make children stretch to full width
                  children: [
                    Text(
                      'Claim Classification',
                      style: theme.textTheme.large.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Please categorize your claim to help us process your application more efficiently.',
                      style: theme.textTheme.muted,
                    ),
                    const SizedBox(height: 16),

                    // Claimant Type Dropdown
                    DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: 'Claimant Type*',
                        border: OutlineInputBorder(),
                        // Ensure the dropdown fits within the container
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 16,
                        ),
                      ),
                      isExpanded: true, // This prevents overflow
                      hint: const Text('Select claimant type'),
                      value: _selectedClaimantType,
                      items:
                          _claimantTypeOptions.map((type) {
                            return DropdownMenuItem<String>(
                              value: type,
                              child: Container(
                                constraints: BoxConstraints(
                                  maxWidth: 300,
                                ), // Limit width of dropdown items
                                child: Text(
                                  type,
                                  overflow:
                                      TextOverflow
                                          .ellipsis, // Handle text overflow
                                  style: TextStyle(
                                    fontSize: 14,
                                  ), // Slightly smaller font for long items
                                ),
                              ),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedClaimantType = value;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select a claimant type';
                        }
                        return null;
                      },
                      onSaved: (value) {
                        // Save to form
                        _formKey.currentState?.fields['claimant_type']
                            ?.didChange(value);
                        _selectedClaimantType = value;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Claim Industry Dropdown
                    DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: 'Claim Industry*',
                        border: OutlineInputBorder(),
                        // Ensure the dropdown fits within the container
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 16,
                        ),
                      ),
                      isExpanded: true, // This prevents overflow
                      hint: const Text('Select industry sector'),
                      value: _selectedClaimIndustry,
                      items:
                          _claimIndustryOptions.map((industry) {
                            return DropdownMenuItem<String>(
                              value: industry,
                              child: Container(
                                constraints: BoxConstraints(
                                  maxWidth: 300,
                                ), // Limit width of dropdown items
                                child: Text(
                                  industry,
                                  overflow:
                                      TextOverflow
                                          .ellipsis, // Handle text overflow
                                  style: TextStyle(
                                    fontSize: 14,
                                  ), // Slightly smaller font for long items
                                ),
                              ),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedClaimIndustry = value;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select an industry';
                        }
                        return null;
                      },
                      onSaved: (value) {
                        // Save to form
                        _formKey.currentState?.fields['claim_industry']
                            ?.didChange(value);
                        _selectedClaimIndustry = value;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Claim Type Dropdown
                    DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: 'Claim Type*',
                        border: OutlineInputBorder(),
                        // Ensure the dropdown fits within the container
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 16,
                        ),
                      ),
                      isExpanded: true, // This prevents overflow
                      hint: const Text('Select claim type'),
                      value: _selectedClaimType,
                      items:
                          _claimTypeOptions.map((type) {
                            return DropdownMenuItem<String>(
                              value: type,
                              child: Container(
                                constraints: BoxConstraints(
                                  maxWidth: 300,
                                ), // Limit width of dropdown items
                                child: Text(
                                  type,
                                  overflow:
                                      TextOverflow
                                          .ellipsis, // Handle text overflow
                                  style: TextStyle(
                                    fontSize: 14,
                                  ), // Slightly smaller font for long items
                                ),
                              ),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedClaimType = value;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please select a claim type';
                        }
                        return null;
                      },
                      onSaved: (value) {
                        // Save to form
                        _formKey.currentState?.fields['claim_type']?.didChange(
                          value,
                        );
                        _selectedClaimType = value;
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'minimum_value_claim',
                label: const Text('Minimum Value of Claim (£)*'),
                placeholder: const Text('e.g., 5000000'),
                controller: _minimumValueClaimController,
                keyboardType: TextInputType.number,
                validator: (v) {
                  if (v.isEmpty) {
                    return 'Minimum value of claim is required.';
                  }
                  final numValue = double.tryParse(v);
                  if (numValue == null) {
                    return 'Please enter a valid number.';
                  }
                  if (numValue <= 0) {
                    return 'Value must be greater than zero.';
                  }
                  // Add more specific validation if needed, e.g. PRD.md:59 mentions £5,000,000
                  return null;
                },
              ),
              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'required_funding_amount',
                label: const Text('Required Funding Amount (£)*'),
                placeholder: const Text('e.g., 100000 (max £3,000,000)'),
                controller: _requiredFundingAmountController,
                keyboardType: TextInputType.number,
                validator: (v) {
                  if (v.isEmpty) {
                    return 'Required funding amount is required.';
                  }
                  final numValue = double.tryParse(v);
                  if (numValue == null) {
                    return 'Please enter a valid number.';
                  }
                  if (numValue <= 0) {
                    return 'Amount must be greater than zero.';
                  }
                  if (numValue > 3000000) {
                    return 'Amount cannot exceed £3,000,000.';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              ShadCheckboxFormField(
                id: 'conditional_fee_agreement_confirmed',
                initialValue: _conditionalFeeAgreementConfirmed,
                inputLabel: const Text(
                  'Conditional Fee Agreement (CFA) Confirmed*',
                ),
                validator: (v) {
                  if (!v) {
                    return 'CFA confirmation is required.';
                  }
                  return null;
                },
                onChanged: (value) {
                  setState(() {
                    _conditionalFeeAgreementConfirmed = value;
                  });
                },
              ),
              const SizedBox(height: 16),

              ShadInputFormField(
                id: 'legal_opinion_success_prospects',
                label: const Text('Legal Opinion Success Prospects (%)*'),
                placeholder: const Text('e.g., 65 (for 65%)'),
                controller: _legalOpinionSuccessProspectsController,
                keyboardType: TextInputType.number,
                // A ShadSliderFormField would be ideal here if available.
                // For now, using ShadInputFormField.
                validator: (v) {
                  if (v.isEmpty) {
                    return 'Success prospects are required.';
                  }
                  final numValue = int.tryParse(v);
                  if (numValue == null) {
                    return 'Please enter a valid percentage (0-100).';
                  }
                  if (numValue < 0 || numValue > 100) {
                    return 'Percentage must be between 0 and 100.';
                  }
                  // PRD.md:59 mentions 55% as an example
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Barristers and Experts Section
              Text('Legal Team', style: theme.textTheme.h4),
              const SizedBox(height: 16),

              // Barristers Section
              _buildBarristersSection(context),
              const SizedBox(height: 24),

              // Experts Section
              _buildExpertsSection(context),
              const SizedBox(height: 24),

              Text(
                'Document Uploads',
                style: theme.textTheme.h4.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Note: Document uploads (Schedule of Costs, Risk Committee Approval Doc, Legal Opinion Doc, etc.) will be required in the next step.',
                style: theme.textTheme.muted,
              ),
              const SizedBox(height: 32),

              if (_errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.destructive.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: theme.colorScheme.destructive),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: theme.colorScheme.destructive,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(
                            color: theme.colorScheme.destructive,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ShadButton.outline(
                    onPressed:
                        _isSavingDraft
                            ? null
                            : () async {
                              if (_formKey.currentState!.saveAndValidate()) {
                                final success = await _saveDraft();
                                if (!mounted) return;

                                if (success) {
                                  if (mounted) {
                                    final currentContext = context;
                                    if (currentContext.mounted) {
                                      ScaffoldMessenger.of(
                                        currentContext,
                                      ).showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                            'Draft saved successfully.',
                                          ),
                                        ),
                                      );
                                    }
                                  }
                                } else {
                                  if (mounted) {
                                    final currentContext = context;
                                    if (currentContext.mounted) {
                                      ScaffoldMessenger.of(
                                        currentContext,
                                      ).showSnackBar(
                                        SnackBar(
                                          content: Text(
                                            _errorMessage ??
                                                'Failed to save draft.',
                                          ),
                                          backgroundColor:
                                              theme.colorScheme.destructive,
                                        ),
                                      );
                                    }
                                  }
                                }
                              } else {
                                if (mounted) {
                                  final currentContext = context;
                                  if (currentContext.mounted) {
                                    ScaffoldMessenger.of(
                                      currentContext,
                                    ).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'Please correct the errors in the form.',
                                          style: TextStyle(
                                            color:
                                                theme
                                                    .colorScheme
                                                    .destructiveForeground,
                                          ),
                                        ),
                                        backgroundColor:
                                            theme.colorScheme.destructive,
                                      ),
                                    );
                                  }
                                }
                              }
                            },
                    child:
                        _isSavingDraft
                            ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Text('Save Draft'),
                  ),
                  const SizedBox(width: 16),
                  ShadButton(
                    onPressed: () async {
                      if (_formKey.currentState!.saveAndValidate()) {
                        // Save the form data first to create/update the funding application
                        final success = await _saveDraft();
                        if (!mounted) return;

                        if (success && _formData.id != null) {
                          // Navigate to documents page with the claim ID
                          if (mounted) {
                            final currentContext = context;
                            if (currentContext.mounted) {
                              Navigator.of(currentContext).pushNamed(
                                FundingApplicationDocumentsPage.routeName,
                                arguments: _formData.id!,
                              );
                            }
                          }
                        } else {
                          // Show user-friendly error message
                          if (mounted) {
                            final currentContext = context;
                            if (currentContext.mounted) {
                              ShadToaster.of(currentContext).show(
                                ShadToast.destructive(
                                  title: const Text('Save Failed'),
                                  description: Text(
                                    _errorMessage ??
                                        'Failed to save form data.',
                                  ),
                                ),
                              );
                            }
                          }
                        }
                      } else {
                        // Show validation error
                        if (mounted) {
                          final currentContext = context;
                          if (currentContext.mounted) {
                            ShadToaster.of(currentContext).show(
                              const ShadToast.destructive(
                                title: Text('Validation Error'),
                                description: Text(
                                  'Please correct the errors in the form.',
                                ),
                              ),
                            );
                          }
                        }
                      }
                    },
                    child: const Text('Continue to Document Upload'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
