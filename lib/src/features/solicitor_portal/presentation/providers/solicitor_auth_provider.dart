import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/solicitor_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/services/solicitor_base_service.dart';

/// Authentication state for solicitors
class SolicitorAuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final RecordModel? user;
  final SolicitorProfileModel? profile;
  final String? error;

  const SolicitorAuthState({
    this.isAuthenticated = false,
    this.isLoading = false,
    this.user,
    this.profile,
    this.error,
  });

  SolicitorAuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    RecordModel? user,
    SolicitorProfileModel? profile,
    String? error,
  }) {
    return SolicitorAuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      profile: profile ?? this.profile,
      error: error ?? this.error,
    );
  }

  bool get isSolicitor => user?.data['user_type'] == 'solicitor';
  bool get hasProfile => profile != null;
  bool get isProfileComplete => profile?.isProfileComplete ?? false;
}

/// Solicitor authentication provider
class SolicitorAuthNotifier extends StateNotifier<SolicitorAuthState> {
  final SolicitorBaseService _service;
  final PocketBase _pb;

  SolicitorAuthNotifier(this._service, this._pb)
    : super(const SolicitorAuthState()) {
    _initializeAuth();
  }

  /// Initialize authentication state
  void _initializeAuth() {
    if (_pb.authStore.isValid) {
      final user = _pb.authStore.record;
      if (user != null && user.data['user_type'] == 'solicitor') {
        state = state.copyWith(isAuthenticated: true, user: user);
        _loadSolicitorProfile();
      }
    }
  }

  /// Load solicitor profile
  Future<void> _loadSolicitorProfile() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final profile = await _service.getCurrentSolicitorProfile();

      state = state.copyWith(isLoading: false, profile: profile);
    } catch (e) {
      LoggerService.error('Error loading solicitor profile', e);
      state = state.copyWith(
        isLoading: false,
        error: _service.getErrorMessage(e),
      );
    }
  }

  /// Sign in solicitor
  Future<bool> signIn(String email, String password) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final authData = await _pb
          .collection('users')
          .authWithPassword(email, password);

      if (authData.record.data['user_type'] != 'solicitor') {
        _pb.authStore.clear();
        state = state.copyWith(
          isLoading: false,
          error: 'Invalid user type. This portal is for solicitors only.',
        );
        return false;
      }

      state = state.copyWith(
        isAuthenticated: true,
        isLoading: false,
        user: authData.record,
      );

      // Refresh FCM token after successful solicitor sign in
      try {
        await FirebaseApiService.refreshToken();
        LoggerService.info('FCM token refreshed for solicitor');
      } catch (e) {
        LoggerService.warning('Failed to refresh FCM token for solicitor: $e');
        // Don't fail the sign in process if FCM token refresh fails
      }

      await _loadSolicitorProfile();
      return true;
    } catch (e) {
      LoggerService.error('Solicitor sign in error', e);
      state = state.copyWith(
        isLoading: false,
        error: _service.getErrorMessage(e),
      );
      return false;
    }
  }

  /// Sign out solicitor
  Future<void> signOut() async {
    try {
      // Clear FCM token before signing out
      try {
        await FirebaseApiService.clearToken();
        LoggerService.info('FCM token cleared for solicitor sign out');
      } catch (e) {
        LoggerService.warning('Failed to clear FCM token for solicitor: $e');
        // Continue with sign out even if FCM token clearing fails
      }

      _pb.authStore.clear();
      state = const SolicitorAuthState();
      LoggerService.info('Solicitor signed out successfully');
    } catch (e) {
      LoggerService.error('Error signing out solicitor', e);
    }
  }

  /// Create or update solicitor profile
  Future<bool> createOrUpdateProfile({
    Map<String, dynamic>? notificationPreferences,
    String? lawFirmName,
    String? solicitorName,
    String? position,
    String? contactNumber,
    String? firmAddress,
    String? firmRegistrationNumber,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      SolicitorProfileModel profile;

      if (state.profile == null) {
        // Create new profile
        profile = await _service.createSolicitorProfile(
          lawFirmName: lawFirmName ?? '',
          solicitorName: solicitorName ?? '',
          position: position ?? '',
          contactNumber: contactNumber ?? '',
          firmAddress: firmAddress ?? '',
          firmRegistrationNumber: firmRegistrationNumber ?? '',
          notificationPreferences: notificationPreferences,
        );
      } else {
        // Update existing profile
        profile = await _service.updateSolicitorProfile(
          solicitorName: solicitorName,
          lawFirmName: lawFirmName,
          sraNumber: firmRegistrationNumber,
          firmAddress: firmAddress,
          contactNumber: contactNumber,
          positionInFirm: position,
        );
      }

      state = state.copyWith(isLoading: false, profile: profile);

      return true;
    } catch (e) {
      LoggerService.error('Error creating/updating solicitor profile', e);
      state = state.copyWith(
        isLoading: false,
        error: _service.getErrorMessage(e),
      );
      return false;
    }
  }

  /// Refresh authentication state
  Future<void> refresh() async {
    if (_pb.authStore.isValid) {
      await _loadSolicitorProfile();
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for solicitor authentication
final solicitorAuthProvider =
    StateNotifierProvider<SolicitorAuthNotifier, SolicitorAuthState>((ref) {
      final service = SolicitorBaseService();
      final pocketBase = PocketBaseService().pb;
      return SolicitorAuthNotifier(service, pocketBase);
    });

/// Provider for current solicitor profile
final currentSolicitorProfileProvider = Provider<SolicitorProfileModel?>((ref) {
  final authState = ref.watch(solicitorAuthProvider);
  return authState.profile;
});

/// Provider for solicitor authentication status
final isSolicitorAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(solicitorAuthProvider);
  return authState.isAuthenticated && authState.isSolicitor;
});
