import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/solicitor_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/services/solicitor_base_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/utils/solicitor_profile_validation.dart';
import 'dart:io';

/// State class for solicitor profile management
class SolicitorProfileState {
  final SolicitorProfileModel? profile;
  final bool isLoading;
  final bool isUpdating;
  final bool isUploadingImage;
  final String? error;
  final String? successMessage;
  final String? profilePictureUrl;

  const SolicitorProfileState({
    this.profile,
    this.isLoading = false,
    this.isUpdating = false,
    this.isUploadingImage = false,
    this.error,
    this.successMessage,
    this.profilePictureUrl,
  });

  SolicitorProfileState copyWith({
    SolicitorProfileModel? profile,
    bool? isLoading,
    bool? isUpdating,
    bool? isUploadingImage,
    String? error,
    String? successMessage,
    String? profilePictureUrl,
  }) {
    return SolicitorProfileState(
      profile: profile ?? this.profile,
      isLoading: isLoading ?? this.isLoading,
      isUpdating: isUpdating ?? this.isUpdating,
      isUploadingImage: isUploadingImage ?? this.isUploadingImage,
      error: error,
      successMessage: successMessage,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
    );
  }
}

/// Profile provider for managing solicitor profile state and operations
class SolicitorProfileNotifier extends StateNotifier<SolicitorProfileState> {
  final SolicitorBaseService _service;

  SolicitorProfileNotifier(this._service)
    : super(const SolicitorProfileState());

  /// Load current profile
  Future<void> loadProfile() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final profile = await _service.getCurrentSolicitorProfile();
      String? profilePictureUrl;

      // Generate profile picture URL if profile exists
      if (profile != null) {
        // Get the user record to generate the URL
        final userRecord = await _service.getUserRecord(profile.userId);
        profilePictureUrl = _service.getProfilePictureUrlFromRecord(userRecord);
      }

      state = state.copyWith(
        isLoading: false,
        profile: profile,
        profilePictureUrl: profilePictureUrl,
      );

      LoggerService.info('Solicitor profile loaded successfully');
    } catch (e) {
      LoggerService.error('Error loading solicitor profile', e);
      state = state.copyWith(
        isLoading: false,
        error: _service.getErrorMessage(e),
      );
    }
  }

  /// Update profile information
  Future<bool> updateProfile({
    String? solicitorName,
    String? lawFirmName,
    String? sraNumber,
    String? firmAddress,
    String? contactNumber,
    String? positionInFirm,
  }) async {
    state = state.copyWith(isUpdating: true, error: null, successMessage: null);

    try {
      final updatedProfile = await _service.updateSolicitorProfile(
        solicitorName: solicitorName,
        lawFirmName: lawFirmName,
        sraNumber: sraNumber,
        firmAddress: firmAddress,
        contactNumber: contactNumber,
        positionInFirm: positionInFirm,
      );

      state = state.copyWith(
        isUpdating: false,
        profile: updatedProfile,
        successMessage: 'Profile updated successfully',
      );

      LoggerService.info('Solicitor profile updated successfully');
      return true;
    } catch (e) {
      LoggerService.error('Error updating solicitor profile', e);
      state = state.copyWith(
        isUpdating: false,
        error: _service.getErrorMessage(e),
      );
      return false;
    }
  }

  /// Upload profile picture
  Future<bool> uploadProfilePicture() async {
    state = state.copyWith(isUploadingImage: true, error: null);

    try {
      // Pick image file with specific allowed extensions to avoid iOS issues
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png'],
        allowMultiple: false,
        withData: true,
      );

      if (result == null || result.files.isEmpty) {
        state = state.copyWith(isUploadingImage: false);
        return false;
      }

      final file = result.files.first;

      // Validate image
      final validationError = SolicitorProfileValidation.validateImageFile(
        file.name,
        file.size,
      );
      if (validationError != null) {
        state = state.copyWith(isUploadingImage: false, error: validationError);
        return false;
      }

      // Check if file data is available
      if (file.bytes == null) {
        // Try to read from path if bytes are not available
        if (file.path != null) {
          try {
            final fileBytes = await _readFileFromPath(file.path!);
            await _service.uploadProfilePicture(fileBytes, file.name);
          } catch (pathError) {
            state = state.copyWith(
              isUploadingImage: false,
              error:
                  'Failed to read image file. Please try selecting a different image.',
            );
            return false;
          }
        } else {
          state = state.copyWith(
            isUploadingImage: false,
            error: 'Failed to access image file. Please try again.',
          );
          return false;
        }
      } else {
        // Upload image using bytes
        final bytes = file.bytes!;
        final fileName = file.name;
        await _service.uploadProfilePicture(bytes, fileName);
      }

      // Reload profile to get updated picture URL
      await loadProfile();

      state = state.copyWith(
        isUploadingImage: false,
        successMessage: 'Profile picture updated successfully',
      );

      LoggerService.info('Profile picture uploaded successfully');
      return true;
    } catch (e) {
      LoggerService.error('Error uploading profile picture', e);

      // Provide more specific error messages for common file picker issues
      String errorMessage;
      if (e.toString().contains('file_picker_error')) {
        errorMessage =
            'Unable to process the selected image. Please try selecting a different image file (JPG or PNG).';
      } else if (e.toString().contains('Failed to process any images')) {
        errorMessage =
            'The selected image format is not supported. Please choose a JPG or PNG file.';
      } else if (e.toString().contains('Cannot load representation')) {
        errorMessage =
            'Unable to access the selected image. Please try selecting a different image.';
      } else {
        errorMessage = _service.getErrorMessage(e);
      }

      state = state.copyWith(isUploadingImage: false, error: errorMessage);
      return false;
    }
  }

  /// Helper method to read file from path
  Future<List<int>> _readFileFromPath(String path) async {
    final file = File(path);
    return await file.readAsBytes();
  }

  /// Clear error message
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clear success message
  void clearSuccessMessage() {
    state = state.copyWith(successMessage: null);
  }

  /// Clear all messages
  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }

  @override
  void dispose() {
    _service.dispose();
    super.dispose();
  }
}

/// Provider for solicitor profile management
final solicitorProfileProvider =
    StateNotifierProvider<SolicitorProfileNotifier, SolicitorProfileState>((
      ref,
    ) {
      return SolicitorProfileNotifier(SolicitorBaseService());
    });

/// Provider for solicitor base service
final solicitorBaseServiceProvider = Provider<SolicitorBaseService>((ref) {
  return SolicitorBaseService();
});
