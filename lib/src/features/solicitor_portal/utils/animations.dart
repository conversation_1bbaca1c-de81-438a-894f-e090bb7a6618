import 'package:flutter/material.dart';

/// Animation utilities for the solicitor portal
/// Provides consistent animations and transitions
class SolicitorAnimations {
  // Animation durations
  static const Duration fastDuration = Duration(milliseconds: 200);
  static const Duration normalDuration = Duration(milliseconds: 300);
  static const Duration slowDuration = Duration(milliseconds: 500);

  // Animation curves
  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve bounceCurve = Curves.elasticOut;
  static const Curve slideCurve = Curves.easeOutCubic;

  /// Fade in animation
  static Widget fadeIn({
    required Widget child,
    Duration duration = normalDuration,
    Curve curve = defaultCurve,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      curve: curve,
      tween: Tween(begin: begin, end: end),
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: child,
        );
      },
      child: child,
    );
  }

  /// Slide in animation
  static Widget slideIn({
    required Widget child,
    Duration duration = normalDuration,
    Curve curve = slideCurve,
    SlideDirection direction = SlideDirection.bottom,
    double distance = 0.1,
  }) {
    Offset begin;
    switch (direction) {
      case SlideDirection.top:
        begin = Offset(0, -distance);
        break;
      case SlideDirection.bottom:
        begin = Offset(0, distance);
        break;
      case SlideDirection.left:
        begin = Offset(-distance, 0);
        break;
      case SlideDirection.right:
        begin = Offset(distance, 0);
        break;
    }

    return TweenAnimationBuilder<Offset>(
      duration: duration,
      curve: curve,
      tween: Tween(begin: begin, end: Offset.zero),
      builder: (context, value, child) {
        return Transform.translate(
          offset: value,
          child: child,
        );
      },
      child: child,
    );
  }

  /// Scale in animation
  static Widget scaleIn({
    required Widget child,
    Duration duration = normalDuration,
    Curve curve = bounceCurve,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      curve: curve,
      tween: Tween(begin: begin, end: end),
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: child,
        );
      },
      child: child,
    );
  }

  /// Fade and slide in animation
  static Widget fadeSlideIn({
    required Widget child,
    Duration duration = normalDuration,
    Curve curve = defaultCurve,
    SlideDirection direction = SlideDirection.bottom,
    double slideDistance = 0.1,
    double fadeBegin = 0.0,
    double fadeEnd = 1.0,
  }) {
    return fadeIn(
      duration: duration,
      curve: curve,
      begin: fadeBegin,
      end: fadeEnd,
      child: slideIn(
        duration: duration,
        curve: curve,
        direction: direction,
        distance: slideDistance,
        child: child,
      ),
    );
  }

  /// Staggered list animation
  static Widget staggeredList({
    required List<Widget> children,
    Duration duration = normalDuration,
    Duration staggerDelay = const Duration(milliseconds: 100),
    Curve curve = defaultCurve,
    SlideDirection direction = SlideDirection.bottom,
  }) {
    return Column(
      children: children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;
        
        return TweenAnimationBuilder<double>(
          duration: duration + (staggerDelay * index),
          curve: curve,
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, value, child) {
            return Opacity(
              opacity: value,
              child: Transform.translate(
                offset: Offset(
                  0,
                  (1 - value) * (direction == SlideDirection.bottom ? 20 : -20),
                ),
                child: child,
              ),
            );
          },
          child: child,
        );
      }).toList(),
    );
  }

  /// Page transition animation
  static PageRouteBuilder<T> pageTransition<T>({
    required Widget page,
    Duration duration = normalDuration,
    PageTransitionType type = PageTransitionType.slideFromRight,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        switch (type) {
          case PageTransitionType.fade:
            return FadeTransition(opacity: animation, child: child);
          
          case PageTransitionType.slideFromRight:
            return SlideTransition(
              position: animation.drive(
                Tween(begin: const Offset(1.0, 0.0), end: Offset.zero)
                    .chain(CurveTween(curve: defaultCurve)),
              ),
              child: child,
            );
          
          case PageTransitionType.slideFromLeft:
            return SlideTransition(
              position: animation.drive(
                Tween(begin: const Offset(-1.0, 0.0), end: Offset.zero)
                    .chain(CurveTween(curve: defaultCurve)),
              ),
              child: child,
            );
          
          case PageTransitionType.slideFromBottom:
            return SlideTransition(
              position: animation.drive(
                Tween(begin: const Offset(0.0, 1.0), end: Offset.zero)
                    .chain(CurveTween(curve: defaultCurve)),
              ),
              child: child,
            );
          
          case PageTransitionType.scale:
            return ScaleTransition(scale: animation, child: child);
        }
      },
    );
  }

  /// Shimmer loading animation
  static Widget shimmer({
    required Widget child,
    Color? baseColor,
    Color? highlightColor,
    Duration duration = const Duration(milliseconds: 1500),
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                baseColor ?? Colors.grey[300]!,
                highlightColor ?? Colors.grey[100]!,
                baseColor ?? Colors.grey[300]!,
              ],
              stops: [
                0.0,
                value,
                1.0,
              ],
            ).createShader(bounds);
          },
          child: child,
        );
      },
      child: child,
    );
  }

  /// Bounce animation
  static Widget bounce({
    required Widget child,
    Duration duration = const Duration(milliseconds: 600),
    double begin = 0.8,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      curve: Curves.elasticOut,
      tween: Tween(begin: begin, end: end),
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: child,
        );
      },
      child: child,
    );
  }

  /// Rotation animation
  static Widget rotate({
    required Widget child,
    Duration duration = const Duration(milliseconds: 1000),
    double begin = 0.0,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      curve: defaultCurve,
      tween: Tween(begin: begin, end: end),
      builder: (context, value, child) {
        return Transform.rotate(
          angle: value * 2 * 3.14159,
          child: child,
        );
      },
      child: child,
    );
  }
}

/// Slide direction enum
enum SlideDirection {
  top,
  bottom,
  left,
  right,
}

/// Page transition type enum
enum PageTransitionType {
  fade,
  slideFromRight,
  slideFromLeft,
  slideFromBottom,
  scale,
}
