/// Validation utilities for solicitor profile fields
class SolicitorProfileValidation {
  /// Validate solicitor name
  static String? validateSolicitorName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Solicitor name is required';
    }

    if (value.trim().length < 2) {
      return 'Solicitor name must be at least 2 characters';
    }

    if (value.trim().length > 100) {
      return 'Solicitor name must be no more than 100 characters';
    }

    // Check for valid characters (letters, spaces, hyphens, apostrophes, periods)
    final nameRegex = RegExp(r"^[a-zA-Z\s\-'.]+$");
    if (!nameRegex.hasMatch(value.trim())) {
      return 'Solicitor name can only contain letters, spaces, hyphens, apostrophes, and periods';
    }

    return null;
  }

  /// Validate law firm name
  static String? validateLawFirmName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Law firm name is required';
    }

    if (value.trim().length < 2) {
      return 'Law firm name must be at least 2 characters';
    }

    if (value.trim().length > 150) {
      return 'Law firm name must be no more than 150 characters';
    }

    return null;
  }

  /// Validate SRA number
  static String? validateSraNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // SRA number is optional
    }

    final trimmedValue = value.trim();

    // Basic validation for SRA number format
    if (trimmedValue.length < 6) {
      return 'SRA number must be at least 6 characters';
    }

    if (trimmedValue.length > 20) {
      return 'SRA number must be no more than 20 characters';
    }

    // Allow alphanumeric characters and common separators
    final sraRegex = RegExp(r'^[A-Za-z0-9\-/]+$');
    if (!sraRegex.hasMatch(trimmedValue)) {
      return 'SRA number can only contain letters, numbers, hyphens, and forward slashes';
    }

    return null;
  }

  /// Validate firm address
  static String? validateFirmAddress(String? value) {
    if (value == null || value.isEmpty) {
      return 'Firm address is required';
    }

    if (value.trim().length < 10) {
      return 'Firm address must be at least 10 characters';
    }

    if (value.trim().length > 500) {
      return 'Firm address must be no more than 500 characters';
    }

    return null;
  }

  /// Validate contact number
  static String? validateContactNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Contact number is optional
    }

    final trimmedValue = value.trim();

    // Basic phone number validation
    if (trimmedValue.length < 10) {
      return 'Contact number must be at least 10 digits';
    }

    if (trimmedValue.length > 20) {
      return 'Contact number must be no more than 20 characters';
    }

    // Allow digits, spaces, hyphens, parentheses, and plus sign
    final phoneRegex = RegExp(r'^[\d\s\-\(\)\+]+$');
    if (!phoneRegex.hasMatch(trimmedValue)) {
      return 'Contact number can only contain digits, spaces, hyphens, parentheses, and plus sign';
    }

    return null;
  }

  /// Validate position in firm
  static String? validatePositionInFirm(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Position is optional
    }

    if (value.trim().length > 100) {
      return 'Position must be no more than 100 characters';
    }

    return null;
  }

  /// Validate email format (reused from claimant validation)
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validate phone number (reused from claimant validation)
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Phone number is optional for basic profile
    }

    final trimmedValue = value.trim();

    // Basic phone number validation
    if (trimmedValue.length < 10) {
      return 'Phone number must be at least 10 digits';
    }

    if (trimmedValue.length > 20) {
      return 'Phone number must be no more than 20 characters';
    }

    // Allow digits, spaces, hyphens, parentheses, and plus sign
    final phoneRegex = RegExp(r'^[\d\s\-\(\)\+]+$');
    if (!phoneRegex.hasMatch(trimmedValue)) {
      return 'Phone number can only contain digits, spaces, hyphens, parentheses, and plus sign';
    }

    return null;
  }

  /// Validate name (for user profile fields)
  static String? validateName(String? value, {String fieldName = 'Name'}) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }

    if (value.trim().length < 2) {
      return '$fieldName must be at least 2 characters';
    }

    if (value.trim().length > 50) {
      return '$fieldName must be no more than 50 characters';
    }

    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    final nameRegex = RegExp(r"^[a-zA-Z\s\-']+$");
    if (!nameRegex.hasMatch(value.trim())) {
      return '$fieldName can only contain letters, spaces, hyphens, and apostrophes';
    }

    return null;
  }

  /// Validate address line
  static String? validateAddressLine(
    String? value, {
    bool required = true,
    String fieldName = 'Address',
  }) {
    if (value == null || value.isEmpty) {
      return required ? '$fieldName is required' : null;
    }

    if (value.trim().length > 100) {
      return '$fieldName must be no more than 100 characters';
    }

    return null;
  }

  /// Validate city
  static String? validateCity(String? value) {
    if (value == null || value.isEmpty) {
      return 'City is required';
    }

    if (value.trim().length < 2) {
      return 'City must be at least 2 characters';
    }

    if (value.trim().length > 50) {
      return 'City must be no more than 50 characters';
    }

    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    final cityRegex = RegExp(r"^[a-zA-Z\s\-']+$");
    if (!cityRegex.hasMatch(value.trim())) {
      return 'City can only contain letters, spaces, hyphens, and apostrophes';
    }

    return null;
  }

  /// Validate postcode (international format)
  static String? validatePostcode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Postcode is required';
    }

    final trimmedValue = value.trim();

    // Basic validation - allow alphanumeric characters, spaces, and hyphens
    if (trimmedValue.length < 3) {
      return 'Postcode must be at least 3 characters';
    }

    if (trimmedValue.length > 12) {
      return 'Postcode must be no more than 12 characters';
    }

    // Allow letters, numbers, spaces, and hyphens
    final postcodeRegex = RegExp(r'^[A-Za-z0-9\s\-]+$');
    if (!postcodeRegex.hasMatch(trimmedValue)) {
      return 'Postcode can only contain letters, numbers, spaces, and hyphens';
    }

    return null;
  }

  /// Validate image file
  static String? validateImageFile(String? fileName, int? fileSizeBytes) {
    if (fileName == null || fileName.isEmpty) {
      return null; // Optional field
    }

    // Check file extension - only allow JPG and PNG for better compatibility
    final allowedExtensions = ['jpg', 'jpeg', 'png'];
    final extension = fileName.toLowerCase().split('.').last;

    if (!allowedExtensions.contains(extension)) {
      return 'Please select a JPG or PNG image file';
    }

    // Check file size (5MB limit)
    if (fileSizeBytes != null && fileSizeBytes > 5 * 1024 * 1024) {
      return 'Image file must be smaller than 5MB';
    }

    return null;
  }
}
