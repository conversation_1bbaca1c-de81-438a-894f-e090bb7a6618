import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as li;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/storage_type.dart';
import 'package:three_pay_group_litigation_platform/src/shared/presentation/widgets/storage_indicator_widget.dart';

/// Storage health status enumeration
enum StorageHealthStatus { healthy, warning, critical, offline }

/// Storage metrics model
class StorageMetrics {
  final int totalFiles;
  final int totalSizeBytes;
  final int usedQuotaBytes;
  final int totalQuotaBytes;
  final double averageUploadSpeed;
  final double averageDownloadSpeed;
  final int apiCallsToday;
  final int apiQuotaDaily;
  final DateTime lastSync;
  final StorageHealthStatus healthStatus;
  final List<String> healthIssues;

  const StorageMetrics({
    required this.totalFiles,
    required this.totalSizeBytes,
    required this.usedQuotaBytes,
    required this.totalQuotaBytes,
    required this.averageUploadSpeed,
    required this.averageDownloadSpeed,
    required this.apiCallsToday,
    required this.apiQuotaDaily,
    required this.lastSync,
    required this.healthStatus,
    required this.healthIssues,
  });

  double get quotaUsagePercentage =>
      totalQuotaBytes > 0 ? (usedQuotaBytes / totalQuotaBytes) * 100 : 0;

  double get apiUsagePercentage =>
      apiQuotaDaily > 0 ? (apiCallsToday / apiQuotaDaily) * 100 : 0;
}

/// Admin widget for monitoring Google Drive storage status and metrics
class StorageStatusWidget extends ConsumerStatefulWidget {
  final bool showDetailedMetrics;
  final bool allowRefresh;

  const StorageStatusWidget({
    super.key,
    this.showDetailedMetrics = true,
    this.allowRefresh = true,
  });

  @override
  ConsumerState<StorageStatusWidget> createState() =>
      _StorageStatusWidgetState();
}

class _StorageStatusWidgetState extends ConsumerState<StorageStatusWidget> {
  StorageMetrics? _metrics;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadStorageMetrics();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        borderRadius: theme.radius,
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme),
          const SizedBox(height: 16),
          if (_isLoading)
            _buildLoadingState(theme)
          else if (_errorMessage != null)
            _buildErrorState(theme)
          else if (_metrics != null)
            _buildMetricsContent(theme)
          else
            _buildEmptyState(theme),
        ],
      ),
    );
  }

  Widget _buildHeader(ShadThemeData theme) {
    return Row(
      children: [
        const StorageIndicatorWidget(
          storageType: StorageType.googleDrive,
          showLabel: false,
          iconSize: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Google Drive Storage',
                style: theme.textTheme.h4.copyWith(
                  color: theme.colorScheme.foreground,
                ),
              ),
              if (_metrics != null)
                Row(
                  children: [
                    _buildHealthIndicator(theme, _metrics!.healthStatus),
                    const SizedBox(width: 8),
                    Text(
                      _getHealthStatusText(_metrics!.healthStatus),
                      style: theme.textTheme.small.copyWith(
                        color: _getHealthStatusColor(
                          theme,
                          _metrics!.healthStatus,
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
        if (widget.allowRefresh)
          ShadTooltip(
            builder: (context) => const Text('Refresh metrics'),
            child: ShadIconButton.ghost(
              icon: Icon(
                _isLoading ? li.LucideIcons.loader2 : li.LucideIcons.refreshCw,
                size: 16,
              ),
              onPressed: _isLoading ? null : _loadStorageMetrics,
            ),
          ),
      ],
    );
  }

  Widget _buildLoadingState(ShadThemeData theme) {
    return const Center(
      child: Column(
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 8),
          Text('Loading storage metrics...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(ShadThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.destructive.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.destructive.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            li.LucideIcons.alertCircle,
            size: 20,
            color: theme.colorScheme.destructive,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Failed to load storage metrics',
                  style: theme.textTheme.large.copyWith(
                    color: theme.colorScheme.destructive,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  _errorMessage!,
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.destructive,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ShadThemeData theme) {
    return Center(
      child: Column(
        children: [
          Icon(
            li.LucideIcons.cloudOff,
            size: 32,
            color: theme.colorScheme.mutedForeground,
          ),
          const SizedBox(height: 8),
          Text(
            'No storage metrics available',
            style: theme.textTheme.large.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsContent(ShadThemeData theme) {
    final metrics = _metrics!;

    return Column(
      children: [
        _buildQuickStats(theme, metrics),
        if (widget.showDetailedMetrics) ...[
          const SizedBox(height: 16),
          _buildDetailedMetrics(theme, metrics),
        ],
        if (metrics.healthIssues.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildHealthIssues(theme, metrics),
        ],
      ],
    );
  }

  Widget _buildQuickStats(ShadThemeData theme, StorageMetrics metrics) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            theme,
            'Files',
            metrics.totalFiles.toString(),
            li.LucideIcons.files,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            theme,
            'Storage Used',
            _formatBytes(metrics.usedQuotaBytes),
            li.LucideIcons.hardDrive,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            theme,
            'API Calls',
            '${metrics.apiCallsToday}/${metrics.apiQuotaDaily}',
            li.LucideIcons.activity,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    ShadThemeData theme,
    String label,
    String value,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        children: [
          Icon(icon, size: 20, color: theme.colorScheme.primary),
          const SizedBox(height: 8),
          Text(
            value,
            style: theme.textTheme.large.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.foreground,
            ),
          ),
          Text(
            label,
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedMetrics(ShadThemeData theme, StorageMetrics metrics) {
    return Column(
      children: [
        _buildProgressMetric(
          theme,
          'Storage Quota',
          metrics.quotaUsagePercentage,
          '${_formatBytes(metrics.usedQuotaBytes)} / ${_formatBytes(metrics.totalQuotaBytes)}',
          li.LucideIcons.hardDrive,
        ),
        const SizedBox(height: 12),
        _buildProgressMetric(
          theme,
          'API Usage',
          metrics.apiUsagePercentage,
          '${metrics.apiCallsToday} / ${metrics.apiQuotaDaily} calls',
          li.LucideIcons.activity,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildSpeedMetric(
                theme,
                'Upload Speed',
                metrics.averageUploadSpeed,
                li.LucideIcons.upload,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSpeedMetric(
                theme,
                'Download Speed',
                metrics.averageDownloadSpeed,
                li.LucideIcons.download,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildLastSyncInfo(theme, metrics),
      ],
    );
  }

  Widget _buildProgressMetric(
    ShadThemeData theme,
    String label,
    double percentage,
    String details,
    IconData icon,
  ) {
    final color =
        percentage > 90
            ? theme.colorScheme.destructive
            : percentage > 75
            ? Colors.orange
            : theme.colorScheme.primary;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 8),
            Text(
              label,
              style: theme.textTheme.large.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.foreground,
              ),
            ),
            const Spacer(),
            Text(
              '${percentage.toStringAsFixed(1)}%',
              style: theme.textTheme.large.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: percentage / 100,
          backgroundColor: theme.colorScheme.muted,
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 8,
        ),
        const SizedBox(height: 4),
        Text(
          details,
          style: theme.textTheme.small.copyWith(
            color: theme.colorScheme.mutedForeground,
          ),
        ),
      ],
    );
  }

  Widget _buildSpeedMetric(
    ShadThemeData theme,
    String label,
    double speedBytesPerSecond,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.border),
      ),
      child: Column(
        children: [
          Icon(icon, size: 16, color: theme.colorScheme.primary),
          const SizedBox(height: 8),
          Text(
            '${_formatBytes(speedBytesPerSecond.round())}/s',
            style: theme.textTheme.large.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.foreground,
            ),
          ),
          Text(
            label,
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLastSyncInfo(ShadThemeData theme, StorageMetrics metrics) {
    final timeDiff = DateTime.now().difference(metrics.lastSync);
    final syncText =
        timeDiff.inMinutes < 1
            ? 'Just now'
            : timeDiff.inHours < 1
            ? '${timeDiff.inMinutes} minutes ago'
            : timeDiff.inDays < 1
            ? '${timeDiff.inHours} hours ago'
            : '${timeDiff.inDays} days ago';

    return Row(
      children: [
        Icon(
          li.LucideIcons.clock,
          size: 16,
          color: theme.colorScheme.mutedForeground,
        ),
        const SizedBox(width: 8),
        Text(
          'Last sync: $syncText',
          style: theme.textTheme.small.copyWith(
            color: theme.colorScheme.mutedForeground,
          ),
        ),
      ],
    );
  }

  Widget _buildHealthIssues(ShadThemeData theme, StorageMetrics metrics) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.destructive.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.destructive.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                li.LucideIcons.alertTriangle,
                size: 16,
                color: theme.colorScheme.destructive,
              ),
              const SizedBox(width: 8),
              Text(
                'Health Issues',
                style: theme.textTheme.large.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.destructive,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...metrics.healthIssues.map(
            (issue) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Icon(
                    li.LucideIcons.dot,
                    size: 12,
                    color: theme.colorScheme.destructive,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      issue,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.destructive,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthIndicator(
    ShadThemeData theme,
    StorageHealthStatus status,
  ) {
    IconData icon;
    Color color;

    switch (status) {
      case StorageHealthStatus.healthy:
        icon = li.LucideIcons.checkCircle2;
        color = Colors.green;
        break;
      case StorageHealthStatus.warning:
        icon = li.LucideIcons.alertTriangle;
        color = Colors.orange;
        break;
      case StorageHealthStatus.critical:
        icon = li.LucideIcons.alertCircle;
        color = theme.colorScheme.destructive;
        break;
      case StorageHealthStatus.offline:
        icon = li.LucideIcons.cloudOff;
        color = theme.colorScheme.mutedForeground;
        break;
    }

    return Icon(icon, size: 14, color: color);
  }

  String _getHealthStatusText(StorageHealthStatus status) {
    switch (status) {
      case StorageHealthStatus.healthy:
        return 'Healthy';
      case StorageHealthStatus.warning:
        return 'Warning';
      case StorageHealthStatus.critical:
        return 'Critical';
      case StorageHealthStatus.offline:
        return 'Offline';
    }
  }

  Color _getHealthStatusColor(ShadThemeData theme, StorageHealthStatus status) {
    switch (status) {
      case StorageHealthStatus.healthy:
        return Colors.green;
      case StorageHealthStatus.warning:
        return Colors.orange;
      case StorageHealthStatus.critical:
        return theme.colorScheme.destructive;
      case StorageHealthStatus.offline:
        return theme.colorScheme.mutedForeground;
    }
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  Future<void> _loadStorageMetrics() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Simulate loading metrics (replace with actual Google Drive API calls)
      await Future.delayed(const Duration(seconds: 2));

      // Mock data for demonstration
      final metrics = StorageMetrics(
        totalFiles: 1247,
        totalSizeBytes: 2 * 1024 * 1024 * 1024, // 2GB
        usedQuotaBytes: 8 * 1024 * 1024 * 1024, // 8GB
        totalQuotaBytes: 15 * 1024 * 1024 * 1024, // 15GB
        averageUploadSpeed: 1.5 * 1024 * 1024, // 1.5 MB/s
        averageDownloadSpeed: 2.2 * 1024 * 1024, // 2.2 MB/s
        apiCallsToday: 1850,
        apiQuotaDaily: 10000,
        lastSync: DateTime.now().subtract(const Duration(minutes: 5)),
        healthStatus: StorageHealthStatus.healthy,
        healthIssues: [],
      );

      if (mounted) {
        setState(() {
          _metrics = metrics;
          _isLoading = false;
        });
      }
    } catch (e) {
      LoggerService.error('Failed to load storage metrics', e);

      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }
}
