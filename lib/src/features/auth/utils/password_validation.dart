/// Password strength levels
enum PasswordStrength { weak, fair, good, strong }

/// Password strength result
class PasswordStrengthResult {
  final PasswordStrength strength;
  final double score; // 0.0 to 1.0
  final List<String> suggestions;
  final List<String> requirements;

  const PasswordStrengthResult({
    required this.strength,
    required this.score,
    required this.suggestions,
    required this.requirements,
  });
}

/// Password validation utilities for the 3Pay Global platform
class PasswordValidation {
  /// Minimum password length
  static const int minLength = 8;

  /// Maximum password length
  static const int maxLength = 128;

  /// Validate password and return error message if invalid
  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'Password is required';
    }

    if (password.length < minLength) {
      return 'Password must be at least $minLength characters long';
    }

    if (password.length > maxLength) {
      return 'Password must be no more than $maxLength characters long';
    }

    // Check for at least one letter
    if (!RegExp(r'[a-zA-Z]').hasMatch(password)) {
      return 'Password must contain at least one letter';
    }

    // Check for at least one number
    if (!RegExp(r'[0-9]').hasMatch(password)) {
      return 'Password must contain at least one number';
    }

    return null; // Password is valid
  }

  /// Validate password confirmation
  static String? validatePasswordConfirm(
    String? password,
    String? passwordConfirm,
  ) {
    if (passwordConfirm == null || passwordConfirm.isEmpty) {
      return 'Please confirm your password';
    }

    if (password != passwordConfirm) {
      return 'Passwords do not match';
    }

    return null;
  }

  /// Check password strength and return detailed analysis
  static PasswordStrengthResult checkPasswordStrength(String password) {
    if (password.isEmpty) {
      return const PasswordStrengthResult(
        strength: PasswordStrength.weak,
        score: 0.0,
        suggestions: ['Enter a password'],
        requirements: [],
      );
    }

    double score = 0.0;
    final suggestions = <String>[];
    final requirements = <String>[];

    // Length check
    if (password.length >= minLength) {
      score += 0.2;
      requirements.add('✓ At least $minLength characters');
    } else {
      suggestions.add('Use at least $minLength characters');
      requirements.add('✗ At least $minLength characters');
    }

    // Lowercase letter check
    if (RegExp(r'[a-z]').hasMatch(password)) {
      score += 0.15;
      requirements.add('✓ Contains lowercase letter');
    } else {
      suggestions.add('Add lowercase letters');
      requirements.add('✗ Contains lowercase letter');
    }

    // Uppercase letter check
    if (RegExp(r'[A-Z]').hasMatch(password)) {
      score += 0.15;
      requirements.add('✓ Contains uppercase letter');
    } else {
      suggestions.add('Add uppercase letters');
      requirements.add('✗ Contains uppercase letter');
    }

    // Number check
    if (RegExp(r'[0-9]').hasMatch(password)) {
      score += 0.2;
      requirements.add('✓ Contains number');
    } else {
      suggestions.add('Add numbers');
      requirements.add('✗ Contains number');
    }

    // Special character check
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      score += 0.2;
      requirements.add('✓ Contains special character');
    } else {
      suggestions.add('Add special characters (!@#\$%^&*)');
      requirements.add('✗ Contains special character');
    }

    // Length bonus
    if (password.length >= 12) {
      score += 0.1;
      requirements.add('✓ 12+ characters (bonus)');
    } else if (password.length >= 10) {
      score += 0.05;
    }

    // Determine strength
    PasswordStrength strength;
    if (score < 0.4) {
      strength = PasswordStrength.weak;
    } else if (score < 0.6) {
      strength = PasswordStrength.fair;
    } else if (score < 0.8) {
      strength = PasswordStrength.good;
    } else {
      strength = PasswordStrength.strong;
    }

    return PasswordStrengthResult(
      strength: strength,
      score: score,
      suggestions: suggestions,
      requirements: requirements,
    );
  }

  /// Get password strength color
  static String getStrengthColor(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.weak:
        return '#ef4444'; // red-500
      case PasswordStrength.fair:
        return '#f97316'; // orange-500
      case PasswordStrength.good:
        return '#eab308'; // yellow-500
      case PasswordStrength.strong:
        return '#22c55e'; // green-500
    }
  }

  /// Get password strength text
  static String getStrengthText(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.weak:
        return 'Weak';
      case PasswordStrength.fair:
        return 'Fair';
      case PasswordStrength.good:
        return 'Good';
      case PasswordStrength.strong:
        return 'Strong';
    }
  }

  /// Check if password meets minimum requirements
  static bool meetsMinimumRequirements(String password) {
    return validatePassword(password) == null;
  }

  /// Get password requirements list
  static List<String> getPasswordRequirements() {
    return [
      'At least $minLength characters long',
      'Contains at least one letter',
      'Contains at least one number',
      'Recommended: Mix of uppercase and lowercase letters',
      'Recommended: Special characters (!@#\$%^&*)',
    ];
  }
}
