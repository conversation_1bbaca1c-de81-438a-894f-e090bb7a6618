import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/notification_permission_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/service_locator.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/change_password_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/sign_in_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/account_deactivated_page.dart';
import 'package:three_pay_group_litigation_platform/src/core/app_widget.dart';

/// Widget for account settings including password management and opt-out
class AccountSettingsWidget extends StatefulWidget {
  const AccountSettingsWidget({super.key});

  @override
  State<AccountSettingsWidget> createState() => _AccountSettingsWidgetState();
}

class _AccountSettingsWidgetState extends State<AccountSettingsWidget> {
  final PocketBaseService _pbService = PocketBaseService();
  bool _isOptingOut = false;
  NotificationPermissionStatus _notificationStatus =
      NotificationPermissionStatus.unknown;
  String? _fcmToken;

  @override
  void initState() {
    super.initState();
    _checkNotificationStatus();
  }

  Future<void> _checkNotificationStatus() async {
    try {
      final status =
          await NotificationPermissionService.checkPermissionStatus();
      final token = FirebaseApiService.getCurrentToken();

      if (mounted) {
        setState(() {
          _notificationStatus = status;
          _fcmToken = token;
        });
      }
    } catch (e) {
      LoggerService.error('Error checking notification status', e);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final currentUser = _pbService.currentUser;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Security Settings
        ShadCard(
          title: Text('Security Settings', style: theme.textTheme.h4),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Column(
              children: [
                ListTile(
                  leading: Icon(
                    LucideIcons.lock,
                    color: theme.colorScheme.primary,
                  ),
                  title: Text('Change Password', style: theme.textTheme.p),
                  subtitle: Text(
                    'Update your account password',
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                  trailing: Icon(
                    LucideIcons.chevronRight,
                    size: 16,
                    color: theme.colorScheme.mutedForeground,
                  ),
                  onTap: () {
                    Navigator.of(
                      context,
                    ).pushNamed(ChangePasswordPage.routeName);
                  },
                ),
                //   const Divider(),
                //   ListTile(
                //     leading: Icon(
                //       LucideIcons.shield,
                //       color: theme.colorScheme.primary,
                //     ),
                //     title: Text(
                //       'Two-Factor Authentication',
                //       style: theme.textTheme.p,
                //     ),
                //     subtitle: Text(
                //       'Add an extra layer of security',
                //       style: theme.textTheme.small.copyWith(
                //         color: theme.colorScheme.mutedForeground,
                //       ),
                //     ),
                //     trailing: Icon(
                //       LucideIcons.chevronRight,
                //       size: 16,
                //       color: theme.colorScheme.mutedForeground,
                //     ),
                //     onTap: () {
                //       ShadToaster.of(context).show(
                //         const ShadToast(
                //           description: Text(
                //             'Two-factor authentication coming soon.',
                //           ),
                //         ),
                //       );
                //     },
                //   ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Notification Status
        ShadCard(
          title: Text('Notification Status', style: theme.textTheme.h4),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _notificationStatus ==
                                  NotificationPermissionStatus.granted ||
                              _notificationStatus ==
                                  NotificationPermissionStatus.provisional
                          ? LucideIcons.check
                          : LucideIcons.x,
                      color:
                          _notificationStatus ==
                                      NotificationPermissionStatus.granted ||
                                  _notificationStatus ==
                                      NotificationPermissionStatus.provisional
                              ? Colors.green
                              : Colors.orange,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        NotificationPermissionService.getPermissionStatusMessage(
                          _notificationStatus,
                        ),
                        style: theme.textTheme.p,
                      ),
                    ),
                  ],
                ),
                if (_fcmToken != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    'FCM Token: Active',
                    style: theme.textTheme.small.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Account Information
        ShadCard(
          title: Text('Account Information', style: theme.textTheme.h4),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow(
                  'Email',
                  currentUser?.data['email'] ?? 'Not available',
                  theme,
                ),
                const SizedBox(height: 12),
                _buildInfoRow(
                  'User Type',
                  _formatUserType(currentUser?.data['user_type']),
                  theme,
                ),
                const SizedBox(height: 12),
                _buildInfoRow(
                  'Account Status',
                  currentUser?.data['verified'] == true
                      ? 'Verified'
                      : 'Unverified',
                  theme,
                ),
                const SizedBox(height: 12),
                _buildInfoRow(
                  'Member Since',
                  _formatDate(currentUser?.data['created']),
                  theme,
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Danger Zone
        ShadCard(
          title: Text(
            'Danger Zone',
            style: theme.textTheme.h4.copyWith(
              color: theme.colorScheme.destructive,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Account Deactivation',
                  style: theme.textTheme.p.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Deactivating your account will prevent you from accessing the platform. This action requires password verification for security.',
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
                const SizedBox(height: 16),
                ShadButton.destructive(
                  onPressed: _isOptingOut ? null : _showOptOutDialog,
                  child:
                      _isOptingOut
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator.adaptive(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : const Text('Deactivate Account'),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 32),

        // Sign Out Button
        ShadButton.outline(
          onPressed: _signOut,
          width: double.infinity,
          child: const Text('Sign Out'),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, ShadThemeData theme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: theme.textTheme.small.copyWith(
              color: theme.colorScheme.mutedForeground,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(child: Text(value, style: theme.textTheme.small)),
      ],
    );
  }

  String _formatUserType(String? userType) {
    if (userType == null) return 'Unknown';

    switch (userType) {
      case 'claimant':
        return 'Claimant';
      case 'solicitor':
        return 'Solicitor';
      case 'co_funder':
        return 'Co-Funder';
      case 'admin':
        return 'Administrator';
      default:
        return userType.toUpperCase();
    }
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'Unknown';

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'Unknown';
    }
  }

  void _showOptOutDialog() {
    showDialog(
      context: context,
      builder: (context) => _OptOutDialog(onOptOut: _handleOptOut),
    );
  }

  Future<void> _handleOptOut(String password) async {
    setState(() {
      _isOptingOut = true;
    });

    try {
      // Verify password by attempting to authenticate
      await _pbService.pb
          .collection('users')
          .authWithPassword(_pbService.currentUser!.data['email'], password);

      // Opt out the account
      await _pbService.optOutAccount();

      if (!mounted) return;

      // Show success message
      ShadToaster.of(context).show(
        const ShadToast(
          title: Text('Account Deactivated'),
          description: Text('Your account has been deactivated successfully.'),
        ),
      );

      // Stop audio service and clear mini player state before navigation
      try {
        await ServiceLocator.backgroundAudioService.stop();
        LoggerService.info(
          'Audio service stopped before account deactivation navigation',
        );
      } catch (e) {
        LoggerService.warning(
          'Failed to stop audio service during account deactivation: $e',
        );
        // Continue with deactivation even if audio service fails to stop
      }

      // Navigate to the deactivated page first
      navigatorKey.currentState?.pushNamedAndRemoveUntil(
        AccountDeactivatedPage.routeName,
        (route) => false,
      );

      // Then sign out. The AccountDeactivatedPage should handle final cleanup.
      await _pbService.signOut();
    } catch (e) {
      LoggerService.error('Account opt-out error', e);

      String errorMessage = 'Failed to deactivate account. Please try again.';
      if (e.toString().contains('password') ||
          e.toString().contains('authenticate')) {
        errorMessage = 'Incorrect password. Please try again.';
      }

      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Deactivation Failed'),
            description: Text(errorMessage),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isOptingOut = false;
        });
      }
    }
  }

  Future<void> _signOut() async {
    // Stop audio service and clear mini player state before navigation
    try {
      await ServiceLocator.backgroundAudioService.stop();
      LoggerService.info('Audio service stopped before sign out navigation');
    } catch (e) {
      LoggerService.warning('Failed to stop audio service during sign out: $e');
      // Continue with sign out even if audio service fails to stop
    }

    await _pbService.signOut();
    if (mounted) {
      Navigator.of(
        context,
      ).pushNamedAndRemoveUntil(SignInPage.routeName, (route) => false);
    }
  }
}

/// Dialog for account opt-out with password verification
class _OptOutDialog extends StatefulWidget {
  final Function(String password) onOptOut;

  const _OptOutDialog({required this.onOptOut});

  @override
  State<_OptOutDialog> createState() => _OptOutDialogState();
}

class _OptOutDialogState extends State<_OptOutDialog> {
  final _formKey = GlobalKey<ShadFormState>();
  final _passwordController = TextEditingController();
  final PocketBaseService _pbService = PocketBaseService();

  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _passwordVerified = false;
  bool _verifyingPassword = false;
  String? _errorMessage;

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _verifyPassword() async {
    if (!_formKey.currentState!.saveAndValidate()) {
      return;
    }

    setState(() {
      _verifyingPassword = true;
      _errorMessage = null;
    });

    try {
      // Verify password by attempting to authenticate
      await _pbService.pb
          .collection('users')
          .authWithPassword(
            _pbService.currentUser!.data['email'],
            _passwordController.text,
          );

      setState(() {
        _passwordVerified = true;
      });

      if (mounted) {
        ShadToaster.of(context).show(
          const ShadToast(
            title: Text('Password Verified'),
            description: Text(
              'Password verified successfully. You can now deactivate your account.',
            ),
          ),
        );
      }
    } catch (e) {
      LoggerService.error('Password verification error', e);
      setState(() {
        _errorMessage = 'Incorrect password. Please try again.';
      });
    } finally {
      setState(() {
        _verifyingPassword = false;
      });
    }
  }

  Future<void> _handleOptOut() async {
    if (!_passwordVerified) {
      setState(() {
        _errorMessage = 'Please verify your password first.';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await widget.onOptOut(_passwordController.text);
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return ShadDialog(
      title: const Text('Deactivate Account'),
      description: const Text(
        'This action will deactivate your account. You will lose access to the platform. Please verify your password to proceed.',
      ),
      actions: [
        ShadButton.outline(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ShadButton.destructive(
          onPressed: (_isLoading || !_passwordVerified) ? null : _handleOptOut,
          child:
              _isLoading
                  ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator.adaptive(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                  : const Text('Deactivate Account'),
        ),
      ],
      child: ShadForm(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Warning
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.destructive.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.destructive.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.triangle,
                    size: 16,
                    color: theme.colorScheme.destructive,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This action cannot be undone. Your account will be permanently deactivated.',
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.destructive,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Password Field
            ShadInputFormField(
              id: 'password',
              controller: _passwordController,
              label: const Text('Current Password'),
              placeholder: const Text('Enter your current password'),
              obscureText: _obscurePassword,
              trailing: ShadButton.ghost(
                child: Icon(
                  _obscurePassword ? LucideIcons.eyeOff : LucideIcons.eye,
                  size: 16,
                ),
                onPressed:
                    () => setState(() => _obscurePassword = !_obscurePassword),
              ),
              validator: (value) {
                if (value.isEmpty) {
                  return 'Password is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Password Verification Section
            if (!_passwordVerified) ...[
              ShadButton.outline(
                onPressed: _verifyingPassword ? null : _verifyPassword,
                width: double.infinity,
                child:
                    _verifyingPassword
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator.adaptive(
                            strokeWidth: 2,
                          ),
                        )
                        : const Text('Verify Password'),
              ),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.green.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      LucideIcons.circleCheck,
                      size: 16,
                      color: Colors.green.shade700,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Password verified successfully. You can now deactivate your account.',
                        style: theme.textTheme.small.copyWith(
                          color: Colors.green.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Error Message
            if (_errorMessage != null) ...[
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.destructive,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
