import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../utils/password_validation.dart';

/// Widget that displays password strength indicator and requirements
class PasswordStrengthIndicator extends StatelessWidget {
  final String password;
  final bool showRequirements;
  final bool compact;

  const PasswordStrengthIndicator({
    super.key,
    required this.password,
    this.showRequirements = true,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final strengthResult = PasswordValidation.checkPasswordStrength(password);

    if (password.isEmpty && !showRequirements) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (password.isNotEmpty) ...[
          // Strength indicator bar
          _buildStrengthBar(strengthResult, theme),
          const SizedBox(height: 8),

          // Strength text
          if (!compact) ...[
            Row(
              children: [
                Text('Password strength: ', style: theme.textTheme.small),
                Text(
                  PasswordValidation.getStrengthText(strengthResult.strength),
                  style: theme.textTheme.small.copyWith(
                    color: _getStrengthColor(strengthResult.strength, theme),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],
        ],

        // Requirements list
        if (showRequirements) ...[
          if (password.isNotEmpty && !compact) ...[
            Text(
              'Requirements:',
              style: theme.textTheme.small.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
          ],

          ...strengthResult.requirements.map((requirement) {
            final isCompleted = requirement.startsWith('✓');
            return Padding(
              padding: const EdgeInsets.only(bottom: 2),
              child: Row(
                children: [
                  Icon(
                    isCompleted ? LucideIcons.check : LucideIcons.x,
                    size: 14,
                    color:
                        isCompleted
                            ? theme.colorScheme.primary
                            : theme.colorScheme.mutedForeground,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      requirement.substring(2), // Remove ✓ or ✗
                      style: theme.textTheme.small.copyWith(
                        color:
                            isCompleted
                                ? theme.colorScheme.foreground
                                : theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],

        // Suggestions
        if (password.isNotEmpty &&
            strengthResult.suggestions.isNotEmpty &&
            !compact) ...[
          const SizedBox(height: 8),
          Text(
            'Suggestions:',
            style: theme.textTheme.small.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 4),
          ...strengthResult.suggestions.map((suggestion) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 2),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.lightbulb,
                    size: 14,
                    color: theme.colorScheme.mutedForeground,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      suggestion,
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ],
    );
  }

  Widget _buildStrengthBar(
    PasswordStrengthResult strengthResult,
    ShadThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 4,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            color: theme.colorScheme.muted,
          ),
          child: Row(
            children: [
              // Strength indicator segments
              for (int i = 0; i < 4; i++)
                Expanded(
                  child: Container(
                    margin: EdgeInsets.only(right: i < 3 ? 2 : 0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2),
                      color: _getSegmentColor(i, strengthResult, theme),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getSegmentColor(
    int index,
    PasswordStrengthResult strengthResult,
    ShadThemeData theme,
  ) {
    final strengthIndex = strengthResult.strength.index;

    if (index <= strengthIndex) {
      return _getStrengthColor(strengthResult.strength, theme);
    }

    return theme.colorScheme.muted;
  }

  Color _getStrengthColor(PasswordStrength strength, ShadThemeData theme) {
    switch (strength) {
      case PasswordStrength.weak:
        return theme.colorScheme.destructive;
      case PasswordStrength.fair:
        return Colors.orange;
      case PasswordStrength.good:
        return Colors.yellow.shade600;
      case PasswordStrength.strong:
        return theme.colorScheme.primary;
    }
  }
}

/// Compact version of password strength indicator for inline use
class CompactPasswordStrengthIndicator extends StatelessWidget {
  final String password;

  const CompactPasswordStrengthIndicator({super.key, required this.password});

  @override
  Widget build(BuildContext context) {
    return PasswordStrengthIndicator(
      password: password,
      showRequirements: false,
      compact: true,
    );
  }
}
