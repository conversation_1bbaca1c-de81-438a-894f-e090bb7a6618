import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/forgot_password_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/role_selection_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/account_deactivated_page.dart';
import 'package:three_pay_group_litigation_platform/src/core/app_widget.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_dashboard_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/claimant_dashboard_page.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart'; // Import PocketBaseService
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/service_locator.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/solicitor_dashboard_page.dart'; // Import SolicitorDashboardPage
import 'package:pocketbase/pocketbase.dart'; // Import for ClientException
// TODO: Add admin dashboard import if/when created

class SignInPage extends StatefulWidget {
  const SignInPage({super.key});

  static const String routeName = '/signin';

  @override
  State<SignInPage> createState() => _SignInPageState();
}

class _SignInPageState extends State<SignInPage> {
  final _formKey = GlobalKey<ShadFormState>();
  bool _obscurePassword = true;
  bool _isLoading = false;
  String? _errorMessage;

  final PocketBaseService _pbService = PocketBaseService();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _performSignIn() async {
    if (_formKey.currentState!.saveAndValidate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
      try {
        final email = _emailController.text;
        final password = _passwordController.text;

        final authData = await _pbService.signIn(email, password);

        // Check if user account is deactivated (opt_out = true)
        final isOptedOut = authData.record.data['opt_out'] == true;
        if (isOptedOut) {
          // Stop audio service and clear mini player state before navigation
          try {
            await ServiceLocator.backgroundAudioService.stop();
            LoggerService.info(
              'Audio service stopped before deactivated account navigation',
            );
          } catch (e) {
            LoggerService.warning(
              'Failed to stop audio service during deactivated account navigation: $e',
            );
            // Continue with navigation even if audio service fails to stop
          }

          // Sign out the user immediately and navigate to account deactivated page
          await _pbService.signOut();

          // Use the global navigator key to avoid context issues after sign out
          navigatorKey.currentState?.pushNamedAndRemoveUntil(
            AccountDeactivatedPage.routeName,
            (route) => false,
          );
          return;
        }

        // Navigate based on user type
        final userType = authData.record.data['user_type'];
        // ignore: use_build_context_synchronously
        if (!mounted) return;

        switch (userType) {
          case 'solicitor':
            Navigator.of(context).pushNamedAndRemoveUntil(
              SolicitorDashboardPage.routeName,
              (route) => route.settings.name == '/',
            );
            break;
          case 'co_funder':
            Navigator.of(context).pushNamedAndRemoveUntil(
              CoFunderDashboardPage.routeName,
              (route) => route.settings.name == '/',
            );
            break;
          case 'claimant':
            Navigator.of(context).pushNamedAndRemoveUntil(
              ClaimantDashboardPage.routeName,
              (route) => route.settings.name == '/',
            );
            break;
          case 'admin':
            // TODO: Navigate to Admin Dashboard when created
            // For now, can navigate to a generic page or show a message
            Navigator.of(context).pushNamedAndRemoveUntil(
              SolicitorDashboardPage.routeName,
              (route) => route.settings.name == '/',
            ); // Placeholder
            ShadToaster.of(context).show(
              const ShadToast(
                title: Text('Admin Logged In'),
                description: Text('Admin dashboard not yet implemented.'),
              ),
            );
            break;
          default:
            // Fallback or error if user_type is unknown or not set
            ShadToaster.of(context).show(
              ShadToast.destructive(
                title: const Text('Sign In Failed'),
                description: Text('Unknown user role: $userType'),
              ),
            );
        }
      } catch (e) {
        String displayErrorMessage;
        if (e is ClientException &&
            e.statusCode == 400 &&
            e.response['message'] == 'Failed to authenticate.') {
          displayErrorMessage = 'Invalid email or password. Please try again.';
        } else {
          displayErrorMessage = e.toString();
        }
        setState(() {
          _errorMessage = displayErrorMessage;
        });
        // ignore: use_build_context_synchronously
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: const Text('Sign In Failed'),
            description: Text(displayErrorMessage),
          ),
        );
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    } else {
      LoggerService.warning('Sign-in validation failed');
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Validation Error'),
          description: const Text('Please check your inputs.'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        // title: const Text(''),
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: ShadForm(
            key: _formKey,
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 400),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      bottom: 24.0,
                    ), // Add some space below the logo
                    child: Image.asset(
                      'assets/images/3paylogo.png',
                      height: 100, // Adjust height as needed
                      // width: 100, // Adjust width as needed
                    ),
                  ),

                  const SizedBox(height: 8),
                  Text(
                    'Sign in to access your dashboard.',
                    style: theme.textTheme.p,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  ShadInputFormField(
                    id: 'email',
                    controller: _emailController,
                    label: const Text('Email'),
                    placeholder: const Text('Enter your email'),
                    keyboardType: TextInputType.emailAddress,
                    validator: (v) {
                      if (v.isEmpty) {
                        return 'Email is required.';
                      }
                      if (!v.contains('@')) {
                        return 'Please enter a valid email.';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  ShadInputFormField(
                    id: 'password',
                    controller: _passwordController,
                    label: const Text('Password'),
                    placeholder: const Text('Enter your password'),
                    obscureText: _obscurePassword,
                    trailing: ShadIconButton(
                      width: 24,
                      height: 24,
                      padding: EdgeInsets.zero,
                      decoration: const ShadDecoration(
                        secondaryBorder: ShadBorder.none,
                        secondaryFocusedBorder: ShadBorder.none,
                      ),
                      icon: Icon(
                        _obscurePassword ? LucideIcons.eyeOff : LucideIcons.eye,
                        size: 16,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    validator: (v) {
                      if (v.isEmpty) {
                        return 'Password is required.';
                      }
                      if (v.length < 6) {
                        return 'Password must be at least 6 characters.';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      child: Text(
                        'Forgot Password?',
                        style: TextStyle(color: theme.colorScheme.primary),
                      ),
                      onPressed: () {
                        Navigator.of(
                          context,
                        ).pushNamed(ForgotPasswordPage.routeName);
                      },
                    ),
                  ),
                  const SizedBox(height: 24),
                  ShadButton(
                    width: double.infinity, // Make button full width
                    onPressed: _isLoading ? null : _performSignIn,
                    child:
                        _isLoading
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator.adaptive(
                                strokeWidth: 2,
                              ),
                            )
                            : const Text('Sign In'),
                  ),
                  if (_errorMessage != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage!,
                      style: TextStyle(color: theme.colorScheme.destructive),
                      textAlign: TextAlign.center,
                    ),
                  ],
                  const SizedBox(height: 16),
                  // ShadButton.outline(
                  //   child: const Text('DEV: Go to Co-Funder Dashboard'),
                  //   onPressed: () {
                  //     Navigator.of(context).pushNamed(CoFunderDashboardPage.routeName);
                  //   },
                  // ),
                  // const SizedBox(height: 16), // Added spacing
                  // ShadButton.outline( // Temporary button for Claimant Dashboard
                  //   child: const Text('DEV: Go to Claimant Dashboard'),
                  //   onPressed: () {
                  //     Navigator.of(context).pushNamed(ClaimantDashboardPage.routeName);
                  //   },
                  // ),
                  const SizedBox(height: 32),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text("Don't have an account? "),
                      TextButton(
                        child: Text(
                          'Sign Up',
                          style: TextStyle(color: theme.colorScheme.primary),
                        ),
                        onPressed: () {
                          Navigator.of(context).pushNamedAndRemoveUntil(
                            RoleSelectionPage.routeName,
                            (route) => route.settings.name == '/',
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
