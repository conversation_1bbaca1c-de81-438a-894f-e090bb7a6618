import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  static const String routeName = '/forgot-password';

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final _formKey = GlobalKey<ShadFormState>();
  final _emailController = TextEditingController();
  final PocketBaseService _pbService = PocketBaseService();

  bool _isLoading = false;
  String? _message;
  bool _isError = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _handlePasswordResetRequest() async {
    if (!_formKey.currentState!.saveAndValidate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _message = null;
      _isError = false;
    });

    try {
      await _pbService.client
          .collection('users')
          .requestPasswordReset(_emailController.text.trim());
      setState(() {
        _message =
            'If an account with that email exists, a password reset link has been sent.';
        _isError = false;
        _emailController.clear(); // Clear field on success
      });
      // ignore: use_build_context_synchronously
      ShadToaster.of(context).show(
        ShadToast(
          title: const Text('Request Sent'),
          description: Text(_message!),
        ),
      );
    } catch (e) {
      setState(() {
        _message = 'Failed to send password reset email. Please try again.';
        _isError = true;
      });
      LoggerService.error('Password Reset Error', e);
      // ignore: use_build_context_synchronously
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Error'),
          description: Text(_message!),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Forgot Password',
          style: theme.textTheme.h4.copyWith(
            color: theme.colorScheme.primaryForeground,
          ),
        ),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.primaryForeground,
        iconTheme: IconThemeData(color: theme.colorScheme.primaryForeground),
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 400),
            child: ShadForm(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'Reset Your Password',
                    style: theme.textTheme.h2,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Enter your email address below and we\'ll send you a link to reset your password.',
                    style: theme.textTheme.p.copyWith(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  ShadInputFormField(
                    id: 'email',
                    controller: _emailController,
                    label: Text(
                      'Email Address',
                      style: theme.textTheme.p.copyWith(fontSize: 16),
                    ),
                    placeholder: Text(
                      'Enter your email',
                      style: theme.textTheme.p.copyWith(fontSize: 16),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: (v) {
                      if (v.isEmpty) {
                        return 'Email is required.';
                      }
                      if (!v.contains('@') || !v.contains('.')) {
                        return 'Please enter a valid email address.';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  ShadButton(
                    width: double.infinity,
                    onPressed: _isLoading ? null : _handlePasswordResetRequest,
                    child:
                        _isLoading
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator.adaptive(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ), // Ensure spinner is visible on button
                              ),
                            )
                            : const Text('Send Reset Instructions'),
                  ),
                  if (_message != null &&
                      !_isLoading &&
                      !_isError) // Show success message only if not loading and not an error that toaster handles
                    Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Text(
                        _message!,
                        style: theme.textTheme.p.copyWith(
                          color:
                              _isError
                                  ? theme.colorScheme.destructive
                                  : theme.colorScheme.primary,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  const SizedBox(height: 24),
                  ShadButton.link(
                    child: const Text(
                      'Back to Sign In',
                    ), // Changed text: to child:
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
