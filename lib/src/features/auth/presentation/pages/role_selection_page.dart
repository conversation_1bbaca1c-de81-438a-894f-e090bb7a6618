import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/solicitor_registration_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/cofunder_registration_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/claimant_registration_page.dart'; // Import ClaimantRegistrationPage
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/sign_in_page.dart'; // Import SignInPage


class RoleSelectionPage extends StatelessWidget {
  const RoleSelectionPage({super.key});

  static const String routeName = '/role-selection';

  Widget _buildRoleCard({
    required BuildContext context,
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onPressed,
    required Color iconColor,
  }) {
    final shadTheme = ShadTheme.of(context);
    return ShadCard(
      title: Row(
        children: [
          Icon(icon, size: 24, color: iconColor),
          const SizedBox(width: 12),
          Text(title, style: shadTheme.textTheme.h4),
        ],
      ),
      description: Padding(
        padding: const EdgeInsets.only(top: 8.0),
        child: Text(description, style: shadTheme.textTheme.p.copyWith(color: shadTheme.colorScheme.mutedForeground)),
      ),
      footer: ShadButton(
        width: double.infinity,
        child: const Text('Select this Role'),
        onPressed: onPressed,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final shadTheme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Your Role'),
        backgroundColor: shadTheme.colorScheme.primary,
        foregroundColor: shadTheme.colorScheme.primaryForeground,
        elevation: 0,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                'Choose how you want to use the platform:',
                style: shadTheme.textTheme.h3,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32.0),
              _buildRoleCard(
                context: context,
                title: 'Solicitor',
                description: 'Represent claimants, manage cases, and apply for litigation funding for claims.',
                icon: LucideIcons.briefcase,
                iconColor: shadTheme.colorScheme.primary,
                onPressed: () {
                  Navigator.pushNamed(context, SolicitorRegistrationPage.routeName);
                },
              ),
              const SizedBox(height: 24.0),
              _buildRoleCard(
                context: context,
                title: 'Co-Funder',
                description: 'Invest in vetted legal cases, diversify your portfolio, and support access to justice.',
                icon: LucideIcons.dollarSign,
                iconColor: Colors.green.shade600, // Example color
                onPressed: () {
                  Navigator.pushNamed(context, CoFunderRegistrationPage.routeName);
                },
              ),
              const SizedBox(height: 24.0),
              _buildRoleCard(
                context: context,
                title: 'Claimant',
                description: 'Seeking funding for your legal case? Register here to connect with solicitors (coming soon).',
                icon: LucideIcons.userCheck,
                iconColor: Colors.orange.shade700, // Example color
                onPressed: () {
                  // TODO: Implement Claimant Registration or a placeholder
                   Navigator.pushNamed(context, ClaimantRegistrationPage.routeName);
                },
              ),
              const SizedBox(height: 48.0),
              TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, SignInPage.routeName);
                },
                child: Text(
                  'Already have an account? Sign In',
                  style: shadTheme.textTheme.p.copyWith(color: shadTheme.colorScheme.primary),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}