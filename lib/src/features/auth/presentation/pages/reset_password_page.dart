import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/utils/password_validation.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/widgets/password_strength_indicator.dart';
import 'sign_in_page.dart';

/// Page for resetting password with token from email
class ResetPasswordPage extends StatefulWidget {
  final String token;

  const ResetPasswordPage({super.key, required this.token});

  static const String routeName = '/reset-password';

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage> {
  final _formKey = GlobalKey<ShadFormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final PocketBaseService _pbService = PocketBaseService();

  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  String? _errorMessage;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handlePasswordReset() async {
    if (!_formKey.currentState!.saveAndValidate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _pbService.confirmPasswordReset(
        token: widget.token,
        password: _passwordController.text,
        passwordConfirm: _confirmPasswordController.text,
      );

      if (!mounted) return;

      // Show success message
      ShadToaster.of(context).show(
        const ShadToast(
          title: Text('Password Reset Successful'),
          description: Text(
            'Your password has been updated. Please sign in with your new password.',
          ),
        ),
      );

      // Navigate to sign in page
      Navigator.of(
        context,
      ).pushNamedAndRemoveUntil(SignInPage.routeName, (route) => false);
    } catch (e) {
      LoggerService.error('Password reset error', e);

      String errorMessage = 'Failed to reset password. Please try again.';

      // Handle specific error cases
      if (e.toString().contains('token')) {
        errorMessage =
            'Invalid or expired reset token. Please request a new password reset.';
      } else if (e.toString().contains('password')) {
        errorMessage =
            'Password requirements not met. Please check your password.';
      }

      setState(() {
        _errorMessage = errorMessage;
      });

      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Reset Failed'),
          description: Text(errorMessage),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Reset Password'),
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 400),
            child: ShadForm(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'Create New Password',
                    style: theme.textTheme.h2,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Please enter your new password below.',
                    style: theme.textTheme.p.copyWith(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),

                  // New Password Field
                  ShadInputFormField(
                    id: 'password',
                    controller: _passwordController,
                    label: Text(
                      'New Password',
                      style: theme.textTheme.p.copyWith(fontSize: 16),
                    ),
                    placeholder: Text(
                      'Enter your new password',
                      style: theme.textTheme.p.copyWith(fontSize: 16),
                    ),
                    obscureText: _obscurePassword,
                    trailing: ShadButton.ghost(
                      child: Icon(
                        _obscurePassword ? LucideIcons.eyeOff : LucideIcons.eye,
                        size: 16,
                      ),
                      onPressed:
                          () => setState(
                            () => _obscurePassword = !_obscurePassword,
                          ),
                    ),
                    validator:
                        (value) => PasswordValidation.validatePassword(value),
                    onChanged:
                        (value) => setState(
                          () {},
                        ), // Trigger rebuild for strength indicator
                  ),
                  const SizedBox(height: 16),

                  // Password Strength Indicator
                  PasswordStrengthIndicator(
                    password: _passwordController.text,
                    showRequirements: true,
                  ),
                  const SizedBox(height: 24),

                  // Confirm Password Field
                  ShadInputFormField(
                    id: 'confirmPassword',
                    controller: _confirmPasswordController,
                    label: Text(
                      'Confirm New Password',
                      style: theme.textTheme.p.copyWith(fontSize: 16),
                    ),
                    placeholder: Text(
                      'Confirm your new password',
                      style: theme.textTheme.p.copyWith(fontSize: 16),
                    ),
                    obscureText: _obscureConfirmPassword,
                    trailing: ShadButton.ghost(
                      child: Icon(
                        _obscureConfirmPassword
                            ? LucideIcons.eyeOff
                            : LucideIcons.eye,
                        size: 16,
                      ),
                      onPressed:
                          () => setState(
                            () =>
                                _obscureConfirmPassword =
                                    !_obscureConfirmPassword,
                          ),
                    ),
                    validator:
                        (value) => PasswordValidation.validatePasswordConfirm(
                          _passwordController.text,
                          value,
                        ),
                  ),
                  const SizedBox(height: 32),

                  // Reset Button
                  ShadButton(
                    width: double.infinity,
                    onPressed: _isLoading ? null : _handlePasswordReset,
                    child:
                        _isLoading
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator.adaptive(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : const Text('Reset Password'),
                  ),

                  // Error Message
                  if (_errorMessage != null && !_isLoading) ...[
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage!,
                      style: theme.textTheme.p.copyWith(
                        color: theme.colorScheme.destructive,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],

                  const SizedBox(height: 24),

                  // Back to Sign In
                  ShadButton.link(
                    child: const Text('Back to Sign In'),
                    onPressed: () {
                      Navigator.of(context).pushNamedAndRemoveUntil(
                        SignInPage.routeName,
                        (route) => false,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
