import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/sign_in_page.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/utils/password_validation.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/widgets/password_strength_indicator.dart';

class ClaimantRegistrationPage extends StatefulWidget {
  const ClaimantRegistrationPage({super.key});

  static const String routeName = '/register-claimant';

  @override
  State<ClaimantRegistrationPage> createState() =>
      _ClaimantRegistrationPageState();
}

class _ClaimantRegistrationPageState extends State<ClaimantRegistrationPage> {
  final _formKey = GlobalKey<ShadFormState>();
  bool _isLoading = false;
  String? _errorMessage;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  final PocketBaseService _pbService = PocketBaseService();

  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _fullNameController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _fullNameController.dispose();
    super.dispose();
  }

  Future<void> _performRegistration() async {
    if (_formKey.currentState!.saveAndValidate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        await _pbService.signUp(
          email: _emailController.text,
          password: _passwordController.text,
          passwordConfirm: _confirmPasswordController.text,
          userType: 'claimant',
          name: _fullNameController.text,
          firstName: _fullNameController.text.split(' ').first,
          lastName:
              _fullNameController.text.contains(' ')
                  ? _fullNameController.text.split(' ').sublist(1).join(' ')
                  : '',
          status: 'pending', // Claimants might be auto-approved or pending
        );

        if (!mounted) return;
        ShadToaster.of(context).show(
          const ShadToast(
            title: Text('Registration Successful'),
            description: Text(
              'Please sign in with your new account to enable notifications.',
            ),
          ),
        );
        Navigator.of(context).pushReplacementNamed(SignInPage.routeName);
      } catch (e) {
        // Use centralized error mapping for user-friendly messages
        final friendlyError = PocketBaseService.mapPocketBaseError(e);

        setState(() {
          _errorMessage = friendlyError.message;
        });

        if (!mounted) return;
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: Text(friendlyError.title),
            description: Text(friendlyError.message),
          ),
        );
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    } else {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Validation Error'),
          description: const Text('Please correct the errors in the form.'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final shadTheme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Claimant Registration'),
        backgroundColor: shadTheme.colorScheme.primary,
        foregroundColor: shadTheme.colorScheme.primaryForeground,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: ShadForm(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                'Create your Claimant Account',
                style: shadTheme.textTheme.h4,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24.0),
              ShadInputFormField(
                id: 'email',
                controller: _emailController,
                label: const Text('Email'),
                placeholder: const Text('Enter your email'),
                keyboardType: TextInputType.emailAddress,
                validator: (v) {
                  if (v.isEmpty) return 'Email is required.';
                  if (!v.contains('@') || !v.contains('.')) {
                    return 'Enter a valid email.';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16.0),
              ShadInputFormField(
                id: 'password',
                controller: _passwordController,
                label: const Text('Password'),
                placeholder: const Text('Enter your password'),
                obscureText: _obscurePassword,
                trailing: ShadButton.ghost(
                  child: Icon(
                    _obscurePassword ? LucideIcons.eyeOff : LucideIcons.eye,
                    size: 16,
                  ),
                  onPressed:
                      () =>
                          setState(() => _obscurePassword = !_obscurePassword),
                ),
                validator: (v) => PasswordValidation.validatePassword(v),
                onChanged:
                    (value) => setState(
                      () {},
                    ), // Trigger rebuild for strength indicator
              ),
              const SizedBox(height: 16.0),

              // Password Strength Indicator
              PasswordStrengthIndicator(
                password: _passwordController.text,
                showRequirements: true,
              ),
              const SizedBox(height: 16.0),
              ShadInputFormField(
                id: 'confirm_password',
                controller: _confirmPasswordController,
                label: const Text('Confirm Password'),
                placeholder: const Text('Confirm your password'),
                obscureText: _obscureConfirmPassword,
                trailing: ShadButton.ghost(
                  child: Icon(
                    _obscureConfirmPassword
                        ? LucideIcons.eyeOff
                        : LucideIcons.eye,
                    size: 16,
                  ),
                  onPressed:
                      () => setState(
                        () =>
                            _obscureConfirmPassword = !_obscureConfirmPassword,
                      ),
                ),
                validator:
                    (v) => PasswordValidation.validatePasswordConfirm(
                      _passwordController.text,
                      v,
                    ),
              ),
              const SizedBox(height: 16.0),
              ShadInputFormField(
                id: 'full_name',
                controller: _fullNameController,
                label: const Text('Full Name'),
                placeholder: const Text('Enter your full name'),
                validator: (v) => v.isEmpty ? 'Full name is required.' : null,
              ),
              const SizedBox(height: 32.0),
              ShadButton(
                width: double.infinity,
                onPressed: _isLoading ? null : _performRegistration,
                child:
                    _isLoading
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator.adaptive(
                            strokeWidth: 2,
                          ),
                        )
                        : const Text('Create Account'),
              ),
              if (_errorMessage != null) ...[
                const SizedBox(height: 16),
                Text(
                  _errorMessage!,
                  style: TextStyle(color: shadTheme.colorScheme.destructive),
                  textAlign: TextAlign.center,
                ),
              ],
              const SizedBox(height: 24.0),
              TextButton(
                onPressed:
                    _isLoading
                        ? null
                        : () {
                          Navigator.of(context).pushNamed(SignInPage.routeName);
                        },
                child: Text(
                  'Already have an account? Sign In',
                  style: shadTheme.textTheme.p.copyWith(
                    color: shadTheme.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
