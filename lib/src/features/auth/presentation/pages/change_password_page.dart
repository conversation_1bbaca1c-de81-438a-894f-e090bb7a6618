import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/utils/password_validation.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/widgets/password_strength_indicator.dart';

/// Page for changing password for authenticated users
class ChangePasswordPage extends StatefulWidget {
  const ChangePasswordPage({super.key});

  static const String routeName = '/change-password';

  @override
  State<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage> {
  final _formKey = GlobalKey<ShadFormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final PocketBaseService _pbService = PocketBaseService();

  bool _isLoading = false;
  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;
  String? _errorMessage;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handlePasswordChange() async {
    if (!_formKey.currentState!.saveAndValidate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _pbService.changePassword(
        oldPassword: _currentPasswordController.text,
        newPassword: _newPasswordController.text,
        newPasswordConfirm: _confirmPasswordController.text,
      );

      if (!mounted) return;

      // Show success message
      ShadToaster.of(context).show(
        const ShadToast(
          title: Text('Password Changed'),
          description: Text('Your password has been updated successfully.'),
        ),
      );

      // Clear form
      _currentPasswordController.clear();
      _newPasswordController.clear();
      _confirmPasswordController.clear();

      // Navigate back
      Navigator.of(context).pop();
    } catch (e) {
      LoggerService.error('Password change error', e);

      String errorMessage = 'Failed to change password. Please try again.';

      // Handle specific error cases
      if (e.toString().contains('authenticate') ||
          e.toString().contains('invalid')) {
        errorMessage = 'Current password is incorrect. Please try again.';
      } else if (e.toString().contains('password')) {
        errorMessage =
            'New password requirements not met. Please check your password.';
      }

      setState(() {
        _errorMessage = errorMessage;
      });

      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Change Failed'),
          description: Text(errorMessage),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Change Password'),
        backgroundColor: theme.colorScheme.background,
        iconTheme: IconThemeData(color: theme.colorScheme.foreground),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 500),
          child: ShadForm(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text('Change Your Password', style: theme.textTheme.h3),
                const SizedBox(height: 8),
                Text(
                  'Enter your current password and choose a new secure password.',
                  style: theme.textTheme.p.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
                const SizedBox(height: 32),

                // Current Password Field
                ShadInputFormField(
                  id: 'currentPassword',
                  controller: _currentPasswordController,
                  label: const Text('Current Password'),
                  placeholder: const Text('Enter your current password'),
                  obscureText: _obscureCurrentPassword,
                  trailing: ShadButton.ghost(
                    child: Icon(
                      _obscureCurrentPassword
                          ? LucideIcons.eyeOff
                          : LucideIcons.eye,
                      size: 16,
                    ),
                    onPressed:
                        () => setState(
                          () =>
                              _obscureCurrentPassword =
                                  !_obscureCurrentPassword,
                        ),
                  ),
                  validator: (value) {
                    if (value.isEmpty) {
                      return 'Current password is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // New Password Field
                ShadInputFormField(
                  id: 'newPassword',
                  controller: _newPasswordController,
                  label: const Text('New Password'),
                  placeholder: const Text('Enter your new password'),
                  obscureText: _obscureNewPassword,
                  trailing: ShadButton.ghost(
                    child: Icon(
                      _obscureNewPassword
                          ? LucideIcons.eyeOff
                          : LucideIcons.eye,
                      size: 16,
                    ),
                    onPressed:
                        () => setState(
                          () => _obscureNewPassword = !_obscureNewPassword,
                        ),
                  ),
                  validator:
                      (value) => PasswordValidation.validatePassword(value),
                  onChanged:
                      (value) => setState(
                        () {},
                      ), // Trigger rebuild for strength indicator
                ),
                const SizedBox(height: 16),

                // Password Strength Indicator
                PasswordStrengthIndicator(
                  password: _newPasswordController.text,
                  showRequirements: true,
                ),
                const SizedBox(height: 24),

                // Confirm New Password Field
                ShadInputFormField(
                  id: 'confirmPassword',
                  controller: _confirmPasswordController,
                  label: const Text('Confirm New Password'),
                  placeholder: const Text('Confirm your new password'),
                  obscureText: _obscureConfirmPassword,
                  trailing: ShadButton.ghost(
                    child: Icon(
                      _obscureConfirmPassword
                          ? LucideIcons.eyeOff
                          : LucideIcons.eye,
                      size: 16,
                    ),
                    onPressed:
                        () => setState(
                          () =>
                              _obscureConfirmPassword =
                                  !_obscureConfirmPassword,
                        ),
                  ),
                  validator:
                      (value) => PasswordValidation.validatePasswordConfirm(
                        _newPasswordController.text,
                        value,
                      ),
                ),
                const SizedBox(height: 32),

                // Error Message
                if (_errorMessage != null && !_isLoading) ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.destructive.withValues(
                        alpha: 0.1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colorScheme.destructive.withValues(
                          alpha: 0.3,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          LucideIcons.triangle,
                          size: 16,
                          color: theme.colorScheme.destructive,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _errorMessage!,
                            style: theme.textTheme.small.copyWith(
                              color: theme.colorScheme.destructive,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: ShadButton.outline(
                        onPressed:
                            _isLoading
                                ? null
                                : () => Navigator.of(context).pop(),
                        child: const Text('Cancel'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ShadButton(
                        onPressed: _isLoading ? null : _handlePasswordChange,
                        child:
                            _isLoading
                                ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator.adaptive(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                )
                                : const Text('Change Password'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
