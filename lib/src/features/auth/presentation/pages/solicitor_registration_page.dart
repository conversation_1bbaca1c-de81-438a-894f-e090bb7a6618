import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/sign_in_page.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/utils/password_validation.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/widgets/password_strength_indicator.dart';

class SolicitorRegistrationPage extends StatefulWidget {
  const SolicitorRegistrationPage({super.key});

  static const String routeName = '/register-solicitor';

  @override
  State<SolicitorRegistrationPage> createState() =>
      _SolicitorRegistrationPageState();
}

class _SolicitorRegistrationPageState extends State<SolicitorRegistrationPage> {
  final _formKey = GlobalKey<ShadFormState>();
  bool _isLoading = false;
  String? _errorMessage;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  final PocketBaseService _pbService = PocketBaseService();

  // Text Editing Controllers
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _lawFirmNameController = TextEditingController();
  final _fullNameController =
      TextEditingController(); // For user's name and solicitor_name
  final _positionController = TextEditingController();
  final _contactNumberController = TextEditingController();
  final _firmAddressController = TextEditingController(); // Added
  final _firmRegNumberController = TextEditingController(); // Added

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _lawFirmNameController.dispose();
    _fullNameController.dispose();
    _positionController.dispose();
    _contactNumberController.dispose();
    _firmAddressController.dispose();
    _firmRegNumberController.dispose();
    super.dispose();
  }

  Future<void> _performRegistration() async {
    if (_formKey.currentState!.saveAndValidate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        // Step 1: Create the user
        final userRecord = await _pbService.signUp(
          email: _emailController.text,
          password: _passwordController.text,
          passwordConfirm: _confirmPasswordController.text,
          userType: 'solicitor',
          name:
              _fullNameController
                  .text, // Use full name for the user's 'name' field
          firstName:
              _fullNameController.text
                  .split(' ')
                  .first, // Basic split for first name
          lastName:
              _fullNameController.text.contains(' ')
                  ? _fullNameController.text.split(' ').sublist(1).join(' ')
                  : '', // Basic split for last name
          status: 'pending', // New registrations might need approval
        );

        // Step 2: Create the solicitor profile
        await _pbService.createSolicitorProfile(
          userId: userRecord.id,
          lawFirmName: _lawFirmNameController.text,
          solicitorName: _fullNameController.text, // Use full name here as well
          position: _positionController.text,
          contactNumber: _contactNumberController.text,
          firmAddress: _firmAddressController.text, // Pass new field
          firmRegistrationNumber:
              _firmRegNumberController.text, // Pass new field
          puStatus: 'pending', // Default PU status
        );

        // ignore: use_build_context_synchronously
        if (!mounted) return;
        ShadToaster.of(context).show(
          const ShadToast(
            title: Text('Registration Successful'),
            description: Text(
              'Please sign in with your new account to enable notifications.',
            ),
          ),
        );
        Navigator.of(context).pushReplacementNamed(SignInPage.routeName);
      } catch (e) {
        // Use centralized error mapping for user-friendly messages
        final friendlyError = PocketBaseService.mapPocketBaseError(e);

        setState(() {
          _errorMessage = friendlyError.message;
        });

        if (!mounted) return;
        ShadToaster.of(context).show(
          ShadToast.destructive(
            title: Text(friendlyError.title),
            description: Text(friendlyError.message),
          ),
        );
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    } else {
      ShadToaster.of(context).show(
        ShadToast.destructive(
          title: const Text('Validation Error'),
          description: const Text('Please correct the errors in the form.'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final shadTheme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Solicitor Registration'),
        backgroundColor: shadTheme.colorScheme.primary, // Use Shadcn theme
        foregroundColor: shadTheme.colorScheme.primaryForeground,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: ShadForm(
          // Changed from Form to ShadForm
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                'Create your Solicitor Account',
                style: shadTheme.textTheme.h4, // Use Shadcn theme
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24.0),
              ShadInputFormField(
                id: 'email',
                controller: _emailController,
                label: const Text('Email'),
                placeholder: const Text('Enter your email'),
                keyboardType: TextInputType.emailAddress,
                validator: (v) {
                  if (v == null || v.isEmpty) return 'Email is required.';
                  if (!v.contains('@') || !v.contains('.'))
                    return 'Enter a valid email.';
                  return null;
                },
              ),
              const SizedBox(height: 16.0),
              ShadInputFormField(
                id: 'password',
                controller: _passwordController,
                label: const Text('Password'),
                placeholder: const Text('Enter your password'),
                obscureText: _obscurePassword,
                trailing: ShadIconButton.ghost(
                  icon: Icon(
                    _obscurePassword ? LucideIcons.eyeOff : LucideIcons.eye,
                    size: 16,
                  ),
                  onPressed:
                      () =>
                          setState(() => _obscurePassword = !_obscurePassword),
                ),
                validator: (v) => PasswordValidation.validatePassword(v),
                onChanged:
                    (value) => setState(
                      () {},
                    ), // Trigger rebuild for strength indicator
              ),
              const SizedBox(height: 16.0),

              // Password Strength Indicator
              PasswordStrengthIndicator(
                password: _passwordController.text,
                showRequirements: true,
              ),
              const SizedBox(height: 16.0),
              ShadInputFormField(
                id: 'confirm_password',
                controller: _confirmPasswordController,
                label: const Text('Confirm Password'),
                placeholder: const Text('Confirm your password'),
                obscureText: _obscureConfirmPassword,
                trailing: ShadIconButton.ghost(
                  icon: Icon(
                    _obscureConfirmPassword
                        ? LucideIcons.eyeOff
                        : LucideIcons.eye,
                    size: 16,
                  ),
                  onPressed:
                      () => setState(
                        () =>
                            _obscureConfirmPassword = !_obscureConfirmPassword,
                      ),
                ),
                validator:
                    (v) => PasswordValidation.validatePasswordConfirm(
                      _passwordController.text,
                      v,
                    ),
              ),
              const SizedBox(height: 16.0),
              ShadInputFormField(
                id: 'solicitor_full_name',
                controller: _fullNameController,
                label: const Text('Full Name'),
                placeholder: const Text('Enter your full name'),
                validator:
                    (v) =>
                        v == null || v.isEmpty
                            ? 'Full name is required.'
                            : null,
              ),
              const SizedBox(height: 16.0),
              ShadInputFormField(
                id: 'law_firm_name',
                controller: _lawFirmNameController,
                label: const Text('Law Firm Name'),
                placeholder: const Text('Enter your law firm\'s name'),
                validator:
                    (v) =>
                        v == null || v.isEmpty
                            ? 'Law firm name is required.'
                            : null,
              ),
              const SizedBox(height: 16.0),
              ShadInputFormField(
                id: 'position_in_firm',
                controller: _positionController,
                label: const Text('Position in Firm'),
                placeholder: const Text('E.g., Partner, Associate'),
                validator:
                    (v) =>
                        v == null || v.isEmpty ? 'Position is required.' : null,
              ),
              const SizedBox(height: 16.0),
              ShadInputFormField(
                id: 'contact_number',
                controller: _contactNumberController,
                label: const Text('Contact Number'),
                placeholder: const Text('Enter your contact number'),
                keyboardType: TextInputType.phone,
                validator:
                    (v) =>
                        v == null || v.isEmpty
                            ? 'Contact number is required.'
                            : null,
              ),
              const SizedBox(height: 16.0),
              ShadInputFormField(
                id: 'firm_address',
                controller: _firmAddressController,
                label: const Text('Firm Address'),
                placeholder: const Text('Enter firm\'s full address'),
                validator:
                    (v) =>
                        v == null || v.isEmpty
                            ? 'Firm address is required.'
                            : null,
              ),
              const SizedBox(height: 16.0),
              ShadInputFormField(
                id: 'firm_registration_number',
                controller: _firmRegNumberController,
                label: const Text('Firm SRA/Registration Number'),
                placeholder: const Text('Enter SRA or registration number'),
                validator:
                    (v) =>
                        v == null || v.isEmpty
                            ? 'Firm registration number is required.'
                            : null,
              ),
              const SizedBox(height: 32.0),
              ShadButton(
                width: double.infinity,
                child:
                    _isLoading
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator.adaptive(
                            strokeWidth: 2,
                          ),
                        )
                        : const Text('Create Account'),
                onPressed: _isLoading ? null : _performRegistration,
              ),
              if (_errorMessage != null) ...[
                const SizedBox(height: 16),
                Text(
                  _errorMessage!,
                  style: TextStyle(color: shadTheme.colorScheme.destructive),
                  textAlign: TextAlign.center,
                ),
              ],
              const SizedBox(height: 24.0),
              TextButton(
                onPressed:
                    _isLoading
                        ? null
                        : () {
                          Navigator.of(context).pushNamed(SignInPage.routeName);
                        },
                child: Text(
                  'Already have an account? Sign In',
                  style: shadTheme.textTheme.p.copyWith(
                    color: shadTheme.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
