class User {
  final String id;
  final String? email;
  final String? name; // General name field
  final String? firstName;
  final String? lastName;
  final String? phoneNumber; // Phone number field
  final String? address; // Address field
  final String userType; // solicitor, co_funder, admin, claimant
  final bool verified; // From PocketBase user record
  final String? avatarUrl; // To construct from PocketBase file token
  final DateTime created;
  final DateTime updated;
  final bool tfaEnabled; // Two-Factor Authentication status
  final List<String>? backupCodes; // For 2FA recovery
  // Add other relevant fields from the 'users' collection as needed

  User({
    required this.id,
    required this.verified,
    this.email,
    this.name,
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.address,
    required this.userType,
    this.avatarUrl,
    required this.created,
    required this.updated,
    required this.tfaEnabled,
    this.backupCodes,
  });

  factory User.fromJson(Map<String, dynamic> record) {
    // Helper to construct full URL for avatar if present
    String? getAvatarUrl(String? avatarFileName) {
      if (avatarFileName == null || avatarFileName.isEmpty) {
        return null;
      }
      // TODO: Replace with actual PocketBase instance URL and collection ID/name
      // This is a placeholder structure. Actual URL construction might differ.
      // e.g., return 'YOUR_POCKETBASE_URL/api/files/COLLECTION_ID_OR_NAME/${record['id']}/$avatarFileName';
      return avatarFileName; // For now, just returning the filename
    }

    return User(
      id: record['id'] as String,
      email: record['email'] as String?,
      name: record['name'] as String?,
      firstName: record['first_name'] as String?,
      lastName: record['last_name'] as String?,
      phoneNumber: record['phone_number'] as String?,
      address: record['address'] as String?,
      userType:
          record['user_type'] as String? ??
          'claimant', // Default or handle error
      verified:
          record['verified'] as bool? ?? false, // PocketBase 'verified' field
      avatarUrl: getAvatarUrl(record['avatar'] as String?),
      created:
          DateTime.tryParse(record['created'] as String? ?? '') ??
          DateTime.now(),
      updated:
          DateTime.tryParse(record['updated'] as String? ?? '') ??
          DateTime.now(),
      tfaEnabled:
          record['tfa_enabled'] as bool? ??
          false, // From PocketBase 'users' collection
      backupCodes:
          record['backup_codes'] != null
              ? List<String>.from(record['backup_codes'] as List<dynamic>)
              : null,
    );
  }

  String get displayName {
    if (firstName != null &&
        lastName != null &&
        firstName!.isNotEmpty &&
        lastName!.isNotEmpty) {
      return '$firstName $lastName';
    }
    if (name != null && name!.isNotEmpty) {
      return name!;
    }
    if (email != null && email!.isNotEmpty) {
      return email!;
    }
    return id; // Fallback to ID if no other name is available
  }

  // toJson might be needed for updates, but usually user profiles are managed via specific auth endpoints
  Map<String, dynamic> toJsonForUpdate() {
    return {
      'name': name,
      'first_name': firstName,
      'last_name': lastName,
      // 'email': email, // Email updates often have special handling
      // 'user_type': userType, // User type changes are usually admin-only
      // Avatar updates are typically done via file upload, not direct JSON
    };
  }
}
