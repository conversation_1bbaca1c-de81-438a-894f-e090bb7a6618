import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:go_router/go_router.dart'; // Keep commented if not root router
import 'package:shadcn_ui/shadcn_ui.dart';

// TODO: Adjust import path if GlobalSearchTermProvider is located elsewhere
import '../../application/global_search_provider.dart';
import '../../data/models/global_search_result_item.dart';

// TODO: Adjust import path for your app's theme/core widgets if necessary
// import '../../../../core/theme/app_theme.dart';
// import '../../../../core/ui/widgets/loading_spinner_widget.dart';
// import '../../../../core/ui/widgets/empty_state_widget.dart';

class SearchResultsPage extends ConsumerStatefulWidget {
  static const routeName = '/search-results';
  final String? searchTerm; // Optional: if passed directly via route

  const SearchResultsPage({super.key, this.searchTerm});

  @override
  ConsumerState<SearchResultsPage> createState() => _SearchResultsPageState();
}

class _SearchResultsPageState extends ConsumerState<SearchResultsPage> {
  String? _activeFilterType;

  @override
  Widget build(BuildContext context) {
    final currentSearchTerm = ref.watch(globalSearchTermProvider);
    final theme = ShadTheme.of(context); // Assuming ShadTheme is available

    return Scaffold(
      appBar: AppBar(
        title: Text('Search Results for: "${currentSearchTerm ?? widget.searchTerm ?? ""}"'),
      ),
      body: Consumer(
        builder: (context, ref, child) {
          if (currentSearchTerm == null || currentSearchTerm.isEmpty) {
            return const Center(child: Text('Please enter a search term.'));
          }

          final searchResultsAsync = ref.watch(globalSearchProvider);

          return searchResultsAsync.when(
            data: (results) {
              if (results.isEmpty) {
                return Center(
                  child: Text(
                    'No results found for "$currentSearchTerm"',
                    style: theme.textTheme.h4, // Using Shadcn theme
                  ),
                );
              }

              final filteredResults = _activeFilterType == null
                  ? results
                  : results.where((item) => item.type == _activeFilterType).toList();

              if (filteredResults.isEmpty && _activeFilterType != null) {
                return Center(
                  child: Text(
                    'No results found for "$currentSearchTerm" with filter "$_activeFilterType"',
                    style: theme.textTheme.h4, // Using Shadcn theme
                    textAlign: TextAlign.center,
                  ),
                );
              }

              final groupedResults = <String, List<GlobalSearchResultItem>>{};
              for (var item in filteredResults) {
                (groupedResults[item.type] ??= []).add(item);
              }

              final uniqueTypes = results.map((item) => item.type).toSet().toList();

              return Column(
                children: [
                  // Filter buttons (commented out for now due to ShadButton issues)
                  // if (uniqueTypes.length > 1)
                  //   Padding(
                  //     padding: const EdgeInsets.all(8.0),
                  //     child: Wrap(
                  //       spacing: 8.0,
                  //       runSpacing: 4.0,
                  //       children: [
                  //         ElevatedButton( // Placeholder
                  //           onPressed: () => setState(() => _activeFilterType = null),
                  //           child: const Text('All'),
                  //         ),
                  //         ...uniqueTypes.map(
                  //           (type) => ElevatedButton( // Placeholder
                  //             onPressed: () => setState(() => _activeFilterType = type),
                  //             child: Text(type),
                  //           ),
                  //         ),
                  //       ],
                  //     ),
                  //   ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: groupedResults.length,
                      itemBuilder: (context, index) {
                        final type = groupedResults.keys.elementAt(index);
                        final itemsInGroup = groupedResults[type]!;
                        return ExpansionTile(
                          title: Text('$type (${itemsInGroup.length})', style: theme.textTheme.h4), // Using Shadcn theme
                          initiallyExpanded: true,
                          children: itemsInGroup.map((item) {
                            return Card( // Using Material Card
                              child: ListTile(
                                title: Text(item.title),
                                subtitle: item.snippet != null ? Text(item.snippet!) : null,
                                onTap: () {
                                  if (item.route != null) {
                                    // Using Navigator.pushNamed as GoRouter might not be root
                                    Navigator.of(context).pushNamed(item.route!, arguments: item.routeArguments);
                                    print("Navigating to: ${item.route} with args: ${item.routeArguments}");
                                  }
                                },
                              ),
                            );
                          }).toList(),
                        );
                      },
                    ),
                  ),
                ],
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stackTrace) => Center(
              child: Text(
                'Error fetching search results: $error',
                // Consider using a Material theme style if Shadcn theme causes issues here
                // style: TextStyle(color: Colors.red),
              ),
            ),
          );
        },
      ),
    );
  }
}
