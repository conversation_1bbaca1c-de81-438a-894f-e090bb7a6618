import 'package:pocketbase/pocketbase.dart';

class GlobalSearchResultItem {
  final String id;
  final String title;
  final String type;
  final String? snippet;
  final RecordModel? originalRecord;
  final String route;
  final Map<String, String> routeArguments;

  GlobalSearchResultItem({
    required this.id,
    required this.title,
    required this.type,
    this.snippet,
    this.originalRecord,
    required this.route,
    required this.routeArguments,
  });

  @override
  String toString() {
    return 'GlobalSearchResultItem(id: $id, title: $title, type: $type, snippet: $snippet, route: $route, routeArguments: $routeArguments, originalRecord: ${originalRecord?.id})';
  }

  // Optional: Add factory constructors or other helper methods if needed later
}