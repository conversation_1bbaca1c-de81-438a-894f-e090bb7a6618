import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/search/data/models/global_search_result_item.dart';

// Provider for the search term
final globalSearchTermProvider = StateProvider<String>((ref) => '');

// Provider for the global search results
final globalSearchProvider = FutureProvider<List<GlobalSearchResultItem>>((ref) async {
  final searchTerm = ref.watch(globalSearchTermProvider);
  if (searchTerm.isEmpty || searchTerm.length < 2) { // Avoid searching for very short terms
    return [];
  }

  final pbClient = ref.watch(pocketBaseClientProvider);
  final List<GlobalSearchResultItem> allResults = [];

  // Collections and fields to search
  // Based on SOLICITOR_FEATKILO.md section 4.6 and parent task details
  final collectionsToSearch = {
    'funding_applications': {
      'fields': ['claim_title', 'id'],
      'type': 'Application',
      'route': '/solicitor/application-detail', // Example route
      'idFieldForRoute': 'id',
    },
    'claims': {
      'fields': ['case_title', 'id'], // Assuming 'case_title' for claims
      'type': 'Claim',
      'route': '/solicitor/claim-detail', // Example route
      'idFieldForRoute': 'id',
    },
    'firm_documents': {
      'fields': ['name'],
      'type': 'Document',
      'route': '/solicitor/firm-documents', // Example, might need specific document view
      'idFieldForRoute': 'id',
    },
    // For people, we might need to search multiple collections or a view
    // Assuming 'users' for general user info and 'solicitor_profiles' for solicitor specific
    'users': {
      'fields': ['name', 'email'],
      'type': 'Person',
      'route': '/solicitor/user-profile', // Example route
      'idFieldForRoute': 'id',
    },
    'solicitor_profiles': {
      'fields': ['solicitor_name', 'law_firm_name'],
      'type': 'Person', // Could also be 'Solicitor Profile'
      'route': '/solicitor/user-profile', // Example, might link to user ID
      'idFieldForRoute': 'user_id', // Assuming user_id links to users table
    },
  };

  final searchTermProcessed = searchTerm.toLowerCase(); // Process search term once

  for (var entry in collectionsToSearch.entries) {
    final collectionName = entry.key;
    final config = entry.value;
    final fieldsToSearch = config['fields'] as List<String>;
    final itemType = config['type'] as String;
    final route = config['route'] as String;
    final idFieldForRoute = config['idFieldForRoute'] as String;

    List<String> filterParts = [];
    for (var field in fieldsToSearch) {
      // PocketBase filter: field ~ 'value' (contains, case-insensitive by default for text)
      filterParts.add("$field ~ '$searchTermProcessed'");
    }
    final filterString = filterParts.join(' || ');

    try {
      final resultList = await pbClient.collection(collectionName).getFullList(
        filter: filterString,
      );

      for (var record in resultList) {
        String title = 'N/A';
        // Determine title based on collection and available fields
        if (collectionName == 'funding_applications') {
          title = record.data['claim_title'] as String? ?? record.id;
        } else if (collectionName == 'claims') {
          title = record.data['case_title'] as String? ?? record.id;
        } else if (collectionName == 'firm_documents') {
          title = record.data['name'] as String? ?? record.id;
        } else if (collectionName == 'users') {
          title = record.data['name'] as String? ?? record.data['email'] as String? ?? record.id;
        } else if (collectionName == 'solicitor_profiles') {
          title = record.data['solicitor_name'] as String? ?? record.data['law_firm_name'] as String? ?? record.id;
        }
        
        String recordIdForRoute = record.data[idFieldForRoute] as String? ?? record.id;
        if (collectionName == 'solicitor_profiles' && idFieldForRoute == 'user_id') {
             recordIdForRoute = record.data['user_id'] as String? ?? record.id; // Ensure user_id is used
        }


        allResults.add(GlobalSearchResultItem(
          id: record.id,
          title: title,
          type: itemType,
          originalRecord: record,
          route: route,
          routeArguments: {'id': recordIdForRoute},
          snippet: fieldsToSearch.map((f) => record.data[f]?.toString() ?? '').where((s) => s.isNotEmpty).join(' | '),
        ));
      }
    } catch (e) {
      print('Error searching collection $collectionName: $e');
      // Optionally, add error indicators to results or handle differently
    }
  }
  // Remove duplicates based on original record ID and type, favoring the first encountered
  final uniqueResults = <String, GlobalSearchResultItem>{};
  for (var result in allResults) {
    final key = '${result.type}-${result.originalRecord?.id}';
    if (!uniqueResults.containsKey(key)) {
      uniqueResults[key] = result;
    }
  }
  return uniqueResults.values.toList();
});