import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A custom file picker implementation that works around platform-specific issues
class CustomFilePicker {
  /// Singleton instance
  static final CustomFilePicker _instance = CustomFilePicker._internal();
  
  /// Factory constructor
  factory CustomFilePicker() => _instance;
  
  /// Internal constructor
  CustomFilePicker._internal();
  
  /// Pick files with error handling for all platforms
  Future<FilePickerResult?> pickFiles({
    BuildContext? context,
    FileType type = FileType.any,
    List<String>? allowedExtensions,
    bool allowMultiple = false,
    bool withData = false,
    bool withReadStream = false,
    bool lockParentWindow = false,
    String? dialogTitle,
  }) async {
    try {
      // Use the standard file picker implementation
      return await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: allowedExtensions,
        allowMultiple: allowMultiple,
        withData: withData,
        withReadStream: withReadStream,
        lockParentWindow: lockParentWindow,
        dialogTitle: dialogTitle,
      );
    } on PlatformException catch (e) {
      _handlePlatformException(e, context);
      return null;
    } catch (e) {
      // Handle platform-specific issues
      if (!kIsWeb && Platform.isLinux) {
        print('Linux file picker error: $e');
        // Show a fallback dialog if context is provided
        if (context != null) {
          _showFallbackDialog(context);
        }
      } else {
        // For other platforms, just print the error
        print('File picker error: $e');
        if (context != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error picking files: $e')),
          );
        }
      }
      return null;
    }
  }
  
  /// Pick a directory with error handling
  Future<String?> getDirectoryPath({
    BuildContext? context,
    String? dialogTitle,
    bool lockParentWindow = false,
  }) async {
    try {
      return await FilePicker.platform.getDirectoryPath(
        dialogTitle: dialogTitle,
        lockParentWindow: lockParentWindow,
      );
    } on PlatformException catch (e) {
      _handlePlatformException(e, context);
      return null;
    } catch (e) {
      if (!kIsWeb && Platform.isLinux) {
        print('Linux directory picker error: $e');
        if (context != null) {
          _showFallbackDialog(context);
        }
      } else {
        print('Directory picker error: $e');
        if (context != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error picking directory: $e')),
          );
        }
      }
      return null;
    }
  }
  
  /// Save a file with error handling
  Future<String?> saveFile({
    BuildContext? context,
    String? dialogTitle,
    String? fileName,
    FileType type = FileType.any,
    List<String>? allowedExtensions,
    bool lockParentWindow = false,
  }) async {
    try {
      return await FilePicker.platform.saveFile(
        dialogTitle: dialogTitle,
        fileName: fileName,
        type: type,
        allowedExtensions: allowedExtensions,
        lockParentWindow: lockParentWindow,
      );
    } on PlatformException catch (e) {
      _handlePlatformException(e, context);
      return null;
    } catch (e) {
      if (!kIsWeb && Platform.isLinux) {
        print('Linux save file error: $e');
        if (context != null) {
          _showFallbackDialog(context);
        }
      } else {
        print('Save file error: $e');
        if (context != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error saving file: $e')),
          );
        }
      }
      return null;
    }
  }
  
  /// Clear temporary files with error handling
  Future<bool> clearTemporaryFiles({BuildContext? context}) async {
    try {
      return await FilePicker.platform.clearTemporaryFiles() ?? false;
    } on PlatformException catch (e) {
      _handlePlatformException(e, context);
      return false;
    } catch (e) {
      print('Clear temporary files error: $e');
      if (context != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error clearing temporary files: $e')),
        );
      }
      return false;
    }
  }
  
  /// Handle platform exceptions
  void _handlePlatformException(PlatformException e, BuildContext? context) {
    print('Platform exception: ${e.message}');
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.message}')),
      );
    }
  }
  
  /// Show a fallback dialog for Linux when file picker fails
  void _showFallbackDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('File Picker Error'),
        content: const Text(
          'There was an error with the file picker on Linux. '
          'This is a known issue with the file_picker package. '
          'Please try again or use a different platform.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
