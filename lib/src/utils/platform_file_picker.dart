import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import '../core/services/logger_service.dart';

/// A platform-aware file picker utility that handles the Linux implementation issue
class PlatformFilePicker {
  /// Singleton instance
  static final PlatformFilePicker _instance = PlatformFilePicker._internal();

  /// Factory constructor
  factory PlatformFilePicker() => _instance;

  /// Internal constructor
  PlatformFilePicker._internal();

  /// Pick files with platform-specific handling
  Future<FilePickerResult?> pickFiles({
    FileType type = FileType.any,
    List<String>? allowedExtensions,
    bool allowMultiple = false,
    bool withData = false,
    bool withReadStream = false,
    bool lockParentWindow = false,
    String? dialogTitle,
  }) async {
    // Handle iOS/macOS image picker issues
    if (!kIsWeb &&
        (Platform.isIOS || Platform.isMacOS) &&
        type == FileType.image) {
      // Use custom file type with specific extensions for better iOS/macOS compatibility
      return await _pickImagesWithFallback(
        allowMultiple: allowMultiple,
        withData: withData,
        withReadStream: withReadStream,
        lockParentWindow: lockParentWindow,
        dialogTitle: dialogTitle,
      );
    }

    // On Linux, we need special handling
    if (!kIsWeb && Platform.isLinux) {
      try {
        return await FilePicker.platform.pickFiles(
          type: type,
          allowedExtensions: allowedExtensions,
          allowMultiple: allowMultiple,
          withData: withData,
          withReadStream: withReadStream,
          lockParentWindow: lockParentWindow,
          dialogTitle: dialogTitle,
        );
      } catch (e) {
        // If the standard method fails on Linux, we can implement a fallback
        // For example, using a different approach or showing a custom dialog
        LoggerService.error('Linux file picker error', e);

        // For now, we'll rethrow, but you could implement a custom solution here
        rethrow;
      }
    } else {
      // For other platforms, use the standard implementation
      return await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: allowedExtensions,
        allowMultiple: allowMultiple,
        withData: withData,
        withReadStream: withReadStream,
        lockParentWindow: lockParentWindow,
        dialogTitle: dialogTitle,
      );
    }
  }

  /// Special handling for iOS/macOS image picking with fallback
  Future<FilePickerResult?> _pickImagesWithFallback({
    bool allowMultiple = false,
    bool withData = false,
    bool withReadStream = false,
    bool lockParentWindow = false,
    String? dialogTitle,
  }) async {
    try {
      // First try with custom file type and specific extensions
      return await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png', 'gif'],
        allowMultiple: allowMultiple,
        withData: withData,
        withReadStream: withReadStream,
        lockParentWindow: lockParentWindow,
        dialogTitle: dialogTitle ?? 'Select Image',
      );
    } catch (e) {
      if (e.toString().contains('file_picker_error') ||
          e.toString().contains('Cannot load representation')) {
        try {
          // Fallback: try with any file type
          return await FilePicker.platform.pickFiles(
            type: FileType.any,
            allowMultiple: allowMultiple,
            withData: withData,
            withReadStream: withReadStream,
            lockParentWindow: lockParentWindow,
            dialogTitle: dialogTitle ?? 'Select File',
          );
        } catch (fallbackError) {
          // If both methods fail, rethrow the original error
          rethrow;
        }
      } else {
        rethrow;
      }
    }
  }

  /// Pick a directory with platform-specific handling
  Future<String?> getDirectoryPath({
    String? dialogTitle,
    bool lockParentWindow = false,
  }) async {
    if (!kIsWeb && Platform.isLinux) {
      try {
        return await FilePicker.platform.getDirectoryPath(
          dialogTitle: dialogTitle,
          lockParentWindow: lockParentWindow,
        );
      } catch (e) {
        LoggerService.error('Linux directory picker error', e);
        rethrow;
      }
    } else {
      return await FilePicker.platform.getDirectoryPath(
        dialogTitle: dialogTitle,
        lockParentWindow: lockParentWindow,
      );
    }
  }

  /// Save a file with platform-specific handling
  Future<String?> saveFile({
    String? dialogTitle,
    String? fileName,
    FileType type = FileType.any,
    List<String>? allowedExtensions,
    bool lockParentWindow = false,
  }) async {
    if (!kIsWeb && Platform.isLinux) {
      try {
        return await FilePicker.platform.saveFile(
          dialogTitle: dialogTitle,
          fileName: fileName,
          type: type,
          allowedExtensions: allowedExtensions,
          lockParentWindow: lockParentWindow,
        );
      } catch (e) {
        LoggerService.error('Linux save file error', e);
        rethrow;
      }
    } else {
      return await FilePicker.platform.saveFile(
        dialogTitle: dialogTitle,
        fileName: fileName,
        type: type,
        allowedExtensions: allowedExtensions,
        lockParentWindow: lockParentWindow,
      );
    }
  }

  /// Clear temporary files with platform-specific handling
  Future<bool?> clearTemporaryFiles() async {
    if (!kIsWeb && Platform.isLinux) {
      try {
        return await FilePicker.platform.clearTemporaryFiles();
      } catch (e) {
        LoggerService.error('Linux clear temporary files error', e);
        return false;
      }
    } else {
      return await FilePicker.platform.clearTemporaryFiles();
    }
  }
}
