import 'dart:io';
import 'package:file_picker/file_picker.dart';

/// A wrapper class for file_picker to handle platform-specific implementations
/// This helps resolve the Linux implementation issue
class FilePickerWrapper {
  /// Singleton instance
  static final FilePickerWrapper _instance = FilePickerWrapper._internal();
  
  /// Factory constructor
  factory FilePickerWrapper() => _instance;
  
  /// Internal constructor
  FilePickerWrapper._internal();
  
  /// The FilePicker instance
  final FilePicker _filePicker = FilePicker.platform;
  
  /// Pick a single file
  /// 
  /// [type] - The type of file to pick
  /// [allowedExtensions] - List of allowed extensions if type is custom
  Future<FilePickerResult?> pickFile({
    FileType type = FileType.any,
    List<String>? allowedExtensions,
    bool allowMultiple = false,
    bool withData = false,
    bool withReadStream = false,
    bool lockParentWindow = false,
    String? dialogTitle,
  }) async {
    try {
      return await _filePicker.pickFiles(
        type: type,
        allowedExtensions: allowedExtensions,
        allowMultiple: allowMultiple,
        withData: withData,
        withReadStream: withReadStream,
        lockParentWindow: lockParentWindow,
        dialogTitle: dialogTitle,
      );
    } catch (e) {
      // Handle Linux-specific errors if needed
      if (Platform.isLinux) {
        print('Linux file picker error: $e');
        // You could implement a fallback method for Linux here if needed
      }
      rethrow;
    }
  }
  
  /// Pick multiple files
  Future<FilePickerResult?> pickMultipleFiles({
    FileType type = FileType.any,
    List<String>? allowedExtensions,
    bool withData = false,
    bool withReadStream = false,
    bool lockParentWindow = false,
    String? dialogTitle,
  }) async {
    return pickFile(
      type: type,
      allowedExtensions: allowedExtensions,
      allowMultiple: true,
      withData: withData,
      withReadStream: withReadStream,
      lockParentWindow: lockParentWindow,
      dialogTitle: dialogTitle,
    );
  }
  
  /// Pick a directory
  Future<String?> pickDirectory({
    String? dialogTitle,
    bool lockParentWindow = false,
  }) async {
    try {
      return await _filePicker.getDirectoryPath(
        dialogTitle: dialogTitle,
        lockParentWindow: lockParentWindow,
      );
    } catch (e) {
      if (Platform.isLinux) {
        print('Linux directory picker error: $e');
        // You could implement a fallback method for Linux here if needed
      }
      rethrow;
    }
  }
  
  /// Save a file
  Future<String?> saveFile({
    String? dialogTitle,
    String? fileName,
    FileType type = FileType.any,
    List<String>? allowedExtensions,
    bool lockParentWindow = false,
  }) async {
    try {
      return await _filePicker.saveFile(
        dialogTitle: dialogTitle,
        fileName: fileName,
        type: type,
        allowedExtensions: allowedExtensions,
        lockParentWindow: lockParentWindow,
      );
    } catch (e) {
      if (Platform.isLinux) {
        print('Linux save file error: $e');
        // You could implement a fallback method for Linux here if needed
      }
      rethrow;
    }
  }
  
  /// Clear cached files
  Future<bool?> clearTemporaryFiles() async {
    try {
      return await _filePicker.clearTemporaryFiles();
    } catch (e) {
      if (Platform.isLinux) {
        print('Linux clear temporary files error: $e');
      }
      rethrow;
    }
  }
}
