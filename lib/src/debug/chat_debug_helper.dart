import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/data/services/chat_service.dart';

/// Debug helper for chat functionality
class ChatDebugHelper {
  static final ChatService _chatService = ChatService();
  static final PocketBaseService _pbService = PocketBaseService();

  /// Debug authentication status
  static void debugAuth() {
    LoggerService.info('=== CHAT DEBUG: Authentication Status ===');
    LoggerService.info('Is authenticated: ${_pbService.isSignedIn}');
    LoggerService.info('Current user: ${_pbService.currentUser?.toJson()}');
    LoggerService.info('Auth token: ${_pbService.authStore.token}');
    LoggerService.info('Is claimant: ${_chatService.isClaimant}');
    LoggerService.info('Current user ID: ${_chatService.currentUserId}');
  }

  /// Debug PocketBase connection
  static Future<void> debugConnection() async {
    LoggerService.info('=== CHAT DEBUG: PocketBase Connection ===');
    try {
      // Test basic connection
      final health = await _pbService.pb.health.check();
      LoggerService.info('PocketBase health check: $health');
      
      // Test collection access
      final collections = await _pbService.pb.collections.getFullList();
      LoggerService.info('Available collections: ${collections.map((c) => c.name).toList()}');
      
      // Check if application_communications collection exists
      final appCommExists = collections.any((c) => c.name == 'application_communications');
      LoggerService.info('application_communications collection exists: $appCommExists');
      
    } catch (e) {
      LoggerService.error('PocketBase connection error', e);
    }
  }

  /// Debug claimant profile
  static Future<void> debugClaimantProfile() async {
    LoggerService.info('=== CHAT DEBUG: Claimant Profile ===');
    try {
      final profile = await _chatService.getCurrentClaimantProfile();
      if (profile != null) {
        LoggerService.info('Claimant profile found: ${profile.id}');
        LoggerService.info('Associated claim IDs: ${profile.associatedClaimIds}');
        LoggerService.info('Display name: ${profile.displayName}');
        LoggerService.info('Display email: ${profile.displayEmail}');
      } else {
        LoggerService.warning('No claimant profile found');
      }
    } catch (e) {
      LoggerService.error('Error getting claimant profile', e);
    }
  }

  /// Debug message sending
  static Future<void> debugSendMessage(String claimId, String content) async {
    LoggerService.info('=== CHAT DEBUG: Send Message ===');
    LoggerService.info('Claim ID: $claimId');
    LoggerService.info('Content: $content');
    
    try {
      // Check authentication first
      debugAuth();
      
      // Check if we can access the collection
      final records = await _pbService.pb
          .collection('application_communications')
          .getList(page: 1, perPage: 1);
      LoggerService.info('Can access application_communications: ${records.items.length} records found');
      
      // Try to send the message
      LoggerService.info('Attempting to send message...');
      final message = await _chatService.sendMessage(
        claimId: claimId,
        content: content,
      );
      
      LoggerService.info('Message sent successfully!');
      LoggerService.info('Message ID: ${message.id}');
      LoggerService.info('Message content: ${message.messageContent}');
      LoggerService.info('Timestamp: ${message.timestamp}');
      
    } catch (e) {
      LoggerService.error('Error sending message', e);
      
      // Additional debugging
      if (e.toString().contains('401')) {
        LoggerService.error('Authentication error - user not authenticated');
      } else if (e.toString().contains('403')) {
        LoggerService.error('Permission error - user not authorized');
      } else if (e.toString().contains('404')) {
        LoggerService.error('Collection not found error');
      } else if (e.toString().contains('400')) {
        LoggerService.error('Bad request error - check data format');
      }
    }
  }

  /// Debug message retrieval
  static Future<void> debugGetMessages(String claimId) async {
    LoggerService.info('=== CHAT DEBUG: Get Messages ===');
    LoggerService.info('Claim ID: $claimId');
    
    try {
      final messages = await _chatService.getMessagesForConversation(claimId);
      LoggerService.info('Retrieved ${messages.length} messages');
      
      for (final message in messages) {
        LoggerService.info('Message ${message.id}: ${message.messageContent}');
      }
    } catch (e) {
      LoggerService.error('Error getting messages', e);
    }
  }

  /// Run comprehensive debug
  static Future<void> runFullDebug(String claimId, String testMessage) async {
    LoggerService.info('=== STARTING COMPREHENSIVE CHAT DEBUG ===');
    
    debugAuth();
    await debugConnection();
    await debugClaimantProfile();
    await debugGetMessages(claimId);
    await debugSendMessage(claimId, testMessage);
    
    LoggerService.info('=== CHAT DEBUG COMPLETE ===');
  }
}
