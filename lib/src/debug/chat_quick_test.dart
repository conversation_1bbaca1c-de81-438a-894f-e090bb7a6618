import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';

/// Quick test to identify common chat issues
class ChatQuickTest {
  static final PocketBaseService _pbService = PocketBaseService();

  /// Test basic connectivity and authentication
  static Future<Map<String, dynamic>> runQuickTest() async {
    final results = <String, dynamic>{};

    // Test 1: PocketBase connection
    try {
      LoggerService.info('Testing PocketBase connection...');
      final health = await _pbService.pb.health.check();
      results['connection'] = 'OK';
      results['health'] = health;
      LoggerService.info('✅ PocketBase connection: OK');
    } catch (e) {
      results['connection'] = 'FAILED';
      results['connection_error'] = e.toString();
      LoggerService.error('❌ PocketBase connection: FAILED', e);
    }

    // Test 2: Authentication
    try {
      final isAuthenticated = _pbService.isSignedIn;
      final currentUser = _pbService.currentUser;

      results['authenticated'] = isAuthenticated;
      if (isAuthenticated && currentUser != null) {
        results['user_id'] = currentUser.id;
        results['user_type'] = currentUser.data['user_type'];
        results['user_email'] = currentUser.data['email'];
        LoggerService.info(
          '✅ Authentication: OK (${currentUser.data['user_type']})',
        );
      } else {
        LoggerService.warning('⚠️ Authentication: User not signed in');
      }
    } catch (e) {
      results['authentication_error'] = e.toString();
      LoggerService.error('❌ Authentication check: FAILED', e);
    }

    // Test 3: Collection access
    try {
      LoggerService.info('Testing collection access...');
      final records = await _pbService.pb
          .collection('application_communications')
          .getList(page: 1, perPage: 1);

      results['collection_access'] = 'OK';
      results['collection_count'] = records.totalItems;
      LoggerService.info(
        '✅ Collection access: OK (${records.totalItems} total records)',
      );
    } catch (e) {
      results['collection_access'] = 'FAILED';
      results['collection_error'] = e.toString();
      LoggerService.error('❌ Collection access: FAILED', e);
    }

    // Test 4: Claimant profile
    if (results['authenticated'] == true) {
      try {
        LoggerService.info('Testing claimant profile...');
        final userId = _pbService.currentUser!.id;
        final profileRecords = await _pbService.pb
            .collection('claimant_profiles')
            .getList(filter: 'user_id = "$userId"');

        if (profileRecords.items.isNotEmpty) {
          final profile = profileRecords.items.first;
          results['claimant_profile'] = 'OK';
          results['profile_id'] = profile.id;
          results['associated_claims'] = profile.data['associated_claim_ids'];
          LoggerService.info('✅ Claimant profile: OK');
        } else {
          results['claimant_profile'] = 'NOT_FOUND';
          LoggerService.warning('⚠️ Claimant profile: Not found');
        }
      } catch (e) {
        results['claimant_profile'] = 'FAILED';
        results['profile_error'] = e.toString();
        LoggerService.error('❌ Claimant profile: FAILED', e);
      }
    }

    // Test 5: Simple message creation test
    if (results['authenticated'] == true &&
        results['collection_access'] == 'OK') {
      try {
        LoggerService.info('Testing message creation...');

        // Get the first available claim ID from the claimant's profile
        String? testClaimId;
        try {
          final claimantProfile = await _pbService.pb
              .collection('claimant_profiles')
              .getFirstListItem('user_id = "${_pbService.currentUser!.id}"');

          final associatedClaims =
              claimantProfile.data['associated_claim_ids'] as List?;
          if (associatedClaims != null && associatedClaims.isNotEmpty) {
            testClaimId = associatedClaims.first.toString();
          }
        } catch (e) {
          LoggerService.error('Failed to get claimant profile for test', e);
        }

        if (testClaimId == null) {
          LoggerService.error(
            'No valid claim ID found for test message creation',
          );
          results['message_creation'] = 'FAILED';
          results['message_error'] = 'No valid claim ID available for testing';
          return results;
        }

        // Try to create a test message with a real claim ID
        final testData = {
          'application_id': testClaimId,
          'sender_id': _pbService.currentUser!.id,
          'message_content': 'Test message from chat debug',
          'recipient_group': 'agent',
        };

        final record = await _pbService.pb
            .collection('application_communications')
            .create(body: testData);

        results['message_creation'] = 'OK';
        results['test_message_id'] = record.id;
        LoggerService.info('✅ Message creation: OK');

        // Clean up test message
        await _pbService.pb
            .collection('application_communications')
            .delete(record.id);
        LoggerService.info('🧹 Test message cleaned up');
      } catch (e) {
        results['message_creation'] = 'FAILED';
        results['message_error'] = e.toString();
        LoggerService.error('❌ Message creation: FAILED', e);
      }
    }

    return results;
  }

  /// Print formatted test results
  static void printResults(Map<String, dynamic> results) {
    LoggerService.info('=== CHAT QUICK TEST RESULTS ===');

    results.forEach((key, value) {
      LoggerService.info('$key: $value');
    });

    LoggerService.info('=== END TEST RESULTS ===');

    // Summary
    final issues = <String>[];

    if (results['connection'] != 'OK') {
      issues.add('PocketBase connection failed');
    }

    if (results['authenticated'] != true) {
      issues.add('User not authenticated');
    }

    if (results['collection_access'] != 'OK') {
      issues.add('Cannot access application_communications collection');
    }

    if (results['claimant_profile'] != 'OK') {
      issues.add('Claimant profile issues');
    }

    if (results['message_creation'] != 'OK') {
      issues.add('Cannot create messages');
    }

    if (issues.isEmpty) {
      LoggerService.info('🎉 All tests passed! Chat should work correctly.');
    } else {
      LoggerService.warning('⚠️ Issues found:');
      for (final issue in issues) {
        LoggerService.warning('  - $issue');
      }
    }
  }
}
