import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/firebase_options.dart';
import 'package:three_pay_group_litigation_platform/src/core/app_widget.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart'; // Import PocketBaseService
import 'package:three_pay_group_litigation_platform/src/core/services/service_locator.dart'; // Import ServiceLocator
import 'package:three_pay_group_litigation_platform/src/core/services/local_notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/system_ui_service.dart';
// Firebase imports - currently disabled due to Xcode 16/iOS 18.5 compatibility issues
import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';
// import 'package:three_pay_group_litigation_platform/firebase_options.dart';

void main() async {
  // Make main async
  WidgetsFlutterBinding.ensureInitialized(); // Ensure bindings are initialized

  try {
    LoggerService.info('Starting 3Pay Global application...');

    // Initialize Firebase (currently disabled due to Xcode 16/iOS 18.5 compatibility issues)
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
    LoggerService.info('Firebase initialized successfully');

    // Initialize system UI service for edge-to-edge display
    await SystemUIService.initialize();
    LoggerService.info('System UI service initialized');

    // Initialize local notification service first
    await LocalNotificationService.initialize();
    LoggerService.info('Local notification service initialized');

    // It's important that PocketBaseService is initialized before other services that might depend on it.
    // The instance in ServiceLocator is separate for now. Consider unifying if PocketBaseService().init() returns the instance.
    await PocketBaseService().init();
    LoggerService.info('PocketBase service initialized');

    // Initialize app services (includes notification service)
    await ServiceLocator.initializeAppServices();
    LoggerService.info('App services initialized');

    await FirebaseApiService.initNotifications();
    LoggerService.info('Firebase API service initialized');

    LoggerService.info('Application initialization completed successfully');
  } catch (e) {
    LoggerService.error('Error during application initialization', e);
    // Continue with app startup even if some services fail
  }

  runApp(const ProviderScope(child: AppWidget()));
}
