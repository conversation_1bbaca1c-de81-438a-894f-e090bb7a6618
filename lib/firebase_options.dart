// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDfgZ9ClbwRDFKOBOFWM2WxkLx5aCnnlOo',
    appId: '1:630046180768:web:34c3825bee338df0e290c5',
    messagingSenderId: '630046180768',
    projectId: 'three-pay-global',
    authDomain: 'three-pay-global.firebaseapp.com',
    storageBucket: 'three-pay-global.firebasestorage.app',
    measurementId: 'G-SY8EJ6DD5P',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBn3UPRSKUMcG656ok6s6rgQef9Scos1gA',
    appId: '1:630046180768:android:7e381ec6bf6a1197e290c5',
    messagingSenderId: '630046180768',
    projectId: 'three-pay-global',
    storageBucket: 'three-pay-global.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDl1BFtD8i-a_frRAkEwRpINt1iJZRiYsM',
    appId: '1:630046180768:ios:3bba7045d193984fe290c5',
    messagingSenderId: '630046180768',
    projectId: 'three-pay-global',
    storageBucket: 'three-pay-global.firebasestorage.app',
    iosBundleId: 'com.iosthreepayglobal.app',
  );

}