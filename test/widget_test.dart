// Basic Flutter widget test for 3Pay Global app
// This test ensures the app builds correctly

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:three_pay_group_litigation_platform/src/core/app_widget.dart';

void main() {
  testWidgets('App builds without crashing', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const AppWidget());

    // Verify that the app builds successfully
    expect(find.byType(MaterialApp), findsOneWidget);
  });
}
