import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/edit_funding_application_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/services/claim_documents_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:pocketbase/pocketbase.dart';

// Generate mocks
@GenerateMocks([ClaimDocumentsService, PocketBaseService])
import 'document_management_ui_test.mocks.dart';

void main() {
  group('Document Management UI Tests', () {
    // Remove unused variable to fix linting warning
    // late MockClaimDocumentsService mockDocumentsService;
    late MockPocketBaseService mockPocketBaseService;

    setUp(() {
      // mockDocumentsService = MockClaimDocumentsService();
      mockPocketBaseService = MockPocketBaseService();

      // LoggerService doesn't have an initialize method in the current implementation
      // LoggerService.initialize(logLevel: LogLevel.debug);
    });

    group('Document Upload UI', () {
      testWidgets('should display upload button', (WidgetTester tester) async {
        // Arrange
        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => createMockClaimRecord());

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Documents'), findsOneWidget);
        expect(find.byIcon(Icons.upload_file), findsWidgets);
      });

      testWidgets('should show upload dialog when upload button is tapped', (
        WidgetTester tester,
      ) async {
        // Arrange
        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => createMockClaimRecord());

        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Act
        final uploadButton = find.byIcon(Icons.upload_file).first;
        await tester.tap(uploadButton);
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Upload Documents'), findsOneWidget);
        expect(find.text('Select Files'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
      });

      testWidgets('should validate file selection', (
        WidgetTester tester,
      ) async {
        // Arrange
        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => createMockClaimRecord());

        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Act - Open upload dialog
        final uploadButton = find.byIcon(Icons.upload_file).first;
        await tester.tap(uploadButton);
        await tester.pumpAndSettle();

        // Try to upload without selecting files
        final uploadDialogButton = find.text('Upload').last;
        await tester.tap(uploadDialogButton);
        await tester.pumpAndSettle();

        // Assert - Should show validation message
        expect(find.text('Please select at least one file'), findsOneWidget);
      });

      testWidgets('should show upload progress', (WidgetTester tester) async {
        // This test would verify upload progress indicators
        // For now, we'll test that the UI can handle loading states

        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => createMockClaimRecord());

        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Look for loading indicators
        expect(
          find.byType(CircularProgressIndicator),
          findsNothing,
        ); // Should not be loading initially
      });
    });

    group('Document List UI', () {
      testWidgets('should display document list', (WidgetTester tester) async {
        // Arrange
        final mockClaim = createMockClaimRecord();
        mockClaim.data['documentRepository'] = [
          {
            'logicalName': 'Contracts',
            'files': [
              {
                'id': 'doc-1',
                'filename': 'contract.pdf',
                'file_size': 1024,
                'uploaded_at': DateTime.now().toIso8601String(),
                'uploaded_by': 'user-123',
              },
            ],
          },
        ];

        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => mockClaim);

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Contracts'), findsOneWidget);
        expect(find.text('contract.pdf'), findsOneWidget);
      });

      testWidgets('should show empty state when no documents', (
        WidgetTester tester,
      ) async {
        // Arrange
        final mockClaim = createMockClaimRecord();
        mockClaim.data['documentRepository'] = [];

        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => mockClaim);

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('No documents uploaded yet'), findsOneWidget);
        expect(
          find.text('Upload your first document to get started'),
          findsOneWidget,
        );
      });

      testWidgets('should display document metadata', (
        WidgetTester tester,
      ) async {
        // Arrange
        final mockClaim = createMockClaimRecord();
        mockClaim.data['documentRepository'] = [
          {
            'logicalName': 'Evidence',
            'files': [
              {
                'id': 'doc-2',
                'filename': 'evidence.pdf',
                'file_size': 2048,
                'uploaded_at': DateTime.now().toIso8601String(),
                'uploaded_by': 'user-456',
                'comment': 'Important evidence document',
              },
            ],
          },
        ];

        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => mockClaim);

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('evidence.pdf'), findsOneWidget);
        expect(find.text('2.0 KB'), findsOneWidget);
        expect(find.text('Important evidence document'), findsOneWidget);
      });
    });

    group('Document Actions UI', () {
      testWidgets('should show document action buttons', (
        WidgetTester tester,
      ) async {
        // Arrange
        final mockClaim = createMockClaimRecord();
        mockClaim.data['documentRepository'] = [
          {
            'logicalName': 'Contracts',
            'files': [
              {
                'id': 'doc-1',
                'filename': 'contract.pdf',
                'file_size': 1024,
                'uploaded_at': DateTime.now().toIso8601String(),
                'uploaded_by': 'user-123',
              },
            ],
          },
        ];

        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => mockClaim);

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert
        expect(find.byIcon(Icons.visibility), findsWidgets); // Preview button
        expect(find.byIcon(Icons.download), findsWidgets); // Download button
        expect(find.byIcon(Icons.edit), findsWidgets); // Edit comment button
      });

      testWidgets('should handle document preview action', (
        WidgetTester tester,
      ) async {
        // Arrange
        final mockClaim = createMockClaimRecord();
        mockClaim.data['documentRepository'] = [
          {
            'logicalName': 'Contracts',
            'files': [
              {
                'id': 'doc-1',
                'filename': 'contract.pdf',
                'file_size': 1024,
                'uploaded_at': DateTime.now().toIso8601String(),
                'uploaded_by': 'user-123',
                'google_drive_id': 'google-drive-id-123',
              },
            ],
          },
        ];

        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => mockClaim);

        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Act
        final previewButton = find.byIcon(Icons.visibility).first;
        await tester.tap(previewButton);
        await tester.pumpAndSettle();

        // Assert - Should attempt to open preview
        // In a real test, this would verify the preview functionality
        expect(previewButton, findsOneWidget);
      });

      testWidgets('should handle document download action', (
        WidgetTester tester,
      ) async {
        // Arrange
        final mockClaim = createMockClaimRecord();
        mockClaim.data['documentRepository'] = [
          {
            'logicalName': 'Contracts',
            'files': [
              {
                'id': 'doc-1',
                'filename': 'contract.pdf',
                'file_size': 1024,
                'uploaded_at': DateTime.now().toIso8601String(),
                'uploaded_by': 'user-123',
                'google_drive_id': 'google-drive-id-123',
              },
            ],
          },
        ];

        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => mockClaim);

        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Act
        final downloadButton = find.byIcon(Icons.download).first;
        await tester.tap(downloadButton);
        await tester.pumpAndSettle();

        // Assert - Should attempt to download
        expect(downloadButton, findsOneWidget);
      });
    });

    group('Document Category Management UI', () {
      testWidgets('should show add category button', (
        WidgetTester tester,
      ) async {
        // Arrange
        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => createMockClaimRecord());

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Add Category'), findsOneWidget);
      });

      testWidgets('should show add category dialog', (
        WidgetTester tester,
      ) async {
        // Arrange
        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => createMockClaimRecord());

        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Act
        final addCategoryButton = find.text('Add Category');
        await tester.tap(addCategoryButton);
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Add Document Category'), findsOneWidget);
        expect(find.text('Category Name'), findsOneWidget);
        expect(find.text('Create'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
      });

      testWidgets('should validate category name input', (
        WidgetTester tester,
      ) async {
        // Arrange
        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => createMockClaimRecord());

        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Act - Open add category dialog
        final addCategoryButton = find.text('Add Category');
        await tester.tap(addCategoryButton);
        await tester.pumpAndSettle();

        // Try to create category without name
        final createButton = find.text('Create');
        await tester.tap(createButton);
        await tester.pumpAndSettle();

        // Assert - Should show validation error
        expect(find.text('Category name is required.'), findsOneWidget);
      });
    });

    group('Error Handling UI', () {
      testWidgets('should display error messages', (WidgetTester tester) async {
        // Arrange
        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenThrow(Exception('Network error'));

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert - Should show error state
        expect(find.text('Error loading claim details'), findsOneWidget);
      });

      testWidgets('should show retry option on error', (
        WidgetTester tester,
      ) async {
        // Arrange
        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenThrow(Exception('Network error'));

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Retry'), findsOneWidget);
      });
    });

    group('Responsive Design', () {
      testWidgets('should adapt to different screen sizes', (
        WidgetTester tester,
      ) async {
        // Arrange
        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => createMockClaimRecord());

        // Test mobile size
        await tester.binding.setSurfaceSize(Size(400, 800));
        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert mobile layout
        expect(find.text('Documents'), findsOneWidget);

        // Test tablet size
        await tester.binding.setSurfaceSize(Size(800, 1200));
        await tester.pumpAndSettle();

        // Assert tablet layout
        expect(find.text('Documents'), findsOneWidget);

        // Reset to default size
        await tester.binding.setSurfaceSize(null);
      });
    });

    group('Accessibility', () {
      testWidgets('should have proper accessibility labels', (
        WidgetTester tester,
      ) async {
        // Arrange
        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => createMockClaimRecord());

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert - Check for semantic labels
        expect(find.byType(Semantics), findsWidgets);
      });

      testWidgets('should support keyboard navigation', (
        WidgetTester tester,
      ) async {
        // This test would verify keyboard navigation support
        // For now, we'll test that focusable elements exist

        when(
          mockPocketBaseService.getOne(collectionName: any, recordId: any),
        ).thenAnswer((_) async => createMockClaimRecord());

        await tester.pumpWidget(
          MaterialApp(
            home: EditFundingApplicationPage(
              fundingApplicationId: 'test-claim-123',
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert - Check for focusable elements
        expect(find.byType(ElevatedButton), findsWidgets);
        expect(find.byType(IconButton), findsWidgets);
      });
    });
  });
}

// Helper functions
RecordModel createMockClaimRecord() {
  return RecordModel.fromJson({
    'id': 'test-claim-123',
    'collectionId': 'funding_applications',
    'collectionName': 'funding_applications',
    'claim_title': 'Test Claim',
    'case_summary_public': 'Test claim description',
    'stage': 'STAGE 1: PRE ACTION',
    'document_repository': [],
    'created': DateTime.now().toIso8601String(),
    'updated': DateTime.now().toIso8601String(),
  });
}
