// Mocks generated by Mocki<PERSON> 5.4.5 from annotations
// in three_pay_group_litigation_platform/test/ui/document_management_ui_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;
import 'dart:typed_data' as _i9;

import 'package:http/http.dart' as _i8;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;
import 'package:pocketbase/pocketbase.dart' as _i3;
import 'package:three_pay_group_litigation_platform/src/core/models/storage_type.dart'
    as _i2;
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart'
    as _i10;
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/models/funding_application_data.dart'
    as _i6;
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/services/claim_documents_service.dart'
    as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeStorageConfiguration_0 extends _i1.SmartFake
    implements _i2.StorageConfiguration {
  _FakeStorageConfiguration_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePocketBase_1 extends _i1.SmartFake implements _i3.PocketBase {
  _FakePocketBase_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAuthStore_2 extends _i1.SmartFake implements _i3.AuthStore {
  _FakeAuthStore_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRecordAuth_3 extends _i1.SmartFake implements _i3.RecordAuth {
  _FakeRecordAuth_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRecordModel_4 extends _i1.SmartFake implements _i3.RecordModel {
  _FakeRecordModel_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResultList_5<M extends _i3.Jsonable> extends _i1.SmartFake
    implements _i3.ResultList<M> {
  _FakeResultList_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ClaimDocumentsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockClaimDocumentsService extends _i1.Mock
    implements _i4.ClaimDocumentsService {
  MockClaimDocumentsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.StorageConfiguration get storageConfiguration =>
      (super.noSuchMethod(
            Invocation.getter(#storageConfiguration),
            returnValue: _FakeStorageConfiguration_0(
              this,
              Invocation.getter(#storageConfiguration),
            ),
          )
          as _i2.StorageConfiguration);

  @override
  _i5.Future<List<_i6.UploadedDocumentCategory>>
  getDocumentsForFundingApplication(String? fundingApplicationId) =>
      (super.noSuchMethod(
            Invocation.method(#getDocumentsForFundingApplication, [
              fundingApplicationId,
            ]),
            returnValue: _i5.Future<List<_i6.UploadedDocumentCategory>>.value(
              <_i6.UploadedDocumentCategory>[],
            ),
          )
          as _i5.Future<List<_i6.UploadedDocumentCategory>>);

  @override
  _i5.Future<String> createDocumentCategory({
    required String? fundingApplicationId,
    required String? logicalName,
    required String? currentVersionFileId,
    required List<_i6.DocumentVersion>? versions,
    String? uploadedBy,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createDocumentCategory, [], {
              #fundingApplicationId: fundingApplicationId,
              #logicalName: logicalName,
              #currentVersionFileId: currentVersionFileId,
              #versions: versions,
              #uploadedBy: uploadedBy,
            }),
            returnValue: _i5.Future<String>.value(
              _i7.dummyValue<String>(
                this,
                Invocation.method(#createDocumentCategory, [], {
                  #fundingApplicationId: fundingApplicationId,
                  #logicalName: logicalName,
                  #currentVersionFileId: currentVersionFileId,
                  #versions: versions,
                  #uploadedBy: uploadedBy,
                }),
              ),
            ),
          )
          as _i5.Future<String>);

  @override
  _i5.Future<void> updateDocumentCategory({
    required String? fundingApplicationId,
    required String? logicalName,
    required String? newCurrentVersionFileId,
    required List<_i6.DocumentVersion>? updatedVersions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateDocumentCategory, [], {
              #fundingApplicationId: fundingApplicationId,
              #logicalName: logicalName,
              #newCurrentVersionFileId: newCurrentVersionFileId,
              #updatedVersions: updatedVersions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> addVersionToCategory({
    required String? fundingApplicationId,
    required String? logicalName,
    required _i6.DocumentVersion? newVersion,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#addVersionToCategory, [], {
              #fundingApplicationId: fundingApplicationId,
              #logicalName: logicalName,
              #newVersion: newVersion,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<bool> categoryExists({
    required String? fundingApplicationId,
    required String? logicalName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#categoryExists, [], {
              #fundingApplicationId: fundingApplicationId,
              #logicalName: logicalName,
            }),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<void> migrateDocumentRepository({
    required String? fundingApplicationId,
    required List<_i6.UploadedDocumentCategory>? documentRepository,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#migrateDocumentRepository, [], {
              #fundingApplicationId: fundingApplicationId,
              #documentRepository: documentRepository,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> updateVersionComment({
    required String? fundingApplicationId,
    required String? logicalName,
    required String? versionFileId,
    required String? newComment,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateVersionComment, [], {
              #fundingApplicationId: fundingApplicationId,
              #logicalName: logicalName,
              #versionFileId: versionFileId,
              #newComment: newComment,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> deleteDocumentCategory({
    required String? fundingApplicationId,
    required String? logicalName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#deleteDocumentCategory, [], {
              #fundingApplicationId: fundingApplicationId,
              #logicalName: logicalName,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<String> uploadFilesAndCreateCategory({
    required String? fundingApplicationId,
    required String? logicalName,
    required List<_i8.MultipartFile>? files,
    required String? uploadedBy,
    String? comment,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadFilesAndCreateCategory, [], {
              #fundingApplicationId: fundingApplicationId,
              #logicalName: logicalName,
              #files: files,
              #uploadedBy: uploadedBy,
              #comment: comment,
            }),
            returnValue: _i5.Future<String>.value(
              _i7.dummyValue<String>(
                this,
                Invocation.method(#uploadFilesAndCreateCategory, [], {
                  #fundingApplicationId: fundingApplicationId,
                  #logicalName: logicalName,
                  #files: files,
                  #uploadedBy: uploadedBy,
                  #comment: comment,
                }),
              ),
            ),
          )
          as _i5.Future<String>);

  @override
  _i5.Future<String> uploadFileAndCreateCategory({
    required String? fundingApplicationId,
    required String? logicalName,
    required _i8.MultipartFile? file,
    required String? uploadedBy,
    String? comment,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadFileAndCreateCategory, [], {
              #fundingApplicationId: fundingApplicationId,
              #logicalName: logicalName,
              #file: file,
              #uploadedBy: uploadedBy,
              #comment: comment,
            }),
            returnValue: _i5.Future<String>.value(
              _i7.dummyValue<String>(
                this,
                Invocation.method(#uploadFileAndCreateCategory, [], {
                  #fundingApplicationId: fundingApplicationId,
                  #logicalName: logicalName,
                  #file: file,
                  #uploadedBy: uploadedBy,
                  #comment: comment,
                }),
              ),
            ),
          )
          as _i5.Future<String>);

  @override
  _i5.Future<String> uploadFileAndAddVersion({
    required String? fundingApplicationId,
    required String? logicalName,
    required _i8.MultipartFile? file,
    required String? uploadedBy,
    String? comment,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadFileAndAddVersion, [], {
              #fundingApplicationId: fundingApplicationId,
              #logicalName: logicalName,
              #file: file,
              #uploadedBy: uploadedBy,
              #comment: comment,
            }),
            returnValue: _i5.Future<String>.value(
              _i7.dummyValue<String>(
                this,
                Invocation.method(#uploadFileAndAddVersion, [], {
                  #fundingApplicationId: fundingApplicationId,
                  #logicalName: logicalName,
                  #file: file,
                  #uploadedBy: uploadedBy,
                  #comment: comment,
                }),
              ),
            ),
          )
          as _i5.Future<String>);

  @override
  _i5.Future<String> getFileUrl(String? versionFileId) =>
      (super.noSuchMethod(
            Invocation.method(#getFileUrl, [versionFileId]),
            returnValue: _i5.Future<String>.value(
              _i7.dummyValue<String>(
                this,
                Invocation.method(#getFileUrl, [versionFileId]),
              ),
            ),
          )
          as _i5.Future<String>);

  @override
  _i5.Future<void> debugClaimDocumentsCollection(
    String? fundingApplicationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#debugClaimDocumentsCollection, [
              fundingApplicationId,
            ]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> checkForDuplicateLogicalNames(
    String? fundingApplicationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#checkForDuplicateLogicalNames, [
              fundingApplicationId,
            ]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> fixFileArrayData(String? fundingApplicationId) =>
      (super.noSuchMethod(
            Invocation.method(#fixFileArrayData, [fundingApplicationId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> checkCollectionSchema() =>
      (super.noSuchMethod(
            Invocation.method(#checkCollectionSchema, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> inspectRecord(String? recordId) =>
      (super.noSuchMethod(
            Invocation.method(#inspectRecord, [recordId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> fixFilenameDuplication(String? recordId) =>
      (super.noSuchMethod(
            Invocation.method(#fixFilenameDuplication, [recordId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> debugProblematicRecord() =>
      (super.noSuchMethod(
            Invocation.method(#debugProblematicRecord, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<String?> testMultipleFileUpload({
    required String? fundingApplicationId,
    required String? uploadedBy,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#testMultipleFileUpload, [], {
              #fundingApplicationId: fundingApplicationId,
              #uploadedBy: uploadedBy,
            }),
            returnValue: _i5.Future<String?>.value(),
          )
          as _i5.Future<String?>);

  @override
  _i5.Future<void> invalidateDocumentCache(String? documentId) =>
      (super.noSuchMethod(
            Invocation.method(#invalidateDocumentCache, [documentId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>?> getCachedMetadata(String? versionFileId) =>
      (super.noSuchMethod(
            Invocation.method(#getCachedMetadata, [versionFileId]),
            returnValue: _i5.Future<Map<String, dynamic>?>.value(),
          )
          as _i5.Future<Map<String, dynamic>?>);

  @override
  _i5.Future<void> cacheMetadata(
    String? versionFileId,
    Map<String, dynamic>? metadata,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cacheMetadata, [versionFileId, metadata]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>> getCacheStatistics() =>
      (super.noSuchMethod(
            Invocation.method(#getCacheStatistics, []),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<void> clearDocumentCache() =>
      (super.noSuchMethod(
            Invocation.method(#clearDocumentCache, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> preloadCache(List<String>? documentIds) =>
      (super.noSuchMethod(
            Invocation.method(#preloadCache, [documentIds]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<Map<String, String>> batchUploadFiles({
    required String? fundingApplicationId,
    required Map<String, List<_i8.MultipartFile>>? categoryFiles,
    required String? uploadedBy,
    Map<String, String>? comments,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#batchUploadFiles, [], {
              #fundingApplicationId: fundingApplicationId,
              #categoryFiles: categoryFiles,
              #uploadedBy: uploadedBy,
              #comments: comments,
            }),
            returnValue: _i5.Future<Map<String, String>>.value(
              <String, String>{},
            ),
          )
          as _i5.Future<Map<String, String>>);

  @override
  _i5.Future<Map<String, dynamic>> checkServiceHealth() =>
      (super.noSuchMethod(
            Invocation.method(#checkServiceHealth, []),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<List<String>> batchGetFileUrls(List<String>? versionFileIds) =>
      (super.noSuchMethod(
            Invocation.method(#batchGetFileUrls, [versionFileIds]),
            returnValue: _i5.Future<List<String>>.value(<String>[]),
          )
          as _i5.Future<List<String>>);

  @override
  _i5.Future<Map<String, dynamic>> getPerformanceMetrics() =>
      (super.noSuchMethod(
            Invocation.method(#getPerformanceMetrics, []),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<_i9.Uint8List> downloadFile(
    String? versionFileId, {
    void Function(int, int)? onProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #downloadFile,
              [versionFileId],
              {#onProgress: onProgress},
            ),
            returnValue: _i5.Future<_i9.Uint8List>.value(_i9.Uint8List(0)),
          )
          as _i5.Future<_i9.Uint8List>);

  @override
  _i5.Future<List<_i6.UploadedDocumentCategory>> searchDocuments({
    required String? fundingApplicationId,
    String? searchQuery,
    List<String>? logicalNames,
    DateTime? uploadedAfter,
    DateTime? uploadedBefore,
    String? uploadedBy,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#searchDocuments, [], {
              #fundingApplicationId: fundingApplicationId,
              #searchQuery: searchQuery,
              #logicalNames: logicalNames,
              #uploadedAfter: uploadedAfter,
              #uploadedBefore: uploadedBefore,
              #uploadedBy: uploadedBy,
            }),
            returnValue: _i5.Future<List<_i6.UploadedDocumentCategory>>.value(
              <_i6.UploadedDocumentCategory>[],
            ),
          )
          as _i5.Future<List<_i6.UploadedDocumentCategory>>);

  @override
  _i5.Future<Map<String, dynamic>> getDocumentStatistics(
    String? fundingApplicationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getDocumentStatistics, [fundingApplicationId]),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);
}

/// A class which mocks [PocketBaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPocketBaseService extends _i1.Mock implements _i10.PocketBaseService {
  MockPocketBaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.PocketBase get client =>
      (super.noSuchMethod(
            Invocation.getter(#client),
            returnValue: _FakePocketBase_1(this, Invocation.getter(#client)),
          )
          as _i3.PocketBase);

  @override
  _i3.PocketBase get pb =>
      (super.noSuchMethod(
            Invocation.getter(#pb),
            returnValue: _FakePocketBase_1(this, Invocation.getter(#pb)),
          )
          as _i3.PocketBase);

  @override
  _i3.AuthStore get authStore =>
      (super.noSuchMethod(
            Invocation.getter(#authStore),
            returnValue: _FakeAuthStore_2(this, Invocation.getter(#authStore)),
          )
          as _i3.AuthStore);

  @override
  bool get isSignedIn =>
      (super.noSuchMethod(Invocation.getter(#isSignedIn), returnValue: false)
          as bool);

  @override
  _i5.Future<void> init() =>
      (super.noSuchMethod(
            Invocation.method(#init, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i3.RecordAuth> signIn(String? email, String? password) =>
      (super.noSuchMethod(
            Invocation.method(#signIn, [email, password]),
            returnValue: _i5.Future<_i3.RecordAuth>.value(
              _FakeRecordAuth_3(
                this,
                Invocation.method(#signIn, [email, password]),
              ),
            ),
          )
          as _i5.Future<_i3.RecordAuth>);

  @override
  _i5.Future<_i3.RecordModel> signUp({
    required String? email,
    required String? password,
    required String? passwordConfirm,
    required String? userType,
    required String? name,
    String? firstName,
    String? lastName,
    String? level,
    String? status,
    Map<String, dynamic>? customData = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUp, [], {
              #email: email,
              #password: password,
              #passwordConfirm: passwordConfirm,
              #userType: userType,
              #name: name,
              #firstName: firstName,
              #lastName: lastName,
              #level: level,
              #status: status,
              #customData: customData,
            }),
            returnValue: _i5.Future<_i3.RecordModel>.value(
              _FakeRecordModel_4(
                this,
                Invocation.method(#signUp, [], {
                  #email: email,
                  #password: password,
                  #passwordConfirm: passwordConfirm,
                  #userType: userType,
                  #name: name,
                  #firstName: firstName,
                  #lastName: lastName,
                  #level: level,
                  #status: status,
                  #customData: customData,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.RecordModel>);

  @override
  _i5.Future<void> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> handleSessionExpiration() =>
      (super.noSuchMethod(
            Invocation.method(#handleSessionExpiration, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> refreshFCMToken() =>
      (super.noSuchMethod(
            Invocation.method(#refreshFCMToken, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> requestPasswordReset(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#requestPasswordReset, [email]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> confirmPasswordReset({
    required String? token,
    required String? password,
    required String? passwordConfirm,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#confirmPasswordReset, [], {
              #token: token,
              #password: password,
              #passwordConfirm: passwordConfirm,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> requestEmailVerification(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#requestEmailVerification, [email]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> confirmEmailVerification(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#confirmEmailVerification, [token]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> changePassword({
    required String? oldPassword,
    required String? newPassword,
    required String? newPasswordConfirm,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#changePassword, [], {
              #oldPassword: oldPassword,
              #newPassword: newPassword,
              #newPasswordConfirm: newPasswordConfirm,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> requestOTP(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#requestOTP, [email]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<bool> verifyOTP(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#verifyOTP, [token]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<void> optOutAccount() =>
      (super.noSuchMethod(
            Invocation.method(#optOutAccount, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i3.RecordModel> createSolicitorProfile({
    required String? userId,
    required String? lawFirmName,
    required String? solicitorName,
    required String? position,
    required String? contactNumber,
    required String? firmAddress,
    required String? firmRegistrationNumber,
    String? puStatus = 'pending',
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createSolicitorProfile, [], {
              #userId: userId,
              #lawFirmName: lawFirmName,
              #solicitorName: solicitorName,
              #position: position,
              #contactNumber: contactNumber,
              #firmAddress: firmAddress,
              #firmRegistrationNumber: firmRegistrationNumber,
              #puStatus: puStatus,
            }),
            returnValue: _i5.Future<_i3.RecordModel>.value(
              _FakeRecordModel_4(
                this,
                Invocation.method(#createSolicitorProfile, [], {
                  #userId: userId,
                  #lawFirmName: lawFirmName,
                  #solicitorName: solicitorName,
                  #position: position,
                  #contactNumber: contactNumber,
                  #firmAddress: firmAddress,
                  #firmRegistrationNumber: firmRegistrationNumber,
                  #puStatus: puStatus,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.RecordModel>);

  @override
  _i5.Future<_i3.RecordModel> createCoFunderProfile({
    required String? userId,
    int? currentLevel = 1,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createCoFunderProfile, [], {
              #userId: userId,
              #currentLevel: currentLevel,
            }),
            returnValue: _i5.Future<_i3.RecordModel>.value(
              _FakeRecordModel_4(
                this,
                Invocation.method(#createCoFunderProfile, [], {
                  #userId: userId,
                  #currentLevel: currentLevel,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.RecordModel>);

  @override
  _i5.Future<_i3.RecordModel> createRecord({
    required String? collectionName,
    required Map<String, dynamic>? data,
    Map<String, dynamic>? query = const {},
    List<_i8.MultipartFile>? files = const [],
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createRecord, [], {
              #collectionName: collectionName,
              #data: data,
              #query: query,
              #files: files,
            }),
            returnValue: _i5.Future<_i3.RecordModel>.value(
              _FakeRecordModel_4(
                this,
                Invocation.method(#createRecord, [], {
                  #collectionName: collectionName,
                  #data: data,
                  #query: query,
                  #files: files,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.RecordModel>);

  @override
  _i5.Future<_i3.RecordModel> updateRecord({
    required String? collectionName,
    required String? recordId,
    required Map<String, dynamic>? data,
    Map<String, dynamic>? query = const {},
    List<_i8.MultipartFile>? files = const [],
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateRecord, [], {
              #collectionName: collectionName,
              #recordId: recordId,
              #data: data,
              #query: query,
              #files: files,
            }),
            returnValue: _i5.Future<_i3.RecordModel>.value(
              _FakeRecordModel_4(
                this,
                Invocation.method(#updateRecord, [], {
                  #collectionName: collectionName,
                  #recordId: recordId,
                  #data: data,
                  #query: query,
                  #files: files,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.RecordModel>);

  @override
  _i5.Future<List<_i3.RecordModel>> getFullList({
    required String? collectionName,
    int? batch = 200,
    String? filter,
    String? sort,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getFullList, [], {
              #collectionName: collectionName,
              #batch: batch,
              #filter: filter,
              #sort: sort,
            }),
            returnValue: _i5.Future<List<_i3.RecordModel>>.value(
              <_i3.RecordModel>[],
            ),
          )
          as _i5.Future<List<_i3.RecordModel>>);

  @override
  _i5.Future<_i3.ResultList<_i3.RecordModel>> getList({
    required String? collectionName,
    int? page = 1,
    int? perPage = 30,
    String? filter,
    String? sort,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getList, [], {
              #collectionName: collectionName,
              #page: page,
              #perPage: perPage,
              #filter: filter,
              #sort: sort,
            }),
            returnValue: _i5.Future<_i3.ResultList<_i3.RecordModel>>.value(
              _FakeResultList_5<_i3.RecordModel>(
                this,
                Invocation.method(#getList, [], {
                  #collectionName: collectionName,
                  #page: page,
                  #perPage: perPage,
                  #filter: filter,
                  #sort: sort,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.ResultList<_i3.RecordModel>>);

  @override
  _i5.Future<_i3.RecordModel> getOne({
    required String? collectionName,
    required String? recordId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getOne, [], {
              #collectionName: collectionName,
              #recordId: recordId,
            }),
            returnValue: _i5.Future<_i3.RecordModel>.value(
              _FakeRecordModel_4(
                this,
                Invocation.method(#getOne, [], {
                  #collectionName: collectionName,
                  #recordId: recordId,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.RecordModel>);

  @override
  _i5.Future<void> deleteRecord({
    required String? collectionName,
    required String? recordId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#deleteRecord, [], {
              #collectionName: collectionName,
              #recordId: recordId,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<Map<String, String>> uploadFileAndGetId({
    required String? targetCollectionName,
    required _i8.MultipartFile? multipartFile,
    Map<String, String>? body = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadFileAndGetId, [], {
              #targetCollectionName: targetCollectionName,
              #multipartFile: multipartFile,
              #body: body,
            }),
            returnValue: _i5.Future<Map<String, String>>.value(
              <String, String>{},
            ),
          )
          as _i5.Future<Map<String, String>>);
}
