# 3Pay Global Notification System Testing

This directory contains comprehensive tests for the notification system authentication integration as specified in Task 10 of the notifications feature implementation.

## Test Structure

### Authentication Integration Tests
- **File**: `test/src/core/services/notification_auth_integration_test.dart`
- **Purpose**: Tests the integration between the notification system and authentication
- **Coverage**: Service status, payload handling, authentication state validation, error handling

### Test Utilities
- **File**: `test/utils/notification_test_utils.dart`
- **Purpose**: Utility functions for creating mock data and test helpers
- **Features**: Mock notification creation, test data generation, validation helpers

## Testing Strategy

### 1. Authentication Integration Testing

The notification system must properly integrate with user authentication:

#### FCM Token Management
- **Login Flow**: FCM tokens should be generated and stored when users sign in
- **Logout Flow**: FCM tokens should be cleared when users sign out
- **User Switching**: Tokens should be updated when switching between user accounts
- **Session Expiration**: Tokens should be cleared when sessions expire

#### User Type Support
- **Claimants**: Notifications for claim updates, messages, documents
- **Solicitors**: Notifications for claim management, funding opportunities
- **Co-funders**: Notifications for investment opportunities, funding updates
- **Admins**: System-wide notifications and alerts

### 2. Notification Flow Testing

#### End-to-End Flow
1. **Creation**: Notification created in PocketBase
2. **Real-time**: Real-time subscription triggers notification
3. **Display**: Local notification shown to user
4. **Interaction**: User taps notification
5. **Navigation**: App navigates to appropriate page

#### App State Handling
- **Foreground**: Show local notification overlay
- **Background**: Show system notification, handle tap
- **Terminated**: Launch app from notification

### 3. Error Handling and Resilience

#### Network Issues
- **Poor Connectivity**: Graceful degradation of notification delivery
- **Offline Mode**: Queue notifications for when connection restored
- **Timeout Handling**: Retry mechanisms for failed operations

#### Permission Issues
- **Denied Permissions**: Fallback notification methods
- **Permission Changes**: Handle runtime permission changes
- **Settings Navigation**: Guide users to enable permissions

#### Service Failures
- **Firebase Unavailable**: Continue with local notifications only
- **PocketBase Errors**: Handle database connection issues
- **Token Refresh Failures**: Retry token operations

### 4. Performance and Load Testing

#### High Volume Scenarios
- **Multiple Notifications**: Handle burst of notifications
- **Concurrent Users**: Multiple users receiving notifications simultaneously
- **Memory Management**: Prevent memory leaks in notification services

#### Long-Running Operations
- **Service Stability**: Notification services remain stable over time
- **Token Refresh**: Periodic token refresh operations
- **Subscription Management**: Real-time subscription cleanup

## Test Categories

### Unit Tests
- Individual service method testing
- Payload creation and parsing
- Error handling scenarios
- Configuration validation

### Integration Tests
- Service interaction testing
- Authentication flow integration
- Real-time subscription testing
- Navigation integration

### Widget Tests
- Notification permission dialogs
- Settings UI components
- Notification display widgets
- User interaction handling

### End-to-End Tests
- Complete notification workflows
- Cross-platform consistency
- User journey validation
- Performance benchmarking

## Running Tests

### All Tests
```bash
flutter test
```

### Specific Test Files
```bash
flutter test test/src/core/services/notification_auth_integration_test.dart
```

### Test Coverage
```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

## Test Data and Mocking

### Mock Services
- **PocketBase**: Mock database operations
- **Firebase**: Mock FCM operations
- **Local Notifications**: Mock notification display

### Test Data
- **Users**: Different user types and states
- **Notifications**: Various notification types and content
- **Tokens**: Mock FCM tokens and authentication data

## Platform-Specific Testing

### Android Testing
- **Notification Channels**: Proper channel configuration
- **Background Restrictions**: Handle battery optimization
- **Permission Models**: Different Android versions

### iOS Testing
- **Notification Categories**: Proper category setup
- **Background App Refresh**: Handle iOS background limitations
- **Critical Alerts**: Test critical notification handling

## Continuous Integration

### Automated Testing
- **Pull Request Checks**: Run tests on every PR
- **Nightly Builds**: Comprehensive test suite execution
- **Performance Monitoring**: Track test execution times

### Test Reporting
- **Coverage Reports**: Maintain high test coverage
- **Failure Analysis**: Detailed failure reporting
- **Performance Metrics**: Track notification delivery times

## Best Practices

### Test Organization
- **Clear Naming**: Descriptive test names and groups
- **Setup/Teardown**: Proper test isolation
- **Documentation**: Well-documented test purposes

### Mock Management
- **Realistic Mocks**: Mocks that reflect real service behavior
- **State Management**: Proper mock state cleanup
- **Error Simulation**: Test error scenarios thoroughly

### Assertion Quality
- **Specific Assertions**: Test exact expected behavior
- **Error Messages**: Clear failure messages
- **Edge Cases**: Test boundary conditions

## Debugging Tests

### Common Issues
- **Async Operations**: Proper async/await handling
- **State Pollution**: Tests affecting each other
- **Mock Configuration**: Incorrect mock setup

### Debugging Tools
- **Test Logs**: Use LoggerService for test debugging
- **Breakpoints**: Debug test execution flow
- **Mock Verification**: Verify mock interactions

## Future Enhancements

### Additional Test Types
- **Visual Regression**: UI consistency testing
- **Accessibility**: Screen reader compatibility
- **Localization**: Multi-language notification testing

### Advanced Scenarios
- **Multi-Device**: Cross-device notification sync
- **Offline-First**: Comprehensive offline testing
- **Security**: Notification data encryption testing

## Contributing

When adding new notification features:

1. **Write Tests First**: TDD approach for new functionality
2. **Update Documentation**: Keep test documentation current
3. **Maintain Coverage**: Ensure new code is properly tested
4. **Review Integration**: Consider authentication integration impacts

## Support

For questions about the testing setup or specific test failures:

1. **Check Logs**: Review test execution logs
2. **Verify Setup**: Ensure proper test environment configuration
3. **Documentation**: Refer to this documentation and inline comments
4. **Team Review**: Discuss complex testing scenarios with the team
