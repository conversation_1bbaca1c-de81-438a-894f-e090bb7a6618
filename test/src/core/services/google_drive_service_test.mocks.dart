// Mocks generated by Mockito 5.4.5 from annotations
// in three_pay_group_litigation_platform/test/src/core/services/google_drive_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;
import 'dart:convert' as _i10;
import 'dart:io' as _i5;
import 'dart:typed_data' as _i11;

import 'package:http/http.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;
import 'package:pocketbase/pocketbase.dart' as _i4;
import 'package:three_pay_group_litigation_platform/src/core/services/google_drive_auth.dart'
    as _i6;
import 'package:three_pay_group_litigation_platform/src/core/services/google_drive_config.dart'
    as _i3;
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart'
    as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeClient_0 extends _i1.SmartFake implements _i2.Client {
  _FakeClient_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoogleDriveConfig_1 extends _i1.SmartFake
    implements _i3.GoogleDriveConfig {
  _FakeGoogleDriveConfig_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePocketBase_2 extends _i1.SmartFake implements _i4.PocketBase {
  _FakePocketBase_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAuthStore_3 extends _i1.SmartFake implements _i4.AuthStore {
  _FakeAuthStore_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRecordAuth_4 extends _i1.SmartFake implements _i4.RecordAuth {
  _FakeRecordAuth_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRecordModel_5 extends _i1.SmartFake implements _i4.RecordModel {
  _FakeRecordModel_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResultList_6<M extends _i4.Jsonable> extends _i1.SmartFake
    implements _i4.ResultList<M> {
  _FakeResultList_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFile_7 extends _i1.SmartFake implements _i5.File {
  _FakeFile_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUri_8 extends _i1.SmartFake implements Uri {
  _FakeUri_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDirectory_9 extends _i1.SmartFake implements _i5.Directory {
  _FakeDirectory_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFileSystemEntity_10 extends _i1.SmartFake
    implements _i5.FileSystemEntity {
  _FakeFileSystemEntity_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDateTime_11 extends _i1.SmartFake implements DateTime {
  _FakeDateTime_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRandomAccessFile_12 extends _i1.SmartFake
    implements _i5.RandomAccessFile {
  _FakeRandomAccessFile_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIOSink_13 extends _i1.SmartFake implements _i5.IOSink {
  _FakeIOSink_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFileStat_14 extends _i1.SmartFake implements _i5.FileStat {
  _FakeFileStat_14(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GoogleDriveAuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoogleDriveAuthService extends _i1.Mock
    implements _i6.GoogleDriveAuthService {
  MockGoogleDriveAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  bool get isValid =>
      (super.noSuchMethod(Invocation.getter(#isValid), returnValue: false)
          as bool);

  @override
  _i7.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i2.Client> getAuthenticatedClient() =>
      (super.noSuchMethod(
            Invocation.method(#getAuthenticatedClient, []),
            returnValue: _i7.Future<_i2.Client>.value(
              _FakeClient_0(
                this,
                Invocation.method(#getAuthenticatedClient, []),
              ),
            ),
          )
          as _i7.Future<_i2.Client>);

  @override
  _i7.Future<bool> validateAuthentication() =>
      (super.noSuchMethod(
            Invocation.method(#validateAuthentication, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<Map<String, dynamic>?> getCurrentUserInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentUserInfo, []),
            returnValue: _i7.Future<Map<String, dynamic>?>.value(),
          )
          as _i7.Future<Map<String, dynamic>?>);

  @override
  _i7.Future<void> refreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#refreshToken, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void close() => super.noSuchMethod(
    Invocation.method(#close, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [GoogleDriveConfigService].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoogleDriveConfigService extends _i1.Mock
    implements _i3.GoogleDriveConfigService {
  MockGoogleDriveConfigService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<_i3.GoogleDriveConfig> loadConfig({String? environment}) =>
      (super.noSuchMethod(
            Invocation.method(#loadConfig, [], {#environment: environment}),
            returnValue: _i7.Future<_i3.GoogleDriveConfig>.value(
              _FakeGoogleDriveConfig_1(
                this,
                Invocation.method(#loadConfig, [], {#environment: environment}),
              ),
            ),
          )
          as _i7.Future<_i3.GoogleDriveConfig>);

  @override
  _i7.Future<void> updateQuotaUsage(int? newUsage) =>
      (super.noSuchMethod(
            Invocation.method(#updateQuotaUsage, [newUsage]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> resetQuota() =>
      (super.noSuchMethod(
            Invocation.method(#resetQuota, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<bool> isQuotaResetNeeded() =>
      (super.noSuchMethod(
            Invocation.method(#isQuotaResetNeeded, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  void clearCache() => super.noSuchMethod(
    Invocation.method(#clearCache, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [PocketBaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPocketBaseService extends _i1.Mock implements _i8.PocketBaseService {
  MockPocketBaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.PocketBase get client =>
      (super.noSuchMethod(
            Invocation.getter(#client),
            returnValue: _FakePocketBase_2(this, Invocation.getter(#client)),
          )
          as _i4.PocketBase);

  @override
  _i4.PocketBase get pb =>
      (super.noSuchMethod(
            Invocation.getter(#pb),
            returnValue: _FakePocketBase_2(this, Invocation.getter(#pb)),
          )
          as _i4.PocketBase);

  @override
  _i4.AuthStore get authStore =>
      (super.noSuchMethod(
            Invocation.getter(#authStore),
            returnValue: _FakeAuthStore_3(this, Invocation.getter(#authStore)),
          )
          as _i4.AuthStore);

  @override
  bool get isSignedIn =>
      (super.noSuchMethod(Invocation.getter(#isSignedIn), returnValue: false)
          as bool);

  @override
  _i7.Future<void> init() =>
      (super.noSuchMethod(
            Invocation.method(#init, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i4.RecordAuth> signIn(String? email, String? password) =>
      (super.noSuchMethod(
            Invocation.method(#signIn, [email, password]),
            returnValue: _i7.Future<_i4.RecordAuth>.value(
              _FakeRecordAuth_4(
                this,
                Invocation.method(#signIn, [email, password]),
              ),
            ),
          )
          as _i7.Future<_i4.RecordAuth>);

  @override
  _i7.Future<_i4.RecordModel> signUp({
    required String? email,
    required String? password,
    required String? passwordConfirm,
    required String? userType,
    required String? name,
    String? firstName,
    String? lastName,
    String? level,
    String? status,
    Map<String, dynamic>? customData = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUp, [], {
              #email: email,
              #password: password,
              #passwordConfirm: passwordConfirm,
              #userType: userType,
              #name: name,
              #firstName: firstName,
              #lastName: lastName,
              #level: level,
              #status: status,
              #customData: customData,
            }),
            returnValue: _i7.Future<_i4.RecordModel>.value(
              _FakeRecordModel_5(
                this,
                Invocation.method(#signUp, [], {
                  #email: email,
                  #password: password,
                  #passwordConfirm: passwordConfirm,
                  #userType: userType,
                  #name: name,
                  #firstName: firstName,
                  #lastName: lastName,
                  #level: level,
                  #status: status,
                  #customData: customData,
                }),
              ),
            ),
          )
          as _i7.Future<_i4.RecordModel>);

  @override
  _i7.Future<void> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> handleSessionExpiration() =>
      (super.noSuchMethod(
            Invocation.method(#handleSessionExpiration, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> refreshFCMToken() =>
      (super.noSuchMethod(
            Invocation.method(#refreshFCMToken, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> requestPasswordReset(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#requestPasswordReset, [email]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> confirmPasswordReset({
    required String? token,
    required String? password,
    required String? passwordConfirm,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#confirmPasswordReset, [], {
              #token: token,
              #password: password,
              #passwordConfirm: passwordConfirm,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> requestEmailVerification(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#requestEmailVerification, [email]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> confirmEmailVerification(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#confirmEmailVerification, [token]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> changePassword({
    required String? oldPassword,
    required String? newPassword,
    required String? newPasswordConfirm,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#changePassword, [], {
              #oldPassword: oldPassword,
              #newPassword: newPassword,
              #newPasswordConfirm: newPasswordConfirm,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> requestOTP(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#requestOTP, [email]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<bool> verifyOTP(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#verifyOTP, [token]),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<void> optOutAccount() =>
      (super.noSuchMethod(
            Invocation.method(#optOutAccount, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i4.RecordModel> createSolicitorProfile({
    required String? userId,
    required String? lawFirmName,
    required String? solicitorName,
    required String? position,
    required String? contactNumber,
    required String? firmAddress,
    required String? firmRegistrationNumber,
    String? puStatus = 'pending',
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createSolicitorProfile, [], {
              #userId: userId,
              #lawFirmName: lawFirmName,
              #solicitorName: solicitorName,
              #position: position,
              #contactNumber: contactNumber,
              #firmAddress: firmAddress,
              #firmRegistrationNumber: firmRegistrationNumber,
              #puStatus: puStatus,
            }),
            returnValue: _i7.Future<_i4.RecordModel>.value(
              _FakeRecordModel_5(
                this,
                Invocation.method(#createSolicitorProfile, [], {
                  #userId: userId,
                  #lawFirmName: lawFirmName,
                  #solicitorName: solicitorName,
                  #position: position,
                  #contactNumber: contactNumber,
                  #firmAddress: firmAddress,
                  #firmRegistrationNumber: firmRegistrationNumber,
                  #puStatus: puStatus,
                }),
              ),
            ),
          )
          as _i7.Future<_i4.RecordModel>);

  @override
  _i7.Future<_i4.RecordModel> createCoFunderProfile({
    required String? userId,
    int? currentLevel = 1,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createCoFunderProfile, [], {
              #userId: userId,
              #currentLevel: currentLevel,
            }),
            returnValue: _i7.Future<_i4.RecordModel>.value(
              _FakeRecordModel_5(
                this,
                Invocation.method(#createCoFunderProfile, [], {
                  #userId: userId,
                  #currentLevel: currentLevel,
                }),
              ),
            ),
          )
          as _i7.Future<_i4.RecordModel>);

  @override
  _i7.Future<_i4.RecordModel> createRecord({
    required String? collectionName,
    required Map<String, dynamic>? data,
    Map<String, dynamic>? query = const {},
    List<_i2.MultipartFile>? files = const [],
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createRecord, [], {
              #collectionName: collectionName,
              #data: data,
              #query: query,
              #files: files,
            }),
            returnValue: _i7.Future<_i4.RecordModel>.value(
              _FakeRecordModel_5(
                this,
                Invocation.method(#createRecord, [], {
                  #collectionName: collectionName,
                  #data: data,
                  #query: query,
                  #files: files,
                }),
              ),
            ),
          )
          as _i7.Future<_i4.RecordModel>);

  @override
  _i7.Future<_i4.RecordModel> updateRecord({
    required String? collectionName,
    required String? recordId,
    required Map<String, dynamic>? data,
    Map<String, dynamic>? query = const {},
    List<_i2.MultipartFile>? files = const [],
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateRecord, [], {
              #collectionName: collectionName,
              #recordId: recordId,
              #data: data,
              #query: query,
              #files: files,
            }),
            returnValue: _i7.Future<_i4.RecordModel>.value(
              _FakeRecordModel_5(
                this,
                Invocation.method(#updateRecord, [], {
                  #collectionName: collectionName,
                  #recordId: recordId,
                  #data: data,
                  #query: query,
                  #files: files,
                }),
              ),
            ),
          )
          as _i7.Future<_i4.RecordModel>);

  @override
  _i7.Future<List<_i4.RecordModel>> getFullList({
    required String? collectionName,
    int? batch = 200,
    String? filter,
    String? sort,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getFullList, [], {
              #collectionName: collectionName,
              #batch: batch,
              #filter: filter,
              #sort: sort,
            }),
            returnValue: _i7.Future<List<_i4.RecordModel>>.value(
              <_i4.RecordModel>[],
            ),
          )
          as _i7.Future<List<_i4.RecordModel>>);

  @override
  _i7.Future<_i4.ResultList<_i4.RecordModel>> getList({
    required String? collectionName,
    int? page = 1,
    int? perPage = 30,
    String? filter,
    String? sort,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getList, [], {
              #collectionName: collectionName,
              #page: page,
              #perPage: perPage,
              #filter: filter,
              #sort: sort,
            }),
            returnValue: _i7.Future<_i4.ResultList<_i4.RecordModel>>.value(
              _FakeResultList_6<_i4.RecordModel>(
                this,
                Invocation.method(#getList, [], {
                  #collectionName: collectionName,
                  #page: page,
                  #perPage: perPage,
                  #filter: filter,
                  #sort: sort,
                }),
              ),
            ),
          )
          as _i7.Future<_i4.ResultList<_i4.RecordModel>>);

  @override
  _i7.Future<_i4.RecordModel> getOne({
    required String? collectionName,
    required String? recordId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getOne, [], {
              #collectionName: collectionName,
              #recordId: recordId,
            }),
            returnValue: _i7.Future<_i4.RecordModel>.value(
              _FakeRecordModel_5(
                this,
                Invocation.method(#getOne, [], {
                  #collectionName: collectionName,
                  #recordId: recordId,
                }),
              ),
            ),
          )
          as _i7.Future<_i4.RecordModel>);

  @override
  _i7.Future<void> deleteRecord({
    required String? collectionName,
    required String? recordId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#deleteRecord, [], {
              #collectionName: collectionName,
              #recordId: recordId,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<Map<String, String>> uploadFileAndGetId({
    required String? targetCollectionName,
    required _i2.MultipartFile? multipartFile,
    Map<String, String>? body = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadFileAndGetId, [], {
              #targetCollectionName: targetCollectionName,
              #multipartFile: multipartFile,
              #body: body,
            }),
            returnValue: _i7.Future<Map<String, String>>.value(
              <String, String>{},
            ),
          )
          as _i7.Future<Map<String, String>>);
}

/// A class which mocks [File].
///
/// See the documentation for Mockito's code generation for more information.
class MockFile extends _i1.Mock implements _i5.File {
  MockFile() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.File get absolute =>
      (super.noSuchMethod(
            Invocation.getter(#absolute),
            returnValue: _FakeFile_7(this, Invocation.getter(#absolute)),
          )
          as _i5.File);

  @override
  String get path =>
      (super.noSuchMethod(
            Invocation.getter(#path),
            returnValue: _i9.dummyValue<String>(this, Invocation.getter(#path)),
          )
          as String);

  @override
  Uri get uri =>
      (super.noSuchMethod(
            Invocation.getter(#uri),
            returnValue: _FakeUri_8(this, Invocation.getter(#uri)),
          )
          as Uri);

  @override
  bool get isAbsolute =>
      (super.noSuchMethod(Invocation.getter(#isAbsolute), returnValue: false)
          as bool);

  @override
  _i5.Directory get parent =>
      (super.noSuchMethod(
            Invocation.getter(#parent),
            returnValue: _FakeDirectory_9(this, Invocation.getter(#parent)),
          )
          as _i5.Directory);

  @override
  _i7.Future<_i5.File> create({
    bool? recursive = false,
    bool? exclusive = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#create, [], {
              #recursive: recursive,
              #exclusive: exclusive,
            }),
            returnValue: _i7.Future<_i5.File>.value(
              _FakeFile_7(
                this,
                Invocation.method(#create, [], {
                  #recursive: recursive,
                  #exclusive: exclusive,
                }),
              ),
            ),
          )
          as _i7.Future<_i5.File>);

  @override
  void createSync({bool? recursive = false, bool? exclusive = false}) =>
      super.noSuchMethod(
        Invocation.method(#createSync, [], {
          #recursive: recursive,
          #exclusive: exclusive,
        }),
        returnValueForMissingStub: null,
      );

  @override
  _i7.Future<_i5.File> rename(String? newPath) =>
      (super.noSuchMethod(
            Invocation.method(#rename, [newPath]),
            returnValue: _i7.Future<_i5.File>.value(
              _FakeFile_7(this, Invocation.method(#rename, [newPath])),
            ),
          )
          as _i7.Future<_i5.File>);

  @override
  _i5.File renameSync(String? newPath) =>
      (super.noSuchMethod(
            Invocation.method(#renameSync, [newPath]),
            returnValue: _FakeFile_7(
              this,
              Invocation.method(#renameSync, [newPath]),
            ),
          )
          as _i5.File);

  @override
  _i7.Future<_i5.FileSystemEntity> delete({bool? recursive = false}) =>
      (super.noSuchMethod(
            Invocation.method(#delete, [], {#recursive: recursive}),
            returnValue: _i7.Future<_i5.FileSystemEntity>.value(
              _FakeFileSystemEntity_10(
                this,
                Invocation.method(#delete, [], {#recursive: recursive}),
              ),
            ),
          )
          as _i7.Future<_i5.FileSystemEntity>);

  @override
  void deleteSync({bool? recursive = false}) => super.noSuchMethod(
    Invocation.method(#deleteSync, [], {#recursive: recursive}),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<_i5.File> copy(String? newPath) =>
      (super.noSuchMethod(
            Invocation.method(#copy, [newPath]),
            returnValue: _i7.Future<_i5.File>.value(
              _FakeFile_7(this, Invocation.method(#copy, [newPath])),
            ),
          )
          as _i7.Future<_i5.File>);

  @override
  _i5.File copySync(String? newPath) =>
      (super.noSuchMethod(
            Invocation.method(#copySync, [newPath]),
            returnValue: _FakeFile_7(
              this,
              Invocation.method(#copySync, [newPath]),
            ),
          )
          as _i5.File);

  @override
  _i7.Future<int> length() =>
      (super.noSuchMethod(
            Invocation.method(#length, []),
            returnValue: _i7.Future<int>.value(0),
          )
          as _i7.Future<int>);

  @override
  int lengthSync() =>
      (super.noSuchMethod(Invocation.method(#lengthSync, []), returnValue: 0)
          as int);

  @override
  _i7.Future<DateTime> lastAccessed() =>
      (super.noSuchMethod(
            Invocation.method(#lastAccessed, []),
            returnValue: _i7.Future<DateTime>.value(
              _FakeDateTime_11(this, Invocation.method(#lastAccessed, [])),
            ),
          )
          as _i7.Future<DateTime>);

  @override
  DateTime lastAccessedSync() =>
      (super.noSuchMethod(
            Invocation.method(#lastAccessedSync, []),
            returnValue: _FakeDateTime_11(
              this,
              Invocation.method(#lastAccessedSync, []),
            ),
          )
          as DateTime);

  @override
  _i7.Future<dynamic> setLastAccessed(DateTime? time) =>
      (super.noSuchMethod(
            Invocation.method(#setLastAccessed, [time]),
            returnValue: _i7.Future<dynamic>.value(),
          )
          as _i7.Future<dynamic>);

  @override
  void setLastAccessedSync(DateTime? time) => super.noSuchMethod(
    Invocation.method(#setLastAccessedSync, [time]),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<DateTime> lastModified() =>
      (super.noSuchMethod(
            Invocation.method(#lastModified, []),
            returnValue: _i7.Future<DateTime>.value(
              _FakeDateTime_11(this, Invocation.method(#lastModified, [])),
            ),
          )
          as _i7.Future<DateTime>);

  @override
  DateTime lastModifiedSync() =>
      (super.noSuchMethod(
            Invocation.method(#lastModifiedSync, []),
            returnValue: _FakeDateTime_11(
              this,
              Invocation.method(#lastModifiedSync, []),
            ),
          )
          as DateTime);

  @override
  _i7.Future<dynamic> setLastModified(DateTime? time) =>
      (super.noSuchMethod(
            Invocation.method(#setLastModified, [time]),
            returnValue: _i7.Future<dynamic>.value(),
          )
          as _i7.Future<dynamic>);

  @override
  void setLastModifiedSync(DateTime? time) => super.noSuchMethod(
    Invocation.method(#setLastModifiedSync, [time]),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<_i5.RandomAccessFile> open({
    _i5.FileMode? mode = _i5.FileMode.read,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#open, [], {#mode: mode}),
            returnValue: _i7.Future<_i5.RandomAccessFile>.value(
              _FakeRandomAccessFile_12(
                this,
                Invocation.method(#open, [], {#mode: mode}),
              ),
            ),
          )
          as _i7.Future<_i5.RandomAccessFile>);

  @override
  _i5.RandomAccessFile openSync({_i5.FileMode? mode = _i5.FileMode.read}) =>
      (super.noSuchMethod(
            Invocation.method(#openSync, [], {#mode: mode}),
            returnValue: _FakeRandomAccessFile_12(
              this,
              Invocation.method(#openSync, [], {#mode: mode}),
            ),
          )
          as _i5.RandomAccessFile);

  @override
  _i7.Stream<List<int>> openRead([int? start, int? end]) =>
      (super.noSuchMethod(
            Invocation.method(#openRead, [start, end]),
            returnValue: _i7.Stream<List<int>>.empty(),
          )
          as _i7.Stream<List<int>>);

  @override
  _i5.IOSink openWrite({
    _i5.FileMode? mode = _i5.FileMode.write,
    _i10.Encoding? encoding = const _i10.Utf8Codec(),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#openWrite, [], {
              #mode: mode,
              #encoding: encoding,
            }),
            returnValue: _FakeIOSink_13(
              this,
              Invocation.method(#openWrite, [], {
                #mode: mode,
                #encoding: encoding,
              }),
            ),
          )
          as _i5.IOSink);

  @override
  _i7.Future<_i11.Uint8List> readAsBytes() =>
      (super.noSuchMethod(
            Invocation.method(#readAsBytes, []),
            returnValue: _i7.Future<_i11.Uint8List>.value(_i11.Uint8List(0)),
          )
          as _i7.Future<_i11.Uint8List>);

  @override
  _i11.Uint8List readAsBytesSync() =>
      (super.noSuchMethod(
            Invocation.method(#readAsBytesSync, []),
            returnValue: _i11.Uint8List(0),
          )
          as _i11.Uint8List);

  @override
  _i7.Future<String> readAsString({
    _i10.Encoding? encoding = const _i10.Utf8Codec(),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#readAsString, [], {#encoding: encoding}),
            returnValue: _i7.Future<String>.value(
              _i9.dummyValue<String>(
                this,
                Invocation.method(#readAsString, [], {#encoding: encoding}),
              ),
            ),
          )
          as _i7.Future<String>);

  @override
  String readAsStringSync({_i10.Encoding? encoding = const _i10.Utf8Codec()}) =>
      (super.noSuchMethod(
            Invocation.method(#readAsStringSync, [], {#encoding: encoding}),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#readAsStringSync, [], {#encoding: encoding}),
            ),
          )
          as String);

  @override
  _i7.Future<List<String>> readAsLines({
    _i10.Encoding? encoding = const _i10.Utf8Codec(),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#readAsLines, [], {#encoding: encoding}),
            returnValue: _i7.Future<List<String>>.value(<String>[]),
          )
          as _i7.Future<List<String>>);

  @override
  List<String> readAsLinesSync({
    _i10.Encoding? encoding = const _i10.Utf8Codec(),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#readAsLinesSync, [], {#encoding: encoding}),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  _i7.Future<_i5.File> writeAsBytes(
    List<int>? bytes, {
    _i5.FileMode? mode = _i5.FileMode.write,
    bool? flush = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #writeAsBytes,
              [bytes],
              {#mode: mode, #flush: flush},
            ),
            returnValue: _i7.Future<_i5.File>.value(
              _FakeFile_7(
                this,
                Invocation.method(
                  #writeAsBytes,
                  [bytes],
                  {#mode: mode, #flush: flush},
                ),
              ),
            ),
          )
          as _i7.Future<_i5.File>);

  @override
  void writeAsBytesSync(
    List<int>? bytes, {
    _i5.FileMode? mode = _i5.FileMode.write,
    bool? flush = false,
  }) => super.noSuchMethod(
    Invocation.method(#writeAsBytesSync, [bytes], {#mode: mode, #flush: flush}),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<_i5.File> writeAsString(
    String? contents, {
    _i5.FileMode? mode = _i5.FileMode.write,
    _i10.Encoding? encoding = const _i10.Utf8Codec(),
    bool? flush = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #writeAsString,
              [contents],
              {#mode: mode, #encoding: encoding, #flush: flush},
            ),
            returnValue: _i7.Future<_i5.File>.value(
              _FakeFile_7(
                this,
                Invocation.method(
                  #writeAsString,
                  [contents],
                  {#mode: mode, #encoding: encoding, #flush: flush},
                ),
              ),
            ),
          )
          as _i7.Future<_i5.File>);

  @override
  void writeAsStringSync(
    String? contents, {
    _i5.FileMode? mode = _i5.FileMode.write,
    _i10.Encoding? encoding = const _i10.Utf8Codec(),
    bool? flush = false,
  }) => super.noSuchMethod(
    Invocation.method(
      #writeAsStringSync,
      [contents],
      {#mode: mode, #encoding: encoding, #flush: flush},
    ),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<bool> exists() =>
      (super.noSuchMethod(
            Invocation.method(#exists, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  bool existsSync() =>
      (super.noSuchMethod(
            Invocation.method(#existsSync, []),
            returnValue: false,
          )
          as bool);

  @override
  _i7.Future<String> resolveSymbolicLinks() =>
      (super.noSuchMethod(
            Invocation.method(#resolveSymbolicLinks, []),
            returnValue: _i7.Future<String>.value(
              _i9.dummyValue<String>(
                this,
                Invocation.method(#resolveSymbolicLinks, []),
              ),
            ),
          )
          as _i7.Future<String>);

  @override
  String resolveSymbolicLinksSync() =>
      (super.noSuchMethod(
            Invocation.method(#resolveSymbolicLinksSync, []),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.method(#resolveSymbolicLinksSync, []),
            ),
          )
          as String);

  @override
  _i7.Future<_i5.FileStat> stat() =>
      (super.noSuchMethod(
            Invocation.method(#stat, []),
            returnValue: _i7.Future<_i5.FileStat>.value(
              _FakeFileStat_14(this, Invocation.method(#stat, [])),
            ),
          )
          as _i7.Future<_i5.FileStat>);

  @override
  _i5.FileStat statSync() =>
      (super.noSuchMethod(
            Invocation.method(#statSync, []),
            returnValue: _FakeFileStat_14(
              this,
              Invocation.method(#statSync, []),
            ),
          )
          as _i5.FileStat);

  @override
  _i7.Stream<_i5.FileSystemEvent> watch({
    int? events = 15,
    bool? recursive = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#watch, [], {
              #events: events,
              #recursive: recursive,
            }),
            returnValue: _i7.Stream<_i5.FileSystemEvent>.empty(),
          )
          as _i7.Stream<_i5.FileSystemEvent>);
}
