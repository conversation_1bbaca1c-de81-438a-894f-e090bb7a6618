import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/google_drive_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/google_drive_permission.dart';

/// Integration tests for Google Drive service
///
/// These tests require actual Google Drive API credentials and should only be run
/// in a controlled test environment with proper service account setup.
///
/// To run these tests:
/// 1. Set up a test Google Cloud project
/// 2. Create a service account with Drive API access
/// 3. Set the GOOGLE_SERVICE_ACCOUNT_CREDENTIALS environment variable
/// 4. Run with: flutter test test/src/core/services/google_drive_integration_test.dart
void main() {
  group('Google Drive Integration Tests', () {
    late GoogleDriveService googleDriveService;

    setUpAll(() async {
      // Initialize logger
      LoggerService.info('Starting Google Drive integration tests');

      // Check if credentials are available
      final hasCredentials =
          Platform.environment.containsKey(
            'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
          ) ||
          Platform.environment.containsKey('GOOGLE_SERVICE_ACCOUNT_FILE');

      if (!hasCredentials) {
        LoggerService.warning(
          'Skipping integration tests - no credentials found',
        );
        return;
      }

      // Initialize service
      googleDriveService = GoogleDriveService();
      await googleDriveService.initialize();
    });

    tearDownAll(() async {
      googleDriveService.dispose();
    });

    group('Authentication and Configuration', () {
      test(
        'should authenticate successfully',
        () async {
          // Skip if no credentials
          if (!Platform.environment.containsKey(
            'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
          )) {
            return;
          }

          expect(googleDriveService.isInitialized, isTrue);
          expect(googleDriveService.isAuthenticated, isTrue);
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );

      test(
        'should perform health check',
        () async {
          // Skip if no credentials
          if (!Platform.environment.containsKey(
            'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
          )) {
            return;
          }

          final health = await googleDriveService.checkServiceHealth();

          expect(health['service_initialized'], isTrue);
          expect(health['authentication_valid'], isTrue);
          expect(health['api_accessible'], isTrue);
          expect(health['errors'], isEmpty);
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );

      test(
        'should get storage usage information',
        () async {
          // Skip if no credentials
          if (!Platform.environment.containsKey(
            'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
          )) {
            return;
          }

          final storageInfo = await googleDriveService.getStorageUsage();

          expect(storageInfo, isNotNull);
          expect(storageInfo['user_email'], isNotNull);
          expect(storageInfo['user_name'], isNotNull);

          LoggerService.info(
            'Storage usage: ${storageInfo['usage']} / ${storageInfo['limit']}',
          );
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );
    });

    group('Folder Operations', () {
      String? testFolderId;

      test(
        'should create test folder',
        () async {
          // Skip if no credentials
          if (!Platform.environment.containsKey(
            'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
          )) {
            return;
          }

          final folderName =
              'Test_Folder_${DateTime.now().millisecondsSinceEpoch}';

          final folder = await googleDriveService.createFolder(
            folderName: folderName,
            metadata: {'test': 'true', 'created_by': 'integration_test'},
          );

          expect(folder.name, equals(folderName));
          expect(folder.isFolder, isTrue);
          expect(folder.id, isNotEmpty);

          testFolderId = folder.id;
          LoggerService.info('Created test folder: ${folder.id}');
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );

      test(
        'should list files in folder',
        () async {
          // Skip if no credentials or no test folder
          if (!Platform.environment.containsKey(
                'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
              ) ||
              testFolderId == null) {
            return;
          }

          final files = await googleDriveService.listFilesInFolder(
            folderId: testFolderId,
            maxResults: 10,
          );

          expect(files, isNotNull);
          expect(files, isList);

          LoggerService.info('Found ${files.length} files in test folder');
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );

      test(
        'should create claim folder structure',
        () async {
          // Skip if no credentials
          if (!Platform.environment.containsKey(
            'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
          )) {
            return;
          }

          final claimId = 'test_claim_${DateTime.now().millisecondsSinceEpoch}';
          const claimTitle = 'Integration Test Claim';
          final categories = ['contracts', 'evidence', 'correspondence'];

          final folderStructure = await googleDriveService
              .createClaimFolderStructure(
                claimId: claimId,
                claimTitle: claimTitle,
                documentCategories: categories,
              );

          expect(folderStructure, isNotNull);
          expect(folderStructure['root'], isNotEmpty);

          for (final category in categories) {
            expect(folderStructure[category], isNotEmpty);
          }

          LoggerService.info(
            'Created claim folder structure: $folderStructure',
          );

          // Clean up - delete the created folders
          for (final folderId in folderStructure.values) {
            try {
              await googleDriveService.deleteFolder(folderId, recursive: true);
            } catch (e) {
              LoggerService.warning('Failed to clean up folder $folderId: $e');
            }
          }
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );
    });

    group('File Operations', () {
      String? testFileId;

      test(
        'should upload test file',
        () async {
          // Skip if no credentials
          if (!Platform.environment.containsKey(
            'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
          )) {
            return;
          }

          // Create a temporary test file
          final tempDir = Directory.systemTemp;
          final testFile = File(
            '${tempDir.path}/test_upload_${DateTime.now().millisecondsSinceEpoch}.txt',
          );
          const testContent =
              'This is a test file for Google Drive integration testing.';
          await testFile.writeAsString(testContent);

          try {
            final uploadedFile = await googleDriveService.uploadFile(
              file: testFile,
              fileName: 'integration_test.txt',
              metadata: {'test': 'true', 'uploaded_by': 'integration_test'},
            );

            expect(uploadedFile.name, equals('integration_test.txt'));
            expect(uploadedFile.id, isNotEmpty);
            expect(uploadedFile.size, equals(testContent.length));

            testFileId = uploadedFile.id;
            LoggerService.info('Uploaded test file: ${uploadedFile.id}');
          } finally {
            // Clean up temporary file
            if (await testFile.exists()) {
              await testFile.delete();
            }
          }
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );

      test(
        'should get file metadata',
        () async {
          // Skip if no credentials or no test file
          if (!Platform.environment.containsKey(
                'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
              ) ||
              testFileId == null) {
            return;
          }

          final fileMetadata = await googleDriveService.getFileMetadata(
            testFileId!,
          );

          expect(fileMetadata.id, equals(testFileId));
          expect(fileMetadata.name, equals('integration_test.txt'));
          expect(fileMetadata.mimeType, isNotEmpty);
          expect(fileMetadata.isFolder, isFalse);

          LoggerService.info(
            'File metadata: ${fileMetadata.name} (${fileMetadata.formattedSize})',
          );
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );

      test(
        'should download file',
        () async {
          // Skip if no credentials or no test file
          if (!Platform.environment.containsKey(
                'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
              ) ||
              testFileId == null) {
            return;
          }

          final fileBytes = await googleDriveService.downloadFile(testFileId!);

          expect(fileBytes, isNotNull);
          expect(fileBytes.length, greaterThan(0));

          final content = String.fromCharCodes(fileBytes);
          expect(content, contains('integration testing'));

          LoggerService.info('Downloaded file: ${fileBytes.length} bytes');
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );

      test(
        'should get file URLs',
        () async {
          // Skip if no credentials or no test file
          if (!Platform.environment.containsKey(
                'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
              ) ||
              testFileId == null) {
            return;
          }

          final viewUrl = await googleDriveService.getFileViewUrl(testFileId!);
          final downloadUrl = await googleDriveService.getFileDownloadUrl(
            testFileId!,
          );

          expect(viewUrl, isNotEmpty);
          expect(viewUrl, contains('drive.google.com'));
          expect(downloadUrl, isNotEmpty);

          LoggerService.info('View URL: $viewUrl');
          LoggerService.info('Download URL: $downloadUrl');
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );

      test(
        'should copy file',
        () async {
          // Skip if no credentials or no test file
          if (!Platform.environment.containsKey(
                'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
              ) ||
              testFileId == null) {
            return;
          }

          final copiedFile = await googleDriveService.copyFile(
            sourceFileId: testFileId!,
            newName: 'integration_test_copy.txt',
          );

          expect(copiedFile.name, equals('integration_test_copy.txt'));
          expect(copiedFile.id, isNotEmpty);
          expect(copiedFile.id, isNot(equals(testFileId)));

          LoggerService.info('Copied file: ${copiedFile.id}');

          // Clean up copied file
          try {
            await googleDriveService.deleteFile(copiedFile.id);
          } catch (e) {
            LoggerService.warning('Failed to clean up copied file: $e');
          }
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );

      test(
        'should search files',
        () async {
          // Skip if no credentials
          if (!Platform.environment.containsKey(
            'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
          )) {
            return;
          }

          final searchResults = await googleDriveService.searchFiles(
            nameQuery: 'integration_test',
            maxResults: 10,
          );

          expect(searchResults, isNotNull);
          expect(searchResults, isList);

          LoggerService.info('Search found ${searchResults.length} files');

          // Should find our test file
          final testFile =
              searchResults.where((file) => file.id == testFileId).firstOrNull;
          if (testFile != null) {
            expect(testFile.name, equals('integration_test.txt'));
          }
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );

      test(
        'should delete test file',
        () async {
          // Skip if no credentials or no test file
          if (!Platform.environment.containsKey(
                'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
              ) ||
              testFileId == null) {
            return;
          }

          await googleDriveService.deleteFile(testFileId!);

          // Verify file is deleted
          final fileExists = await googleDriveService.fileExists(testFileId!);
          expect(fileExists, isFalse);

          LoggerService.info('Deleted test file: $testFileId');
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );
    });

    group('Permission Management', () {
      test(
        'should manage file permissions',
        () async {
          // Skip if no credentials
          if (!Platform.environment.containsKey(
            'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
          )) {
            return;
          }

          // This test would require a test file and valid email addresses
          // For now, we'll just test the permission template creation

          final userReader = GoogleDrivePermissionTemplates.userReader(
            '<EMAIL>',
          );
          expect(userReader['type'], equals('user'));
          expect(userReader['role'], equals('reader'));

          final publicReader = GoogleDrivePermissionTemplates.publicReader();
          expect(publicReader['type'], equals('anyone'));
          expect(publicReader['role'], equals('reader'));

          LoggerService.info('Permission templates created successfully');
        },
        skip:
            !Platform.environment.containsKey(
              'GOOGLE_SERVICE_ACCOUNT_CREDENTIALS',
            ),
      );
    });
  });
}
