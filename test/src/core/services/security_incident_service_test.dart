import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/security_incident_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/enhanced_audit_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/security_event.dart';
import '../../../utils/notification_test_utils.dart';

// Generate mocks
@GenerateMocks([PocketBaseService, EnhancedAuditService])
import 'security_incident_service_test.mocks.dart';

void main() {
  group('SecurityIncidentService', () {
    late SecurityIncidentService incidentService;
    late MockPocketBaseService mockPocketBaseService;
    late MockEnhancedAuditService mockAuditService;

    setUp(() {
      incidentService = SecurityIncidentService();
      mockPocketBaseService = MockPocketBaseService();
      mockAuditService = MockEnhancedAuditService();

      // LoggerService is ready to use without initialization
    });

    group('Initialization', () {
      test('should initialize successfully', () async {
        // Act & Assert
        expect(() => incidentService.initialize(), returnsNormally);
      });

      test('should start continuous monitoring', () async {
        // Act
        await incidentService.initialize();
        final status = incidentService.getStatus();

        // Assert
        expect(status['service_initialized'], isTrue);
        expect(status['monitoring_active'], isTrue);
      });
    });

    group('Failed Authentication Detection', () {
      test(
        'should detect brute force attack after multiple failed attempts',
        () async {
          // Arrange
          when(
            mockPocketBaseService.createRecord(
              collectionName: 'security_incidents',
              data: any,
            ),
          ).thenAnswer(
            (_) async => NotificationTestUtils.createMockRecord(
              id: "test_id",
              data: {},
              collectionName: "security_incidents",
            ),
          );

          when(
            mockAuditService.logSecurityEvent(any),
          ).thenAnswer((_) async => {});

          final failedAuthEvent = SecurityEvent.authentication(
            id: 'auth-fail-1',
            success: false,
            userId: 'user-123',
            ipAddress: '*************',
            errorMessage: 'Invalid password',
          );

          // Act - Simulate multiple failed attempts
          for (int i = 0; i < 6; i++) {
            await incidentService.analyzeSecurityEvent(failedAuthEvent);
          }

          // Assert
          verify(
            mockPocketBaseService.createRecord(
              collectionName: 'security_incidents',
              data: any,
            ),
          ).called(1);
        },
      );

      test('should track failed attempts per user and IP combination', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "security_incidents",
          ),
        );

        final user1Event = SecurityEvent.authentication(
          id: 'auth-fail-user1',
          success: false,
          userId: 'user-1',
          ipAddress: '*************',
        );

        final user2Event = SecurityEvent.authentication(
          id: 'auth-fail-user2',
          success: false,
          userId: 'user-2',
          ipAddress: '*************',
        );

        // Act - Different users should be tracked separately
        for (int i = 0; i < 3; i++) {
          await incidentService.analyzeSecurityEvent(user1Event);
          await incidentService.analyzeSecurityEvent(user2Event);
        }

        // Assert - Should not trigger incident yet (each user only has 3 attempts)
        verifyNever(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        );
      });

      test('should reset failed attempts after time window', () async {
        // This test would require manipulating time, which is complex
        // For now, we'll test the status reporting
        final status = incidentService.getStatus();
        expect(status.containsKey('failed_attempt_tracking'), isTrue);
        expect(status.containsKey('max_failed_attempts'), isTrue);
        expect(status.containsKey('failed_attempt_window_minutes'), isTrue);
      });
    });

    group('Suspicious Activity Detection', () {
      test('should detect off-hours access', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "security_incidents",
          ),
        );

        when(
          mockAuditService.logSecurityEvent(any),
        ).thenAnswer((_) async => {});

        // Create event with off-hours timestamp (2 AM)
        final offHoursEvent = SecurityEvent(
          id: 'off-hours-1',
          type: 'file_access',
          timestamp: DateTime(2024, 1, 1, 2, 0), // 2 AM
          success: true,
          userId: 'user-123',
          resource: 'sensitive-document.pdf',
          action: 'download',
          riskScore: 85,
          severity: SecurityEventSeverity.medium,
          requiresAttention: true,
          tags: ['file_access'],
        );

        // Act
        await incidentService.analyzeSecurityEvent(offHoursEvent);

        // Assert
        verify(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).called(1);
      });

      test('should detect suspicious user agents', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "security_incidents",
          ),
        );

        when(
          mockAuditService.logSecurityEvent(any),
        ).thenAnswer((_) async => {});

        final suspiciousEvent = SecurityEvent(
          id: 'suspicious-ua-1',
          type: 'file_access',
          timestamp: DateTime.now(),
          success: true,
          userId: 'user-123',
          details: {'user_agent': 'curl/7.68.0'}, // Suspicious user agent
          riskScore: 75,
          severity: SecurityEventSeverity.medium,
          requiresAttention: true,
          tags: ['file_access'],
        );

        // Act
        await incidentService.analyzeSecurityEvent(suspiciousEvent);

        // Assert
        verify(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).called(1);
      });

      test('should detect rapid successive actions', () async {
        // Arrange
        when(
          mockPocketBaseService.getFullList(
            collectionName: 'security_events',
            filter: any,
          ),
        ).thenAnswer(
          (_) async => List.generate(
            15,
            (i) => NotificationTestUtils.createMockRecord(
              id: "incident_$i",
              data: {},
              collectionName: "security_incidents",
            ),
          ),
        ); // 15 recent events

        when(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "security_incidents",
          ),
        );

        when(
          mockAuditService.logSecurityEvent(any),
        ).thenAnswer((_) async => {});

        final rapidEvent = SecurityEvent(
          id: 'rapid-1',
          type: 'file_access',
          timestamp: DateTime.now(),
          success: true,
          userId: 'user-123',
          riskScore: 60,
          severity: SecurityEventSeverity.low,
          requiresAttention: false,
          tags: ['file_access'],
        );

        // Act
        await incidentService.analyzeSecurityEvent(rapidEvent);

        // Assert
        verify(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).called(1);
      });
    });

    group('High-Risk Activity Detection', () {
      test('should create incident for high-risk events', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "security_incidents",
          ),
        );

        when(
          mockAuditService.logSecurityEvent(any),
        ).thenAnswer((_) async => {});

        final highRiskEvent = SecurityEvent(
          id: 'high-risk-1',
          type: 'permission_change',
          timestamp: DateTime.now(),
          success: true,
          userId: 'user-123',
          resource: 'admin-panel',
          action: 'grant_admin_access',
          riskScore: 95, // High risk score
          severity: SecurityEventSeverity.high,
          requiresAttention: true,
          tags: ['permission', 'admin'],
        );

        // Act
        await incidentService.analyzeSecurityEvent(highRiskEvent);

        // Assert
        verify(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).called(1);
      });
    });

    group('Anomaly Detection', () {
      test('should detect first-time resource access', () async {
        // Arrange
        when(
          mockPocketBaseService.getFullList(
            collectionName: 'security_events',
            filter: any,
          ),
        ).thenAnswer((_) async => []); // No previous access

        when(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "security_incidents",
          ),
        );

        when(
          mockAuditService.logSecurityEvent(any),
        ).thenAnswer((_) async => {});

        final firstAccessEvent = SecurityEvent(
          id: 'first-access-1',
          type: 'file_access',
          timestamp: DateTime.now(),
          success: true,
          userId: 'user-123',
          resource: 'confidential-document.pdf',
          action: 'view',
          riskScore: 50,
          severity: SecurityEventSeverity.low,
          requiresAttention: false,
          tags: ['file_access'],
        );

        // Act
        await incidentService.analyzeSecurityEvent(firstAccessEvent);

        // Assert
        verify(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).called(1);
      });

      test('should detect new IP address access', () async {
        // Arrange
        when(
          mockPocketBaseService.getFullList(
            collectionName: 'security_events',
            filter: any,
          ),
        ).thenAnswer(
          (_) async => [
            NotificationTestUtils.createMockRecord(
              id: 'event1',
              data: {'ip_address': '***********'},
              collectionName: 'security_incidents',
            ),
            NotificationTestUtils.createMockRecord(
              id: 'event2',
              data: {'ip_address': '***********'},
              collectionName: 'security_incidents',
            ),
          ],
        );

        when(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "security_incidents",
          ),
        );

        when(
          mockAuditService.logSecurityEvent(any),
        ).thenAnswer((_) async => {});

        final newIpEvent = SecurityEvent(
          id: 'new-ip-1',
          type: 'authentication',
          timestamp: DateTime.now(),
          success: true,
          userId: 'user-123',
          ipAddress: '********', // New IP address
          action: 'login',
          riskScore: 40,
          severity: SecurityEventSeverity.low,
          requiresAttention: false,
          tags: ['auth'],
        );

        // Act
        await incidentService.analyzeSecurityEvent(newIpEvent);

        // Assert
        verify(
          mockPocketBaseService.createRecord(
            collectionName: 'security_incidents',
            data: any,
          ),
        ).called(1);
      });
    });

    group('Incident Management', () {
      test(
        'should trigger immediate response for critical incidents',
        () async {
          // Arrange
          when(
            mockPocketBaseService.createRecord(
              collectionName: 'security_incidents',
              data: any,
            ),
          ).thenAnswer(
            (_) async => NotificationTestUtils.createMockRecord(
              id: "test_id",
              data: {},
              collectionName: "security_incidents",
            ),
          );

          when(
            mockPocketBaseService.updateRecord(
              collectionName: 'security_incidents',
              recordId: any,
              data: any,
            ),
          ).thenAnswer(
            (_) async => NotificationTestUtils.createMockRecord(
              id: "test_id",
              data: {},
              collectionName: "security_incidents",
            ),
          );

          when(
            mockAuditService.logSecurityEvent(any),
          ).thenAnswer((_) async => {});

          final criticalEvent = SecurityEvent(
            id: 'critical-1',
            type: 'system_compromise',
            timestamp: DateTime.now(),
            success: false,
            userId: 'attacker-123',
            riskScore: 100,
            severity: SecurityEventSeverity.critical,
            requiresAttention: true,
            tags: ['critical', 'compromise'],
          );

          // Act
          await incidentService.analyzeSecurityEvent(criticalEvent);

          // Assert
          verify(
            mockPocketBaseService.createRecord(
              collectionName: 'security_incidents',
              data: any,
            ),
          ).called(1);
          verify(
            mockPocketBaseService.updateRecord(
              collectionName: 'security_incidents',
              recordId: any,
              data: any,
            ),
          ).called(1);
        },
      );

      test('should track active incidents', () {
        // Act
        final activeIncidents = incidentService.getActiveIncidents();
        final status = incidentService.getStatus();

        // Assert
        expect(activeIncidents, isA<List<SecurityIncident>>());
        expect(status.containsKey('active_incidents'), isTrue);
      });
    });

    group('Periodic Monitoring', () {
      test('should perform periodic security checks', () async {
        // This test would require time manipulation for proper testing
        // For now, we'll test that the monitoring is active
        await incidentService.initialize();
        final status = incidentService.getStatus();

        expect(status['monitoring_active'], isTrue);
      });

      test('should clean up old failed attempt records', () async {
        // This test would require time manipulation
        // For now, we'll verify the service tracks failed attempts
        final status = incidentService.getStatus();
        expect(status.containsKey('failed_attempt_tracking'), isTrue);
      });
    });

    group('Service Status and Health', () {
      test('should return comprehensive service status', () {
        // Act
        final status = incidentService.getStatus();

        // Assert
        expect(status, isA<Map<String, dynamic>>());
        expect(status['service_initialized'], isTrue);
        expect(status.containsKey('active_incidents'), isTrue);
        expect(status.containsKey('monitoring_active'), isTrue);
        expect(status.containsKey('failed_attempt_tracking'), isTrue);
        expect(status.containsKey('max_failed_attempts'), isTrue);
        expect(status.containsKey('failed_attempt_window_minutes'), isTrue);
        expect(status.containsKey('suspicious_activity_threshold'), isTrue);
        expect(status.containsKey('timestamp'), isTrue);
      });

      test('should dispose resources correctly', () {
        // Act & Assert
        expect(() => incidentService.dispose(), returnsNormally);
      });
    });

    group('Error Handling', () {
      test('should handle database errors gracefully', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(collectionName: any, data: any),
        ).thenThrow(Exception('Database connection failed'));

        final testEvent = SecurityEvent(
          id: 'test-1',
          type: 'file_access',
          timestamp: DateTime.now(),
          success: true,
          riskScore: 90,
          severity: SecurityEventSeverity.high,
          requiresAttention: true,
          tags: ['test'],
        );

        // Act & Assert - Should not throw
        expect(
          () => incidentService.analyzeSecurityEvent(testEvent),
          returnsNormally,
        );
      });

      test('should continue monitoring after errors', () async {
        // Arrange
        when(
          mockPocketBaseService.getFullList(collectionName: any, filter: any),
        ).thenThrow(Exception('Query failed'));

        final testEvent = SecurityEvent(
          id: 'test-2',
          type: 'authentication',
          timestamp: DateTime.now(),
          success: true,
          userId: 'user-123',
          riskScore: 30,
          severity: SecurityEventSeverity.low,
          requiresAttention: false,
          tags: ['auth'],
        );

        // Act
        await incidentService.analyzeSecurityEvent(testEvent);

        // Assert - Service should still be operational
        final status = incidentService.getStatus();
        expect(status['service_initialized'], isTrue);
      });
    });

    group('Security Event Analysis', () {
      test('should analyze events without throwing exceptions', () async {
        // Arrange
        final normalEvent = SecurityEvent(
          id: 'normal-1',
          type: 'file_access',
          timestamp: DateTime.now(),
          success: true,
          userId: 'user-123',
          resource: 'document.pdf',
          action: 'view',
          riskScore: 10,
          severity: SecurityEventSeverity.low,
          requiresAttention: false,
          tags: ['file_access'],
        );

        // Act & Assert
        expect(
          () => incidentService.analyzeSecurityEvent(normalEvent),
          returnsNormally,
        );
      });

      test('should handle events with missing optional fields', () async {
        // Arrange
        final minimalEvent = SecurityEvent(
          id: 'minimal-1',
          type: 'unknown',
          timestamp: DateTime.now(),
          success: true,
          riskScore: 0,
          severity: SecurityEventSeverity.low,
          requiresAttention: false,
          tags: [],
        );

        // Act & Assert
        expect(
          () => incidentService.analyzeSecurityEvent(minimalEvent),
          returnsNormally,
        );
      });
    });
  });
}

// Helper functions removed - using NotificationTestUtils.createMockRecord instead
