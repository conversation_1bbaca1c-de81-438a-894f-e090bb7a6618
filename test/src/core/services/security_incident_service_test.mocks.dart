// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in three_pay_group_litigation_platform/test/src/core/services/security_incident_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:http/http.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;
import 'package:pocketbase/pocketbase.dart' as _i2;
import 'package:three_pay_group_litigation_platform/src/core/models/security_event.dart'
    as _i7;
import 'package:three_pay_group_litigation_platform/src/core/services/enhanced_audit_service.dart'
    as _i6;
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart'
    as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakePocketBase_0 extends _i1.SmartFake implements _i2.PocketBase {
  _FakePocketBase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAuthStore_1 extends _i1.SmartFake implements _i2.AuthStore {
  _FakeAuthStore_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRecordAuth_2 extends _i1.SmartFake implements _i2.RecordAuth {
  _FakeRecordAuth_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRecordModel_3 extends _i1.SmartFake implements _i2.RecordModel {
  _FakeRecordModel_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResultList_4<M extends _i2.Jsonable> extends _i1.SmartFake
    implements _i2.ResultList<M> {
  _FakeResultList_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [PocketBaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPocketBaseService extends _i1.Mock implements _i3.PocketBaseService {
  MockPocketBaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.PocketBase get client =>
      (super.noSuchMethod(
            Invocation.getter(#client),
            returnValue: _FakePocketBase_0(this, Invocation.getter(#client)),
          )
          as _i2.PocketBase);

  @override
  _i2.PocketBase get pb =>
      (super.noSuchMethod(
            Invocation.getter(#pb),
            returnValue: _FakePocketBase_0(this, Invocation.getter(#pb)),
          )
          as _i2.PocketBase);

  @override
  _i2.AuthStore get authStore =>
      (super.noSuchMethod(
            Invocation.getter(#authStore),
            returnValue: _FakeAuthStore_1(this, Invocation.getter(#authStore)),
          )
          as _i2.AuthStore);

  @override
  bool get isSignedIn =>
      (super.noSuchMethod(Invocation.getter(#isSignedIn), returnValue: false)
          as bool);

  @override
  _i4.Future<void> init() =>
      (super.noSuchMethod(
            Invocation.method(#init, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.RecordAuth> signIn(String? email, String? password) =>
      (super.noSuchMethod(
            Invocation.method(#signIn, [email, password]),
            returnValue: _i4.Future<_i2.RecordAuth>.value(
              _FakeRecordAuth_2(
                this,
                Invocation.method(#signIn, [email, password]),
              ),
            ),
          )
          as _i4.Future<_i2.RecordAuth>);

  @override
  _i4.Future<_i2.RecordModel> signUp({
    required String? email,
    required String? password,
    required String? passwordConfirm,
    required String? userType,
    required String? name,
    String? firstName,
    String? lastName,
    String? level,
    String? status,
    Map<String, dynamic>? customData = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUp, [], {
              #email: email,
              #password: password,
              #passwordConfirm: passwordConfirm,
              #userType: userType,
              #name: name,
              #firstName: firstName,
              #lastName: lastName,
              #level: level,
              #status: status,
              #customData: customData,
            }),
            returnValue: _i4.Future<_i2.RecordModel>.value(
              _FakeRecordModel_3(
                this,
                Invocation.method(#signUp, [], {
                  #email: email,
                  #password: password,
                  #passwordConfirm: passwordConfirm,
                  #userType: userType,
                  #name: name,
                  #firstName: firstName,
                  #lastName: lastName,
                  #level: level,
                  #status: status,
                  #customData: customData,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.RecordModel>);

  @override
  _i4.Future<void> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> handleSessionExpiration() =>
      (super.noSuchMethod(
            Invocation.method(#handleSessionExpiration, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> refreshFCMToken() =>
      (super.noSuchMethod(
            Invocation.method(#refreshFCMToken, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> requestPasswordReset(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#requestPasswordReset, [email]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> confirmPasswordReset({
    required String? token,
    required String? password,
    required String? passwordConfirm,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#confirmPasswordReset, [], {
              #token: token,
              #password: password,
              #passwordConfirm: passwordConfirm,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> requestEmailVerification(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#requestEmailVerification, [email]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> confirmEmailVerification(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#confirmEmailVerification, [token]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> changePassword({
    required String? oldPassword,
    required String? newPassword,
    required String? newPasswordConfirm,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#changePassword, [], {
              #oldPassword: oldPassword,
              #newPassword: newPassword,
              #newPasswordConfirm: newPasswordConfirm,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> requestOTP(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#requestOTP, [email]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> verifyOTP(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#verifyOTP, [token]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<void> optOutAccount() =>
      (super.noSuchMethod(
            Invocation.method(#optOutAccount, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.RecordModel> createSolicitorProfile({
    required String? userId,
    required String? lawFirmName,
    required String? solicitorName,
    required String? position,
    required String? contactNumber,
    required String? firmAddress,
    required String? firmRegistrationNumber,
    String? puStatus = 'pending',
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createSolicitorProfile, [], {
              #userId: userId,
              #lawFirmName: lawFirmName,
              #solicitorName: solicitorName,
              #position: position,
              #contactNumber: contactNumber,
              #firmAddress: firmAddress,
              #firmRegistrationNumber: firmRegistrationNumber,
              #puStatus: puStatus,
            }),
            returnValue: _i4.Future<_i2.RecordModel>.value(
              _FakeRecordModel_3(
                this,
                Invocation.method(#createSolicitorProfile, [], {
                  #userId: userId,
                  #lawFirmName: lawFirmName,
                  #solicitorName: solicitorName,
                  #position: position,
                  #contactNumber: contactNumber,
                  #firmAddress: firmAddress,
                  #firmRegistrationNumber: firmRegistrationNumber,
                  #puStatus: puStatus,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.RecordModel>);

  @override
  _i4.Future<_i2.RecordModel> createCoFunderProfile({
    required String? userId,
    int? currentLevel = 1,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createCoFunderProfile, [], {
              #userId: userId,
              #currentLevel: currentLevel,
            }),
            returnValue: _i4.Future<_i2.RecordModel>.value(
              _FakeRecordModel_3(
                this,
                Invocation.method(#createCoFunderProfile, [], {
                  #userId: userId,
                  #currentLevel: currentLevel,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.RecordModel>);

  @override
  _i4.Future<_i2.RecordModel> createRecord({
    required String? collectionName,
    required Map<String, dynamic>? data,
    Map<String, dynamic>? query = const {},
    List<_i5.MultipartFile>? files = const [],
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createRecord, [], {
              #collectionName: collectionName,
              #data: data,
              #query: query,
              #files: files,
            }),
            returnValue: _i4.Future<_i2.RecordModel>.value(
              _FakeRecordModel_3(
                this,
                Invocation.method(#createRecord, [], {
                  #collectionName: collectionName,
                  #data: data,
                  #query: query,
                  #files: files,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.RecordModel>);

  @override
  _i4.Future<_i2.RecordModel> updateRecord({
    required String? collectionName,
    required String? recordId,
    required Map<String, dynamic>? data,
    Map<String, dynamic>? query = const {},
    List<_i5.MultipartFile>? files = const [],
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateRecord, [], {
              #collectionName: collectionName,
              #recordId: recordId,
              #data: data,
              #query: query,
              #files: files,
            }),
            returnValue: _i4.Future<_i2.RecordModel>.value(
              _FakeRecordModel_3(
                this,
                Invocation.method(#updateRecord, [], {
                  #collectionName: collectionName,
                  #recordId: recordId,
                  #data: data,
                  #query: query,
                  #files: files,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.RecordModel>);

  @override
  _i4.Future<List<_i2.RecordModel>> getFullList({
    required String? collectionName,
    int? batch = 200,
    String? filter,
    String? sort,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getFullList, [], {
              #collectionName: collectionName,
              #batch: batch,
              #filter: filter,
              #sort: sort,
            }),
            returnValue: _i4.Future<List<_i2.RecordModel>>.value(
              <_i2.RecordModel>[],
            ),
          )
          as _i4.Future<List<_i2.RecordModel>>);

  @override
  _i4.Future<_i2.ResultList<_i2.RecordModel>> getList({
    required String? collectionName,
    int? page = 1,
    int? perPage = 30,
    String? filter,
    String? sort,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getList, [], {
              #collectionName: collectionName,
              #page: page,
              #perPage: perPage,
              #filter: filter,
              #sort: sort,
            }),
            returnValue: _i4.Future<_i2.ResultList<_i2.RecordModel>>.value(
              _FakeResultList_4<_i2.RecordModel>(
                this,
                Invocation.method(#getList, [], {
                  #collectionName: collectionName,
                  #page: page,
                  #perPage: perPage,
                  #filter: filter,
                  #sort: sort,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.ResultList<_i2.RecordModel>>);

  @override
  _i4.Future<_i2.RecordModel> getOne({
    required String? collectionName,
    required String? recordId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getOne, [], {
              #collectionName: collectionName,
              #recordId: recordId,
            }),
            returnValue: _i4.Future<_i2.RecordModel>.value(
              _FakeRecordModel_3(
                this,
                Invocation.method(#getOne, [], {
                  #collectionName: collectionName,
                  #recordId: recordId,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.RecordModel>);

  @override
  _i4.Future<void> deleteRecord({
    required String? collectionName,
    required String? recordId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#deleteRecord, [], {
              #collectionName: collectionName,
              #recordId: recordId,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<Map<String, String>> uploadFileAndGetId({
    required String? targetCollectionName,
    required _i5.MultipartFile? multipartFile,
    Map<String, String>? body = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadFileAndGetId, [], {
              #targetCollectionName: targetCollectionName,
              #multipartFile: multipartFile,
              #body: body,
            }),
            returnValue: _i4.Future<Map<String, String>>.value(
              <String, String>{},
            ),
          )
          as _i4.Future<Map<String, String>>);
}

/// A class which mocks [EnhancedAuditService].
///
/// See the documentation for Mockito's code generation for more information.
class MockEnhancedAuditService extends _i1.Mock
    implements _i6.EnhancedAuditService {
  MockEnhancedAuditService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> logDocumentAccess({
    required String? documentId,
    required String? action,
    required bool? success,
    String? userId,
    String? userEmail,
    String? userRole,
    String? ipAddress,
    String? userAgent,
    String? sessionId,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logDocumentAccess, [], {
              #documentId: documentId,
              #action: action,
              #success: success,
              #userId: userId,
              #userEmail: userEmail,
              #userRole: userRole,
              #ipAddress: ipAddress,
              #userAgent: userAgent,
              #sessionId: sessionId,
              #errorMessage: errorMessage,
              #metadata: metadata,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> logAuthentication({
    required String? userId,
    required bool? success,
    String? userEmail,
    String? errorMessage,
    String? ipAddress,
    String? userAgent,
    String? sessionId,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logAuthentication, [], {
              #userId: userId,
              #success: success,
              #userEmail: userEmail,
              #errorMessage: errorMessage,
              #ipAddress: ipAddress,
              #userAgent: userAgent,
              #sessionId: sessionId,
              #metadata: metadata,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> logPermissionChange({
    required String? resourceId,
    required Map<String, dynamic>? changes,
    required bool? success,
    String? userId,
    String? userEmail,
    String? userRole,
    String? errorMessage,
    String? ipAddress,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logPermissionChange, [], {
              #resourceId: resourceId,
              #changes: changes,
              #success: success,
              #userId: userId,
              #userEmail: userEmail,
              #userRole: userRole,
              #errorMessage: errorMessage,
              #ipAddress: ipAddress,
              #metadata: metadata,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> logDataProcessing({
    required String? action,
    required String? dataType,
    required bool? success,
    String? userId,
    String? userEmail,
    String? userRole,
    String? errorMessage,
    String? ipAddress,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logDataProcessing, [], {
              #action: action,
              #dataType: dataType,
              #success: success,
              #userId: userId,
              #userEmail: userEmail,
              #userRole: userRole,
              #errorMessage: errorMessage,
              #ipAddress: ipAddress,
              #metadata: metadata,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> logSecurityEvent(_i7.SecurityEvent? event) =>
      (super.noSuchMethod(
            Invocation.method(#logSecurityEvent, [event]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<Map<String, dynamic>> generateComplianceReport({
    required DateTime? startDate,
    required DateTime? endDate,
    List<String>? complianceTags,
    String? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#generateComplianceReport, [], {
              #startDate: startDate,
              #endDate: endDate,
              #complianceTags: complianceTags,
              #userId: userId,
            }),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<bool> verifyAuditIntegrity({required String? auditId}) =>
      (super.noSuchMethod(
            Invocation.method(#verifyAuditIntegrity, [], {#auditId: auditId}),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  Map<String, dynamic> getStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getStatus, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}
