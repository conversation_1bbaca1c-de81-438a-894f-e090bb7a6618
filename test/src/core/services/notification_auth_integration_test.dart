import 'package:flutter_test/flutter_test.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/local_notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

void main() {
  group('Notification Authentication Integration Tests', () {
    setUpAll(() {
      // Logger service is automatically initialized when first used
    });

    group('Service Status and Configuration', () {
      test('should provide service status information', () {
        // Act
        final status = FirebaseApiService.getServiceStatus();

        // Assert
        expect(status, isA<Map<String, dynamic>>());
        expect(status.containsKey('isInitialized'), isTrue);
        expect(status.containsKey('hasPermission'), isTrue);
        expect(status.containsKey('hasToken'), isTrue);
      });

      test('should handle service initialization state', () {
        // Act
        final isInitialized = FirebaseApiService.isInitialized();
        final hasPermission = FirebaseApiService.hasPermission();
        final currentToken = FirebaseApiService.getCurrentToken();

        // Assert
        expect(isInitialized, isA<bool>());
        expect(hasPermission, isA<bool>());
        expect(currentToken, anyOf(isNull, isA<String>()));
      });

      test('should handle local notification service state', () {
        // Act
        final isInitialized = LocalNotificationService.isInitialized;
        final hasPermission = LocalNotificationService.isPermissionGranted;

        // Assert
        expect(isInitialized, isA<bool>());
        expect(hasPermission, isA<bool>());
      });
    });

    group('Notification Payload Handling', () {
      test('should create and parse notification payload correctly', () {
        // Arrange
        const type = 'claim_update';
        const id = 'claim_123';
        const route = '/claims/123';
        final additionalData = {
          'title': 'Claim Updated',
          'message': 'Your claim has been updated',
          'claimId': 'claim_123',
        };

        // Act
        final payload = LocalNotificationService.createNotificationPayload(
          type: type,
          id: id,
          route: route,
          additionalData: additionalData,
        );

        final parsedData = LocalNotificationService.parseNotificationPayload(
          payload,
        );

        // Assert
        expect(payload, isNotEmpty);
        expect(parsedData, isA<Map<String, dynamic>>());
        expect(parsedData['type'], equals(type));
        expect(parsedData['id'], equals(id));
        expect(parsedData['route'], equals(route));
        expect(parsedData['title'], equals('Claim Updated'));
        expect(parsedData['message'], equals('Your claim has been updated'));
        expect(parsedData['claimId'], equals('claim_123'));
      });

      test('should handle different notification types in payload', () {
        final testCases = [
          {
            'type': 'claim_update',
            'id': 'claim_456',
            'route': '/claims/456',
            'additionalData': {'claimStatus': 'approved'},
          },
          {
            'type': 'funding_opportunity',
            'id': 'funding_789',
            'route': '/funding/789',
            'additionalData': {'amount': '50000'},
          },
          {
            'type': 'message',
            'id': 'message_101',
            'route': '/messages/101',
            'additionalData': {'sender': 'Agent Smith'},
          },
          {
            'type': 'document_upload',
            'id': 'doc_202',
            'route': '/documents/202',
            'additionalData': {'fileName': 'contract.pdf'},
          },
        ];

        for (final testCase in testCases) {
          // Act
          final payload = LocalNotificationService.createNotificationPayload(
            type: testCase['type'] as String,
            id: testCase['id'] as String,
            route: testCase['route'] as String,
            additionalData: testCase['additionalData'] as Map<String, dynamic>,
          );

          final parsedData = LocalNotificationService.parseNotificationPayload(
            payload,
          );

          // Assert
          expect(parsedData['type'], equals(testCase['type']));
          expect(parsedData['id'], equals(testCase['id']));
          expect(parsedData['route'], equals(testCase['route']));

          final additionalData =
              testCase['additionalData'] as Map<String, dynamic>;
          for (final entry in additionalData.entries) {
            expect(parsedData[entry.key], equals(entry.value));
          }
        }
      });

      test('should handle empty or null payload gracefully', () {
        // Act & Assert
        expect(
          () => LocalNotificationService.parseNotificationPayload(''),
          returnsNormally,
        );

        expect(
          () =>
              LocalNotificationService.parseNotificationPayload('invalid_json'),
          returnsNormally,
        );

        // Should return empty map for invalid payloads
        final emptyResult = LocalNotificationService.parseNotificationPayload(
          '',
        );
        final invalidResult = LocalNotificationService.parseNotificationPayload(
          'invalid',
        );

        expect(emptyResult, isA<Map<String, dynamic>>());
        expect(invalidResult, isA<Map<String, dynamic>>());
      });
    });

    group('Authentication State Validation', () {
      test('should validate notification service configuration', () {
        // This test validates that the notification services are properly configured
        // for authentication integration without requiring actual authentication

        // Act
        final firebaseStatus = FirebaseApiService.getServiceStatus();

        // Assert - Check that status contains expected keys
        expect(firebaseStatus.containsKey('isInitialized'), isTrue);
        expect(firebaseStatus.containsKey('hasPermission'), isTrue);
        expect(firebaseStatus.containsKey('hasToken'), isTrue);

        // Validate that the service can handle status queries without errors
        expect(() => FirebaseApiService.isInitialized(), returnsNormally);
        expect(() => FirebaseApiService.hasPermission(), returnsNormally);
        expect(() => FirebaseApiService.getCurrentToken(), returnsNormally);
      });

      test('should handle notification channel configuration', () {
        // Test that notification channels are properly configured
        // This validates the setup without requiring actual notifications

        // Act & Assert - These should not throw errors
        expect(
          () => LocalNotificationService.CLAIMS_CHANNEL_ID,
          returnsNormally,
        );
        expect(
          () => LocalNotificationService.FUNDING_CHANNEL_ID,
          returnsNormally,
        );
        expect(
          () => LocalNotificationService.MESSAGES_CHANNEL_ID,
          returnsNormally,
        );
        expect(
          () => LocalNotificationService.DEFAULT_CHANNEL_ID,
          returnsNormally,
        );

        // Validate channel IDs are not empty
        expect(LocalNotificationService.CLAIMS_CHANNEL_ID, isNotEmpty);
        expect(LocalNotificationService.FUNDING_CHANNEL_ID, isNotEmpty);
        expect(LocalNotificationService.MESSAGES_CHANNEL_ID, isNotEmpty);
        expect(LocalNotificationService.DEFAULT_CHANNEL_ID, isNotEmpty);
      });
    });

    group('Error Handling and Resilience', () {
      test(
        'should handle service method calls gracefully when not initialized',
        () {
          // These tests ensure that service methods don't crash when called
          // before proper initialization, which is important for authentication flows

          // Act & Assert - Should not throw exceptions
          expect(() => FirebaseApiService.getServiceStatus(), returnsNormally);
          expect(() => FirebaseApiService.getCurrentToken(), returnsNormally);
          expect(() => LocalNotificationService.isInitialized, returnsNormally);
          expect(
            () => LocalNotificationService.isPermissionGranted,
            returnsNormally,
          );
        },
      );

      test('should validate notification payload structure', () {
        // Test payload validation for different scenarios
        final validPayload = LocalNotificationService.createNotificationPayload(
          type: 'test',
          id: 'test_id',
          route: '/test',
          additionalData: {'key': 'value'},
        );

        // Act
        final parsed = LocalNotificationService.parseNotificationPayload(
          validPayload,
        );

        // Assert
        expect(parsed, isA<Map<String, dynamic>>());
        expect(parsed.containsKey('type'), isTrue);
        expect(parsed.containsKey('id'), isTrue);
        expect(parsed.containsKey('route'), isTrue);
      });
    });

    group('Integration Readiness', () {
      test(
        'should have proper service interfaces for authentication integration',
        () {
          // This test validates that the services expose the necessary methods
          // for authentication integration

          // Firebase API Service methods
          expect(() => FirebaseApiService.isInitialized(), returnsNormally);
          expect(() => FirebaseApiService.hasPermission(), returnsNormally);
          expect(() => FirebaseApiService.getCurrentToken(), returnsNormally);
          expect(() => FirebaseApiService.getServiceStatus(), returnsNormally);

          // Local Notification Service properties
          expect(() => LocalNotificationService.isInitialized, returnsNormally);
          expect(
            () => LocalNotificationService.isPermissionGranted,
            returnsNormally,
          );
        },
      );

      test('should support notification type routing', () {
        // Test that different notification types map to appropriate routes
        final notificationTypes = [
          'claim_update',
          'claim_status_change',
          'claim_milestone',
          'funding_opportunity',
          'funding_application_status',
          'investment_opportunity',
          'message',
          'agent_message',
          'communication',
          'document_upload',
          'document_approval',
          'document_expiration',
          'profile_change',
          'permission_change',
          'security_alert',
        ];

        for (final type in notificationTypes) {
          // Act
          final payload = LocalNotificationService.createNotificationPayload(
            type: type,
            id: 'test_id',
            route: '/test',
            additionalData: {},
          );

          final parsed = LocalNotificationService.parseNotificationPayload(
            payload,
          );

          // Assert
          expect(parsed['type'], equals(type));
          expect(parsed, containsPair('type', type));
        }
      });

      test('should handle authentication state changes gracefully', () {
        // Test that services can handle authentication state changes
        // without crashing or losing configuration

        // Act & Assert - These should work regardless of auth state
        expect(() => FirebaseApiService.getServiceStatus(), returnsNormally);
        expect(
          () => LocalNotificationService.createNotificationPayload(
            type: 'test',
            id: 'test',
            route: '/test',
            additionalData: {},
          ),
          returnsNormally,
        );
      });
    });

    group('Logging and Monitoring', () {
      test('should support logging for authentication events', () {
        // Test that logger service is available for notification auth events

        // Act & Assert
        expect(
          () => LoggerService.info('Test notification auth event'),
          returnsNormally,
        );
        expect(
          () => LoggerService.warning('Test notification auth warning'),
          returnsNormally,
        );
        expect(
          () => LoggerService.error(
            'Test notification auth error',
            Exception('test'),
          ),
          returnsNormally,
        );
      });

      test('should provide debugging information', () {
        // Test that services provide useful debugging information

        // Act
        final firebaseStatus = FirebaseApiService.getServiceStatus();

        // Assert
        expect(firebaseStatus, isA<Map<String, dynamic>>());
        expect(firebaseStatus.keys.length, greaterThan(0));

        // Should contain useful debugging information
        for (final key in firebaseStatus.keys) {
          expect(key, isA<String>());
          expect(key, isNotEmpty);
        }
      });
    });
  });
}
