import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/credential_management_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/encryption_service.dart';
import '../../../utils/notification_test_utils.dart';

// Generate mocks
@GenerateMocks([PocketBaseService, EncryptionService])
import 'credential_management_service_test.mocks.dart';

void main() {
  group('CredentialManagementService', () {
    late CredentialManagementService credentialService;
    late MockPocketBaseService mockPocketBaseService;
    late MockEncryptionService mockEncryptionService;

    setUp(() {
      credentialService = CredentialManagementService();
      mockPocketBaseService = MockPocketBaseService();
      mockEncryptionService = MockEncryptionService();

      // LoggerService is ready to use without initialization
    });

    group('Credential Storage', () {
      test(
        'should store Google service account credentials successfully',
        () async {
          // Arrange
          final validCredentials = {
            'type': 'service_account',
            'project_id': 'test-project',
            'private_key_id': 'key-id',
            'private_key':
                '-----BEGIN PRIVATE KEY-----\ntest-key\n-----END PRIVATE KEY-----',
            'client_email': '<EMAIL>',
            'client_id': 'client-id',
            'auth_uri': 'https://accounts.google.com/o/oauth2/auth',
            'token_uri': 'https://oauth2.googleapis.com/token',
          };

          final mockEncryptionResult = EncryptionResult(
            encryptedData: 'encrypted-data',
            iv: 'iv-data',
            salt: 'salt-data',
            checksum: 'checksum',
            algorithm: 'AES-256-GCM-SIMULATED',
            keyId: 'key-id',
            metadata: {},
            timestamp: DateTime.now(),
          );

          when(
            mockEncryptionService.encryptData(
              any,
              key: anyNamed('key'),
              metadata: anyNamed('metadata'),
            ),
          ).thenAnswer((_) async => mockEncryptionResult);

          when(
            mockPocketBaseService.createRecord(
              collectionName: 'secure_credentials',
              data: any,
            ),
          ).thenAnswer(
            (_) async => NotificationTestUtils.createMockRecord(
              id:'test_credential_id',
              data: {'encrypted_data': 'test_encrypted'},
              collectionName: 'secure_credentials',
            ),
          );

          // Act
          final credentialId = await credentialService.storeCredentials(
            credentialId: 'test-google-creds',
            credentials: validCredentials,
            type: CredentialType.googleServiceAccount,
            description: 'Test Google service account',
          );

          // Assert
          expect(credentialId, equals('test-google-creds'));
          verify(
            mockEncryptionService.encryptData(
              jsonEncode(validCredentials),
              key: any,
              metadata: any,
            ),
          ).called(1);
          verify(
            mockPocketBaseService.createRecord(
              collectionName: 'secure_credentials',
              data: any,
            ),
          ).called(1);
        },
      );

      test('should validate Google service account credentials', () async {
        // Arrange
        final invalidCredentials = {
          'type': 'service_account',
          // Missing required fields
        };

        // Act & Assert
        expect(
          () => credentialService.storeCredentials(
            credentialId: 'invalid-creds',
            credentials: invalidCredentials,
            type: CredentialType.googleServiceAccount,
          ),
          throwsA(isA<CredentialException>()),
        );
      });

      test('should validate API key credentials', () async {
        // Arrange
        final validApiKeyCredentials = {'api_key': 'valid-api-key-123'};
        final invalidApiKeyCredentials = {'api_key': ''};

        final mockEncryptionResult = EncryptionResult(
          encryptedData: 'encrypted-data',
          iv: 'iv-data',
          salt: 'salt-data',
          checksum: 'checksum',
          algorithm: 'AES-256-GCM-SIMULATED',
          keyId: 'key-id',
          metadata: {},
          timestamp: DateTime.now(),
        );

        when(
          mockEncryptionService.encryptData(
            any,
            key: anyNamed('key'),
            metadata: anyNamed('metadata'),
          ),
        ).thenAnswer((_) async => mockEncryptionResult);

        when(
          mockPocketBaseService.createRecord(
            collectionName: 'secure_credentials',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: 'test_credential_id',
            data: {'encrypted_data': 'test_encrypted'},
            collectionName: 'secure_credentials',
          ),
        );

        // Act & Assert - Valid credentials should work
        expect(
          () => credentialService.storeCredentials(
            credentialId: 'valid-api-key',
            credentials: validApiKeyCredentials,
            type: CredentialType.apiKey,
          ),
          returnsNormally,
        );

        // Invalid credentials should throw
        expect(
          () => credentialService.storeCredentials(
            credentialId: 'invalid-api-key',
            credentials: invalidApiKeyCredentials,
            type: CredentialType.apiKey,
          ),
          throwsA(isA<CredentialException>()),
        );
      });

      test('should set expiry date when provided', () async {
        // Arrange
        final credentials = {'api_key': 'test-key'};
        final expiryDuration = Duration(days: 30);

        final mockEncryptionResult = EncryptionResult(
          encryptedData: 'encrypted-data',
          iv: 'iv-data',
          salt: 'salt-data',
          checksum: 'checksum',
          algorithm: 'AES-256-GCM-SIMULATED',
          keyId: 'key-id',
          metadata: {},
          timestamp: DateTime.now(),
        );

        when(
          mockEncryptionService.encryptData(
            any,
            key: anyNamed('key'),
            metadata: anyNamed('metadata'),
          ),
        ).thenAnswer((_) async => mockEncryptionResult);

        when(
          mockPocketBaseService.createRecord(
            collectionName: 'secure_credentials',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord( 
            id: 'test_credential_id',
            data: {'encrypted_data': 'test_encrypted'},
            collectionName: 'secure_credentials',
          ),
        );

        // Act
        await credentialService.storeCredentials(
          credentialId: 'expiring-creds',
          credentials: credentials,
          type: CredentialType.apiKey,
          expiryDuration: expiryDuration,
        );

        // Assert
        final capturedData =
            verify(
                  mockPocketBaseService.createRecord(
                    collectionName: 'secure_credentials',
                    data: captureAny,
                  ),
                ).captured.first
                as Map<String, dynamic>;

        expect(capturedData['expires_at'], isNotNull);
      });
    });

    group('Credential Retrieval', () {
      test('should retrieve and decrypt credentials successfully', () async {
        // Arrange
        final originalCredentials = {'api_key': 'secret-key-123'};
        final credentialId = 'test-credential';

        
        when(
          mockPocketBaseService.getFullList(
            collectionName: 'secure_credentials',
            filter: any,
          ),
        ).thenAnswer(
          (_) async => [
            NotificationTestUtils.createMockRecord( 
              id: "test_id",
              data: {},
              collectionName: "test_collection",
            ),
          ],
        );

        when(
          mockEncryptionService.decryptData(any, any),
        ).thenAnswer((_) async => jsonEncode(originalCredentials));

        // Act
        final retrievedCredentials = await credentialService
            .retrieveCredentials(credentialId);

        // Assert
        expect(retrievedCredentials, equals(originalCredentials));
        verify(mockEncryptionService.decryptData(any, any)).called(1);
      });

      test('should throw exception for non-existent credentials', () async {
        // Arrange
        when(
          mockPocketBaseService.getFullList(
            collectionName: 'secure_credentials',
            filter: any,
          ),
        ).thenAnswer((_) async => []);

        // Act & Assert
        expect(
          () => credentialService.retrieveCredentials('non-existent'),
          throwsA(isA<CredentialException>()),
        );
      });

      test('should validate credential access permissions', () async {
        // Arrange
        final credentialId = 'restricted-credential';
        

        when(
          mockPocketBaseService.getFullList(
            collectionName: 'secure_credentials',
            filter: any,
          ),
        ).thenAnswer(
          (_) async => [
            NotificationTestUtils.createMockRecord( 
              id: "test_id",
              data: {},
              collectionName: "test_collection",
            ),
          ],
        );

        when(mockPocketBaseService.currentUser).thenReturn(
          NotificationTestUtils.createMockUserRecord(
            id: 'current-user-id',
            email: '<EMAIL>',
          ),
        );

        // Act & Assert
        expect(
          () => credentialService.retrieveCredentials(credentialId),
          throwsA(isA<CredentialException>()),
        );
      });

      test('should check credential expiry', () async {
        // Arrange
        final credentialId = 'expired-credential';
       

        when(
          mockPocketBaseService.getFullList(
            collectionName: 'secure_credentials',
            filter: any,
          ),
        ).thenAnswer(
          (_) async => [
            NotificationTestUtils.createMockRecord( 
              id: "test_id",
              data: {},
              collectionName: "test_collection",
            ),
          ],
        );

        // Act & Assert
        expect(
          () => credentialService.retrieveCredentials(credentialId),
          throwsA(isA<CredentialException>()),
        );
      });

      test('should check if credential is active', () async {
        // Arrange
        final credentialId = 'inactive-credential';
        final mockRecord = NotificationTestUtils.createMockRecord( id: "test_id", data: {}, collectionName: "secure_credentials");
        when(mockRecord.data).thenReturn({
          'id': credentialId,
          'type': 'api_key',
          'encrypted_data': 'encrypted-data',
          'iv': 'iv-data',
          'salt': 'salt-data',
          'checksum': 'checksum',
          'key_id': 'key-id',
          'is_active': false, // Inactive
          'created_at': DateTime.now().toIso8601String(),
        });

        when(
          mockPocketBaseService.getFullList(
            collectionName: 'secure_credentials',
            filter: any,
          ),
        ).thenAnswer(
          (_) async => [
            NotificationTestUtils.createMockRecord( 
              id: "test_id",
              data: {},
              collectionName: "test_collection",
            ),
          ],
        );

        // Act & Assert
        expect(
          () => credentialService.retrieveCredentials(credentialId),
          throwsA(isA<CredentialException>()),
        );
      });
    });

    group('Credential Updates', () {
      test('should update credentials successfully', () async {
        // Arrange
        final credentialId = 'update-test';
        final newCredentials = {'api_key': 'new-secret-key'};

        final existingRecord = NotificationTestUtils.createMockRecord(id: "test_id", data: {}, collectionName: "secure_credentials");
        when(existingRecord.data).thenReturn({
          'id': credentialId,
          'type': 'api_key',
          'encrypted_data': 'old-encrypted-data',
          'iv': 'old-iv',
          'salt': 'old-salt',
          'checksum': 'old-checksum',
          'key_id': 'old-key-id',
          'is_active': true,
          'created_at':
              DateTime.now().subtract(Duration(days: 1)).toIso8601String(),
        });

        final mockEncryptionResult = EncryptionResult(
          encryptedData: 'new-encrypted-data',
          iv: 'new-iv',
          salt: 'new-salt',
          checksum: 'new-checksum',
          algorithm: 'AES-256-GCM-SIMULATED',
          keyId: 'new-key-id',
          metadata: {},
          timestamp: DateTime.now(),
        );

        when(
          mockPocketBaseService.getFullList(
            collectionName: 'secure_credentials',
            filter: any,
          ),
        ).thenAnswer((_) async => [existingRecord]);

        when(
          mockEncryptionService.encryptData(
            any,
            key: anyNamed('key'),
            metadata: anyNamed('metadata'),
          ),
        ).thenAnswer((_) async => mockEncryptionResult);

        when(
          mockPocketBaseService.updateRecord(
            collectionName: 'secure_credentials',
            recordId: credentialId,
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord( 
            id: "test_id",
            data: {},
            collectionName: "test_collection",
          ),
        );

        // Act
        await credentialService.updateCredentials(
          credentialId: credentialId,
          newCredentials: newCredentials,
        );

        // Assert
        verify(
          mockEncryptionService.encryptData(
            jsonEncode(newCredentials),
            key: any,
            metadata: any,
          ),
        ).called(1);
        verify(
          mockPocketBaseService.updateRecord(
            collectionName: 'secure_credentials',
            recordId: credentialId,
            data: any,
          ),
        ).called(1);
      });
    });

    group('Credential Deletion', () {
      test('should delete credentials successfully', () async {
        // Arrange
        final credentialId = 'delete-test';
        final existingRecord = NotificationTestUtils.createMockRecord( id: "test_id", data: {}, collectionName: "secure_credentials");
        when(existingRecord.data).thenReturn({
          'id': credentialId,
          'type': 'api_key',
          'is_active': true,
        });

        when(
          mockPocketBaseService.getFullList(
            collectionName: 'secure_credentials',
            filter: any,
          ),
        ).thenAnswer((_) async => [existingRecord]);

        when(
          mockPocketBaseService.deleteRecord(
            collectionName: 'secure_credentials',
            recordId: credentialId,
          ),
        ).thenAnswer((_) async => {});

        // Act
        await credentialService.deleteCredentials(credentialId);

        // Assert
        verify(
          mockPocketBaseService.deleteRecord(
            collectionName: 'secure_credentials',
            recordId: credentialId,
          ),
        ).called(1);
      });
    });

    group('Credential Listing', () {
      test('should list credentials with filters', () async {
        // Arrange
        final mockRecords = [
          NotificationTestUtils.createMockRecord(
            id: 'cred1',
            data: {'type': 'api_key', 'is_active': true},
            collectionName: 'secure_credentials',
          ),
          NotificationTestUtils.createMockRecord(
            id: 'cred2',
            data: {'type': 'oauth2', 'is_active': true},
            collectionName: 'secure_credentials',
          ),
          NotificationTestUtils.createMockRecord(
            id: 'cred3',
            data: {'type': 'api_key', 'is_active': false},
            collectionName: 'secure_credentials',
          ),
        ];

        when(
          mockPocketBaseService.getFullList(
            collectionName: 'secure_credentials',
            filter: any,
          ),
        ).thenAnswer((_) async => mockRecords);

        // Act
        final credentials = await credentialService.listCredentials(
          type: CredentialType.apiKey,
          activeOnly: true,
        );

        // Assert
        expect(credentials.length, equals(3)); // All records returned from mock
        verify(
          mockPocketBaseService.getFullList(
            collectionName: 'secure_credentials',
            filter: 'type = "api_key" && is_active = true',
          ),
        ).called(1);
      });
    });

    group('Credential Rotation', () {
      test('should rotate credentials successfully', () async {
        // Arrange
        final credentialId = 'rotate-test';
        final newCredentials = {'api_key': 'rotated-key'};

        final existingRecord = NotificationTestUtils.createMockRecord(id: "test_id", data: {}, collectionName: "secure_credentials");
        when(existingRecord.data).thenReturn({
          'id': credentialId,
          'type': 'api_key',
          'description': 'Original credential',
          'allowed_users': ['user1', 'user2'],
          'is_active': true,
        });

        final mockEncryptionResult = EncryptionResult(
          encryptedData: 'rotated-encrypted-data',
          iv: 'rotated-iv',
          salt: 'rotated-salt',
          checksum: 'rotated-checksum',
          algorithm: 'AES-256-GCM-SIMULATED',
          keyId: 'rotated-key-id',
          metadata: {},
          timestamp: DateTime.now(),
        );

        when(
          mockPocketBaseService.getFullList(
            collectionName: 'secure_credentials',
            filter: any,
          ),
        ).thenAnswer((_) async => [existingRecord]);

        when(
          mockPocketBaseService.updateRecord(
            collectionName: 'secure_credentials',
            recordId: credentialId,
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord( 
            id: "test_id",
            data: {},
            collectionName: "test_collection",
          ),
        );

        when(
          mockEncryptionService.encryptData(
            any,
            key: anyNamed('key'),
            metadata: anyNamed('metadata'),
          ),
        ).thenAnswer((_) async => mockEncryptionResult);

        when(
          mockPocketBaseService.createRecord(
            collectionName: 'secure_credentials',
            data: any,
          ),
        ).thenAnswer((_) async => NotificationTestUtils.createMockRecord(
          id: "new_credential_id",
          data: {},
          collectionName: "secure_credentials",
        ));

        // Act
        final newCredentialId = await credentialService.rotateCredentials(
          credentialId: credentialId,
          newCredentials: newCredentials,
        );

        // Assert
        expect(newCredentialId, startsWith(credentialId));
        expect(newCredentialId, isNot(equals(credentialId)));
        verify(
          mockPocketBaseService.updateRecord(
            collectionName: 'secure_credentials',
            recordId: credentialId,
            data: {'is_active': false, 'updated_at': any},
          ),
        ).called(1);
        verify(
          mockPocketBaseService.createRecord(
            collectionName: 'secure_credentials',
            data: any,
          ),
        ).called(1);
      });
    });

    group('Service Status', () {
      test('should return correct service status', () {
        // Act
        final status = credentialService.getStatus();

        // Assert
        expect(status, isA<Map<String, dynamic>>());
        expect(status['service_initialized'], isTrue);
        expect(status.containsKey('cached_credentials'), isTrue);
        expect(status['encryption_service_available'], isTrue);
        expect(status.containsKey('timestamp'), isTrue);
      });

      test('should clear cache correctly', () {
        // Act & Assert
        expect(() => credentialService.clearCache(), returnsNormally);
      });
    });
  });
}

// Helper functions and mock classes removed - using NotificationTestUtils.createMockRecord instead
