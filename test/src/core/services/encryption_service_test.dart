import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/encryption_service.dart';

void main() {
  group('EncryptionService', () {
    late EncryptionService encryptionService;

    setUp(() {
      encryptionService = EncryptionService();
      // LoggerService is ready to use without initialization
    });

    group('Data Encryption', () {
      test('should encrypt and decrypt text data successfully', () async {
        // Arrange
        const testData = 'This is sensitive test data';
        const encryptionKey = 'test-encryption-key-32-characters';

        // Act
        final encryptionResult = await encryptionService.encryptData(
          testData,
          key: encryptionKey,
        );
        final decryptedData = await encryptionService.decryptData(
          encryptionResult,
          encryptionKey,
        );

        // Assert
        expect(decryptedData, equals(testData));
        expect(encryptionResult.encryptedData, isNotEmpty);
        expect(encryptionResult.iv, isNotEmpty);
        expect(encryptionResult.salt, isNotEmpty);
        expect(encryptionResult.checksum, isNotEmpty);
        expect(encryptionResult.keyId, isNotEmpty);
      });

      test('should generate different encrypted data for same input', () async {
        // Arrange
        const testData = 'Same input data';
        const encryptionKey = 'test-encryption-key-32-characters';

        // Act
        final result1 = await encryptionService.encryptData(
          testData,
          key: encryptionKey,
        );
        final result2 = await encryptionService.encryptData(
          testData,
          key: encryptionKey,
        );

        // Assert
        expect(result1.encryptedData, isNot(equals(result2.encryptedData)));
        expect(result1.iv, isNot(equals(result2.iv)));
        expect(result1.salt, isNot(equals(result2.salt)));
      });

      test('should include metadata in encryption result', () async {
        // Arrange
        const testData = 'Test data with metadata';
        const metadata = {'source': 'test', 'type': 'document'};

        // Act
        final result = await encryptionService.encryptData(
          testData,
          metadata: metadata,
        );

        // Assert
        expect(result.metadata, equals(metadata));
        expect(result.timestamp, isNotNull);
        expect(result.algorithm, equals('AES-256-GCM-SIMULATED'));
      });

      test('should fail decryption with wrong key', () async {
        // Arrange
        const testData = 'Sensitive data';
        const correctKey = 'correct-key-32-characters-long';
        const wrongKey = 'wrong-key-32-characters-long---';

        final encryptionResult = await encryptionService.encryptData(
          testData,
          key: correctKey,
        );

        // Act & Assert
        expect(
          () => encryptionService.decryptData(encryptionResult, wrongKey),
          throwsA(isA<EncryptionException>()),
        );
      });

      test('should detect data tampering', () async {
        // Arrange
        const testData = 'Original data';
        const encryptionKey = 'test-key-32-characters-long----';

        final originalResult = await encryptionService.encryptData(
          testData,
          key: encryptionKey,
        );

        // Tamper with encrypted data
        final tamperedResult = EncryptionResult(
          encryptedData: 'tampered-data',
          iv: originalResult.iv,
          salt: originalResult.salt,
          checksum: originalResult.checksum,
          algorithm: originalResult.algorithm,
          keyId: originalResult.keyId,
          metadata: originalResult.metadata,
          timestamp: originalResult.timestamp,
        );

        // Act & Assert
        expect(
          () => encryptionService.decryptData(tamperedResult, encryptionKey),
          throwsA(isA<EncryptionException>()),
        );
      });
    });

    group('File Encryption', () {
      test('should encrypt and decrypt file data successfully', () async {
        // Arrange
        final testFileData = Uint8List.fromList([
          1,
          2,
          3,
          4,
          5,
          6,
          7,
          8,
          9,
          10,
        ]);
        const encryptionKey = 'file-encryption-key-32-chars---';
        const filename = 'test-document.pdf';

        // Act
        final encryptionResult = await encryptionService.encryptFile(
          testFileData,
          key: encryptionKey,
          filename: filename,
        );
        final decryptedData = await encryptionService.decryptFile(
          encryptionResult,
          encryptionKey,
        );

        // Assert
        expect(decryptedData, equals(testFileData));
        expect(
          encryptionResult.metadata['original_filename'],
          equals(filename),
        );
        expect(
          encryptionResult.metadata['file_size'],
          equals(testFileData.length),
        );
      });

      test('should handle large file encryption', () async {
        // Arrange
        final largeFileData = Uint8List(1024 * 1024); // 1MB file
        for (int i = 0; i < largeFileData.length; i++) {
          largeFileData[i] = i % 256;
        }
        const encryptionKey = 'large-file-key-32-characters---';

        // Act
        final encryptionResult = await encryptionService.encryptFile(
          largeFileData,
          key: encryptionKey,
          filename: 'large-file.bin',
        );
        final decryptedData = await encryptionService.decryptFile(
          encryptionResult,
          encryptionKey,
        );

        // Assert
        expect(decryptedData.length, equals(largeFileData.length));
        expect(decryptedData, equals(largeFileData));
      });

      test('should include file metadata in encryption result', () async {
        // Arrange
        final fileData = Uint8List.fromList([1, 2, 3, 4, 5]);
        const filename = 'document.pdf';
        const metadata = {'category': 'legal', 'confidential': true};

        // Act
        final result = await encryptionService.encryptFile(
          fileData,
          filename: filename,
          metadata: metadata,
        );

        // Assert
        expect(result.metadata['original_filename'], equals(filename));
        expect(result.metadata['file_size'], equals(fileData.length));
        expect(result.metadata['category'], equals('legal'));
        expect(result.metadata['confidential'], equals(true));
      });
    });

    group('Encryption Result Validation', () {
      test('should validate correct encryption result', () async {
        // Arrange
        const testData = 'Test validation data';
        final result = await encryptionService.encryptData(testData);

        // Act
        final isValid = encryptionService.validateEncryptionResult(result);

        // Assert
        expect(isValid, isTrue);
      });

      test('should reject encryption result with missing fields', () {
        // Arrange
        final invalidResult = EncryptionResult(
          encryptedData: '', // Empty required field
          iv: 'valid-iv',
          salt: 'valid-salt',
          checksum: 'valid-checksum',
          algorithm: 'AES-256-GCM-SIMULATED',
          keyId: 'valid-key-id',
          metadata: {},
          timestamp: DateTime.now(),
        );

        // Act
        final isValid = encryptionService.validateEncryptionResult(
          invalidResult,
        );

        // Assert
        expect(isValid, isFalse);
      });

      test('should reject encryption result with invalid base64', () {
        // Arrange
        final invalidResult = EncryptionResult(
          encryptedData: 'invalid-base64!@#',
          iv: 'valid-iv-base64==',
          salt: 'valid-salt-base64==',
          checksum: 'valid-checksum',
          algorithm: 'AES-256-GCM-SIMULATED',
          keyId: 'valid-key-id',
          metadata: {},
          timestamp: DateTime.now(),
        );

        // Act
        final isValid = encryptionService.validateEncryptionResult(
          invalidResult,
        );

        // Assert
        expect(isValid, isFalse);
      });

      test('should reject encryption result with future timestamp', () {
        // Arrange
        final futureResult = EncryptionResult(
          encryptedData: 'dGVzdA==', // Valid base64
          iv: 'aXY=', // Valid base64
          salt: 'c2FsdA==', // Valid base64
          checksum: 'valid-checksum',
          algorithm: 'AES-256-GCM-SIMULATED',
          keyId: 'valid-key-id',
          metadata: {},
          timestamp: DateTime.now().add(Duration(hours: 1)), // Future timestamp
        );

        // Act
        final isValid = encryptionService.validateEncryptionResult(
          futureResult,
        );

        // Assert
        expect(isValid, isFalse);
      });
    });

    group('Key Management', () {
      test('should generate unique keys for different encryptions', () async {
        // Arrange
        const testData = 'Same data, different keys';

        // Act
        final result1 = await encryptionService.encryptData(testData);
        final result2 = await encryptionService.encryptData(testData);

        // Assert
        expect(result1.keyId, isNot(equals(result2.keyId)));
      });

      test('should use provided key consistently', () async {
        // Arrange
        const testData = 'Test data';
        const providedKey = 'consistent-key-32-characters--';

        // Act
        final result1 = await encryptionService.encryptData(
          testData,
          key: providedKey,
        );
        final result2 = await encryptionService.encryptData(
          testData,
          key: providedKey,
        );

        // Assert
        expect(result1.keyId, equals(result2.keyId));
      });
    });

    group('Error Handling', () {
      test('should handle empty data encryption', () async {
        // Arrange
        const emptyData = '';

        // Act & Assert
        expect(() => encryptionService.encryptData(emptyData), returnsNormally);
      });

      test('should handle null metadata gracefully', () async {
        // Arrange
        const testData = 'Test data';

        // Act
        final result = await encryptionService.encryptData(
          testData,
          metadata: null,
        );

        // Assert
        expect(result.metadata, isEmpty);
      });

      test('should throw exception for corrupted encryption result', () async {
        // Arrange
        final corruptedResult = EncryptionResult(
          encryptedData: 'corrupted',
          iv: 'corrupted',
          salt: 'corrupted',
          checksum: 'wrong-checksum',
          algorithm: 'AES-256-GCM-SIMULATED',
          keyId: 'key-id',
          metadata: {},
          timestamp: DateTime.now(),
        );

        // Act & Assert
        expect(
          () => encryptionService.decryptData(corruptedResult, 'any-key'),
          throwsA(isA<EncryptionException>()),
        );
      });
    });

    group('Service Status', () {
      test('should return correct encryption service status', () {
        // Act
        final status = encryptionService.getEncryptionStatus();

        // Assert
        expect(status, isA<Map<String, dynamic>>());
        expect(status['service_initialized'], isTrue);
        expect(status['algorithm'], equals('AES-256-GCM-SIMULATED'));
        expect(status.containsKey('key_length'), isTrue);
        expect(status.containsKey('iv_length'), isTrue);
        expect(status.containsKey('salt_length'), isTrue);
        expect(status.containsKey('timestamp'), isTrue);
      });
    });

    group('Serialization', () {
      test('should serialize and deserialize encryption result', () async {
        // Arrange
        const testData = 'Serialization test data';
        final originalResult = await encryptionService.encryptData(testData);

        // Act
        final json = originalResult.toJson();
        final deserializedResult = EncryptionResult.fromJson(json);

        // Assert
        expect(
          deserializedResult.encryptedData,
          equals(originalResult.encryptedData),
        );
        expect(deserializedResult.iv, equals(originalResult.iv));
        expect(deserializedResult.salt, equals(originalResult.salt));
        expect(deserializedResult.checksum, equals(originalResult.checksum));
        expect(deserializedResult.algorithm, equals(originalResult.algorithm));
        expect(deserializedResult.keyId, equals(originalResult.keyId));
        expect(deserializedResult.metadata, equals(originalResult.metadata));
        expect(deserializedResult.timestamp, equals(originalResult.timestamp));
      });

      test('should handle JSON serialization with complex metadata', () async {
        // Arrange
        const testData = 'Complex metadata test';
        final complexMetadata = {
          'nested': {'key': 'value'},
          'list': [1, 2, 3],
          'boolean': true,
          'number': 42.5,
        };

        final result = await encryptionService.encryptData(
          testData,
          metadata: complexMetadata,
        );

        // Act
        final json = result.toJson();
        final deserializedResult = EncryptionResult.fromJson(json);

        // Assert
        expect(deserializedResult.metadata, equals(complexMetadata));
      });
    });
  });
}
