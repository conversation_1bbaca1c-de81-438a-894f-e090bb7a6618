import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/enhanced_audit_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/security_event.dart';
import '../../../utils/notification_test_utils.dart';

// Generate mocks
@GenerateMocks([PocketBaseService])
import 'enhanced_audit_service_test.mocks.dart';

void main() {
  group('EnhancedAuditService', () {
    late EnhancedAuditService auditService;
    late MockPocketBaseService mockPocketBaseService;

    setUp(() {
      auditService = EnhancedAuditService();
      mockPocketBaseService = MockPocketBaseService();

      // LoggerService is ready to use without initialization
    });

    group('Initialization', () {
      test('should initialize successfully', () async {
        // Act & Assert
        expect(() => auditService.initialize(), returnsNormally);
      });

      test('should start batch processing on initialization', () async {
        // Act
        await auditService.initialize();
        final status = auditService.getStatus();

        // Assert
        expect(status['service_initialized'], isTrue);
        expect(status.containsKey('pending_audit_entries'), isTrue);
        expect(status.containsKey('pending_security_events'), isTrue);
      });
    });

    group('Document Access Logging', () {
      test('should log successful document access', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(
            collectionName: 'audit_logs',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "test_collection",
          ),
        );

        // Act
        await auditService.logDocumentAccess(
          documentId: 'doc-123',
          action: 'view',
          success: true,
          userId: 'user-456',
          userEmail: '<EMAIL>',
          userRole: 'solicitor',
          ipAddress: '***********',
          metadata: {'file_size': 1024},
        );

        // Allow batch processing
        await Future.delayed(Duration(milliseconds: 100));

        // Assert
        final status = auditService.getStatus();
        expect(status['pending_audit_entries'], greaterThan(0));
      });

      test('should log failed document access with error details', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(
            collectionName: 'audit_logs',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: 'audit_log_id',
            data: {'action': 'document_access'},
            collectionName: 'audit_logs',
          ),
        );

        // Act
        await auditService.logDocumentAccess(
          documentId: 'doc-123',
          action: 'download',
          success: false,
          userId: 'user-456',
          errorMessage: 'Access denied',
          ipAddress: '***********',
        );

        // Assert
        final status = auditService.getStatus();
        expect(status['pending_audit_entries'], greaterThan(0));
      });

      test('should include digital signature in audit entries', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(
            collectionName: 'audit_logs',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: 'audit_log_id',
            data: {'action': 'document_access'},
            collectionName: 'audit_logs',
          ),
        );

        // Act
        await auditService.logDocumentAccess(
          documentId: 'doc-123',
          action: 'modify',
          success: true,
          userId: 'user-456',
        );

        // Allow batch processing
        await Future.delayed(Duration(milliseconds: 100));

        // Assert - Digital signature should be added to metadata
        final status = auditService.getStatus();
        expect(status['pending_audit_entries'], greaterThan(0));
      });
    });

    group('Authentication Logging', () {
      test('should log successful authentication', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(collectionName: any, data: any),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: 'audit_log_id',
            data: {'action': 'authentication'},
            collectionName: 'audit_logs',
          ),
        );

        // Act
        await auditService.logAuthentication(
          userId: 'user-123',
          success: true,
          userEmail: '<EMAIL>',
          ipAddress: '***********',
          sessionId: 'session-456',
        );

        // Assert
        final status = auditService.getStatus();
        expect(status['pending_audit_entries'], greaterThan(0));
        expect(status['pending_security_events'], greaterThan(0));
      });

      test('should log failed authentication with security event', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(collectionName: any, data: any),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "test_collection",
          ),
        );

        // Act
        await auditService.logAuthentication(
          userId: 'user-123',
          success: false,
          errorMessage: 'Invalid password',
          ipAddress: '***********',
        );

        // Assert
        final status = auditService.getStatus();
        expect(status['pending_audit_entries'], greaterThan(0));
        expect(status['pending_security_events'], greaterThan(0));
      });
    });

    group('Permission Change Logging', () {
      test('should log permission changes with high risk level', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(collectionName: any, data: any),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "test_collection",
          ),
        );

        final changes = {
          'before': {'role': 'viewer'},
          'after': {'role': 'admin'},
        };

        // Act
        await auditService.logPermissionChange(
          resourceId: 'resource-123',
          changes: changes,
          success: true,
          userId: 'admin-456',
          userRole: 'admin',
        );

        // Assert
        final status = auditService.getStatus();
        expect(status['pending_audit_entries'], greaterThan(0));
        expect(status['pending_security_events'], greaterThan(0));
      });
    });

    group('Data Processing Logging', () {
      test('should log GDPR data processing activities', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(
            collectionName: 'audit_logs',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "test_collection",
          ),
        );

        // Act
        await auditService.logDataProcessing(
          action: 'export',
          dataType: 'personal_data',
          success: true,
          userId: 'user-123',
          metadata: {'export_format': 'json'},
        );

        // Assert
        final status = auditService.getStatus();
        expect(status['pending_audit_entries'], greaterThan(0));
      });

      test(
        'should assign appropriate risk levels for data processing',
        () async {
          // Arrange
          when(
            mockPocketBaseService.createRecord(
              collectionName: 'audit_logs',
              data: any,
            ),
          ).thenAnswer(
            (_) async => NotificationTestUtils.createMockRecord(
              id: "test_id",
              data: {},
              collectionName: "test_collection",
            ),
          );

          // Act - High risk action
          await auditService.logDataProcessing(
            action: 'delete',
            dataType: 'personal_data',
            success: true,
            userId: 'user-123',
          );

          // Act - Low risk action
          await auditService.logDataProcessing(
            action: 'view',
            dataType: 'public_data',
            success: true,
            userId: 'user-123',
          );

          // Assert
          final status = auditService.getStatus();
          expect(status['pending_audit_entries'], greaterThan(0));
        },
      );
    });

    group('Security Event Logging', () {
      test('should log security events', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(
            collectionName: 'security_events',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "test_collection",
          ),
        );

        final securityEvent = SecurityEvent.suspiciousActivity(
          id: 'event-123',
          type: 'unusual_access',
          userId: 'user-456',
          description: 'Access from unusual location',
          ipAddress: '********',
        );

        // Act
        await auditService.logSecurityEvent(securityEvent);

        // Assert
        final status = auditService.getStatus();
        expect(status['pending_security_events'], greaterThan(0));
      });

      test('should flush critical events immediately', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(
            collectionName: 'security_events',
            data: any,
          ),
        ).thenAnswer(
          (_) async => NotificationTestUtils.createMockRecord(
            id: "test_id",
            data: {},
            collectionName: "test_collection",
          ),
        );

        final criticalEvent = SecurityEvent(
          id: 'critical-123',
          type: 'security_breach',
          timestamp: DateTime.now(),
          success: false,
          severity: SecurityEventSeverity.critical,
          riskScore: 95,
          requiresAttention: true,
          tags: ['critical', 'breach'],
        );

        // Act
        await auditService.logSecurityEvent(criticalEvent);

        // Assert - Critical events should be flushed immediately
        verify(
          mockPocketBaseService.createRecord(
            collectionName: 'security_events',
            data: any,
          ),
        ).called(1);
      });
    });

    group('Batch Processing', () {
      test('should flush batches when size limit is reached', () async {
        // This test would require access to internal batch size limits
        // For now, we'll test the status reporting
        final status = auditService.getStatus();
        expect(status.containsKey('batch_size_limit'), isTrue);
        expect(status.containsKey('flush_interval_seconds'), isTrue);
      });

      test('should handle batch processing errors gracefully', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(collectionName: any, data: any),
        ).thenThrow(Exception('Database error'));

        // Act
        await auditService.logDocumentAccess(
          documentId: 'doc-123',
          action: 'view',
          success: true,
          userId: 'user-456',
        );

        // Assert - Should not throw exception
        expect(() => auditService.getStatus(), returnsNormally);
      });
    });

    group('Compliance Reporting', () {
      test('should generate compliance report for date range', () async {
        // Arrange
        final startDate = DateTime.now().subtract(Duration(days: 30));
        final endDate = DateTime.now();

        when(
          mockPocketBaseService.getFullList(collectionName: any, filter: any),
        ).thenAnswer(
          (_) async => [
            NotificationTestUtils.createMockRecord(
              id: 'audit-1',
              data: {'success': true, 'action': 'test_action'},
              collectionName: 'audit_logs',
            ),
            NotificationTestUtils.createMockRecord(
              id: 'audit-2',
              data: {'success': false, 'action': 'test_action'},
              collectionName: 'audit_logs',
            ),
          ],
        );

        // Act
        final report = await auditService.generateComplianceReport(
          startDate: startDate,
          endDate: endDate,
        );

        // Assert
        expect(report, isA<Map<String, dynamic>>());
        expect(report.containsKey('report_id'), isTrue);
        expect(report.containsKey('generated_at'), isTrue);
        expect(report.containsKey('period'), isTrue);
        expect(report.containsKey('summary'), isTrue);
        expect(report.containsKey('audit_entries'), isTrue);
        expect(report.containsKey('security_events'), isTrue);
        expect(report['summary']['total_audit_entries'], equals(2));
        expect(report['summary']['successful_operations'], equals(1));
        expect(report['summary']['failed_operations'], equals(1));
      });

      test('should filter compliance report by user', () async {
        // Arrange
        final startDate = DateTime.now().subtract(Duration(days: 7));
        final endDate = DateTime.now();
        const userId = 'specific-user-123';

        when(
          mockPocketBaseService.getFullList(
            collectionName: any,
            filter: argThat(contains(userId)),
          ),
        ).thenAnswer(
          (_) async => [
            NotificationTestUtils.createMockRecord(
              id: 'audit-1',
              data: {'success': true, 'action': 'test_action'},
              collectionName: 'audit_logs',
            ),
          ],
        );

        // Act
        final report = await auditService.generateComplianceReport(
          startDate: startDate,
          endDate: endDate,
          userId: userId,
        );

        // Assert
        expect(report['summary']['total_audit_entries'], equals(1));
        verify(
          mockPocketBaseService.getFullList(
            collectionName: any,
            filter: argThat(contains(userId)),
          ),
        ).called(2); // Called for both audit logs and security events
      });
    });

    group('Audit Integrity Verification', () {
      test('should verify valid audit log integrity', () async {
        // Arrange
        final auditId = 'audit-123';
        final mockRecord = NotificationTestUtils.createMockRecord(
          id: auditId,
          data: {
            'success': true,
            'action': 'test_action',
            'digital_signature': 'valid-signature',
          },
          collectionName: 'audit_logs',
        );

        when(
          mockPocketBaseService.getFullList(
            collectionName: 'audit_logs',
            filter: 'id = "$auditId"',
          ),
        ).thenAnswer((_) async => [mockRecord]);

        // Act
        final isValid = await auditService.verifyAuditIntegrity(
          auditId: auditId,
        );

        // Assert
        expect(isValid, isA<bool>());
      });

      test('should detect tampered audit logs', () async {
        // Arrange
        final auditId = 'tampered-audit-123';
        final mockRecord = NotificationTestUtils.createMockRecord(
          id: auditId,
          data: {
            'success': true,
            'action': 'test_action',
            'digital_signature': 'invalid-signature',
          },
          collectionName: 'audit_logs',
        );

        when(
          mockPocketBaseService.getFullList(
            collectionName: 'audit_logs',
            filter: 'id = "$auditId"',
          ),
        ).thenAnswer((_) async => [mockRecord]);

        // Act
        final isValid = await auditService.verifyAuditIntegrity(
          auditId: auditId,
        );

        // Assert
        expect(isValid, isFalse);
      });

      test('should handle missing audit logs', () async {
        // Arrange
        final auditId = 'missing-audit-123';

        when(
          mockPocketBaseService.getFullList(
            collectionName: 'audit_logs',
            filter: 'id = "$auditId"',
          ),
        ).thenAnswer((_) async => []);

        // Act
        final isValid = await auditService.verifyAuditIntegrity(
          auditId: auditId,
        );

        // Assert
        expect(isValid, isFalse);
      });
    });

    group('Service Management', () {
      test('should return correct service status', () {
        // Act
        final status = auditService.getStatus();

        // Assert
        expect(status, isA<Map<String, dynamic>>());
        expect(status['service_initialized'], isTrue);
        expect(status.containsKey('pending_audit_entries'), isTrue);
        expect(status.containsKey('pending_security_events'), isTrue);
        expect(status.containsKey('batch_size_limit'), isTrue);
        expect(status.containsKey('flush_interval_seconds'), isTrue);
        expect(status.containsKey('timestamp'), isTrue);
      });

      test('should dispose resources correctly', () {
        // Act & Assert
        expect(() => auditService.dispose(), returnsNormally);
      });
    });

    group('Error Handling', () {
      test('should handle database connection errors gracefully', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(collectionName: any, data: any),
        ).thenThrow(Exception('Connection timeout'));

        // Act & Assert - Should not throw
        expect(
          () => auditService.logDocumentAccess(
            documentId: 'doc-123',
            action: 'view',
            success: true,
            userId: 'user-456',
          ),
          returnsNormally,
        );
      });

      test('should continue operation when logging fails', () async {
        // Arrange
        when(
          mockPocketBaseService.createRecord(collectionName: any, data: any),
        ).thenThrow(Exception('Logging failed'));

        // Act
        await auditService.logDocumentAccess(
          documentId: 'doc-123',
          action: 'view',
          success: true,
          userId: 'user-456',
        );

        // Assert - Service should still be operational
        final status = auditService.getStatus();
        expect(status['service_initialized'], isTrue);
      });
    });
  });
}

// Helper functions removed - using NotificationTestUtils.createMockRecord instead
