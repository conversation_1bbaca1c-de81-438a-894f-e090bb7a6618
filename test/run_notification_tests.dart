#!/usr/bin/env dart

import 'dart:io';

/// Test runner for notification system tests
///
/// This script runs the notification authentication integration tests
/// and provides detailed output about test results.
///
/// Note: Using print statements is appropriate for test runner scripts
/// as they need to output directly to console for user feedback.
void main(List<String> args) async {
  // ignore: avoid_print
  // ignore: avoid_print
  print('🔔 3Pay Global Notification System Test Runner');
  // ignore: avoid_print
  // ignore: avoid_print
  print('=' * 50);

  // Check if we're in the correct directory
  if (!await Directory('test').exists()) {
    // ignore: avoid_print
  print('❌ Error: Must be run from the project root directory');
    exit(1);
  }

  // Parse command line arguments
  final bool verbose = args.contains('--verbose') || args.contains('-v');
  final bool coverage = args.contains('--coverage') || args.contains('-c');
  final String? specific = _getSpecificTest(args);

  // ignore: avoid_print
  print('📋 Test Configuration:');
  // ignore: avoid_print
  print('   Verbose: $verbose');
  // ignore: avoid_print
  print('   Coverage: $coverage');
  if (specific != null) {
    // ignore: avoid_print
  print('   Specific Test: $specific');
  }
  // ignore: avoid_print
  print('');

  // Run the tests
  await _runNotificationTests(
    verbose: verbose,
    coverage: coverage,
    specific: specific,
  );
}

/// Extract specific test name from arguments
String? _getSpecificTest(List<String> args) {
  final testIndex = args.indexOf('--test');
  if (testIndex != -1 && testIndex + 1 < args.length) {
    return args[testIndex + 1];
  }
  return null;
}

/// Run notification system tests
Future<void> _runNotificationTests({
  bool verbose = false,
  bool coverage = false,
  String? specific,
}) async {
  // ignore: avoid_print
  print('🚀 Starting Notification Tests...');
  // ignore: avoid_print
  print('');

  // Build test command
  final List<String> command = ['flutter', 'test'];

  if (coverage) {
    command.add('--coverage');
  }

  if (verbose) {
    command.add('--verbose');
  }

  // Add specific test file or run all notification tests
  if (specific != null) {
    command.add('--name');
    command.add(specific);
  }

  // Add test file path
  command.add('test/src/core/services/notification_auth_integration_test.dart');

  // ignore: avoid_print
  print('📝 Running command: ${command.join(' ')}');
  // ignore: avoid_print
  print('');

  // Execute the test command
  final ProcessResult result = await Process.run(
    command.first,
    command.skip(1).toList(),
    workingDirectory: Directory.current.path,
  );

  // Display results
  _displayTestResults(result, coverage);
}

/// Display test results with formatting
void _displayTestResults(ProcessResult result, bool coverage) {
  // ignore: avoid_print
  print('📊 Test Results:');
  // ignore: avoid_print
  print('=' * 30);

  if (result.exitCode == 0) {
    // ignore: avoid_print
  print('✅ All tests passed!');
  } else {
    // ignore: avoid_print
  print('❌ Some tests failed!');
  }

  // ignore: avoid_print
  print('');
  // ignore: avoid_print
  print('📄 Test Output:');
  // ignore: avoid_print
  print('-' * 20);
  // ignore: avoid_print
  print(result.stdout);

  if (result.stderr.toString().isNotEmpty) {
    // ignore: avoid_print
  print('');
    // ignore: avoid_print
  print('⚠️  Error Output:');
    // ignore: avoid_print
  print('-' * 20);
    // ignore: avoid_print
  print(result.stderr);
  }

  if (coverage) {
    // ignore: avoid_print
  print('');
    // ignore: avoid_print
  print('📈 Coverage Report:');
    // ignore: avoid_print
  print('-' * 20);
    // ignore: avoid_print
  print('Coverage report generated in coverage/lcov.info');
    // ignore: avoid_print
  print(
      'To view HTML report, run: genhtml coverage/lcov.info -o coverage/html',
    );
  }

  // ignore: avoid_print
  print('');
  // ignore: avoid_print
  print('🏁 Test execution completed with exit code: ${result.exitCode}');

  // Exit with the same code as the test process
  exit(result.exitCode);
}
