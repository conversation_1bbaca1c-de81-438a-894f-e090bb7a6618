# 3Pay Global Document Management Testing Configuration
# This file defines the testing strategy and requirements for the document management system

# Global Test Settings
global:
  timeout_minutes: 30
  retry_attempts: 3
  parallel_execution: true
  max_parallel_jobs: 4
  generate_coverage: true
  minimum_coverage_threshold: 85.0
  fail_on_coverage_below_threshold: true

# Test Categories Configuration
categories:
  unit:
    description: "Unit tests for individual services and components"
    timeout_minutes: 10
    minimum_coverage: 90.0
    priority: critical
    
  integration:
    description: "Integration tests for service interactions"
    timeout_minutes: 15
    minimum_coverage: 80.0
    priority: critical
    
  security:
    description: "Security validation and vulnerability tests"
    timeout_minutes: 8
    minimum_coverage: 95.0
    priority: critical
    
  performance:
    description: "Performance and load testing"
    timeout_minutes: 20
    minimum_coverage: 70.0
    priority: high
    
  ui:
    description: "User interface and widget tests"
    timeout_minutes: 12
    minimum_coverage: 75.0
    priority: medium

# Security Test Requirements
security_requirements:
  authentication:
    - "Validate service account credentials"
    - "Test session management"
    - "Verify token expiry handling"
    - "Test brute force protection"
    - "Validate input sanitization"
    
  authorization:
    - "Test permission validation"
    - "Verify access control enforcement"
    - "Test role-based access"
    - "Validate resource permissions"
    
  encryption:
    - "Test data encryption/decryption"
    - "Validate key management"
    - "Test encryption result integrity"
    - "Verify secure key storage"
    
  audit:
    - "Test audit log creation"
    - "Verify audit integrity"
    - "Test compliance reporting"
    - "Validate audit trail completeness"

# Performance Test Requirements
performance_requirements:
  file_upload:
    small_files: # < 1MB
      max_time_seconds: 1
      concurrent_uploads: 10
      
    medium_files: # 1-10MB
      max_time_seconds: 5
      concurrent_uploads: 5
      
    large_files: # 10-100MB
      max_time_seconds: 30
      concurrent_uploads: 2
      
  file_download:
    small_files:
      max_time_seconds: 0.5
      concurrent_downloads: 20
      
    medium_files:
      max_time_seconds: 3
      concurrent_downloads: 10
      
    large_files:
      max_time_seconds: 15
      concurrent_downloads: 5
      
  encryption:
    small_data: # < 1KB
      max_time_milliseconds: 100
      
    medium_data: # 1KB-1MB
      max_time_milliseconds: 1000
      
    large_data: # 1-10MB
      max_time_seconds: 5
      
  database_operations:
    create_record:
      max_time_milliseconds: 500
      concurrent_operations: 100
      
    query_records:
      max_time_milliseconds: 1000
      max_results: 1000
      
    update_record:
      max_time_milliseconds: 300
      concurrent_operations: 50

# UI Test Requirements
ui_requirements:
  responsiveness:
    mobile_width: 400
    tablet_width: 800
    desktop_width: 1200
    
  accessibility:
    - "Semantic labels present"
    - "Keyboard navigation support"
    - "Screen reader compatibility"
    - "Color contrast compliance"
    
  user_workflows:
    - "Document upload workflow"
    - "Document download workflow"
    - "Document sharing workflow"
    - "Category management workflow"
    - "Error handling display"

# Test Data Configuration
test_data:
  file_sizes:
    small: 1024        # 1KB
    medium: 1048576    # 1MB
    large: 10485760    # 10MB
    xlarge: 52428800   # 50MB
    
  file_types:
    - "pdf"
    - "docx"
    - "xlsx"
    - "png"
    - "jpg"
    
  test_users:
    - id: "test-solicitor-1"
      role: "solicitor"
      permissions: ["read", "write", "delete"]
      
    - id: "test-admin-1"
      role: "admin"
      permissions: ["read", "write", "delete", "admin"]
      
    - id: "test-viewer-1"
      role: "viewer"
      permissions: ["read"]

# Mock Configuration
mocks:
  google_drive:
    simulate_network_delay: true
    delay_milliseconds: 100
    simulate_failures: true
    failure_rate_percent: 5
    
  pocketbase:
    simulate_network_delay: true
    delay_milliseconds: 50
    simulate_failures: true
    failure_rate_percent: 2
    
  encryption:
    simulate_processing_time: true
    processing_time_per_mb_milliseconds: 100

# Reporting Configuration
reporting:
  formats:
    - "json"
    - "html"
    - "junit"
    
  include_coverage: true
  include_performance_metrics: true
  include_security_scan_results: true
  
  output_directory: "test/reports"
  
  notifications:
    on_failure: true
    on_success: false
    channels:
      - "console"
      - "file"

# Environment Configuration
environments:
  test:
    pocketbase_url: "http://localhost:8090"
    google_drive_credentials: "test/fixtures/test_credentials.json"
    encryption_key: "test-encryption-key-32-characters"
    
  staging:
    pocketbase_url: "https://staging.3payglobal.com"
    google_drive_credentials: "credentials/staging_credentials.json"
    encryption_key: "${STAGING_ENCRYPTION_KEY}"
    
  production:
    pocketbase_url: "https://api.3payglobal.com"
    google_drive_credentials: "credentials/production_credentials.json"
    encryption_key: "${PRODUCTION_ENCRYPTION_KEY}"

# Quality Gates
quality_gates:
  code_coverage:
    minimum_percentage: 85.0
    exclude_patterns:
      - "**/*.g.dart"
      - "**/*.freezed.dart"
      - "**/test/**"
      
  performance:
    max_response_time_seconds: 5
    max_memory_usage_mb: 512
    max_cpu_usage_percent: 80
    
  security:
    no_critical_vulnerabilities: true
    no_high_vulnerabilities: true
    max_medium_vulnerabilities: 5
    
  reliability:
    max_error_rate_percent: 1
    min_uptime_percent: 99.9

# Continuous Integration
ci_cd:
  run_on_pull_request: true
  run_on_merge: true
  run_nightly: true
  
  parallel_execution: true
  cache_dependencies: true
  
  failure_actions:
    - "notify_team"
    - "create_issue"
    - "block_deployment"
    
  success_actions:
    - "update_status"
    - "archive_reports"

# Test Maintenance
maintenance:
  cleanup_old_reports_days: 30
  cleanup_test_data_after_run: true
  update_test_dependencies_weekly: true
  
  review_schedule:
    test_cases: "monthly"
    performance_benchmarks: "quarterly"
    security_requirements: "monthly"
