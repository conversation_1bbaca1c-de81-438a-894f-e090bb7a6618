import 'package:flutter_test/flutter_test.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/features/notifications/data/models/enhanced_notification_model.dart';

/// Test utilities for notification testing
class NotificationTestUtils {
  /// Create a mock enhanced notification model for testing
  static EnhancedNotificationModel createMockNotification({
    String? id,
    String? title,
    String? message,
    String? type,
    bool isRead = false,
    List<String>? recipientId,
    DateTime? created,
    DateTime? updated,
    DateTime? readAt,
    bool isGlobal = false,
    String? link,
    String? icon,
  }) {
    return EnhancedNotificationModel(
      id: id ?? 'test_notification_${DateTime.now().millisecondsSinceEpoch}',
      title: title ?? 'Test Notification',
      message: message ?? 'This is a test notification message',
      type: type ?? 'info',
      isRead: isRead,
      recipientId: recipientId,
      created: created ?? DateTime.now(),
      updated: updated ?? DateTime.now(),
      readAt: readAt,
      isGlobal: isGlobal,
      link: link,
      icon: icon,
    );
  }

  /// Create a mock PocketBase record for testing
  /// Note: In real PocketBase, 'created' and 'updated' are automatically managed by autodate fields
  static RecordModel createMockRecord({
    String? id,
    Map<String, dynamic>? data,
    String? collectionId,
    String? collectionName,
    DateTime? created,
    DateTime? updated,
  }) {
    final recordData = {
      'id': id ?? 'test_record_${DateTime.now().millisecondsSinceEpoch}',
      'collectionId': collectionId ?? 'test_collection',
      'collectionName': collectionName ?? 'test_collection',
      // For testing purposes, we still include these fields to simulate PocketBase behavior
      // In production, these are automatically managed by PocketBase autodate fields
      'created': (created ?? DateTime.now()).toIso8601String(),
      'updated': (updated ?? DateTime.now()).toIso8601String(),
      ...?data,
    };
    return RecordModel.fromJson(recordData);
  }

  /// Create mock notification record data
  static Map<String, dynamic> createMockNotificationData({
    String? title,
    String? message,
    String? type,
    bool isRead = false,
    List<String>? recipientId,
    String? link,
    String? icon,
  }) {
    return {
      'title': title ?? 'Test Notification',
      'message': message ?? 'Test notification message',
      'type': type ?? 'info',
      'isRead': isRead,
      'recipientId': recipientId ?? [],
      'link': link,
      'icon': icon,
    };
  }

  /// Create mock user record for authentication testing
  static RecordModel createMockUserRecord({
    String? id,
    String? email,
    String? userType,
    String? fcmToken,
  }) {
    return createMockRecord(
      id: id ?? 'test_user_${DateTime.now().millisecondsSinceEpoch}',
      data: {
        'email': email ?? '<EMAIL>',
        'user_type': userType ?? 'claimant',
        'fcm_token': fcmToken,
        // Note: In production, timestamp fields are managed by PocketBase autodate fields
        'fcm_token_updated': DateTime.now().toIso8601String(),
      },
      collectionName: 'users',
    );
  }

  /// Create mock read state record
  static RecordModel createMockReadStateRecord({
    String? id,
    String? notificationId,
    String? userId,
    bool isRead = false,
    DateTime? readAt,
  }) {
    return createMockRecord(
      id: id ?? 'test_read_state_${DateTime.now().millisecondsSinceEpoch}',
      data: {
        'notification_id': notificationId ?? 'test_notification_id',
        'user_id': userId ?? 'test_user_id',
        'is_read': isRead,
        'read_at': readAt?.toIso8601String(),
      },
      collectionName: 'notification_read_states',
    );
  }

  /// Wait for async operations to complete in tests
  static Future<void> waitForAsync() async {
    await Future.delayed(const Duration(milliseconds: 10));
  }

  /// Verify that a notification has the expected properties
  static void verifyNotificationProperties(
    EnhancedNotificationModel notification, {
    String? expectedTitle,
    String? expectedMessage,
    String? expectedType,
    bool? expectedIsRead,
    bool? expectedIsGlobal,
  }) {
    if (expectedTitle != null) {
      expect(notification.title, equals(expectedTitle));
    }
    if (expectedMessage != null) {
      expect(notification.message, equals(expectedMessage));
    }
    if (expectedType != null) {
      expect(notification.type, equals(expectedType));
    }
    if (expectedIsRead != null) {
      expect(notification.isRead, equals(expectedIsRead));
    }
    if (expectedIsGlobal != null) {
      expect(notification.isGlobal, equals(expectedIsGlobal));
    }
  }

  /// Create a list of mock notifications for testing
  static List<EnhancedNotificationModel> createMockNotificationList({
    int count = 5,
    bool includeRead = true,
    bool includeUnread = true,
  }) {
    final notifications = <EnhancedNotificationModel>[];

    for (int i = 0; i < count; i++) {
      final isRead = includeRead && includeUnread ? i % 2 == 0 : includeRead;

      notifications.add(
        createMockNotification(
          id: 'test_notification_$i',
          title: 'Test Notification $i',
          message: 'Test message $i',
          type:
              i % 3 == 0
                  ? 'claim_update'
                  : i % 3 == 1
                  ? 'message'
                  : 'info',
          isRead: isRead,
          created: DateTime.now().subtract(Duration(hours: i)),
        ),
      );
    }

    return notifications;
  }

  /// Verify FCM token storage in user record
  static void verifyFCMTokenStorage(
    Map<String, dynamic> userData,
    String expectedToken,
  ) {
    expect(userData['fcm_token'], equals(expectedToken));
    expect(userData['fcm_token_updated'], isNotNull);
  }

  /// Create mock authentication data
  static RecordAuth createMockAuthData({
    String? userId,
    String? userType,
    String? token,
  }) {
    final user = createMockUserRecord(id: userId, userType: userType);

    return RecordAuth(token: token ?? 'mock_auth_token', record: user);
  }
}
