#!/usr/bin/env dart

import 'dart:io';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Validation script for notification authentication integration
///
/// This script validates that the notification system is properly
/// integrated with the authentication system by checking:
/// 1. Service interfaces and methods
/// 2. Authentication flow integration points
/// 3. Error handling and resilience
/// 4. Configuration consistency
void main() async {
  LoggerService.info('🔐 3Pay Global Notification Authentication Validation');
  LoggerService.info('=' * 55);
  LoggerService.info('');

  final validator = NotificationAuthValidator();
  await validator.runValidation();
}

class NotificationAuthValidator {
  int _passedChecks = 0;
  int _totalChecks = 0;
  final List<String> _issues = [];

  /// Run comprehensive validation
  Future<void> runValidation() async {
    LoggerService.info(
      '🔍 Validating Notification Authentication Integration...',
    );
    LoggerService.info('');

    await _validateServiceFiles();
    await _validateAuthenticationIntegration();
    await _validateTestCoverage();
    await _validateConfiguration();

    _displayResults();
  }

  /// Validate that required service files exist and have proper structure
  Future<void> _validateServiceFiles() async {
    LoggerService.info('📁 Validating Service Files...');

    final requiredFiles = [
      'lib/src/core/services/firebase_api_service.dart',
      'lib/src/core/services/local_notification_service.dart',
      'lib/src/core/services/notification_service.dart',
      'lib/src/core/services/pocketbase_service.dart',
    ];

    for (final filePath in requiredFiles) {
      _checkFile(filePath, 'Service file exists');
    }

    // Check for authentication integration in PocketBase service
    await _checkFileContent(
      'lib/src/core/services/pocketbase_service.dart',
      ['FirebaseApiService.refreshToken', 'FirebaseApiService.clearToken'],
      'PocketBase service has Firebase integration',
    );

    // Check for authentication integration in Firebase service
    await _checkFileContent(
      'lib/src/core/services/firebase_api_service.dart',
      ['_pocketBaseService.isSignedIn', '_storeFCMToken'],
      'Firebase service has authentication integration',
    );

    LoggerService.info('');
  }

  /// Validate authentication integration points
  Future<void> _validateAuthenticationIntegration() async {
    LoggerService.info('🔐 Validating Authentication Integration...');

    // Check authentication providers
    final authProviders = [
      'lib/src/features/claimant_portal/presentation/providers/claimant_auth_provider.dart',
      'lib/src/features/solicitor_portal/presentation/providers/solicitor_auth_provider.dart',
      'lib/src/features/cofunder_portal/presentation/providers/cofunder_auth_provider.dart',
    ];

    for (final provider in authProviders) {
      await _checkFileContent(provider, [
        'FirebaseApiService.refreshToken',
      ], 'Auth provider integrates with Firebase');
    }

    // Check for FCM token management
    await _checkFileContent(
      'lib/src/core/services/firebase_api_service.dart',
      ['fcm_token', 'fcm_token_updated'],
      'FCM token management implemented',
    );

    LoggerService.info('');
  }

  /// Validate test coverage
  Future<void> _validateTestCoverage() async {
    LoggerService.info('🧪 Validating Test Coverage...');

    final testFiles = [
      'test/src/core/services/notification_auth_integration_test.dart',
      'test/utils/notification_test_utils.dart',
      'test/NOTIFICATION_TESTING_STRATEGY.md',
    ];

    for (final testFile in testFiles) {
      _checkFile(testFile, 'Test file exists');
    }

    // Check test content
    await _checkFileContent(
      'test/src/core/services/notification_auth_integration_test.dart',
      [
        'Authentication Integration Tests',
        'Service Status',
        'Payload Handling',
      ],
      'Test file has comprehensive coverage',
    );

    LoggerService.info('');
  }

  /// Validate configuration consistency
  Future<void> _validateConfiguration() async {
    LoggerService.info('⚙️  Validating Configuration...');

    // Check for notification channels
    await _checkFileContent(
      'lib/src/core/services/local_notification_service.dart',
      ['CLAIMS_CHANNEL_ID', 'FUNDING_CHANNEL_ID', 'MESSAGES_CHANNEL_ID'],
      'Notification channels configured',
    );

    // Check for error handling
    await _checkFileContent(
      'lib/src/core/services/firebase_api_service.dart',
      ['LoggerService.error', 'try', 'catch'],
      'Error handling implemented',
    );

    // Check for authentication state validation
    await _checkFileContent(
      'lib/src/core/services/firebase_api_service.dart',
      ['isSignedIn', 'currentUser'],
      'Authentication state validation',
    );

    LoggerService.info('');
  }

  /// Check if a file exists
  void _checkFile(String filePath, String description) {
    _totalChecks++;

    if (File(filePath).existsSync()) {
      _passedChecks++;
      LoggerService.info('  ✅ $description: $filePath');
    } else {
      _issues.add('Missing file: $filePath');
      LoggerService.error('  ❌ $description: $filePath (NOT FOUND)');
    }
  }

  /// Check file content for specific patterns
  Future<void> _checkFileContent(
    String filePath,
    List<String> patterns,
    String description,
  ) async {
    _totalChecks++;

    if (!File(filePath).existsSync()) {
      _issues.add('File not found for content check: $filePath');
      LoggerService.error('  ❌ $description: File not found');
      return;
    }

    try {
      final content = await File(filePath).readAsString();
      final foundPatterns =
          patterns.where((pattern) => content.contains(pattern)).toList();

      if (foundPatterns.length == patterns.length) {
        _passedChecks++;
        LoggerService.info('  ✅ $description');
      } else {
        final missingPatterns =
            patterns.where((pattern) => !content.contains(pattern)).toList();
        _issues.add(
          'Missing patterns in $filePath: ${missingPatterns.join(', ')}',
        );
        LoggerService.error(
          '  ❌ $description: Missing patterns: ${missingPatterns.join(', ')}',
        );
      }
    } catch (e) {
      _issues.add('Error reading file $filePath: $e');
      LoggerService.error('  ❌ $description: Error reading file');
    }
  }

  /// Display validation results
  void _displayResults() {
    LoggerService.info('📊 Validation Results:');
    LoggerService.info('=' * 25);
    LoggerService.info('');

    final percentage =
        _totalChecks > 0 ? (_passedChecks / _totalChecks * 100).round() : 0;

    LoggerService.info('✅ Passed: $_passedChecks/$_totalChecks ($percentage%)');

    if (_issues.isNotEmpty) {
      LoggerService.error('❌ Issues Found: ${_issues.length}');
      LoggerService.info('');
      LoggerService.info('🔧 Issues to Address:');
      LoggerService.info('-' * 20);
      for (int i = 0; i < _issues.length; i++) {
        LoggerService.info('${i + 1}. ${_issues[i]}');
      }
    } else {
      LoggerService.info('🎉 No issues found!');
    }

    LoggerService.info('');
    LoggerService.info('📋 Summary:');
    LoggerService.info('-' * 10);

    if (percentage >= 90) {
      LoggerService.info(
        '🟢 EXCELLENT: Notification authentication integration is well implemented',
      );
    } else if (percentage >= 75) {
      LoggerService.info(
        '🟡 GOOD: Notification authentication integration is mostly complete',
      );
    } else if (percentage >= 50) {
      LoggerService.warning(
        '🟠 FAIR: Notification authentication integration needs improvement',
      );
    } else {
      LoggerService.error(
        '🔴 POOR: Notification authentication integration requires significant work',
      );
    }

    LoggerService.info('');
    LoggerService.info('📝 Recommendations:');
    LoggerService.info('-' * 15);

    if (_issues.isNotEmpty) {
      LoggerService.info('1. Address the issues listed above');
      LoggerService.info('2. Run tests to verify functionality');
      LoggerService.info('3. Test on physical devices');
    } else {
      LoggerService.info('1. Run comprehensive tests');
      LoggerService.info('2. Test on multiple devices and OS versions');
      LoggerService.info('3. Monitor performance and error logs');
    }

    LoggerService.info('');
    LoggerService.info('🏁 Validation completed!');

    // Exit with appropriate code
    exit(_issues.isEmpty ? 0 : 1);
  }
}
