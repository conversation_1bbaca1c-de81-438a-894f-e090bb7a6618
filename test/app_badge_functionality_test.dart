import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/app_badge_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/providers/app_badge_provider.dart';
import 'package:three_pay_group_litigation_platform/src/core/providers/notification_counter_provider.dart';

void main() {
  // Initialize Flutter binding for tests
  TestWidgetsFlutterBinding.ensureInitialized();
  group('App Badge Functionality Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('AppBadgeService should initialize correctly', () async {
      await AppBadgeService.initialize();

      expect(AppBadgeService.isInitialized, isTrue);
      expect(AppBadgeService.isSupported, isA<bool>());
    });

    test('AppBadgeService should handle platform info', () {
      final platformInfo = AppBadgeService.getPlatformInfo();

      expect(platformInfo, isA<Map<String, dynamic>>());
      expect(platformInfo.containsKey('platform'), isTrue);
      expect(platformInfo.containsKey('isSupported'), isTrue);
      expect(platformInfo.containsKey('isInitialized'), isTrue);
    });

    test('AppBadgeService should handle badge updates', () async {
      await AppBadgeService.initialize();

      // These should not throw, even if not supported
      expect(() => AppBadgeService.updateBadge(5), returnsNormally);
      expect(() => AppBadgeService.clearBadge(), returnsNormally);
    });

    test('AppBadgeProvider should be available', () {
      expect(() => container.read(appBadgeProvider), returnsNormally);
      expect(() => container.read(appBadgeSupportedProvider), returnsNormally);
      expect(() => container.read(currentBadgeCountProvider), returnsNormally);
      expect(() => container.read(badgeUpdatingProvider), returnsNormally);
      expect(() => container.read(badgeErrorProvider), returnsNormally);
    });

    test('AppBadgeState should have correct initial values', () {
      final badgeState = container.read(appBadgeProvider);

      expect(badgeState.currentCount, equals(0));
      expect(badgeState.isUpdating, isFalse);
      expect(badgeState.error, isNull);
    });

    test('Badge providers should return correct types', () {
      expect(container.read(appBadgeSupportedProvider), isA<bool>());
      expect(container.read(currentBadgeCountProvider), isA<int>());
      expect(container.read(badgeUpdatingProvider), isA<bool>());
      expect(container.read(badgeErrorProvider), isA<String?>());
    });

    test('AppBadgeNotifier should handle manual operations', () async {
      final notifier = container.read(appBadgeProvider.notifier);

      // These should not throw
      expect(() => notifier.refreshBadge(), returnsNormally);
      expect(() => notifier.clearBadge(), returnsNormally);
      expect(() => notifier.getPlatformInfo(), returnsNormally);
    });

    test('Badge should integrate with notification counter', () async {
      // Wait a moment for initialization
      await Future.delayed(const Duration(milliseconds: 100));

      final badgeCount = container.read(currentBadgeCountProvider);
      final notificationCount = container.read(currentUnreadCountProvider);

      expect(badgeCount, isA<int>());
      expect(notificationCount, isA<int>());
      expect(badgeCount, greaterThanOrEqualTo(0));
      expect(notificationCount, greaterThanOrEqualTo(0));
    });

    test('Badge state should be copyable', () {
      const state1 = AppBadgeState(
        isSupported: true,
        isInitialized: true,
        currentCount: 5,
        isUpdating: false,
        error: null,
      );

      final state2 = state1.copyWith(currentCount: 10);

      expect(state2.currentCount, equals(10));
      expect(state2.isSupported, equals(state1.isSupported));
      expect(state2.isInitialized, equals(state1.isInitialized));
      expect(state2.isUpdating, equals(state1.isUpdating));
      expect(state2.error, equals(state1.error));
    });

    test('Badge state should have proper toString', () {
      const state = AppBadgeState(
        isSupported: true,
        isInitialized: true,
        currentCount: 5,
        isUpdating: false,
        error: null,
      );

      final stringRepresentation = state.toString();
      expect(stringRepresentation, contains('AppBadgeState'));
      expect(stringRepresentation, contains('isSupported: true'));
      expect(stringRepresentation, contains('currentCount: 5'));
    });
  });

  group('Badge Integration Tests', () {
    test('Badge should handle edge cases', () async {
      await AppBadgeService.initialize();

      // Test edge cases
      expect(() => AppBadgeService.updateBadge(0), returnsNormally);
      expect(() => AppBadgeService.updateBadge(-1), returnsNormally);
      expect(() => AppBadgeService.updateBadge(999), returnsNormally);
    });

    test('Badge service should handle disposal', () async {
      await AppBadgeService.initialize();
      expect(() => AppBadgeService.dispose(), returnsNormally);
    });

    test('Multiple badge operations should work', () async {
      await AppBadgeService.initialize();

      // Multiple operations should not interfere
      await AppBadgeService.updateBadge(1);
      await AppBadgeService.updateBadge(2);
      await AppBadgeService.clearBadge();
      await AppBadgeService.updateBadge(3);

      // Should complete without errors
      expect(true, isTrue);
    });

    test('Badge provider should handle multiple containers', () {
      final container1 = ProviderContainer();
      final container2 = ProviderContainer();

      try {
        final state1 = container1.read(appBadgeProvider);
        final state2 = container2.read(appBadgeProvider);

        expect(state1.currentCount, equals(0));
        expect(state2.currentCount, equals(0));
      } finally {
        container1.dispose();
        container2.dispose();
      }
    });
  });

  group('Platform Compatibility Tests', () {
    test('Badge should work on all platforms', () async {
      await AppBadgeService.initialize();

      final platformInfo = AppBadgeService.getPlatformInfo();

      // Should have platform information
      expect(platformInfo['platform'], isA<String>());
      expect(platformInfo['isSupported'], isA<bool>());
      expect(platformInfo['isInitialized'], isA<bool>());
    });

    test('Badge should handle unsupported platforms gracefully', () async {
      await AppBadgeService.initialize();

      // Even if not supported, these should not throw
      await AppBadgeService.updateBadge(5);
      await AppBadgeService.clearBadge();

      expect(AppBadgeService.isInitialized, isTrue);
    });
  });
}
