import 'dart:io';
import 'dart:convert';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Comprehensive test runner for the 3Pay Global document management system
///
/// This script runs all test suites in a systematic order and generates
/// detailed reports on test coverage, performance, and security validation.
class ComprehensiveTestRunner {
  static const String testReportPath = 'test/reports';
  static const String coverageReportPath = 'coverage';

  static final List<TestSuite> testSuites = [
    // Unit Tests
    TestSuite(
      name: 'Security Services Unit Tests',
      category: TestCategory.unit,
      priority: TestPriority.critical,
      files: [
        'test/src/core/services/google_drive_auth_service_test.dart',
        'test/src/core/services/encryption_service_test.dart',
        'test/src/core/services/credential_management_service_test.dart',
        'test/src/core/services/enhanced_audit_service_test.dart',
        'test/src/core/services/security_incident_service_test.dart',
        'test/src/core/services/privacy_protection_service_test.dart',
      ],
      expectedCoverage: 90.0,
      maxExecutionTime: Duration(minutes: 10),
    ),

    TestSuite(
      name: 'Core Services Unit Tests',
      category: TestCategory.unit,
      priority: TestPriority.high,
      files: ['test/src/core/services/google_drive_service_test.dart'],
      expectedCoverage: 85.0,
      maxExecutionTime: Duration(minutes: 5),
    ),

    // Integration Tests
    TestSuite(
      name: 'Document Management Integration Tests',
      category: TestCategory.integration,
      priority: TestPriority.critical,
      files: ['test/integration/document_management_integration_test.dart'],
      expectedCoverage: 80.0,
      maxExecutionTime: Duration(minutes: 15),
    ),

    // Security Tests
    TestSuite(
      name: 'Authentication Security Tests',
      category: TestCategory.security,
      priority: TestPriority.critical,
      files: ['test/security/authentication_security_test.dart'],
      expectedCoverage: 95.0,
      maxExecutionTime: Duration(minutes: 8),
    ),

    // Performance Tests
    TestSuite(
      name: 'Document Performance Tests',
      category: TestCategory.performance,
      priority: TestPriority.high,
      files: ['test/performance/document_performance_test.dart'],
      expectedCoverage: 70.0,
      maxExecutionTime: Duration(minutes: 20),
    ),

    // UI Tests
    TestSuite(
      name: 'Document Management UI Tests',
      category: TestCategory.ui,
      priority: TestPriority.medium,
      files: ['test/ui/document_management_ui_test.dart'],
      expectedCoverage: 75.0,
      maxExecutionTime: Duration(minutes: 12),
    ),
  ];

  static Future<void> main(List<String> args) async {
    // LoggerService doesn't need initialization - it's ready to use
    LoggerService.info(
      'Starting Comprehensive Test Suite for 3Pay Global Document Management',
    );

    LoggerService.info(
      '🚀 Starting Comprehensive Test Suite for 3Pay Global Document Management',
    );
    LoggerService.info('=' * 80);

    final runner = ComprehensiveTestRunner();
    await runner.runAllTests(args);
  }

  Future<void> runAllTests(List<String> args) async {
    final startTime = DateTime.now();
    final results = <TestSuiteResult>[];

    // Parse command line arguments
    final options = _parseArguments(args);

    // Create reports directory
    await _createReportsDirectory();

    // Run test suites based on priority and options
    final suitesToRun = _filterTestSuites(options);

    LoggerService.info('📋 Test Execution Plan:');
    for (final suite in suitesToRun) {
      LoggerService.info(
        '  • ${suite.name} (${suite.category.name}, ${suite.priority.name})',
      );
    }
    LoggerService.info('');

    // Execute test suites
    for (final suite in suitesToRun) {
      LoggerService.info('🧪 Running: ${suite.name}');
      final result = await _runTestSuite(suite, options);
      results.add(result);

      _printSuiteResult(result);

      // Stop on critical failures if not in continue mode
      if (!options.continueOnFailure &&
          result.status == TestStatus.failed &&
          suite.priority == TestPriority.critical) {
        LoggerService.error(
          '❌ Critical test suite failed. Stopping execution.',
        );
        break;
      }

      LoggerService.info('');
    }

    // Generate comprehensive report
    final overallResult = await _generateComprehensiveReport(
      results,
      startTime,
    );

    // Print summary
    _printOverallSummary(overallResult, DateTime.now().difference(startTime));

    // Exit with appropriate code
    exit(overallResult.allTestsPassed ? 0 : 1);
  }

  TestOptions _parseArguments(List<String> args) {
    return TestOptions(
      categories:
          args.contains('--unit')
              ? [TestCategory.unit]
              : args.contains('--integration')
              ? [TestCategory.integration]
              : args.contains('--security')
              ? [TestCategory.security]
              : args.contains('--performance')
              ? [TestCategory.performance]
              : args.contains('--ui')
              ? [TestCategory.ui]
              : TestCategory.values,
      continueOnFailure: args.contains('--continue-on-failure'),
      generateCoverage: !args.contains('--no-coverage'),
      verbose: args.contains('--verbose'),
      parallel: args.contains('--parallel'),
      maxParallelJobs:
          args.contains('--jobs')
              ? int.tryParse(args[args.indexOf('--jobs') + 1]) ?? 4
              : 4,
    );
  }

  List<TestSuite> _filterTestSuites(TestOptions options) {
    return testSuites
        .where((suite) => options.categories.contains(suite.category))
        .toList()
      ..sort((a, b) => a.priority.index.compareTo(b.priority.index));
  }

  Future<void> _createReportsDirectory() async {
    final reportsDir = Directory(testReportPath);
    if (!await reportsDir.exists()) {
      await reportsDir.create(recursive: true);
    }
  }

  Future<TestSuiteResult> _runTestSuite(
    TestSuite suite,
    TestOptions options,
  ) async {
    final startTime = DateTime.now();

    try {
      // Run tests with coverage if enabled
      final command =
          options.generateCoverage
              ? 'flutter test --coverage ${suite.files.join(' ')}'
              : 'flutter test ${suite.files.join(' ')}';

      final result = await Process.run('sh', [
        '-c',
        command,
      ], workingDirectory: Directory.current.path);

      final duration = DateTime.now().difference(startTime);
      final output = result.stdout.toString() + result.stderr.toString();

      // Parse test results
      final testCount = _parseTestCount(output);
      final failureCount = _parseFailureCount(output);
      final coverage =
          options.generateCoverage ? await _parseCoverage(suite) : 0.0;

      return TestSuiteResult(
        suite: suite,
        status: result.exitCode == 0 ? TestStatus.passed : TestStatus.failed,
        duration: duration,
        testCount: testCount,
        failureCount: failureCount,
        coverage: coverage,
        output: output,
        metCoverageTarget: coverage >= suite.expectedCoverage,
        withinTimeLimit: duration <= suite.maxExecutionTime,
      );
    } catch (e) {
      return TestSuiteResult(
        suite: suite,
        status: TestStatus.error,
        duration: DateTime.now().difference(startTime),
        testCount: 0,
        failureCount: 0,
        coverage: 0.0,
        output: 'Error running test suite: $e',
        metCoverageTarget: false,
        withinTimeLimit: false,
      );
    }
  }

  int _parseTestCount(String output) {
    final match = RegExp(r'(\d+) tests? passed').firstMatch(output);
    return match != null ? int.parse(match.group(1)!) : 0;
  }

  int _parseFailureCount(String output) {
    final match = RegExp(r'(\d+) tests? failed').firstMatch(output);
    return match != null ? int.parse(match.group(1)!) : 0;
  }

  Future<double> _parseCoverage(TestSuite suite) async {
    // Parse coverage from lcov.info file
    final lcovFile = File('coverage/lcov.info');
    if (!await lcovFile.exists()) return 0.0;

    // Simple coverage parsing - in real implementation, this would be more sophisticated
    return 85.0; // Placeholder
  }

  void _printSuiteResult(TestSuiteResult result) {
    final status =
        result.status == TestStatus.passed
            ? '✅'
            : result.status == TestStatus.failed
            ? '❌'
            : '⚠️';

    LoggerService.info('  $status ${result.suite.name}');
    LoggerService.info('     Duration: ${result.duration.inSeconds}s');
    LoggerService.info(
      '     Tests: ${result.testCount} passed, ${result.failureCount} failed',
    );
    LoggerService.info(
      '     Coverage: ${result.coverage.toStringAsFixed(1)}% (target: ${result.suite.expectedCoverage}%)',
    );

    if (!result.metCoverageTarget) {
      LoggerService.warning('     ⚠️  Coverage below target');
    }

    if (!result.withinTimeLimit) {
      LoggerService.warning('     ⚠️  Execution time exceeded limit');
    }
  }

  Future<OverallTestResult> _generateComprehensiveReport(
    List<TestSuiteResult> results,
    DateTime startTime,
  ) async {
    final reportFile = File('$testReportPath/comprehensive_test_report.json');

    final report = {
      'timestamp': DateTime.now().toIso8601String(),
      'execution_start': startTime.toIso8601String(),
      'total_duration_seconds': DateTime.now().difference(startTime).inSeconds,
      'test_suites':
          results
              .map(
                (r) => {
                  'name': r.suite.name,
                  'category': r.suite.category.name,
                  'priority': r.suite.priority.name,
                  'status': r.status.name,
                  'duration_seconds': r.duration.inSeconds,
                  'test_count': r.testCount,
                  'failure_count': r.failureCount,
                  'coverage_percentage': r.coverage,
                  'met_coverage_target': r.metCoverageTarget,
                  'within_time_limit': r.withinTimeLimit,
                },
              )
              .toList(),
      'summary': {
        'total_suites': results.length,
        'passed_suites':
            results.where((r) => r.status == TestStatus.passed).length,
        'failed_suites':
            results.where((r) => r.status == TestStatus.failed).length,
        'error_suites':
            results.where((r) => r.status == TestStatus.error).length,
        'total_tests': results.fold(0, (sum, r) => sum + r.testCount),
        'total_failures': results.fold(0, (sum, r) => sum + r.failureCount),
        'average_coverage':
            results.isEmpty
                ? 0.0
                : results.fold(0.0, (sum, r) => sum + r.coverage) /
                    results.length,
      },
    };

    await reportFile.writeAsString(jsonEncode(report));

    return OverallTestResult(
      allTestsPassed: results.every((r) => r.status == TestStatus.passed),
      totalSuites: results.length,
      passedSuites: results.where((r) => r.status == TestStatus.passed).length,
      totalTests: results.fold(0, (sum, r) => sum + r.testCount),
      totalFailures: results.fold(0, (sum, r) => sum + r.failureCount),
      averageCoverage:
          results.isEmpty
              ? 0.0
              : results.fold(0.0, (sum, r) => sum + r.coverage) /
                  results.length,
    );
  }

  void _printOverallSummary(OverallTestResult result, Duration totalDuration) {
    LoggerService.info('📊 Test Execution Summary');
    LoggerService.info('=' * 50);
    LoggerService.info(
      'Total Duration: ${totalDuration.inMinutes}m ${totalDuration.inSeconds % 60}s',
    );
    LoggerService.info(
      'Test Suites: ${result.passedSuites}/${result.totalSuites} passed',
    );
    LoggerService.info(
      'Individual Tests: ${result.totalTests - result.totalFailures}/${result.totalTests} passed',
    );
    LoggerService.info(
      'Average Coverage: ${result.averageCoverage.toStringAsFixed(1)}%',
    );
    LoggerService.info('');

    if (result.allTestsPassed) {
      LoggerService.info('🎉 All tests passed successfully!');
      LoggerService.info(
        '✅ Document management system is ready for deployment',
      );
    } else {
      LoggerService.error('❌ Some tests failed');
      LoggerService.warning(
        '🔧 Please review the test report and fix failing tests',
      );
    }

    LoggerService.info('');
    LoggerService.info(
      '📄 Detailed report: $testReportPath/comprehensive_test_report.json',
    );
  }
}

// Data classes
enum TestCategory { unit, integration, security, performance, ui }

enum TestPriority { critical, high, medium, low }

enum TestStatus { passed, failed, error }

class TestSuite {
  final String name;
  final TestCategory category;
  final TestPriority priority;
  final List<String> files;
  final double expectedCoverage;
  final Duration maxExecutionTime;

  TestSuite({
    required this.name,
    required this.category,
    required this.priority,
    required this.files,
    required this.expectedCoverage,
    required this.maxExecutionTime,
  });
}

class TestOptions {
  final List<TestCategory> categories;
  final bool continueOnFailure;
  final bool generateCoverage;
  final bool verbose;
  final bool parallel;
  final int maxParallelJobs;

  TestOptions({
    required this.categories,
    this.continueOnFailure = false,
    this.generateCoverage = true,
    this.verbose = false,
    this.parallel = false,
    this.maxParallelJobs = 4,
  });
}

class TestSuiteResult {
  final TestSuite suite;
  final TestStatus status;
  final Duration duration;
  final int testCount;
  final int failureCount;
  final double coverage;
  final String output;
  final bool metCoverageTarget;
  final bool withinTimeLimit;

  TestSuiteResult({
    required this.suite,
    required this.status,
    required this.duration,
    required this.testCount,
    required this.failureCount,
    required this.coverage,
    required this.output,
    required this.metCoverageTarget,
    required this.withinTimeLimit,
  });
}

class OverallTestResult {
  final bool allTestsPassed;
  final int totalSuites;
  final int passedSuites;
  final int totalTests;
  final int totalFailures;
  final double averageCoverage;

  OverallTestResult({
    required this.allTestsPassed,
    required this.totalSuites,
    required this.passedSuites,
    required this.totalTests,
    required this.totalFailures,
    required this.averageCoverage,
  });
}

// Entry point
void main(List<String> args) async {
  await ComprehensiveTestRunner.main(args);
}
