import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:three_pay_group_litigation_platform/src/core/providers/notification_counter_provider.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/notification_service.dart';

void main() {
  group('Notification Counter Fix Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('notification counter provider should be available', () {
      expect(
        () => container.read(notificationCounterProvider.notifier),
        returnsNormally,
      );
      expect(() => container.read(currentUnreadCountProvider), returnsNormally);
      expect(
        () => container.read(unreadNotificationCountProvider),
        returnsNormally,
      );
    });

    test('notification service provider should be available', () {
      expect(
        () => container.read(notificationServiceProvider),
        returnsNormally,
      );
      expect(
        () => container.read(notificationServiceHelperProvider),
        returnsNormally,
      );
    });

    test('initial unread count should be 0', () {
      final initialCount = container.read(currentUnreadCountProvider);
      expect(initialCount, equals(0));
    });

    test(
      'notification counter should update when notifications change',
      () async {
        final notifier = container.read(notificationCounterProvider.notifier);

        // Test manual refresh
        await notifier.refresh();

        // Should not throw and should maintain count
        final count = container.read(currentUnreadCountProvider);
        expect(count, isA<int>());
        expect(count, greaterThanOrEqualTo(0));
      },
    );

    test('notification loading state should be accessible', () {
      final isLoading = container.read(notificationsLoadingProvider);
      expect(isLoading, isA<bool>());
    });

    test('notification error state should be accessible', () {
      final error = container.read(notificationErrorProvider);
      expect(error, isNull); // Should be null initially
    });

    test('notification service should handle initialization', () async {
      final service = container.read(notificationServiceProvider);
      expect(service, isA<NotificationService>());

      // Should not throw when calling methods
      expect(() => service.notifications, returnsNormally);
    });

    test('notification counter should handle mark as read', () async {
      final notifier = container.read(notificationCounterProvider.notifier);

      // Should not throw when marking non-existent notification as read
      expect(() => notifier.markAsRead('test-id'), returnsNormally);
    });

    test('providers should be properly typed', () {
      // Test that all providers return expected types
      expect(container.read(currentUnreadCountProvider), isA<int>());
      expect(container.read(notificationsLoadingProvider), isA<bool>());
      expect(container.read(notificationErrorProvider), isA<String?>());
      expect(
        container.read(notificationServiceProvider),
        isA<NotificationService>(),
      );
      expect(
        container.read(notificationServiceHelperProvider),
        isA<NotificationService>(),
      );
    });
  });

  group('Notification Counter Integration Tests', () {
    test('counter should work with real-time updates', () async {
      final container = ProviderContainer();

      try {
        final notifier = container.read(notificationCounterProvider.notifier);
        final initialCount = container.read(currentUnreadCountProvider);

        // Test refresh functionality
        await notifier.refresh();
        final refreshedCount = container.read(currentUnreadCountProvider);

        expect(refreshedCount, isA<int>());
        expect(refreshedCount, greaterThanOrEqualTo(0));

        // Both counts should be valid integers
        expect(initialCount, isA<int>());
        expect(refreshedCount, isA<int>());
      } finally {
        container.dispose();
      }
    });

    test('multiple provider instances should work independently', () {
      final container1 = ProviderContainer();
      final container2 = ProviderContainer();

      try {
        final count1 = container1.read(currentUnreadCountProvider);
        final count2 = container2.read(currentUnreadCountProvider);

        expect(count1, isA<int>());
        expect(count2, isA<int>());

        // Both should start at 0
        expect(count1, equals(0));
        expect(count2, equals(0));
      } finally {
        container1.dispose();
        container2.dispose();
      }
    });

    test('notification service should handle disposal correctly', () {
      final container = ProviderContainer();

      // Get the service
      final service = container.read(notificationServiceProvider);
      expect(service, isA<NotificationService>());

      // Dispose should not throw
      expect(() => container.dispose(), returnsNormally);
    });
  });

  group('Cross-Portal Consistency Tests', () {
    test('all portals should use same notification counter logic', () {
      final container = ProviderContainer();

      try {
        // Test that the same providers work for all portals
        final count = container.read(currentUnreadCountProvider);
        final loading = container.read(notificationsLoadingProvider);
        final error = container.read(notificationErrorProvider);

        expect(count, isA<int>());
        expect(loading, isA<bool>());
        expect(error, isA<String?>());

        // All portals should get the same count
        expect(count, greaterThanOrEqualTo(0));
      } finally {
        container.dispose();
      }
    });

    test('notification counter should be consistent across reads', () {
      final container = ProviderContainer();

      try {
        final count1 = container.read(currentUnreadCountProvider);
        final count2 = container.read(currentUnreadCountProvider);
        final count3 = container.read(currentUnreadCountProvider);

        // All reads should return the same value
        expect(count1, equals(count2));
        expect(count2, equals(count3));
        expect(count1, equals(count3));
      } finally {
        container.dispose();
      }
    });
  });
}
