// Mocks generated by <PERSON>cki<PERSON> 5.4.5 from annotations
// in three_pay_group_litigation_platform/test/performance/document_performance_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:io' as _i7;
import 'dart:typed_data' as _i8;

import 'package:http/http.dart' as _i14;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;
import 'package:pocketbase/pocketbase.dart' as _i4;
import 'package:three_pay_group_litigation_platform/src/core/models/google_drive_file.dart'
    as _i2;
import 'package:three_pay_group_litigation_platform/src/core/models/google_drive_permission.dart'
    as _i10;
import 'package:three_pay_group_litigation_platform/src/core/models/security_event.dart'
    as _i12;
import 'package:three_pay_group_litigation_platform/src/core/services/encryption_service.dart'
    as _i3;
import 'package:three_pay_group_litigation_platform/src/core/services/enhanced_audit_service.dart'
    as _i11;
import 'package:three_pay_group_litigation_platform/src/core/services/google_drive_service.dart'
    as _i5;
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart'
    as _i13;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGoogleDriveFile_0 extends _i1.SmartFake
    implements _i2.GoogleDriveFile {
  _FakeGoogleDriveFile_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoogleDriveBatchResult_1<T> extends _i1.SmartFake
    implements _i2.GoogleDriveBatchResult<T> {
  _FakeGoogleDriveBatchResult_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEncryptionResult_2 extends _i1.SmartFake
    implements _i3.EncryptionResult {
  _FakeEncryptionResult_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePocketBase_3 extends _i1.SmartFake implements _i4.PocketBase {
  _FakePocketBase_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAuthStore_4 extends _i1.SmartFake implements _i4.AuthStore {
  _FakeAuthStore_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRecordAuth_5 extends _i1.SmartFake implements _i4.RecordAuth {
  _FakeRecordAuth_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRecordModel_6 extends _i1.SmartFake implements _i4.RecordModel {
  _FakeRecordModel_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResultList_7<M extends _i4.Jsonable> extends _i1.SmartFake
    implements _i4.ResultList<M> {
  _FakeResultList_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GoogleDriveService].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoogleDriveService extends _i1.Mock
    implements _i5.GoogleDriveService {
  MockGoogleDriveService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  bool get isAuthenticated =>
      (super.noSuchMethod(
            Invocation.getter(#isAuthenticated),
            returnValue: false,
          )
          as bool);

  @override
  _i6.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i2.GoogleDriveFile> uploadFile({
    required _i7.File? file,
    required String? fileName,
    String? folderId,
    Map<String, String>? metadata,
    dynamic Function(_i2.GoogleDriveUploadProgress)? onProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadFile, [], {
              #file: file,
              #fileName: fileName,
              #folderId: folderId,
              #metadata: metadata,
              #onProgress: onProgress,
            }),
            returnValue: _i6.Future<_i2.GoogleDriveFile>.value(
              _FakeGoogleDriveFile_0(
                this,
                Invocation.method(#uploadFile, [], {
                  #file: file,
                  #fileName: fileName,
                  #folderId: folderId,
                  #metadata: metadata,
                  #onProgress: onProgress,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.GoogleDriveFile>);

  @override
  _i6.Future<_i8.Uint8List> downloadFile(String? fileId) =>
      (super.noSuchMethod(
            Invocation.method(#downloadFile, [fileId]),
            returnValue: _i6.Future<_i8.Uint8List>.value(_i8.Uint8List(0)),
          )
          as _i6.Future<_i8.Uint8List>);

  @override
  _i6.Future<_i2.GoogleDriveFile> getFileMetadata(String? fileId) =>
      (super.noSuchMethod(
            Invocation.method(#getFileMetadata, [fileId]),
            returnValue: _i6.Future<_i2.GoogleDriveFile>.value(
              _FakeGoogleDriveFile_0(
                this,
                Invocation.method(#getFileMetadata, [fileId]),
              ),
            ),
          )
          as _i6.Future<_i2.GoogleDriveFile>);

  @override
  _i6.Future<void> deleteFile(String? fileId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteFile, [fileId]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<bool> fileExists(String? fileId) =>
      (super.noSuchMethod(
            Invocation.method(#fileExists, [fileId]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<String> getFileDownloadUrl(String? fileId) =>
      (super.noSuchMethod(
            Invocation.method(#getFileDownloadUrl, [fileId]),
            returnValue: _i6.Future<String>.value(
              _i9.dummyValue<String>(
                this,
                Invocation.method(#getFileDownloadUrl, [fileId]),
              ),
            ),
          )
          as _i6.Future<String>);

  @override
  _i6.Future<String> getFileViewUrl(String? fileId) =>
      (super.noSuchMethod(
            Invocation.method(#getFileViewUrl, [fileId]),
            returnValue: _i6.Future<String>.value(
              _i9.dummyValue<String>(
                this,
                Invocation.method(#getFileViewUrl, [fileId]),
              ),
            ),
          )
          as _i6.Future<String>);

  @override
  _i6.Future<_i2.GoogleDriveFile> createFolder({
    required String? folderName,
    String? parentFolderId,
    Map<String, String>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createFolder, [], {
              #folderName: folderName,
              #parentFolderId: parentFolderId,
              #metadata: metadata,
            }),
            returnValue: _i6.Future<_i2.GoogleDriveFile>.value(
              _FakeGoogleDriveFile_0(
                this,
                Invocation.method(#createFolder, [], {
                  #folderName: folderName,
                  #parentFolderId: parentFolderId,
                  #metadata: metadata,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.GoogleDriveFile>);

  @override
  _i6.Future<List<_i2.GoogleDriveFile>> listFilesInFolder({
    String? folderId,
    String? query,
    int? maxResults,
    bool? includeSubfolders = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#listFilesInFolder, [], {
              #folderId: folderId,
              #query: query,
              #maxResults: maxResults,
              #includeSubfolders: includeSubfolders,
            }),
            returnValue: _i6.Future<List<_i2.GoogleDriveFile>>.value(
              <_i2.GoogleDriveFile>[],
            ),
          )
          as _i6.Future<List<_i2.GoogleDriveFile>>);

  @override
  _i6.Future<Map<String, String>> createClaimFolderStructure({
    required String? claimId,
    required String? claimTitle,
    List<String>? documentCategories,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createClaimFolderStructure, [], {
              #claimId: claimId,
              #claimTitle: claimTitle,
              #documentCategories: documentCategories,
            }),
            returnValue: _i6.Future<Map<String, String>>.value(
              <String, String>{},
            ),
          )
          as _i6.Future<Map<String, String>>);

  @override
  _i6.Future<void> deleteFolder(String? folderId, {bool? recursive = false}) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteFolder,
              [folderId],
              {#recursive: recursive},
            ),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setFilePermissions(
    String? fileId,
    List<Map<String, dynamic>>? permissions,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setFilePermissions, [fileId, permissions]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<List<_i10.GoogleDrivePermission>> getFilePermissions(
    String? fileId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getFilePermissions, [fileId]),
            returnValue: _i6.Future<List<_i10.GoogleDrivePermission>>.value(
              <_i10.GoogleDrivePermission>[],
            ),
          )
          as _i6.Future<List<_i10.GoogleDrivePermission>>);

  @override
  _i6.Future<void> shareFileWithUser({
    required String? fileId,
    required String? emailAddress,
    required _i10.GoogleDrivePermissionRole? role,
    DateTime? expirationTime,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#shareFileWithUser, [], {
              #fileId: fileId,
              #emailAddress: emailAddress,
              #role: role,
              #expirationTime: expirationTime,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<String> createPublicSharingLink(String? fileId) =>
      (super.noSuchMethod(
            Invocation.method(#createPublicSharingLink, [fileId]),
            returnValue: _i6.Future<String>.value(
              _i9.dummyValue<String>(
                this,
                Invocation.method(#createPublicSharingLink, [fileId]),
              ),
            ),
          )
          as _i6.Future<String>);

  @override
  _i6.Future<void> revokeFilePermission(String? fileId, String? permissionId) =>
      (super.noSuchMethod(
            Invocation.method(#revokeFilePermission, [fileId, permissionId]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i2.GoogleDriveBatchResult<_i2.GoogleDriveFile>> uploadFiles({
    required List<_i7.File>? files,
    required List<String>? fileNames,
    String? folderId,
    dynamic Function(_i2.GoogleDriveUploadProgress)? onProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadFiles, [], {
              #files: files,
              #fileNames: fileNames,
              #folderId: folderId,
              #onProgress: onProgress,
            }),
            returnValue:
                _i6.Future<
                  _i2.GoogleDriveBatchResult<_i2.GoogleDriveFile>
                >.value(
                  _FakeGoogleDriveBatchResult_1<_i2.GoogleDriveFile>(
                    this,
                    Invocation.method(#uploadFiles, [], {
                      #files: files,
                      #fileNames: fileNames,
                      #folderId: folderId,
                      #onProgress: onProgress,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.GoogleDriveBatchResult<_i2.GoogleDriveFile>>);

  @override
  _i6.Future<_i2.GoogleDriveBatchResult<String>> deleteFiles(
    List<String>? fileIds,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#deleteFiles, [fileIds]),
            returnValue: _i6.Future<_i2.GoogleDriveBatchResult<String>>.value(
              _FakeGoogleDriveBatchResult_1<String>(
                this,
                Invocation.method(#deleteFiles, [fileIds]),
              ),
            ),
          )
          as _i6.Future<_i2.GoogleDriveBatchResult<String>>);

  @override
  _i6.Future<Map<String, dynamic>> checkServiceHealth() =>
      (super.noSuchMethod(
            Invocation.method(#checkServiceHealth, []),
            returnValue: _i6.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<List<_i2.GoogleDriveFile>> searchFiles({
    String? nameQuery,
    String? contentQuery,
    String? mimeType,
    String? folderId,
    int? maxResults,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#searchFiles, [], {
              #nameQuery: nameQuery,
              #contentQuery: contentQuery,
              #mimeType: mimeType,
              #folderId: folderId,
              #maxResults: maxResults,
            }),
            returnValue: _i6.Future<List<_i2.GoogleDriveFile>>.value(
              <_i2.GoogleDriveFile>[],
            ),
          )
          as _i6.Future<List<_i2.GoogleDriveFile>>);

  @override
  _i6.Future<Map<String, dynamic>> getStorageUsage() =>
      (super.noSuchMethod(
            Invocation.method(#getStorageUsage, []),
            returnValue: _i6.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<_i2.GoogleDriveFile> copyFile({
    required String? sourceFileId,
    required String? newName,
    String? destinationFolderId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#copyFile, [], {
              #sourceFileId: sourceFileId,
              #newName: newName,
              #destinationFolderId: destinationFolderId,
            }),
            returnValue: _i6.Future<_i2.GoogleDriveFile>.value(
              _FakeGoogleDriveFile_0(
                this,
                Invocation.method(#copyFile, [], {
                  #sourceFileId: sourceFileId,
                  #newName: newName,
                  #destinationFolderId: destinationFolderId,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.GoogleDriveFile>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [EncryptionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockEncryptionService extends _i1.Mock implements _i3.EncryptionService {
  MockEncryptionService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i3.EncryptionResult> encryptData(
    String? data, {
    String? key,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #encryptData,
              [data],
              {#key: key, #metadata: metadata},
            ),
            returnValue: _i6.Future<_i3.EncryptionResult>.value(
              _FakeEncryptionResult_2(
                this,
                Invocation.method(
                  #encryptData,
                  [data],
                  {#key: key, #metadata: metadata},
                ),
              ),
            ),
          )
          as _i6.Future<_i3.EncryptionResult>);

  @override
  _i6.Future<String> decryptData(
    _i3.EncryptionResult? encryptionResult,
    String? key,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#decryptData, [encryptionResult, key]),
            returnValue: _i6.Future<String>.value(
              _i9.dummyValue<String>(
                this,
                Invocation.method(#decryptData, [encryptionResult, key]),
              ),
            ),
          )
          as _i6.Future<String>);

  @override
  _i6.Future<_i3.EncryptionResult> encryptFile(
    _i8.Uint8List? fileBytes, {
    String? key,
    String? filename,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #encryptFile,
              [fileBytes],
              {#key: key, #filename: filename, #metadata: metadata},
            ),
            returnValue: _i6.Future<_i3.EncryptionResult>.value(
              _FakeEncryptionResult_2(
                this,
                Invocation.method(
                  #encryptFile,
                  [fileBytes],
                  {#key: key, #filename: filename, #metadata: metadata},
                ),
              ),
            ),
          )
          as _i6.Future<_i3.EncryptionResult>);

  @override
  _i6.Future<_i8.Uint8List> decryptFile(
    _i3.EncryptionResult? encryptionResult,
    String? key,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#decryptFile, [encryptionResult, key]),
            returnValue: _i6.Future<_i8.Uint8List>.value(_i8.Uint8List(0)),
          )
          as _i6.Future<_i8.Uint8List>);

  @override
  bool validateEncryptionResult(_i3.EncryptionResult? result) =>
      (super.noSuchMethod(
            Invocation.method(#validateEncryptionResult, [result]),
            returnValue: false,
          )
          as bool);

  @override
  Map<String, dynamic> getEncryptionStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getEncryptionStatus, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);
}

/// A class which mocks [EnhancedAuditService].
///
/// See the documentation for Mockito's code generation for more information.
class MockEnhancedAuditService extends _i1.Mock
    implements _i11.EnhancedAuditService {
  MockEnhancedAuditService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> logDocumentAccess({
    required String? documentId,
    required String? action,
    required bool? success,
    String? userId,
    String? userEmail,
    String? userRole,
    String? ipAddress,
    String? userAgent,
    String? sessionId,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logDocumentAccess, [], {
              #documentId: documentId,
              #action: action,
              #success: success,
              #userId: userId,
              #userEmail: userEmail,
              #userRole: userRole,
              #ipAddress: ipAddress,
              #userAgent: userAgent,
              #sessionId: sessionId,
              #errorMessage: errorMessage,
              #metadata: metadata,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> logAuthentication({
    required String? userId,
    required bool? success,
    String? userEmail,
    String? errorMessage,
    String? ipAddress,
    String? userAgent,
    String? sessionId,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logAuthentication, [], {
              #userId: userId,
              #success: success,
              #userEmail: userEmail,
              #errorMessage: errorMessage,
              #ipAddress: ipAddress,
              #userAgent: userAgent,
              #sessionId: sessionId,
              #metadata: metadata,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> logPermissionChange({
    required String? resourceId,
    required Map<String, dynamic>? changes,
    required bool? success,
    String? userId,
    String? userEmail,
    String? userRole,
    String? errorMessage,
    String? ipAddress,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logPermissionChange, [], {
              #resourceId: resourceId,
              #changes: changes,
              #success: success,
              #userId: userId,
              #userEmail: userEmail,
              #userRole: userRole,
              #errorMessage: errorMessage,
              #ipAddress: ipAddress,
              #metadata: metadata,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> logDataProcessing({
    required String? action,
    required String? dataType,
    required bool? success,
    String? userId,
    String? userEmail,
    String? userRole,
    String? errorMessage,
    String? ipAddress,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logDataProcessing, [], {
              #action: action,
              #dataType: dataType,
              #success: success,
              #userId: userId,
              #userEmail: userEmail,
              #userRole: userRole,
              #errorMessage: errorMessage,
              #ipAddress: ipAddress,
              #metadata: metadata,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> logSecurityEvent(_i12.SecurityEvent? event) =>
      (super.noSuchMethod(
            Invocation.method(#logSecurityEvent, [event]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<Map<String, dynamic>> generateComplianceReport({
    required DateTime? startDate,
    required DateTime? endDate,
    List<String>? complianceTags,
    String? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#generateComplianceReport, [], {
              #startDate: startDate,
              #endDate: endDate,
              #complianceTags: complianceTags,
              #userId: userId,
            }),
            returnValue: _i6.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<bool> verifyAuditIntegrity({required String? auditId}) =>
      (super.noSuchMethod(
            Invocation.method(#verifyAuditIntegrity, [], {#auditId: auditId}),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  Map<String, dynamic> getStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getStatus, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [PocketBaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPocketBaseService extends _i1.Mock implements _i13.PocketBaseService {
  MockPocketBaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.PocketBase get client =>
      (super.noSuchMethod(
            Invocation.getter(#client),
            returnValue: _FakePocketBase_3(this, Invocation.getter(#client)),
          )
          as _i4.PocketBase);

  @override
  _i4.PocketBase get pb =>
      (super.noSuchMethod(
            Invocation.getter(#pb),
            returnValue: _FakePocketBase_3(this, Invocation.getter(#pb)),
          )
          as _i4.PocketBase);

  @override
  _i4.AuthStore get authStore =>
      (super.noSuchMethod(
            Invocation.getter(#authStore),
            returnValue: _FakeAuthStore_4(this, Invocation.getter(#authStore)),
          )
          as _i4.AuthStore);

  @override
  bool get isSignedIn =>
      (super.noSuchMethod(Invocation.getter(#isSignedIn), returnValue: false)
          as bool);

  @override
  _i6.Future<void> init() =>
      (super.noSuchMethod(
            Invocation.method(#init, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.RecordAuth> signIn(String? email, String? password) =>
      (super.noSuchMethod(
            Invocation.method(#signIn, [email, password]),
            returnValue: _i6.Future<_i4.RecordAuth>.value(
              _FakeRecordAuth_5(
                this,
                Invocation.method(#signIn, [email, password]),
              ),
            ),
          )
          as _i6.Future<_i4.RecordAuth>);

  @override
  _i6.Future<_i4.RecordModel> signUp({
    required String? email,
    required String? password,
    required String? passwordConfirm,
    required String? userType,
    required String? name,
    String? firstName,
    String? lastName,
    String? level,
    String? status,
    Map<String, dynamic>? customData = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUp, [], {
              #email: email,
              #password: password,
              #passwordConfirm: passwordConfirm,
              #userType: userType,
              #name: name,
              #firstName: firstName,
              #lastName: lastName,
              #level: level,
              #status: status,
              #customData: customData,
            }),
            returnValue: _i6.Future<_i4.RecordModel>.value(
              _FakeRecordModel_6(
                this,
                Invocation.method(#signUp, [], {
                  #email: email,
                  #password: password,
                  #passwordConfirm: passwordConfirm,
                  #userType: userType,
                  #name: name,
                  #firstName: firstName,
                  #lastName: lastName,
                  #level: level,
                  #status: status,
                  #customData: customData,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.RecordModel>);

  @override
  _i6.Future<void> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> handleSessionExpiration() =>
      (super.noSuchMethod(
            Invocation.method(#handleSessionExpiration, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> refreshFCMToken() =>
      (super.noSuchMethod(
            Invocation.method(#refreshFCMToken, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> requestPasswordReset(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#requestPasswordReset, [email]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> confirmPasswordReset({
    required String? token,
    required String? password,
    required String? passwordConfirm,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#confirmPasswordReset, [], {
              #token: token,
              #password: password,
              #passwordConfirm: passwordConfirm,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> requestEmailVerification(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#requestEmailVerification, [email]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> confirmEmailVerification(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#confirmEmailVerification, [token]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> changePassword({
    required String? oldPassword,
    required String? newPassword,
    required String? newPasswordConfirm,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#changePassword, [], {
              #oldPassword: oldPassword,
              #newPassword: newPassword,
              #newPasswordConfirm: newPasswordConfirm,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> requestOTP(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#requestOTP, [email]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<bool> verifyOTP(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#verifyOTP, [token]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<void> optOutAccount() =>
      (super.noSuchMethod(
            Invocation.method(#optOutAccount, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.RecordModel> createSolicitorProfile({
    required String? userId,
    required String? lawFirmName,
    required String? solicitorName,
    required String? position,
    required String? contactNumber,
    required String? firmAddress,
    required String? firmRegistrationNumber,
    String? puStatus = 'pending',
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createSolicitorProfile, [], {
              #userId: userId,
              #lawFirmName: lawFirmName,
              #solicitorName: solicitorName,
              #position: position,
              #contactNumber: contactNumber,
              #firmAddress: firmAddress,
              #firmRegistrationNumber: firmRegistrationNumber,
              #puStatus: puStatus,
            }),
            returnValue: _i6.Future<_i4.RecordModel>.value(
              _FakeRecordModel_6(
                this,
                Invocation.method(#createSolicitorProfile, [], {
                  #userId: userId,
                  #lawFirmName: lawFirmName,
                  #solicitorName: solicitorName,
                  #position: position,
                  #contactNumber: contactNumber,
                  #firmAddress: firmAddress,
                  #firmRegistrationNumber: firmRegistrationNumber,
                  #puStatus: puStatus,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.RecordModel>);

  @override
  _i6.Future<_i4.RecordModel> createCoFunderProfile({
    required String? userId,
    int? currentLevel = 1,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createCoFunderProfile, [], {
              #userId: userId,
              #currentLevel: currentLevel,
            }),
            returnValue: _i6.Future<_i4.RecordModel>.value(
              _FakeRecordModel_6(
                this,
                Invocation.method(#createCoFunderProfile, [], {
                  #userId: userId,
                  #currentLevel: currentLevel,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.RecordModel>);

  @override
  _i6.Future<_i4.RecordModel> createRecord({
    required String? collectionName,
    required Map<String, dynamic>? data,
    Map<String, dynamic>? query = const {},
    List<_i14.MultipartFile>? files = const [],
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createRecord, [], {
              #collectionName: collectionName,
              #data: data,
              #query: query,
              #files: files,
            }),
            returnValue: _i6.Future<_i4.RecordModel>.value(
              _FakeRecordModel_6(
                this,
                Invocation.method(#createRecord, [], {
                  #collectionName: collectionName,
                  #data: data,
                  #query: query,
                  #files: files,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.RecordModel>);

  @override
  _i6.Future<_i4.RecordModel> updateRecord({
    required String? collectionName,
    required String? recordId,
    required Map<String, dynamic>? data,
    Map<String, dynamic>? query = const {},
    List<_i14.MultipartFile>? files = const [],
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateRecord, [], {
              #collectionName: collectionName,
              #recordId: recordId,
              #data: data,
              #query: query,
              #files: files,
            }),
            returnValue: _i6.Future<_i4.RecordModel>.value(
              _FakeRecordModel_6(
                this,
                Invocation.method(#updateRecord, [], {
                  #collectionName: collectionName,
                  #recordId: recordId,
                  #data: data,
                  #query: query,
                  #files: files,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.RecordModel>);

  @override
  _i6.Future<List<_i4.RecordModel>> getFullList({
    required String? collectionName,
    int? batch = 200,
    String? filter,
    String? sort,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getFullList, [], {
              #collectionName: collectionName,
              #batch: batch,
              #filter: filter,
              #sort: sort,
            }),
            returnValue: _i6.Future<List<_i4.RecordModel>>.value(
              <_i4.RecordModel>[],
            ),
          )
          as _i6.Future<List<_i4.RecordModel>>);

  @override
  _i6.Future<_i4.ResultList<_i4.RecordModel>> getList({
    required String? collectionName,
    int? page = 1,
    int? perPage = 30,
    String? filter,
    String? sort,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getList, [], {
              #collectionName: collectionName,
              #page: page,
              #perPage: perPage,
              #filter: filter,
              #sort: sort,
            }),
            returnValue: _i6.Future<_i4.ResultList<_i4.RecordModel>>.value(
              _FakeResultList_7<_i4.RecordModel>(
                this,
                Invocation.method(#getList, [], {
                  #collectionName: collectionName,
                  #page: page,
                  #perPage: perPage,
                  #filter: filter,
                  #sort: sort,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.ResultList<_i4.RecordModel>>);

  @override
  _i6.Future<_i4.RecordModel> getOne({
    required String? collectionName,
    required String? recordId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getOne, [], {
              #collectionName: collectionName,
              #recordId: recordId,
            }),
            returnValue: _i6.Future<_i4.RecordModel>.value(
              _FakeRecordModel_6(
                this,
                Invocation.method(#getOne, [], {
                  #collectionName: collectionName,
                  #recordId: recordId,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.RecordModel>);

  @override
  _i6.Future<void> deleteRecord({
    required String? collectionName,
    required String? recordId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#deleteRecord, [], {
              #collectionName: collectionName,
              #recordId: recordId,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<Map<String, String>> uploadFileAndGetId({
    required String? targetCollectionName,
    required _i14.MultipartFile? multipartFile,
    Map<String, String>? body = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#uploadFileAndGetId, [], {
              #targetCollectionName: targetCollectionName,
              #multipartFile: multipartFile,
              #body: body,
            }),
            returnValue: _i6.Future<Map<String, String>>.value(
              <String, String>{},
            ),
          )
          as _i6.Future<Map<String, String>>);
}
