import 'dart:math';
import 'package:flutter_test/flutter_test.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/performance_monitoring_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/document_cache_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/utils/performance_utils.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/performance_metrics.dart';

/// Comprehensive performance test suite for document management system
void main() {
  group('Performance Tests', () {
    late PerformanceMonitoringService performanceService;
    late DocumentCacheService cacheService;

    setUpAll(() async {
      performanceService = PerformanceMonitoringService();
      cacheService = DocumentCacheService();

      await performanceService.initialize();
      await cacheService.initialize();
    });

    tearDownAll(() async {
      performanceService.dispose();
      await cacheService.dispose();
    });

    group('Performance Monitoring Service Tests', () {
      test('should track operation performance correctly', () async {
        const operationType = 'test_operation';
        const expectedDuration = 100; // milliseconds

        final result = await performanceService.trackOperation(
          operationType,
          () async {
            await Future.delayed(Duration(milliseconds: expectedDuration));
            return 'test_result';
          },
          metadata: {'test': 'data'},
        );

        expect(result, equals('test_result'));

        final stats = performanceService.getOperationStatistics(operationType);
        expect(stats, isNotNull);
        expect(stats!.totalOperations, equals(1));
        expect(stats.successfulOperations, equals(1));
        expect(stats.successRate, equals(1.0));
        expect(stats.averageDurationMs, greaterThanOrEqualTo(expectedDuration));
      });

      test('should handle operation failures correctly', () async {
        const operationType = 'failing_operation';

        expect(
          () async =>
              await performanceService.trackOperation(operationType, () async {
                throw Exception('Test error');
              }),
          throwsA(isA<Exception>()),
        );

        final stats = performanceService.getOperationStatistics(operationType);
        expect(stats, isNotNull);
        expect(stats!.totalOperations, equals(1));
        expect(stats.failedOperations, equals(1));
        expect(stats.successRate, equals(0.0));
      });

      test('should calculate performance statistics correctly', () async {
        const operationType = 'batch_operation';
        final durations = [100, 200, 300, 400, 500]; // milliseconds

        // Execute multiple operations with known durations
        for (final duration in durations) {
          await performanceService.trackOperation(operationType, () async {
            await Future.delayed(Duration(milliseconds: duration));
            return 'result';
          });
        }

        final stats = performanceService.getOperationStatistics(operationType);
        expect(stats, isNotNull);
        expect(stats!.totalOperations, equals(durations.length));
        expect(stats.successfulOperations, equals(durations.length));
        expect(stats.successRate, equals(1.0));

        // Check that average is approximately correct (allowing for test timing variance)
        final expectedAverage =
            durations.reduce((a, b) => a + b) / durations.length;
        expect(stats.averageDurationMs, closeTo(expectedAverage, 50));
      });

      test('should generate performance snapshot', () async {
        final snapshot = await performanceService.getPerformanceSnapshot();

        expect(snapshot.timestamp, isNotNull);
        expect(snapshot.systemMetrics, isNotNull);
        expect(snapshot.networkMetrics, isNotNull);
        expect(snapshot.cacheMetrics, isNotNull);
        expect(snapshot.operationStats, isNotNull);
      });
    });

    group('Performance Utils Tests', () {
      test('should calculate percentiles correctly', () {
        final durations = [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000];
        final percentiles = PerformanceUtils.calculatePercentiles(durations);

        expect(percentiles['min'], equals(100.0));
        expect(percentiles['max'], equals(1000.0));
        expect(percentiles['avg'], equals(550.0));
        expect(percentiles['p50'], equals(500.0)); // Median
        expect(percentiles['p95'], equals(950.0)); // 95th percentile
      });

      test('should calculate network speed correctly', () {
        const fileSizeBytes = 1024 * 1024; // 1MB
        const durationMs = 1000; // 1 second

        final speed = PerformanceUtils.calculateNetworkSpeed(
          fileSizeBytes,
          durationMs,
        );
        expect(speed, equals(fileSizeBytes)); // 1MB/s
      });

      test('should format file sizes correctly', () {
        expect(PerformanceUtils.formatFileSize(512), equals('512 B'));
        expect(PerformanceUtils.formatFileSize(1024), equals('1.0 KB'));
        expect(PerformanceUtils.formatFileSize(1024 * 1024), equals('1.0 MB'));
        expect(
          PerformanceUtils.formatFileSize(1024 * 1024 * 1024),
          equals('1.0 GB'),
        );
      });

      test('should format durations correctly', () {
        expect(PerformanceUtils.formatDuration(500), equals('500ms'));
        expect(PerformanceUtils.formatDuration(1500), equals('1.5s'));
        expect(PerformanceUtils.formatDuration(90000), equals('1.5m'));
      });

      test('should check performance targets correctly', () {
        // Upload operation under 30 seconds for 10MB file
        final uploadMetrics = PerformanceMetrics(
          operationType: 'upload',
          operationId: 'test_upload',
          startTime: DateTime.now().subtract(const Duration(seconds: 25)),
          endTime: DateTime.now(),
          success: true,
          fileSizeBytes: 10 * 1024 * 1024,
        );
        expect(PerformanceUtils.meetsPerformanceTargets(uploadMetrics), isTrue);

        // Download operation under 10 seconds for 10MB file
        final downloadMetrics = PerformanceMetrics(
          operationType: 'download',
          operationId: 'test_download',
          startTime: DateTime.now().subtract(const Duration(seconds: 8)),
          endTime: DateTime.now(),
          success: true,
          fileSizeBytes: 10 * 1024 * 1024,
        );
        expect(
          PerformanceUtils.meetsPerformanceTargets(downloadMetrics),
          isTrue,
        );

        // API call under 2 seconds
        final apiMetrics = PerformanceMetrics(
          operationType: 'api_call',
          operationId: 'test_api',
          startTime: DateTime.now().subtract(
            const Duration(milliseconds: 1500),
          ),
          endTime: DateTime.now(),
          success: true,
        );
        expect(PerformanceUtils.meetsPerformanceTargets(apiMetrics), isTrue);
      });

      test(
        'should calculate optimal chunk size based on network conditions',
        () {
          const fileSizeBytes = 10 * 1024 * 1024; // 10MB

          // Slow network
          const slowNetwork = NetworkMetrics(
            connectionType: 'cellular',
            bandwidthMbps: 0.5,
            latencyMs: 200,
            packetLossPercent: 2.0,
            isOnline: true,
            isMetered: true,
          );
          final slowChunkSize = PerformanceUtils.calculateOptimalChunkSize(
            slowNetwork,
            fileSizeBytes,
          );
          expect(slowChunkSize, equals(256 * 1024)); // 256KB for slow network

          // Fast network
          const fastNetwork = NetworkMetrics(
            connectionType: 'wifi',
            bandwidthMbps: 50.0,
            latencyMs: 10,
            packetLossPercent: 0.1,
            isOnline: true,
            isMetered: false,
          );
          final fastChunkSize = PerformanceUtils.calculateOptimalChunkSize(
            fastNetwork,
            fileSizeBytes,
          );
          expect(
            fastChunkSize,
            equals(8 * 1024 * 1024),
          ); // 8MB for fast network
        },
      );

      test(
        'should calculate optimal concurrency based on system resources',
        () {
          // Low resource system
          const lowResourceSystem = SystemResourceMetrics(
            cpuUsagePercent: 85.0,
            memoryUsagePercent: 90.0,
            diskUsagePercent: 70.0,
            activeConnections: 10,
            batteryLevel: 0.15,
            isLowPowerMode: true,
          );
          final lowConcurrency = PerformanceUtils.calculateOptimalConcurrency(
            lowResourceSystem,
          );
          expect(lowConcurrency, equals(1)); // Minimum concurrency

          // High resource system
          const highResourceSystem = SystemResourceMetrics(
            cpuUsagePercent: 30.0,
            memoryUsagePercent: 40.0,
            diskUsagePercent: 50.0,
            activeConnections: 3,
            batteryLevel: 0.80,
            isLowPowerMode: false,
          );
          final highConcurrency = PerformanceUtils.calculateOptimalConcurrency(
            highResourceSystem,
          );
          expect(highConcurrency, equals(8)); // Maximum concurrency
        },
      );

      test('should determine retry logic correctly', () {
        // Network errors should be retried
        expect(PerformanceUtils.shouldRetry('Network timeout', 1), isTrue);
        expect(PerformanceUtils.shouldRetry('Connection failed', 2), isTrue);

        // Server errors should be retried
        expect(
          PerformanceUtils.shouldRetry('503 Service Unavailable', 1),
          isTrue,
        );
        expect(PerformanceUtils.shouldRetry('502 Bad Gateway', 2), isTrue);

        // Client errors should not be retried
        expect(PerformanceUtils.shouldRetry('400 Bad Request', 1), isFalse);
        expect(PerformanceUtils.shouldRetry('401 Unauthorized', 1), isFalse);
        expect(PerformanceUtils.shouldRetry('404 Not Found', 1), isFalse);

        // Max retries exceeded
        expect(PerformanceUtils.shouldRetry('Network timeout', 3), isFalse);
      });

      test('should calculate exponential backoff correctly', () {
        final delay1 = PerformanceUtils.calculateBackoffDelay(1);
        final delay2 = PerformanceUtils.calculateBackoffDelay(2);
        final delay3 = PerformanceUtils.calculateBackoffDelay(3);

        expect(
          delay1.inMilliseconds,
          greaterThanOrEqualTo(1000),
        ); // At least 1 second
        expect(
          delay2.inMilliseconds,
          greaterThanOrEqualTo(2000),
        ); // At least 2 seconds
        expect(
          delay3.inMilliseconds,
          greaterThanOrEqualTo(4000),
        ); // At least 4 seconds

        // Should not exceed max delay
        expect(delay1.inMilliseconds, lessThanOrEqualTo(30000));
        expect(delay2.inMilliseconds, lessThanOrEqualTo(30000));
        expect(delay3.inMilliseconds, lessThanOrEqualTo(30000));
      });
    });

    group('Cache Performance Tests', () {
      test('should measure cache access performance', () async {
        const testKey = 'test_cache_key';
        const testData = 'test_cache_data';

        // Measure cache write performance
        final writeStart = DateTime.now();
        await cacheService.cacheUrl(testKey, testData);
        final writeEnd = DateTime.now();
        final writeDuration = writeEnd.difference(writeStart).inMilliseconds;

        expect(writeDuration, lessThan(100)); // Should be fast

        // Measure cache read performance
        final readStart = DateTime.now();
        final cachedData = await cacheService.getCachedUrl(testKey);
        final readEnd = DateTime.now();
        final readDuration = readEnd.difference(readStart).inMilliseconds;

        expect(cachedData, equals(testData));
        expect(readDuration, lessThan(50)); // Should be very fast
      });

      test('should maintain good cache hit ratio', () async {
        const numOperations = 100;
        const numUniqueKeys = 20;

        // Populate cache with some data
        for (int i = 0; i < numUniqueKeys; i++) {
          await cacheService.cacheUrl('key_$i', 'data_$i');
        }

        int hits = 0;
        int misses = 0;

        // Perform random cache accesses
        final random = Random();
        for (int i = 0; i < numOperations; i++) {
          final keyIndex = random.nextInt(
            numUniqueKeys * 2,
          ); // Some keys won't exist
          final data = await cacheService.getCachedUrl('key_$keyIndex');

          if (data != null) {
            hits++;
          } else {
            misses++;
          }
        }

        final hitRatio = hits / (hits + misses);
        expect(hitRatio, greaterThan(0.4)); // Should have reasonable hit ratio
      });
    });

    group('Load Testing', () {
      test('should handle concurrent operations efficiently', () async {
        const numConcurrentOps = 10;
        const operationType = 'concurrent_test';

        final futures = <Future>[];
        final startTime = DateTime.now();

        // Launch concurrent operations
        for (int i = 0; i < numConcurrentOps; i++) {
          futures.add(
            performanceService.trackOperation(operationType, () async {
              await Future.delayed(
                Duration(milliseconds: 100 + Random().nextInt(100)),
              );
              return 'result_$i';
            }, operationId: 'op_$i'),
          );
        }

        // Wait for all operations to complete
        final results = await Future.wait(futures);
        final endTime = DateTime.now();
        final totalDuration = endTime.difference(startTime).inMilliseconds;

        expect(results.length, equals(numConcurrentOps));
        expect(
          totalDuration,
          lessThan(500),
        ); // Should complete in reasonable time

        final stats = performanceService.getOperationStatistics(operationType);
        expect(stats, isNotNull);
        expect(stats!.totalOperations, equals(numConcurrentOps));
        expect(stats.successRate, equals(1.0));
      });

      test('should maintain performance under stress', () async {
        const numOperations = 50;
        const operationType = 'stress_test';

        final startTime = DateTime.now();

        // Execute many operations sequentially
        for (int i = 0; i < numOperations; i++) {
          await performanceService.trackOperation(operationType, () async {
            await Future.delayed(const Duration(milliseconds: 10));
            return 'result_$i';
          });
        }

        final endTime = DateTime.now();
        final totalDuration = endTime.difference(startTime).inMilliseconds;

        final stats = performanceService.getOperationStatistics(operationType);
        expect(stats, isNotNull);
        expect(stats!.totalOperations, equals(numOperations));
        expect(stats.successRate, equals(1.0));

        // Average operation time should remain reasonable
        expect(stats.averageDurationMs, lessThan(50));

        // Total time should be reasonable
        expect(
          totalDuration,
          lessThan(numOperations * 30),
        ); // Allow some overhead
      });
    });
  });
}
