#!/bin/bash

# 3Pay Global Document Management Testing Setup Script
# This script sets up the testing environment and generates necessary mock files

set -e  # Exit on any error

echo "🚀 Setting up 3Pay Global Document Management Testing Environment"
echo "=================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Flutter is installed
check_flutter() {
    print_status "Checking Flutter installation..."
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    flutter --version
    print_success "Flutter is installed"
}

# Check if we're in the correct directory
check_directory() {
    print_status "Checking project directory..."
    if [ ! -f "pubspec.yaml" ]; then
        print_error "pubspec.yaml not found. Please run this script from the project root directory."
        exit 1
    fi
    
    if [ ! -d "test" ]; then
        print_error "test directory not found. Please ensure you're in the correct project directory."
        exit 1
    fi
    
    print_success "Project directory verified"
}

# Install dependencies
install_dependencies() {
    print_status "Installing Flutter dependencies..."
    flutter pub get
    
    if [ $? -eq 0 ]; then
        print_success "Dependencies installed successfully"
    else
        print_error "Failed to install dependencies"
        exit 1
    fi
}

# Generate mock files
generate_mocks() {
    print_status "Generating mock files for testing..."
    
    # List of test files that need mock generation
    test_files=(
        "test/src/core/services/google_drive_auth_service_test.dart"
        "test/src/core/services/encryption_service_test.dart"
        "test/src/core/services/credential_management_service_test.dart"
        "test/src/core/services/enhanced_audit_service_test.dart"
        "test/src/core/services/security_incident_service_test.dart"
        "test/src/core/services/privacy_protection_service_test.dart"
        "test/integration/document_management_integration_test.dart"
        "test/security/authentication_security_test.dart"
        "test/performance/document_performance_test.dart"
        "test/ui/document_management_ui_test.dart"
    )
    
    # Generate mocks for each test file
    for test_file in "${test_files[@]}"; do
        if [ -f "$test_file" ]; then
            print_status "Generating mocks for $test_file..."
            flutter packages pub run build_runner build --build-filter="$test_file"
        else
            print_warning "Test file not found: $test_file"
        fi
    done
    
    print_success "Mock generation completed"
}

# Create test directories
create_directories() {
    print_status "Creating test directories..."
    
    directories=(
        "test/reports"
        "test/reports/performance"
        "test/reports/security"
        "test/reports/coverage"
        "test/fixtures"
        "test/mocks"
        "coverage"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_status "Created directory: $dir"
        fi
    done
    
    print_success "Test directories created"
}

# Create test fixtures
create_fixtures() {
    print_status "Creating test fixtures..."
    
    # Create test credentials file
    cat > test/fixtures/test_credentials.json << EOF
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
EOF
    
    # Create test data files
    cat > test/fixtures/test_claim_data.json << EOF
{
  "id": "test-claim-123",
  "title": "Test Litigation Claim",
  "description": "Test claim for document management testing",
  "stage": "STAGE 1: PRE ACTION",
  "documentRepository": [
    {
      "logicalName": "Contracts",
      "files": [
        {
          "id": "test-doc-1",
          "filename": "test-contract.pdf",
          "file_size": 1024,
          "uploaded_at": "2024-01-15T10:00:00Z",
          "uploaded_by": "test-user-123",
          "google_drive_id": "test-google-drive-id-1"
        }
      ]
    }
  ]
}
EOF
    
    print_success "Test fixtures created"
}

# Validate test configuration
validate_config() {
    print_status "Validating test configuration..."
    
    if [ ! -f "test/test_config.yaml" ]; then
        print_warning "test_config.yaml not found, using default configuration"
    else
        print_success "Test configuration found"
    fi
    
    # Check if all required test files exist
    required_files=(
        "test/run_comprehensive_tests.dart"
        "test/TESTING_DOCUMENTATION.md"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Required file not found: $file"
            exit 1
        fi
    done
    
    print_success "Test configuration validated"
}

# Run a quick test to verify setup
run_verification_test() {
    print_status "Running verification test..."
    
    # Run a simple test to verify the setup
    if flutter test test/src/core/services/google_drive_service_test.dart --no-coverage > /dev/null 2>&1; then
        print_success "Verification test passed"
    else
        print_warning "Verification test failed, but setup is complete"
        print_warning "You may need to implement the actual service classes"
    fi
}

# Display usage information
show_usage() {
    echo ""
    echo "📋 Test Setup Complete!"
    echo "======================"
    echo ""
    echo "Available test commands:"
    echo ""
    echo "  # Run all tests"
    echo "  dart test/run_comprehensive_tests.dart"
    echo ""
    echo "  # Run specific test categories"
    echo "  dart test/run_comprehensive_tests.dart --unit"
    echo "  dart test/run_comprehensive_tests.dart --integration"
    echo "  dart test/run_comprehensive_tests.dart --security"
    echo "  dart test/run_comprehensive_tests.dart --performance"
    echo "  dart test/run_comprehensive_tests.dart --ui"
    echo ""
    echo "  # Run with coverage"
    echo "  flutter test --coverage"
    echo ""
    echo "  # Generate coverage report"
    echo "  genhtml coverage/lcov.info -o coverage/html"
    echo ""
    echo "📄 Documentation: test/TESTING_DOCUMENTATION.md"
    echo "⚙️  Configuration: test/test_config.yaml"
    echo "📊 Reports will be generated in: test/reports/"
    echo ""
}

# Main execution
main() {
    print_status "Starting test environment setup..."
    
    check_flutter
    check_directory
    install_dependencies
    create_directories
    create_fixtures
    generate_mocks
    validate_config
    run_verification_test
    
    print_success "Test environment setup completed successfully!"
    show_usage
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "3Pay Global Document Management Testing Setup Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --clean        Clean previous test artifacts"
        echo "  --mocks-only   Only generate mock files"
        echo ""
        exit 0
        ;;
    --clean)
        print_status "Cleaning previous test artifacts..."
        rm -rf test/reports/*
        rm -rf coverage/*
        rm -rf test/fixtures/*
        rm -rf .dart_tool/build/
        print_success "Cleanup completed"
        ;;
    --mocks-only)
        print_status "Generating mocks only..."
        check_flutter
        check_directory
        generate_mocks
        print_success "Mock generation completed"
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
