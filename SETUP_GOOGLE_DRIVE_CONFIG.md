# Google Drive Configuration Setup

## Issue
The app is failing with the error:
```
GoogleDriveConfigException: No Google Drive configuration found for environment: development
```

## Solution
You need to create the `google_drive_config` collection in PocketBase and add a development configuration record.

## Step 1: Create the Collection

1. Open PocketBase Admin Interface at: https://3paydb-new-production.up.railway.app/_/
2. Navigate to **Collections**
3. Click **+ New Collection**
4. Set **Collection name**: `google_drive_config`
5. Set **Collection type**: `Base collection`

## Step 2: Add Fields

Add the following fields to the collection:

### service_account_email
- **Type**: Email
- **Required**: Yes
- **Description**: Service account email for Google Drive API

### root_folder_id
- **Type**: Text
- **Required**: Yes
- **Description**: Root folder ID in Google Drive for document storage

### api_quota_limit
- **Type**: Number
- **Required**: Yes
- **Default**: 10000
- **Description**: Daily API quota limit

### api_quota_used
- **Type**: Number
- **Required**: Yes
- **Default**: 0
- **Description**: Current API quota usage

### last_quota_reset
- **Type**: Date
- **Required**: Yes
- **Description**: Last time the quota was reset

### encryption_enabled
- **Type**: Bool
- **Required**: Yes
- **Default**: true
- **Description**: Whether encryption is enabled for stored documents

### backup_enabled
- **Type**: Bool
- **Required**: Yes
- **Default**: true
- **Description**: Whether backup is enabled

### environment
- **Type**: Text
- **Required**: Yes
- **Description**: Environment name (development, staging, production)

## Step 3: Create Development Record

1. Go to the **Records** tab of the `google_drive_config` collection
2. Click **+ New Record**
3. Fill in the following values:

```
service_account_email: <EMAIL>
root_folder_id: 1BQJ8K9X7Y2Z3A4B5C6D7E8F9G0H1I2J3
api_quota_limit: 10000
api_quota_used: 0
last_quota_reset: [Current date/time]
encryption_enabled: true
backup_enabled: true
environment: development
```

4. Click **Save**

## Step 4: Update Root Folder ID (Important!)

The `root_folder_id` value above is a placeholder. You need to:

1. Create a folder in Google Drive for document storage
2. Get the folder ID from the URL (the part after `/folders/`)
3. Update the record with the actual folder ID

## Step 5: Set Up Service Account Credentials

The app will look for Google service account credentials in this order:

1. **Environment variable**: `GOOGLE_SERVICE_ACCOUNT_CREDENTIALS`
2. **File**: `lib/pay-global-document-storage-9179f2284308.json`
3. **Assets**: `assets/credentials/service-account.json`

### Option 1: Environment Variable (Recommended for Production)
Set the `GOOGLE_SERVICE_ACCOUNT_CREDENTIALS` environment variable with the JSON content of your service account key.

### Option 2: File (Recommended for Development)
Place your service account JSON file at:
```
lib/pay-global-document-storage-9179f2284308.json
```

### Option 3: Assets (Alternative)
Place your service account JSON file at:
```
assets/credentials/service-account.json
```

## Step 6: Test the Configuration

After completing the setup:

1. Restart the app
2. Try uploading a document
3. Check the logs for successful Google Drive configuration loading

## Automatic Fallback

If you don't create the collection manually, the app will attempt to create a default configuration automatically when it first tries to access Google Drive. However, it's better to set it up manually to ensure proper configuration.

## Troubleshooting

### Collection Creation Fails
- Make sure you have admin access to PocketBase
- Check that all required fields are properly configured
- Verify field types match the specifications above

### Authentication Fails
- Verify your service account credentials are valid
- Check that the service account has the necessary Google Drive permissions
- Ensure the credentials file is in the correct location

### Folder Access Issues
- Make sure the root folder exists in Google Drive
- Verify the service account has access to the folder
- Check that the folder ID is correct (no extra characters or spaces)

## Next Steps

Once the configuration is set up, the Google Drive integration should work properly and you should be able to:

- Upload documents to Google Drive
- Download documents from Google Drive
- View storage status and metrics
- Access the storage settings page

The UI components created in Task 6 will now be able to connect to the actual Google Drive service.
