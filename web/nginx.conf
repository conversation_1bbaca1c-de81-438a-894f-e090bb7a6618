server {
    listen 80;
    server_name _;
    root /usr/share/nginx/html;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # CORS headers for audio and media files
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, HEAD" always;
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control" always;
    add_header Access-Control-Expose-Headers "Content-Length, Content-Range" always;

    # Content Security Policy for audio support
    add_header Content-Security-Policy "
        default-src 'self' data: blob: 'unsafe-inline' 'unsafe-eval';
        script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: data:;
        style-src 'self' 'unsafe-inline' data: blob:;
        img-src 'self' data: blob: https: http:;
        media-src 'self' data: blob: https: http: *.3payglobal.com *.railway.app;
        connect-src 'self' data: blob: https: http: ws: wss: *.3payglobal.com *.railway.app;
        font-src 'self' data: blob: https: http:;
        object-src 'none';
        base-uri 'self';
        form-action 'self';
    " always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        audio/mpeg
        audio/wav
        audio/ogg
        audio/mp4;

    # Handle preflight requests for CORS
    location / {
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, HEAD" always;
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control" always;
            add_header Access-Control-Max-Age 86400 always;
            add_header Content-Type "text/plain; charset=utf-8" always;
            add_header Content-Length 0 always;
            return 204;
        }

        # Flutter web routing
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
    }

    # Cache static assets with proper MIME types
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable" always;
        add_header Access-Control-Allow-Origin "*" always;
        
        # Ensure proper MIME types
        location ~* \.js$ {
            add_header Content-Type "application/javascript" always;
        }
        location ~* \.css$ {
            add_header Content-Type "text/css" always;
        }
    }

    # Audio file handling with proper MIME types and CORS
    location ~* \.(mp3|wav|ogg|m4a|aac|flac|mp4|webm)$ {
        expires 1d;
        add_header Cache-Control "public, max-age=86400" always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Range, Content-Range, Content-Length" always;
        add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges" always;
        add_header Accept-Ranges "bytes" always;

        # Proper MIME types for audio files
        location ~* \.mp3$ {
            add_header Content-Type "audio/mpeg" always;
        }
        location ~* \.wav$ {
            add_header Content-Type "audio/wav" always;
        }
        location ~* \.ogg$ {
            add_header Content-Type "audio/ogg" always;
        }
        location ~* \.m4a$ {
            add_header Content-Type "audio/mp4" always;
        }
        location ~* \.aac$ {
            add_header Content-Type "audio/aac" always;
        }
        location ~* \.flac$ {
            add_header Content-Type "audio/flac" always;
        }
        location ~* \.webm$ {
            add_header Content-Type "audio/webm" always;
        }
    }

    # Flutter service worker
    location /flutter_service_worker.js {
        expires off;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Content-Type "application/javascript" always;
    }

    # Manifest file
    location /manifest.json {
        expires 1d;
        add_header Cache-Control "public, max-age=86400" always;
        add_header Content-Type "application/manifest+json" always;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain always;
    }

    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /index.html;

    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log warn;
}
