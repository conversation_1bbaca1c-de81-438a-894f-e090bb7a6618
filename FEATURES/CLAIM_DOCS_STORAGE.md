# Claim Documents Storage with Google Drive

## Overview

This document outlines the implementation of Google Drive as the primary storage solution for 3Pay Global claim documents, providing enhanced scalability, collaboration features, and cost-effectiveness.

## 1. Document Storage Architecture

### Target Data Structure
The document storage system will use:

- **Collection**: `claim_documents` in PocketBase (metadata only)
- **File Storage**: Google Drive with organized folder structure
- **Data Structure**:
  ```json
  {
    "id": "record_id",
    "funding_application_id": "relation_id",
    "logical_name": "document_category",
    "current_version_file_id": "record_id_v1",
    "versions": [
      {
        "file_id": "record_id_v1",
        "filename": "document.pdf",
        "uploaded_at": "2024-01-01T00:00:00Z",
        "uploaded_by": "user_id",
        "notes": "Optional notes",
        "google_drive_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        "google_drive_url": "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view",
        "download_url": "https://drive.google.com/uc?id=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        "file_size": 1024000,
        "mime_type": "application/pdf",
        "checksum": "sha256_hash"
      }
    ],
    "google_drive_folder_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "storage_type": "google_drive",
    "name": "display_name",
    "uploaded_by": "user_id"
  }
  ```

### Core Functionality
- **Upload**: Direct upload to Google Drive with metadata tracking
- **Download**: Google Drive direct download URLs with caching
- **Versioning**: Enhanced version tracking with Google Drive metadata
- **Access Control**: Google Drive permissions integrated with 3Pay roles
- **File Management**: Organized folder structure with automatic organization

### Benefits
- **Scalability**: Unlimited storage with Google Drive
- **Collaboration**: Native Google Drive sharing and collaboration
- **Cost Efficiency**: Reduced storage costs compared to PocketBase
- **Backup**: Automatic backup and redundancy with Google Drive
- **Integration**: Rich API and third-party integrations

## 2. Google Drive Integration Architecture

### Service Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    3Pay Global App                          │
├─────────────────────────────────────────────────────────────┤
│  ClaimDocumentsService (Modified)                          │
│  ├── GoogleDriveService (New)                              │
│  └── DocumentCacheService (New)                            │
├─────────────────────────────────────────────────────────────┤
│                 PocketBase Backend                          │
│  ├── claim_documents (Modified Schema)                     │
│  ├── google_drive_config (New Collection)                  │
│  └── document_access_logs (New Collection)                 │
├─────────────────────────────────────────────────────────────┤
│                  Google Drive API                           │
│  ├── Drive API v3                                          │
│  ├── Service Account Authentication                        │
│  └── Folder Structure Management                           │
└─────────────────────────────────────────────────────────────┘
```

### Google Drive Folder Structure
```
3Pay Global Documents/
├── Claims/
│   ├── {funding_application_id}/
│   │   ├── {logical_name}/
│   │   │   ├── v1_{filename}
│   │   │   ├── v2_{filename}
│   │   │   └── metadata.json
│   │   └── ...
│   └── ...
├── Templates/
├── Archived/
└── Shared/
```

## 3. Implementation Strategy

### Phase 1: Foundation Setup (Week 1)
1. **Google Drive Setup**
   - Create Google Cloud Project
   - Enable Drive API
   - Create Service Account
   - Generate credentials
   - Set up folder structure

2. **Schema Updates**
   - Modify `claim_documents` collection for Google Drive metadata
   - Add new collections for Drive configuration and audit logging
   - Configure collections for Google Drive as primary storage

3. **Service Implementation**
   - Implement `GoogleDriveService`
   - Create document cache service
   - Add error handling and logging

### Phase 2: Core Integration (Week 2)
1. **Service Integration**
   - Integrate Google Drive with ClaimDocumentsService
   - Implement direct Google Drive uploads for new documents
   - Add caching layer for performance optimization

2. **Testing and Validation**
   - Test file upload/download functionality
   - Validate folder structure and permissions
   - Performance testing with Google Drive

### Phase 3: Production Deployment (Week 3)
1. **Production Deployment**
   - Deploy Google Drive integration to production
   - Configure production Google Drive settings
   - Monitor system performance and stability

2. **User Training and Documentation**
   - Update user documentation
   - Train users on any new features
   - Monitor user feedback and system usage

## 4. URL Storage Schema

### Modified claim_documents Collection
```json
{
  "id": "record_id",
  "funding_application_id": "relation_id",
  "logical_name": "document_category",
  "current_version_file_id": "record_id_v1",
  "versions": [
    {
      "file_id": "record_id_v1",
      "filename": "document.pdf",
      "uploaded_at": "2024-01-01T00:00:00Z",
      "uploaded_by": "user_id",
      "notes": "Optional notes",
      "google_drive_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
      "google_drive_url": "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view",
      "download_url": "https://drive.google.com/uc?id=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
      "file_size": 1024000,
      "mime_type": "application/pdf",
      "checksum": "sha256_hash"
    }
  ],
  "google_drive_folder_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
  "storage_type": "google_drive",
  "name": "display_name",
  "uploaded_by": "user_id"
}
```

### New Collections

#### google_drive_config
```json
{
  "id": "config_id",
  "service_account_email": "<EMAIL>",
  "root_folder_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
  "api_quota_limit": 1000,
  "api_quota_used": 150,
  "last_quota_reset": "2024-01-01T00:00:00Z",
  "encryption_enabled": true,
  "backup_enabled": true
}
```

#### document_access_logs
```json
{
  "id": "log_id",
  "document_id": "claim_document_id",
  "user_id": "user_id",
  "action": "download", // upload, download, view, share
  "timestamp": "2024-01-01T00:00:00Z",
  "ip_address": "***********",
  "user_agent": "Mozilla/5.0...",
  "success": true,
  "error_message": null
}
```

## 5. Service Implementation Plan

### GoogleDriveService Implementation
```dart
class GoogleDriveService {
  static const String _scope = 'https://www.googleapis.com/auth/drive';
  late final GoogleApi _driveApi;
  
  // Authentication and initialization
  Future<void> initialize() async { /* implementation */ }
  
  // File operations
  Future<String> uploadFile(File file, String folderId, Map<String, String> metadata) async { /* implementation */ }
  Future<void> downloadFile(String fileId, String localPath) async { /* implementation */ }
  Future<String> getFileUrl(String fileId) async { /* implementation */ }
  Future<void> deleteFile(String fileId) async { /* implementation */ }
  
  // Folder operations
  Future<String> createFolder(String name, String parentId) async { /* implementation */ }
  Future<List<DriveFile>> listFiles(String folderId) async { /* implementation */ }
  
  // Permission management
  Future<void> setFilePermissions(String fileId, List<Permission> permissions) async { /* implementation */ }
  Future<void> shareFile(String fileId, String email, String role) async { /* implementation */ }
  
  // Utility methods
  Future<bool> fileExists(String fileId) async { /* implementation */ }
  Future<String> getFileChecksum(String fileId) async { /* implementation */ }
}
```

### Modified ClaimDocumentsService
```dart
class ClaimDocumentsService {
  final PocketBase _pb;
  final GoogleDriveService _driveService;
  final DocumentCacheService _cacheService;

  // Modified methods to use Google Drive as primary storage
  Future<String> getFileUrl(String versionFileId) async {
    // Check cache first
    final cachedUrl = await _cacheService.getCachedUrl(versionFileId);
    if (cachedUrl != null) return cachedUrl;

    // Get document version from database
    final version = await _getDocumentVersion(versionFileId);

    // Use Google Drive URL
    final url = await _driveService.getFileUrl(version.googleDriveId);
    await _cacheService.cacheUrl(versionFileId, url);
    return url;
  }

  Future<String> uploadFileAndCreateCategory({
    required String fundingApplicationId,
    required String logicalName,
    required File file,
    required String uploadedBy,
    String? comment,
  }) async {
    // Create folder structure in Google Drive
    final folderId = await _ensureFolderStructure(fundingApplicationId, logicalName);

    // Upload to Google Drive
    final driveFileId = await _driveService.uploadFile(file, folderId, {
      'funding_application_id': fundingApplicationId,
      'logical_name': logicalName,
      'uploaded_by': uploadedBy,
    });

    // Create database record with Google Drive references
    return _createDatabaseRecord(fundingApplicationId, logicalName, driveFileId, uploadedBy, comment);
  }
}
```

## 6. Security and Access Control

### Authentication Strategy
- **Service Account**: Use Google Service Account for server-side operations
- **OAuth 2.0**: For user-specific operations (future enhancement)
- **API Keys**: Secure storage in environment variables
- **Token Management**: Automatic token refresh and caching

### Permission Model
```dart
class DrivePermissionManager {
  // Role-based access control
  static const Map<String, String> roleMapping = {
    'solicitor': 'writer',
    'claimant': 'reader',
    'co_funder': 'reader',
    'admin': 'owner',
  };
  
  Future<void> setDocumentPermissions(String fileId, String userId, String userRole) async {
    final driveRole = roleMapping[userRole] ?? 'reader';
    await _driveService.setFilePermissions(fileId, [
      Permission(
        type: 'user',
        role: driveRole,
        emailAddress: await _getUserEmail(userId),
      ),
    ]);
  }
}
```

### Data Encryption
- **In-Transit**: HTTPS for all API communications
- **At-Rest**: Google Drive's native encryption
- **Client-Side**: Optional file encryption before upload
- **Key Management**: Secure key storage and rotation

## 7. Error Handling and Fallback

### Error Scenarios
1. **Google Drive API Failures**
   - Rate limiting
   - Service unavailability
   - Authentication failures
   - Network connectivity issues

2. **File Operation Failures**
   - Upload failures
   - Download timeouts
   - Corruption detection
   - Permission errors

### Error Handling Strategy
```dart
class DocumentStorageService {
  Future<String> getFileUrl(String versionFileId) async {
    try {
      // Use Google Drive as primary storage
      return await _googleDriveService.getFileUrl(versionFileId);
    } catch (e) {
      LoggerService.error('Google Drive access failed', e);

      // Implement retry mechanism with exponential backoff
      return await _retryWithBackoff(() =>
        _googleDriveService.getFileUrl(versionFileId)
      );
    }
  }

  Future<T> _retryWithBackoff<T>(Future<T> Function() operation) async {
    int attempts = 0;
    const maxAttempts = 3;

    while (attempts < maxAttempts) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        if (attempts >= maxAttempts) {
          throw DocumentAccessException('Document temporarily unavailable after $maxAttempts attempts');
        }
        await Future.delayed(Duration(seconds: pow(2, attempts).toInt()));
      }
    }
    throw DocumentAccessException('Document temporarily unavailable');
  }
}
```

### Retry Mechanisms
- **Exponential Backoff**: For API rate limiting
- **Circuit Breaker**: Prevent cascade failures
- **Health Checks**: Monitor service availability
- **Graceful Degradation**: Reduced functionality during outages

## 8. Performance Considerations

### Caching Strategy
```dart
class DocumentCacheService {
  static const Duration _urlCacheDuration = Duration(hours: 1);
  static const Duration _metadataCacheDuration = Duration(minutes: 30);

  // URL caching for frequently accessed documents
  Future<String?> getCachedUrl(String versionFileId) async {
    final cached = await _cache.get('url_$versionFileId');
    if (cached != null && !_isExpired(cached.timestamp)) {
      return cached.url;
    }
    return null;
  }

  // Metadata caching for document listings
  Future<void> cacheDocumentMetadata(String documentId, DocumentMetadata metadata) async {
    await _cache.set('meta_$documentId', metadata, _metadataCacheDuration);
  }

  // Preload frequently accessed documents
  Future<void> preloadDocuments(List<String> documentIds) async {
    for (final id in documentIds) {
      _backgroundPreload(id);
    }
  }
}
```

### Upload Optimization
- **Chunked Upload**: For large files (>5MB)
- **Parallel Processing**: Multiple file uploads
- **Progress Tracking**: Real-time upload progress
- **Resume Capability**: Resume interrupted uploads

### Download Optimization
- **Direct Links**: Use Google Drive direct download URLs
- **CDN Integration**: Leverage Google's global CDN
- **Compression**: Automatic compression for supported formats
- **Streaming**: Stream large files instead of full download

### API Quota Management
```dart
class DriveQuotaManager {
  static const int _dailyQuotaLimit = 1000000;
  static const int _perUserQuotaLimit = 1000;

  Future<bool> checkQuotaAvailable(String operation) async {
    final currentUsage = await _getCurrentQuotaUsage();
    final operationCost = _getOperationCost(operation);

    return (currentUsage + operationCost) <= _dailyQuotaLimit;
  }

  Future<void> trackQuotaUsage(String operation) async {
    final cost = _getOperationCost(operation);
    await _incrementQuotaUsage(cost);
  }
}
```

## 9. Integration with Existing Features

### Document Versioning
- **Maintain Version History**: Preserve existing version tracking
- **Version Comparison**: Enhanced diff capabilities via Drive API
- **Rollback Support**: Easy version rollback functionality
- **Branch Management**: Support for document branches (future)

### Audit Logging Enhancement
```dart
class EnhancedAuditLogger {
  Future<void> logDocumentAccess({
    required String documentId,
    required String userId,
    required String action,
    Map<String, dynamic>? metadata,
  }) async {
    await _pocketBaseService.createRecord(
      collectionName: 'document_access_logs',
      data: {
        'document_id': documentId,
        'user_id': userId,
        'action': action,
        'timestamp': DateTime.now().toIso8601String(),
        'metadata': metadata,
        'storage_type': 'google_drive',
        'drive_file_id': metadata?['drive_file_id'],
      },
    );
  }
}
```

### User Permissions Integration
- **Role-Based Access**: Map 3Pay roles to Drive permissions
- **Dynamic Permissions**: Update permissions based on claim status
- **Sharing Controls**: Controlled sharing with external parties
- **Access Revocation**: Automatic access removal on role changes

## 10. Testing Strategy

### Unit Testing
```dart
// Test Google Drive service operations
class GoogleDriveServiceTest {
  void testFileUpload() async {
    final service = GoogleDriveService();
    final file = File('test_document.pdf');

    final fileId = await service.uploadFile(file, 'test_folder_id', {});
    expect(fileId, isNotEmpty);

    final exists = await service.fileExists(fileId);
    expect(exists, isTrue);
  }

  void testPermissionManagement() async {
    final service = GoogleDriveService();
    final fileId = 'test_file_id';

    await service.setFilePermissions(fileId, [
      Permission(type: 'user', role: 'reader', emailAddress: '<EMAIL>'),
    ]);

    // Verify permissions were set correctly
    final permissions = await service.getFilePermissions(fileId);
    expect(permissions.length, equals(1));
  }
}
```

### Integration Testing
- **End-to-End Workflows**: Complete upload/download cycles
- **Google Drive Integration**: Verify Google Drive API integration
- **Performance Testing**: Load testing with concurrent operations
- **Failure Scenarios**: Test error handling and recovery

### Performance Testing
- **Load Testing**: Simulate high concurrent usage
- **Stress Testing**: Test system limits
- **Endurance Testing**: Long-running operations
- **Scalability Testing**: Growth scenario testing

## 11. Implementation Timeline

### Week 1: Foundation
- [ ] Google Cloud Project setup
- [ ] Drive API configuration
- [ ] Service account creation
- [ ] Basic GoogleDriveService implementation
- [ ] Schema design and PocketBase updates

### Week 2: Core Development
- [ ] Complete GoogleDriveService implementation
- [ ] Modify ClaimDocumentsService for Google Drive integration
- [ ] Implement caching layer
- [ ] Unit test development
- [ ] Error handling implementation

### Week 3: Production Deployment
- [ ] Integrate with existing UI components
- [ ] Update file upload/download flows for Google Drive
- [ ] Performance optimization
- [ ] Integration testing
- [ ] Deploy to production
- [ ] Monitor system performance
- [ ] User acceptance testing
- [ ] Documentation updates

## 12. Monitoring and Maintenance

### Monitoring Metrics
- **API Usage**: Track Google Drive API quota consumption
- **Performance**: Monitor upload/download speeds
- **Error Rates**: Track failure rates and types
- **Storage Usage**: Monitor Drive storage consumption
- **User Activity**: Track document access patterns

### Maintenance Tasks
- **Quota Monitoring**: Daily quota usage review
- **Performance Optimization**: Regular performance tuning
- **Security Audits**: Periodic security reviews
- **Backup Verification**: Regular backup integrity checks
- **Cost Optimization**: Monthly cost analysis and optimization

### Alerting System
```dart
class DriveMonitoringService {
  Future<void> checkSystemHealth() async {
    // Check API quota usage
    final quotaUsage = await _getQuotaUsage();
    if (quotaUsage > 0.8) {
      await _sendAlert('High API quota usage: ${quotaUsage * 100}%');
    }

    // Check error rates
    final errorRate = await _getErrorRate();
    if (errorRate > 0.05) {
      await _sendAlert('High error rate: ${errorRate * 100}%');
    }

    // Check storage usage
    final storageUsage = await _getStorageUsage();
    if (storageUsage > 0.9) {
      await _sendAlert('High storage usage: ${storageUsage * 100}%');
    }
  }
}
```

This comprehensive solution provides a robust Google Drive integration for 3Pay Global document storage, offering enhanced scalability, collaboration features, and cost-effectiveness as the primary storage solution from the start.
