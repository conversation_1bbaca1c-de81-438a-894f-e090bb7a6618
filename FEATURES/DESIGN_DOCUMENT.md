# Application Design: 3Pay Group Litigation Funding Platform

**1. Introduction**

This document outlines the application design for the 3Pay Group Litigation Funding Platform, based on the requirements specified in the [`PRD.md`](PRD.md). The platform aims to be a comprehensive digital solution for litigation funding in England and Wales, serving Solicitors, Prospective Co-Funders (PCFs), and Claimants. The technology stack will consist of Flutter with shadcn_ui for the frontend (mobile and web) and Pocketbase as the Backend-as-a-Service (BaaS).

**2. High-Level Architecture**

The platform will adopt a client-server architecture with Pocketbase serving as the central backend.

```mermaid
graph TD
    subgraph Frontend Clients
        A[Mobile App (Flutter + shadcn_ui)]
        B[Web Platform (Flutter + shadcn_ui)]
    end

    subgraph Backend
        C[Pocketbase BaaS]
        C --- D[Database]
        C --- E[Authentication]
        C --- F[File Storage]
        C --- G[Real-time Subscriptions]
        C --- H[Pocketbase SuperAdmin UI]  // For superuser tasks
    end
    // Custom Admin Portal is part of the Flutter App (A, B)

    subgraph External Services
        I[Payment Gateway (Stripe)]
        J[Email Service]
        K[SMS Gateway]
        M[AML/KYC Service] // Document Verification deferred
        N[Analytics (Google Analytics, Posthog)]
        P[Cloud Storage (S3 - for scale/backup)]
    end

    A --> C
    B --> C
    C --> I
    C --> J
    C --> K
    C --> M
    C --> N
    C --> P

    style A fill:#ccf,stroke:#333,stroke-width:2px
    style B fill:#ccf,stroke:#333,stroke-width:2px
    style C fill:#f9f,stroke:#333,stroke-width:2px
    style External Services fill:#cfc,stroke:#333,stroke-width:2px
```

**3. User Roles and Personas**

As defined in [`PRD.md:4-12`](PRD.md:4-12):

*   **Solicitor (User Type 1):** Legal professionals seeking funding. Must achieve Permitted User (PU) status.
    *   *Goals:* Register firm, apply for PU status, submit funding applications, manage cases, communicate, add additional users from their firm.
*   **Prospective Co-Funder (PCF) / Co-Funder (User Type 2):** Individuals interested in funding cases, progressing through a level-based system (Level 0 to Level 4).
    *   *Goals (vary by level):* Access educational content, register, provide KYC/AML details, pass knowledge tests, review cases, commit funds, manage investments, track returns.
*   **Claimant (User Type 3):** Individuals/entities whose cases are seeking/receiving funding.
    *   *Goals:* View case/funding status (if permissioned by their solicitor), access relevant documents (permissioned), access educational content (including offline on mobile), potentially communicate with their solicitor.
*   **Platform Administrator:** Manages specific aspects of the platform via a custom Admin Portal within the Flutter application (e.g., user oversight, basic content management workflows, viewing platform analytics, managing user roles). Superuser functions and core data schema management remain with Pocketbase SuperAdmin UI.

**4. Data Model (Pocketbase Collections)**

The following Pocketbase collections are proposed:

*   **`users`** (Extends Pocketbase default `users` collection)
    *   `email` (text, unique, required)
    *   `password` (Pocketbase managed)
    *   `role` (select: 'solicitor', 'co_funder', 'claimant', 'admin', required)
    *   `full_name` (text, for co-funders, claimants)
    *   `last_login_at` (date)
    *   `activity_log_ids` (relation, multiple to `activity_logs`)
    *   `otp_enabled` (bool, default: false)
    *   `profile_id` (relation, single to respective profile collection e.g. `solicitor_profiles`)

*   **`solicitor_profiles`** ([`PRD.md:5`](PRD.md:5))
    *   `user_id` (relation, single to `users`, unique, required)
    *   `law_firm_name` (text, required)
    *   `solicitor_name` (text, required)
    *   `position` (text, required)
    *   `contact_number` (text, required)
    *   `firm_address` (text, required)
    *   `firm_registration_number` (text, required)
    *   `pu_status` (select: 'none', 'pending', 'approved', 'rejected', default: 'none')
    *   `pu_application_date` (date)
    *   `pu_decision_date` (date)
    *   `verification_documents_uploads` (file, multiple, e.g., Legal Practitioner's Certificate - [`PRD.md:48`](PRD.md:48)) # Manual verification assumed
    *   `additional_users` (relation, multiple to `users` - for firm members)

*   **`co_funder_profiles`** ([`PRD.md:6`](PRD.md:6))
    *   `user_id` (relation, single to `users`, unique, required)
    *   `current_level` (number, 0-4, default: 0)
    *   `residential_address` (text)
    *   `age` (number)
    *   `date_of_birth` (date)
    *   `occupation` (text)
    *   `mobile_number` (text)
    *   `nationality` (text)
    *   `country_of_residence` (text)
    *   `net_worth` (text)
    *   `assets_portfolio_summary` (text)
    *   `available_surplus_cash` (text)
    *   `risk_appetite` (select: 'low', 'medium', 'high')
    *   `identity_document` (file, e.g., passport/license copy) # Manual verification assumed
    *   `proof_of_residence` (file) # Manual verification assumed
    *   `bank_account_details_encrypted` (text)
    *   `nda_signed_at` (date)
    *   `aml_kyc_status` (select: 'pending', 'approved', 'rejected', default: 'pending' for Level 3+) # External service integration
    *   `aml_kyc_check_date` (date)
    *   `knowledge_test_ids` (relation, multiple to `knowledge_test_attempts`)
    *   `non_recourse_facility_agreements` (file, multiple)

*   **`claimant_profiles`** ([`PRD.md:7-12`](PRD.md:7-12))
    *   `user_id` (relation, single to `users`, unique, required)
    *   `associated_solicitor_id` (relation, single to `solicitor_profiles`)
    *   `associated_case_id` (relation, single to `cases`)
    *   `permissions_config_by_solicitor` (json, e.g., `{"can_view_status": true, "can_view_docs": ["doc_id1"]}`)

*   **`funding_applications`** ([`PRD.md:51-61`](PRD.md:51-61))
    *   `solicitor_profile_id` (relation, single to `solicitor_profiles`, required)
    *   `claim_title` (text, required)
    *   `minimum_value_claim` (number, required, e.g., 5000000)
    *   `required_funding_amount` (number, required, e.g., max 3000000)
    *   `schedule_of_costs_doc_template_id` (relation, single to `document_templates`, if applicable for download)
    *   `schedule_of_costs_doc_uploaded` (file)
    *   `conditional_fee_agreement_confirmed` (bool, required)
    *   `risk_committee_approval_doc_template_id` (relation, single to `document_templates`, if applicable for download)
    *   `risk_committee_approval_doc_uploaded` (file)
    *   `legal_opinion_doc_template_id` (relation, single to `document_templates`, if applicable for download)
    *   `legal_opinion_doc_uploaded` (file)
    *   `legal_opinion_success_prospects` (number, required, e.g., 55 for 55%)
    *   `application_status` (select: 'draft', 'submitted', 'under_review', 'requires_info', 'approved', 'rejected', 'funded', default: 'draft')
    *   `submission_date` (date)
    *   `review_notes` (text, internal)
    *   `decision_date` (date)
    *   `four_pillars_status` (json, e.g. `{"claimant_solicitor_ok": true, "leading_counsel_ok": false, ...}`)
    *   `uploaded_documents` (json_array: `[{"name": "Schedule of Costs", "file_id": "...", "uploaded_at": "..."}]`)

*   **`document_templates`** (New collection to support downloadable, dynamically filled documents)
    *   `name` (text, required, e.g., "Schedule of Costs Template")
    *   `template_file` (file, required, e.g., a .docx or .pdf template)
    *   `description` (text)
    *   `dynamic_fields` (json_array: `["claim_title", "solicitor_name"]`, fields to be pre-filled)
    *   `version` (number, default: 1)

*   **`cases`** (Represents funded claims - [`PRD.md:62-69`](PRD.md:62-69))
    *   `funding_application_id` (relation, single to `funding_applications`, unique, required)
    *   `case_title` (text, derived from application)
    *   `case_summary_public` (text, redacted for dashboard)
    *   `solicitor_details_public` (text, for dashboard)
    *   `barrister_details_public` (text, for dashboard)
    *   `current_status` (text, e.g., 'Pre-Action', 'Discovery', 'Trial Prep')
    *   `status_updates` (json_array: `[{"date": "...", "update": "...", "by": "user_id"}]`)
    *   `document_repository` (json_array: `[{"name": "...", "file_id": "...", "uploaded_at": "...", "access_roles": ["role"]}]`)
    *   `cost_tracking_info` (json)
    *   `associated_co_funder_ids` (relation, multiple to `co_funder_profiles`)
    *   `total_funding_secured` (number)
    *   `expected_frfr_schedule` (json, e.g. `{"Pre-Action": 70, "Trial Prep": 10}`)

*   **`content_items`** ([`PRD.md:31-40`](PRD.md:31-40))
    *   `type` (select: 'blog', 'podcast', 'newsletter', 'educational_module', 'resource', required)
    *   `title` (text, required)
    *   `slug` (text, unique, required)
    *   `summary` (text)
    *   `body_content` (editor / markdown)
    *   `media_url` (url, for podcasts, videos)
    *   `thumbnail_image` (file)
    *   `author_id` (relation, single to `users` - admin/editor)
    *   `published_at` (date)
    *   `status` (select: 'draft', 'published', 'archived', default: 'draft')
    *   `target_user_levels` (json_array: `[0, 1, 2, 3, 4]`, for co-funder content)
    *   `tags` (json_array: `["tag1", "tag2"]`)
    *   `analytics_views` (number, default: 0)

*   **`user_activity_logs`** ([`PRD.md:23`](PRD.md:23), [`PRD.md:28`](PRD.md:28))
    *   `user_id` (relation, single to `users`, required)
    *   `timestamp` (date, required, default: now)
    *   `action` (text, required, e.g., 'login', 'view_content_item', 'submit_application')
    *   `details` (json, optional)
    *   `ip_address` (text)

*   **`knowledge_tests`** ([`PRD.md:97`](PRD.md:97), [`PRD.md:113`](PRD.md:113))
    *   `title` (text, required)
    *   `description` (text)
    *   `target_level` (number, e.g., 3 for test before Level 4)
    *   `questions` (json_array: `[{"question_text": "...", "options": ["..."], "correct_answer_index": 0}]`)
    *   `passing_score_percentage` (number, e.g., 75)

*   **`knowledge_test_attempts`**
    *   `co_funder_profile_id` (relation, single to `co_funder_profiles`, required)
    *   `knowledge_test_id` (relation, single to `knowledge_tests`, required)
    *   `attempted_at` (date, required, default: now)
    *   `answers` (json_array: `[{"question_index": 0, "selected_answer_index": 1}]`)
    *   `score` (number)
    *   `passed` (bool)
    *   `certificate_generated` (bool, default: false)
    *   `certificate_file` (file, if applicable - [`PRD.md:114`](PRD.md:114))

*   **`funding_commitments`** ([`PRD.md:98-109`](PRD.md:98-109), [`PRD.md:119-127`](PRD.md:119-127))
    *   `co_funder_profile_id` (relation, single to `co_funder_profiles`, required)
    *   `case_id` (relation, single to `cases`, required)
    *   `funding_type` (select: 'discretionary', 'non_discretionary', required)
    *   `amount_committed` (number, required)
    *   `commitment_date` (date, required, default: now)
    *   `lenders_application_form_agreed` (bool, required)
    *   `declaration_of_continued_interest_form_agreed` (bool, required)
    *   `non_recourse_form_agreed` (bool, required)
    *   `status` (select: 'pending_approval', 'approved', 'rejected', 'active', 'completed')
    *   `approval_date` (date)
    *   `rejection_reason` (text, if rejected)
    *   `funds_returned_date` (date, if rejected)
    *   `frfr_earned` (number, calculated)

*   **`notifications`** ([`PRD.md:50`](PRD.md:50), [`PRD.md:55`](PRD.md:55), [`PRD.md:135`](PRD.md:135))
    *   `user_id` (relation, single to `users`, required)
    *   `message` (text, required)
    *   `type` (select: 'info', 'alert', 'action_required')
    *   `related_item_collection` (text, e.g., 'funding_applications')
    *   `related_item_id` (text)
    *   `created_at` (date, default: now)
    *   `read_at` (date, nullable)

*   **`consultation_bookings`** ([`PRD.md:92`](PRD.md:92), [`PRD.md:116`](PRD.md:116))
    *   `co_funder_profile_id` (relation, single to `co_funder_profiles`, required)
    *   `expert_name` (text, or relation to an `experts` collection if managed)
    *   `booking_time` (date, required)
    *   `duration_minutes` (number, default: 60)
    *   `status` (select: 'requested', 'confirmed', 'completed', 'cancelled')
    *   `notes` (text)

**5. Core Modules Design**

*   **5.1 Authentication & Security** ([`PRD.md:18-23`](PRD.md:18-23))
    *   **Pocketbase Auth:** Leverage Pocketbase for email/password, OTP ([`PRD.md:20`](PRD.md:20)).
    *   **Biometric Auth (Mobile):** Flutter plugins for fingerprint/face ID, storing Pocketbase session tokens securely.
    *   **Session Management:** Pocketbase handles web sessions; secure token storage on mobile.
    *   **Role-Based Access Control (RBAC):** Implemented via Pocketbase collection rules and application-level checks based on `users.role` and `co_funder_profiles.current_level`.
    *   **Activity Logging:** Store actions in `user_activity_logs`.

*   **5.2 User Management** ([`PRD.md:24-30`](PRD.md:24-30))
    *   **Registration:** Separate flows for Solicitors and Co-Funders. Claimants might be invited or linked by Solicitors.
    *   **Profile Creation/Editing:** Forms in Flutter app, data saved to respective profile collections.
    *   **Role Assignment:** Set during registration, potentially modifiable by Admins.
    *   **Solicitor's Additional Users:** A Solicitor (PU) can invite/manage other users from their firm, linking them to the `solicitor_profiles`.

*   **5.3 Content Management System (CMS)** ([`PRD.md:31-40`](PRD.md:31-40))
    *   **Admin Interface:**
        *   **Custom Admin Portal (Flutter):** Provides tools for platform administrators to oversee content, manage publishing workflows (e.g., approving content submitted by others if applicable), view content analytics, and manage content categories/tags.
        *   **Pocketbase SuperAdmin UI:** Used by superadmins for direct content creation/modification, especially for educational modules and resources, and managing the `content_items` schema.
    *   **Frontend Display:** Flutter app fetches and displays content based on user role/level. Educational content can be marked/downloaded for offline viewing on mobile.
    *   **Newsletter:** For Level 1+ Co-Funders. Integration with an email service (e.g., SendGrid, Mailchimp via Pocketbase hooks or a small serverless function).
    *   **Search:** Pocketbase filtering for basic search; consider Algolia or Typesense integration for advanced search if Pocketbase's capabilities are insufficient. Simple level tags on content items will be used for targeting.
    *   **Analytics:** Track views on `content_items`, integrate with GA/Posthog.

**6. User Portal Designs**

*   **6.1 Solicitor Portal** ([`PRD.md:43-69`](PRD.md:43-69))
    *   Dashboard: Overview of applications, cases, notifications.
    *   Registration & PU Status: Forms for firm info, document uploads ([`PRD.md:48`](PRD.md:48)), status tracking.
    *   Funding Application:
        *   Multi-step form for application details.
        *   **Document Provision:** The platform will provide relevant document templates (e.g., Schedule of Costs, Legal Opinion) for download. Some sections of these documents may be dynamically pre-filled with known information (e.g., solicitor name, claim title).
        *   Solicitors will then complete these documents offline and upload them.
        *   Fields in `funding_applications` collection updated to reflect template usage and separate upload fields (e.g., `schedule_of_costs_doc_template_id`, `schedule_of_costs_doc_uploaded`).
        *   A new `document_templates` collection is introduced to manage these templates.
        *   Draft saving functionality.
        *   Submission and progress tracking.
    *   Case Management:
        *   View funded cases, track status, document repository, communication tools.
        *   **Claimant Assignment:** Solicitors can search for existing users by email and assign them as Claimants to specific cases they manage. This will link the `claimant_profiles` to the `cases`. Permissions for what the claimant can see will be managed by the solicitor via the `claimant_profiles.permissions_config_by_solicitor` field.

*   **6.2 Co-Funder Portal** ([`PRD.md:70-127`](PRD.md:70-127))
    *   **Level-Based Access:** Content and features unlocked as users progress.
        *   **Level 0 (Public):** Basic content, registration prompts.
        *   **Level 1 (Curious):** Name/email signup. Enhanced content, newsletter.
        *   **Level 2 (Interested):** More personal info. Advanced content (funding model).
        *   **Level 3 (Desire/Associates):** NDA, AML/KYC, full personal/financial info. Full content, case review, consultation booking.
        *   **Level 4 (Action/Co-Funders):** Pass knowledge test. Investment capabilities, fund management.
    *   Educational Features: Interactive modules, progress tracking, knowledge tests, certificates, resource library.
    *   Investment Management: View cases, commit funds, track portfolio/returns (FRFR), manage documents.

*   **6.3 Claimant Portal** ([`PRD.md:128-135`](PRD.md:128-135))
    *   Simplified dashboard.
    *   View Case Status (permissioned by solicitor).
    *   View Funding Status (permissioned by solicitor).
    *   Limited Document Access (permissioned by solicitor).
    *   Access to general educational content (including viewing downloaded educational content offline on mobile).
    *   Notifications.

**7. Key User Flows (Illustrative)**

*   **Co-Funder Level Progression (Simplified)**
    ```mermaid
    graph TD
        Start((Start: Public User)) --> L0{Level 0: Access Basic Content};
        L0 -- Sign up (Name, Email) --> L1{Level 1: Curious};
        L1 -- Provide More Info --> L2{Level 2: Interested};
        L2 -- Sign NDA, AML/KYC, Full Profile --> L3{Level 3: Desire/Associate};
        L3 -- Pass Knowledge Test, Ready to Commit --> L4{Level 4: Action/Co-Funder};
        L4 -- Invest in Cases --> End((Funding Cases));

        style Start fill:#ddd,stroke:#333
        style End fill:#ddd,stroke:#333
    ```

*   **Funding Application Submission & Initial Review (Adjusted)**
    ```mermaid
    graph TD
        Solicitor[Solicitor (PU)] -- Starts Application --> AppDetails{Application: Enter Details};
        AppDetails -- Requests Docs --> DownloadDocs{Platform: Provides Document Templates (Dynamically Filled)};
        Solicitor -- Completes & Uploads Docs --> DocsUploaded{Application: Documents Uploaded};
        DocsUploaded -- Submits Application --> SubmittedApp{Application: Submitted};
        SubmittedApp --> PlatformReview{Platform: Initial Review};
        PlatformReview -- Valid --> DueDiligence{Underwriting/Due Diligence (28+ days, 'Four Pillars' status tracked)};
        PlatformReview -- Invalid/More Info --> RequiresInfo{Application: Requires Info};
        RequiresInfo -- Solicitor Updates --> SubmittedApp;
        DueDiligence -- Approved --> ApprovedApp{Application: Approved};
        DueDiligence -- Rejected --> RejectedApp{Application: Rejected};
        ApprovedApp --> CaseCreation{Case Created & Published (Redacted)};

        style Solicitor fill:#ccf,stroke:#333
    ```

**8. Integration Strategy** ([`PRD.md:159-168`](PRD.md:159-168))

*   **Payment (Stripe):** For potential platform fees or co-funder commitments. Integrate via Pocketbase hooks or dedicated microservice if complex logic is needed.
*   **Email/SMS:** For notifications, newsletters. Use services like SendGrid/Twilio, triggered by Pocketbase hooks.
*   **AML/KYC:** Integrate with a specialized service (e.g., ComplyAdvantage, Jumio) for Level 3+ Co-Funders. (Note: Automated document verification services are deferred for now; verification of solicitor credentials and co-funder IDs will be a manual process initially).
*   **Analytics (GA, Posthog):** Frontend SDKs for event tracking. Key metrics may also be displayed within the custom Admin Portal.
*   **Cloud Storage (S3):** Pocketbase handles primary storage. S3 for backups or large-scale file needs, potentially via Pocketbase's S3 storage backend option.
*   **(No External CRM Integration):** User data overview and basic management functionalities will be part of the custom Admin Portal within the Flutter application.

**9. Security and Compliance Strategy** ([`PRD.md:169-189`](PRD.md:169-189))

*   **Data Encryption:** Leverage Pocketbase's HTTPS and at-rest encryption. Implement field-level encryption in Flutter for highly sensitive data before sending to Pocketbase if necessary (e.g., bank details).
*   **RBAC:** Strictly enforce using Pocketbase collection rules.
*   **Audits & Pen Testing:** Plan for regular external security assessments.
*   **GDPR:** Implement data minimization, user consent, right to access/erasure. Clear privacy policy.
*   **FCA Adherence:** Ensure platform content and operations stay within unregulated lending boundaries.
*   **AML/KYC:** Robust integration with specialized services.