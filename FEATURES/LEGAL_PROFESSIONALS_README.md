# Barristers and Experts Collections for 3Pay Platform

This document contains the schema definitions and instructions for creating the barristers and experts collections in PocketBase for the 3Pay litigation funding platform.

## Collection Schemas

### Barristers Collection
- **barrister_with_conduct**: Name of the barrister (text, required)
- **barrister_chambers**: Chambers the barrister belongs to (text, required)
- **email**: <PERSON><PERSON>'s email address (email, required)
- **claims**: Relation to funding_applications collection (many-to-many)

### Experts Collection
- **name**: Name of the expert witness (text, required)
- **firm_name**: Firm or organization the expert belongs to (text, required)
- **email**: <PERSON><PERSON>'s email address (email, required)
- **type**: Type of expert (select: legal, financial, engineer, construction, other)
- **claims**: Relation to funding_applications collection (many-to-many)

## Manual Setup Instructions

### Option 1: Using the PocketBase Admin UI

1. **Log in to PocketBase Admin UI**

2. **Create Barristers Collection**
   - Go to Collections > New Collection
   - Name: `barristers`
   - Type: Base Collection
   - Add the following fields:
     - barrister_with_conduct (Text, Required)
     - barrister_chambers (Text, Required)
     - email (Email, Required)
     - claims (Relation, Collection: funding_applications)
   - Set access rules:
     - List: `@request.auth.id != "" && (@request.auth.user_type = "solicitor" || @request.auth.user_type = "admin")`
     - View: `@request.auth.id != "" && (@request.auth.user_type = "solicitor" || @request.auth.user_type = "admin")`
     - Create: `@request.auth.id != "" && (@request.auth.user_type = "solicitor" || @request.auth.user_type = "admin")`
     - Update: `@request.auth.id != "" && (@request.auth.user_type = "solicitor" || @request.auth.user_type = "admin")`
     - Delete: `@request.auth.user_type = "admin"`

3. **Create Experts Collection**
   - Go to Collections > New Collection
   - Name: `experts`
   - Type: Base Collection
   - Add the following fields:
     - name (Text, Required)
     - firm_name (Text, Required)
     - email (Email, Required)
     - type (Select, Required, Options: legal, financial, engineer, construction, other)
     - claims (Relation, Collection: funding_applications)
   - Set access rules:
     - List: `@request.auth.id != "" && (@request.auth.user_type = "solicitor" || @request.auth.user_type = "admin")`
     - View: `@request.auth.id != "" && (@request.auth.user_type = "solicitor" || @request.auth.user_type = "admin")`
     - Create: `@request.auth.id != "" && (@request.auth.user_type = "solicitor" || @request.auth.user_type = "admin")`
     - Update: `@request.auth.id != "" && (@request.auth.user_type = "solicitor" || @request.auth.user_type = "admin")`
     - Delete: `@request.auth.user_type = "admin"`

4. **Update Funding Applications Collection**
   - Go to Collections > funding_applications
   - Add two new relation fields:
     - barristers (Relation, Collection: barristers)
     - experts (Relation, Collection: experts)

### Option 2: Using the API

1. **Install Dependencies**
   ```bash
   npm install axios
   ```

2. **Update Admin Credentials**
   - Open `create_collections.js`
   - Update `ADMIN_EMAIL` and `ADMIN_PASSWORD` with your PocketBase admin credentials
   - Update `PB_URL` if your PocketBase instance is not running on the default URL

3. **Run the Script**
   ```bash
   node create_collections.js
   ```

## Integration with Solicitor Interface

The barristers and experts collections are designed to be used in the solicitor interface for adding legal professionals to claims. The interface should:

1. Allow solicitors to search existing barristers and expert witnesses
2. Provide the ability to add new barristers and expert witnesses on the fly
3. Associate selected professionals with specific claims
4. Display the associated professionals in the claim details view

## Access Control

- Solicitors can view, create, and update barristers and expert witnesses
- Only admins can delete barristers and expert witnesses
- The collections maintain many-to-many relationships with the funding_applications collection
