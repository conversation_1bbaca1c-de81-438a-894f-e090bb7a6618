TASK 09: <PERSON><PERSON><PERSON><PERSON> MANAGEMENT
===========================

DESCRIPTION:
Create profile management functionality that allows claimants to view and update their personal information and preferences.

OBJECTIVES:
- Build profile viewing and editing interface
- Implement profile data validation
- Add profile picture upload capability
- Create preferences management

DELIVERABLES:

1. PROFILE VIEW PAGE
   - Create lib/src/features/claimant_portal/presentation/pages/profile_view_page.dart
   - Display current profile information
   - Show profile picture or avatar
   - Include edit button and navigation
   - Add sections: Personal Info, Contact Details, Preferences

2. PROFILE EDIT PAGE
   - Create lib/src/features/claimant_portal/presentation/pages/profile_edit_page.dart
   - Editable form for profile information
   - Include validation for all fields
   - Add save/cancel buttons
   - Implement form state management

3. PROFILE INFORMATION WIDGET
   - Create lib/src/features/claimant_portal/presentation/widgets/profile_info_widget.dart
   - Display name, email, phone, address
   - Show associated claim information
   - Include join date and account status
   - Add quick action buttons

4. PROFILE EDIT FORM
   - Create lib/src/features/claimant_portal/presentation/widgets/profile_edit_form.dart
   - Form fields for all editable information
   - Real-time validation feedback
   - Consistent styling with ShadCN components
   - Handle form submission and errors

5. PROFILE PICTURE WIDGET
   - Create lib/src/features/claimant_portal/presentation/widgets/profile_picture_widget.dart
   - Display current profile picture or default avatar
   - Add upload/change picture functionality
   - Image cropping and resizing
   - Handle image validation and errors

6. PREFERENCES MANAGEMENT
   - Create lib/src/features/claimant_portal/presentation/widgets/preferences_widget.dart
   - Notification preferences (email, push, SMS)
   - Communication preferences
   - Privacy settings
   - Language and timezone settings

7. PROFILE PROVIDER
   - Create lib/src/features/claimant_portal/presentation/providers/profile_provider.dart
   - Manage profile state and updates
   - Handle form validation and submission
   - Implement image upload functionality
   - Manage preferences changes

8. PROFILE SERVICE UPDATES
   - Extend existing profile service for claimant-specific operations
   - Add methods:
     * updateClaimantProfile(ClaimantProfile profile)
     * uploadProfilePicture(File image)
     * updatePreferences(Map<String, dynamic> preferences)
   - Implement proper validation and error handling

9. VALIDATION HELPERS
   - Create lib/src/features/claimant_portal/utils/profile_validation.dart
   - Email format validation
   - Phone number validation
   - Address validation
   - Name validation rules

ACCEPTANCE CRITERIA:
- Profile view displays all current information correctly
- Profile edit form validates input properly
- Profile picture upload works with proper validation
- Preferences can be updated and saved
- Form submission handles success and error states
- UI is responsive and follows design patterns
- Data persistence works correctly
- Validation provides clear error messages

DEPENDENCIES:
- Task 01: Setup and Infrastructure (completed)
- Claimant profile model and service
- File upload service for profile pictures
- Form validation utilities

ESTIMATED TIME: 8-10 hours

PRIORITY: MEDIUM (Important for user experience)
