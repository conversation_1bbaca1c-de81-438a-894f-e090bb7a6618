TASK 04: CLAIMS MANAGEMENT - LIST VIEW
=====================================

DESCRIPTION:
Implement the claims list view that displays all claims associated with the claimant in a clean, organized interface.

OBJECTIVES:
- Create claims list page with filtering and sorting
- Implement claim summary cards
- Add search and filter functionality
- Set up navigation to claim details

DELIVERABLES:

1. CLAIMS LIST PAGE
   - Create lib/src/features/claimant_portal/presentation/pages/claims_list_page.dart
   - Implement scrollable list of claim cards
   - Add pull-to-refresh functionality
   - Include empty state when no claims exist
   - Add loading states and error handling

2. CLAIM SUMMARY CARD
   - Create lib/src/features/claimant_portal/presentation/widgets/claim_summary_card.dart
   - Display: Claim ID, Title, Current Status, Submission Date
   - Add status indicator with color coding
   - Include tap gesture to navigate to details
   - Implement consistent ShadCN card styling

3. CLAIMS LIST PROVIDER
   - Create lib/src/features/claimant_portal/presentation/providers/claims_list_provider.dart
   - Manage claims list state and loading
   - Handle filtering and sorting logic
   - Implement search functionality
   - Add refresh and pagination support

4. FILTERING AND SORTING
   - Create lib/src/features/claimant_portal/presentation/widgets/claims_filter_widget.dart
   - Add filters: Status, Date Range, Claim Type
   - Implement sorting: Date (newest/oldest), Status, Title
   - Include clear filters option
   - Save filter preferences locally

5. SEARCH FUNCTIONALITY
   - Add search bar to filter claims by title or ID
   - Implement debounced search to avoid excessive API calls
   - Highlight search terms in results
   - Add search history and suggestions

6. STATUS INDICATORS
   - Create lib/src/features/claimant_portal/presentation/widgets/claim_status_indicator.dart
   - Color-coded status badges
   - Status icons and descriptions
   - Progress indicators for multi-stage statuses

ACCEPTANCE CRITERIA:
- Claims list loads and displays all claimant's claims
- Claim cards show correct information and status
- Filtering and sorting work correctly
- Search functionality filters results in real-time
- Pull-to-refresh updates the claims list
- Navigation to claim details works properly
- Loading and error states display appropriately
- Responsive design works on all screen sizes

DEPENDENCIES:
- Task 03: Claims Management Models (completed)
- Claims service and repository
- ShadCN UI components
- Navigation system

ESTIMATED TIME: 8-10 hours

PRIORITY: HIGH (Primary claims interface)
