TASK 08: CHAT SYSTEM - USER INTERFACE
=====================================

DESCRIPTION:
Build the chat user interface that allows claimants to communicate with 3Pay agents in a modern, intuitive chat experience.

OBJECTIVES:
- Create chat conversation list view
- Build chat message interface
- Implement real-time message updates
- Add file sharing capabilities

DELIVERABLES:

1. CHAT CONVERSATIONS LIST
   - Create lib/src/features/claimant_portal/presentation/pages/chat_conversations_page.dart
   - Display all active conversations
   - Show last message preview and timestamp
   - Include unread message indicators
   - Add search functionality for conversations

2. CONVERSATION LIST ITEM
   - Create lib/src/features/claimant_portal/presentation/widgets/conversation_list_item.dart
   - Display agent name and avatar
   - Show last message preview (truncated)
   - Include timestamp and unread count
   - Add online/offline status indicator

3. CHAT INTERFACE PAGE
   - Create lib/src/features/claimant_portal/presentation/pages/chat_interface_page.dart
   - Full-screen chat interface
   - Message list with scroll-to-bottom
   - Message input field with send button
   - File attachment button and preview

4. MESSAGE BUBBLE WIDGET
   - Create lib/src/features/claimant_portal/presentation/widgets/message_bubble_widget.dart
   - Different styles for sent/received messages
   - Support text, file, and system messages
   - Include timestamp and delivery status
   - Add message reactions if needed

5. MESSAGE INPUT WIDGET
   - Create lib/src/features/claimant_portal/presentation/widgets/message_input_widget.dart
   - Text input with emoji support
   - File attachment button
   - Send button with loading state
   - Character count and validation

6. FILE ATTACHMENT WIDGET
   - Create lib/src/features/claimant_portal/presentation/widgets/file_attachment_widget.dart
   - File picker integration
   - File preview before sending
   - Upload progress indicator
   - File type and size validation

7. CHAT PROVIDERS
   - Create lib/src/features/claimant_portal/presentation/providers/chat_conversations_provider.dart
   - Create lib/src/features/claimant_portal/presentation/providers/chat_interface_provider.dart
   - Manage chat state and real-time updates
   - Handle message sending and receiving
   - Implement typing indicators

8. TYPING INDICATOR
   - Create lib/src/features/claimant_portal/presentation/widgets/typing_indicator_widget.dart
   - Show when agent is typing
   - Animated dots or text indicator
   - Auto-hide after timeout

9. CHAT HEADER
   - Create lib/src/features/claimant_portal/presentation/widgets/chat_header_widget.dart
   - Show agent name and status
   - Include back button and options menu
   - Display conversation info (claim reference)

ACCEPTANCE CRITERIA:
- Chat conversations list shows all active chats
- Chat interface displays messages correctly
- Real-time messaging works smoothly
- File attachments can be sent and received
- Typing indicators work properly
- Message delivery status is visible
- UI is responsive and works on all screen sizes
- Performance is good with message history
- Follows ShadCN design patterns

DEPENDENCIES:
- Task 07: Chat System Models (completed)
- Chat service and repository
- File upload service
- Real-time messaging capability
- Reference existing chat implementation

ESTIMATED TIME: 12-15 hours

PRIORITY: MEDIUM (Important for user support)
