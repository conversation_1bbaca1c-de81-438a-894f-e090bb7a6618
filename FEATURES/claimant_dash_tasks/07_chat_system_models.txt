TASK 07: CHAT SYSTEM - MODELS AND SERVICES
==========================================

DESCRIPTION:
Create the data layer for the chat system that enables communication between claimants and 3Pay agents, leveraging existing application_communications collection.

OBJECTIVES:
- Create chat-specific data models
- Build chat service layer
- Implement real-time messaging capability
- Set up conversation management

DELIVERABLES:

1. CHAT MESSAGE MODEL
   - Create lib/src/features/claimant_portal/data/models/chat_message_model.dart
   - Map from existing application_communications collection
   - Include fields: id, conversation_id, sender_id, message_content, timestamp, message_type
   - Add sender information (name, role, avatar)
   - Support different message types (text, file, system)

2. CHAT CONVERSATION MODEL
   - Create lib/src/features/claimant_portal/data/models/chat_conversation_model.dart
   - Include fields: id, claim_id, participants, last_message, unread_count, created_date
   - Add conversation status (active, closed, archived)
   - Include participant information and roles

3. CHAT SERVICE
   - Create lib/src/features/claimant_portal/data/services/chat_service.dart
   - Implement methods:
     * getConversationsForClaimant(String claimantId)
     * getMessagesForConversation(String conversationId)
     * sendMessage(String conversationId, String content)
     * createConversation(String claimId)
     * subscribeToConversationUpdates(String conversationId)
   - Handle file attachments and media messages
   - Implement message delivery status

4. CHAT REPOSITORY
   - Create lib/src/features/claimant_portal/data/repositories/chat_repository.dart
   - Abstract chat data access
   - Handle message caching and offline support
   - Implement conversation state management
   - Add message synchronization logic

5. REAL-TIME MESSAGING
   - Set up PocketBase real-time subscriptions for new messages
   - Handle typing indicators
   - Implement message delivery and read receipts
   - Manage connection state and reconnection

6. MESSAGE VALIDATION
   - Add message content validation
   - Implement spam protection
   - Handle message length limits
   - Add profanity filtering if required

7. FILE ATTACHMENT SUPPORT
   - Support file uploads in chat messages
   - Handle image, document, and media files
   - Implement file size and type restrictions
   - Add file preview capabilities

ACCEPTANCE CRITERIA:
- Chat models correctly map from existing collections
- Chat service can create and manage conversations
- Real-time messaging works reliably
- File attachments can be sent and received
- Message validation prevents invalid content
- Offline support maintains message queue
- Error handling covers network issues
- Performance is good with message history

DEPENDENCIES:
- Task 01: Setup and Infrastructure (completed)
- Existing application_communications collection
- File upload service
- Real-time subscription capability
- Reference existing application_chat_page.dart

ESTIMATED TIME: 8-10 hours

PRIORITY: MEDIUM (Important for support communication)
