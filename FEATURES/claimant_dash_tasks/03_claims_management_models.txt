TASK 03: CLAIMS MANAGEMENT - DATA MODELS
========================================

DESCRIPTION:
Create data models and services for claims management functionality, leveraging existing funding_applications collection.

OBJECTIVES:
- Create claimant-specific claim models
- Build service layer for claims data access
- Implement data transformation and filtering
- Set up real-time updates for claim status

DELIVERABLES:

1. CLAIM DATA MODELS
   - Create lib/src/features/claimant_portal/data/models/claimant_claim_model.dart
   - Map from existing funding_applications collection
   - Include fields: id, title, status, submission_date, current_stage, description
   - Add status enum and helper methods
   - Implement fromRecord() method for PocketBase integration

2. CLAIM STATUS MODEL
   - Create lib/src/features/claimant_portal/data/models/claim_status_model.dart
   - Define status types: submitted, under_review, approved, rejected, funded
   - Add status display names and colors
   - Include timestamp and status change history

3. CLAIMS SERVICE
   - Create lib/src/features/claimant_portal/data/services/claims_service.dart
   - Implement methods:
     * getClaimsForClaimant(String claimantId)
     * getClaimDetails(String claimId)
     * subscribeToClaimUpdates(String claimId)
   - Add proper error handling and logging
   - Filter claims based on claimant association

4. CLAIMS REPOSITORY
   - Create lib/src/features/claimant_portal/data/repositories/claims_repository.dart
   - Abstract data access layer
   - Handle caching and offline support
   - Implement data transformation between PocketBase and app models

5. REAL-TIME UPDATES
   - Set up PocketBase real-time subscriptions for claim status changes
   - Implement proper subscription cleanup
   - Handle connection state and reconnection
   - Add notification triggers for status updates

ACCEPTANCE CRITERIA:
- ClaimantClaim model correctly maps from funding_applications collection
- Claims service can fetch claimant-specific claims
- Real-time updates work for claim status changes
- Proper error handling for network issues
- Data filtering works correctly for claimant association
- Models include proper validation and type safety

DEPENDENCIES:
- Task 01: Setup and Infrastructure (completed)
- Existing funding_applications collection
- PocketBase service integration
- Logger service

ESTIMATED TIME: 5-7 hours

PRIORITY: HIGH (Core data layer for claims)
