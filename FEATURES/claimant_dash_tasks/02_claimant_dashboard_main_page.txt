TASK 02: CLAIMANT DASHBOARD MAIN PAGE
====================================

DESCRIPTION:
Create the main dashboard page that serves as the central hub for claimants to access all features.

OBJECTIVES:
- Build the main claimant dashboard page with navigation
- Implement responsive layout using ShadCN components
- Create overview cards for each major feature
- Set up navigation to individual feature pages

DELIVERABLES:

1. MAIN DASHBOARD PAGE
   - Create lib/src/features/claimant_portal/presentation/pages/claimant_dashboard_page.dart
   - Implement responsive layout with card-based design
   - Include header with user greeting and notifications bell
   - Add quick access cards for: Claims, Notifications, Chat, Profile, Funding

2. DASHBOARD WIDGETS
   - Create lib/src/features/claimant_portal/presentation/widgets/dashboard_overview_card.dart
   - Create lib/src/features/claimant_portal/presentation/widgets/dashboard_header.dart
   - Create lib/src/features/claimant_portal/presentation/widgets/quick_stats_widget.dart
   - Implement consistent styling with ShadCN components

3. NAVIGATION SETUP
   - Update app routing to include claimant dashboard routes
   - Add navigation from landing page to claimant dashboard
   - Implement proper route guards for claimant authentication
   - Add breadcrumb navigation

4. DASHBOARD PROVIDER
   - Create lib/src/features/claimant_portal/presentation/providers/dashboard_provider.dart
   - Manage dashboard state and data loading
   - Handle quick stats and overview data
   - Implement refresh functionality

5. RESPONSIVE DESIGN
   - Implement desktop-friendly layout (3-column grid for tablets)
   - Add proper breakpoints for mobile, tablet, desktop
   - Ensure horizontal card arrangements for desktop
   - Test on different screen sizes

ACCEPTANCE CRITERIA:
- Dashboard page loads without errors for authenticated claimants
- All navigation cards are clickable and lead to correct pages
- Responsive design works across mobile, tablet, and desktop
- Header displays user information and notification count
- Quick stats show relevant claimant data
- Consistent ShadCN styling throughout

DEPENDENCIES:
- Task 01: Setup and Infrastructure (completed)
- Existing authentication system
- ShadCN UI components
- App routing system

ESTIMATED TIME: 6-8 hours

PRIORITY: HIGH (Core navigation hub)
