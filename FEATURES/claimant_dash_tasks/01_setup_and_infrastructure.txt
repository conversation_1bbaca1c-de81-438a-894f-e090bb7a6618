TASK 01: SETUP AND INFRASTRUCTURE
=====================================

DESCRIPTION:
Set up the basic infrastructure and foundation for the claimant dashboard implementation.

OBJECTIVES:
- Create the basic folder structure for claimant portal features
- Set up data models for claimant-specific functionality
- Establish PocketBase collection relationships
- Create basic service classes

DELIVERABLES:

1. FOLDER STRUCTURE CREATION
   - Create lib/src/features/claimant_portal/ directory
   - Create subdirectories:
     * presentation/pages/
     * presentation/widgets/
     * presentation/providers/
     * data/models/
     * data/services/

2. CLAIMANT PROFILE MODEL
   - Create lib/src/features/claimant_portal/data/models/claimant_profile_model.dart
   - Include fields: id, user_id, name, email, phone_number, address, associated_case_id
   - Add from<PERSON>son() and to<PERSON>son() methods
   - Add validation methods

3. POCKETBASE COLLECTION SETUP
   - Verify existing collections: funding_applications, notifications, application_communications, users, funding_commitments
   - Create claimant_profiles collection if needed
   - Set up proper relationships and access rules

4. BASE SERVICE CLASS
   - Create lib/src/features/claimant_portal/data/services/claimant_base_service.dart
   - Extend from existing PocketBase service patterns
   - Include error handling and logging
   - Add authentication checks

5. PROVIDER SETUP
   - Create lib/src/features/claimant_portal/presentation/providers/claimant_auth_provider.dart
   - Handle claimant-specific authentication state
   - Manage user session and permissions

ACCEPTANCE CRITERIA:
- All folder structures created and properly organized
- ClaimantProfile model compiles without errors
- PocketBase collections accessible with proper permissions
- Base service class follows existing codebase patterns
- Authentication provider integrates with existing auth system

DEPENDENCIES:
- Existing PocketBase service (lib/src/core/services/pocketbase_service.dart)
- Existing authentication system
- ShadCN UI components

ESTIMATED TIME: 4-6 hours

PRIORITY: HIGH (Foundation for all other tasks)
