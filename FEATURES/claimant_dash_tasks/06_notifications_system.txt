TASK 06: NOTIFICATIONS SYSTEM
=============================

DESCRIPTION:
Implement the notifications system that keeps claimants informed about important updates and events related to their claims.

OBJECTIVES:
- Create notifications list and detail views
- Implement notification bell with unread count
- Set up real-time notification updates
- Add notification preferences and management

DELIVERABLES:

1. NOTIFICATION MODELS
   - Create lib/src/features/claimant_portal/data/models/claimant_notification_model.dart
   - Map from existing notifications collection
   - Include fields: id, title, message, type, read_status, created_date, claim_id
   - Add notification type enum (claim_update, funding_update, system, chat)
   - Implement priority levels and categorization

2. NOTIFICATIONS SERVICE
   - Create lib/src/features/claimant_portal/data/services/notifications_service.dart
   - Implement methods:
     * getNotificationsForClaimant(String claimantId)
     * markNotificationAsRead(String notificationId)
     * markAllAsRead()
     * subscribeToNotifications()
   - Filter notifications for claimant-specific content
   - Handle real-time subscription management

3. NOTIFICATION BELL WIDGET
   - Create lib/src/features/claimant_portal/presentation/widgets/notification_bell_widget.dart
   - Display in dashboard header
   - Show unread count badge
   - Animate when new notifications arrive
   - Navigate to notifications list on tap

4. NOTIFICATIONS LIST PAGE
   - Create lib/src/features/claimant_portal/presentation/pages/notifications_list_page.dart
   - Display all notifications in chronological order
   - Group by date (Today, Yesterday, This Week, etc.)
   - Show read/unread status with visual indicators
   - Add pull-to-refresh and infinite scroll

5. NOTIFICATION ITEM WIDGET
   - Create lib/src/features/claimant_portal/presentation/widgets/notification_item_widget.dart
   - Display notification title, preview, and timestamp
   - Show notification type icon and priority
   - Handle tap to view full notification
   - Mark as read when opened

6. NOTIFICATION DETAIL PAGE
   - Create lib/src/features/claimant_portal/presentation/pages/notification_detail_page.dart
   - Show full notification content
   - Include related claim information if applicable
   - Add action buttons (View Claim, Start Chat, etc.)
   - Auto-mark as read when opened

7. NOTIFICATIONS PROVIDER
   - Create lib/src/features/claimant_portal/presentation/providers/notifications_provider.dart
   - Manage notifications state and real-time updates
   - Handle read/unread status changes
   - Implement filtering and categorization
   - Manage notification preferences

8. REAL-TIME UPDATES
   - Set up PocketBase real-time subscription for new notifications
   - Handle notification arrival with local notifications
   - Update unread count in real-time
   - Manage subscription lifecycle

ACCEPTANCE CRITERIA:
- Notification bell shows correct unread count
- Notifications list displays all claimant notifications
- Real-time updates work for new notifications
- Mark as read functionality works correctly
- Notification detail view shows complete information
- Filtering and categorization work properly
- Performance is good with large notification lists
- UI follows consistent ShadCN styling

DEPENDENCIES:
- Task 01: Setup and Infrastructure (completed)
- Existing notifications collection
- Real-time subscription capability
- Local notification service (optional)

ESTIMATED TIME: 8-10 hours

PRIORITY: HIGH (Critical for user engagement)
