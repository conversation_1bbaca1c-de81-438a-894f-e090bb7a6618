TASK 10: FUNDING DASHBOARD
==========================

DESCRIPTION:
Create a funding dashboard that provides claimants with visibility into their funding status, commitments, and financial information.

OBJECTIVES:
- Build funding overview dashboard
- Display funding commitments and status
- Show funding timeline and milestones
- Implement funding-related notifications

DELIVERABLES:

1. FUNDING MODELS
   - Create lib/src/features/claimant_portal/data/models/funding_status_model.dart
   - Map from existing funding_commitments collection
   - Include fields: commitment_id, amount, status, commitment_date, funder_info
   - Add funding stage enum and status indicators
   - Implement funding calculation helpers

2. FUNDING SERVICE
   - Create lib/src/features/claimant_portal/data/services/funding_service.dart
   - Implement methods:
     * getFundingStatusForClaim(String claimId)
     * getFundingCommitments(String claimId)
     * getFundingTimeline(String claimId)
   - Handle funding calculations and aggregations
   - Add real-time funding updates

3. FUNDING DASHBOARD PAGE
   - Create lib/src/features/claimant_portal/presentation/pages/funding_dashboard_page.dart
   - Overview of total funding secured
   - Funding status indicators
   - List of funding commitments
   - Funding timeline visualization

4. FUNDING OVERVIEW WIDGET
   - Create lib/src/features/claimant_portal/presentation/widgets/funding_overview_widget.dart
   - Total funding amount and percentage
   - Funding status (secured, pending, target)
   - Visual progress indicators
   - Key funding metrics

5. FUNDING COMMITMENT CARD
   - Create lib/src/features/claimant_portal/presentation/widgets/funding_commitment_card.dart
   - Individual funder commitment details
   - Commitment amount and date
   - Funder information (if available)
   - Commitment status indicator

6. FUNDING TIMELINE WIDGET
   - Create lib/src/features/claimant_portal/presentation/widgets/funding_timeline_widget.dart
   - Visual timeline of funding milestones
   - Key dates and events
   - Progress indicators
   - Estimated completion dates

7. FUNDING PROVIDER
   - Create lib/src/features/claimant_portal/presentation/providers/funding_provider.dart
   - Manage funding dashboard state
   - Handle real-time funding updates
   - Implement funding calculations
   - Manage funding notifications

8. FUNDING STATISTICS WIDGET
   - Create lib/src/features/claimant_portal/presentation/widgets/funding_stats_widget.dart
   - Key funding statistics and metrics
   - Funding velocity and trends
   - Comparative funding information
   - Visual charts and graphs

9. FUNDING NOTIFICATIONS
   - Extend notification system for funding updates
   - New commitment notifications
   - Funding milestone alerts
   - Status change notifications
   - Integration with main notification system

ACCEPTANCE CRITERIA:
- Funding dashboard displays accurate funding information
- Funding commitments are listed correctly
- Timeline shows proper funding progression
- Real-time updates work for funding changes
- Statistics and metrics are calculated correctly
- Notifications work for funding events
- UI is responsive and visually appealing
- Data accuracy is maintained across views

DEPENDENCIES:
- Task 01: Setup and Infrastructure (completed)
- Task 06: Notifications System (completed)
- Existing funding_commitments collection
- Claims data for funding association

ESTIMATED TIME: 10-12 hours

PRIORITY: HIGH (Core funding visibility)
