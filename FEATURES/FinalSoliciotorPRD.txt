<context>
# Overview
This document outlines the Product Requirements for the Solicitor Portal of the 3Pay Group Litigation Funding Platform.
The portal aims to solve the need for a centralized, efficient, and secure platform for solicitors to manage litigation funding applications and active claims.
It is designed for solicitors and their firms who engage with 3Pay Global for litigation funding.
The value lies in streamlining complex workflows, enhancing communication (via 3Pay Global agents), providing transparency in claim progression, and ensuring robust document management and data security, ultimately improving solicitor productivity and experience.

# Core Features
The Solicitor Portal encompasses several core features:

1.  **Funding Application Management:**
    *   **What it does:** Allows solicitors to create, draft, submit, and track funding applications for claims. Includes multi-step forms for detailed claim information (including new fields for Claimant Type, Claim Industry, Claim Type), document uploads (Schedule of Costs, Legal Opinion, etc.), and CFA confirmation.
    *   **Why it's important:** Centralizes and standardizes the application process, ensuring all necessary information is captured for review by 3Pay Global agents.
    *   **How it works:** Solicitors fill out a guided multi-step form, upload required documents, and submit for review. They can save drafts and track application status (Draft, Submitted, Under Review, Requires Info, Approved, Rejected, Funded).

2.  **Claim Management:**
    *   **What it does:** Provides tools to manage active funded claims, including status tracking, document repository, linking professionals (Barristers, Experts, internal firm members), and cost tracking (basic).
    *   **Why it's important:** Offers a centralized hub for all information and activities related to an ongoing funded claim.
    *   **How it works:** Approved applications transition to active claims. Solicitors can view claim details, update status/milestones (for internal/agent reporting), manage claim-specific documents, and link relevant parties for record-keeping.

3.  **Evidence Management:**
    *   **What it does:** Secure system for submitting, tracking, and managing evidentiary and supporting documents for both applications and active claims. Supports versioning (timestamp-based) and simple text comments for document review.
    *   **Why it's important:** Ensures all documents are organized, versioned, and accessible with contextual comments, facilitating review by solicitors and 3Pay Global agents.
    *   **How it works:** Solicitors upload documents via file upload components. The system stores versions and allows text comments on each version. Document previews are available.

4.  **Communication (Centralized via 3Pay Global Agents):**
    *   **What it does:** Facilitates secure, context-specific communication between solicitors and 3Pay Global agents for each application and claim. All external communication (with claimants, co-funders) is managed by these agents.
    *   **Why it's important:** Ensures a controlled, auditable, and consistent communication flow.
    *   **How it works:** Solicitors use a dedicated "Communication with 3Pay Agent" tab within application/claim views. Internal firm collaboration on claims is supported via a separate "Internal Firm Notes/Discussion" tab.

5.  **Solicitor Dashboard:**
    *   **What it does:** Personalized landing page providing an at-a-glance overview of active claims, pending applications, tasks requiring attention (e.g., messages from agents, applications needing info), urgent notifications, and quick access to common actions. Optional KPIs.
    *   **Why it's important:** Enhances solicitor efficiency by surfacing critical information and actionable items.
    *   **How it works:** Widget-based dashboard dynamically displaying relevant information aggregated from various platform modules.

6.  **Firm Document Management:**
    *   **What it does:** Centralized repository for the solicitor's firm to manage general business documents not tied to specific claims (e.g., compliance documents, internal templates). Supports folder structures and simple version control.
    *   **Why it's important:** Keeps important firm-level documents organized and securely accessible.
    *   **How it works:** Dedicated section with folder navigation and document upload/management capabilities.

7.  **Reporting & Analytics:**
    *   **What it does:** Offers reports on funding application analytics (submission rates, success rates, time to decision) and active claim analytics (total claims, status breakdown, funding secured).
    *   **Why it's important:** Provides data-driven insights for solicitors and their firms.
    *   **How it works:** Dedicated reports section with filters, visualizations (charts, tables), and export options.

8.  **User Profile & Firm Management:**
    *   **What it does:** Allows solicitors to manage personal profiles, notification settings. PU solicitors/firm admins can manage firm profiles and associated firm users (invite, manage roles, disable/remove).
    *   **Why it's important:** Provides user control, facilitates firm administration, and enhances security.
    *   **How it works:** Settings pages with forms and lists for managing user and firm information.

9.  **Search & Filtering:**
    *   **What it does:** Global search bar and list-specific filtering/sorting for applications, claims, documents, etc.
    *   **Why it's important:** Enables quick and efficient information retrieval.
    *   **How it works:** Search across key fields, with results grouped by category and further filterable.

# User Experience
*   **User Personas:**
    *   **Primary User:** Solicitors (Permitted Users - PU, and additional firm users) responsible for preparing funding applications, managing active claims, and interacting with 3Pay Global.
*   **Key User Flows:**
    *   New Funding Application Submission (Multi-step, guided process).
    *   Tracking Application/Claim Status (List views, detail views, notifications).
    *   Managing Documents (Upload, versioning, text comments, preview).
    *   Communicating with 3Pay Global Agents (Contextual threads per application/claim).
    *   Managing Active Claims (Overview, status updates, linking professionals, internal notes).
    *   Responding to 'Requires Info' from 3Pay Global Agents.
    *   Managing Firm Users and Profiles.
*   **UI/UX Considerations:**
    *   **Intuitive Interface:** Clean, consistent design using Flutter with shadcn_ui components.
    *   **Efficiency:** Streamlined workflows, quick access to common actions, and informative dashboards.
    *   **Clarity:** Clear status indicators, notifications, and error messages.
    *   **Accessibility:** Adherence to WCAG 2.1 AA guidelines (keyboard navigation, color contrast, text alternatives, ARIA attributes for custom components).
    *   **Error Handling:** Graceful handling of validation, network, server, and authorization errors with user-friendly messages.
    *   **Empty States & Loading States:** Clear visual feedback for no data scenarios and data fetching.
    *   **Consistency:** Alignment with 3Pay brand and co-funder portal where appropriate, with strategic divergences for solicitor-specific needs.
    *   **Responsive Design:** Adaptable to different screen sizes.
</context>
<PRD>
# Technical Architecture
*   **System Components:**
    *   **Frontend:** Flutter application using shadcn_ui component library.
    *   **Backend:** PocketBase (managing data storage, business logic, authentication).
    *   **Communication:** Interactions between frontend and backend via PocketBase SDK/API.
*   **Data Models (Key PocketBase Collections):**
    *   `users`: Stores user authentication details.
    *   `solicitor_profiles`: Stores solicitor and firm-specific information, PU status, linked firm users.
    *   `funding_applications`: Core collection for funding application details, status, linked documents, review notes.
        *   *New Fields Required:* `claimant_type` (select), `claim_industry` (select), `claim_type` (select).
    *   `claims` (formerly `cases`): Manages active funded claims, status, linked documents, professionals, cost tracking info.
    *   `claimant_profiles`: Information about claimants (communication managed by agents).
    *   `barristers`, `experts`: Profiles for linked legal professionals.
    *   `document_templates`: Templates for solicitors (e.g., Schedule of Costs).
    *   `uploaded_documents` (JSON field within `funding_applications` and `claims`): Manages metadata for uploaded files, including versions and text comments.
    *   `claim_communications` (or similar): Stores messages between solicitors and 3Pay Global agents, linked to applications/claims.
    *   `firm_documents`: For firm-level document storage.
    *   `notifications`: Manages in-app and email notifications.
    *   `user_activity_logs`: Tracks significant user actions.
*   **APIs and Integrations:**
    *   Primary interaction with PocketBase backend.
    *   Potential for future integration with dedicated search services (e.g., Algolia, Typesense) for advanced search capabilities.
*   **Infrastructure Requirements:**
    *   Hosting for Flutter web/mobile application.
    *   PocketBase server instance.
    *   Email service for notifications.

# Development Roadmap
*   **MVP Requirements (Illustrative - requires detailed planning):**
    *   User authentication and registration for solicitors.
    *   PU Application and approval flow (managed by 3Pay Global agents).
    *   Core funding application submission workflow (including new claim type/industry fields).
    *   Basic document upload with versioning and simple text comments.
    *   Application tracking and status updates.
    *   Communication channel with 3Pay Global agents per application.
    *   Transition of approved applications to basic "claims" (formerly "cases").
    *   Solicitor Dashboard with essential widgets (e.g., pending applications, messages from agents).
    *   Basic user profile management.
*   **Future Enhancements (Illustrative):**
    *   Full-featured claim management module.
    *   Advanced reporting and analytics.
    *   Comprehensive firm document repository with folder structures.
    *   Internal firm collaboration tools (e.g., internal notes on claims).
    *   Advanced search integration.
    *   Granular permission controls within firms.
    *   Full mobile application support if initially web-focused.

*(Note: Detailed phasing and timelines are beyond the scope of this UX design document and require separate project planning.)*

# Logical Dependency Chain
1.  **Foundation:**
    *   User Authentication & Authorization (Solicitor roles, firm association).
    *   PocketBase schema setup for core collections (`users`, `solicitor_profiles`, `funding_applications`, `claims`).
2.  **Core Application Flow:**
    *   PU Application process (interface for solicitors, review for agents).
    *   Funding Application Submission (multi-step form, document upload, new dropdowns).
    *   Application Status Tracking.
3.  **Communication & Review:**
    *   Communication module for Solicitor <-> 3Pay Global Agent.
    *   Review process for applications by agents (setting status, adding notes).
4.  **Claim Initiation:**
    *   Creation of `claims` records from approved applications.
    *   Basic Claim Detail View.
5.  **Dashboard & UI Shell:**
    *   Solicitor Dashboard with initial widgets.
    *   Navigation, profile management.
6.  **Supporting Features (Iterative):**
    *   Evidence Management enhancements (version history, comments UI).
    *   Reporting (basic then advanced).
    *   Firm Document Management.
    *   Full Claim Management features.
    *   Search capabilities.

*(Focus on getting a usable frontend for application submission and agent communication early, then build out claim management and other features.)*

# Risks and Mitigations
*   **Technical Challenges:**
    *   **Risk:** Implementing robust and performant document versioning and commenting within PocketBase JSON fields if complexity grows.
    *   **Mitigation:** Start with simple implementation; evaluate need for dedicated document management solutions or more structured DB approaches if performance degrades. Thorough testing.
    *   **Risk:** Scalability of PocketBase for a large number of users and claims.
    *   **Mitigation:** Follow PocketBase best practices for schema design and querying. Monitor performance and plan for potential scaling strategies (e.g., optimized queries, read replicas if supported, or eventual migration if limits are reached).
    *   **Risk:** Ensuring seamless and secure communication flow via 3Pay Global agents.
    *   **Mitigation:** Clear API contracts and robust notification systems between solicitor portal and agent interfaces.
*   **Figuring out the MVP that we can build upon:**
    *   **Risk:** Scope creep for MVP, delaying initial launch.
    *   **Mitigation:** Prioritize core application submission, agent communication, and basic claim tracking for MVP. Defer advanced reporting, full claim management features, and extensive firm admin tools to later phases. Adhere strictly to defined MVP requirements.
*   **Resource Constraints:**
    *   **Risk:** Limited development resources impacting delivery timelines or feature scope.
    *   **Mitigation:** Phased development approach. Clear prioritization of features. Regular review of scope against available resources.
*   **User Adoption:**
    *   **Risk:** Solicitors finding the new centralized communication model (via agents) cumbersome.
    *   **Mitigation:** Clear communication of benefits (auditability, consistency). Ensure agent responsiveness is high. Gather user feedback post-launch and iterate on agent interaction workflows.
*   **Data Migration (if applicable from existing systems):**
    *   **Risk:** Complexity and potential data loss during migration.
    *   **Mitigation:** Not explicitly mentioned, but if there's an old system, plan migration carefully with thorough validation.

# Appendix
*   [`SOLICITOR_FEATKILO.md`](SOLICITOR_FEATKILO.md) (This UX Design Document)
*   [`PRD.md`](PRD.md) (Overall Product Requirements Document)
*   [`DESIGN_DOCUMENT.md`](DESIGN_DOCUMENT.md) (System Architecture Document)
*   PocketBase Schema definitions for relevant collections (as outlined in Technical Architecture).
</PRD>