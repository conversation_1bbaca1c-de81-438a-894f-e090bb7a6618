# Local Notifications Integration Guide

This document outlines how to integrate local notifications into the 3Pay Global Flutter application using the `flutter_local_notifications` package.

## Overview

Local notifications allow the app to display notifications to users even when the app is not in the foreground. This is useful for:
- Reminding users about important updates
- Notifying about claim status changes
- Sending periodic reminders about funding opportunities
- Alerting users about new messages or documents

## Package Installation

Add the `flutter_local_notifications` package to your project:

```bash
flutter pub add flutter_local_notifications
```

## iOS Setup

For iOS, you need to configure the app delegate to handle notifications:

### 1. Edit `ios/Runner/AppDelegate.swift`

Add the following code to your `AppDelegate.swift` file:

```swift
import UIKit
import Flutter
import flutter_local_notifications // Add this import

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // Add this code block
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
    }
    
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
```

## Android Setup

For Android, the package works out of the box with minimal configuration. The default Flutter icon will be used unless you specify a custom notification icon.

### Custom Notification Icon (Optional)

To use a custom notification icon:
1. Place your icon in `android/app/src/main/res/drawable/`
2. Reference it in the notification settings (see implementation below)

## Implementation

### 1. Create Local Notification Service

Create a new service file `lib/src/core/services/local_notification_service.dart`:

```dart
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

class LocalNotificationService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  
  static bool _isInitialized = false;
  
  static bool get isInitialized => _isInitialized;

  /// Initialize the local notification service
  static Future<void> initialize() async {
    if (_isInitialized) {
      LoggerService.info('Local notifications already initialized');
      return;
    }

    try {
      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher'); // Use app icon

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // Combined initialization settings
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // Initialize the plugin
      await _notificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      _isInitialized = true;
      LoggerService.info('Local notifications initialized successfully');
    } catch (e) {
      LoggerService.error('Failed to initialize local notifications', e);
      rethrow;
    }
  }

  /// Handle notification tap events
  static void _onNotificationTapped(NotificationResponse response) {
    LoggerService.info('Notification tapped: ${response.payload}');
    // Handle navigation based on payload
    // You can implement custom navigation logic here
  }

  /// Show a local notification
  static Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_isInitialized) {
      LoggerService.warning('Local notifications not initialized');
      return;
    }

    try {
      // Android notification details
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        '3pay_channel', // Channel ID
        '3Pay Global Notifications', // Channel name
        channelDescription: 'Notifications for 3Pay Global app',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: true,
      );

      // iOS notification details
      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      // Combined notification details
      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // Show the notification
      await _notificationsPlugin.show(
        id,
        title,
        body,
        platformChannelSpecifics,
        payload: payload,
      );

      LoggerService.info('Local notification shown: $title');
    } catch (e) {
      LoggerService.error('Failed to show local notification', e);
    }
  }

  /// Schedule a notification for later
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    if (!_isInitialized) {
      LoggerService.warning('Local notifications not initialized');
      return;
    }

    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        '3pay_scheduled_channel',
        '3Pay Global Scheduled Notifications',
        channelDescription: 'Scheduled notifications for 3Pay Global app',
        importance: Importance.max,
        priority: Priority.high,
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _notificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        scheduledDate,
        platformChannelSpecifics,
        payload: payload,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );

      LoggerService.info('Scheduled notification: $title for $scheduledDate');
    } catch (e) {
      LoggerService.error('Failed to schedule notification', e);
    }
  }

  /// Cancel a specific notification
  static Future<void> cancelNotification(int id) async {
    try {
      await _notificationsPlugin.cancel(id);
      LoggerService.info('Cancelled notification with ID: $id');
    } catch (e) {
      LoggerService.error('Failed to cancel notification', e);
    }
  }

  /// Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    try {
      await _notificationsPlugin.cancelAll();
      LoggerService.info('Cancelled all notifications');
    } catch (e) {
      LoggerService.error('Failed to cancel all notifications', e);
    }
  }

  /// Request notification permissions (iOS)
  static Future<bool> requestPermissions() async {
    try {
      final bool? result = await _notificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
      
      LoggerService.info('Notification permissions granted: ${result ?? false}');
      return result ?? false;
    } catch (e) {
      LoggerService.error('Failed to request notification permissions', e);
      return false;
    }
  }
}
```

### 2. Initialize in Main Function

Update your `main.dart` file to initialize local notifications:

```dart
import 'package:flutter/material.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/local_notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize local notifications
  await LocalNotificationService.initialize();
  
  runApp(MyApp());
}
```

### 3. Integration with Existing Notification Service

You can integrate local notifications with your existing `NotificationService` to show local notifications when new remote notifications arrive:

```dart
// In your existing NotificationService._showToast method
void _showToast(EnhancedNotificationModel notification) {
  // Show local notification
  LocalNotificationService.showNotification(
    id: notification.id.hashCode, // Convert string ID to int
    title: notification.title,
    body: notification.message,
    payload: notification.id, // Pass notification ID for handling taps
  );
  
  LoggerService.info('New notification: ${notification.title}');
}
```

## Usage Examples

### Basic Notification
```dart
await LocalNotificationService.showNotification(
  id: 1,
  title: 'Claim Update',
  body: 'Your funding application has been approved!',
);
```

### Scheduled Notification
```dart
await LocalNotificationService.scheduleNotification(
  id: 2,
  title: 'Reminder',
  body: 'Don\'t forget to check your claim status',
  scheduledDate: DateTime.now().add(Duration(hours: 24)),
);
```

## Best Practices

1. **Unique IDs**: Use unique integer IDs for each notification to avoid conflicts
2. **Permission Handling**: Always check and request permissions on iOS
3. **Error Handling**: Wrap notification calls in try-catch blocks
4. **Payload Usage**: Use payloads to handle navigation when notifications are tapped
5. **Channel Management**: Use different channels for different types of notifications
6. **Testing**: Test on both iOS and Android devices to ensure proper functionality

## Integration Points

This local notification system can be integrated with:
- **Claim Status Updates**: Notify when claim status changes
- **Document Uploads**: Alert when new documents are available
- **Funding Opportunities**: Remind about new funding opportunities
- **Communication**: Notify about new messages from 3Pay Global agents
- **Deadlines**: Remind about important dates and deadlines

## Troubleshooting

1. **iOS Permissions**: Ensure permissions are requested and granted
2. **Android Icons**: Verify notification icons are properly configured
3. **Background Notifications**: Test notifications when app is in background
4. **Payload Handling**: Ensure navigation logic handles notification taps correctly
