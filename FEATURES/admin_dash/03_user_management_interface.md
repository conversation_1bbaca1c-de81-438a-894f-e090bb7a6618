# Task 03: Admin User Management Interface

## Objective
Implement comprehensive user management interface allowing admins to view, search, filter, and manage all platform users (solicitors, co-funders, claimants) through a unified interface with proper permissions and audit logging.

## Dependencies
- Task 01: Admin Authentication Setup
- Task 02: Basic Dashboard Structure

## Estimated Time
3-4 days

## Deliverables

### 1. User Management Main Page
**File**: `lib/src/features/admin_portal/presentation/pages/user_management_page.dart`

**Requirements**:
- Unified view of all user types with filtering
- Search functionality across user fields
- Bulk selection and actions
- User status management (active/inactive/pending)
- Responsive table/card layout

### 2. User Management Service
**File**: `lib/src/features/admin_portal/data/services/user_management_service.dart`

**Requirements**:
- CRUD operations for all user types
- Bulk user operations with proper error handling
- User status updates with audit logging
- Search and filtering capabilities
- Integration with existing PocketBase collections

### 3. User Management Provider
**File**: `lib/src/features/admin_portal/application/providers/user_management_provider.dart`

**Requirements**:
- State management for user lists and filters
- Real-time user data updates
- Search and pagination handling
- Bulk operation state management

### 4. User Detail Modal/Page
**File**: `lib/src/features/admin_portal/presentation/widgets/user_detail_widget.dart`

**Requirements**:
- Comprehensive user information display
- Edit capabilities for admin-manageable fields
- User activity history
- Status change controls with confirmation

### 5. User Management Models
**File**: `lib/src/features/admin_portal/data/models/admin_user_management_models.dart`

**Requirements**:
- Unified user model for admin interface
- User filter and search models
- Bulk operation models
- User status and permission models

## Implementation Details

### User Management Interface Structure
```dart
class UserManagementPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin/users';
  
  @override
  ConsumerState<UserManagementPage> createState() => _UserManagementPageState();
}

class _UserManagementPageState extends ConsumerState<UserManagementPage> {
  // User filtering and search state
  // Bulk selection management
  // Responsive layout implementation
}
```

### User Filter Options
```dart
enum UserType { all, solicitor, coFunder, claimant, admin }
enum UserStatus { all, active, inactive, pending, suspended }
enum SortOption { nameAsc, nameDesc, dateAsc, dateDesc, statusAsc, statusDesc }
```

### Bulk Operations
```dart
enum BulkUserOperation {
  activate,
  deactivate,
  suspend,
  delete,
  exportData,
  sendNotification,
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] Display all users in unified interface with type indicators
- [ ] Search functionality works across name, email, and user type
- [ ] Filter by user type, status, registration date, and activity
- [ ] Sort by multiple criteria (name, date, status, etc.)
- [ ] Bulk selection and operations with confirmation dialogs
- [ ] User detail view with comprehensive information
- [ ] Edit user status and basic information
- [ ] Audit logging for all user management actions

### Performance Requirements
- [ ] User list loads within 2 seconds for up to 10,000 users
- [ ] Search results appear within 1 second
- [ ] Pagination handles large datasets efficiently
- [ ] Bulk operations complete within 30 seconds with progress indicators

### Security Requirements
- [ ] Admin permission verification for all operations
- [ ] Sensitive user data properly protected
- [ ] Audit trail for all user modifications
- [ ] Confirmation required for destructive operations

### UI/UX Requirements
- [ ] Responsive design works on all screen sizes
- [ ] Consistent with existing portal designs
- [ ] Loading states and error handling implemented
- [ ] Accessibility standards met
- [ ] Intuitive navigation and user flows

## Code Examples

### User Management Service
```dart
class UserManagementService extends PocketBaseService {
  Future<List<AdminUserModel>> getAllUsers({
    UserType? type,
    UserStatus? status,
    String? searchQuery,
    int page = 1,
    int perPage = 50,
  }) async {
    try {
      String filter = _buildFilterQuery(type, status, searchQuery);
      
      final records = await getList(
        collectionName: 'users',
        page: page,
        perPage: perPage,
        filter: filter,
        sort: '-created',
        expand: 'solicitor_profiles,co_funder_profiles,claimant_profiles',
      );
      
      return records.items.map((record) => AdminUserModel.fromRecord(record)).toList();
    } catch (e) {
      LoggerService.error('Error fetching users for admin', e);
      rethrow;
    }
  }
  
  Future<void> bulkUpdateUserStatus(
    List<String> userIds,
    UserStatus newStatus,
    String reason,
  ) async {
    try {
      for (final userId in userIds) {
        await updateRecord(
          collectionName: 'users',
          recordId: userId,
          data: {'status': newStatus.name},
        );
        
        // Log audit trail
        await logAdminActivity('bulk_user_status_update', {
          'user_id': userId,
          'new_status': newStatus.name,
          'reason': reason,
          'admin_id': currentUser?.id,
        });
      }
    } catch (e) {
      LoggerService.error('Error in bulk user status update', e);
      rethrow;
    }
  }
}
```

### User Management Page Structure
```dart
class UserManagementPage extends ConsumerStatefulWidget {
  @override
  ConsumerState<UserManagementPage> createState() => _UserManagementPageState();
}

class _UserManagementPageState extends ConsumerState<UserManagementPage> {
  final TextEditingController _searchController = TextEditingController();
  UserType _selectedType = UserType.all;
  UserStatus _selectedStatus = UserStatus.all;
  Set<String> _selectedUsers = {};
  
  @override
  Widget build(BuildContext context) {
    final usersAsync = ref.watch(userManagementProvider);
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Management'),
        actions: [
          _buildBulkActionsButton(),
          _buildExportButton(),
        ],
      ),
      body: Column(
        children: [
          _buildFiltersAndSearch(),
          Expanded(
            child: usersAsync.when(
              data: (users) => isDesktop 
                ? _buildDesktopTable(users)
                : _buildMobileList(users),
              loading: () => const UserManagementLoadingWidget(),
              error: (error, stack) => UserManagementErrorWidget(error: error),
            ),
          ),
        ],
      ),
    );
  }
}
```

### User Detail Widget
```dart
class UserDetailWidget extends ConsumerStatefulWidget {
  final String userId;
  
  const UserDetailWidget({required this.userId});
  
  @override
  ConsumerState<UserDetailWidget> createState() => _UserDetailWidgetState();
}

class _UserDetailWidgetState extends ConsumerState<UserDetailWidget> {
  @override
  Widget build(BuildContext context) {
    final userAsync = ref.watch(userDetailProvider(widget.userId));
    
    return ShadDialog(
      title: const Text('User Details'),
      content: SizedBox(
        width: 600,
        height: 500,
        child: userAsync.when(
          data: (user) => _buildUserDetailContent(user),
          loading: () => const CustomSkeletonWidget(),
          error: (error, stack) => Text('Error: $error'),
        ),
      ),
      actions: [
        ShadButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
        ShadButton(
          onPressed: () => _editUser(),
          child: const Text('Edit User'),
        ),
      ],
    );
  }
}
```

## Testing Requirements

### Unit Tests
- User management service methods
- Filter and search logic
- Bulk operation handling
- User model validation

### Widget Tests
- User management page rendering
- Filter and search widgets
- User detail modal
- Bulk action buttons

### Integration Tests
- End-to-end user management flows
- PocketBase integration validation
- Audit logging verification
- Permission checking

### Manual Testing
- Cross-device responsive testing
- User management workflows
- Bulk operations with large datasets
- Error handling scenarios

## Data Integration

### PocketBase Collections
- `users` - Primary user data
- `solicitor_profiles` - Solicitor-specific information
- `co_funder_profiles` - Co-funder details and levels
- `claimant_profiles` - Claimant information
- `user_activity_logs` - Audit trail for user actions

### Search Fields
- Name (from profiles)
- Email (from users)
- User type
- Registration date
- Last login
- Status

### Filter Options
- User type (solicitor, co-funder, claimant, admin)
- Status (active, inactive, pending, suspended)
- Registration date range
- Last activity date range
- Access level (for co-funders)

## Security Considerations

### Access Control
- Verify admin permissions before any operation
- Implement role-based access to sensitive operations
- Audit all user management actions
- Protect sensitive user data

### Data Protection
- Mask sensitive information in lists
- Require additional confirmation for destructive actions
- Log all data access and modifications
- Implement proper error handling to prevent data leaks

## Next Steps
Upon completion, proceed to Task 04: Audit Logging System to implement comprehensive activity tracking for admin actions.
