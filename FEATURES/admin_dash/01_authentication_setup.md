# Task 01: Admin Authentication Setup

## Objective
Establish secure authentication and authorization system for admin users, extending existing PocketBase authentication patterns to support admin role verification and session management.

## Dependencies
- None (Foundation task)

## Estimated Time
2-3 days

## Deliverables

### 1. Admin Authentication Service
**File**: `lib/src/features/admin_portal/data/services/admin_auth_service.dart`

**Requirements**:
- Extend existing PocketBaseService patterns
- Implement admin role verification (`user_type = "admin"`)
- Add admin-specific session management
- Include audit logging for admin authentication events
- Support multi-factor authentication preparation

### 2. Admin User Model
**File**: `lib/src/features/admin_portal/data/models/admin_user_model.dart`

**Requirements**:
- Extend existing User model with admin-specific fields
- Include permission levels (super_admin, content_admin, support_admin)
- Add last login tracking and session information
- Include admin preferences and settings

### 3. Admin Authentication Provider
**File**: `lib/src/features/admin_portal/application/providers/admin_auth_provider.dart`

**Requirements**:
- Riverpod state management for admin authentication
- Real-time authentication state updates
- Session timeout handling
- Admin permission level management

### 4. Admin Login Page
**File**: `lib/src/features/admin_portal/presentation/pages/admin_login_page.dart`

**Requirements**:
- ShadCN UI components for consistent design
- Enhanced security features (MFA preparation)
- Responsive design following existing patterns
- Integration with existing sign-in flow

## Implementation Details

### Admin Authentication Service Structure
```dart
class AdminAuthService extends PocketBaseService {
  // Admin-specific authentication methods
  Future<AdminUser> signInAdmin(String email, String password);
  Future<void> verifyAdminPermissions(String userId);
  Future<void> logAdminActivity(String action, Map<String, dynamic> details);
  Future<bool> hasPermission(String permission);
  Future<void> refreshAdminSession();
}
```

### Admin User Model Fields
```dart
class AdminUser {
  final String id;
  final String email;
  final String name;
  final AdminPermissionLevel permissionLevel;
  final DateTime lastLogin;
  final bool mfaEnabled;
  final Map<String, dynamic> preferences;
  final List<String> permissions;
}
```

### Authentication Flow
1. User enters credentials on admin login page
2. Service validates credentials against users collection
3. Verify `user_type = "admin"` in user record
4. Check admin permission level and active status
5. Create admin session with enhanced security
6. Log authentication event to audit trail
7. Redirect to admin dashboard

## Acceptance Criteria

### Functional Requirements
- [ ] Admin users can authenticate with email/password
- [ ] System verifies admin role before granting access
- [ ] Authentication state persists across app restarts
- [ ] Failed login attempts are logged and rate-limited
- [ ] Admin sessions have appropriate timeout periods
- [ ] All authentication events are audit logged

### Security Requirements
- [ ] Passwords are handled securely (PocketBase managed)
- [ ] Session tokens are stored securely
- [ ] Admin role verification is server-side enforced
- [ ] Rate limiting prevents brute force attacks
- [ ] Audit trail captures all authentication events

### UI/UX Requirements
- [ ] Login page follows existing design patterns
- [ ] Responsive design works on all screen sizes
- [ ] Loading states and error handling implemented
- [ ] Consistent with 3Pay Global branding
- [ ] Accessibility standards met

## Testing Requirements

### Unit Tests
- Admin authentication service methods
- Admin user model validation
- Permission checking logic
- Session management functions

### Integration Tests
- End-to-end authentication flow
- PocketBase integration validation
- Audit logging verification
- Session persistence testing

### Security Tests
- Role verification enforcement
- Session timeout handling
- Rate limiting functionality
- Audit trail completeness

## Code Examples

### Admin Authentication Service
```dart
class AdminAuthService extends PocketBaseService {
  Future<AdminUser> signInAdmin(String email, String password) async {
    try {
      final authData = await signIn(email, password);
      
      // Verify admin role
      final userType = authData.record?.data['user_type'];
      if (userType != 'admin') {
        throw AdminAuthException('Insufficient permissions');
      }
      
      // Log admin login
      await logAdminActivity('admin_login', {
        'user_id': authData.record!.id,
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      return AdminUser.fromRecord(authData.record!);
    } catch (e) {
      LoggerService.error('Admin authentication failed', e);
      rethrow;
    }
  }
}
```

### Admin Login Page Structure
```dart
class AdminLoginPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin-login';
  
  @override
  ConsumerState<AdminLoginPage> createState() => _AdminLoginPageState();
}

class _AdminLoginPageState extends ConsumerState<AdminLoginPage> {
  final _formKey = GlobalKey<ShadFormState>();
  // Implementation following existing sign-in patterns
}
```

## Integration Points

### Existing Services
- Extend `PocketBaseService` for admin-specific functionality
- Integrate with existing `LoggerService` for audit logging
- Use existing error handling patterns

### Navigation Integration
- Update app routing to include admin login route
- Modify existing sign-in flow to detect admin users
- Implement admin-specific navigation guards

### State Management
- Follow existing Riverpod patterns
- Integrate with existing authentication providers
- Maintain consistency with other portal auth states

## Security Considerations

### Access Control
- Server-side role verification required
- Client-side checks are supplementary only
- Admin permissions stored securely
- Session management follows security best practices

### Audit Requirements
- All admin authentication events logged
- Failed login attempts tracked
- Session creation/destruction recorded
- Permission changes audited

## Next Steps
Upon completion, proceed to Task 02: Basic Dashboard Structure to build the foundation admin dashboard layout.
