# Task 07: Navigation and Routing

## Objective
Implement comprehensive navigation and routing system for the admin portal, including deep linking, navigation guards, breadcrumbs, and integration with the existing app routing structure.

## Dependencies
- Task 01: Admin Authentication Setup
- Task 02: Basic Dashboard Structure
- Task 03: Admin User Management Interface

## Estimated Time
2-3 days

## Deliverables

### 1. Admin Route Configuration
**File**: `lib/src/features/admin_portal/presentation/routes/admin_routes.dart`

**Requirements**:
- Complete route definitions for all admin pages
- Route guards for authentication and permissions
- Deep linking support
- Parameter passing and validation
- Integration with existing app routing

### 2. Navigation Guard Service
**File**: `lib/src/features/admin_portal/data/services/navigation_guard_service.dart`

**Requirements**:
- Authentication verification
- Permission-based access control
- Route protection logic
- Redirect handling for unauthorized access
- Session validation

### 3. Breadcrumb Navigation Widget
**File**: `lib/src/features/admin_portal/presentation/widgets/breadcrumb_navigation_widget.dart`

**Requirements**:
- Dynamic breadcrumb generation
- Clickable navigation elements
- Current page highlighting
- Responsive design adaptation
- Integration with route hierarchy

### 4. Admin Navigation Controller
**File**: `lib/src/features/admin_portal/application/controllers/admin_navigation_controller.dart`

**Requirements**:
- Navigation state management
- Route history tracking
- Deep link handling
- Navigation analytics
- Error handling for invalid routes

### 5. Route Models and Constants
**File**: `lib/src/features/admin_portal/data/models/admin_route_models.dart`

**Requirements**:
- Route definition models
- Navigation parameter models
- Permission requirement models
- Route metadata structures

## Implementation Details

### Admin Route Structure
```dart
class AdminRoutes {
  static const String dashboard = '/admin/dashboard';
  static const String userManagement = '/admin/users';
  static const String userDetail = '/admin/users/:userId';
  static const String contentManagement = '/admin/content';
  static const String blogManagement = '/admin/content/blogs';
  static const String podcastManagement = '/admin/content/podcasts';
  static const String analytics = '/admin/analytics';
  static const String notifications = '/admin/notifications';
  static const String auditLogs = '/admin/audit-logs';
  static const String settings = '/admin/settings';
  static const String systemHealth = '/admin/system-health';
}
```

### Route Guard Implementation
```dart
class AdminRouteGuard {
  static Future<bool> canActivate(
    String route,
    AdminUser? currentUser,
  ) async {
    // Check authentication
    if (currentUser == null) return false;
    
    // Check admin role
    if (currentUser.userType != 'admin') return false;
    
    // Check specific permissions
    return _hasPermissionForRoute(route, currentUser);
  }
  
  static bool _hasPermissionForRoute(String route, AdminUser user) {
    switch (route) {
      case AdminRoutes.userManagement:
        return user.hasPermission('user_management');
      case AdminRoutes.contentManagement:
        return user.hasPermission('content_management');
      case AdminRoutes.systemHealth:
        return user.hasPermission('system_admin');
      default:
        return true; // Basic admin access
    }
  }
}
```

### Navigation Controller
```dart
class AdminNavigationController extends StateNotifier<AdminNavigationState> {
  AdminNavigationController() : super(const AdminNavigationState());
  
  void navigateTo(String route, {Map<String, String>? parameters}) {
    // Validate route and permissions
    // Update navigation state
    // Handle deep linking
    // Track navigation analytics
  }
  
  void goBack() {
    // Handle back navigation
    // Update breadcrumb state
  }
  
  void updateBreadcrumbs(String currentRoute) {
    // Generate breadcrumb trail
    // Update navigation state
  }
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] All admin pages accessible via defined routes
- [ ] Authentication guards prevent unauthorized access
- [ ] Permission-based route protection implemented
- [ ] Deep linking works for all admin pages
- [ ] Breadcrumb navigation shows current location
- [ ] Back navigation functions correctly
- [ ] Route parameters passed and validated properly

### Security Requirements
- [ ] Admin authentication verified on all routes
- [ ] Permission checks enforced for sensitive pages
- [ ] Unauthorized access redirects to login
- [ ] Session validation on route changes
- [ ] Audit logging for navigation events

### User Experience Requirements
- [ ] Smooth navigation transitions
- [ ] Loading states during route changes
- [ ] Error handling for invalid routes
- [ ] Responsive navigation on all devices
- [ ] Intuitive breadcrumb navigation

### Performance Requirements
- [ ] Route changes complete within 1 second
- [ ] Efficient route guard execution
- [ ] Minimal impact on app performance
- [ ] Proper memory management for navigation state

## Code Examples

### Admin Routes Configuration
```dart
class AdminRoutes {
  static final routes = <String, WidgetBuilder>{
    dashboard: (context) => const AdminDashboardPage(),
    userManagement: (context) => const UserManagementPage(),
    contentManagement: (context) => const ContentManagementPage(),
    analytics: (context) => const AnalyticsPage(),
    auditLogs: (context) => const AuditLogPage(),
    settings: (context) => const AdminSettingsPage(),
  };
  
  static final routeGuards = <String, List<String>>{
    userManagement: ['user_management'],
    contentManagement: ['content_management'],
    analytics: ['analytics_access'],
    settings: ['system_admin'],
  };
  
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final routeName = settings.name;
    final arguments = settings.arguments;
    
    // Check if route exists
    if (!routes.containsKey(routeName)) {
      return _errorRoute('Route not found: $routeName');
    }
    
    // Apply route guards
    return MaterialPageRoute(
      builder: (context) => AdminRouteGuardWrapper(
        route: routeName!,
        child: routes[routeName]!(context),
      ),
      settings: settings,
    );
  }
  
  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder: (context) => AdminErrorPage(message: message),
    );
  }
}
```

### Route Guard Wrapper
```dart
class AdminRouteGuardWrapper extends ConsumerWidget {
  final String route;
  final Widget child;
  
  const AdminRouteGuardWrapper({
    required this.route,
    required this.child,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(adminAuthProvider);
    
    return authState.when(
      data: (user) {
        if (user == null) {
          // Redirect to login
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(context).pushReplacementNamed('/admin/login');
          });
          return const AdminLoadingPage();
        }
        
        if (!AdminRouteGuard.hasPermissionForRoute(route, user)) {
          return const AdminUnauthorizedPage();
        }
        
        // Log navigation for audit
        ref.read(adminAuditServiceProvider).logAdminAction(
          action: 'navigate_to_page',
          adminId: user.id,
          details: {'route': route},
        );
        
        return child;
      },
      loading: () => const AdminLoadingPage(),
      error: (error, stack) => AdminErrorPage(message: error.toString()),
    );
  }
}
```

### Breadcrumb Navigation Widget
```dart
class BreadcrumbNavigationWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final navigationState = ref.watch(adminNavigationProvider);
    final theme = ShadTheme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.border),
        ),
      ),
      child: Row(
        children: [
          Icon(
            LucideIcons.home,
            size: 16,
            color: theme.colorScheme.mutedForeground,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildBreadcrumbTrail(navigationState.breadcrumbs),
          ),
        ],
      ),
    );
  }
  
  Widget _buildBreadcrumbTrail(List<BreadcrumbItem> breadcrumbs) {
    return Wrap(
      children: breadcrumbs.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final isLast = index == breadcrumbs.length - 1;
        
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (index > 0) ...[
              Icon(
                LucideIcons.chevronRight,
                size: 14,
                color: ShadTheme.of(context).colorScheme.mutedForeground,
              ),
              const SizedBox(width: 8),
            ],
            isLast
                ? Text(
                    item.title,
                    style: ShadTheme.of(context).textTheme.small.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  )
                : TextButton(
                    onPressed: () => _navigateToBreadcrumb(item),
                    child: Text(
                      item.title,
                      style: ShadTheme.of(context).textTheme.small.copyWith(
                        color: ShadTheme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
            const SizedBox(width: 8),
          ],
        );
      }).toList(),
    );
  }
}
```

### Navigation State Management
```dart
@freezed
class AdminNavigationState with _$AdminNavigationState {
  const factory AdminNavigationState({
    @Default('') String currentRoute,
    @Default([]) List<BreadcrumbItem> breadcrumbs,
    @Default([]) List<String> navigationHistory,
    @Default(false) bool isLoading,
    String? error,
  }) = _AdminNavigationState;
}

class BreadcrumbItem {
  final String title;
  final String route;
  final Map<String, String>? parameters;
  
  const BreadcrumbItem({
    required this.title,
    required this.route,
    this.parameters,
  });
}
```

## Testing Requirements

### Unit Tests
- Route guard logic
- Navigation controller methods
- Breadcrumb generation
- Parameter validation

### Widget Tests
- Route guard wrapper functionality
- Breadcrumb navigation rendering
- Navigation state updates
- Error page display

### Integration Tests
- End-to-end navigation flows
- Authentication integration
- Permission checking
- Deep linking functionality

### Manual Testing
- Cross-device navigation testing
- Back button functionality
- Breadcrumb navigation
- Route parameter handling

## Integration Points

### App Router Integration
- Extend existing app routing configuration
- Maintain consistency with other portal routing
- Handle route conflicts and priorities
- Support for nested routing

### Authentication Integration
- Connect with admin authentication system
- Handle session expiration during navigation
- Redirect flows for unauthorized access
- Permission-based menu filtering

### Analytics Integration
- Track navigation patterns
- Monitor route performance
- Identify popular admin features
- Error tracking for navigation issues

## Security Considerations

### Route Protection
- Server-side permission validation
- Client-side guards as UX enhancement
- Audit logging for all navigation
- Protection against route manipulation

### Deep Link Security
- Validate all route parameters
- Prevent unauthorized deep link access
- Sanitize URL parameters
- Rate limiting for route access

## Next Steps
Upon completion, proceed to Task 08: Error Handling and Loading States to implement comprehensive error management and user feedback systems.
