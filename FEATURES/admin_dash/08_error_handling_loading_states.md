# Task 08: Error Handling and Loading States

## Objective
Implement comprehensive error handling and loading state management throughout the admin portal, providing consistent user feedback, graceful error recovery, and professional loading experiences following existing 3Pay Global patterns.

## Dependencies
- Task 01: Admin Authentication Setup
- Task 02: Basic Dashboard Structure
- Task 07: Navigation and Routing

## Estimated Time
2-3 days

## Deliverables

### 1. Admin Error Handling Service
**File**: `lib/src/features/admin_portal/data/services/admin_error_handling_service.dart`

**Requirements**:
- Centralized error processing and categorization
- User-friendly error message mapping
- Error reporting and logging
- Recovery action suggestions
- Integration with existing error patterns

### 2. Admin Loading State Manager
**File**: `lib/src/features/admin_portal/application/providers/admin_loading_state_provider.dart`

**Requirements**:
- Global loading state management
- Operation-specific loading indicators
- Progress tracking for long operations
- Loading state coordination across providers

### 3. Error Display Widgets
**File**: `lib/src/features/admin_portal/presentation/widgets/admin_error_widgets.dart`

**Requirements**:
- Consistent error display components
- Error page templates
- Inline error indicators
- Recovery action buttons
- Responsive error layouts

### 4. Loading State Widgets
**File**: `lib/src/features/admin_portal/presentation/widgets/admin_loading_widgets.dart`

**Requirements**:
- Skeleton loading components
- Progress indicators
- Loading overlays
- Operation-specific loading states
- Responsive loading designs

### 5. Error Recovery System
**File**: `lib/src/features/admin_portal/data/services/error_recovery_service.dart`

**Requirements**:
- Automatic retry mechanisms
- Manual recovery actions
- State restoration after errors
- Graceful degradation strategies

## Implementation Details

### Error Categories
```dart
enum AdminErrorType {
  authentication,
  authorization,
  network,
  validation,
  serverError,
  notFound,
  rateLimit,
  maintenance,
}

enum AdminErrorSeverity {
  info,
  warning,
  error,
  critical,
}
```

### Loading State Types
```dart
enum AdminLoadingType {
  initial,
  refresh,
  pagination,
  operation,
  background,
}

enum AdminOperationType {
  userManagement,
  contentManagement,
  dataExport,
  bulkOperation,
  systemOperation,
}
```

### Error Handling Service Structure
```dart
class AdminErrorHandlingService {
  static AdminError processError(dynamic error) {
    // Categorize and map errors
    // Generate user-friendly messages
    // Determine recovery actions
    // Log error details
  }
  
  static List<RecoveryAction> getRecoveryActions(AdminErrorType type) {
    // Return appropriate recovery actions
  }
  
  static Future<void> reportError(AdminError error) {
    // Report to logging service
    // Send to error tracking if configured
  }
}
```

## Acceptance Criteria

### Error Handling Requirements
- [ ] All API errors caught and processed consistently
- [ ] User-friendly error messages displayed
- [ ] Appropriate recovery actions suggested
- [ ] Error logging for debugging and monitoring
- [ ] Graceful degradation for non-critical failures
- [ ] Network error handling with retry options
- [ ] Authentication error handling with re-login flow

### Loading State Requirements
- [ ] Loading indicators for all async operations
- [ ] Skeleton loading for data-heavy components
- [ ] Progress indicators for long-running operations
- [ ] Non-blocking loading for background operations
- [ ] Consistent loading design across all components
- [ ] Responsive loading states for all screen sizes

### User Experience Requirements
- [ ] Clear feedback for all user actions
- [ ] No hanging states without feedback
- [ ] Intuitive error recovery flows
- [ ] Accessible error and loading states
- [ ] Consistent visual language for states

### Performance Requirements
- [ ] Loading states appear within 100ms
- [ ] Error handling doesn't impact performance
- [ ] Efficient skeleton loading rendering
- [ ] Minimal memory usage for state management

## Code Examples

### Admin Error Handling Service
```dart
class AdminErrorHandlingService {
  static AdminError processError(dynamic error) {
    AdminErrorType type;
    String userMessage;
    List<RecoveryAction> recoveryActions;
    
    if (error is PocketBaseException) {
      switch (error.statusCode) {
        case 401:
          type = AdminErrorType.authentication;
          userMessage = 'Your session has expired. Please log in again.';
          recoveryActions = [RecoveryAction.reLogin];
          break;
        case 403:
          type = AdminErrorType.authorization;
          userMessage = 'You don\'t have permission to perform this action.';
          recoveryActions = [RecoveryAction.contactSupport];
          break;
        case 404:
          type = AdminErrorType.notFound;
          userMessage = 'The requested resource was not found.';
          recoveryActions = [RecoveryAction.goBack, RecoveryAction.refresh];
          break;
        case 429:
          type = AdminErrorType.rateLimit;
          userMessage = 'Too many requests. Please wait a moment and try again.';
          recoveryActions = [RecoveryAction.waitAndRetry];
          break;
        default:
          type = AdminErrorType.serverError;
          userMessage = 'A server error occurred. Please try again.';
          recoveryActions = [RecoveryAction.retry, RecoveryAction.refresh];
      }
    } else if (error is SocketException) {
      type = AdminErrorType.network;
      userMessage = 'Network connection error. Please check your internet connection.';
      recoveryActions = [RecoveryAction.retry, RecoveryAction.checkConnection];
    } else {
      type = AdminErrorType.serverError;
      userMessage = 'An unexpected error occurred. Please try again.';
      recoveryActions = [RecoveryAction.retry, RecoveryAction.refresh];
    }
    
    final adminError = AdminError(
      type: type,
      message: userMessage,
      originalError: error,
      recoveryActions: recoveryActions,
      timestamp: DateTime.now(),
    );
    
    // Log error
    LoggerService.error('Admin error processed', error);
    
    return adminError;
  }
}
```

### Loading State Provider
```dart
final adminLoadingStateProvider = StateNotifierProvider<AdminLoadingStateNotifier, AdminLoadingState>((ref) {
  return AdminLoadingStateNotifier();
});

class AdminLoadingStateNotifier extends StateNotifier<AdminLoadingState> {
  AdminLoadingStateNotifier() : super(const AdminLoadingState());
  
  void setLoading(String operationId, AdminLoadingType type, {String? message}) {
    final operations = Map<String, LoadingOperation>.from(state.operations);
    operations[operationId] = LoadingOperation(
      id: operationId,
      type: type,
      message: message,
      startTime: DateTime.now(),
    );
    
    state = state.copyWith(
      operations: operations,
      isLoading: operations.isNotEmpty,
    );
  }
  
  void setProgress(String operationId, double progress, {String? message}) {
    final operations = Map<String, LoadingOperation>.from(state.operations);
    if (operations.containsKey(operationId)) {
      operations[operationId] = operations[operationId]!.copyWith(
        progress: progress,
        message: message,
      );
      state = state.copyWith(operations: operations);
    }
  }
  
  void clearLoading(String operationId) {
    final operations = Map<String, LoadingOperation>.from(state.operations);
    operations.remove(operationId);
    
    state = state.copyWith(
      operations: operations,
      isLoading: operations.isNotEmpty,
    );
  }
}
```

### Error Display Widget
```dart
class AdminErrorWidget extends StatelessWidget {
  final AdminError error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  
  const AdminErrorWidget({
    required this.error,
    this.onRetry,
    this.onDismiss,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getErrorIcon(),
              size: 48,
              color: _getErrorColor(),
            ),
            const SizedBox(height: 16),
            Text(
              error.message,
              style: theme.textTheme.p,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Wrap(
              spacing: 12,
              children: error.recoveryActions.map((action) {
                return ShadButton(
                  onPressed: () => _handleRecoveryAction(context, action),
                  child: Text(_getActionLabel(action)),
                );
              }).toList(),
            ),
            if (onDismiss != null) ...[
              const SizedBox(height: 12),
              TextButton(
                onPressed: onDismiss,
                child: const Text('Dismiss'),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  IconData _getErrorIcon() {
    switch (error.type) {
      case AdminErrorType.network:
        return LucideIcons.wifiOff;
      case AdminErrorType.authentication:
        return LucideIcons.lock;
      case AdminErrorType.authorization:
        return LucideIcons.shield;
      case AdminErrorType.notFound:
        return LucideIcons.search;
      default:
        return LucideIcons.alertCircle;
    }
  }
  
  Color _getErrorColor() {
    switch (error.type) {
      case AdminErrorType.authentication:
      case AdminErrorType.authorization:
        return Colors.orange;
      case AdminErrorType.network:
        return Colors.blue;
      default:
        return Colors.red;
    }
  }
}
```

### Loading Skeleton Widget
```dart
class AdminLoadingSkeletonWidget extends StatelessWidget {
  final AdminLoadingType type;
  final int itemCount;
  
  const AdminLoadingSkeletonWidget({
    required this.type,
    this.itemCount = 5,
  });
  
  @override
  Widget build(BuildContext context) {
    switch (type) {
      case AdminLoadingType.initial:
        return _buildDashboardSkeleton();
      case AdminLoadingType.refresh:
        return _buildRefreshSkeleton();
      default:
        return _buildGenericSkeleton();
    }
  }
  
  Widget _buildDashboardSkeleton() {
    return Column(
      children: [
        // KPI cards skeleton
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 4,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: List.generate(4, (index) => const CustomSkeletonWidget()),
        ),
        const SizedBox(height: 32),
        // Activity feed skeleton
        ...List.generate(
          itemCount,
          (index) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                const CustomSkeletonWidget(width: 40, height: 40),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomSkeletonWidget(
                        width: double.infinity,
                        height: 16,
                      ),
                      const SizedBox(height: 4),
                      CustomSkeletonWidget(
                        width: 200,
                        height: 12,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
```

### Progress Indicator Widget
```dart
class AdminProgressIndicator extends ConsumerWidget {
  final String operationId;
  final String? title;
  
  const AdminProgressIndicator({
    required this.operationId,
    this.title,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final loadingState = ref.watch(adminLoadingStateProvider);
    final operation = loadingState.operations[operationId];
    
    if (operation == null) return const SizedBox.shrink();
    
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title != null) ...[
              Text(
                title!,
                style: ShadTheme.of(context).textTheme.h4,
              ),
              const SizedBox(height: 12),
            ],
            if (operation.progress != null) ...[
              LinearProgressIndicator(value: operation.progress),
              const SizedBox(height: 8),
              Text('${(operation.progress! * 100).toInt()}%'),
            ] else ...[
              const CircularProgressIndicator(),
            ],
            if (operation.message != null) ...[
              const SizedBox(height: 8),
              Text(
                operation.message!,
                style: ShadTheme.of(context).textTheme.small,
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
```

## Testing Requirements

### Unit Tests
- Error processing and categorization
- Loading state management
- Recovery action handling
- Error message generation

### Widget Tests
- Error widget rendering
- Loading skeleton display
- Progress indicator functionality
- Recovery action buttons

### Integration Tests
- End-to-end error handling flows
- Loading state coordination
- Error recovery mechanisms
- User feedback systems

### Error Simulation Tests
- Network error scenarios
- Authentication failures
- Server error responses
- Rate limiting situations

## Integration Points

### Existing Services
- Integrate with PocketBaseService error handling
- Use existing LoggerService for error logging
- Connect with notification service for error alerts
- Maintain consistency with other portal error patterns

### Provider Integration
- Coordinate with all admin providers
- Handle loading states across operations
- Manage error states in data providers
- Synchronize loading indicators

## Next Steps
Upon completion, proceed to Task 09: Dashboard KPIs and Metrics to implement comprehensive platform analytics and monitoring capabilities.
