# Task 11: Content Management System

## Objective
Implement comprehensive content management system for the admin portal, enabling creation, editing, and management of blog posts, educational content, podcasts, and platform announcements with rich text editing, media management, and publication workflows.

## Dependencies
- Task 02: Basic Dashboard Structure
- Task 04: Audit Logging System
- Task 08: Error Handling and Loading States

## Estimated Time
4-5 days

## Deliverables

### 1. Content Management Service
**File**: `lib/src/features/admin_portal/data/services/content_management_service.dart`

**Requirements**:
- CRUD operations for all content types
- File upload and media management
- Content versioning and drafts
- Publication workflow management
- Integration with existing content collections

### 2. Content Management Provider
**File**: `lib/src/features/admin_portal/application/providers/content_management_provider.dart`

**Requirements**:
- State management for content operations
- Draft auto-saving functionality
- Media upload progress tracking
- Content search and filtering
- Publication status management

### 3. Content Management Dashboard
**File**: `lib/src/features/admin_portal/presentation/pages/content_management_page.dart`

**Requirements**:
- Overview of all content types
- Quick access to create new content
- Content status monitoring
- Search and filter capabilities
- Bulk content operations

### 4. Blog Post Editor
**File**: `lib/src/features/admin_portal/presentation/pages/blog_editor_page.dart`

**Requirements**:
- Rich text editor with formatting options
- Image and media insertion
- SEO metadata management
- Preview functionality
- Draft saving and publishing

### 5. Content Models and Types
**File**: `lib/src/features/admin_portal/data/models/content_models.dart`

**Requirements**:
- Blog post models
- Educational content models
- Podcast content models
- Announcement models
- Media asset models

## Implementation Details

### Content Types
```dart
enum ContentType {
  blogPost,
  educationalContent,
  podcast,
  announcement,
  newsletter,
}

enum ContentStatus {
  draft,
  review,
  published,
  archived,
  scheduled,
}

enum ContentCategory {
  litigation,
  funding,
  legal,
  business,
  technology,
  news,
}
```

### Rich Text Editor Features
```dart
enum EditorFeature {
  bold,
  italic,
  underline,
  heading,
  bulletList,
  numberedList,
  link,
  image,
  video,
  quote,
  code,
  table,
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] Create, edit, and delete all content types
- [ ] Rich text editor with comprehensive formatting options
- [ ] Image and media upload with optimization
- [ ] Draft auto-saving every 30 seconds
- [ ] Content preview before publishing
- [ ] SEO metadata management (title, description, keywords)
- [ ] Content scheduling for future publication
- [ ] Content versioning and revision history

### Content Management Requirements
- [ ] Search and filter content by type, status, and category
- [ ] Bulk operations for content management
- [ ] Content analytics and performance metrics
- [ ] Tag and category management
- [ ] Content approval workflow for team environments
- [ ] Content duplication and templating

### Media Management Requirements
- [ ] Image upload with automatic resizing
- [ ] Video and audio file support
- [ ] Media library with organization
- [ ] Alt text and accessibility features
- [ ] File size optimization and compression

### SEO and Publishing Requirements
- [ ] URL slug generation and customization
- [ ] Meta description and keywords
- [ ] Social media preview generation
- [ ] Publication date scheduling
- [ ] Content visibility controls

## Code Examples

### Content Management Service
```dart
class ContentManagementService extends PocketBaseService {
  Future<BlogPost> createBlogPost({
    required String title,
    required String content,
    required String authorId,
    String? excerpt,
    List<String>? tags,
    String? featuredImage,
    ContentStatus status = ContentStatus.draft,
  }) async {
    try {
      final slug = _generateSlug(title);
      
      final data = {
        'title': title,
        'content': content,
        'excerpt': excerpt ?? _generateExcerpt(content),
        'slug': slug,
        'author_id': authorId,
        'tags': tags ?? [],
        'featured_image': featuredImage,
        'status': status.name,
        'content_type': ContentType.blogPost.name,
        'seo_title': title,
        'seo_description': excerpt ?? _generateExcerpt(content),
      };
      
      final record = await createRecord(
        collectionName: 'content_items',
        data: data,
      );
      
      // Log content creation
      await logAdminActivity('create_blog_post', {
        'content_id': record.id,
        'title': title,
        'status': status.name,
      });
      
      return BlogPost.fromRecord(record);
    } catch (e) {
      LoggerService.error('Error creating blog post', e);
      rethrow;
    }
  }
  
  Future<BlogPost> updateBlogPost({
    required String id,
    String? title,
    String? content,
    String? excerpt,
    List<String>? tags,
    String? featuredImage,
    ContentStatus? status,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      
      if (title != null) {
        updateData['title'] = title;
        updateData['slug'] = _generateSlug(title);
        updateData['seo_title'] = title;
      }
      if (content != null) {
        updateData['content'] = content;
        updateData['excerpt'] = excerpt ?? _generateExcerpt(content);
        updateData['seo_description'] = excerpt ?? _generateExcerpt(content);
      }
      if (tags != null) updateData['tags'] = tags;
      if (featuredImage != null) updateData['featured_image'] = featuredImage;
      if (status != null) {
        updateData['status'] = status.name;
        if (status == ContentStatus.published) {
          updateData['published_at'] = DateTime.now().toIso8601String();
        }
      }
      
      final record = await updateRecord(
        collectionName: 'content_items',
        recordId: id,
        data: updateData,
      );
      
      // Log content update
      await logAdminActivity('update_blog_post', {
        'content_id': id,
        'changes': updateData.keys.toList(),
      });
      
      return BlogPost.fromRecord(record);
    } catch (e) {
      LoggerService.error('Error updating blog post', e);
      rethrow;
    }
  }
  
  Future<String> uploadContentMedia({
    required http.MultipartFile file,
    required String contentId,
    String? altText,
  }) async {
    try {
      final result = await uploadFileAndGetId(
        targetCollectionName: 'content_media',
        multipartFile: file,
        body: {
          'content_id': contentId,
          'alt_text': altText ?? '',
          'file_type': _getFileType(file.filename ?? ''),
        },
      );
      
      return result['id']!;
    } catch (e) {
      LoggerService.error('Error uploading content media', e);
      rethrow;
    }
  }
  
  String _generateSlug(String title) {
    return title
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s]'), '')
        .replaceAll(RegExp(r'\s+'), '-')
        .trim();
  }
  
  String _generateExcerpt(String content, {int maxLength = 160}) {
    final plainText = content.replaceAll(RegExp(r'<[^>]*>'), '');
    return plainText.length > maxLength
        ? '${plainText.substring(0, maxLength)}...'
        : plainText;
  }
}
```

### Blog Editor Page
```dart
class BlogEditorPage extends ConsumerStatefulWidget {
  final String? blogId; // null for new blog post
  static const String routeName = '/admin/content/blog-editor';
  
  const BlogEditorPage({this.blogId});
  
  @override
  ConsumerState<BlogEditorPage> createState() => _BlogEditorPageState();
}

class _BlogEditorPageState extends ConsumerState<BlogEditorPage> {
  final _formKey = GlobalKey<ShadFormState>();
  final _titleController = TextEditingController();
  final _excerptController = TextEditingController();
  final _contentController = TextEditingController();
  final _seoTitleController = TextEditingController();
  final _seoDescriptionController = TextEditingController();
  
  List<String> _selectedTags = [];
  String? _featuredImageId;
  ContentStatus _status = ContentStatus.draft;
  Timer? _autoSaveTimer;
  
  @override
  void initState() {
    super.initState();
    _setupAutoSave();
    if (widget.blogId != null) {
      _loadExistingBlogPost();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.blogId == null ? 'Create Blog Post' : 'Edit Blog Post'),
        actions: [
          _buildPreviewButton(),
          _buildSaveButton(),
          _buildPublishButton(),
        ],
      ),
      body: ShadForm(
        key: _formKey,
        child: isDesktop ? _buildDesktopLayout() : _buildMobileLayout(),
      ),
    );
  }
  
  Widget _buildDesktopLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main editor area
        Expanded(
          flex: 2,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: _buildMainEditorContent(),
          ),
        ),
        
        // Sidebar with metadata and settings
        SizedBox(
          width: 300,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: _buildEditorSidebar(),
          ),
        ),
      ],
    );
  }
  
  Widget _buildMainEditorContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title input
        ShadInputFormField(
          id: 'title',
          controller: _titleController,
          label: const Text('Title'),
          placeholder: const Text('Enter blog post title'),
          validator: (value) => value?.isEmpty == true ? 'Title is required' : null,
          onChanged: (_) => _scheduleAutoSave(),
        ),
        const SizedBox(height: 24),
        
        // Excerpt input
        ShadInputFormField(
          id: 'excerpt',
          controller: _excerptController,
          label: const Text('Excerpt'),
          placeholder: const Text('Brief description of the blog post'),
          maxLines: 3,
          onChanged: (_) => _scheduleAutoSave(),
        ),
        const SizedBox(height: 24),
        
        // Featured image section
        _buildFeaturedImageSection(),
        const SizedBox(height: 24),
        
        // Rich text editor
        _buildRichTextEditor(),
      ],
    );
  }
  
  Widget _buildRichTextEditor() {
    return ShadCard(
      title: const Text('Content'),
      child: SizedBox(
        height: 500,
        child: Column(
          children: [
            // Editor toolbar
            _buildEditorToolbar(),
            const Divider(height: 1),
            
            // Editor content area
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: _contentController,
                  maxLines: null,
                  expands: true,
                  decoration: const InputDecoration(
                    hintText: 'Start writing your blog post...',
                    border: InputBorder.none,
                  ),
                  style: const TextStyle(fontSize: 16, height: 1.5),
                  onChanged: (_) => _scheduleAutoSave(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildEditorToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Wrap(
        spacing: 8,
        children: [
          _buildToolbarButton(Icons.format_bold, 'Bold', () => _insertFormatting('**', '**')),
          _buildToolbarButton(Icons.format_italic, 'Italic', () => _insertFormatting('*', '*')),
          _buildToolbarButton(Icons.format_underlined, 'Underline', () => _insertFormatting('<u>', '</u>')),
          const VerticalDivider(),
          _buildToolbarButton(Icons.format_list_bulleted, 'Bullet List', () => _insertFormatting('\n- ', '')),
          _buildToolbarButton(Icons.format_list_numbered, 'Numbered List', () => _insertFormatting('\n1. ', '')),
          const VerticalDivider(),
          _buildToolbarButton(Icons.link, 'Link', _insertLink),
          _buildToolbarButton(Icons.image, 'Image', _insertImage),
          _buildToolbarButton(Icons.format_quote, 'Quote', () => _insertFormatting('\n> ', '')),
        ],
      ),
    );
  }
  
  Widget _buildToolbarButton(IconData icon, String tooltip, VoidCallback onPressed) {
    return Tooltip(
      message: tooltip,
      child: IconButton(
        icon: Icon(icon, size: 18),
        onPressed: onPressed,
        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
      ),
    );
  }
  
  void _insertFormatting(String before, String after) {
    final text = _contentController.text;
    final selection = _contentController.selection;
    
    if (selection.isValid) {
      final selectedText = text.substring(selection.start, selection.end);
      final newText = text.replaceRange(
        selection.start,
        selection.end,
        '$before$selectedText$after',
      );
      
      _contentController.text = newText;
      _contentController.selection = TextSelection.collapsed(
        offset: selection.start + before.length + selectedText.length + after.length,
      );
    }
  }
  
  void _setupAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      if (_hasUnsavedChanges()) {
        _saveDraft();
      }
    });
  }
  
  void _scheduleAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer(const Duration(seconds: 5), () {
      if (_hasUnsavedChanges()) {
        _saveDraft();
      }
    });
  }
}
```

### Content Management Dashboard
```dart
class ContentManagementPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin/content';
  
  @override
  ConsumerState<ContentManagementPage> createState() => _ContentManagementPageState();
}

class _ContentManagementPageState extends ConsumerState<ContentManagementPage> {
  ContentType? _selectedType;
  ContentStatus? _selectedStatus;
  String _searchQuery = '';
  
  @override
  Widget build(BuildContext context) {
    final contentAsync = ref.watch(contentManagementProvider);
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Content Management'),
        actions: [
          _buildCreateContentButton(),
          _buildBulkActionsButton(),
        ],
      ),
      body: Column(
        children: [
          _buildFiltersAndSearch(),
          Expanded(
            child: contentAsync.when(
              data: (content) => _buildContentGrid(content, isDesktop),
              loading: () => const ContentManagementLoadingWidget(),
              error: (error, stack) => ContentManagementErrorWidget(error: error),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildContentGrid(List<ContentItem> content, bool isDesktop) {
    final crossAxisCount = isDesktop ? 3 : (AdminResponsiveLayout.isTablet(context) ? 2 : 1);
    
    return GridView.builder(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: isDesktop ? 1.2 : 1.5,
      ),
      itemCount: content.length,
      itemBuilder: (context, index) {
        final item = content[index];
        return ContentItemCard(
          item: item,
          onEdit: () => _editContent(item),
          onDelete: () => _deleteContent(item),
          onDuplicate: () => _duplicateContent(item),
        );
      },
    );
  }
}
```

## Testing Requirements

### Unit Tests
- Content management service methods
- Rich text editor functionality
- Auto-save mechanisms
- SEO metadata generation

### Widget Tests
- Blog editor page rendering
- Content management dashboard
- Rich text editor toolbar
- Media upload components

### Integration Tests
- End-to-end content creation flow
- Auto-save functionality
- Media upload and management
- Content publication workflow

### Content Tests
- Rich text formatting preservation
- Image optimization and resizing
- SEO metadata validation
- Content search and filtering

## Next Steps
Upon completion, proceed to Task 12: Notification Management System to implement comprehensive notification creation and targeting capabilities.
