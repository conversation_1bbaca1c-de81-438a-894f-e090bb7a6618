# Task 10: User Activity Monitoring

## Objective
Implement comprehensive user activity monitoring system for the admin portal, providing detailed insights into user behavior, engagement patterns, and platform usage across all user types with real-time tracking and historical analysis.

## Dependencies
- Task 04: Audit Logging System
- Task 09: Dashboard KPIs and Metrics
- Task 06: Real-time Updates System

## Estimated Time
3-4 days

## Deliverables

### 1. User Activity Monitoring Service
**File**: `lib/src/features/admin_portal/data/services/user_activity_monitoring_service.dart`

**Requirements**:
- Real-time user activity tracking
- Session monitoring and analytics
- Behavior pattern analysis
- Activity aggregation and reporting
- Integration with existing audit logging

### 2. Activity Monitoring Provider
**File**: `lib/src/features/admin_portal/application/providers/activity_monitoring_provider.dart`

**Requirements**:
- State management for activity data
- Real-time activity updates
- Filter and search capabilities
- Performance optimization with caching
- Error handling for monitoring failures

### 3. User Activity Dashboard Page
**File**: `lib/src/features/admin_portal/presentation/pages/user_activity_dashboard_page.dart`

**Requirements**:
- Comprehensive activity overview
- User-specific activity details
- Activity timeline visualization
- Search and filtering interface
- Export functionality for reports

### 4. Activity Visualization Widgets
**File**: `lib/src/features/admin_portal/presentation/widgets/activity_visualization_widgets.dart`

**Requirements**:
- Activity timeline components
- Heatmap visualizations
- User journey mapping
- Engagement metrics display
- Interactive activity charts

### 5. Activity Models and Analytics
**File**: `lib/src/features/admin_portal/data/models/user_activity_models.dart`

**Requirements**:
- Activity event models
- User session models
- Engagement metrics models
- Activity pattern models

## Implementation Details

### Activity Event Types
```dart
enum UserActivityType {
  login,
  logout,
  pageView,
  documentUpload,
  documentDownload,
  formSubmission,
  searchQuery,
  profileUpdate,
  claimSubmission,
  investmentAction,
  communicationSent,
  notificationRead,
}
```

### Activity Monitoring Scope
```dart
enum ActivityScope {
  allUsers,
  solicitors,
  coFunders,
  claimants,
  specificUser,
}
```

### Engagement Metrics
```dart
enum EngagementMetric {
  sessionDuration,
  pageViews,
  actionsPerSession,
  returnVisitRate,
  featureUsage,
  documentInteractions,
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] Track all user activities across the platform
- [ ] Real-time activity monitoring with live updates
- [ ] Historical activity analysis and reporting
- [ ] User session tracking and analytics
- [ ] Activity pattern recognition and insights
- [ ] Search and filter activities by user, type, and date
- [ ] Export activity reports for compliance

### Performance Requirements
- [ ] Activity dashboard loads within 3 seconds
- [ ] Real-time updates appear within 2 seconds
- [ ] Efficient handling of large activity datasets
- [ ] Optimized queries for activity aggregation
- [ ] Minimal impact on user experience

### Privacy Requirements
- [ ] Compliance with data protection regulations
- [ ] Appropriate data anonymization where required
- [ ] Secure storage of activity data
- [ ] Access control for sensitive activity information

### Visualization Requirements
- [ ] Clear and intuitive activity timelines
- [ ] Interactive charts and heatmaps
- [ ] Responsive design for all screen sizes
- [ ] Professional visualization with 3Pay branding

## Code Examples

### User Activity Monitoring Service
```dart
class UserActivityMonitoringService extends PocketBaseService {
  Future<List<UserActivityEvent>> getUserActivities({
    String? userId,
    UserActivityType? activityType,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int perPage = 50,
  }) async {
    try {
      String filter = _buildActivityFilter(
        userId: userId,
        activityType: activityType,
        startDate: startDate,
        endDate: endDate,
      );
      
      final records = await getList(
        collectionName: 'user_activity_logs',
        page: page,
        perPage: perPage,
        filter: filter,
        sort: '-created',
        expand: 'user_id',
      );
      
      return records.items
          .map((record) => UserActivityEvent.fromRecord(record))
          .toList();
    } catch (e) {
      LoggerService.error('Error fetching user activities', e);
      rethrow;
    }
  }
  
  Future<UserEngagementMetrics> getUserEngagementMetrics({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final activities = await getUserActivities(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );
      
      return _calculateEngagementMetrics(activities);
    } catch (e) {
      LoggerService.error('Error calculating engagement metrics', e);
      rethrow;
    }
  }
  
  Future<Map<DateTime, int>> getActivityHeatmapData({
    UserActivityType? activityType,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final activities = await getUserActivities(
        activityType: activityType,
        startDate: startDate,
        endDate: endDate,
      );
      
      final heatmapData = <DateTime, int>{};
      
      for (final activity in activities) {
        final day = DateTime(
          activity.timestamp.year,
          activity.timestamp.month,
          activity.timestamp.day,
        );
        heatmapData[day] = (heatmapData[day] ?? 0) + 1;
      }
      
      return heatmapData;
    } catch (e) {
      LoggerService.error('Error generating heatmap data', e);
      rethrow;
    }
  }
  
  UserEngagementMetrics _calculateEngagementMetrics(List<UserActivityEvent> activities) {
    if (activities.isEmpty) {
      return const UserEngagementMetrics.empty();
    }
    
    // Calculate session duration
    final sessions = _groupActivitiesIntoSessions(activities);
    final avgSessionDuration = sessions.isNotEmpty
        ? sessions.map((s) => s.duration.inMinutes).reduce((a, b) => a + b) / sessions.length
        : 0.0;
    
    // Calculate page views
    final pageViews = activities.where((a) => a.type == UserActivityType.pageView).length;
    
    // Calculate actions per session
    final avgActionsPerSession = sessions.isNotEmpty
        ? activities.length / sessions.length
        : 0.0;
    
    // Calculate feature usage
    final featureUsage = <String, int>{};
    for (final activity in activities) {
      final feature = activity.details['feature'] as String? ?? 'unknown';
      featureUsage[feature] = (featureUsage[feature] ?? 0) + 1;
    }
    
    return UserEngagementMetrics(
      sessionDuration: avgSessionDuration,
      pageViews: pageViews,
      actionsPerSession: avgActionsPerSession,
      featureUsage: featureUsage,
      totalActivities: activities.length,
    );
  }
}
```

### User Activity Dashboard Page
```dart
class UserActivityDashboardPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin/user-activity';
  
  @override
  ConsumerState<UserActivityDashboardPage> createState() => _UserActivityDashboardPageState();
}

class _UserActivityDashboardPageState extends ConsumerState<UserActivityDashboardPage> {
  ActivityScope _selectedScope = ActivityScope.allUsers;
  UserActivityType? _selectedActivityType;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String? _selectedUserId;
  
  @override
  Widget build(BuildContext context) {
    final activitiesAsync = ref.watch(userActivityProvider(_buildActivityFilter()));
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Activity Monitoring'),
        actions: [
          _buildExportButton(),
          _buildRefreshButton(),
        ],
      ),
      body: Column(
        children: [
          _buildFiltersSection(),
          Expanded(
            child: activitiesAsync.when(
              data: (activities) => _buildActivityContent(activities, isDesktop),
              loading: () => const ActivityMonitoringLoadingWidget(),
              error: (error, stack) => ActivityMonitoringErrorWidget(error: error),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildActivityContent(List<UserActivityEvent> activities, bool isDesktop) {
    return SingleChildScrollView(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Activity Overview Cards
          _buildActivityOverviewCards(activities),
          const SizedBox(height: 32),
          
          // Activity Visualizations
          if (isDesktop)
            _buildDesktopVisualizationLayout(activities)
          else
            _buildMobileVisualizationLayout(activities),
          
          const SizedBox(height: 32),
          
          // Activity Timeline
          _buildActivityTimeline(activities),
        ],
      ),
    );
  }
  
  Widget _buildDesktopVisualizationLayout(List<UserActivityEvent> activities) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Column(
            children: [
              ActivityHeatmapWidget(
                activities: activities,
                startDate: _startDate,
                endDate: _endDate,
              ),
              const SizedBox(height: 24),
              ActivityTypeDistributionWidget(activities: activities),
            ],
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          flex: 1,
          child: Column(
            children: [
              TopActiveUsersWidget(activities: activities),
              const SizedBox(height: 24),
              ActivityTrendsWidget(activities: activities),
            ],
          ),
        ),
      ],
    );
  }
}
```

### Activity Timeline Widget
```dart
class ActivityTimelineWidget extends StatelessWidget {
  final List<UserActivityEvent> activities;
  final bool showUserInfo;
  
  const ActivityTimelineWidget({
    required this.activities,
    this.showUserInfo = true,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    
    return ShadCard(
      title: const Text('Activity Timeline'),
      child: SizedBox(
        height: 400,
        child: activities.isEmpty
            ? const Center(child: Text('No activities found'))
            : ListView.builder(
                itemCount: activities.length,
                itemBuilder: (context, index) {
                  final activity = activities[index];
                  return ActivityTimelineItem(
                    activity: activity,
                    showUserInfo: showUserInfo,
                    isFirst: index == 0,
                    isLast: index == activities.length - 1,
                  );
                },
              ),
      ),
    );
  }
}

class ActivityTimelineItem extends StatelessWidget {
  final UserActivityEvent activity;
  final bool showUserInfo;
  final bool isFirst;
  final bool isLast;
  
  const ActivityTimelineItem({
    required this.activity,
    required this.showUserInfo,
    required this.isFirst,
    required this.isLast,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator
          SizedBox(
            width: 40,
            child: Column(
              children: [
                if (!isFirst)
                  Container(
                    width: 2,
                    height: 12,
                    color: theme.colorScheme.border,
                  ),
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getActivityColor(activity.type),
                    shape: BoxShape.circle,
                  ),
                ),
                if (!isLast)
                  Expanded(
                    child: Container(
                      width: 2,
                      color: theme.colorScheme.border,
                    ),
                  ),
              ],
            ),
          ),
          
          // Activity content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 12, bottom: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getActivityIcon(activity.type),
                        size: 16,
                        color: _getActivityColor(activity.type),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _getActivityTitle(activity),
                        style: theme.textTheme.p.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        _formatTimestamp(activity.timestamp),
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  if (showUserInfo && activity.userName != null) ...[
                    Text(
                      'User: ${activity.userName}',
                      style: theme.textTheme.small.copyWith(
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                    const SizedBox(height: 4),
                  ],
                  Text(
                    _getActivityDescription(activity),
                    style: theme.textTheme.small,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Color _getActivityColor(UserActivityType type) {
    switch (type) {
      case UserActivityType.login:
        return Colors.green;
      case UserActivityType.logout:
        return Colors.orange;
      case UserActivityType.documentUpload:
        return Colors.blue;
      case UserActivityType.claimSubmission:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
  
  IconData _getActivityIcon(UserActivityType type) {
    switch (type) {
      case UserActivityType.login:
        return LucideIcons.logIn;
      case UserActivityType.logout:
        return LucideIcons.logOut;
      case UserActivityType.pageView:
        return LucideIcons.eye;
      case UserActivityType.documentUpload:
        return LucideIcons.upload;
      case UserActivityType.documentDownload:
        return LucideIcons.download;
      default:
        return LucideIcons.activity;
    }
  }
}
```

### Activity Heatmap Widget
```dart
class ActivityHeatmapWidget extends ConsumerWidget {
  final List<UserActivityEvent> activities;
  final DateTime startDate;
  final DateTime endDate;
  
  const ActivityHeatmapWidget({
    required this.activities,
    required this.startDate,
    required this.endDate,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final heatmapData = _generateHeatmapData();
    final theme = ShadTheme.of(context);
    
    return ShadCard(
      title: const Text('Activity Heatmap'),
      child: SizedBox(
        height: 200,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: _buildHeatmapGrid(heatmapData, theme),
        ),
      ),
    );
  }
  
  Map<DateTime, int> _generateHeatmapData() {
    final data = <DateTime, int>{};
    
    for (final activity in activities) {
      final day = DateTime(
        activity.timestamp.year,
        activity.timestamp.month,
        activity.timestamp.day,
      );
      data[day] = (data[day] ?? 0) + 1;
    }
    
    return data;
  }
  
  Widget _buildHeatmapGrid(Map<DateTime, int> data, ShadThemeData theme) {
    final maxValue = data.values.isEmpty ? 1 : data.values.reduce((a, b) => a > b ? a : b);
    final days = _generateDateRange(startDate, endDate);
    
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7, // Days of week
        crossAxisSpacing: 2,
        mainAxisSpacing: 2,
      ),
      itemCount: days.length,
      itemBuilder: (context, index) {
        final day = days[index];
        final value = data[day] ?? 0;
        final intensity = value / maxValue;
        
        return Tooltip(
          message: '${_formatDate(day)}: $value activities',
          child: Container(
            decoration: BoxDecoration(
              color: _getHeatmapColor(intensity),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        );
      },
    );
  }
  
  Color _getHeatmapColor(double intensity) {
    if (intensity == 0) return Colors.grey.shade200;
    return Colors.blue.withOpacity(0.2 + (intensity * 0.8));
  }
}
```

## Testing Requirements

### Unit Tests
- Activity monitoring service methods
- Engagement metrics calculations
- Activity filtering and aggregation
- Heatmap data generation

### Widget Tests
- Activity dashboard rendering
- Timeline widget functionality
- Heatmap visualization
- Filter controls

### Integration Tests
- End-to-end activity tracking
- Real-time activity updates
- Data export functionality
- Performance with large datasets

### Privacy Tests
- Data anonymization verification
- Access control validation
- Compliance requirement testing

## Next Steps
Upon completion, proceed to Task 11: Content Management System to implement blog post and educational content administration capabilities.
