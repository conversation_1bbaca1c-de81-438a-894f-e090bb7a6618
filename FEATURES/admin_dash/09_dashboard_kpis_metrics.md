# Task 09: Dashboard KPIs and Metrics

## Objective
Implement comprehensive KPI tracking and metrics display system for the admin dashboard, providing real-time insights into platform performance, user engagement, and business metrics with interactive charts and detailed analytics.

## Dependencies
- Task 05: Dashboard Overview Cards
- Task 06: Real-time Updates System
- Task 08: Error Handling and Loading States

## Estimated Time
3-4 days

## Deliverables

### 1. Analytics Service
**File**: `lib/src/features/admin_portal/data/services/analytics_service.dart`

**Requirements**:
- Platform metrics calculation and aggregation
- Time-series data processing
- Performance benchmarking
- Trend analysis and forecasting
- Integration with existing PocketBase collections

### 2. Analytics Provider
**File**: `lib/src/features/admin_portal/application/providers/analytics_provider.dart`

**Requirements**:
- State management for analytics data
- Real-time metrics updates
- Date range filtering
- Caching for performance optimization
- Error handling for analytics failures

### 3. Analytics Dashboard Page
**File**: `lib/src/features/admin_portal/presentation/pages/analytics_dashboard_page.dart`

**Requirements**:
- Comprehensive metrics overview
- Interactive charts and graphs
- Customizable date ranges
- Export functionality
- Responsive design implementation

### 4. Chart Widgets
**File**: `lib/src/features/admin_portal/presentation/widgets/analytics_chart_widgets.dart`

**Requirements**:
- Line charts for trends
- Bar charts for comparisons
- Pie charts for distributions
- Area charts for cumulative data
- Interactive chart features

### 5. Metrics Models
**File**: `lib/src/features/admin_portal/data/models/analytics_models.dart`

**Requirements**:
- KPI data structures
- Time-series models
- Chart data models
- Filter and aggregation models

## Implementation Details

### Key Performance Indicators
```dart
enum PlatformKPI {
  totalUsers,
  activeUsers,
  userGrowthRate,
  totalClaims,
  activeClaims,
  claimSuccessRate,
  fundingVolume,
  averageFundingAmount,
  platformRevenue,
  userEngagement,
  systemUptime,
  responseTime,
}
```

### Analytics Time Periods
```dart
enum AnalyticsTimePeriod {
  last24Hours,
  last7Days,
  last30Days,
  last90Days,
  lastYear,
  custom,
}
```

### Chart Types
```dart
enum ChartType {
  line,
  bar,
  pie,
  area,
  scatter,
  heatmap,
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] Display all key platform metrics with real-time updates
- [ ] Interactive charts with zoom, pan, and tooltip features
- [ ] Customizable date range selection
- [ ] Export functionality for charts and data
- [ ] Drill-down capabilities for detailed analysis
- [ ] Comparison features for different time periods
- [ ] Trend analysis with growth indicators

### Performance Requirements
- [ ] Analytics dashboard loads within 3 seconds
- [ ] Chart rendering completes within 2 seconds
- [ ] Real-time updates appear within 5 seconds
- [ ] Efficient handling of large datasets
- [ ] Smooth interactions with charts

### Visual Requirements
- [ ] Professional chart designs with 3Pay branding
- [ ] Responsive charts that adapt to screen size
- [ ] Consistent color schemes and typography
- [ ] Accessible chart designs with proper contrast
- [ ] Loading states for chart rendering

### Data Requirements
- [ ] Accurate metric calculations
- [ ] Proper data aggregation for different time periods
- [ ] Error handling for missing or invalid data
- [ ] Data validation and sanitization

## Code Examples

### Analytics Service Implementation
```dart
class AnalyticsService extends PocketBaseService {
  Future<PlatformMetrics> getPlatformMetrics({
    required AnalyticsTimePeriod period,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final dateRange = _getDateRange(period, startDate, endDate);
      
      // Parallel data fetching for performance
      final results = await Future.wait([
        _getUserMetrics(dateRange),
        _getClaimMetrics(dateRange),
        _getFundingMetrics(dateRange),
        _getEngagementMetrics(dateRange),
        _getSystemMetrics(dateRange),
      ]);
      
      return PlatformMetrics(
        userMetrics: results[0],
        claimMetrics: results[1],
        fundingMetrics: results[2],
        engagementMetrics: results[3],
        systemMetrics: results[4],
        period: period,
        startDate: dateRange.start,
        endDate: dateRange.end,
      );
    } catch (e) {
      LoggerService.error('Error fetching platform metrics', e);
      rethrow;
    }
  }
  
  Future<UserMetrics> _getUserMetrics(DateTimeRange range) async {
    final filter = 'created >= "${range.start.toIso8601String()}" && created <= "${range.end.toIso8601String()}"';
    
    final users = await getFullList(
      collectionName: 'users',
      filter: filter,
    );
    
    // Calculate metrics
    int totalUsers = users.length;
    int solicitors = 0;
    int coFunders = 0;
    int claimants = 0;
    
    final dailyRegistrations = <DateTime, int>{};
    
    for (final user in users) {
      final userType = user.data['user_type'] as String?;
      final created = DateTime.parse(user.created);
      final day = DateTime(created.year, created.month, created.day);
      
      switch (userType) {
        case 'solicitor':
          solicitors++;
          break;
        case 'co_funder':
          coFunders++;
          break;
        case 'claimant':
          claimants++;
          break;
      }
      
      dailyRegistrations[day] = (dailyRegistrations[day] ?? 0) + 1;
    }
    
    return UserMetrics(
      totalUsers: totalUsers,
      solicitors: solicitors,
      coFunders: coFunders,
      claimants: claimants,
      dailyRegistrations: dailyRegistrations,
      growthRate: _calculateGrowthRate(dailyRegistrations),
    );
  }
  
  Future<List<ChartDataPoint>> getTimeSeriesData({
    required PlatformKPI kpi,
    required AnalyticsTimePeriod period,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final dateRange = _getDateRange(period, startDate, endDate);
    
    switch (kpi) {
      case PlatformKPI.totalUsers:
        return _getUserTimeSeriesData(dateRange);
      case PlatformKPI.totalClaims:
        return _getClaimTimeSeriesData(dateRange);
      case PlatformKPI.fundingVolume:
        return _getFundingTimeSeriesData(dateRange);
      default:
        throw UnimplementedError('KPI $kpi not implemented');
    }
  }
}
```

### Analytics Dashboard Page
```dart
class AnalyticsDashboardPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin/analytics';
  
  @override
  ConsumerState<AnalyticsDashboardPage> createState() => _AnalyticsDashboardPageState();
}

class _AnalyticsDashboardPageState extends ConsumerState<AnalyticsDashboardPage> {
  AnalyticsTimePeriod _selectedPeriod = AnalyticsTimePeriod.last30Days;
  DateTime? _customStartDate;
  DateTime? _customEndDate;
  
  @override
  Widget build(BuildContext context) {
    final analyticsAsync = ref.watch(analyticsProvider(_selectedPeriod));
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics Dashboard'),
        actions: [
          _buildExportButton(),
          _buildRefreshButton(),
        ],
      ),
      body: Column(
        children: [
          _buildTimePeriodSelector(),
          Expanded(
            child: analyticsAsync.when(
              data: (metrics) => _buildAnalyticsContent(metrics, isDesktop),
              loading: () => const AnalyticsLoadingWidget(),
              error: (error, stack) => AnalyticsErrorWidget(error: error),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildAnalyticsContent(PlatformMetrics metrics, bool isDesktop) {
    return SingleChildScrollView(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // KPI Summary Cards
          _buildKPISummarySection(metrics),
          const SizedBox(height: 32),
          
          // Charts Section
          if (isDesktop) 
            _buildDesktopChartsLayout(metrics)
          else 
            _buildMobileChartsLayout(metrics),
          
          const SizedBox(height: 32),
          
          // Detailed Tables
          _buildDetailedTablesSection(metrics),
        ],
      ),
    );
  }
  
  Widget _buildDesktopChartsLayout(PlatformMetrics metrics) {
    return Column(
      children: [
        // Top row - User and Claim trends
        Row(
          children: [
            Expanded(
              child: AnalyticsChartWidget(
                title: 'User Growth',
                chartType: ChartType.line,
                data: metrics.userMetrics.dailyRegistrations,
                height: 300,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AnalyticsChartWidget(
                title: 'Claim Submissions',
                chartType: ChartType.bar,
                data: metrics.claimMetrics.dailySubmissions,
                height: 300,
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
        
        // Bottom row - Funding and Distribution
        Row(
          children: [
            Expanded(
              child: AnalyticsChartWidget(
                title: 'Funding Volume',
                chartType: ChartType.area,
                data: metrics.fundingMetrics.dailyVolume,
                height: 300,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AnalyticsChartWidget(
                title: 'User Type Distribution',
                chartType: ChartType.pie,
                data: metrics.userMetrics.typeDistribution,
                height: 300,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
```

### Chart Widget Implementation
```dart
class AnalyticsChartWidget extends StatelessWidget {
  final String title;
  final ChartType chartType;
  final Map<dynamic, dynamic> data;
  final double height;
  final Color? primaryColor;
  
  const AnalyticsChartWidget({
    required this.title,
    required this.chartType,
    required this.data,
    this.height = 250,
    this.primaryColor,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    
    return ShadCard(
      title: Text(title, style: theme.textTheme.h4),
      child: SizedBox(
        height: height,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: _buildChart(),
        ),
      ),
    );
  }
  
  Widget _buildChart() {
    switch (chartType) {
      case ChartType.line:
        return _buildLineChart();
      case ChartType.bar:
        return _buildBarChart();
      case ChartType.pie:
        return _buildPieChart();
      case ChartType.area:
        return _buildAreaChart();
      default:
        return const Center(child: Text('Chart type not supported'));
    }
  }
  
  Widget _buildLineChart() {
    // Implementation using fl_chart or similar charting library
    return LineChart(
      LineChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(show: true),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: _convertToFlSpots(data),
            isCurved: true,
            color: primaryColor ?? Colors.blue,
            barWidth: 3,
            dotData: FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: (primaryColor ?? Colors.blue).withOpacity(0.1),
            ),
          ),
        ],
      ),
    );
  }
  
  List<FlSpot> _convertToFlSpots(Map<dynamic, dynamic> data) {
    final spots = <FlSpot>[];
    int index = 0;
    
    for (final entry in data.entries) {
      spots.add(FlSpot(index.toDouble(), entry.value.toDouble()));
      index++;
    }
    
    return spots;
  }
}
```

### Analytics Models
```dart
@freezed
class PlatformMetrics with _$PlatformMetrics {
  const factory PlatformMetrics({
    required UserMetrics userMetrics,
    required ClaimMetrics claimMetrics,
    required FundingMetrics fundingMetrics,
    required EngagementMetrics engagementMetrics,
    required SystemMetrics systemMetrics,
    required AnalyticsTimePeriod period,
    required DateTime startDate,
    required DateTime endDate,
  }) = _PlatformMetrics;
}

@freezed
class UserMetrics with _$UserMetrics {
  const factory UserMetrics({
    required int totalUsers,
    required int solicitors,
    required int coFunders,
    required int claimants,
    required Map<DateTime, int> dailyRegistrations,
    required double growthRate,
    required Map<String, int> typeDistribution,
  }) = _UserMetrics;
}

@freezed
class ChartDataPoint with _$ChartDataPoint {
  const factory ChartDataPoint({
    required DateTime timestamp,
    required double value,
    String? label,
    Map<String, dynamic>? metadata,
  }) = _ChartDataPoint;
}
```

## Testing Requirements

### Unit Tests
- Analytics service calculations
- Metric aggregation logic
- Chart data conversion
- Growth rate calculations

### Widget Tests
- Chart widget rendering
- Analytics dashboard layout
- Time period selector
- Export functionality

### Integration Tests
- End-to-end analytics flow
- Real-time metric updates
- Chart interaction handling
- Data export validation

### Performance Tests
- Large dataset handling
- Chart rendering performance
- Memory usage optimization
- Real-time update efficiency

## Chart Library Integration

### Recommended Libraries
- `fl_chart` for comprehensive charting
- `syncfusion_flutter_charts` for advanced features
- `charts_flutter` for Google Charts integration

### Chart Features
- Interactive tooltips
- Zoom and pan capabilities
- Data point selection
- Animation effects
- Export to image/PDF

## Next Steps
Upon completion, proceed to Task 10: User Activity Monitoring to implement comprehensive user behavior tracking and analysis.
