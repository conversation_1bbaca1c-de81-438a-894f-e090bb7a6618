# Task 02: Basic Dashboard Structure

## Objective
Create the foundational admin dashboard layout with responsive navigation, header, and main content areas following existing 3Pay Global design patterns and ShadCN UI components.

## Dependencies
- Task 01: Admin Authentication Setup

## Estimated Time
2-3 days

## Deliverables

### 1. Admin Dashboard Main Page
**File**: `lib/src/features/admin_portal/presentation/pages/admin_dashboard_page.dart`

**Requirements**:
- Responsive layout following existing portal patterns
- Header with admin profile, notifications, and quick actions
- Sidebar navigation for desktop, bottom nav for mobile
- Main content area with dashboard overview
- Integration with admin authentication state

### 2. Admin Navigation Widget
**File**: `lib/src/features/admin_portal/presentation/widgets/admin_navigation_widget.dart`

**Requirements**:
- Collapsible sidebar for desktop
- Bottom navigation for mobile
- Navigation items with icons and labels
- Active state indication
- Permission-based menu item visibility

### 3. Admin App Bar Widget
**File**: `lib/src/features/admin_portal/presentation/widgets/admin_app_bar_widget.dart`

**Requirements**:
- Admin profile display with avatar
- Notification bell with badge count
- Quick action buttons
- Responsive design adaptation
- Logout functionality

### 4. Admin Responsive Layout Utilities
**File**: `lib/src/features/admin_portal/utils/admin_responsive_layout.dart`

**Requirements**:
- Consistent breakpoints with existing portals
- Responsive padding and spacing utilities
- Grid layout helpers for admin content
- Mobile/tablet/desktop detection methods

### 5. Admin Dashboard Provider
**File**: `lib/src/features/admin_portal/application/providers/admin_dashboard_provider.dart`

**Requirements**:
- Dashboard state management
- Navigation state handling
- Quick stats data loading
- Real-time updates preparation

## Implementation Details

### Dashboard Layout Structure
```dart
class AdminDashboardPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin-dashboard';
  
  @override
  ConsumerState<AdminDashboardPage> createState() => _AdminDashboardPageState();
}

class _AdminDashboardPageState extends ConsumerState<AdminDashboardPage> {
  // Responsive layout with sidebar/bottom nav
  // Main content area with dashboard widgets
  // Integration with admin auth state
}
```

### Navigation Menu Items
```dart
enum AdminNavigationItem {
  dashboard,
  userManagement,
  contentManagement,
  analytics,
  notifications,
  settings,
  auditLogs,
}
```

### Responsive Breakpoints
- Mobile: < 600px (bottom navigation)
- Tablet: 600px - 1200px (collapsible sidebar)
- Desktop: > 1200px (expanded sidebar)

## Acceptance Criteria

### Layout Requirements
- [ ] Responsive design works across all screen sizes
- [ ] Navigation adapts appropriately (sidebar/bottom nav)
- [ ] Header displays admin information correctly
- [ ] Main content area scales properly
- [ ] Consistent with existing portal designs

### Navigation Requirements
- [ ] All admin sections accessible via navigation
- [ ] Active navigation state clearly indicated
- [ ] Permission-based menu item filtering
- [ ] Smooth transitions between sections
- [ ] Mobile navigation is touch-friendly

### Performance Requirements
- [ ] Dashboard loads within 2 seconds
- [ ] Navigation transitions are smooth
- [ ] Responsive layout changes are fluid
- [ ] Memory usage is optimized

### Accessibility Requirements
- [ ] Keyboard navigation support
- [ ] Screen reader compatibility
- [ ] Sufficient color contrast
- [ ] Touch targets meet minimum size requirements

## Code Examples

### Admin Dashboard Page Structure
```dart
class AdminDashboardPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin-dashboard';
  
  @override
  ConsumerState<AdminDashboardPage> createState() => _AdminDashboardPageState();
}

class _AdminDashboardPageState extends ConsumerState<AdminDashboardPage> {
  int _selectedIndex = 0;
  
  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(adminAuthProvider);
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    
    return Scaffold(
      appBar: AdminAppBarWidget(),
      body: Row(
        children: [
          if (isDesktop) AdminSidebarWidget(
            selectedIndex: _selectedIndex,
            onItemSelected: _onNavigationItemSelected,
          ),
          Expanded(
            child: _buildMainContent(),
          ),
        ],
      ),
      bottomNavigationBar: isDesktop ? null : AdminBottomNavWidget(
        selectedIndex: _selectedIndex,
        onItemSelected: _onNavigationItemSelected,
      ),
    );
  }
}
```

### Admin Navigation Widget
```dart
class AdminNavigationWidget extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;
  final bool isCollapsed;
  
  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    
    return Container(
      width: isCollapsed ? 80 : 250,
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(
          right: BorderSide(color: theme.colorScheme.border),
        ),
      ),
      child: Column(
        children: [
          _buildNavigationHeader(),
          Expanded(
            child: _buildNavigationItems(),
          ),
        ],
      ),
    );
  }
}
```

### Responsive Layout Utilities
```dart
class AdminResponsiveLayout {
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;
  
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }
  
  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isDesktop(context)) {
      return const EdgeInsets.all(24.0);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(20.0);
    } else {
      return const EdgeInsets.all(16.0);
    }
  }
}
```

## Testing Requirements

### Widget Tests
- Dashboard page rendering
- Navigation widget functionality
- App bar widget display
- Responsive layout behavior

### Integration Tests
- Navigation between admin sections
- Authentication state integration
- Responsive breakpoint transitions
- Permission-based navigation filtering

### Manual Testing
- Cross-device responsive testing
- Navigation usability testing
- Performance testing on various devices
- Accessibility testing with screen readers

## Design Specifications

### Color Scheme
- Follow existing 3Pay Global brand colors
- Use ShadCN UI color tokens
- Maintain consistency with other portals
- Ensure sufficient contrast ratios

### Typography
- Use existing Inter font family
- Follow ShadCN text theme patterns
- Maintain hierarchy and readability
- Scale appropriately across devices

### Spacing and Layout
- Use consistent spacing tokens
- Follow 8px grid system
- Maintain proper content hierarchy
- Ensure touch-friendly interaction areas

## Integration Points

### Authentication Integration
- Check admin authentication state
- Handle authentication redirects
- Display admin user information
- Manage session timeouts

### Navigation Integration
- Update app routing configuration
- Implement navigation guards
- Handle deep linking
- Manage navigation state

### Theme Integration
- Use existing app theme configuration
- Follow ShadCN UI theming patterns
- Support light/dark mode if applicable
- Maintain brand consistency

## Next Steps
Upon completion, proceed to Task 03: Admin User Management Interface to implement the core user management functionality.
