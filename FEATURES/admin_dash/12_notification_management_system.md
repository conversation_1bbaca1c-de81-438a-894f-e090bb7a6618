# Task 12: Notification Management System

## Objective
Implement comprehensive notification management system for the admin portal, enabling creation, targeting, scheduling, and tracking of notifications across all user types with template management, delivery analytics, and integration with existing notification infrastructure.

## Dependencies
- Task 04: Audit Logging System
- Task 06: Real-time Updates System
- Task 11: Content Management System

## Estimated Time
3-4 days

## Deliverables

### 1. Notification Management Service
**File**: `lib/src/features/admin_portal/data/services/notification_management_service.dart`

**Requirements**:
- Create and send targeted notifications
- Template management and customization
- Scheduling and automation
- Delivery tracking and analytics
- Integration with existing notification system

### 2. Notification Management Provider
**File**: `lib/src/features/admin_portal/application/providers/notification_management_provider.dart`

**Requirements**:
- State management for notification operations
- Template and campaign management
- Delivery status tracking
- Analytics data aggregation
- Error handling for notification failures

### 3. Notification Dashboard Page
**File**: `lib/src/features/admin_portal/presentation/pages/notification_dashboard_page.dart`

**Requirements**:
- Overview of notification campaigns
- Delivery analytics and metrics
- Template management interface
- Quick notification creation
- Campaign performance tracking

### 4. Notification Composer
**File**: `lib/src/features/admin_portal/presentation/pages/notification_composer_page.dart`

**Requirements**:
- Rich notification creation interface
- User targeting and segmentation
- Template selection and customization
- Preview and testing functionality
- Scheduling options

### 5. Notification Models and Templates
**File**: `lib/src/features/admin_portal/data/models/notification_management_models.dart`

**Requirements**:
- Notification campaign models
- Template and targeting models
- Analytics and metrics models
- Delivery status models

## Implementation Details

### Notification Types
```dart
enum NotificationType {
  announcement,
  systemAlert,
  userUpdate,
  claimUpdate,
  fundingUpdate,
  educational,
  promotional,
  reminder,
}

enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}

enum DeliveryChannel {
  inApp,
  email,
  both,
}
```

### Targeting Options
```dart
enum TargetingCriteria {
  userType,
  accessLevel,
  registrationDate,
  lastActivity,
  claimStatus,
  fundingActivity,
  location,
  custom,
}

enum UserSegment {
  allUsers,
  solicitors,
  coFunders,
  claimants,
  activeUsers,
  newUsers,
  inactiveUsers,
  custom,
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] Create and send notifications to targeted user groups
- [ ] Template management with customizable variables
- [ ] User segmentation and targeting options
- [ ] Notification scheduling for future delivery
- [ ] Delivery tracking and analytics
- [ ] A/B testing capabilities for notification content
- [ ] Integration with existing email service

### Targeting Requirements
- [ ] Target by user type (solicitor, co-funder, claimant)
- [ ] Target by access level (for co-funders)
- [ ] Target by activity status and engagement
- [ ] Custom targeting with multiple criteria
- [ ] Exclude specific users or groups
- [ ] Preview target audience size before sending

### Analytics Requirements
- [ ] Delivery rate tracking
- [ ] Open and click-through rates
- [ ] User engagement metrics
- [ ] Campaign performance comparison
- [ ] Export analytics data
- [ ] Real-time delivery monitoring

### Template Requirements
- [ ] Pre-built notification templates
- [ ] Custom template creation
- [ ] Variable substitution (user name, claim details, etc.)
- [ ] Rich text formatting support
- [ ] Template versioning and history

## Code Examples

### Notification Management Service
```dart
class NotificationManagementService extends PocketBaseService {
  Future<NotificationCampaign> createNotificationCampaign({
    required String title,
    required String message,
    required NotificationType type,
    required List<TargetingRule> targetingRules,
    NotificationPriority priority = NotificationPriority.normal,
    DeliveryChannel channel = DeliveryChannel.inApp,
    DateTime? scheduledAt,
    String? templateId,
    Map<String, dynamic>? variables,
  }) async {
    try {
      // Calculate target audience
      final targetUsers = await _calculateTargetAudience(targetingRules);
      
      final campaignData = {
        'title': title,
        'message': message,
        'type': type.name,
        'priority': priority.name,
        'delivery_channel': channel.name,
        'targeting_rules': targetingRules.map((r) => r.toJson()).toList(),
        'target_user_count': targetUsers.length,
        'scheduled_at': scheduledAt?.toIso8601String(),
        'template_id': templateId,
        'variables': variables ?? {},
        'status': scheduledAt != null ? 'scheduled' : 'draft',
        'created_by': currentUser?.id,
      };
      
      final record = await createRecord(
        collectionName: 'notification_campaigns',
        data: campaignData,
      );
      
      // Log campaign creation
      await logAdminActivity('create_notification_campaign', {
        'campaign_id': record.id,
        'title': title,
        'target_count': targetUsers.length,
      });
      
      return NotificationCampaign.fromRecord(record);
    } catch (e) {
      LoggerService.error('Error creating notification campaign', e);
      rethrow;
    }
  }
  
  Future<void> sendNotificationCampaign(String campaignId) async {
    try {
      final campaign = await getOne(
        collectionName: 'notification_campaigns',
        recordId: campaignId,
      );
      
      final targetingRules = (campaign.data['targeting_rules'] as List)
          .map((r) => TargetingRule.fromJson(r))
          .toList();
      
      final targetUsers = await _calculateTargetAudience(targetingRules);
      
      // Send notifications based on delivery channel
      final deliveryChannel = DeliveryChannel.values.firstWhere(
        (c) => c.name == campaign.data['delivery_channel'],
      );
      
      switch (deliveryChannel) {
        case DeliveryChannel.inApp:
          await _sendInAppNotifications(campaign, targetUsers);
          break;
        case DeliveryChannel.email:
          await _sendEmailNotifications(campaign, targetUsers);
          break;
        case DeliveryChannel.both:
          await _sendInAppNotifications(campaign, targetUsers);
          await _sendEmailNotifications(campaign, targetUsers);
          break;
      }
      
      // Update campaign status
      await updateRecord(
        collectionName: 'notification_campaigns',
        recordId: campaignId,
        data: {
          'status': 'sent',
          'sent_at': DateTime.now().toIso8601String(),
          'actual_recipient_count': targetUsers.length,
        },
      );
      
      // Log campaign send
      await logAdminActivity('send_notification_campaign', {
        'campaign_id': campaignId,
        'recipient_count': targetUsers.length,
      });
      
    } catch (e) {
      LoggerService.error('Error sending notification campaign', e);
      rethrow;
    }
  }
  
  Future<List<String>> _calculateTargetAudience(List<TargetingRule> rules) async {
    final allUsers = await getFullList(collectionName: 'users');
    final targetUsers = <String>[];
    
    for (final user in allUsers) {
      bool matchesAllRules = true;
      
      for (final rule in rules) {
        if (!_userMatchesRule(user, rule)) {
          matchesAllRules = false;
          break;
        }
      }
      
      if (matchesAllRules) {
        targetUsers.add(user.id);
      }
    }
    
    return targetUsers;
  }
  
  bool _userMatchesRule(RecordModel user, TargetingRule rule) {
    switch (rule.criteria) {
      case TargetingCriteria.userType:
        return user.data['user_type'] == rule.value;
      case TargetingCriteria.registrationDate:
        final userCreated = DateTime.parse(user.created);
        final targetDate = DateTime.parse(rule.value);
        return rule.operator == 'after' 
            ? userCreated.isAfter(targetDate)
            : userCreated.isBefore(targetDate);
      case TargetingCriteria.lastActivity:
        // Implementation for last activity targeting
        return true; // Placeholder
      default:
        return false;
    }
  }
  
  Future<void> _sendInAppNotifications(RecordModel campaign, List<String> userIds) async {
    for (final userId in userIds) {
      try {
        await createRecord(
          collectionName: 'notifications',
          data: {
            'title': campaign.data['title'],
            'message': campaign.data['message'],
            'type': campaign.data['type'],
            'recipientId': [userId],
            'campaign_id': campaign.id,
            'isRead': false,
          },
        );
      } catch (e) {
        LoggerService.error('Error sending in-app notification to user $userId', e);
      }
    }
  }
  
  Future<NotificationAnalytics> getCampaignAnalytics(String campaignId) async {
    try {
      // Get campaign details
      final campaign = await getOne(
        collectionName: 'notification_campaigns',
        recordId: campaignId,
      );
      
      // Get delivery metrics
      final notifications = await getFullList(
        collectionName: 'notifications',
        filter: 'campaign_id = "$campaignId"',
      );
      
      final totalSent = notifications.length;
      final totalRead = notifications.where((n) => n.data['isRead'] == true).length;
      final readRate = totalSent > 0 ? (totalRead / totalSent) * 100 : 0.0;
      
      return NotificationAnalytics(
        campaignId: campaignId,
        totalSent: totalSent,
        totalRead: totalRead,
        readRate: readRate,
        deliveryRate: 100.0, // Assuming successful delivery for in-app notifications
        clickThroughRate: 0.0, // Would need click tracking implementation
      );
    } catch (e) {
      LoggerService.error('Error getting campaign analytics', e);
      rethrow;
    }
  }
}
```

### Notification Composer Page
```dart
class NotificationComposerPage extends ConsumerStatefulWidget {
  final String? campaignId; // null for new campaign
  static const String routeName = '/admin/notifications/compose';
  
  const NotificationComposerPage({this.campaignId});
  
  @override
  ConsumerState<NotificationComposerPage> createState() => _NotificationComposerPageState();
}

class _NotificationComposerPageState extends ConsumerState<NotificationComposerPage> {
  final _formKey = GlobalKey<ShadFormState>();
  final _titleController = TextEditingController();
  final _messageController = TextEditingController();
  
  NotificationType _selectedType = NotificationType.announcement;
  NotificationPriority _selectedPriority = NotificationPriority.normal;
  DeliveryChannel _selectedChannel = DeliveryChannel.inApp;
  List<TargetingRule> _targetingRules = [];
  DateTime? _scheduledAt;
  String? _selectedTemplateId;
  
  @override
  Widget build(BuildContext context) {
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.campaignId == null ? 'Create Notification' : 'Edit Notification'),
        actions: [
          _buildPreviewButton(),
          _buildSaveButton(),
          _buildSendButton(),
        ],
      ),
      body: ShadForm(
        key: _formKey,
        child: isDesktop ? _buildDesktopLayout() : _buildMobileLayout(),
      ),
    );
  }
  
  Widget _buildDesktopLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main composer area
        Expanded(
          flex: 2,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: _buildMainComposerContent(),
          ),
        ),
        
        // Sidebar with targeting and settings
        SizedBox(
          width: 350,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: _buildComposerSidebar(),
          ),
        ),
      ],
    );
  }
  
  Widget _buildMainComposerContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Template selection
        _buildTemplateSelector(),
        const SizedBox(height: 24),
        
        // Title input
        ShadInputFormField(
          id: 'title',
          controller: _titleController,
          label: const Text('Notification Title'),
          placeholder: const Text('Enter notification title'),
          validator: (value) => value?.isEmpty == true ? 'Title is required' : null,
        ),
        const SizedBox(height: 24),
        
        // Message input
        ShadInputFormField(
          id: 'message',
          controller: _messageController,
          label: const Text('Message'),
          placeholder: const Text('Enter notification message'),
          maxLines: 5,
          validator: (value) => value?.isEmpty == true ? 'Message is required' : null,
        ),
        const SizedBox(height: 24),
        
        // Preview section
        _buildNotificationPreview(),
      ],
    );
  }
  
  Widget _buildComposerSidebar() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Notification settings
        _buildNotificationSettings(),
        const SizedBox(height: 24),
        
        // Targeting section
        _buildTargetingSection(),
        const SizedBox(height: 24),
        
        // Scheduling section
        _buildSchedulingSection(),
        const SizedBox(height: 24),
        
        // Target audience preview
        _buildTargetAudiencePreview(),
      ],
    );
  }
  
  Widget _buildTargetingSection() {
    return ShadCard(
      title: const Text('Target Audience'),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quick targeting options
          _buildQuickTargetingButtons(),
          const SizedBox(height: 16),
          
          // Custom targeting rules
          ..._targetingRules.asMap().entries.map((entry) {
            final index = entry.key;
            final rule = entry.value;
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: TargetingRuleWidget(
                rule: rule,
                onChanged: (newRule) {
                  setState(() {
                    _targetingRules[index] = newRule;
                  });
                },
                onRemove: () {
                  setState(() {
                    _targetingRules.removeAt(index);
                  });
                },
              ),
            );
          }).toList(),
          
          // Add targeting rule button
          ShadButton(
            onPressed: _addTargetingRule,
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(LucideIcons.plus, size: 16),
                SizedBox(width: 8),
                Text('Add Rule'),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildQuickTargetingButtons() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildQuickTargetButton('All Users', () => _setQuickTarget(UserSegment.allUsers)),
        _buildQuickTargetButton('Solicitors', () => _setQuickTarget(UserSegment.solicitors)),
        _buildQuickTargetButton('Co-funders', () => _setQuickTarget(UserSegment.coFunders)),
        _buildQuickTargetButton('Claimants', () => _setQuickTarget(UserSegment.claimants)),
        _buildQuickTargetButton('Active Users', () => _setQuickTarget(UserSegment.activeUsers)),
      ],
    );
  }
  
  Widget _buildQuickTargetButton(String label, VoidCallback onPressed) {
    return ShadButton(
      size: ShadButtonSize.sm,
      variant: ShadButtonVariant.outline,
      onPressed: onPressed,
      child: Text(label),
    );
  }
  
  Widget _buildNotificationPreview() {
    return ShadCard(
      title: const Text('Preview'),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: ShadTheme.of(context).colorScheme.muted,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getNotificationIcon(_selectedType),
                  size: 20,
                  color: _getNotificationColor(_selectedType),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _titleController.text.isEmpty ? 'Notification Title' : _titleController.text,
                    style: ShadTheme.of(context).textTheme.p.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Text(
                  'now',
                  style: ShadTheme.of(context).textTheme.small.copyWith(
                    color: ShadTheme.of(context).colorScheme.mutedForeground,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _messageController.text.isEmpty ? 'Notification message will appear here...' : _messageController.text,
              style: ShadTheme.of(context).textTheme.small,
            ),
          ],
        ),
      ),
    );
  }
}
```

### Notification Analytics Widget
```dart
class NotificationAnalyticsWidget extends ConsumerWidget {
  final String campaignId;
  
  const NotificationAnalyticsWidget({required this.campaignId});
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsAsync = ref.watch(notificationAnalyticsProvider(campaignId));
    
    return analyticsAsync.when(
      data: (analytics) => _buildAnalyticsContent(context, analytics),
      loading: () => const CustomSkeletonWidget(),
      error: (error, stack) => Text('Error: $error'),
    );
  }
  
  Widget _buildAnalyticsContent(BuildContext context, NotificationAnalytics analytics) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'Total Sent',
                analytics.totalSent.toString(),
                LucideIcons.send,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildMetricCard(
                'Read Rate',
                '${analytics.readRate.toStringAsFixed(1)}%',
                LucideIcons.eye,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'Delivery Rate',
                '${analytics.deliveryRate.toStringAsFixed(1)}%',
                LucideIcons.checkCircle,
                Colors.purple,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildMetricCard(
                'Click Rate',
                '${analytics.clickThroughRate.toStringAsFixed(1)}%',
                LucideIcons.mousePointer,
                Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: ShadTheme.of(context).textTheme.h3.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: ShadTheme.of(context).textTheme.small.copyWith(
                color: ShadTheme.of(context).colorScheme.mutedForeground,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

## Testing Requirements

### Unit Tests
- Notification management service methods
- Targeting rule evaluation
- Template variable substitution
- Analytics calculations

### Widget Tests
- Notification composer interface
- Targeting rule widgets
- Analytics display components
- Preview functionality

### Integration Tests
- End-to-end notification creation and sending
- Template management workflows
- Analytics data collection
- Email service integration

### Performance Tests
- Large audience targeting performance
- Bulk notification sending
- Real-time analytics updates
- Template rendering efficiency

## Next Steps
Upon completion, proceed to Task 13: System Configuration to implement admin settings and platform configuration management.
