# Task 05: Dashboard Overview Cards

## Objective
Implement the main dashboard overview with KPI cards displaying real-time platform metrics, user statistics, and system health indicators using responsive design patterns consistent with existing portals.

## Dependencies
- Task 01: Admin Authentication Setup
- Task 02: Basic Dashboard Structure
- Task 04: Audit Logging System

## Estimated Time
2-3 days

## Deliverables

### 1. Dashboard Overview Widget
**File**: `lib/src/features/admin_portal/presentation/widgets/dashboard_overview_widget.dart`

**Requirements**:
- Responsive grid layout for KPI cards
- Real-time data updates
- Loading states and error handling
- Consistent with existing portal card designs
- Click-through navigation to detailed views

### 2. Dashboard Statistics Service
**File**: `lib/src/features/admin_portal/data/services/dashboard_statistics_service.dart`

**Requirements**:
- Real-time platform metrics calculation
- User statistics aggregation
- System health monitoring
- Performance metrics tracking
- Integration with existing PocketBase collections

### 3. Dashboard Statistics Provider
**File**: `lib/src/features/admin_portal/application/providers/dashboard_statistics_provider.dart`

**Requirements**:
- State management for dashboard metrics
- Real-time updates via PocketBase subscriptions
- Caching for performance optimization
- Error handling and retry logic

### 4. KPI Card Widgets
**File**: `lib/src/features/admin_portal/presentation/widgets/kpi_card_widgets.dart`

**Requirements**:
- Individual KPI card components
- Trend indicators and sparklines
- Interactive elements with navigation
- Responsive design adaptation
- Loading and error states

### 5. Dashboard Models
**File**: `lib/src/features/admin_portal/data/models/dashboard_models.dart`

**Requirements**:
- Platform statistics model
- KPI data models
- Trend calculation models
- System health status models

## Implementation Details

### Dashboard KPI Categories
```dart
enum DashboardKPI {
  totalUsers,
  activeUsers,
  newRegistrations,
  totalClaims,
  activeClaims,
  fundingVolume,
  systemHealth,
  recentActivity,
}
```

### Platform Statistics Model
```dart
class PlatformStatistics {
  final int totalUsers;
  final int activeSolicitors;
  final int activeCoFunders;
  final int activeClaimants;
  final int totalClaims;
  final int activeClaims;
  final double totalFundingVolume;
  final int newRegistrationsToday;
  final int newRegistrationsWeek;
  final SystemHealthStatus systemHealth;
  final List<RecentActivity> recentActivities;
}
```

### KPI Card Structure
```dart
class KPICard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color? iconColor;
  final TrendIndicator? trend;
  final VoidCallback? onTap;
  
  @override
  Widget build(BuildContext context) {
    // Responsive card implementation
    // Following existing portal card patterns
  }
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] Display real-time platform statistics
- [ ] Show user counts by type (solicitors, co-funders, claimants)
- [ ] Display claim statistics (total, active, by status)
- [ ] Show funding volume metrics
- [ ] Display system health indicators
- [ ] Show recent activity feed
- [ ] Trend indicators for key metrics
- [ ] Click-through navigation to detailed views

### Performance Requirements
- [ ] Dashboard loads within 2 seconds
- [ ] Real-time updates appear within 5 seconds
- [ ] Responsive design works smoothly across devices
- [ ] Efficient data aggregation for large datasets

### Visual Requirements
- [ ] Consistent with existing portal card designs
- [ ] Responsive grid layout (1-4 columns based on screen size)
- [ ] Proper loading states and error handling
- [ ] Accessible color schemes and contrast
- [ ] Smooth animations and transitions

### Data Requirements
- [ ] Accurate real-time statistics
- [ ] Proper error handling for data failures
- [ ] Caching for performance optimization
- [ ] Audit logging for dashboard access

## Code Examples

### Dashboard Statistics Service
```dart
class DashboardStatisticsService extends PocketBaseService {
  Future<PlatformStatistics> getPlatformStatistics() async {
    try {
      // Parallel data fetching for performance
      final results = await Future.wait([
        _getUserStatistics(),
        _getClaimStatistics(),
        _getFundingStatistics(),
        _getSystemHealth(),
        _getRecentActivity(),
      ]);
      
      return PlatformStatistics(
        totalUsers: results[0]['total'],
        activeSolicitors: results[0]['solicitors'],
        activeCoFunders: results[0]['coFunders'],
        activeClaimants: results[0]['claimants'],
        totalClaims: results[1]['total'],
        activeClaims: results[1]['active'],
        totalFundingVolume: results[2]['volume'],
        newRegistrationsToday: results[0]['newToday'],
        newRegistrationsWeek: results[0]['newWeek'],
        systemHealth: results[3],
        recentActivities: results[4],
      );
    } catch (e) {
      LoggerService.error('Error fetching platform statistics', e);
      rethrow;
    }
  }
  
  Future<Map<String, int>> _getUserStatistics() async {
    final users = await getFullList(collectionName: 'users');
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekAgo = today.subtract(const Duration(days: 7));
    
    int total = users.length;
    int solicitors = 0;
    int coFunders = 0;
    int claimants = 0;
    int newToday = 0;
    int newWeek = 0;
    
    for (final user in users) {
      final userType = user.data['user_type'] as String?;
      final created = DateTime.parse(user.created);
      
      switch (userType) {
        case 'solicitor':
          solicitors++;
          break;
        case 'co_funder':
          coFunders++;
          break;
        case 'claimant':
          claimants++;
          break;
      }
      
      if (created.isAfter(today)) newToday++;
      if (created.isAfter(weekAgo)) newWeek++;
    }
    
    return {
      'total': total,
      'solicitors': solicitors,
      'coFunders': coFunders,
      'claimants': claimants,
      'newToday': newToday,
      'newWeek': newWeek,
    };
  }
}
```

### Dashboard Overview Widget
```dart
class DashboardOverviewWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statisticsAsync = ref.watch(dashboardStatisticsProvider);
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    final isTablet = AdminResponsiveLayout.isTablet(context);
    
    return statisticsAsync.when(
      data: (stats) => _buildDashboardContent(context, stats, isDesktop, isTablet),
      loading: () => _buildLoadingState(context, isDesktop, isTablet),
      error: (error, stack) => _buildErrorState(context, error),
    );
  }
  
  Widget _buildDashboardContent(
    BuildContext context,
    PlatformStatistics stats,
    bool isDesktop,
    bool isTablet,
  ) {
    final crossAxisCount = isDesktop ? 4 : (isTablet ? 3 : 2);
    
    return Padding(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Platform Overview',
            style: ShadTheme.of(context).textTheme.h2,
          ),
          const SizedBox(height: 24),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: isDesktop ? 1.2 : 1.0,
            children: [
              KPICard(
                title: 'Total Users',
                value: stats.totalUsers.toString(),
                subtitle: '+${stats.newRegistrationsWeek} this week',
                icon: LucideIcons.users,
                iconColor: Colors.blue,
                trend: TrendIndicator.up,
                onTap: () => _navigateToUserManagement(context),
              ),
              KPICard(
                title: 'Active Claims',
                value: stats.activeClaims.toString(),
                subtitle: '${stats.totalClaims} total',
                icon: LucideIcons.fileText,
                iconColor: Colors.green,
                onTap: () => _navigateToClaimManagement(context),
              ),
              KPICard(
                title: 'Funding Volume',
                value: '£${_formatCurrency(stats.totalFundingVolume)}',
                subtitle: 'Total platform volume',
                icon: LucideIcons.trendingUp,
                iconColor: Colors.purple,
                onTap: () => _navigateToAnalytics(context),
              ),
              KPICard(
                title: 'System Health',
                value: stats.systemHealth.status,
                subtitle: stats.systemHealth.description,
                icon: LucideIcons.activity,
                iconColor: stats.systemHealth.color,
                onTap: () => _navigateToSystemHealth(context),
              ),
            ],
          ),
          const SizedBox(height: 32),
          _buildRecentActivitySection(context, stats.recentActivities),
        ],
      ),
    );
  }
}
```

### KPI Card Widget
```dart
class KPICard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color? iconColor;
  final TrendIndicator? trend;
  final VoidCallback? onTap;
  
  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    
    return ShadCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Icon(
                    icon,
                    color: iconColor ?? theme.colorScheme.primary,
                    size: 24,
                  ),
                  if (trend != null) _buildTrendIndicator(trend!),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: theme.textTheme.h3.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle!,
                  style: theme.textTheme.small.copyWith(
                    color: theme.colorScheme.mutedForeground,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildTrendIndicator(TrendIndicator trend) {
    IconData icon;
    Color color;
    
    switch (trend) {
      case TrendIndicator.up:
        icon = LucideIcons.trendingUp;
        color = Colors.green;
        break;
      case TrendIndicator.down:
        icon = LucideIcons.trendingDown;
        color = Colors.red;
        break;
      case TrendIndicator.stable:
        icon = LucideIcons.minus;
        color = Colors.grey;
        break;
    }
    
    return Icon(icon, color: color, size: 16);
  }
}
```

## Testing Requirements

### Unit Tests
- Dashboard statistics service calculations
- KPI card rendering
- Trend calculation logic
- Data formatting functions

### Widget Tests
- Dashboard overview widget rendering
- KPI card interactions
- Responsive layout behavior
- Loading and error states

### Integration Tests
- Real-time data updates
- Navigation from KPI cards
- Performance with large datasets
- Error handling scenarios

## Performance Optimization

### Data Caching
- Cache dashboard statistics for 5 minutes
- Implement incremental updates for real-time data
- Use efficient database queries with proper indexing
- Optimize parallel data fetching

### UI Performance
- Implement proper loading states
- Use efficient list rendering for large datasets
- Optimize image and icon loading
- Implement smooth animations

## Next Steps
Upon completion, proceed to Task 06: Real-time Updates System to implement live data synchronization across the admin dashboard.
