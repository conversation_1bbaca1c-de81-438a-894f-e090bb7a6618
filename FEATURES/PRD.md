Product Requirements Document (PRD) - 3Pay Group Litigation Funding Platform
1. Product Overview
1.1 Product Purpose The 3Pay Group Litigation Funding Platform is a comprehensive digital platform designed to facilitate litigation funding in England and Wales. Its primary focus is on unregulated lending practices. The platform serves as an educational resource and funding facilitation tool for solicitors and prospective co-funders. It aims to provide valuable information on unregulated lending practices in England and Wales. The platform's services fall outside the scope of the Financial Services and Markets Act 2000, ensuring compliance with the Financial Conduct Authority (FCA) and Claims Management Regulations by providing no content or commentary on regulated financial products or services. The platform emerges as a pivotal resource in the litigation funding landscape by providing insight, education, and sharing high-level knowledge with its users. It also creates real value, transparency, and security for litigants, solicitors, co-funders, and students.
1.2 Target Users The platform is designed to serve multiple user types. Development for these users will utilize Flutter with shadcn ui for the user interfaces and Pocketbase for data management and backend functionalities.
•	User Type 1 (Solicitors): These are legal professionals seeking funding for claims. Solicitors must apply for and obtain Permitted User (PU) status before submitting a funding application. Information required from solicitors includes law firm details (name, solicitor's name, position, email, SRA number, contact number, firm's address, registration number) and funding application details (minimum value claim, required funding amount, schedule of costs, confirmation of Conditional Fee Agreement, risk committee approval, legal opinion, undertaking for updates). They can also add additional users.
•	User Type 2 (Prospective Co-Funders / PCF): Individuals interested in funding litigation cases. These users access the platform through a level-based system. To become a co-funder, a PCF must graduate through 4 tier levels, demonstrating knowledge of litigation funding, associated risks, and financial capacity. Information required includes full name, email, residential address, age, date of birth, occupation, mobile number, nationality, country of residence, net worth, assets portfolio, available surplus cash, risk appetite, copy of passport/photo driving license, proof of residence, and bank account details.
•	User Type 3 (Claimant): Claimants are the individuals or entities whose legal cases are seeking or receiving funding through the platform. Claimant user type could require features allowing them to: 
o	View the status of their specific funding application (if applicable and permissioned via their solicitor).
o	Track the progress of their case (again, if permitted by their solicitor and appropriate privacy controls are in place).
o	Potentially access documents related to the funding or case (with strict permissioning).
o	Communicate with their solicitor (possibly through a feature integrated into the platform).
o	Access general educational content about litigation funding and the process.

2. Platform Requirements
The platform will be accessible via a mobile app (iOS and Android) and a web interface .It will use Flutter as the cross-platform framework, leveraging the shadcn ui library for Flutter for a consistent and polished user interface across all platforms. Pocketbase will serve as the primary BaaS, handling database, authentication, file storage, and real-time subscriptions.
2.1 Core Platform Features
These features are foundational for all or multiple user types and will be implemented using Flutter with shadcn ui for the frontend components and Pocketbase for backend logic and data storage.
•	2.1.1 Authentication & Security 
o	Biometric authentication support for mobile devices.
o	One-time password (OTP) implementation offered by Pocketbase
o	Secure session management.
o	Role-based access control (User 1, User 2 levels, Claimant).
o	Activity logging.
•	2.1.2 User Management 
o	User registration and profile creation.
o	Role assignment and permissions management.
o	Additional user management for solicitors (User 1).
o	User activity logging and monitoring.
o	Profile editing and update capabilities.
o	Account deactivation and deletion options.
•	2.1.3 Content Management System 
o	Blog post creation and management. (Admin Portal)
o	Podcast hosting and streaming.
o	Newsletter distribution system (for Level 1+ users).
o	Educational content management (including interactive modules, resource library). – Data will be stored in Pocketbase and managed with the Admin Portal
o	Realtime content updates.
o	Content categorization and tagging.
o	Search functionality (advanced search).
o	Content analytics and metrics.
o	Content specifically crafted for different user levels (e.g., Level 1 focuses on high returns/low risk, Level 2 on the funding model).
3. User-Specific Requirements
These features are tailored to the specific needs of each user type and will be implemented using Flutter with shadcn ui for the UI and interacting with Pocketbase for backend data and logic.
3.1 Solicitor Portal (User 1)
•	3.1.1 Registration & Verification 
o	Law firm information submission.
o	Solicitor credentials verification.
o	PermiYed User (PU) status application.
o	Document upload capabili-es (e.g., Legal Practitioner's Certificate).
o	Verification status tracking.
o	Automated no-fication system (acceptance/rejection within 2 business days for PU status).
•	3.1.2 Funding Application 
o	Structured application form.
o	Document upload system (e.g., schedule of costs, legal opinion, Conditional Fee Agreement).
o	Progress tracking.
o	Application status no-fications (after rigorous review/underwriting process of at least 28 business days).
o	Communication channel with reviewers.
o	Application history.
o	Dra\ saving functionality.
o	Submission requires specific details like minimum claim value (£5m), maximum required funding (£3m), schedule of costs, confirmation of Conditional Fee Agreement, risk committee approval, and legal opinion with at least 55% success prospects.
o	Undertaking to provide periodic updates on the funded claim's progress.
o	Facilitates the rigorous due diligence process involving four teams of legal experts (Claimant's Solicitors, Leading Counsel, 3Pay Group's Lawyers, ATE Insurer's Lawyers - the 'four pillars').
•	3.1.3 Case Management 
o	Case status tracking.
o	Document repository.
o	Progress updates (regular reporting tools).
o	Communication tools.
o	Deadline management.
o	Cost tracking.
o	Viewing details of claims published on the platform dashboard once funded, including solicitor and barrister details, and a summary of the claim's journey (with confidential details redacted).
3.2 Co-Funder Portal (User 2)
•	3.2.1 Level-Based Access System 
o	Basic Level 0: Public access 
	News feed.
	Basic educational content (news, blogs, podcasts).
	Registration prompts/Call to actions to graduate to Level 1.
o	Level 1 (Curious) 
	Requires sign-up with full name and email.
	Enhanced content access (news, blogs, podcasts, newsletters).
	Newsletter subscription.
	Basic platform features.
	Educational resources focused on getting into litigation funding, funds commitment, expected returns, low-risk aspects.
o	Level 2 (Interested) 
	Requires sign-up with residential address, age, date of birth, occupation, mobile number, nationality, country of residence.
	Advanced content access (Industry insights).
	Content centered around the 3Pay Group funding model: claim types, sectors, minimum/maximum funding, due diligence, leveraging.
o	Level 3 (Desire) / Associates 
	Requires sign-up including entering a non-disclosure agreement.
	Passes through 3Pay Group’s anti-money laundering (AML) and know your customer (KYC) requirements. Uses latest technology for AML/KYC interfaces.
	Submission of additional personal information (Net Worth, Assets Portfolio, Available Surplus Cash, Risk Appetite, copies of passport/license, proof of residence, bank details). Acceptance/rejection notification within 2 business days.
	Full platform access (including access to Levels 1 and 2 content).
	Case review capabilities: Access to review 3Pay Group’s complete schedule of funding activities and follow specific claims.
	Expert consultation booking (1 hour 1-on-1 coaching with a litigation funding expert).
	Detailed analytics.
o	Level 4 (Action) / Co-Funders 
	Only available to Associates.
	Requires readiness to commit funds to claims at sign-up.
	Knowledge test platform - Quick Knowledge Test required to move to to be eligible to fund claims.
	Investment capabilities.
	Fund management.
	Performance tracking.
	Advanced analytics.
	Requires entering into a non-recourse facility agreement.
	Application approval within 2 business days. Rejected funds returned within 5 business days.
	Claim funding follows these steps/flow for every claim they fund
	Choose funding type (Discretionary or non-discretionary)
	Enter amount in pounds sterling to fund with
	Agree to Lenders Application form
	Agree to Delecration of continued interest form
	Agree to non-recource form
•	3.2.2 Educational Features 
o	Interactive learning modules.
o	Progress tracking (for coursework/tests).
o	Knowledge assessment tools (Quick Knowledge Tests).
o	Certificate generation (implies completion tracking).
o	Resource library.
o	Expert consultation booking.
o	Educational content recommendations.
•	3.2.3 Investment Management 
o	Fund commitment interface.
o	Portfolio management (viewing committed funds across claims).
o	Return tracking (viewing Fixed Rate Funder Returns - FRFR). FRFR varies based on claim stage (70% Pre-Action, decreasing to 10% Trial Prep).
o	Risk assessment tools.
o	Investment history.
o	Performance analytics.
o	Document management (related to investments/agreements).
o	Awareness of fixed returns decreasing as claims advance.
o	Entering non-recourse facility agreements.
3.3 Claimant Portal (User 3)
Potential Requirements: 
o	Basic access: Limited, possibly read-only access.
o	View Case Status: A simplified view showing the current stage of their specific legal case, perhaps linked to the milestones tracked in the Solicitor's Case Management section.
o	View Funding Status: A simplified view indicating if funding has been secured for their case.
o	Document Access: Limited access to specific documents related to the case or funding, shared and permissioned by their solicitor.
o	Educational Resources: Access to basic educational content about litigation funding and the platform through blog posts
o	Notification System: Receive notifications about key updates related to their case status or funding.
4. Technical Requirements
The platform will be built using Flutter for both the mobile and web clients, utilizing the shadcn ui library for Flutter for UI components. Pocketbase will be used as the primary Backend-as-a-Service (BaaS).
•	4.1 Mobile App Requirements 
o	Developed using Flutter with shadcn ui.
o	Offline functionality for basic features (needs further definition).
o	Push notification support.
o	Biometric authentication.
o	Camera integration for document scanning and identification.
o	Local data encryption.
o	Automatic updates.
o	Performance optimization.
o	Integrates with Pocketbase for backend operations.
•	4.2 Web Platform Requirements 
o	Developed using Flutter with shadcn ui.
o	Responsive design (Desktop, tablet, mobile).
o	Cross-browser compatibility.
o	Progressive Web App (PWA) features
o	Realtime updates (leveraging Pocketbase real-time subscriptions).
o	Advanced search functionality.
o	Document preview.
o	Accessibility compliance.
o	Performance optimization.
o	Integrates with Pocketbase for backend operations.
•	4.3 Integration Requirements 
o	Payment gateway integration - Stripe.
o	Email service integration.
o	SMS gateway integration.
o	Document verification services.
o	AML/KYC service integration (Pocketbase may handle some of this or integrate with external services).
o	Analytics integration (e.g., Google Analytics and Posthog).
o	CRM integration.
o	Cloud storage integration (Pocketbase handles file storage natively, but integration with external cloud storage like s3 will be needed for scale or backup).
o	Integrations primarily handled via Pocketbase.
5. Security & Compliance
Platform security and compliance are paramount, especially given the handling of sensitive legal and financial information. Pocketbase's built-in security features (authentication, access control) and data encryption will be leveraged, supplemented by application-level security implemented in Flutter.
•	5.1 Security Requirements 
o	End-to-end encryption.
o	Regular security audits.
o	Penetration testing.
o	Secure data storage (Pocketbase).
o	Access control (Role-based, managed via Pocketbase).
o	Activity logging (Pocketbase can log API requests).
o	Intrusion detection using Rate Limiting in Pocketbase
o	DDoS protection.
•	5.2 Compliance Requirements 
o	GDPR compliance.
o	FCA guidelines adherence (specifically regarding unregulated lending).
o	Data protection standards.
o	Financial services regulations (adhering to boundaries of unregulated lending).
o	Legal compliance documentation.
o	Regular compliance audits.
o	User data management.
o	Privacy policy implementation.
o	AML/KYC compliance, facilitated by integrated services.
