# SOLICITOR_FEATKILO.md: Solicitor User Experience Design

## 1. Introduction

This document details the user experience (UX) design for the Solicitor role within the 3Pay Group Litigation Funding Platform. It builds upon the foundational requirements specified in [`PRD.md`](PRD.md) and the overall system architecture outlined in [`DESIGN_DOCUMENT.md`](DESIGN_DOCUMENT.md). The design aims to provide solicitors with a rich, intuitive, and comprehensive interface for managing litigation funding applications, claims, and related activities.

The primary goal is to enhance solicitor efficiency, provide task clarity, ensure ease of use, maintain robust data security, and adhere to regulatory compliance. This design will leverage the Flutter with shadcn_ui frontend stack, aiming for consistency with the co-funder portal where appropriate, while strategically diverging to meet the specific needs of legal professionals. Accessibility best practices (WCAG) are integral to this design.

This document is structured as follows:

*   **Part 1: Funding Application & Claim Management:** Details the workflows for application submission, tracking, evidence management, review, and resolution.
*   **Part 2: Solicitor Dashboard:** Conceptualizes a personalized hub for active claims, tasks, notifications, and KPIs.
*   **Part 3: Core Solicitor-Specific Functionalities:** Covers claim management, communication (internal and externally via 3Pay Global agents), document management, reporting, user profiles, and search.
*   **Part 4: Cross-Cutting Concerns:** Addresses error handling, accessibility, and UI/UX consistency.

## 2. Part 1: Funding Application & Claim Management

This section focuses on the core lifecycle of funding applications and subsequent claims from the solicitor's perspective. It emphasizes streamlined processes, clear status visibility, and robust evidence handling.

### 2.1 Overview & Lifecycle Diagram

The funding application and claim management process for solicitors involves several key stages, from initial application drafting to potential funding and ongoing claim management. The platform aims to support solicitors throughout this lifecycle.

**High-Level Lifecycle:** 

```mermaid
graph TD
    A[Start: PU Solicitor Logs In] --> B{Draft Funding Application};
    B -- Save Draft --> B;
    B -- Submit Application --> C{Application Submitted};
    C --> D[Platform Review & Due Diligence];
    D -- Requires More Info --> E{Application: Requires Info};
    E -- Solicitor Updates & Resubmits --> C;
    D -- Approved --> F{Application Approved};
    F --> G[Claim Created & Active];
    G --> H{Manage Active Claim: Updates, Evidence, Communication with 3Pay Global Agents};
    D -- Rejected --> I{Application Rejected};

    style A fill:#D6EAF8,stroke:#2E86C1
    style B fill:#FEF9E7,stroke:#F1C40F
    style C fill:#E8F8F5,stroke:#1ABC9C
    style D fill:#EBDEF0,stroke:#8E44AD
    style E fill:#FADBD8,stroke:#C0392B
    style F fill:#D5F5E3,stroke:#229954
    style G fill:#D5F5E3,stroke:#229954
    style H fill:#FCF3CF,stroke:#F39C12
    style I fill:#FDEDEC,stroke:#E74C3C
```

Key interactions include:
*   Creating and submitting detailed funding applications.
*   Uploading and managing supporting documents (evidence).
*   Tracking the status of applications and active claims.
*   Communicating with 3Pay Global agents who act as intermediaries for all external parties (including platform administrators and claimants).
*   Receiving notifications for important events and required actions.

### 2.2 Application Submission Workflow

Solicitors (who have achieved Permitted User - PU status) can initiate new funding applications. The process is designed to be guided and allow for saving drafts.

**User Flow: New Funding Application Submission**

```mermaid
sequenceDiagram
    participant S as Solicitor
    participant P as Platform (Flutter App)
    participant B as Backend (Pocketbase)

    S->>P: Clicks "Start New Funding Application"
    P->>S: Displays Multi-Step Application Form (Step 1: Basic Claim Info)
    S->>P: Enters Claim Title, Min. Value, Required Funding Amount, Claimant Type, Claim Industry, Claim Type
    P-->>S: Validates input locally
    S->>P: Clicks "Next" or "Save Draft"
    alt Save Draft
        P->>B: Saves application data to `funding_applications` (status: 'draft')
        B-->>P: Confirms save
        P-->>S: Shows "Draft Saved" notification
    else Proceed to Next Step
        P->>S: Displays Step 2: Document Upload (Schedule of Costs, CFA, Legal Opinion etc.)
        P-->>S: Provides links to download relevant document templates (from `document_templates`)
        S->>P: Uploads completed documents (e.g., Schedule of Costs, Legal Opinion)
        P->>B: Uploads files to Pocketbase storage, links to `funding_applications.uploaded_documents`
        B-->>P: Confirms file uploads
        S->>P: Confirms CFA, enters Legal Opinion Success Prospects
        P-->>S: Validates input
        S->>P: Clicks "Next" or "Save Draft"
        alt Save Draft
             P->>B: Saves application data & document links
             B-->>P: Confirms save
             P-->>S: Shows "Draft Saved" notification
        else Proceed to Final Review
            P->>S: Displays Step 3: Review & Submit
            S->>P: Reviews all entered information and uploaded documents
            S->>P: Clicks "Submit Application"
            P->>B: Submits application (updates `funding_applications.application_status` to 'submitted', `submission_date` to now)
            B-->>P: Confirms submission
            P-->>S: Shows "Application Submitted Successfully" notification
            P->>S: Redirects to Application Tracking / Dashboard
        end
    end
end
```

**Wireframe Description: New Application Form (Multi-Step)**

*   **Overall Layout:** A clean, tabbed or stepped interface (e.g., using shadcn_ui Stepper component). Each step focuses on a logical group of information. A progress indicator shows the current step and total steps. "Save Draft," "Previous," and "Next/Submit" buttons are consistently placed.
*   **Step 1: Claim Details**
    *   Fields:
        *   `Claim Title` (Text Input, Required, from [`funding_applications.claim_title`](DESIGN_DOCUMENT.md:123))
        *   `Minimum Value of Claim` (Number Input, Required, from [`funding_applications.minimum_value_claim`](DESIGN_DOCUMENT.md:124))
        *   `Required Funding Amount` (Number Input, Required, from [`funding_applications.required_funding_amount`](DESIGN_DOCUMENT.md:125))
        *   `Claimant Type` (Dropdown/Select, Required, New field for `funding_applications`)
            *   Options: Individual, Single Claimants, Group Claimants, Other Legal Entity, Other
        *   `Claim Industry` (Dropdown/Select, Required, New field for `funding_applications`)
            *   Options: Aerospace, Automotive, Aviation, Banking, insurance and other financial services, Chemicals, Construction and Housing, Construction Products, Consumer Goods, Creative, cultural, tourism and sport, Data Protection, Defence, Digital, technology and computer services, Drivers, Ecommerce, Education, Electricity including Renewables, Electronics, Machinery and Parts, Energy, Farming, food and drink sectors, Fisheries, Gas Markets, Health and Care Sector, Life Sciences, Media and Broadcasting, Mining and non-metal manufacturing, Natural environment, Nuclear, Oil and Gas Production, Parcel Delivery Services, Pesticides, Professional Business services, Public Procurement Policy, Retail, Research & Innovation, Space, Steel and metal manufacturing, Telecoms, Transport and haulage, Veterinary Sector
        *   `Claim Type` (Dropdown/Select, Required, New field for `funding_applications`)
            *   Options:
                *   Construction group:
                    *   Construction - Property Developer
                    *   Construction - Insurer
                    *   Construction - Other
                *   Professional Negligence group:
                    *   Professional Negligence - Solicitor
                    *   Professional Negligence - Barrister
                    *   Professional Negligence - Accountant
                    *   Professional Negligence - Auditor
                    *   Professional Negligence - Surveyor
                    *   Professional Negligence - Architect
                    *   Professional Negligence - Valuer
                    *   Professional Negligence - Other
        *   `Stage of Claim` (Dropdown/Select, from [`funding_applications.stage`](DESIGN_DOCUMENT.md:326-342))
    *   Clear labels, placeholder text, and validation messages.
    *   *Schema Note: The new fields `Claimant Type`, `Claim Industry`, and `Claim Type` will need to be added to the `funding_applications` collection in PocketBase as 'select' type fields with the specified options.*
*   **Step 2: Document Provision & Upload**
    *   Sections for each required document type (e.g., Schedule of Costs, Legal Opinion, Risk Committee Approval).
    *   For each document type:
        *   Link to download the relevant template (if applicable, from [`document_templates`](DESIGN_DOCUMENT.md:141-147)). Text indicating "Download Template (pre-filled where possible)".
        *   File Upload component (shadcn_ui File Upload or similar) for the solicitor to upload their completed document. Shows upload progress and success/failure status. (Stored in [`funding_applications.schedule_of_costs_doc_uploaded`](DESIGN_DOCUMENT.md:127), etc., and tracked in [`funding_applications.uploaded_documents`](DESIGN_DOCUMENT.md:139)).
    *   Checkbox: `Conditional Fee Agreement Confirmed` (Required, from [`funding_applications.conditional_fee_agreement_confirmed`](DESIGN_DOCUMENT.md:128)).
    *   Field: `Legal Opinion Success Prospects (%)` (Number Input, 0-100, Required, from [`funding_applications.legal_opinion_success_prospects`](DESIGN_DOCUMENT.md:133)).
*   **Step 3: Four Pillars Assessment (Internal - if applicable for solicitor input)**
    *   This section might be for internal platform use primarily, but if solicitors contribute to this assessment:
    *   Checkboxes or dropdowns for each of the "Four Pillars" (e.g., `Claimant & Solicitor OK?`, `Leading Counsel OK?`, `Defendant OK?`, `Enforcement OK?`). (Reflected in [`funding_applications.four_pillars_status`](DESIGN_DOCUMENT.md:138)).
*   **Step 4: Review & Submit**
    *   A read-only summary of all information entered in previous steps.
    *   Links to view/download uploaded documents.
    *   A final declaration checkbox: "I confirm all information provided is accurate to the best of my knowledge."
    *   Prominent "Submit Application" button. "Save Draft" and "Previous" buttons also available.

**Key Features & Benefits:**

*   **Guided Process:** Multi-step form simplifies a complex task.
*   **Draft Saving:** Allows solicitors to complete applications at their own pace.
*   **Template Provision:** Ensures consistency and provides necessary starting points for documentation.
*   **Clear Validation:** Immediate feedback on required fields and data formats.
*   **Centralized Uploads:** All application-related documents are managed in one place.

---
*(Further sections of Part 1, and Parts 2, 3, 4 will be detailed subsequently)*

### 2.3 Application/Claim Tracking & Status Updates

Solicitors need a clear and efficient way to monitor the progress of their submitted funding applications and any subsequent active cases.

**Purpose:** To provide solicitors with real-time visibility into the status of their applications and cases, enabling them to stay informed and take necessary actions promptly.

**Key Information Displayed:**
For each application/case, the following information should be readily accessible:
*   `Application/Case ID`: Unique identifier.
*   `Claim Title`: As provided by the solicitor.
*   `Submission Date` / `Case Start Date`.
*   `Current Status`: E.g., 'Draft', 'Submitted', 'Under Review', 'Requires Info', 'Approved', 'Rejected', 'Funded', 'Active - Pre-Action', 'Active - Discovery', etc. (from [`funding_applications.application_status`](DESIGN_DOCUMENT.md:134) or [`cases.current_status`](DESIGN_DOCUMENT.md:154)).
*   `Last Updated Date`: Timestamp of the most recent change or update.
*   `Next Steps / Action Required`: Clear indication if any action is pending from the solicitor.

**Wireframe Descriptions:**

*   **A. Application/Claim List View (Dashboard Widget or Dedicated Page):**
    *   **Layout:** A sortable and filterable table (using shadcn_ui Table) or a list of cards (using shadcn_ui Card).
    *   **Table Columns / Card Information:**
        *   `Claim Title` (Clickable, navigates to Detail View)
        *   `Application ID`
        *   `Submission Date` (or Case Start Date)
        *   `Current Status` (Displayed with a visually distinct badge, e.g., Green for 'Approved', Amber for 'Requires Info', Red for 'Rejected').
        *   `Last Updated`
        *   `Action Required` (Icon or text if action is needed from the solicitor)
    *   **Filtering Options:**
        *   By Status (Dropdown: All, Draft, Submitted, Under Review, Requires Info, Approved, Rejected, Funded, Active).
        *   By Date Range (Submission/Start Date).
        *   Keyword Search (on Claim Title, Application ID).
    *   **Sorting Options:**
        *   Submission Date (Asc/Desc).
        *   Last Update Date (Asc/Desc).
        *   Status.
    *   **Empty State:** Message like "You have no submitted applications yet. Start a new application."

*   **B. Application/Claim Detail View:**
    *   **Header:** Prominently display `Claim Title`, `Application ID`, and current `Status` with its visual badge.
    *   **Layout:** Tabbed interface (using shadcn_ui Tabs) for organized information:
        *   **Tab 1: Overview:**
            *   Key Dates: Submission Date, Decision Date (if applicable, from [`funding_applications.decision_date`](DESIGN_DOCUMENT.md:137)), Case Start Date.
            *   Summary of Claim (brief).
            *   Funding Amount Requested/Secured (from [`funding_applications.required_funding_amount`](DESIGN_DOCUMENT.md:125) / [`cases.total_funding_secured`](DESIGN_DOCUMENT.md:159)).
            *   Solicitor Firm, Lead Solicitor.
        *   **Tab 2: Status History & Notes:**
            *   A chronological log of all status changes. Each entry shows: `Date`, `Old Status`, `New Status`, `Updated By` (Platform/System), and any associated `Review Notes` (from [`funding_applications.review_notes`](DESIGN_DOCUMENT.md:136)) or `Status Update Notes` (from [`cases.status_updates`](DESIGN_DOCUMENT.md:155)).
        *   **Tab 3: Documents:** (Detailed in Section 2.4)
        *   **Tab 4: Communication:** (Detailed in Section 2.8)
        *   **Tab 5: Linked Professionals:** (Displays linked Barristers and Experts, see Part 3)
        *   **Tab 6: Audit Trail:** (Detailed in Section 2.7)
    *   **Actions:** Contextual actions like "Upload Supplementary Documents" (if status is 'Requires Info'), "Withdraw Application" (if applicable before certain stages).

**Status Updates & Notifications:**

*   Status changes are driven by platform administrators or automated system processes.
*   Solicitors receive notifications (in-app and optionally email, via [`notifications`](DESIGN_DOCUMENT.md:216-223) collection) for:
    *   Application submission confirmation.
    *   Change in application status (e.g., to 'Under Review', 'Requires Info', 'Approved', 'Rejected').
    *   Requests for additional information.
    *   Significant updates on active cases.
*   The `application_status` field in [`funding_applications`](DESIGN_DOCUMENT.md:134) and `current_status` in [`cases`](DESIGN_DOCUMENT.md:154) are the primary sources for status information.

**Benefits:**
*   **Transparency:** Solicitors have a clear view of where their applications/cases stand.
*   **Proactive Management:** Enables timely responses if information or action is required.
*   **Centralized Information:** All relevant details for an application/case are accessible from one place.

### 2.4 Evidence Management (Upload, Versioning, Simple Text Comments)

Robust evidence management is crucial for the funding application process and ongoing claim management.

**Purpose:** To provide solicitors with a secure and organized system for submitting, tracking, and managing all evidentiary and supporting documents, with simple text commenting capabilities.

**Core Features:**

*   **A. Document Upload:**
    *   **Initial Upload:** During the new application submission workflow (as detailed in Section 2.2).
    *   **Supplementary Uploads:** Ability to upload additional documents to an existing application (especially if its status is 'Requires Info') or to an active claim. An "Upload Document" button will be available on the Application/Claim Detail View (Documents Tab).
    *   **Supported File Types:** PDF and image formats (JPG, JPEG, PNG, GIF). System should clearly indicate supported types.
    *   **Mechanism:** Drag-and-drop support and a traditional file browser dialog.
    *   **Feedback:** Visual progress indicators during upload, and clear success or error messages.
    *   **Storage:** Files are stored using Pocketbase's file storage capabilities. Metadata is linked within the respective collection records.

*   **B. Document Versioning (Simple, Timestamp-based):**
    *   **Concept:** When a solicitor needs to update a previously uploaded document (e.g., a revised Schedule of Costs), they can upload a new version.
    *   **Mechanism:** The system will not strictly enforce filename uniqueness for versioning. Instead, if a solicitor uploads a document intended to supersede a previous one for a specific "document slot" (e.g., "Legal Opinion"), the platform will:
        *   Store the new file.
        *   The `uploaded_documents` JSON array in [`funding_applications`](DESIGN_DOCUMENT.md:139) or `document_repository` in [`cases`](DESIGN_DOCUMENT.md:156) will maintain a history for that logical document.
        *   Example structure within `uploaded_documents` for a single logical document:
            ```json
            {
              "logical_name": "Schedule of Costs", // User-defined or system-defined category
              "versions": [
                { "file_id": "file_id_v1", "filename": "Schedule_Costs_v1.pdf", "uploaded_at": "2024-01-15T10:00:00Z", "uploaded_by": "solicitor_user_id_1", "notes": "Initial submission." },
                { "file_id": "file_id_v2", "filename": "Schedule_Costs_revised.pdf", "uploaded_at": "2024-01-20T14:30:00Z", "uploaded_by": "solicitor_user_id_1", "notes": "Revised based on RFI." }
              ],
              "current_version_file_id": "file_id_v2"
            }
            ```
    *   **UI Display:** The document list will always show/link to the `current_version_file_id`. An option (e.g., a "Version History" link/icon) will allow viewing and downloading previous versions.

*   **C. Simple Text Comments (Document Metadata):**
    *   **Purpose:** Allow solicitors to add simple text-based contextual comments or descriptions to each uploaded document version. This is metadata, not in-document markup. Advanced in-document PDF annotation features (like highlighting, drawing, or threaded comments within the PDF) are not supported. Document review is facilitated by these text comments alongside document preview capabilities.
    *   **Mechanism:** When uploading a new document or a new version, or by editing an existing document entry, solicitors can add/edit these text comments.
    *   **Storage:** These comments are stored within the `versions` array in the JSON structure described above (e.g., the `notes` field, which represents the comment).
    *   **UI Display:** Text comments are visible alongside the document details in the document list.

*   **D. Organization & Viewing:**
    *   **Location:** Primarily within the "Documents" tab of the Application/Claim Detail View.
    *   **Display:** Documents listed in a clear, organized manner (e.g., table or list of cards).
    *   Information per document: Logical Name/Category, Current Filename (link to download/preview), Upload Date (of current version), Uploaded By, Version (e.g., "v2 - 2024-01-20" or link to history), Text Comment (preview or full).
    *   **Actions per document:** Download, Preview, Upload New Version, Edit Text Comment, View Version History.
    *   **Preview:** Basic preview for common file types (PDFs, images) within a modal or dedicated view is essential for document review.

**Wireframe Descriptions:**

*   **A. Documents Tab within Application/Claim Detail View:**
    *   **Layout:** A table listing all logical documents associated with the application/claim.
    *   **Table Columns:**
        *   `Document Category/Logical Name` (e.g., "Schedule of Costs", "Legal Opinion", "Witness Statement 1")
        *   `Current Filename` (Clickable to download/preview the latest version)
        *   `Last Updated` (Timestamp of the latest version)
        *   `Uploaded By` (Name/ID of the user who uploaded the latest version)
        *   `Comment` (Short preview of the latest text comment, expandable)
        *   `Actions` (Icon buttons for: "Upload New Version", "View History / Versions", "Edit Comment", "Download Latest", "Preview")
    *   **Overall Actions:** A prominent "Upload New Document" button at the top of the tab for adding entirely new evidentiary items not fitting existing categories.

*   **B. Upload New Document/Version Modal:**
    *   **Context:** Clearly states if it's for a new logical document or a new version of an existing one.
    *   If new logical document: Field for `Document Category/Logical Name`.
    *   Standard file upload component (drag & drop, browse).
    *   Text area for `Text Comment / Version Notes`.
    *   "Upload" and "Cancel" buttons.

*   **C. Version History Modal:**
    *   Triggered by "View History / Versions" action.
    *   Lists all versions of a specific logical document chronologically (newest first).
    *   For each version: Filename, Upload Date, Uploaded By, Text Comment for that version, Download link.

**Data Storage & Collections:**
*   Files physically stored by Pocketbase.
*   Metadata, including version history and text comments, managed within the `uploaded_documents` JSON field of the [`funding_applications`](DESIGN_DOCUMENT.md:139) collection and the `document_repository` JSON field of the `claims` collection. The structure proposed above (logical name with an array of versions, where `notes` field holds the text comment) would be implemented within these JSON fields.

**Benefits:**
*   **Organized Evidence:** All documents related to a claim are centrally managed and easily accessible.
*   **Clear Audit Trail:** Version history provides a clear record of document changes over time.
*   **Contextual Information:** Simple text comments allow solicitors to add important context to documents, aiding review.
*   **Reduced Ambiguity:** Clear distinction between different versions of a document.

### 2.5 Review Processes - Solicitor's View

Once an application is submitted, it undergoes review by 3Pay Global agents (acting as administrators/underwriters). Solicitors need visibility into this process and a clear way to respond if further information is required.

**Purpose:** To facilitate a transparent and efficient review cycle, allowing solicitors to understand review outcomes and address any queries or requirements from 3Pay Global agents.

**Key Interactions & Information for Solicitors:**

*   **Notification of Status Change:** Solicitors are notified (in-app, email) when an application's status changes to 'Requires Info', 'Approved', or 'Rejected'.
*   **Access to Review Notes:** If an application is marked as 'Requires Info' or 'Rejected' by a 3Pay Global agent, any accompanying review notes or reasons (from [`funding_applications.review_notes`](DESIGN_DOCUMENT.md:136)) are made visible to the solicitor.
*   **Responding to 'Requires Info':**
    *   The application detail view will clearly highlight what additional information or documents are needed, based on feedback from the 3Pay Global agent.
    *   Solicitors can upload supplementary documents (as per Section 2.4).
    *   They may need to update certain fields in the application form (the form should become editable for relevant sections).
    *   A "Resubmit Application" action becomes available.
*   **Viewing Decision:** For 'Approved' or 'Rejected' statuses, the decision date ([`funding_applications.decision_date`](DESIGN_DOCUMENT.md:137)) and any final notes from the 3Pay Global agent are displayed.

**User Flow: Responding to 'Requires Info'**

```mermaid
sequenceDiagram
    participant Agent as 3Pay Global Agent (Admin/Underwriter)
    participant P as Platform (Flutter App)
    participant S as Solicitor

    Agent->>P: Reviews Application, sets status to 'Requires Info'
    P->>P: Updates `funding_applications.application_status` to 'Requires Info'
    P->>P: Adds review notes from Agent to `funding_applications.review_notes`
    P->>S: Sends Notification: "Application [ID] Requires Additional Information"
    S->>P: Opens Application Detail View
    P->>S: Displays current status 'Requires Info' and review notes from Agent
    P->>S: Enables relevant form sections for editing / supplementary document uploads
    S->>P: Edits application fields / Uploads new documents
    S->>P: Clicks "Resubmit Application"
    P->>P: Updates `funding_applications` with new data, sets status back to 'Submitted' (or 'Under Review - Updated')
    P->>S: Shows "Application Resubmitted Successfully" notification
    P->>Agent: Notifies Agent of resubmission
end
```

**Wireframe Description: Application Detail View (When 'Requires Info')**

*   **Status Display:** The 'Requires Info' status is prominently displayed, possibly with an alert icon.
*   **Review Notes Section:** A clearly demarcated section showing the notes from the 3Pay Global agent, detailing what is needed. This could be part of the "Status History & Notes" tab or a dedicated "Review Feedback" section.
*   **Actionable Items:**
    *   If specific documents are requested by the agent, links or prompts to the document upload section.
    *   If form fields need updating based on agent feedback, those fields in the "Overview" or relevant tabs become editable.
    *   A "Resubmit Application with Updates" button is clearly visible.
    *   Option to "Withdraw Application" might still be present.

**Benefits:**
*   **Clarity on Requirements:** Solicitors understand exactly what's needed (based on 3Pay Global agent feedback) to move their application forward.
*   **Efficient Response:** Direct pathway to provide missing information or documents.
*   **Reduced Back-and-Forth:** Clear communication of review feedback from 3Pay Global agents minimizes misunderstandings.

### 2.6 Resolution

This section covers the solicitor's view of an application's final resolution: approval (leading to a funded claim) or rejection.

**Purpose:** To clearly communicate the final outcome of a funding application to the solicitor, as determined by 3Pay Global.

*   **Application Approved:**
    *   **Notification:** Solicitor receives a notification: "Application [ID] for [Claim Title] has been Approved."
    *   **Status Update:** Application status in the list and detail view changes to 'Approved' (and subsequently 'Funded' once financial processes are complete, then a `claim` record is created in the `claims` collection, formerly `cases`).
    *   **Claim Creation:** Upon approval and completion of any pre-funding conditions, a corresponding record is created in the `claims` (formerly `cases`) collection (see [`DESIGN_DOCUMENT.md:148-161`](DESIGN_DOCUMENT.md:148-161), assuming `cases` collection is now `claims`). The solicitor will then manage this as an active claim (see Part 3).
    *   **Information Displayed:**
        *   Decision Date ([`funding_applications.decision_date`](DESIGN_DOCUMENT.md:137)).
        *   Any final approval notes from 3Pay Global agents.
        *   Link to the newly created Claim record (once available).
*   **Application Rejected:**
    *   **Notification:** Solicitor receives a notification: "Application [ID] for [Claim Title] has been Rejected."
    *   **Status Update:** Application status changes to 'Rejected'.
    *   **Information Displayed:**
        *   Decision Date.
        *   Reason for rejection (from [`funding_applications.review_notes`](DESIGN_DOCUMENT.md:136) or a dedicated rejection reason field if added, provided by 3Pay Global agents).
    *   The application remains in the solicitor's list for historical reference but is clearly marked as inactive.

**Wireframe Considerations:**
*   The Application Detail View will reflect the final status.
*   For approved applications, a clear call to action or link to "View Claim Details" will appear once the claim is created.
*   For rejected applications, the reasons for rejection are displayed clearly and empathetically.

**Benefits:**
*   **Definitive Outcomes:** Solicitors are unambiguously informed of the funding decision.
*   **Smooth Transition (for Approved):** Seamless progression from approved application to active claim management.
*   **Learning Opportunity (for Rejected):** Clear reasons for rejection can help solicitors in future applications.

### 2.7 Audit Trails

Maintaining a comprehensive audit trail is essential for transparency, accountability, and potential dispute resolution.

**Purpose:** To provide a chronological record of all significant actions and changes related to a funding application or claim, accessible to authorized users.

**Key Information Logged (Examples):**
The [`user_activity_logs`](DESIGN_DOCUMENT.md:177-183) collection will be the primary source, supplemented by specific status history fields in `funding_applications` and the `claims` collection.
*   Application creation (`action`: 'create_application', `details`: {application_id}).
*   Application submission (`action`: 'submit_application').
*   Application status changes (e.g., 'draft' -> 'submitted', 'submitted' -> 'under_review', 'under_review' -> 'requires_info', etc.). Logged with old and new status.
*   Document uploads/new versions (`action`: 'upload_document', `details`: {application_id, document_name, file_id, version}).
*   Review notes added/updated by 3Pay Global agent.
*   Solicitor resubmission of an application.
*   Application approval/rejection.
*   Claim creation.
*   Claim status updates.
*   Significant changes to claim details by authorized personnel.
*   Communication sent/received with 3Pay Global agents (metadata).

**Solicitor's View of Audit Trail:**

*   **Location:** A dedicated "Audit Trail" or "History" tab within the Application/Claim Detail View.
*   **Display:** A chronological list of events, newest first.
    *   Each entry shows: `Timestamp`, `User` (e.g., "Solicitor: [Name]", "3Pay Global Agent", "System"), `Action Performed`, and `Details/Changes` (e.g., "Status changed from 'Submitted' to 'Under Review'", "Document 'Legal Opinion v2.pdf' uploaded").
*   **Filtering/Search (Basic):** Potentially filter by date range or action type if the trail becomes very long.
*   **Read-Only:** The audit trail is for viewing only by solicitors; they cannot edit or delete entries.

**Wireframe Description: Audit Trail Tab**

*   **Layout:** A simple, clean list or table.
*   **Columns/Information per entry:**
    *   `Date & Time`
    *   `User / Actor` (e.g., "You", "3Pay Global Agent", "System")
    *   `Action Description` (e.g., "Application Submitted", "Status changed to 'Requires Info'", "Document 'Evidence_Bundle.pdf' uploaded")
    *   `Details` (Optional: brief summary of changes, e.g., "Reason: Missing financial statements")
*   **No interactive elements other than potential basic filtering.**

**Benefits:**
*   **Full Transparency:** All parties can see a history of actions.
*   **Accountability:** Tracks who did what and when.
*   **Dispute Resolution:** Provides an objective record if questions arise.
*   **Compliance:** Helps meet regulatory requirements for record-keeping.

### 2.8 Integrated Communication (via 3Pay Global Agents) per Application/Claim

All external communication for solicitors (with claimants, co-funders, and platform administration) is centralized through 3Pay Global agents.

**Purpose:** To provide a secure, centralized, and context-specific communication channel between solicitors and 3Pay Global agents for each funding application and active claim. 3Pay Global agents then act as intermediaries for communication with other parties like claimants and co-funders.

**Key Features:**

*   **Contextual Communication:** Communication threads are tied directly to a specific application or claim.
*   **Participants in Solicitor-Facing Thread:**
    *   Solicitor(s) associated with the application/claim.
    *   3Pay Global Agent(s) assigned to the application/claim.
*   **No Direct Communication:** Solicitors do not directly communicate with claimants or co-funders via the platform. All such communication is routed through 3Pay Global agents.
*   **Mechanism:**
    *   A "Communication with 3Pay Agent" tab within the Application/Claim Detail View.
    *   A threaded message interface for solicitor-agent communication.
    *   Ability for solicitors to send messages to the 3Pay Global agent and potentially attach documents relevant to their discussion with the agent.
*   **Notifications:** Solicitors receive notifications (in-app, email) for new messages from 3Pay Global agents.
*   **Message Storage:** Communication metadata (sender, receiver, timestamp, related application/claim ID) and message content stored in a collection like `claim_communications` (or `application_communications`), linking to `funding_applications` and `claims` collections. The `recipient_group` would be 'agent' or specific agent IDs.

**User Flow: Solicitor Sends a Message to 3Pay Global Agent regarding an Application**

```mermaid
sequenceDiagram
    participant S as Solicitor
    participant P as Platform (Flutter App)
    participant B as Backend (Pocketbase)
    participant Agent as 3Pay Global Agent

    S->>P: Navigates to Application Detail View, Clicks "Communication with 3Pay Agent" Tab
    P->>B: Fetches existing messages between Solicitor and Agent for this application
    B-->>P: Returns message history
    P->>S: Displays message thread with Agent
    S->>P: Types message in input field, Clicks "Send to Agent"
    P->>B: Saves new message to `application_communications` (linking to application_id, sender_id: solicitor, recipient_id: agent_id/agent_group)
    B-->>P: Confirms message saved
    P->>S: Appends new message to local thread
    B->>Agent: (Via system notification) Notifies Agent of new message from Solicitor regarding Application [ID]
end
```

**Wireframe Description: Communication with 3Pay Agent Tab**

*   **Layout:**
    *   Main panel displaying the chronological message thread between the solicitor and the 3Pay Global agent. Each message shows sender (Solicitor or Agent Name), timestamp, and content.
    *   Text input area at the bottom for typing new messages to the agent.
    *   "Send Message to Agent" button.
    *   Optional: A small button for attaching a file to the message for the agent's attention.
*   **Participants List (Implicit):** The communication is clearly between the solicitor and the assigned 3Pay Global Agent(s) for that specific application/claim.
*   **Read Receipts (Optional):** Simple indicators if messages have been seen by the agent.

**Benefits:**
*   **Centralized & Auditable Communication:** All official communication regarding an application/claim is channeled through 3Pay Global agents and recorded.
*   **Clarity of Roles:** Clear distinction that 3Pay Global agents manage external communication.
*   **Secure:** Uses the platform's secure infrastructure for sensitive discussions.
*   **Streamlined Process:** Solicitors have a single point of contact (the 3Pay Global agent) for all queries and updates related to external parties.

---
*(End of Part 1. Part 2: Solicitor Dashboard will follow.)*

## 3. Part 2: Solicitor Dashboard

The Solicitor Dashboard is envisioned as the primary landing page for solicitors after logging in. It serves as a personalized and actionable hub, providing an at-a-glance overview of their most critical information and quick access to essential functions.

### 3.1 Concept & Purpose

**Concept:** A dynamic, widget-based dashboard tailored to the solicitor's immediate needs and priorities. It should surface relevant information proactively, reducing the need to navigate through multiple sections for routine checks.

**Purpose:**
*   **Efficiency:** Allow solicitors to quickly grasp their current workload and pending items.
*   **Actionability:** Highlight tasks requiring attention and provide direct links to address them.
*   **Overview:** Offer a summary of active cases, application statuses, and key performance indicators.
*   **Engagement:** Serve as a central point for accessing various platform functionalities.

### 3.2 Key Sections/Widgets

The dashboard will be composed of several distinct widgets or sections, each serving a specific informational or functional purpose. The layout should be customizable or intelligently prioritized based on user activity or urgency.

1.  **My Active Cases (Widget):**
    *   **Purpose:** Display a summary of cases currently funded and being managed by the solicitor/firm.
    *   **Content:** A concise list/card view of 3-5 most recently updated active cases. Each entry shows:
        *   `Case Title` (Clickable, links to Case Detail View)
        *   `Current Status` (e.g., 'Pre-Action', 'Discovery')
        *   `Next Key Date` (if applicable, e.g., upcoming court date, deadline)
        *   `Unread Communications/Updates` (Indicator)
    *   **Action:** "View All Active Cases" link.
    *   **Data Source:** [`cases`](DESIGN_DOCUMENT.md:148-161) collection, filtered for the solicitor's firm and active statuses.

2.  **Pending Funding Applications (Widget):**
    *   **Purpose:** Track the status of submitted funding applications that are not yet resolved (approved/rejected).
    *   **Content:** List/card view of applications with statuses like 'Submitted', 'Under Review', 'Requires Info'. Each entry shows:
        *   `Claim Title` (Clickable, links to Application Detail View)
        *   `Application ID`
        *   `Current Status` (e.g., 'Under Review', 'Requires Info')
        *   `Submission Date`
        *   `Action Required` (Highlight if 'Requires Info')
    *   **Action:** "View All Applications" link.
    *   **Data Source:** [`funding_applications`](DESIGN_DOCUMENT.md:121-140) collection, filtered for the solicitor and relevant statuses.

3.  **Pending Tasks / Actions Required (Widget):**
    *   **Purpose:** Aggregate all items across the platform that require the solicitor's direct attention.
    *   **Content:** A consolidated list of tasks, such as:
        *   "Application [ID] requires additional information" (Links to Application Detail View)
        *   "New message regarding Case [ID]" (Links to Case Communication Tab)
        *   "PU Status document expiring soon" (If applicable)
        *   "Unread important notifications"
    *   Each task item should be a direct link to the relevant section/item.
    *   **Data Source:** Aggregated from [`notifications`](DESIGN_DOCUMENT.md:216-223) (type: 'action_required'), applications in 'Requires Info' status, etc.

4.  **Urgent Notifications (Widget):**
    *   **Purpose:** Display critical system alerts or time-sensitive notifications.
    *   **Content:** A list of the most recent unread urgent notifications (from [`notifications`](DESIGN_DOCUMENT.md:216-223) where `type` = 'alert' or similar high-priority designation).
        *   Brief message snippet.
        *   Timestamp.
        *   Link to view full notification or related item.
    *   **Action:** "View All Notifications" link.

5.  **Key Performance Indicators (KPIs) (Widget - Optional, could be a separate "Reports" link):**
    *   **Purpose:** Provide a quick visual summary of performance.
    *   **Content (Examples):**
        *   `Total Active Cases`: Number.
        *   `Applications Submitted (Last 30 Days)`: Number.
        *   `Application Success Rate (Overall)`: Percentage.
        *   `Average Funding Amount Secured (Per Approved Case)`: Currency.
    *   **Display:** Simple numerical stats or small charts (e.g., sparklines, mini bar charts).
    *   **Action:** "View Detailed Reports" link.
    *   **Data Source:** Aggregated data from [`funding_applications`](DESIGN_DOCUMENT.md:121-140) and [`cases`](DESIGN_DOCUMENT.md:148-161).

6.  **Quick Access / Common Actions (Widget/Section):**
    *   **Purpose:** Provide direct links to frequently used functionalities.
    *   **Content:** Buttons or prominent links for:
        *   "Start New Funding Application"
        *   "View My PU Status"
        *   "Manage Firm Users" (If the solicitor has admin rights for their firm)
        *   "Access Document Repository" (Firm-level documents)
        *   "Help & Support"

### 3.3 Wireframe (Conceptual Dashboard Layout)

```
+--------------------------------------------------------------------------+
| Solicitor Dashboard                                     [User Profile] |
| [Firm Name]                                                              |
+--------------------------------------------------------------------------+
| Quick Access: [Start New Application] [View PU Status] [Manage Users]    |
+--------------------------------------------------------------------------+
| Row 1:                                                                   |
| +-----------------------------------+  +-----------------------------------+
| | Pending Tasks / Actions (Scroll)  |  | Urgent Notifications (Scroll)     |
| | - App X Requires Info [Link]      |  | - Critical Alert 1 [Link]         |
| | - New Message on Claim Y [Link]   |  | - Alert: PU Doc Expiring [Link]   |
| | - ...                             |  | - ...                             |
| +-----------------------------------+  +-----------------------------------+
+--------------------------------------------------------------------------+
| Row 2:                                                                   |
| +------------------------------------------------------------------------+
| | My Active Claims                                    [View All Claims] |
| | +-----------------+ +-----------------+ +-----------------+            |
| | | Claim A         | | Claim B         | | Claim C         | (Card View)|
| | | Status: Discovery| | Status: Pre-Act | | Status: Trial   |            |
| | | Next: 15/07/24  | | Unread Agent Msg: 2 | | ...         |            |
| | +-----------------+ +-----------------+ +-----------------+            |
| +------------------------------------------------------------------------+
+--------------------------------------------------------------------------+
| Row 3:                                                                   |
| +------------------------------------------------------------------------+
| | Pending Funding Applications                   [View All Applications] |
| | +-----------------+ +-----------------+ +-----------------+            |
| | | App Alpha       | | App Beta        | | App Gamma       | (Card View)|
| | | Status: Under Rev| | Status: Req.Info| | Status: Submitted|            |
| | | Submitted: 01/06| | Action: Yes     | | Submitted: 10/06|            |
| | +-----------------+ +-----------------+ +-----------------+            |
| +------------------------------------------------------------------------+
+--------------------------------------------------------------------------+
| Row 4 (Optional KPIs):                                                   |
| +----------------------+ +----------------------+ +----------------------+
| | Total Active Claims  | | Apps Submitted (30d) | | Success Rate         |
| | Value: 12            | | Value: 5             | | Value: 75%           |
| +----------------------+ +----------------------+ +----------------------+
+--------------------------------------------------------------------------+
```

**Notes on Wireframe:**
*   This is a conceptual layout. Actual implementation would use shadcn_ui components for a polished look and feel.
*   "Card View" suggests using `Card` components for list items for better visual separation.
*   Scrollable sections for lists that might exceed available space.
*   User Profile link would lead to profile and notification settings.

### 3.4 Feature Descriptions (Dashboard Widgets)

*   **My Active Claims Widget:**
    *   **Purpose:** Quick insight into ongoing funded claims.
    *   **Interaction:** Clicking a claim title navigates to the full Claim Detail view. "View All Claims" links to a dedicated page listing all active claims with full filtering/sorting.
    *   **Benefits:** Helps solicitors prioritize work on active matters.

*   **Pending Funding Applications Widget:**
    *   **Purpose:** Monitor progress of applications awaiting decision.
    *   **Interaction:** Clicking an application title navigates to its Application Detail view. "View All Applications" links to the full application list. Highlighting "Action Required" for 'Requires Info' status prompts immediate attention.
    *   **Benefits:** Keeps solicitors informed about their pipeline and any bottlenecks.

*   **Pending Tasks / Actions Required Widget:**
    *   **Purpose:** Centralizes all items needing solicitor input.
    *   **Interaction:** Each task is a direct link to the relevant page/modal where the action can be performed. Tasks are removed or updated once addressed. Communication tasks link to the thread with the 3Pay Global Agent.
    *   **Benefits:** Prevents tasks from being overlooked and improves responsiveness.

*   **Urgent Notifications Widget:**
    *   **Purpose:** Ensure critical alerts are seen promptly.
    *   **Interaction:** Clicking a notification marks it as read (potentially) and navigates to more details or the related item.
    *   **Benefits:** Timely awareness of important system messages or critical events.

*   **Key Performance Indicators (KPIs) Widget:**
    *   **Purpose:** Offer a snapshot of operational performance.
    *   **Interaction:** Primarily informational. A "View Detailed Reports" link would navigate to a more comprehensive reporting section.
    *   **Benefits:** Provides data-driven insights for the solicitor/firm.

*   **Quick Access / Common Actions Section:**
    *   **Purpose:** Reduce clicks for frequently performed tasks.
    *   **Interaction:** Direct navigation to the respective functionalities.
    *   **Benefits:** Improves platform navigation efficiency.

---
*(End of Part 2. Part 3: Core Solicitor-Specific Functionalities will follow.)*

## 4. Part 3: Core Solicitor-Specific Functionalities

This part details functionalities that extend beyond individual application/claim management and the dashboard, catering to the broader needs of solicitors and their firms using the platform. Communication with external parties (claimants, co-funders) is managed via 3Pay Global agents.

### 4.1 Comprehensive Claim Management

Once a funding application is approved and transitions into an active claim (stored in the `claims` collection, formerly `cases` - see [`DESIGN_DOCUMENT.md:148-161`](DESIGN_DOCUMENT.md:148-161) assuming this update), solicitors require tools to manage these ongoing matters effectively.

**Purpose:** To provide a centralized system for overseeing all aspects of active funded claims, including status tracking, document management, internal team collaboration, and linking relevant professionals.

**Key Features:**

*   **Dedicated "My Claims" Section:** A main navigation item leading to a list of all active and closed claims associated with the solicitor's firm.
    *   **List View:** Similar to the Application List View (Section 2.3.A), offering robust sorting (by claim title, start date, status, last update) and filtering (by status, claimant (as recorded), date range, keyword).
    *   Each claim entry links to a detailed Claim View.
*   **Claim Detail View:** An expanded version of the Application Detail View, tailored for active claims. It would include tabs for:
    *   **Overview:** Claim Title, Claim ID, Current Status (e.g., 'Pre-Action', 'Discovery', 'Trial Prep', 'Settled', 'Closed' - from `claims.current_status`), Key Dates (start date, estimated next milestone date), Lead Solicitor from firm, Recorded Claimant(s), Total Funding Secured (`claims.total_funding_secured`), Public Summary (`claims.claim_summary_public`).
    *   **Status Updates & Milestones:** Chronological log of claim progress updates (from `claims.status_updates`). Ability for the solicitor to add new status updates/milestones (for internal tracking and reporting to 3Pay Global agents).
    *   **Document Repository:** (As per Section 2.4, but specific to `claims.document_repository`). This is the central store for all claim-related documents post-funding approval.
    *   **Linked Professionals:**
        *   **Claimants (Recorded):** Lists claimants associated with the claim as recorded in the system ([`claimant_profiles`](DESIGN_DOCUMENT.md:115-120) linked via `claimant_profiles.associated_claim_id`). Management of claimant data is for record-keeping; communication is via 3Pay Global agents.
        *   **Barristers:** List linked barristers (from `barristers` collection). Ability to search and link existing barristers or add new barrister profiles on the fly.
        *   **Experts:** List linked experts (from `experts` collection). Ability to search and link existing experts or add new expert profiles.
        *   **Firm Members:** Assign other users from the solicitor's firm ([`solicitor_profiles.additional_users`](DESIGN_DOCUMENT.md:90)) to the claim, potentially with different roles/access levels within the claim context (e.g., lead, support).
    *   **Communication (with 3Pay Global Agent):** (As per Section 2.8, but specific to the claim). Thread for communication with the 3Pay Global agent assigned to this claim.
    *   **Cost Tracking (Basic):** A section to log key cost information or link to external cost management tools if integrated. (Reflected in `claims.cost_tracking_info`). This might initially be simple notes or structured fields for budget vs. actuals.
    *   **FRFR Schedule (View):** Display the Expected FRFR Schedule (`claims.expected_frfr_schedule`).
    *   **Audit Trail:** (As per Section 2.7, specific to the claim).

**User Flow: Assigning a Barrister to an Active Claim**

```mermaid
sequenceDiagram
    participant S as Solicitor
    participant P as Platform (Flutter App)
    participant B as Backend (Pocketbase)

    S->>P: Navigates to Claim Detail View, Clicks "Linked Professionals" Tab
    S->>P: Clicks "Add Barrister"
    P->>S: Displays "Add Barrister to Claim" Modal
    P->>S: Option to "Search Existing Barristers" or "Add New Barrister Profile"

    alt Search Existing
        S->>P: Enters search query (name, chambers)
        P->>B: Searches `barristers` collection
        B-->>P: Returns matching barristers
        P->>S: Displays search results
        S->>P: Selects a barrister, Clicks "Link to Claim"
        P->>B: Updates `claims` record to link selected barrister (and `barristers` record to link claim)
        B-->>P: Confirms link
        P->>S: Shows "Barrister Linked" notification, updates UI
    else Add New Barrister Profile
        S->>P: Clicks "Add New Barrister Profile"
        P->>S: Displays form for Barrister details (Name, Chambers, Email)
        S->>P: Fills form, Clicks "Save and Link to Claim"
        P->>B: Creates new record in `barristers` collection
        B-->>P: Returns new barrister ID
        P->>B: Updates `claims` record to link new barrister
        B-->>P: Confirms link
        P->>S: Shows "Barrister Added and Linked" notification, updates UI
    end
end
```

**Wireframe Descriptions:**

*   **A. "My Claims" List View:**
    *   **Layout:** Table or card list similar to "My Applications".
    *   **Columns/Info:** Claim Title, Claim ID, Claimant(s) (Recorded), Current Status, Start Date, Last Update, Action Required (e.g., message from Agent).
    *   **Actions:** "View Details", potentially quick actions like "Add Update (for Agent)".
    *   **Filtering/Sorting:** By status, claimant (recorded), date, solicitor in charge (if multiple firm users).

*   **B. Claim Detail View - "Linked Professionals" Tab:**
    *   **Sections:** Separate sections for "Claimants (Recorded)", "Barristers", "Experts", "Firm Members".
    *   **Each Section:**
        *   Lists currently linked individuals/entities with key details (e.g., Name, Role/Firm, Contact).
        *   "Add [Claimant (Record)/Barrister/Expert/Firm Member]" button.
        *   Actions per linked entity (e.g., "View Profile", "Unlink from Claim"). Claimant permissions are not managed here as direct access is removed.

**Benefits:**
*   **Holistic View:** All claim-related information and parties in one place for the solicitor's reference.
*   **Efficient Collaboration (Internal):** Easy to assign and manage internal team members. External professional linking is for record and for 3Pay Global Agent awareness.
*   **Streamlined Information Flow:** Information for claimants is relayed via 3Pay Global agents.

### 4.2 Communication Model: Internal Collaboration and External via 3Pay Global Agents

As established in Section 2.8, all external communication (with claimants, co-funders, platform administration) is managed by 3Pay Global agents. This section focuses on how solicitors interact with this model and manage internal firm collaboration.

**Purpose:** To define clear communication pathways for solicitors, ensuring all external interactions are routed through 3Pay Global agents, while providing tools for effective internal firm collaboration.

**Key Features:**

*   **A. External Communication (Managed by 3Pay Global Agents):**
    *   **Solicitor to 3Pay Global Agent:** Solicitors communicate directly with their assigned 3Pay Global agent for all matters concerning an application or active claim (as detailed in Section 2.8 and 4.1 Claim Detail View > "Communication with 3Pay Global Agent" tab).
    *   **Information Relay:** Solicitors provide all necessary information, updates, and documents intended for claimants or co-funders to the 3Pay Global agent.
    *   **Agent as Intermediary:** The 3Pay Global agent is responsible for:
        *   Relaying relevant information to and from claimants (e.g., via the Claimant Portal, if applicable, or other designated channels).
        *   Relaying relevant information to and from co-funders.
    *   **No Direct Solicitor-Client/Co-funder Communication via Platform:** The platform does not facilitate direct messaging between solicitors and claimants/co-funders.

*   **B. Internal Firm Communication/Collaboration:**
    *   **Purpose:** Allow users from the same solicitor firm (linked via [`solicitor_profiles.additional_users`](DESIGN_DOCUMENT.md:90)) to communicate and collaborate securely within the platform, primarily in the context of specific claims.
    *   **Mechanism (Claim-Centric Internal Notes/Discussion):**
        *   Within each Claim Detail View, a dedicated "Internal Firm Notes" or "Internal Discussion" tab could be provided.
        *   This area would only be visible to users from the same solicitor firm who are assigned to or have access to that claim.
        *   Features: Threaded discussions, ability to @mention firm colleagues, share internal-only drafts or notes related to the claim. These are not visible to 3Pay Global agents or any external parties.
        *   This allows the solicitor team to strategize and prepare information before officially communicating updates or documents to the 3Pay Global agent.
    *   **Mechanism (General Firm Chat - Future Enhancement):** A general, non-claim-specific internal chat for the firm could be considered as a future enhancement if there's a strong need beyond claim-centric collaboration.
    *   **File Sharing (Internal):** Ability to share documents within these internal collaboration spaces. These would be for internal working purposes only.

**Wireframe Considerations:**

*   **Claim Detail View - "Communication with 3Pay Global Agent" Tab:** (As described in Section 2.8)
*   **Claim Detail View - "Internal Firm Notes/Discussion" Tab (New):**
    *   Layout similar to a chat/forum thread.
    *   Clearly labeled as "Internal - Visible to [Your Firm Name] Only".
    *   Input field for new notes/messages.
    *   List of internal participants on the claim.

**Benefits:**
*   **Controlled External Communication:** Ensures all external communication is managed, logged, and consistent through 3Pay Global agents.
*   **Secure Internal Collaboration:** Provides a dedicated space for solicitor firms to discuss claims internally before engaging with 3Pay Global agents.
*   **Clear Audit Trails:** Communication with agents is logged per claim. Internal discussions can also be logged for firm records.
*   **Reduced Miscommunication:** Centralizing external comms via agents minimizes the risk of conflicting information.
*   **Improved Teamwork:** Facilitates better collaboration among solicitor firm members on claims.

### 4.3 Robust Document Management (Beyond Individual Claims - Firm-Level)

Solicitors and their firms often have documents that are not tied to a specific application or claim but are important for their practice or interaction with 3Pay Global (e.g., PU certification, firm-wide templates, general correspondence with 3Pay Global agents).

**Purpose:** To provide a centralized, secure repository for the solicitor's firm to manage general business documents.

**Key Features:**

*   **Dedicated "Firm Documents" or "Document Repository" Section:** Accessible from the main solicitor portal navigation.
*   **Folder Structure:**
    *   Ability for solicitors (with appropriate firm admin rights) to create a folder structure to organize documents (e.g., "Compliance Documents," "Internal Templates," "Platform Correspondence").
    *   Standard folder operations: Create, Rename, Delete (with confirmations).
*   **Document Upload:**
    *   Similar to case document upload (Section 2.4.A), supporting various file types.
    *   Ability to upload documents into specific folders.
*   **Document Listing & Viewing:**
    *   List documents within folders, showing: Filename, Upload Date, Uploaded By, File Size, Type.
    *   Sorting and filtering options (by name, date, type).
    *   Preview capabilities for common file types.
*   **Version Control (Simple):**
    *   When uploading a file with the same name into the same folder, prompt the user: "A file with this name already exists. Would you like to replace it (create a new version) or rename the uploaded file?"
    *   If "Replace" is chosen, the old file is archived (or previous versions are accessible via a history, similar to claim documents but perhaps simpler).
    *   **Access Control (Within the Firm):**
        *   The primary PU solicitor for the firm has full access.
        *   They can potentially define permissions for `additional_users` from their firm (e.g., read-only, read/write) on a per-folder basis or for the entire repository. This requires careful consideration of granularity.
        *   *Initial Scope:* All firm users might have read/write access, with the PU having delete rights. Granular folder permissions can be a future enhancement.
    *   **Search:** Ability to search for documents within the firm repository by filename or keywords within document content (if advanced search is integrated).

**Wireframe Description:**

*   **A. Firm Document Repository - Main View:**
    *   **Layout:** Two-panel layout:
        *   Left Panel: Folder tree navigation (shadcn_ui Tree view or similar).
        *   Right Panel: List of files and sub-folders within the selected folder. Displays items in a table or grid view.
    *   **Actions:** "Upload Document," "New Folder" buttons.
    *   **File/Folder List Columns:** Icon (folder/file type), Name, Last Modified, Size, Actions (Download, Rename, Delete, View Versions, Edit Permissions - if applicable).

*   **B. Upload Document Modal (to Firm Repository):**
    *   File selection component.
    *   Dropdown/selector to choose the target folder (defaults to current folder).
    *   Optional field for "Description/Notes" about the document.
    *   "Upload" button.

**Data Storage:**
*   A new Pocketbase collection, e.g., `firm_documents`.
    *   `firm_id` (relation to `solicitor_profiles` - the main firm profile)
    *   `folder_path` (text, e.g., "/Compliance/Certificates/")
    *   `file_name` (text)
    *   `pocketbase_file_id` (relation to the actual file stored in Pocketbase)
    *   `uploaded_by` (relation to `users`)
    *   `uploaded_at` (date)
    *   `version_history` (json, to store previous file_ids if versioning is implemented)
    *   `description` (text)

**Benefits:**
*   **Centralized Organization:** Keeps important firm-level documents organized and accessible.
*   **Secure Storage:** Leverages platform security for sensitive documents.
*   **Improved Efficiency:** Easy to find and manage firm-wide resources.

### 4.4 Reporting & Analytics (Claimload, Performance)

Providing solicitors with insights into their activities and performance on the platform can help them optimize their processes and understand their engagement.

**Purpose:** To offer solicitors and their firms relevant reports and analytics regarding their funding applications, active claims, and overall platform usage.

**Key Features & Report Types:**

*   **Dedicated "Reports" Section:** Accessible from the main solicitor portal navigation.
*   **Dashboard Integration:** Key KPIs might also be displayed on the main Solicitor Dashboard (as per Section 3.2.5).
*   **Report Categories:**
    1.  **Funding Application Analytics:**
        *   `Total Applications Submitted` (selectable date range).
        *   `Application Status Breakdown` (Pie chart or bar chart: Draft, Submitted, Under Review, Requires Info, Approved, Rejected, Funded).
        *   `Success Rate` (Approved applications / Total resolved applications).
        *   `Average Time to Decision` (from submission to approval/rejection).
        *   `Funding Amounts Requested vs. Approved`.
    2.  **Active Claim Analytics:**
        *   `Total Active Claims`.
        *   `Claim Status Breakdown` (Pie/bar chart: Pre-Action, Discovery, Trial Prep, etc.).
        *   `Average Claim Duration` (for closed/settled claims).
        *   `Total Funding Secured` across all active/past claims.
    3.  **Document Activity (Optional, more advanced):**
        *   Number of documents uploaded.
        *   Storage utilization (if relevant for platform limits).
*   **Filtering & Parameters:** Most reports should allow filtering by:
    *   Date Range (e.g., Last 30 days, Last Quarter, Custom Range).
    *   For firms with multiple users: Filter by individual solicitor or view firm-wide.
*   **Visualizations:** Use charts (bar, line, pie - using a Flutter charting library like `fl_chart` or `charts_flutter`) and data tables for clear presentation.
*   **Export Options:** Ability to export report data (e.g., to CSV, PDF) for offline analysis or record-keeping.

**Wireframe Description: Reports Section**

*   **A. Reports Landing Page:**
    *   **Layout:** A dashboard-like interface with cards or links for each available report category (e.g., "Application Analytics," "Claim Analytics").
    *   Each card might show a headline KPI for that category.

*   **B. Individual Report View (e.g., Application Analytics):**
    *   **Header:** Report Title, Date Range Selector, other relevant filters (e.g., "View for: My Stats / Firm Stats"). "Export" button.
    *   **Content Area:** A mix of:
        *   Key numerical figures (e.g., "Total Applications: 50", "Success Rate: 65%").
        *   Charts visualizing data (e.g., Pie chart for status breakdown, Line chart for applications over time).
        *   Data tables providing detailed breakdowns where appropriate.
    *   Interactive elements: Hovering over chart segments to see exact values, clicking on table headers to sort.

**Data Sources:**
*   Aggregated data primarily from [`funding_applications`](DESIGN_DOCUMENT.md:121-140), `claims` collection, and potentially [`user_activity_logs`](DESIGN_DOCUMENT.md:177-183).
*   Calculations would be performed either on the backend (if complex, via Pocketbase view collections or custom API endpoints if extended) or on the frontend for simpler aggregations.

**Benefits:**
*   **Data-Driven Insights:** Helps solicitors understand their performance and identify trends.
*   **Improved Decision Making:** Information to optimize application strategies or manage claimloads.
*   **Transparency:** Clear view of their firm's interaction and success on the platform.

### 4.5 User Profile & Notification Settings (Incl. Firm User Management)

Solicitors need to manage their personal profile, their firm's profile (if they are the primary PU or have admin rights for the firm), and control how they receive notifications.

**Purpose:** To provide users with control over their personal and firm-level information, manage associated users within their firm, and customize their notification preferences.

**Key Features:**

*   **A. Personal User Profile:**
    *   **Access:** Via a "My Profile" or user avatar link in the main navigation/header.
    *   **Editable Fields (from [`users`](DESIGN_DOCUMENT.md:68-77) and [`solicitor_profiles`](DESIGN_DOCUMENT.md:78-91) for the logged-in solicitor):**
        *   Name (if `full_name` is used, or derived from `solicitor_name`)
        *   Email (Display only, change might require a separate verified process)
        *   Contact Number
        *   Position within the firm
        *   Password Change (securely handled)
        *   Two-Factor Authentication (OTP) Setup/Management (if `otp_enabled` is true)
    *   **Display Only:** Role (`solicitor`), Last Login.

*   **B. Firm Profile Management (for PU Solicitor / Firm Admin):**
    *   **Access:** A subsection within "My Profile" or a separate "Firm Settings" area.
    *   **Editable Fields (from [`solicitor_profiles`](DESIGN_DOCUMENT.md:78-91)):**
        *   Law Firm Name
        *   Firm Address
        *   Firm Registration Number (SRA Number)
        *   Primary Firm Contact Details (if different from PU solicitor)
    *   **PU Status Information (Display Only for PU, potentially editable by Platform Admin):**
        *   Current `pu_status` ('none', 'pending', 'approved', 'rejected')
        *   `pu_application_date`, `pu_decision_date`
        *   Uploaded `verification_documents_uploads` (view/download, new uploads might be part of a re-verification flow managed by platform admins).

*   **C. Firm User Management (for PU Solicitor / Firm Admin):**
    *   **Purpose:** Allow the primary solicitor or designated firm admin to manage other users from their law firm who need access to the platform under the firm's umbrella.
    *   **Functionality (linked to [`solicitor_profiles.additional_users`](DESIGN_DOCUMENT.md:90)):**
        *   **Invite New User:** Form to send an invitation email to a colleague. The colleague would then complete their own basic registration, automatically linking them to the firm.
        *   **List Firm Users:** View a list of all users associated with the firm. Shows Name, Email, Role within Platform (likely 'solicitor'), Status (Active/Invited/Disabled).
        *   **Manage User Roles (within firm context - if applicable):** E.g., designate another user as a "Firm Admin" for platform management tasks, or assign specific claim access (though claim-specific assignment is better handled in Claim Management). *Initial scope might be simpler: all additional users are standard solicitors under the firm.*
        *   **Disable/Re-enable User Account:** Temporarily suspend a firm user's access.
        *   **Remove User from Firm:** Dissociate a user from the firm's profile (e.g., if they leave the firm).
    *   **Data:** Manages relations in `solicitor_profiles.additional_users` which points to `users` collection records.

*   **D. Notification Settings:**
    *   **Access:** A "Notifications" tab within "My Profile" or "Settings".
    *   **Granular Controls:** Checkboxes to enable/disable notifications for various event types:
        *   **In-App Notifications:** (Displayed within the platform's notification center/widgets)
            *   New message from 3Pay Global Agent regarding Application/Claim
            *   Application status change (e.g., Submitted, Requires Info, Approved, Rejected)
            *   Claim status update
            *   Task assigned / Action required
            *   General platform announcements
        *   **Email Notifications:** (Option to receive email summaries or individual emails for the same events)
            *   Daily/Weekly Digest option for non-urgent email notifications.
    *   **Data Storage:** User preferences could be stored as a JSON object in the `users` or `solicitor_profiles` record.

**Wireframe Descriptions:**

*   **A. My Profile / Settings Page:**
    *   **Layout:** Tabbed interface (e.g., "Personal Details," "Firm Profile," "Firm Users," "Notification Preferences," "Security").
    *   **Personal Details Tab:** Forms for editable fields, display for read-only info. "Change Password," "Setup 2FA" buttons.
    *   **Firm Profile Tab:** Forms for firm details. Display PU status info.
    *   **Firm Users Tab:** Table listing firm users. "Invite New User" button. Actions per user (Edit Role - if applicable, Disable, Remove).
    *   **Notification Preferences Tab:** Grouped checkboxes for in-app and email notification types. "Save Preferences" button.
    *   **Security Tab:** Password change, 2FA management.

**Benefits:**
*   **User Control:** Empowers users to manage their information and preferences.
*   **Efficient Firm Management:** Streamlines adding and managing firm members' access.
*   **Reduced Noise:** Customizable notifications ensure users only receive alerts relevant to them.
*   **Enhanced Security:** Options for password management and 2FA.

### 4.6 Powerful Search/Filtering Capabilities

Effective search and filtering are essential for navigating large amounts of data quickly.

**Purpose:** To enable solicitors to easily find specific applications, claims, documents, communications (with 3Pay Global Agents), or other relevant information across the platform.

**Key Features:**

*   **A. Global Search Bar:**
    *   **Location:** Prominently placed in the main platform header/navigation.
    *   **Functionality:**
        *   As the user types, suggests results dynamically across different categories (e.g., "Applications: Claim Title X", "Claims: Claim Y", "Documents: filename.pdf", "Contacts: Barrister Z").
        *   Pressing Enter or clicking "Search" navigates to a dedicated search results page.
    *   **Scope:** Searches across key fields in `funding_applications`, `claims` collection, `solicitor_profiles` (for firm colleagues), `claimant_profiles` (linked to their claims), `barristers`, `experts`, document names/metadata, and potentially indexed content of documents (if advanced search like Algolia/Typesense is integrated). Communication with agents might also be searchable if indexed.

*   **B. Dedicated Search Results Page:**
    *   **Layout:** Displays search results grouped by category (e.g., Applications, Claims, Documents, People).
    *   **Filtering on Results Page:** Allow further refinement of search results by:
        *   Category type.
        *   Date ranges.
        *   Status (for applications/claims).
        *   Author/Uploaded by (for documents).
    *   Each search result is a direct link to the relevant item/page.

*   **C. List-Specific Filtering & Sorting:**
    *   As described in previous sections for Application Lists (2.3.A), Claim Lists (4.1), Document Lists (2.4.D, 4.3), these views will have their own contextual filtering (e.g., by status, date, type) and sorting capabilities.
    *   Filters should be persistent within a session or allow saving of common filter sets.

**Wireframe Description: Global Search Interaction**

*   **Search Bar (Header):** Standard input field with a search icon.
*   **Dynamic Suggestions Dropdown:** Appears below the search bar as the user types.
    *   Shows top 3-5 matches, grouped by type (e.g., "Applications", "Claims", "Documents").
    *   Each suggestion is clickable.
    *   "View all results for '...' " link at the bottom.
*   **Search Results Page:**
    *   Search query displayed at the top.
    *   Left sidebar for filter options (Category, Date, Status, etc.).
    *   Main content area lists results, paginated if numerous. Each result shows a title, a brief snippet of context, and the category.

**Technical Considerations:**
*   Basic search can be implemented using Pocketbase's filtering capabilities on text fields.
*   For more advanced search (fuzzy search, relevance ranking, searching within document content), integration with a dedicated search service like Algolia or Typesense would be necessary, as mentioned in [`DESIGN_DOCUMENT.md:254`](DESIGN_DOCUMENT.md:254). This would involve indexing relevant data from Pocketbase into the search service.

**Benefits:**
*   **Time Savings:** Quickly locate information without manual browsing.
*   **Improved Efficiency:** Faster access to relevant data improves workflow.
*   **Enhanced Usability:** Makes the platform easier to navigate, especially as data grows.

---
*(End of Part 3. Part 4: Cross-Cutting Concerns will follow.)*
## 5. Part 4: Cross-Cutting Concerns

This part addresses overarching considerations that apply across the entire Solicitor portal, ensuring a robust, accessible, and consistent user experience.

### 5.1 Error Handling, Edge Cases, System States

A good user experience gracefully handles errors, edge cases, and various system states.

**A. Error Handling:**

*   **Validation Errors:**
    *   **Inline Validation:** For form fields, display validation errors directly below or next to the field as the user types or on blur (e.g., "Email is invalid," "This field is required"). Use clear, concise language. Highlight problematic fields (e.g., red border).
    *   **Submission Errors:** If server-side validation fails upon form submission, display a summary of errors at the top of the form (e.g., using shadcn_ui Alert with `variant="destructive"`) and scroll to the first field with an error.
*   **Network Errors:**
    *   Display a non-intrusive notification (e.g., shadcn_ui Toast) for temporary network issues (e.g., "Network connection lost. Attempting to reconnect...").
    *   For critical actions (e.g., form submission) that fail due to network issues, provide a clear error message and an option to "Retry."
*   **Server Errors (5xx):**
    *   Display a user-friendly message (e.g., "An unexpected error occurred on our end. Please try again later. If the issue persists, contact support.") using an Alert or Toast. Avoid showing technical stack traces to the user.
*   **Authorization/Permission Errors (401/403):**
    *   If a user tries to access a feature they don't have permission for, display a clear "Access Denied" message or redirect them appropriately.
    *   If a session expires, redirect to the login page with a message like "Your session has expired. Please log in again."

**B. Edge Cases:**

*   **No Data / Empty States:**
    *   For lists (applications, claims, documents, etc.), when there is no data to display, show a clear message (e.g., "You have no active claims yet," "No documents found in this folder") and, where appropriate, a call to action (e.g., "Start New Funding Application," "Upload Your First Document"). Use illustrative icons if possible.
    *   **Large Data Sets:**
        *   Implement pagination for all lists that can grow large (e.g., applications, claims, document lists, audit trails). Use shadcn_ui Pagination component.
        *   Ensure efficient loading and rendering of large lists to prevent UI lag. Consider virtual scrolling for very long lists if performance becomes an issue.
*   **Slow Network Conditions:**
    *   Use loading indicators (e.g., shadcn_ui Skeleton components for content placeholders, spinners for actions) to provide feedback during data fetching or processing.
    *   Optimize payloads and queries to minimize data transfer.

**C. System States:**

*   **Loading State:**
    *   **Page/View Level:** Use skeleton screens or a global loading indicator when an entire view's data is being fetched.
    *   **Component Level:** Use spinners or subtle loading animations for individual components or actions (e.g., when a button is clicked and an API call is made).
*   **Disabled State:** Clearly indicate disabled interactive elements (buttons, form fields) using standard styling (e.g., reduced opacity, not-allowed cursor). Provide tooltips explaining why an element is disabled if not obvious.
*   **Success State:** Provide clear confirmation messages for successful actions (e.g., "Application submitted successfully," "Document uploaded," "Profile updated") using Toasts or Alerts.
*   **Idle State:** Ensure the application behaves predictably when left idle (e.g., session timeout as per security policy).

**Wireframe Snippets (Conceptual):**

*   **Empty State for "My Cases" List:**
    ```
    +------------------------------------------------------+
    | My Claims                                            |
    +------------------------------------------------------+
    |                                                      |
    |  [Icon: Briefcase with a question mark]              |
    |  You have no active claims yet.                      |
    |  [Button: Start New Funding Application]             |
    |                                                      |
    +------------------------------------------------------+
    ```
*   **Inline Form Validation Error:**
    ```
    Email Address:
    [ input field with red border: "john.doe" ]
    <ErrorText style="color:red">Please enter a valid email address.</ErrorText>
    ```
*   **Loading State with Skeleton:**
    ```
    +------------------------------------------------------+
    | Claim Details                                        |
    +------------------------------------------------------+
    | Claim Title: [SkeletonLoader width="60%"]            |
    | Status:     [SkeletonLoader width="30%"]             |
    |                                                      |
    | Overview Tab:                                        |
    | [SkeletonLoader height="100px" width="100%"]         |
    +------------------------------------------------------+
    ```

**Benefits:**
*   **Improved User Trust:** Graceful error handling makes the system feel more reliable.
*   **Reduced Frustration:** Clear messages and guidance help users navigate issues.
*   **Enhanced Usability:** Predictable system states make the application easier to understand and use.

### 5.2 Accessibility Considerations (WCAG Compliance)

Ensuring the platform is accessible to all users, including those with disabilities, is a core requirement. The design will strive to meet WCAG 2.1 AA guidelines.

**Key Principles & Techniques:**

*   **Perceivable:**
    *   **Text Alternatives:** Provide appropriate alt text for all informative images (e.g., icons with meaning, charts). Decorative images should have empty alt attributes.
    *   **Adaptable Content:** Ensure content can be presented in different ways (e.g., responsive layout for different screen sizes and orientations) without losing information or structure. Use semantic HTML5 elements.
    *   **Distinguishable:**
        *   **Color Contrast:** Ensure sufficient color contrast between text and background (at least 4.5:1 for normal text, 3:1 for large text). Use tools to check contrast.
        *   **Don't Rely on Color Alone:** Information conveyed with color should also be available through text or other visual cues (e.g., icons alongside status colors).
        *   **Text Resizing:** Ensure text can be resized up to 200% without loss of content or functionality.
*   **Operable:**
    *   **Keyboard Accessibility:** All interactive elements (links, buttons, form fields, custom components) must be operable via a keyboard.
        *   Ensure logical tab order.
        *   Visible focus indicators for all focusable elements (custom focus styles if default is not clear enough).
        *   Keyboard traps must be avoided.
    *   **Sufficient Time:** Avoid time limits for tasks unless essential. If present, provide ways to extend or adjust them.
    *   **Seizures and Physical Reactions:** Avoid content that flashes more than three times per second.
    *   **Navigable:** Provide clear headings, labels, and navigation mechanisms. Use breadcrumbs for complex navigation paths.
*   **Understandable:**
    *   **Readable:** Use clear and simple language. Define jargon or complex terms.
    *   **Predictable:** Ensure navigation and interactive elements behave consistently throughout the platform.
    *   **Input Assistance:** Provide clear instructions and error messages for forms. Help users avoid and correct mistakes.
*   **Robust:**
    *   **Compatible:** Maximize compatibility with current and future user agents, including assistive technologies (e.g., screen readers). Use valid HTML and ARIA (Accessible Rich Internet Applications) attributes where necessary for custom components (e.g., tabs, modals, custom dropdowns built with shadcn_ui).
    *   **ARIA Roles & Attributes:** For custom interactive components (e.g., custom dropdowns, tabs, modals), use appropriate ARIA roles (e.g., `role="dialog"`, `role="tab"`) and attributes (e.g., `aria-labelledby`, `aria-describedby`, `aria-expanded`, `aria-hidden`) to convey their purpose and state to assistive technologies.

**Specific Considerations for Flutter + shadcn_ui:**
*   Leverage Flutter's built-in accessibility features (e.g., `Semantics` widget).
*   When using or customizing shadcn_ui components, ensure their accessibility features are preserved or enhanced. Many shadcn_ui components are built with accessibility in mind (e.g., Radix UI primitives).
*   Regularly test with accessibility tools (e.g., browser developer tools, screen readers like NVDA/VoiceOver, Axe DevTools).

**Benefits:**
*   **Inclusivity:** Allows a wider range of users to access and use the platform.
*   **Improved Usability:** Accessibility best practices often benefit all users.
*   **Legal Compliance:** Helps meet legal and regulatory requirements in many jurisdictions.

### 5.3 UI/UX Consistency & Divergence vs. Co-funder Portal

Maintaining a degree of consistency with the co-funder portal is important for brand identity and for users who might interact with both sides (e.g., platform administrators). However, the solicitor portal must also cater to the specific needs and workflows of legal professionals.

**A. Areas of Consistency:**

*   **Branding & Visual Identity:**
    *   Logo, color palette, typography, and overall visual style should align with the established 3Pay brand.
*   **Core UI Components (shadcn_ui):**
    *   Utilize the same base shadcn_ui component library for elements like buttons, input fields, modals, tables, tabs, alerts, toasts, etc. This ensures a consistent look, feel, and interaction pattern for fundamental UI elements.
*   **Layout Shell (High-Level):**
    *   Consider a similar overall page structure (e.g., main navigation placement - sidebar or top bar, header, footer) if it makes sense for both user types. This can provide a familiar framework.
*   **Iconography:** Use a consistent set of icons for common actions (e.g., edit, delete, download, search).
*   **Terminology (Where Overlapping):** For shared concepts (e.g., "notifications," "profile settings"), use consistent terminology.

**B. Strategic Divergences (Justified):**

*   **Dashboard Content & Layout:**
    *   **Justification:** Solicitors have vastly different priorities and information needs than co-funders.
    *   **Divergence:** The Solicitor Dashboard (Part 2) will feature widgets specific to application/claim management (e.g., "My Active Claims"), pending legal tasks, communication with 3Pay Global Agents, and solicitor-centric KPIs, unlike the co-funder dashboard which focuses on investments, educational content, and co-funder level progression.
    *   **Primary Navigation & Information Architecture:**
        *   **Justification:** The core tasks and workflows differ significantly.
        *   **Divergence:** The main navigation items for solicitors will focus on "Funding Applications," "My Claims," "Firm Documents," "PU Status," "Reporting," and "Firm User Management." Co-funders would have "Investments," "Educational Content," "Knowledge Tests," etc.
    *   **Terminology (Role-Specific):**
        *   **Justification:** Legal professionals use specific terminology. The term "claim" is now consistently used.
        *   **Divergence:** Terms like "Claim Title," "Schedule of Costs," "Legal Opinion," "Barrister," "Expert Witness," "CFA" will be prevalent in the solicitor portal. Communication flows will refer to "3Pay Global Agents" as the intermediaries.
    *   **Forms & Workflows:**
        *   **Justification:** Application submission, claim management, and PU status processes are unique to solicitors. Communication workflows are now centralized via 3Pay Global Agents.
        *   **Divergence:** The multi-step funding application form (now including new claim type/industry dropdowns), claim detail views with specific tabs for legal professionals (including communication with agents and internal firm notes), and the PU application process are entirely solicitor-specific.
    *   **Data Density & Presentation:**
        *   **Justification:** Solicitors often deal with complex information and may prefer denser data displays for efficiency.
        *   **Divergence:** List views for solicitors might default to tables with more columns and advanced filtering. This is a generalization and will be assessed per feature.
    *   **Reporting & Analytics:**
        *   **Justification:** KPIs and reporting needs are distinct.
        *   **Divergence:** Solicitor reports will focus on application success rates, claimload management, and firm performance, distinct from co-funder investment performance reports.

**C. Maintaining Cohesion:**

*   **Shared Design System Principles:** Even with divergences, adhere to underlying design principles (e.g., spacing, visual hierarchy, interaction feedback) established for the platform.
*   **User Testing:** Conduct user testing with solicitors to validate that divergences are intuitive and meet their needs, and that consistencies are not confusing.
*   **Documentation:** Clearly document design decisions, especially for divergences, in this document and any internal design system documentation.

**Benefits:**
*   **Brand Recognition:** Consistency reinforces the platform's identity.
*   **Reduced Learning Curve (for shared elements):** Users familiar with one portal will find common elements easy to use in the other.
*   **Tailored Experience:** Strategic divergences ensure each user role has an optimized and efficient experience for their specific tasks.

---
*(End of Part 4 and Document)*