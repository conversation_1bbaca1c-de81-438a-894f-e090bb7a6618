TASK 8: AUTHENTICATION INTEGRATION
===================================

OVERVIEW:
Integrate notification services with the existing authentication system, ensuring FCM tokens are properly managed during login/logout cycles and user-specific notifications work correctly.

TASKS:

8.1 UPDATE LOGIN FLOW
----------------------
□ Integrate FCM token refresh in login success
  - File: lib/src/core/services/pocketbase_service.dart
  - Locate authenticateWithEmailAndPassword method
  - Add FirebaseApiService.refreshToken() call after successful authentication
  - Handle token refresh errors without blocking login
  - Expected outcome: FCM tokens updated on every login


□ Add login error handling
  - Handle FCM token refresh failures during login
  - Log token refresh errors without affecting user experience
  - Provide fallback behavior for token failures
  - Expected outcome: Login works even if FCM token refresh fails

8.2 UPDATE LOGOUT FLOW
-----------------------
□ Integrate FCM token cleanup in logout
  - File: lib/src/core/services/pocketbase_service.dart
  - Locate logout/signOut method
  - Add FirebaseApiService.clearToken() call before clearing auth
  - Handle token cleanup errors gracefully
  - Expected outcome: FCM tokens removed on logout

□ Update session expiration handling
  - Locate session expiration/timeout handling
  - Add FCM token cleanup for expired sessions
  - Handle automatic logout scenarios
  - Expected outcome: FCM tokens cleaned up on session expiration

□ Add logout error handling
  - Handle FCM token cleanup failures during logout
  - Log cleanup errors without blocking logout
  - Ensure logout completes even if token cleanup fails
  - Expected outcome: Logout works even if FCM cleanup fails

8.3 UPDATE USER REGISTRATION FLOW
----------------------------------
□ Integrate FCM token setup in registration
  - File: Registration/signup related services
  - Add FCM token initialization after successful registration
  - Request notification permissions during onboarding
  - Handle permission denial gracefully
  - Expected outcome: New users configured for notifications

□ Update email verification flow
  - Add FCM token refresh after email verification
  - Handle verified user notification setup
  - Expected outcome: Verified users ready for notifications

□ Add registration error handling
  - Handle FCM setup failures during registration
  - Don't block registration for notification failures
  - Log setup errors for debugging
  - Expected outcome: Registration works even if notifications fail

8.4 UPDATE USER PROFILE MANAGEMENT
-----------------------------------
□ Add FCM token to user profile data
  - Verify users collection has fcm_token field
  - Update profile display to show notification status
  - Add notification preferences to user settings
  - Expected outcome: FCM token visible in user management

□ Update profile editing
  - Add notification preferences to profile editing
  - Allow users to enable/disable notification types
  - Handle preference changes immediately
  - Expected outcome: Users can control notification preferences

□ Add notification status indicators
  - Show notification permission status in profile
  - Display FCM token status (for debugging)
  - Add notification troubleshooting info
  - Expected outcome: Users can see notification status

8.5 UPDATE MULTI-USER SUPPORT
------------------------------
□ Handle user switching scenarios
  - Clear previous user's FCM token on user switch
  - Set up new user's FCM token
  - Handle rapid user switching
  - Expected outcome: FCM tokens properly managed for user switching

□ Update impersonation features
  - Handle FCM tokens during admin impersonation
  - Ensure notifications go to correct user
  - Restore original user's token after impersonation
  - Expected outcome: Impersonation doesn't break notifications

□ Add user-specific notification filtering
  - Ensure notifications are filtered by user ID
  - Handle global vs user-specific notifications
  - Verify notification privacy
  - Expected outcome: Users only see their own notifications

8.6 UPDATE PERMISSION MANAGEMENT
---------------------------------
□ Add notification permission checking
  - Create method to check current notification permissions
  - Add permission status to user profile
  - Handle permission changes at runtime
  - Expected outcome: Real-time permission status tracking

□ Add permission request flow
  - Create guided permission request flow
  - Explain notification benefits to users
  - Handle permission denial gracefully
  - Provide re-request options
  - Expected outcome: User-friendly permission management

□ Add permission troubleshooting
  - Detect when permissions are disabled
  - Provide instructions for re-enabling
  - Handle different platform permission models
  - Expected outcome: Users can troubleshoot permission issues

8.7 UPDATE AUTHENTICATION STATE MANAGEMENT
-------------------------------------------
□ Integrate with existing auth state providers
  - File: Authentication state management files
  - Add notification service calls to auth state changes
  - Handle auth state restoration
  - Expected outcome: Notifications sync with auth state

□ Update auth state persistence
  - Ensure FCM token persistence across app restarts
  - Handle auth state restoration scenarios
  - Verify token validity on app startup
  - Expected outcome: Persistent notification configuration

□ Add auth state error recovery
  - Handle corrupted auth state scenarios
  - Recover notification configuration
  - Provide fallback authentication
  - Expected outcome: Robust auth state management

8.8 UPDATE ROLE-BASED NOTIFICATION ACCESS
------------------------------------------
□ Implement role-based notification filtering
  - Filter notifications based on user role (solicitor, claimant, co-funder)
  - Configure different notification channels per role
  - Handle role changes dynamically
  - Expected outcome: Role-appropriate notifications

□ Add permission-based notification access
  - Check user permissions before sending notifications
  - Filter notification types based on access level
  - Handle permission upgrades/downgrades
  - Expected outcome: Permission-appropriate notifications

□ Update admin notification capabilities
  - Allow admins to send notifications to user groups
  - Implement admin notification management
  - Add notification analytics for admins
  - Expected outcome: Admin notification management

8.9 ADD AUTHENTICATION ERROR HANDLING
--------------------------------------
□ Handle network errors during auth
  - Gracefully handle FCM token operations during network issues
  - Implement retry mechanisms for token operations
  - Cache token operations for later retry
  - Expected outcome: Resilient token management

□ Handle authentication service errors
  - Handle PocketBase connection errors
  - Handle Firebase authentication errors
  - Provide meaningful error messages to users
  - Expected outcome: Good error handling for auth issues

□ Add authentication monitoring
  - Monitor authentication success/failure rates
  - Track FCM token operation success rates
  - Log authentication-related notification issues
  - Expected outcome: Monitoring and debugging capabilities

DEPENDENCIES:
- Task 7: Main Application Integration
- Existing authentication system and PocketBase service
- User management and profile systems

NEXT TASK:
- Task 9: Existing Service Integration

FILES MODIFIED:
- lib/src/core/services/pocketbase_service.dart
- Authentication-related service files
- User profile management files
- Authentication state management files

ESTIMATED TIME:
- 50-65 minutes

SAMPLE INTEGRATION CODE:
```dart
// In PocketBaseService
Future<void> authenticateWithEmailAndPassword({
  required String email,
  required String password,
}) async {
  try {
    // Existing authentication logic
    await client.collection('users').authWithPassword(email, password);
    
    // Refresh FCM token after successful login
    await FirebaseApiService.refreshToken();
    
    LoggerService.info('User authenticated and FCM token refreshed');
  } catch (e) {
    LoggerService.error('Authentication error', e);
    rethrow;
  }
}

Future<void> logout() async {
  try {
    // Clear FCM token before logout
    await FirebaseApiService.clearToken();
    
    // Existing logout logic
    client.authStore.clear();
    
    LoggerService.info('User logged out and FCM token cleared');
  } catch (e) {
    LoggerService.error('Logout error', e);
    rethrow;
  }
}
```

NOTES:
- Test all authentication flows thoroughly
- Ensure FCM operations don't block authentication
- Handle edge cases like rapid login/logout
- Consider authentication performance impact
- Document authentication-notification integration
- Test with different user roles and permissions
