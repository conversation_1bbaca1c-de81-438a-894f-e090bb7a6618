TASK 6: FIREBASE PUSH NOTIFICATION SERVICE IMPLEMENTATION
=========================================================

OVERVIEW:
Implement the FirebaseApiService class to handle Firebase Cloud Messaging (FCM) push notifications with token management, message handling, and PocketBase integration.

TASKS:

6.1 CREATE FIREBASE API SERVICE
--------------------------------
□ Create service file
  - File: lib/src/core/services/firebase_api_service.dart
  - Create class: FirebaseApiService
  - Add required imports: firebase_messaging, flutter/material.dart
  - Expected outcome: Service file structure created

□ Add Firebase messaging instance
  - Create static FirebaseMessaging instance
  - Add PocketBaseService instance for token storage
  - Add navigation key for routing
  - Expected outcome: Core service dependencies established

□ Import existing services
  - Import LoggerService for consistent logging
  - Import PocketBaseService for data operations
  - Import existing navigation/routing utilities
  - Expected outcome: Integration with existing architecture

6.2 IMPLEMENT NOTIFICATION INITIALIZATION
------------------------------------------
□ Create initNotifications method
  - Request notification permissions from user
  - Handle different authorization statuses
  - Log permission request results
  - Expected outcome: Permission handling implemented

□ Implement FCM token management
  - Get FCM token for current device
  - Log token for debugging purposes
  - Call token storage method
  - Handle token refresh scenarios
  - Expected outcome: FCM token lifecycle managed

□ Set up message listeners
  - Configure FirebaseMessaging.onMessage for foreground
  - Configure FirebaseMessaging.onMessageOpenedApp for background
  - Set up background message handler
  - Expected outcome: All message scenarios covered

6.3 IMPLEMENT TOKEN MANAGEMENT
-------------------------------
□ Create _storeFCMToken method
  - Validate user authentication status
  - Update user record in PocketBase with FCM token
  - Handle storage errors gracefully
  - Log token storage operations
  - Expected outcome: FCM tokens stored in PocketBase

□ Create refreshToken method
  - Get fresh FCM token from Firebase
  - Update stored token in PocketBase
  - Call during login/app startup
  - Expected outcome: Token refresh functionality

□ Create clearToken method
  - Remove FCM token from PocketBase user record
  - Delete token from Firebase
  - Call during logout
  - Expected outcome: Token cleanup on logout

6.4 IMPLEMENT MESSAGE HANDLING
-------------------------------
□ Create _handleForegroundMessage method
  - Process messages received while app is active
  - Log message details
  - Show local notification for foreground messages
  - Extract and process message data
  - Expected outcome: Foreground message processing

□ Create _handleBackgroundMessage method
  - Process messages when app is background/terminated
  - Log message details
  - Handle navigation based on message data
  - Expected outcome: Background message processing

□ Create background message handler function
  - Implement top-level firebaseMessagingBackgroundHandler
  - Add @pragma('vm:entry-point') annotation
  - Log background message reception
  - Expected outcome: Background message handler working

6.5 IMPLEMENT NAVIGATION HANDLING
----------------------------------
□ Create _handleNotificationNavigation method
  - Parse message data for navigation information
  - Route to specific pages based on notification type
  - Handle different notification types:
    * claim_update -> claim detail page
    * funding_opportunity -> funding page
    * message -> messages page
    * document_upload -> documents page
  - Expected outcome: Smart navigation from notifications

□ Add navigation data structure
  - Define expected data payload structure:
    * type: notification type
    * claim_id: for claim-related notifications
    * user_id: for user-specific notifications
    * route: specific route to navigate to
  - Expected outcome: Consistent navigation data format

□ Implement fallback navigation
  - Handle missing or invalid navigation data
  - Default to appropriate dashboard based on user type
  - Log navigation failures
  - Expected outcome: Graceful navigation fallbacks

6.6 INTEGRATE WITH LOCAL NOTIFICATIONS
---------------------------------------
□ Create _showLocalNotification method
  - Call LocalNotificationService for foreground messages
  - Convert FCM message to local notification format
  - Use appropriate notification channel
  - Pass through navigation payload
  - Expected outcome: Local notifications for foreground FCM messages

□ Add notification type mapping
  - Map FCM message types to local notification channels
  - Use different channels for different message types
  - Configure appropriate importance levels
  - Expected outcome: Proper channel usage for FCM messages

6.7 IMPLEMENT ERROR HANDLING
-----------------------------
□ Add comprehensive error handling
  - Wrap all Firebase operations in try-catch blocks
  - Use LoggerService for error logging
  - Provide meaningful error messages
  - Handle network connectivity issues
  - Expected outcome: Robust error handling

□ Add permission error handling
  - Handle denied notification permissions
  - Provide user-friendly error messages
  - Log permission denial events
  - Expected outcome: Graceful permission handling

□ Add token error handling
  - Handle FCM token generation failures
  - Handle PocketBase storage failures
  - Implement retry mechanisms where appropriate
  - Expected outcome: Resilient token management

6.8 ADD SERVICE UTILITIES
-------------------------
□ Create service status methods
  - isInitialized() - check if service is ready
  - hasPermission() - check notification permissions
  - getTokenStatus() - check if token is stored
  - Expected outcome: Service status monitoring

□ Add debugging utilities
  - Create logMessageDetails() method
  - Add token validation methods
  - Create service health check method
  - Expected outcome: Debugging and monitoring tools

□ Add configuration methods
  - Create updateNotificationSettings() method
  - Allow enabling/disabling notification types
  - Support runtime configuration changes
  - Expected outcome: Configurable push notifications

DEPENDENCIES:
- Task 5: Local Notification Service Implementation
- Firebase project configuration from Task 1
- Existing PocketBaseService and LoggerService

NEXT TASK:
- Task 7: Main Application Integration

FILES CREATED:
- lib/src/core/services/firebase_api_service.dart

FILES MODIFIED:
- None (new service implementation)

ESTIMATED TIME:
- 60-75 minutes

SAMPLE CODE STRUCTURE:
```dart
class FirebaseApiService {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final PocketBaseService _pocketBaseService = PocketBaseService();
  
  static Future<void> initNotifications() async { /* implementation */ }
  static Future<void> _storeFCMToken(String? token) async { /* implementation */ }
  static Future<void> refreshToken() async { /* implementation */ }
  static Future<void> clearToken() async { /* implementation */ }
  static void _handleForegroundMessage(RemoteMessage message) { /* implementation */ }
  static void _handleBackgroundMessage(RemoteMessage message) { /* implementation */ }
  static void _handleNotificationNavigation(RemoteMessage message) { /* implementation */ }
}

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Background message handling
}
```

NOTES:
- Ensure background message handler is top-level function
- Test token persistence across app restarts
- Verify message handling in all app states
- Consider message priority and delivery guarantees
- Plan for message analytics and tracking
- Handle edge cases like network failures gracefully
