TASK 1: PREREQUISITES AND SETUP
=====================================

OVERVIEW:
Set up Firebase project, install required tools, and prepare development environment for both local and push notifications implementation.

TASKS:

1.1 FIREBASE PROJECT SETUP
---------------------------
□ Create Firebase project at https://console.firebase.google.com
  - Project name: "3Pay Global"
  - Disable Google Analytics (optional)
  - Expected outcome: Firebase project created and accessible

□ Enable Cloud Messaging service
  - Navigate to Project Settings > Cloud Messaging
  - Enable Firebase Cloud Messaging API
  - Expected outcome: FCM service active in Firebase console

□ Configure Firebase for Android platform
  - Add Android app to Firebase project
  - Package name: com.threepayglobal.app (or existing package name from android/app/build.gradle.kts)
  - Download google-services.json
  - Expected outcome: Android app registered in Firebase

□ Configure Firebase for iOS platform
  - Add iOS app to Firebase project
  - Bundle ID: com.threepayglobal.app (or existing from ios/Runner.xcodeproj)
  - Download GoogleService-Info.plist
  - Expected outcome: iOS app registered in Firebase

1.2 DEVELOPMENT TOOLS INSTALLATION
-----------------------------------
□ Install Firebase CLI
  - Run: npm install -g firebase-tools
  - Verify: firebase --version
  - Expected outcome: Firebase CLI installed and accessible

□ Install FlutterFire CLI
  - Run: dart pub global activate flutterfire_cli
  - Add to PATH if needed (export PATH="$PATH":"$HOME/.pub-cache/bin")
  - Verify: flutterfire --version
  - Expected outcome: FlutterFire CLI installed and accessible

□ Login to Firebase
  - Run: firebase login
  - Authenticate with Google account
  - Expected outcome: Authenticated with Firebase CLI

1.3 PROJECT CONFIGURATION
--------------------------
□ Configure FlutterFire for project
  - Navigate to project root: /Users/<USER>/projects/ttl/3pg_flutter
  - Run: flutterfire configure
  - Select "3Pay Global" project
  - Select both Android and iOS platforms
  - Expected outcome: firebase_options.dart file generated

□ Verify configuration files
  - Check: android/app/google-services.json exists
  - Check: ios/Runner/GoogleService-Info.plist exists
  - Check: lib/firebase_options.dart exists
  - Expected outcome: All Firebase configuration files present

1.4 POCKETBASE SCHEMA PREPARATION
----------------------------------
□ Add FCM token field to users collection
  - Access PocketBase admin panel
  - Navigate to users collection schema
  - Add field: fcm_token (Text, not required)
  - Expected outcome: users collection supports FCM token storage

□ Verify notification collections exist
  - Check: notifications collection exists
  - Check: notification_read_states collection exists
  - Expected outcome: Existing notification infrastructure confirmed

DEPENDENCIES:
- None (this is the starting point)

NEXT TASK:
- Task 2: Package Installation and Configuration

ESTIMATED TIME:
- 30-45 minutes

NOTES:
- Keep Firebase project credentials secure
- Document package names and bundle IDs for consistency
- Ensure stable internet connection for Firebase setup
