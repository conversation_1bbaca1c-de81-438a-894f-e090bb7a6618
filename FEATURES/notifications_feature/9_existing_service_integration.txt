TASK 9: EXISTING SERVICE INTEGRATION
====================================

OVERVIEW:
Integrate the new notification services with existing 3Pay Global services including the current NotificationService, claim management, communication systems, and user management.

TASKS:

9.1 INTEGRATE WITH EXISTING NOTIFICATION SERVICE
-------------------------------------------------
□ Update NotificationService._showToast method
  - File: lib/src/core/services/notification_service.dart
  - Replace LoggerService.info call with LocalNotificationService.showNotification
  - Map EnhancedNotificationModel to local notification format
  - Use notification type to determine appropriate channel
  - Expected outcome: In-app notifications trigger local notifications

□ Add notification type to channel mapping
  - Create _getNotificationChannel method
  - Map notification types to channels:
    * 'claim_update' -> LocalNotificationService.CLAIMS_CHANNEL_ID
    * 'funding_opportunity' -> LocalNotificationService.FUNDING_CHANNEL_ID
    * 'message' -> LocalNotificationService.MESSAGES_CHANNEL_ID
    * 'document_upload' -> LocalNotificationService.DEFAULT_CHANNEL_ID
  - Expected outcome: Proper channel usage for different notification types

□ Update notification creation for FCM compatibility
  - Modify notification creation in existing services
  - Add FCM-compatible data structure to notifications
  - Include navigation payload in notification data
  - Expected outcome: Notifications work with both local and push systems

□ Add notification ID generation
  - Create consistent notification ID generation
  - Use notification record ID hash for local notification ID
  - Ensure uniqueness across notification types
  - Expected outcome: Consistent notification IDs

9.2 INTEGRATE WITH CLAIM MANAGEMENT
-----------------------------------
□ Update claim status change notifications
  - File: Claim management services
  - Add LocalNotificationService calls for claim updates
  - Include claim ID and status in notification payload
  - Use CLAIMS_CHANNEL_ID for claim notifications
  - Expected outcome: Claim updates trigger local notifications

□ Update solicitor assignment notifications
  - Add notifications when solicitors are added to claims
  - Include claim details in notification
  - Use appropriate notification channel
  - Expected outcome: Solicitor assignments trigger notifications

□ Update claim document notifications
  - Add notifications for document uploads
  - Include document type and claim ID
  - Use document-specific notification channel
  - Expected outcome: Document uploads trigger notifications

□ Add claim milestone notifications
  - Create notifications for important claim milestones
  - Include milestone details and next steps
  - Use high-priority notification settings
  - Expected outcome: Important milestones trigger notifications

9.3 INTEGRATE WITH COMMUNICATION SYSTEM
----------------------------------------
□ Update claim communication provider
  - File: lib/src/features/solicitor_portal/application/providers/claim_communication_provider.dart
  - Add LocalNotificationService calls for new messages
  - Include message preview and sender info
  - Use MESSAGES_CHANNEL_ID for message notifications
  - Expected outcome: New messages trigger local notifications

□ Update agent message notifications
  - Enhance existing agent message notification creation
  - Add local notification trigger
  - Include message content preview
  - Expected outcome: Agent messages trigger both in-app and local notifications

□ Add message read status integration
  - Update notification read status when messages are read
  - Clear related local notifications
  - Sync read status across notification systems
  - Expected outcome: Message read status synced across systems

9.4 INTEGRATE WITH FUNDING SYSTEM
----------------------------------
□ Update funding opportunity notifications
  - Add LocalNotificationService calls for new funding opportunities
  - Include opportunity details and deadlines
  - Use FUNDING_CHANNEL_ID for funding notifications
  - Expected outcome: Funding opportunities trigger local notifications

□ Update funding application status notifications
  - Add notifications for application status changes
  - Include status details and next steps
  - Use appropriate priority levels
  - Expected outcome: Funding status changes trigger notifications

□ Update co-funder investment notifications
  - Add notifications for investment opportunities
  - Include investment details and terms
  - Use investor-specific notification settings
  - Expected outcome: Investment opportunities trigger notifications

9.5 INTEGRATE WITH USER MANAGEMENT
-----------------------------------
□ Update user profile change notifications
  - Add notifications for important profile changes
  - Include change details and verification requirements
  - Use user-specific notification preferences
  - Expected outcome: Profile changes trigger appropriate notifications

□ Update permission change notifications
  - Add notifications when user permissions change
  - Include new permission details
  - Use high-priority notification settings
  - Expected outcome: Permission changes trigger notifications

□ Update account security notifications
  - Add notifications for security events
  - Include event details and recommended actions
  - Use critical notification priority
  - Expected outcome: Security events trigger immediate notifications

9.6 INTEGRATE WITH DOCUMENT MANAGEMENT
---------------------------------------
□ Update document upload notifications
  - File: Document management services
  - Add LocalNotificationService calls for document uploads
  - Include document type and uploader info
  - Use document-specific notification channel
  - Expected outcome: Document uploads trigger local notifications

□ Update document approval notifications
  - Add notifications for document approval/rejection
  - Include approval status and feedback
  - Use appropriate notification priority
  - Expected outcome: Document approvals trigger notifications

□ Update document expiration notifications
  - Add scheduled notifications for document expirations
  - Include renewal instructions
  - Use scheduled notification functionality
  - Expected outcome: Document expirations trigger advance notifications

9.7 INTEGRATE WITH AUDIT LOGGING
---------------------------------
□ Update audit logging for notifications
  - File: Audit logging services
  - Add notification events to audit logs
  - Include notification type, recipient, and delivery status
  - Log both local and push notification events
  - Expected outcome: Notification events properly audited

□ Add notification delivery tracking
  - Track notification delivery success/failure
  - Log notification interaction events
  - Monitor notification performance metrics
  - Expected outcome: Comprehensive notification analytics

□ Add notification preference tracking
  - Log user notification preference changes
  - Track permission grant/denial events
  - Monitor notification engagement
  - Expected outcome: User notification behavior tracked

9.8 INTEGRATE WITH BACKGROUND SERVICES
---------------------------------------
□ Update background audio service integration
  - File: lib/src/core/services/background_audio_service.dart
  - Ensure notification channels don't conflict
  - Coordinate notification priorities
  - Handle audio playback notifications
  - Expected outcome: Audio and general notifications coexist

□ Update service locator integration
  - File: lib/src/core/services/service_locator.dart
  - Ensure proper service initialization order
  - Handle service dependencies correctly
  - Add notification service health checks
  - Expected outcome: All services work together harmoniously

□ Add background task coordination
  - Coordinate background notification processing
  - Handle concurrent notification operations
  - Manage notification queue and priorities
  - Expected outcome: Efficient background notification processing

9.9 UPDATE ERROR HANDLING AND LOGGING
--------------------------------------
□ Standardize error handling across services
  - Use consistent error handling patterns
  - Integrate with existing LoggerService
  - Provide meaningful error messages
  - Handle notification service failures gracefully
  - Expected outcome: Consistent error handling

□ Add notification-specific logging
  - Create notification-specific log categories
  - Log notification lifecycle events
  - Track notification performance metrics
  - Expected outcome: Comprehensive notification logging

□ Add service health monitoring
  - Monitor notification service health
  - Track service availability and performance
  - Implement service recovery mechanisms
  - Expected outcome: Reliable notification service operation

DEPENDENCIES:
- Task 8: Authentication Integration
- All existing 3Pay Global services
- Completed notification service implementations

NEXT TASK:
- Task 10: Testing and Validation

FILES MODIFIED:
- lib/src/core/services/notification_service.dart
- lib/src/core/services/service_locator.dart
- Claim management service files
- Communication provider files
- Document management service files
- User management service files
- Audit logging service files

ESTIMATED TIME:
- 60-75 minutes

SAMPLE INTEGRATION CODE:
```dart
// In existing NotificationService
void _showToast(EnhancedNotificationModel notification) {
  // Show local notification
  LocalNotificationService.showNotification(
    id: notification.id.hashCode,
    title: notification.title,
    body: notification.message,
    payload: jsonEncode({
      'type': notification.type,
      'id': notification.id,
      'route': _getRouteForNotificationType(notification.type),
    }),
  );
  
  LoggerService.info('New notification: ${notification.title}');
}

String _getRouteForNotificationType(String type) {
  switch (type) {
    case 'claim_update':
      return '/claims';
    case 'funding_opportunity':
      return '/funding';
    case 'message':
      return '/messages';
    default:
      return '/dashboard';
  }
}
```

NOTES:
- Test integration with each existing service
- Ensure no conflicts between notification systems
- Verify notification channels work correctly
- Test notification payload and navigation
- Monitor performance impact of integration
- Document integration points for future maintenance
