TASK 4: IOS PLATFORM SETUP
===========================

OVERVIEW:
Configure iOS-specific settings for both local and push notifications, including capabilities, permissions, and native code setup.

TASKS:

4.1 XCODE PROJECT CONFIGURATION
--------------------------------
□ Open Xcode workspace
  - Navigate to: ios/Runner.xcworkspace
  - Open in Xcode (required for capability configuration)
  - Expected outcome: Xcode project opened

□ Add Push Notifications capability
  - Select Runner target in Xcode
  - Go to "Signing & Capabilities" tab
  - Click "+ Capability"
  - Add "Push Notifications"
  - Expected outcome: Push Notifications capability enabled

□ Add Background Modes capability
  - In "Signing & Capabilities" tab
  - Click "+ Capability"
  - Add "Background Modes"
  - Enable "Background processing"
  - Enable "Remote notifications"
  - Expected outcome: Background notification processing enabled

□ Verify Bundle Identifier
  - Check Bundle Identifier matches Firebase iOS app configuration
  - Should match: com.threepay.global (or your configured bundle ID)
  - Expected outcome: Bundle ID consistency verified

4.2 APP DELEGATE CONFIGURATION
-------------------------------
□ Update AppDelegate.swift for local notifications
  - File: ios/Runner/AppDelegate.swift
  - Add import: import flutter_local_notifications
  - Add notification delegate setup in didFinishLaunchingWithOptions:
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
    }
  - Expected outcome: Local notifications delegate configured

□ Add Firebase imports
  - File: ios/Runner/AppDelegate.swift
  - Add import: import Firebase
  - Expected outcome: Firebase available in AppDelegate

□ Configure notification handling methods
  - File: ios/Runner/AppDelegate.swift
  - Add UNUserNotificationCenterDelegate methods if needed
  - Expected outcome: Notification handling methods available

4.3 INFO.PLIST CONFIGURATION
-----------------------------
□ Add notification permissions descriptions
  - File: ios/Runner/Info.plist
  - Add key: NSUserNotificationUsageDescription
  - Value: "3Pay Global needs notifications to keep you updated on claim status and important messages"
  - Expected outcome: Permission request description configured

□ Add background modes
  - File: ios/Runner/Info.plist
  - Add key: UIBackgroundModes (if not exists)
  - Add array values:
    * "remote-notification"
    * "background-processing"
  - Expected outcome: Background notification modes enabled

□ Configure notification categories (optional)
  - File: ios/Runner/Info.plist
  - Plan custom notification categories for different types
  - Expected outcome: Notification categories planned

4.4 FIREBASE CONFIGURATION
---------------------------
□ Verify GoogleService-Info.plist placement
  - File should be at: ios/Runner/GoogleService-Info.plist
  - Verify file is added to Xcode project
  - Expected outcome: Firebase configuration file properly placed

□ Add GoogleService-Info.plist to Xcode project
  - In Xcode, right-click Runner folder
  - Add Files to "Runner"
  - Select GoogleService-Info.plist
  - Ensure "Add to target" includes Runner
  - Expected outcome: Configuration file included in build

4.5 PODFILE CONFIGURATION
--------------------------
□ Verify iOS deployment target
  - File: ios/Podfile
  - Ensure: platform :ios, '13.0' or higher
  - Update if necessary
  - Expected outcome: Compatible iOS version for notifications

□ Add Firebase-specific pod configurations
  - File: ios/Podfile
  - Add if needed:
    pod 'Firebase/Messaging'
    pod 'FirebaseCore'
  - Expected outcome: Firebase pods explicitly configured

□ Run pod install
  - Navigate to ios/ directory
  - Run: pod install
  - Verify no errors
  - Expected outcome: iOS dependencies updated

4.6 NOTIFICATION SERVICE EXTENSION (OPTIONAL)
----------------------------------------------
□ Plan notification service extension
  - Consider creating notification service extension for:
    * Rich media notifications
    * Notification modification before display
    * Analytics tracking
  - Expected outcome: Extension strategy planned

□ Create notification service extension (if needed)
  - In Xcode: File > New > Target
  - Choose "Notification Service Extension"
  - Configure target settings
  - Expected outcome: Service extension created

4.7 DEVELOPMENT TEAM AND SIGNING
---------------------------------
□ Configure development team
  - In Xcode "Signing & Capabilities"
  - Select appropriate development team
  - Ensure automatic signing is enabled
  - Expected outcome: Code signing configured

□ Verify provisioning profile
  - Check provisioning profile includes push notification entitlement
  - Update profile if necessary
  - Expected outcome: Push notifications enabled in provisioning

4.8 TESTING PREPARATION
------------------------
□ Configure iOS Simulator testing
  - Note: Push notifications don't work in simulator
  - Plan physical device testing
  - Expected outcome: Testing limitations understood

□ Prepare physical iOS device
  - Ensure device is registered in Apple Developer account
  - Install development build on device
  - Expected outcome: Physical device ready for testing

4.9 BUILD VERIFICATION
-----------------------
□ Test iOS build
  - Run: flutter build ios --debug
  - Verify no build errors
  - Expected outcome: Successful iOS build

□ Install and test on device
  - Run: flutter install (with iOS device connected)
  - Verify app launches without crashes
  - Expected outcome: App runs on iOS device

□ Check Firebase connection
  - Monitor Xcode console for Firebase initialization
  - Look for successful Firebase connection messages
  - Expected outcome: Firebase properly initialized on iOS

□ Test notification permissions
  - Launch app and trigger permission request
  - Verify permission dialog appears
  - Expected outcome: Permission request working

DEPENDENCIES:
- Task 3: Android Platform Setup
- macOS with Xcode installed
- Apple Developer account (for device testing)

NEXT TASK:
- Task 5: Local Notification Service Implementation

FILES MODIFIED:
- ios/Runner/AppDelegate.swift
- ios/Runner/Info.plist
- ios/Podfile

FILES VERIFIED:
- ios/Runner/GoogleService-Info.plist

ESTIMATED TIME:
- 35-45 minutes (including Xcode configuration)

NOTES:
- Xcode is required for iOS capability configuration
- Push notifications cannot be tested in iOS Simulator
- Physical iOS device required for full testing
- Apple Developer account needed for device provisioning
- Keep Bundle ID consistent across all configurations
