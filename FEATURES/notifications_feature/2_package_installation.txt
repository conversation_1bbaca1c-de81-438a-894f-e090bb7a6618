TASK 2: <PERSON><PERSON><PERSON><PERSON> INSTALLATION AND CONFIGURATION
===============================================

OVERVIEW:
Install and configure all required packages for both local and push notifications in the Flutter project.

TASKS:

2.1 FLUTTER PACKAGE INSTALLATION
---------------------------------
□ Install Firebase packages
  - Run: flutter pub add firebase_core firebase_messaging
  - Expected outcome: Firebase packages added to pubspec.yaml
  - Files modified: pubspec.yaml

□ Install local notifications package
  - Run: flutter pub add flutter_local_notifications
  - Expected outcome: Local notifications package added to pubspec.yaml
  - Files modified: pubspec.yaml

□ Verify package installation
  - Run: flutter pub get
  - Check pubspec.yaml contains:
    * firebase_core: ^latest_version
    * firebase_messaging: ^latest_version
    * flutter_local_notifications: ^latest_version
  - Expected outcome: All packages successfully installed

2.2 PUBSPEC.YAML VERIFICATION
------------------------------
□ Verify existing dependencies compatibility
  - Check no version conflicts with existing packages
  - Ensure minimum SDK version supports new packages
  - Expected outcome: No dependency conflicts

□ Document package versions
  - Record exact versions installed for future reference
  - Expected outcome: Version documentation for reproducible builds

2.3 ANDROID GRADLE CONFIGURATION
---------------------------------
□ Update project-level build.gradle
  - File: android/build.gradle.kts
  - Add to dependencies block:
    classpath("com.google.gms:google-services:4.4.0")
  - Expected outcome: Google Services plugin available

□ Update app-level build.gradle
  - File: android/app/build.gradle.kts
  - Add to plugins block:
    id("com.google.gms.google-services")
  - Add to dependencies block:
    implementation("com.google.firebase:firebase-messaging:23.4.0")
  - Expected outcome: Firebase messaging integrated in Android build

□ Place google-services.json
  - Copy google-services.json to android/app/
  - Verify file is in correct location
  - Expected outcome: Android Firebase configuration active

□ Update minimum SDK version if needed
  - File: android/app/build.gradle.kts
  - Ensure minSdkVersion is at least 21
  - Expected outcome: Compatible SDK version for notifications

2.4 IOS CONFIGURATION FILES
----------------------------
□ Place GoogleService-Info.plist
  - Copy GoogleService-Info.plist to ios/Runner/
  - Add to Xcode project (if using Xcode)
  - Expected outcome: iOS Firebase configuration active

□ Update iOS deployment target
  - File: ios/Podfile
  - Ensure platform :ios, '13.0' or higher
  - Expected outcome: Compatible iOS version for notifications

□ Run pod install
  - Navigate to ios/ directory
  - Run: pod install
  - Expected outcome: iOS dependencies installed

2.5 ANDROID MANIFEST PERMISSIONS
---------------------------------
□ Add notification permissions
  - File: android/app/src/main/AndroidManifest.xml
  - Add permissions:
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
  - Expected outcome: Android notification permissions configured

□ Add notification channels configuration
  - File: android/app/src/main/AndroidManifest.xml
  - Add meta-data for default notification channel
  - Expected outcome: Default notification channel configured

2.6 BUILD VERIFICATION
-----------------------
□ Clean and rebuild project
  - Run: flutter clean
  - Run: flutter pub get
  - Run: flutter build apk --debug (Android)
  - Run: flutter build ios --debug (iOS, if on macOS)
  - Expected outcome: Project builds successfully with new packages

□ Verify no build errors
  - Check for any compilation errors
  - Resolve any dependency conflicts
  - Expected outcome: Clean build with no errors

DEPENDENCIES:
- Task 1: Prerequisites and Setup (Firebase project must be configured)

NEXT TASK:
- Task 3: Android Platform Setup

FILES MODIFIED:
- pubspec.yaml
- android/build.gradle.kts
- android/app/build.gradle.kts
- android/app/src/main/AndroidManifest.xml
- ios/Podfile

FILES ADDED:
- android/app/google-services.json
- ios/Runner/GoogleService-Info.plist
- lib/firebase_options.dart (from Task 1)

ESTIMATED TIME:
- 20-30 minutes

NOTES:
- Always run flutter clean after major dependency changes
- Keep package versions documented for team consistency
- Test build on both platforms if possible
