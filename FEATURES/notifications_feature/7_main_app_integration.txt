TASK 7: MAIN APPLICATION INTEGRATION
====================================

OVERVIEW:
Integrate both notification services into the main application, update initialization flow, and configure service locator for dependency injection.

TASKS:

7.1 UPDATE MAIN.DART INITIALIZATION
------------------------------------
□ Add Firebase imports
  - File: lib/main.dart
  - Add imports:
    import 'package:firebase_core/firebase_core.dart';
    import 'package:firebase_messaging/firebase_messaging.dart';
    import 'firebase_options.dart';
  - Expected outcome: Firebase dependencies available in main

□ Add notification service imports
  - File: lib/main.dart
  - Add imports:
    import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';
    import 'package:three_pay_group_litigation_platform/src/core/services/local_notification_service.dart';
  - Expected outcome: Notification services available in main

□ Update main function
  - Make main function async: void main() async
  - Add WidgetsFlutterBinding.ensureInitialized()
  - Initialize Firebase: await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform)
  - Set background message handler: FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler)
  - Expected outcome: Firebase initialized before app starts

□ Initialize notification services
  - Add LocalNotificationService.initialize()
  - Add FirebaseApiService.initNotifications()
  - Handle initialization errors gracefully
  - Expected outcome: Both notification services initialized

7.2 UPDATE SERVICE LOCATOR
---------------------------
□ Register notification services
  - File: lib/src/core/services/service_locator.dart
  - Add LocalNotificationService registration
  - Add FirebaseApiService registration
  - Configure as singletons
  - Expected outcome: Notification services available via dependency injection

□ Update initializeAppServices method
  - Add notification service initialization calls
  - Ensure proper initialization order
  - Handle service dependencies
  - Expected outcome: Services initialized in correct order

□ Add service disposal
  - Update disposal methods to include notification services
  - Ensure proper cleanup on app termination
  - Expected outcome: Clean service lifecycle management

7.3 UPDATE EXISTING NOTIFICATION SERVICE
-----------------------------------------
□ Integrate with LocalNotificationService
  - File: lib/src/core/services/notification_service.dart
  - Update _showToast method to use LocalNotificationService
  - Convert EnhancedNotificationModel to local notification format
  - Use appropriate notification channels
  - Expected outcome: In-app notifications trigger local notifications

□ Add notification type mapping
  - Map notification types to local notification channels:
    * 'claim_update' -> CLAIMS_CHANNEL_ID
    * 'funding_opportunity' -> FUNDING_CHANNEL_ID
    * 'message' -> MESSAGES_CHANNEL_ID
    * default -> DEFAULT_CHANNEL_ID
  - Expected outcome: Proper channel usage for different notification types

□ Update notification creation
  - Modify notification creation to include FCM-compatible data
  - Add payload structure for navigation
  - Ensure consistency between local and push notifications
  - Expected outcome: Unified notification data structure

7.4 UPDATE AUTHENTICATION FLOW
-------------------------------
□ Integrate FCM token management with login
  - File: lib/src/core/services/pocketbase_service.dart (or auth service)
  - Call FirebaseApiService.refreshToken() after successful login
  - Handle token refresh errors
  - Expected outcome: FCM tokens updated on login

□ Integrate FCM token cleanup with logout
  - Call FirebaseApiService.clearToken() before logout
  - Ensure token is removed from PocketBase
  - Handle cleanup errors gracefully
  - Expected outcome: FCM tokens cleaned up on logout

□ Update user registration flow
  - Add FCM token initialization for new users
  - Handle permission requests during onboarding
  - Expected outcome: New users properly configured for notifications

7.5 UPDATE NAVIGATION SYSTEM
-----------------------------
□ Add notification navigation routes
  - File: lib/src/core/routing/app_router.dart (or routing file)
  - Add routes for notification-triggered navigation
  - Handle deep linking from notifications
  - Expected outcome: Notification navigation routes available

□ Create navigation helper
  - Create NotificationNavigationHelper class
  - Centralize notification navigation logic
  - Handle different user types and permissions
  - Expected outcome: Centralized notification navigation

□ Update route guards
  - Ensure notification routes respect authentication
  - Handle unauthorized navigation attempts
  - Redirect to login if needed
  - Expected outcome: Secure notification navigation

7.6 UPDATE APP LIFECYCLE HANDLING
----------------------------------
□ Handle app state changes
  - File: lib/main.dart or app widget
  - Implement WidgetsBindingObserver
  - Handle didChangeAppLifecycleState
  - Refresh FCM token on app resume
  - Expected outcome: Proper handling of app lifecycle

□ Add background app refresh
  - Handle app returning from background
  - Refresh notification state
  - Sync with PocketBase if needed
  - Expected outcome: Fresh notification state on app resume

□ Handle app termination
  - Ensure proper service cleanup
  - Save notification state if needed
  - Expected outcome: Clean app termination

7.7 ADD GLOBAL NAVIGATION KEY
-----------------------------
□ Create global navigation key
  - File: lib/main.dart
  - Create static GlobalKey<NavigatorState> navigatorKey
  - Assign to MaterialApp navigatorKey property
  - Export for use in notification services
  - Expected outcome: Global navigation available for notifications

□ Update notification services
  - Import global navigation key in FirebaseApiService
  - Use for navigation from background notifications
  - Handle navigation context properly
  - Expected outcome: Background notifications can navigate

7.8 ADD ERROR HANDLING AND LOGGING
-----------------------------------
□ Add initialization error handling
  - Wrap all initialization calls in try-catch
  - Log initialization failures
  - Provide fallback behavior for failed services
  - Expected outcome: Graceful handling of initialization failures

□ Add service health monitoring
  - Create service health check methods
  - Log service status periodically
  - Handle service failures gracefully
  - Expected outcome: Monitoring and recovery for notification services

□ Add user feedback for errors
  - Show user-friendly messages for permission denials
  - Provide guidance for enabling notifications
  - Handle service unavailability
  - Expected outcome: Good user experience even with errors

DEPENDENCIES:
- Task 6: Firebase Push Notification Service Implementation
- Task 5: Local Notification Service Implementation
- Existing service locator and routing infrastructure

NEXT TASK:
- Task 8: Authentication Integration

FILES MODIFIED:
- lib/main.dart
- lib/src/core/services/service_locator.dart
- lib/src/core/services/notification_service.dart
- lib/src/core/services/pocketbase_service.dart (or auth service)
- lib/src/core/routing/app_router.dart (or routing file)

ESTIMATED TIME:
- 45-60 minutes

SAMPLE MAIN.DART STRUCTURE:
```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  
  // Set background message handler
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  
  // Initialize notification services
  await LocalNotificationService.initialize();
  await FirebaseApiService.initNotifications();
  
  runApp(MyApp());
}
```

NOTES:
- Test initialization on both platforms
- Verify services are available throughout the app
- Handle initialization failures gracefully
- Consider initialization performance impact
- Document service dependencies clearly
- Test navigation from different app states
