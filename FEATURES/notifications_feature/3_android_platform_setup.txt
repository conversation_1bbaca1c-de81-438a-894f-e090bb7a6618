TASK 3: ANDROID PLATFORM SETUP
===============================

OVERVIEW:
Configure Android-specific settings for both local and push notifications, including permissions, channels, and native code setup.

TASKS:

3.1 ANDROID MANIFEST CONFIGURATION
-----------------------------------
□ Configure notification permissions
  - File: android/app/src/main/AndroidManifest.xml
  - Add inside <manifest> tag:
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
  - Expected outcome: All required permissions declared

□ Configure notification channels metadata
  - File: android/app/src/main/AndroidManifest.xml
  - Add inside <application> tag:
    <meta-data
        android:name="com.google.firebase.messaging.default_notification_channel_id"
        android:value="3pay_default_channel" />
    <meta-data
        android:name="com.google.firebase.messaging.default_notification_icon"
        android:value="@mipmap/ic_launcher" />
  - Expected outcome: Default notification settings configured

□ Add Firebase messaging service
  - File: android/app/src/main/AndroidManifest.xml
  - Add inside <application> tag:
    <service
        android:name="com.google.firebase.messaging.FirebaseMessagingService"
        android:exported="false">
        <intent-filter>
            <action android:name="com.google.firebase.MESSAGING_EVENT" />
        </intent-filter>
    </service>
  - Expected outcome: Firebase messaging service registered

3.2 NOTIFICATION ICONS SETUP
-----------------------------
□ Verify app icon exists
  - Check: android/app/src/main/res/mipmap-*/ic_launcher.png files exist
  - Expected outcome: Default app icon available for notifications

□ Create notification icon (optional)
  - Create: android/app/src/main/res/drawable/notification_icon.xml
  - Design: Simple white icon on transparent background
  - Expected outcome: Custom notification icon available

□ Update notification icon reference
  - File: android/app/src/main/AndroidManifest.xml
  - Update meta-data to use custom icon if created:
    android:value="@drawable/notification_icon"
  - Expected outcome: Custom notification icon configured

3.3 GRADLE BUILD CONFIGURATION
-------------------------------
□ Verify Google Services plugin
  - File: android/app/build.gradle.kts
  - Ensure plugins block contains:
    id("com.google.gms.google-services")
  - Expected outcome: Google Services plugin active

□ Configure ProGuard rules (if using)
  - File: android/app/proguard-rules.pro
  - Add Firebase-specific rules:
    -keep class com.google.firebase.** { *; }
    -keep class com.google.android.gms.** { *; }
  - Expected outcome: Firebase classes preserved in release builds

□ Verify minimum SDK version
  - File: android/app/build.gradle.kts
  - Ensure minSdk is at least 21
  - Update if necessary: minSdk = 21
  - Expected outcome: Compatible SDK version for all notification features

3.4 NOTIFICATION CHANNELS PREPARATION
--------------------------------------
□ Plan notification channels
  - Document channel IDs to be used:
    * "3pay_default_channel" - General notifications
    * "3pay_claims_channel" - Claim updates
    * "3pay_funding_channel" - Funding opportunities
    * "3pay_messages_channel" - Agent messages
  - Expected outcome: Channel strategy documented

□ Prepare channel importance levels
  - High importance: Critical claim updates
  - Default importance: General notifications
  - Low importance: Marketing/educational content
  - Expected outcome: Importance levels planned

3.5 BACKGROUND PROCESSING SETUP
--------------------------------
□ Configure background execution
  - File: android/app/src/main/AndroidManifest.xml
  - Add background processing permissions if needed
  - Expected outcome: Background notification handling enabled

□ Verify battery optimization handling
  - Plan user education for battery optimization whitelist
  - Expected outcome: Strategy for reliable background notifications

3.6 TESTING PREPARATION
------------------------
□ Enable developer options on test device
  - Settings > About phone > Tap build number 7 times
  - Enable USB debugging
  - Expected outcome: Test device ready for debugging

□ Configure test notification channels
  - Plan test scenarios for different notification types
  - Expected outcome: Testing strategy prepared

□ Verify Google Play Services
  - Ensure test device has Google Play Services installed
  - Update to latest version if needed
  - Expected outcome: Firebase messaging will work on test device

3.7 BUILD VERIFICATION
-----------------------
□ Test Android build
  - Run: flutter build apk --debug
  - Verify no build errors
  - Expected outcome: Successful Android build

□ Install and test on device
  - Run: flutter install
  - Verify app launches without crashes
  - Expected outcome: App runs on Android device

□ Check Firebase connection
  - Monitor Android Studio logcat for Firebase initialization
  - Look for successful Firebase connection messages
  - Expected outcome: Firebase properly initialized on Android

DEPENDENCIES:
- Task 2: Package Installation and Configuration

NEXT TASK:
- Task 4: iOS Platform Setup

FILES MODIFIED:
- android/app/src/main/AndroidManifest.xml
- android/app/build.gradle.kts
- android/app/proguard-rules.pro (if applicable)

FILES POTENTIALLY ADDED:
- android/app/src/main/res/drawable/notification_icon.xml

ESTIMATED TIME:
- 25-35 minutes

NOTES:
- Test on multiple Android versions if possible (API 21+)
- Consider different device manufacturers (Samsung, Huawei, etc.)
- Document any device-specific issues encountered
- Keep notification channel IDs consistent across the app
