TASK 1: <PERSON><PERSON><PERSON><PERSON> CLOUD PROJECT AND DRIVE API SETUP
================================================

OVERVIEW:
Set up Google Cloud Project, enable Drive API, create service accounts, and configure authentication for the 3Pay Global document storage migration.

TASKS:

1.1 GOOGLE CLOUD PROJECT CREATION
----------------------------------
□ Create new Google Cloud Project
  - Project name: "3pay-global-document-storage"
  - Project ID: "3pay-global-docs-[environment]" (dev/staging/prod)
  - Organization: Link to 3Pay Global organization
  - Expected outcome: Active Google Cloud Project with billing enabled

□ Enable required APIs
  - Google Drive API v3
  - Google Cloud Storage API (for backup)
  - Cloud Resource Manager API
  - Identity and Access Management (IAM) API
  - Expected outcome: All APIs enabled and accessible

□ Set up billing account
  - Link appropriate billing account
  - Set up budget alerts for API usage
  - Configure spending limits
  - Expected outcome: Billing configured with monitoring

1.2 SERVICE ACCOUNT CONFIGURATION
---------------------------------
□ Create service account for document operations
  - Service account name: "3pay-docs-service"
  - Service account ID: "<EMAIL>" #<EMAIL> <EMAIL>
  - Description: "Service account for 3Pay Global document storage operations"
  - Expected outcome: Service account created and configured

□ Generate service account credentials
  - Create JSON key file
  - Download and securely store credentials
  - Set up credential rotation schedule
  - Expected outcome: JSON credentials file available for application use

□ Configure service account permissions
  - Note: Service accounts access Google Drive through API scopes, not IAM roles
  - Grant "Storage Admin" role (for Google Cloud Storage backups if needed)
  - Grant "Service Account User" role if the service account will be used by other services
  - Configure least-privilege access for any additional Google Cloud resources
  - Expected outcome: Service account has minimum required IAM permissions for Google Cloud resources

1.3 GOOGLE DRIVE API CONFIGURATION
-----------------------------------
□ Configure Drive API settings
  - Set up API quotas and limits (default: 1,000 requests per 100 seconds per user)
  - Configure rate limiting and monitoring
  - Set up API usage monitoring and alerting
  - Expected outcome: Drive API properly configured with monitoring

□ Configure API scopes for service account
  - Primary scope: https://www.googleapis.com/auth/drive (full Drive access)
  - Alternative scope: https://www.googleapis.com/auth/drive.file (access only to files created by the app)
  - Note: Scopes are configured in the application code, not in Google Cloud Console
  - Expected outcome: Appropriate API scopes identified for implementation

□ Create root folder structure in Google Drive
  - Create "3Pay Global Documents" root folder using the service account
  - Create subfolders: "Claims", "Templates", "Archived", "Shared"
  - Set appropriate permissions on folders (service account as owner)
  - Expected outcome: Folder structure created and accessible

□ Test API connectivity and permissions
  - Create test script to verify API access with service account credentials
  - Test file upload/download operations with proper scopes
  - Verify folder creation and permission management
  - Test API quota usage and rate limiting
  - Expected outcome: API connectivity and permissions confirmed working

1.4 SECURITY AND ACCESS CONTROL
--------------------------------
□ Configure OAuth 2.0 settings (for future user access)
  - Set up OAuth consent screen
  - Configure authorized domains
  - Set up redirect URIs
  - Expected outcome: OAuth configured for future user authentication

□ Set up API key restrictions
  - Restrict API keys to specific APIs
  - Configure IP restrictions if needed
  - Set up referrer restrictions
  - Expected outcome: API keys properly secured

□ Configure audit logging
  - Enable Cloud Audit Logs
  - Configure log retention policies
  - Set up log monitoring
  - Expected outcome: Comprehensive audit logging enabled

1.5 ENVIRONMENT CONFIGURATION
------------------------------
□ Set up development environment
  - Create separate project for development
  - Configure development-specific settings
  - Set up test data and folders
  - Expected outcome: Development environment ready for testing

□ Set up staging environment
  - Create staging project
  - Mirror production configuration
  - Set up staging-specific folders
  - Expected outcome: Staging environment ready for pre-production testing

□ Set up production environment
  - Create production project
  - Configure production security settings
  - Set up production folder structure
  - Expected outcome: Production environment ready for deployment

1.6 MONITORING AND ALERTING
----------------------------
□ Configure Google Cloud Monitoring
  - Set up API usage monitoring
  - Configure quota monitoring
  - Set up error rate monitoring
  - Expected outcome: Comprehensive monitoring dashboard

□ Set up alerting policies
  - API quota threshold alerts (80% usage)
  - Error rate threshold alerts (>5%)
  - Unusual activity alerts
  - Expected outcome: Proactive alerting system configured

□ Configure notification channels
  - Email notifications for critical alerts
  - Slack integration for team notifications
  - SMS for emergency alerts
  - Expected outcome: Multi-channel alerting system

1.7 DOCUMENTATION AND CREDENTIALS MANAGEMENT
---------------------------------------------
□ Document API configuration
  - Document all API settings and configurations
  - Create setup guides for each environment
  - Document security policies and procedures
  - Expected outcome: Comprehensive documentation available

□ Secure credential storage
  - Store service account keys in secure location
  - Set up credential rotation procedures
  - Document credential access procedures
  - Expected outcome: Secure credential management system

□ Create environment configuration files
  - Create .env templates for each environment
  - Document required environment variables
  - Set up secure configuration management
  - Expected outcome: Environment configuration templates ready

DEPENDENCIES:
- None (this is the starting point)

NEXT TASK:
- Task 2: PocketBase Schema Modifications

FILES CREATED:
- Google Cloud Project configurations
- Service account JSON credentials
- Environment configuration templates
- API documentation

ESTIMATED TIME:
- 4-6 hours

TESTING REQUIREMENTS:
- Verify API connectivity from development environment
- Test file upload/download operations
- Validate folder permissions and access control
- Confirm monitoring and alerting functionality

ROLLBACK PROCEDURES:
- Disable APIs if issues occur
- Revoke service account permissions
- Delete test folders and files
- Document any configuration changes for rollback

NOTES:
- Keep all credentials secure and never commit to version control
- Use separate projects for each environment
- Monitor API usage closely during initial setup
- Document all configuration decisions for future reference
- Google Drive access is controlled by API scopes in the application, not IAM roles
- Service accounts automatically have access to Google Drive API when proper scopes are used
- The service account will own the files it creates in Google Drive
- Consider using https://www.googleapis.com/auth/drive.file scope for more restrictive access
