TASK 3: GOOG<PERSON> DRIVE SERVICE IMPLEMENTATION
===========================================

OVERVIEW:
Implement the core GoogleDriveService class to handle all Google Drive API operations including authentication, file management, folder operations, and permission management.

TASKS:

3.1 PACKAGE DEPENDENCIES
-------------------------
□ Add required packages to pubspec.yaml
  - googleapis: ^11.4.0 (Google APIs client library)
  - googleapis_auth: ^1.4.1 (Google APIs authentication)
  - crypto: ^3.0.3 (for checksums and encryption)
  - mime: ^1.0.4 (for MIME type detection)
  - Expected outcome: All required packages added and resolved

□ Update import statements
  - Add Google Drive API imports
  - Add authentication imports
  - Add utility imports
  - Expected outcome: Clean import structure for Google Drive operations

3.2 CORE SERVICE IMPLEMENTATION
-------------------------------
□ Create GoogleDriveService class
  - File: lib/src/core/services/google_drive_service.dart
  - Implement singleton pattern for service instance
  - Add service initialization and configuration
  - Expected outcome: Core service class structure created

□ Implement authentication
  - Service account authentication using JSON credentials
  - Configure appropriate API scopes (https://www.googleapis.com/auth/drive or https://www.googleapis.com/auth/drive.file)
  - Token management and refresh (handled automatically by googleapis_auth)
  - Error handling for authentication failures
  - Expected outcome: Robust authentication system with proper scopes

□ Add configuration management
  - Load configuration from PocketBase google_drive_config
  - Environment-specific configuration handling
  - Configuration caching and refresh
  - Expected outcome: Flexible configuration management

3.3 FILE OPERATIONS IMPLEMENTATION
-----------------------------------
□ Implement file upload functionality
  - Single file upload with metadata
  - Chunked upload for large files (>5MB)
  - Progress tracking and callbacks
  - Retry mechanism for failed uploads
  - Expected outcome: Reliable file upload system

□ Implement file download functionality
  - Direct download URL generation
  - Streaming download for large files
  - Download progress tracking
  - Temporary URL generation with expiration
  - Expected outcome: Efficient file download system

□ Implement file management operations
  - File existence checking
  - File metadata retrieval
  - File deletion with confirmation
  - File copying and moving
  - Expected outcome: Complete file management capabilities

□ Add file validation and integrity checks
  - Checksum calculation and verification
  - MIME type validation
  - File size validation
  - Virus scanning integration (future)
  - Expected outcome: Secure file handling with integrity checks

3.4 FOLDER OPERATIONS IMPLEMENTATION
-------------------------------------
□ Implement folder management
  - Folder creation with proper hierarchy
  - Folder listing and navigation
  - Folder permission management
  - Folder deletion with safety checks
  - Expected outcome: Complete folder management system

□ Implement folder structure automation
  - Auto-create folder structure for new claims
  - Template-based folder creation
  - Folder naming conventions enforcement
  - Cleanup of empty folders
  - Expected outcome: Automated folder organization

□ Add folder search and filtering
  - Search folders by name and metadata
  - Filter folders by creation date and permissions
  - Hierarchical folder traversal
  - Expected outcome: Efficient folder discovery

3.5 PERMISSION MANAGEMENT IMPLEMENTATION
-----------------------------------------
□ Implement permission system
  - Role-based permission mapping
  - User permission assignment
  - Permission inheritance handling
  - Permission revocation
  - Expected outcome: Comprehensive permission management

□ Add sharing functionality
  - Share files with specific users
  - Generate shareable links with expiration
  - Manage sharing permissions
  - Revoke sharing access
  - Expected outcome: Secure sharing capabilities

□ Implement access control validation
  - Validate user access before operations
  - Check permission levels for actions
  - Audit permission changes
  - Expected outcome: Secure access control system

3.6 ERROR HANDLING AND RESILIENCE
----------------------------------
□ Implement comprehensive error handling
  - API rate limiting handling with exponential backoff
  - Network connectivity error handling
  - Authentication error recovery
  - Quota exceeded error handling
  - Expected outcome: Resilient service with graceful error handling

□ Add retry mechanisms
  - Configurable retry policies
  - Circuit breaker pattern for API failures
  - Fallback strategies for critical operations
  - Expected outcome: Reliable service operation under adverse conditions

□ Implement logging and monitoring
  - Structured logging for all operations
  - Performance metrics collection
  - Error tracking and reporting
  - API usage monitoring
  - Expected outcome: Comprehensive observability

3.7 CACHING AND PERFORMANCE OPTIMIZATION
-----------------------------------------
□ Implement metadata caching
  - Cache file and folder metadata
  - Cache permission information
  - Cache folder structure
  - TTL-based cache invalidation
  - Expected outcome: Improved performance through intelligent caching

□ Add batch operations
  - Batch file uploads
  - Batch permission updates
  - Batch metadata retrieval
  - Expected outcome: Efficient bulk operations

□ Implement connection pooling
  - HTTP connection reuse
  - Connection timeout management
  - Connection health monitoring
  - Expected outcome: Optimized network resource usage

3.8 UTILITY METHODS AND HELPERS
--------------------------------
□ Implement utility methods
  - MIME type detection from file content
  - File size formatting and validation
  - URL generation and validation
  - Path manipulation utilities
  - Expected outcome: Comprehensive utility support

□ Add data transformation helpers
  - Convert between PocketBase and Drive formats
  - Metadata serialization/deserialization
  - Date/time format conversions
  - Expected outcome: Seamless data format handling

□ Implement validation helpers
  - File name validation
  - Path validation
  - Permission validation
  - Configuration validation
  - Expected outcome: Robust input validation

DEPENDENCIES:
- Task 1: Google Cloud Project and Drive API Setup
- Task 2: PocketBase Schema Modifications

NEXT TASK:
- Task 4: Document Cache Service Implementation

FILES CREATED:
- lib/src/core/services/google_drive_service.dart
- lib/src/core/services/google_drive_auth.dart
- lib/src/core/services/google_drive_config.dart
- lib/src/core/models/google_drive_file.dart
- lib/src/core/models/google_drive_permission.dart
- lib/src/core/utils/google_drive_utils.dart

FILES MODIFIED:
- pubspec.yaml
- lib/src/core/services/service_locator.dart

ESTIMATED TIME:
- 12-16 hours

TESTING REQUIREMENTS:
- Unit tests for all public methods
- Integration tests with Google Drive API
- Error scenario testing
- Performance testing with large files
- Authentication testing
- Permission management testing

ROLLBACK PROCEDURES:
- Remove Google Drive service from service locator
- Revert pubspec.yaml changes
- Remove all Google Drive service files
- Verify existing functionality unaffected

SAMPLE CODE STRUCTURE:
```dart
class GoogleDriveService {
  static final GoogleDriveService _instance = GoogleDriveService._internal();
  factory GoogleDriveService() => _instance;
  GoogleDriveService._internal();

  static const List<String> _scopes = [DriveApi.driveScope]; // or DriveApi.driveFileScope for restricted access
  late final DriveApi _driveApi;
  bool _isInitialized = false;

  Future<void> initialize() async {
    final credentials = await _loadServiceAccountCredentials();
    final client = await clientViaServiceAccount(credentials, _scopes);
    _driveApi = DriveApi(client);
    _isInitialized = true;
  }

  Future<String> uploadFile(File file, String folderId, Map<String, String> metadata) async { /* implementation */ }
  Future<void> downloadFile(String fileId, String localPath) async { /* implementation */ }
  Future<String> getFileUrl(String fileId) async { /* implementation */ }
  Future<void> deleteFile(String fileId) async { /* implementation */ }
  Future<String> createFolder(String name, String parentId) async { /* implementation */ }
  Future<void> setFilePermissions(String fileId, List<Permission> permissions) async { /* implementation */ }
}
```

NOTES:
- Follow existing 3Pay Global service patterns
- Use LoggerService for all logging
- Implement proper error handling with user-friendly messages
- Cache frequently accessed data for performance
- Follow Google Drive API best practices
- Ensure thread safety for concurrent operations
- Google Drive access is controlled by API scopes, not IAM roles
- Use DriveApi.driveScope for full access or DriveApi.driveFileScope for restricted access
- Service account will own all files it creates in Google Drive
- Consider file sharing permissions for user access to service account-owned files
