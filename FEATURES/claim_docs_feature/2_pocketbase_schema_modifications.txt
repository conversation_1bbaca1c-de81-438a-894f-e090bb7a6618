TASK 2: POCKETBASE SCHEMA MODIFICATIONS
=======================================

OVERVIEW:
Modify existing PocketBase collections and create new collections to support Google Drive integration as the primary storage solution for all document operations.

TASKS:

2.1 SCHEMA PREPARATION
----------------------
□ Document current claim_documents collection structure
  - Document all existing fields and their usage
  - Map current file relationships
  - Document existing API dependencies
  - Expected outcome: Comprehensive documentation of current state

□ Create schema backup procedures
  - Document steps to backup collection schema
  - Create backup scripts for development testing
  - Test backup procedures in development
  - Expected outcome: Tested backup procedures available

2.2 MODIFY CLAIM_DOCUMENTS COLLECTION
--------------------------------------
□ Add Google Drive integration fields
  - Add field: google_drive_folder_id (Text, required)
  - Add field: storage_type (Select: google_drive) - Default to google_drive for all uploads
  - Expected outcome: New fields added for Google Drive support

□ Modify versions field structure
  - Update versions JSON schema to include Google Drive fields
  - Add google_drive_id field to version objects (required)
  - Add google_drive_url field to version objects (required)
  - Add download_url field to version objects (required)
  - Add file_size field to version objects (required)
  - Add mime_type field to version objects (required)
  - Add checksum field to version objects (required)
  - Expected outcome: Enhanced version tracking with Google Drive metadata

□ Update collection rules and permissions
  - Maintain existing read/write permissions
  - Configure for Google Drive as primary storage
  - Expected outcome: Permissions updated for Google Drive integration

2.3 CREATE GOOGLE_DRIVE_CONFIG COLLECTION
------------------------------------------
□ Create new collection: google_drive_config
  - Field: service_account_email (Email, required)
  - Field: root_folder_id (Text, required)
  - Field: api_quota_limit (Number, default: 1000000)
  - Field: api_quota_used (Number, default: 0)
  - Field: last_quota_reset (DateTime, required)
  - Field: encryption_enabled (Bool, default: true)
  - Field: backup_enabled (Bool, default: true)
  - Field: environment (Select: development, staging, production)
  - Expected outcome: Configuration collection created and populated

□ Set up collection permissions
  - Admin-only write access
  - Service-level read access
  - No public access
  - Expected outcome: Secure configuration management

□ Create initial configuration records
  - Development environment config
  - Staging environment config
  - Production environment config
  - Expected outcome: Environment-specific configurations ready

2.4 CREATE DOCUMENT_ACCESS_LOGS COLLECTION
-------------------------------------------
□ Create new collection: document_access_logs
  - Field: document_id (Relation to claim_documents, required)
  - Field: user_id (Relation to users, required)
  - Field: action (Select: upload, download, view, share, delete)
  - Field: timestamp (DateTime, required)
  - Field: ip_address (Text, optional)
  - Field: user_agent (Text, optional)
  - Field: success (Bool, required)
  - Field: error_message (Text, optional)
  - Field: storage_type (Select: google_drive)
  - Field: google_drive_file_id (Text, required)
  - Field: file_size (Number, optional)
  - Field: duration_ms (Number, optional)
  - Expected outcome: Comprehensive audit logging capability

□ Set up collection permissions
  - Service-level write access
  - Admin read access
  - User read access for own records
  - Expected outcome: Secure audit logging with appropriate access

□ Configure data retention
  - Set up automatic cleanup for old logs (90 days)
  - Configure archival procedures
  - Set up log rotation
  - Expected outcome: Efficient log management system

2.5 UPDATE EXISTING COLLECTIONS
--------------------------------
□ Update users collection for Drive permissions
  - Add field: google_drive_permissions (JSON, optional)
  - Add field: drive_access_enabled (Bool, default: true)
  - Expected outcome: User-level Drive access control

2.6 CREATE DATABASE VIEWS AND INDEXES
--------------------------------------
□ Create performance indexes
  - Index on claim_documents.storage_type
  - Index on document_access_logs.timestamp
  - Index on document_access_logs.user_id
  - Composite index on (funding_application_id, logical_name)
  - Expected outcome: Optimized query performance

□ Create database views for reporting
  - Document access summary view
  - Storage usage summary view
  - Expected outcome: Efficient reporting capabilities

2.7 DATA VALIDATION AND TESTING
--------------------------------
□ Validate schema changes
  - Test all existing API endpoints
  - Verify data integrity
  - Test collection permissions
  - Expected outcome: Schema changes don't break existing functionality

□ Create test data
  - Create sample documents with new Google Drive fields
  - Test Google Drive integration workflows
  - Validate audit logging
  - Expected outcome: Test data available for development

□ Performance testing
  - Test query performance with new indexes
  - Validate collection access times
  - Test concurrent access scenarios
  - Expected outcome: Performance meets requirements

DEPENDENCIES:
- Task 1: Google Cloud Project and Drive API Setup

NEXT TASK:
- Task 3: Google Drive Service Implementation

FILES MODIFIED:
- PocketBase collection schemas
- Collection permission rules

FILES CREATED:
- Schema backup files
- Test data scripts
- Documentation updates

ESTIMATED TIME:
- 6-8 hours

TESTING REQUIREMENTS:
- Verify all existing functionality still works
- Test new field access and updates
- Validate collection permissions
- Performance testing with new indexes

ROLLBACK PROCEDURES:
- Restore original collection schemas from backup
- Remove new collections
- Restore original permissions
- Verify all existing functionality restored

NOTES:
- Focus on Google Drive as primary storage solution
- Test thoroughly in development before staging
- Document all schema changes
- Keep schema modification scripts for future reference
- Monitor performance impact of new indexes
