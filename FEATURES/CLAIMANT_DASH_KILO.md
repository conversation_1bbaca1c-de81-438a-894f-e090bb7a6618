## Claimant Dashboard Architecture

### 1. Introduction
The Claimant Dashboard provides a centralized interface for claimants to manage their claims, receive updates, communicate with 3Pay support, manage their profile, and track funding activities. This document outlines the proposed architecture for this dashboard, assuming a Flutter-based mobile application and a backend system like PocketBase (inferred from the project structure).

### 2. Key Features
1.  **Claims Management:** View claims and track their statuses.
2.  **Notifications:** Receive important updates and alerts.
3.  **Chat:** Communicate with 3Pay agents/admins.
4.  **Profile Page:** View and manage personal information.
5.  **Funding Activities:** Track financial transactions related to claims.

### 3. High-Level Architecture Diagram

```mermaid
graph TD
    subgraph "3Pay Global Mobile App"
        App[Claimant App] --> Dash(Claimant Dashboard)
        Dash --> ModCM[1. Claims Management]
        Dash --> ModN[2. Notifications]
        Dash --> ModChat[3. Chat Module]
        Dash --> ModP[4. Profile Management]
        Dash --> ModFA[5. Funding Activities]
    end

    subgraph "Backend (PocketBase)"
        PB[3Pay PocketBase Server]
        ColClaims[funding_applications]
        ColNotifications[notifications]
        ColChatMsgs[application_communications]
        ColChatConvos[application_chat_conversations]
        ColUsers[users]
        ColFunding[funding_commitments]
    end

    ModCM -- "API Calls (CRUD)" --> ColClaims
    ModN -- "API Calls (Read, Update)" --> ColNotifications
    ModN -- "Real-time Sub" --> PB
    ModChat -- "API Calls (CRUD)" --> ColChatMsgs
    ModChat -- "API Calls (Read, Update)" --> ColChatConvos
    ModChat -- "Real-time Sub" --> PB
    ModP -- "API Calls (Read, Update)" --> ColUsers
    ModFA -- "API Calls (Read)" --> ColFunding

    style App fill:#lightgrey,stroke:#333,stroke-width:2px
    style Dash fill:#lightblue,stroke:#333,stroke-width:2px
    style PB fill:#lightgreen,stroke:#333,stroke-width:2px
```

### 4. Detailed Component Breakdown

#### 4.1. Claims Management
*   **Purpose:** Allows claimants to view a list of their submitted claims and track the status of each.
*   **UI Components:**
    *   `ClaimsListView`: Displays a summary of each claim (e.g., Claim ID, Title, Current Status, Submission Date).
    *   `ClaimDetailView`: Shows comprehensive information for a selected claim, including status history, documents, and detailed descriptions.
    *   `StatusTimelineWidget`: Visually represents the progression of a claim's status.
    *   Filtering and Sorting options (e.g., by status, date).
*   **Data Models (Conceptual - PocketBase Collections):**
    *   `funding_applications`: this collection already exists and will be used to display claims.
        
*   **Backend Interaction:**
    *   Fetch list of claims for the logged-in claimant.
    *   Fetch details for a specific claim.
    *   Real-time updates for claim status changes.

#### 4.2. Notifications
*   **Purpose:** Informs claimants about important events and updates.
*   **UI Components:**
    *   `NotificationBellIcon`: Indicates new notifications in the app header.
    *   `NotificationsListView`: Lists all notifications (title, snippet, timestamp, read/unread status).
    *   `NotificationDetailView`: Displays the full content of a selected notification.
*   **Data Models (Conceptual - PocketBase Collections):**
    *   `notifications`: this collection already exists and will be used to display notifications.
    
*   **Backend Interaction:**
    *   Fetch notifications for the logged-in claimant.
    *   Mark notifications as read/unread.
    *   Real-time subscription for new notifications.

#### 4.3. Chat with 3Pay Agents/Admins
*   **Purpose:** Enables direct communication between claimants and support personnel.
*   **UI Components:**
    *   `ChatIconOnClaim`: A chat icon on the claim card for quick access and initiate a chat.
    *   `ChatMessageView`: The main chat interface displaying messages, input field, and send button.
*   **Data Models (Conceptual - PocketBase Collections):**
    *   `application_communications`: this collection already exists and will be used to display chat messages.
        *   
*   **Backend Interaction:**
    *   Fetch list of chat conversations.
    *   Fetch messages for a selected conversation.
    *   Send new messages.
    *   Real-time subscription for new messages and conversation updates.
    *   use exisitng code in application_chat_page.dart in the codebase

#### 4.4. Profile Page
*   **Purpose:** Allows claimants to view and manage their personal and account information.
*   **UI Components:**
    *   `ProfileView`: Displays user information (name, email, phone, address).
    *   `EditProfileForm`: Allows modification of profile details.
    *   `ChangePasswordForm`.
    *   `NotificationSettings`: Toggle preferences for different notification types.
*   **Data Models (Conceptual - PocketBase Collections):**
    *   `users` (PocketBase default auth collection, extended or linked to a `claimant_profiles` collection):
*   **Backend Interaction:**
    *   Fetch claimant profile data.
    *   Update profile data.
    *   Handle password change requests. (use pocketbase's built in password change functionality)
    *   Update notification preferences. (saved to `claimant_profiles` collection)

#### 4.5. Funding Activities
*   **Purpose:** Provides claimants with a transparent view of all financial transactions related to their claims.
*   **UI Components:**
    *   `FundingActivityListView`: Lists transactions (date, description, amount, status).
    *   `TransactionDetailView` (Optional): For more detailed information if needed.
    *   Filtering options (e.g., by date range, type of activity).
*   **Data Models (Conceptual - PocketBase Collections):**
    *   `funding_commitments`: this collection already exists and will be used to display funding activities.
      
*   **Backend Interaction:**
    *   Fetch list of funding activities for the claims for the logged-in claimant.

### 5. General Considerations
*   **Authentication & Authorization:** Securely managed by the backend (PocketBase handles this well). API endpoints must be protected. - DONE
*   **Error Handling:** Comprehensive error handling for API calls and user interactions.
*   **UI/UX:** Clean, intuitive, and responsive design.
*   **Offline Support (Optional):** Consider caching key data for limited offline access if required.