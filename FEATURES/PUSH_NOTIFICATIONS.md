# Push Notifications Integration Guide

This document outlines how to integrate Firebase Cloud Messaging (FCM) push notifications into the 3Pay Global Flutter application.

## Overview

Push notifications allow the app to send notifications to users even when the app is closed or in the background. This is essential for:
- Claim status updates when app is closed
- New funding opportunities alerts
- Important messages from 3Pay Global agents
- Document upload notifications
- Payment and funding confirmations

## Prerequisites

1. **Firebase Project Setup**
   - Create a Firebase project at [Firebase Console](https://console.firebase.google.com)
   - Enable Cloud Messaging service
   - Configure for both Android and iOS platforms

2. **FlutterFire CLI Installation**
   ```bash
   dart pub global activate flutterfire_cli
   ```

## Step 1: Firebase Project Configuration

### 1.1 Create Firebase Project
1. Go to Firebase Console
2. Create new project: "3Pay Global"
3. Disable Google Analytics (optional)
4. Enable Cloud Messaging in project settings

### 1.2 Connect Flutter Project to Firebase
```bash
# Login to Firebase
firebase login

# Configure FlutterFire
flutterfire configure
```

Select your Firebase project and choose both Android and iOS platforms.

## Step 2: Package Installation

Add Firebase packages to your `pubspec.yaml`:

```bash
flutter pub add firebase_core firebase_messaging
```

This will add the following dependencies:
```yaml
dependencies:
  firebase_core: ^latest_version
  firebase_messaging: ^latest_version
```

## Step 3: Android Configuration

### 3.1 Update build.gradle
In `android/build.gradle.kts`, ensure you have:
```kotlin
dependencies {
    classpath("com.google.gms:google-services:4.4.0")
}
```

### 3.2 Update app-level build.gradle
In `android/app/build.gradle.kts`, add:
```kotlin
plugins {
    id("com.google.gms.google-services")
}

dependencies {
    implementation("com.google.firebase:firebase-messaging:23.4.0")
}
```

### 3.3 Android Manifest Permissions
Add to `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.VIBRATE" />
```

## Step 4: iOS Configuration

### 4.1 Update iOS Deployment Target
In `ios/Podfile`, ensure minimum iOS version:
```ruby
platform :ios, '13.0'
```

### 4.2 Enable Push Notifications Capability
1. Open `ios/Runner.xcworkspace` in Xcode
2. Select Runner target
3. Go to "Signing & Capabilities"
4. Add "Push Notifications" capability
5. Add "Background Modes" capability and enable "Background processing"

## Step 5: Implementation

### 5.1 Create Firebase API Service

Create `lib/src/core/services/firebase_api_service.dart`:

```dart
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';

class FirebaseApiService {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final PocketBaseService _pocketBaseService = PocketBaseService();

  /// Initialize push notifications
  static Future<void> initNotifications() async {
    try {
      // Request permission from user
      NotificationSettings settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        LoggerService.info('User granted notification permissions');
        
        // Get FCM token for this device
        final fcmToken = await _firebaseMessaging.getToken();
        LoggerService.info('FCM Token: $fcmToken');
        
        // Store token in PocketBase for the current user
        await _storeFCMToken(fcmToken);
        
        // Initialize background message handling
        await _initPushNotifications();
        
        // Handle notification taps when app is running
        FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
        
        // Handle notification taps when app is in background
        FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);
        
      } else {
        LoggerService.warning('User denied notification permissions');
      }
    } catch (e) {
      LoggerService.error('Error initializing push notifications', e);
    }
  }

  /// Store FCM token in PocketBase for current user
  static Future<void> _storeFCMToken(String? token) async {
    if (token == null || !_pocketBaseService.pb.authStore.isValid) return;
    
    try {
      final userId = _pocketBaseService.pb.authStore.record!.id;
      
      // Update user record with FCM token
      await _pocketBaseService.updateRecord(
        collectionName: 'users',
        recordId: userId,
        data: {'fcm_token': token},
      );
      
      LoggerService.info('FCM token stored for user: $userId');
    } catch (e) {
      LoggerService.error('Error storing FCM token', e);
    }
  }

  /// Initialize background message handling
  static Future<void> _initPushNotifications() async {
    // Handle notification when app was terminated and now opened
    RemoteMessage? initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _handleBackgroundMessage(initialMessage);
    }
  }

  /// Handle messages when app is in foreground
  static void _handleForegroundMessage(RemoteMessage message) {
    LoggerService.info('Foreground message received: ${message.notification?.title}');
    
    // Show local notification for foreground messages
    _showLocalNotification(message);
  }

  /// Handle messages when app is in background or terminated
  static void _handleBackgroundMessage(RemoteMessage message) {
    LoggerService.info('Background message received: ${message.notification?.title}');
    
    // Navigate to specific page based on message data
    _handleNotificationNavigation(message);
  }

  /// Show local notification for foreground messages
  static void _showLocalNotification(RemoteMessage message) {
    // Integration with existing LocalNotificationService
    // This will be implemented when LocalNotificationService is added
    LoggerService.info('Showing local notification: ${message.notification?.title}');
  }

  /// Handle navigation based on notification data
  static void _handleNotificationNavigation(RemoteMessage message) {
    final data = message.data;
    
    // Navigate based on notification type
    if (data.containsKey('type')) {
      switch (data['type']) {
        case 'claim_update':
          // Navigate to specific claim
          if (data.containsKey('claim_id')) {
            LoggerService.info('Navigating to claim: ${data['claim_id']}');
            // Implement navigation logic
          }
          break;
        case 'funding_opportunity':
          // Navigate to funding opportunities
          LoggerService.info('Navigating to funding opportunities');
          break;
        case 'message':
          // Navigate to messages
          LoggerService.info('Navigating to messages');
          break;
        default:
          LoggerService.info('Unknown notification type: ${data['type']}');
      }
    }
  }

  /// Refresh FCM token (call when user logs in)
  static Future<void> refreshToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      await _storeFCMToken(token);
    } catch (e) {
      LoggerService.error('Error refreshing FCM token', e);
    }
  }

  /// Clear FCM token (call when user logs out)
  static Future<void> clearToken() async {
    try {
      if (_pocketBaseService.pb.authStore.isValid) {
        final userId = _pocketBaseService.pb.authStore.record!.id;
        
        await _pocketBaseService.updateRecord(
          collectionName: 'users',
          recordId: userId,
          data: {'fcm_token': null},
        );
      }
      
      await _firebaseMessaging.deleteToken();
      LoggerService.info('FCM token cleared');
    } catch (e) {
      LoggerService.error('Error clearing FCM token', e);
    }
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  LoggerService.info('Background message: ${message.notification?.title}');
}
```

### 5.2 Update Main Function

Update `lib/main.dart`:

```dart
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';
import 'firebase_options.dart'; // Generated by flutterfire configure

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  // Set background message handler
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  
  // Initialize push notifications
  await FirebaseApiService.initNotifications();
  
  runApp(MyApp());
}
```

### 5.3 Integration with Authentication

Update your authentication flow to handle FCM tokens:

```dart
// After successful login
await FirebaseApiService.refreshToken();

// Before logout
await FirebaseApiService.clearToken();
```

## Step 6: Server-Side Integration

### 6.1 Update PocketBase Schema

Add FCM token field to users collection:
```javascript
// In PocketBase admin panel, add field to users collection:
// Field name: fcm_token
// Field type: Text
// Required: No
```

### 6.2 Sending Push Notifications

Create a server-side function to send push notifications:

```javascript
// Example server-side function (Node.js/Deno)
async function sendPushNotification(userIds, title, body, data = {}) {
  const users = await pb.collection('users').getFullList({
    filter: userIds.map(id => `id="${id}"`).join(' || ')
  });
  
  const tokens = users
    .map(user => user.fcm_token)
    .filter(token => token);
  
  if (tokens.length === 0) return;
  
  const message = {
    notification: { title, body },
    data: data,
    tokens: tokens
  };
  
  // Send via Firebase Admin SDK
  const response = await admin.messaging().sendMulticast(message);
  console.log('Push notifications sent:', response.successCount);
}
```

## Step 7: Testing

### 7.1 Test with Firebase Console
1. Go to Firebase Console > Cloud Messaging
2. Create new campaign
3. Use the FCM token from your device
4. Send test notification

### 7.2 Test Scenarios
- App in foreground
- App in background
- App terminated
- Different notification types with custom data

## Integration with Existing Services

This push notification system integrates with:
- **Existing NotificationService**: Complement real-time notifications
- **PocketBase**: Store FCM tokens and notification logs
- **Email Service**: Provide multi-channel communication
- **Local Notifications**: Show immediate feedback for foreground messages

## Best Practices

1. **Token Management**: Refresh tokens on app start and login
2. **Permission Handling**: Gracefully handle denied permissions
3. **Data Payload**: Use custom data for navigation and context
4. **Error Handling**: Implement robust error handling and logging
5. **Testing**: Test on both platforms and different app states
6. **Privacy**: Only store tokens for authenticated users

## Troubleshooting

1. **Android Build Issues**: Ensure Google Services plugin is properly configured
2. **iOS Permissions**: Verify push notification capability is enabled
3. **Token Issues**: Check Firebase project configuration
4. **Background Handling**: Ensure background message handler is top-level function
