# Enhanced Notification System Implementation Guide

## Overview

This document outlines the implementation of an enhanced notification system that solves the global notification read state issue while maintaining backward compatibility with existing user-specific notifications.

## Problem Statement

The current notification system has a design flaw where global notifications (with empty `recipientId` arrays) share a single `isRead` field across all users. When one user marks a global notification as read, it appears as read for all other users.

## Solution: Hybrid Approach with User Read States

### Architecture

1. **Maintain existing notifications collection** for backward compatibility
2. **Add new notification_read_states collection** to track per-user read states for global notifications
3. **Enhanced service layer** that handles both systems seamlessly
4. **Gradual migration path** from old to new system

### Key Components

#### 1. New Collection: `notification_read_states`

**Purpose**: Track individual user read states for global notifications

**Schema**:
- `notification_id` (relation to notifications, required)
- `user_id` (relation to users, required)
- `is_read` (boolean, default: false)
- `read_at` (datetime, nullable)
- Unique index on (`notification_id`, `user_id`)

**Access Rules**:
- Users can only access their own read state records
- Automatic cleanup when notifications or users are deleted (cascade)

#### 2. Enhanced Notification Model

**Features**:
- Backward compatible with existing `NotificationModel`
- Additional fields: `readAt`, `isGlobal`
- Utility methods for filtering, sorting, and grouping
- Type-safe handling of both global and user-specific notifications

#### 3. Enhanced Notification Service

**Capabilities**:
- Dual subscription system (notifications + read states)
- Intelligent read state management
- Real-time updates for both notification changes and read state changes
- Automatic detection of global vs user-specific notifications

#### 4. Migration Service

**Functions**:
- Migrate existing global notifications to new system
- Validate migration integrity
- Cleanup orphaned records
- Ensure collection existence

## Implementation Steps

### Step 1: Create the notification_read_states Collection

1. Import the provided JSON schema into PocketBase:
   ```bash
   # Use the notification_read_states_collection.json file
   ```

2. Verify the collection is created with proper indexes and access rules

### Step 2: Generate Enhanced Model Files

Run the code generation for the new enhanced notification model:

```bash
flutter packages pub run build_runner build
```

### Step 3: Update Service Dependencies

Replace the existing notification service usage with the enhanced version:

```dart
// Old
final notificationService = NotificationService(pocketBaseService);

// New
final enhancedNotificationService = EnhancedNotificationService(pocketBaseService);
```

### Step 4: Run Migration (One-time)

Execute the migration for existing data:

```dart
final migrationService = NotificationMigrationService(pocketBaseService);

// Ensure collection exists
await migrationService.ensureReadStatesCollectionExists();

// Migrate existing global notifications
await migrationService.migrateGlobalNotifications();

// Validate migration
final result = await migrationService.validateMigration();
if (!result.isValid) {
  print('Migration issues: ${result.issues}');
}

// Cleanup orphaned records
await migrationService.cleanupOrphanedReadStates();
```

### Step 5: Update UI Components

Replace existing notification pages with enhanced versions:

1. Update dashboard unread count logic
2. Replace notifications page with enhanced version
3. Update notification detail page to use enhanced service
4. Update real-time subscription handling

## Behavior Changes

### Global Notifications (recipientId: [])

**Before**:
- Single `isRead` field affects all users
- Marking as read makes it read for everyone

**After**:
- Individual read states per user in `notification_read_states` collection
- Each user has independent read/unread status
- Original `isRead` field reset to `false` and ignored for globals

### User-Specific Notifications (recipientId: ["user1", "user2"])

**Before & After**:
- No changes in behavior
- Continue using original `isRead` field
- No read state records created

## Database Schema Changes

### New Collection: notification_read_states

```sql
CREATE TABLE notification_read_states (
  id TEXT PRIMARY KEY,
  notification_id TEXT NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  is_read BOOLEAN DEFAULT FALSE,
  read_at DATETIME,
  created DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(notification_id, user_id)
);
```

### Existing Collection: notifications

**No schema changes required** - maintains full backward compatibility

## API Changes

### Enhanced Filtering Logic

**Old Filter**:
```
(recipientId ~ "$userId" || recipientId:length < 1) && isRead = false
```

**New Filter Logic** (handled in service):
1. Fetch notifications: `(recipientId ~ "$userId" || recipientId:length < 1)`
2. For each notification:
   - If global: Check user's read state in `notification_read_states`
   - If user-specific: Use original `isRead` field

### New Endpoints Usage

**Mark Global Notification as Read**:
```dart
// Creates/updates record in notification_read_states
await enhancedNotificationService.markAsRead(notificationId);
```

**Mark User-Specific Notification as Read**:
```dart
// Updates isRead field in notifications collection
await enhancedNotificationService.markAsRead(notificationId);
```

## Real-time Subscriptions

### Dual Subscription System

1. **Notifications Collection**: Listen for new/updated/deleted notifications
2. **Read States Collection**: Listen for read state changes

### Event Handling

**Notification Events**:
- `create`: Add to user's notification list if applicable
- `update`: Update notification content
- `delete`: Remove from user's notification list

**Read State Events**:
- `create/update`: Update read status for corresponding notification
- `delete`: Reset to unread status

## Performance Considerations

### Optimizations

1. **Indexed Queries**: Unique index on (notification_id, user_id)
2. **Efficient Filtering**: Separate queries for global vs user-specific
3. **Batch Operations**: Group read state updates when possible
4. **Caching**: Service-level caching of notification lists

### Scalability

- Read states only created when users actually read global notifications
- Automatic cleanup of orphaned records
- Efficient real-time updates using targeted subscriptions

## Testing Strategy

### Unit Tests

1. Test enhanced notification model serialization/deserialization
2. Test service methods for both global and user-specific notifications
3. Test migration service functionality
4. Test utility functions for filtering and sorting

### Integration Tests

1. Test real-time subscription handling
2. Test cross-user read state isolation
3. Test backward compatibility with existing notifications
4. Test migration process with sample data

### Manual Testing Scenarios

1. **Global Notification Read States**:
   - User A marks global notification as read
   - Verify User B still sees it as unread
   - Verify User A sees it as read after refresh

2. **User-Specific Notifications**:
   - Verify existing behavior unchanged
   - Test read state updates
   - Test real-time updates

3. **Mixed Scenarios**:
   - User with both global and user-specific notifications
   - Test filtering and sorting
   - Test unread count accuracy

## Migration Checklist

- [ ] Create notification_read_states collection
- [ ] Generate enhanced model files
- [ ] Update service dependencies
- [ ] Run migration script
- [ ] Validate migration results
- [ ] Update UI components
- [ ] Test all notification scenarios
- [ ] Monitor performance and error logs
- [ ] Document any issues and resolutions

## Rollback Plan

If issues arise, the system can be rolled back by:

1. Reverting to original NotificationService
2. Restoring original UI components
3. The notification_read_states collection can remain (no impact)
4. Original notifications collection unchanged

## Future Enhancements

1. **Notification Preferences**: User-specific notification type preferences
2. **Bulk Operations**: Mark all as read, delete all read notifications
3. **Notification Categories**: Enhanced categorization and filtering
4. **Push Notifications**: Integration with mobile push notification services
5. **Notification Templates**: Reusable notification templates for common scenarios
