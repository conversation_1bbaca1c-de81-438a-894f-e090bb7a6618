# Fix for Claimant Profiles Query Error

## Problem
When navigating to the Claim Detail Page after tapping the "View" button, the following error was occurring:

```
ClientException: {url: http://127.0.0.1:8090/api/collections/claimant_profiles/records?page=1&perPage=500&filter=associated_claim_id+%3D+%220mpu368tz3fal89%22&skipTotal=true, isAbort: false, statusCode: 400, response: {data: {}, message: Something went wrong while processing your request., status: 400}, originalError: null}
```

## Root Cause Analysis

The error was caused by an incorrect PocketBase query in the `_fetchClaimDetails` method of the Claim Detail Page:

### Issues Identified:

1. **Wrong Field Name**: The query was using `associated_claim_id` (singular) but the actual field name in the `claimant_profiles` collection is `associated_claim_ids` (plural)

2. **Wrong Operator**: The query was using `=` operator, but since `associated_claim_ids` is an array/relation field, it should use the `~` operator (contains)

### Original Code (Incorrect):
```dart
final claimantRecords = await pbClient
    .collection('claimant_profiles')
    .getFullList(filter: 'associated_claim_id = "${widget.claimId}"');
```

### Fixed Code (Correct):
```dart
final claimantRecords = await pbClient
    .collection('claimant_profiles')
    .getFullList(filter: 'associated_claim_ids ~ "${widget.claimId}"');
```

## Solution Applied

### File Modified:
`lib/src/features/solicitor_portal/presentation/pages/claim_detail_page.dart`

### Changes Made:
- **Line 200**: Changed filter from `'associated_claim_id = "${widget.claimId}"'` to `'associated_claim_ids ~ "${widget.claimId}"'`

### PocketBase Query Syntax:
- **`=` operator**: Used for exact matches on single values
- **`~` operator**: Used for array/relation fields to check if the array contains the specified value

## Verification

### Field Schema Confirmation:
From `claimant_profile_model.dart`:
```dart
class ClaimantProfile {
  final List<String> associatedClaimIds; // Updated to match PocketBase field
  // ...
}
```

The model clearly shows `associatedClaimIds` (plural) as a `List<String>`, confirming it's an array field.

### PocketBase Collection Structure:
- **Collection**: `claimant_profiles`
- **Field**: `associated_claim_ids` (relation, multiple to funding_applications)
- **Type**: Array of claim/funding application IDs

## Testing

After applying the fix:

1. **Navigation Flow**: Solicitor Dashboard → My Claims → View button → Claim Detail Page
2. **Expected Result**: Page loads successfully without the 400 error
3. **Data Loading**: Claimant profiles associated with the claim should load correctly
4. **UI Display**: The "Professionals" tab should show linked claimants properly

## Impact

### ✅ **Fixed**:
- Claim Detail Page navigation now works without errors
- Claimant profiles are properly fetched and displayed
- The "Professionals" tab shows associated claimants

### ✅ **Maintained**:
- All existing functionality remains intact
- No breaking changes to other parts of the application
- Error handling and logging continue to work as expected

## Related Collections

This fix ensures proper querying of the `claimant_profiles` collection, which has relationships with:
- `users` collection (via `user_id` field)
- `funding_applications` collection (via `associated_claim_ids` field)

The fix aligns with the established data model and PocketBase query syntax patterns used throughout the 3Pay Global application.
