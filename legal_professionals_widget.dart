import 'package:flutter/material.dart';
import 'package:pocketbase/pocketbase.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';

class LegalProfessionalsWidget extends StatefulWidget {
  final String claimId;
  final String claimTitle;

  const LegalProfessionalsWidget({
    super.key,
    required this.claimId,
    required this.claimTitle,
  });

  @override
  State<LegalProfessionalsWidget> createState() =>
      _LegalProfessionalsWidgetState();
}

class _LegalProfessionalsWidgetState extends State<LegalProfessionalsWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final PocketBaseService _pbService = PocketBaseService();

  List<RecordModel> barristers = [];
  List<RecordModel> experts = [];
  List<RecordModel> selectedBarristers = [];
  List<RecordModel> selectedExperts = [];

  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final pb = _pbService.client;

      // Load barristers
      final barristersResult = await pb
          .collection('barristers')
          .getList(
            page: 1,
            perPage: 50,
            filter:
                _searchQuery.isNotEmpty
                    ? 'barrister_with_conduct ~ "$_searchQuery" || barrister_chambers ~ "$_searchQuery"'
                    : '',
          );

      // Load experts
      final expertsResult = await pb
          .collection('experts')
          .getList(
            page: 1,
            perPage: 50,
            filter:
                _searchQuery.isNotEmpty
                    ? 'name ~ "$_searchQuery" || firm_name ~ "$_searchQuery"'
                    : '',
          );

      // Load claim details to get currently associated professionals
      final claimResult = await pb
          .collection('funding_applications')
          .getOne(widget.claimId, expand: 'barristers,experts');

      setState(() {
        barristers = barristersResult.items;
        experts = expertsResult.items;

        // Get currently selected professionals
        final barristersExpanded = claimResult.get<List<RecordModel>>(
          "expand.barristers",
        );
        final expertsExpanded = claimResult.get<List<RecordModel>>(
          "expand.experts",
        );

        selectedBarristers = barristersExpanded;
        selectedExperts = expertsExpanded;

        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading data: $e')));
      }
    }
  }

  Future<void> _searchProfessionals(String query) async {
    setState(() {
      _searchQuery = query;
    });
    await _loadData();
  }

  Future<void> _toggleBarristerSelection(RecordModel barrister) async {
    final isSelected = selectedBarristers.any((b) => b.id == barrister.id);

    try {
      final pb = _pbService.client;

      // Get current claim data
      final claim = await pb
          .collection('funding_applications')
          .getOne(widget.claimId);

      // Get current barrister IDs
      List<String> barristerIds = List<String>.from(
        claim.data['barristers'] ?? [],
      );

      if (isSelected) {
        // Remove barrister
        barristerIds.remove(barrister.id);
      } else {
        // Add barrister
        barristerIds.add(barrister.id);
      }

      // Update claim
      await pb
          .collection('funding_applications')
          .update(widget.claimId, body: {'barristers': barristerIds});

      // Refresh data
      await _loadData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating barristers: $e')),
        );
      }
    }
  }

  Future<void> _toggleExpertSelection(RecordModel expert) async {
    final isSelected = selectedExperts.any((e) => e.id == expert.id);

    try {
      final pb = _pbService.client;

      // Get current claim data
      final claim = await pb
          .collection('funding_applications')
          .getOne(widget.claimId);

      // Get current expert IDs
      List<String> expertIds = List<String>.from(claim.data['experts'] ?? []);

      if (isSelected) {
        // Remove expert
        expertIds.remove(expert.id);
      } else {
        // Add expert
        expertIds.add(expert.id);
      }

      // Update claim
      await pb
          .collection('funding_applications')
          .update(widget.claimId, body: {'experts': expertIds});

      // Refresh data
      await _loadData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error updating experts: $e')));
      }
    }
  }

  void _showAddBarristerDialog() {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController();
    final chambersController = TextEditingController();
    final emailController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Add New Barrister'),
            content: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: nameController,
                      decoration: const InputDecoration(labelText: 'Name'),
                      validator: (value) => value!.isEmpty ? 'Required' : null,
                    ),
                    TextFormField(
                      controller: chambersController,
                      decoration: const InputDecoration(labelText: 'Chambers'),
                      validator: (value) => value!.isEmpty ? 'Required' : null,
                    ),
                    TextFormField(
                      controller: emailController,
                      decoration: const InputDecoration(labelText: 'Email'),
                      validator:
                          (value) =>
                              value!.isEmpty || !value.contains('@')
                                  ? 'Enter a valid email'
                                  : null,
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    try {
                      final pb = _pbService.client;

                      // Create new barrister
                      final newBarrister = await pb
                          .collection('barristers')
                          .create(
                            body: {
                              'barrister_with_conduct': nameController.text,
                              'barrister_chambers': chambersController.text,
                              'email': emailController.text,
                              'claims': [widget.claimId],
                            },
                          );

                      // Get current claim data
                      final claim = await pb
                          .collection('funding_applications')
                          .getOne(widget.claimId);

                      // Get current barrister IDs
                      List<String> barristerIds = List<String>.from(
                        claim.data['barristers'] ?? [],
                      );

                      // Add new barrister
                      barristerIds.add(newBarrister.id);

                      // Update claim
                      await pb
                          .collection('funding_applications')
                          .update(
                            widget.claimId,
                            body: {'barristers': barristerIds},
                          );

                      // Refresh data
                      await _loadData();

                      if (context.mounted) {
                        Navigator.pop(context);
                      }
                    } catch (e) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Error adding barrister: $e')),
                        );
                      }
                    }
                  }
                },
                child: const Text('Save'),
              ),
            ],
          ),
    );
  }

  void _showAddExpertDialog() {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController();
    final firmController = TextEditingController();
    final emailController = TextEditingController();
    String selectedType = 'legal';

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('Add New Expert Witness'),
                  content: Form(
                    key: formKey,
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          TextFormField(
                            controller: nameController,
                            decoration: const InputDecoration(
                              labelText: 'Name',
                            ),
                            validator:
                                (value) => value!.isEmpty ? 'Required' : null,
                          ),
                          TextFormField(
                            controller: firmController,
                            decoration: const InputDecoration(
                              labelText: 'Firm/Organization',
                            ),
                            validator:
                                (value) => value!.isEmpty ? 'Required' : null,
                          ),
                          TextFormField(
                            controller: emailController,
                            decoration: const InputDecoration(
                              labelText: 'Email',
                            ),
                            validator:
                                (value) =>
                                    value!.isEmpty || !value.contains('@')
                                        ? 'Enter a valid email'
                                        : null,
                          ),
                          DropdownButtonFormField<String>(
                            value: selectedType,
                            decoration: const InputDecoration(
                              labelText: 'Expert Type',
                            ),
                            items: const [
                              DropdownMenuItem(
                                value: 'legal',
                                child: Text('Legal'),
                              ),
                              DropdownMenuItem(
                                value: 'financial',
                                child: Text('Financial'),
                              ),
                              DropdownMenuItem(
                                value: 'engineer',
                                child: Text('Engineer'),
                              ),
                              DropdownMenuItem(
                                value: 'construction',
                                child: Text('Construction'),
                              ),
                              DropdownMenuItem(
                                value: 'other',
                                child: Text('Other'),
                              ),
                            ],
                            onChanged: (value) {
                              setState(() {
                                selectedType = value!;
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () async {
                        if (formKey.currentState!.validate()) {
                          try {
                            final pb = _pbService.client;

                            // Create new expert
                            final newExpert = await pb
                                .collection('experts')
                                .create(
                                  body: {
                                    'name': nameController.text,
                                    'firm_name': firmController.text,
                                    'email': emailController.text,
                                    'type': selectedType,
                                    'claims': [widget.claimId],
                                  },
                                );

                            // Get current claim data
                            final claim = await pb
                                .collection('funding_applications')
                                .getOne(widget.claimId);

                            // Get current expert IDs
                            List<String> expertIds = List<String>.from(
                              claim.data['experts'] ?? [],
                            );

                            // Add new expert
                            expertIds.add(newExpert.id);

                            // Update claim
                            await pb
                                .collection('funding_applications')
                                .update(
                                  widget.claimId,
                                  body: {'experts': expertIds},
                                );

                            // Refresh data
                            await _loadData();

                            if (context.mounted) {
                              Navigator.pop(context);
                            }
                          } catch (e) {
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Error adding expert: $e'),
                                ),
                              );
                            }
                          }
                        }
                      },
                      child: const Text('Save'),
                    ),
                  ],
                ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Legal Team - ${widget.claimTitle}'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [Tab(text: 'Barristers'), Tab(text: 'Expert Witnesses')],
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Search',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _searchProfessionals('');
                  },
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
              onChanged: (value) {
                _searchProfessionals(value);
              },
            ),
          ),
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : TabBarView(
                      controller: _tabController,
                      children: [
                        // Barristers Tab
                        Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: ElevatedButton.icon(
                                onPressed: _showAddBarristerDialog,
                                icon: const Icon(Icons.add),
                                label: const Text('Add New Barrister'),
                                style: ElevatedButton.styleFrom(
                                  minimumSize: const Size.fromHeight(40),
                                ),
                              ),
                            ),
                            Expanded(
                              child:
                                  barristers.isEmpty
                                      ? const Center(
                                        child: Text('No barristers found'),
                                      )
                                      : ListView.builder(
                                        itemCount: barristers.length,
                                        itemBuilder: (context, index) {
                                          final barrister = barristers[index];
                                          final isSelected = selectedBarristers
                                              .any((b) => b.id == barrister.id);

                                          return ListTile(
                                            title: Text(
                                              barrister
                                                      .data['barrister_with_conduct'] ??
                                                  '',
                                            ),
                                            subtitle: Text(
                                              barrister
                                                      .data['barrister_chambers'] ??
                                                  '',
                                            ),
                                            trailing: Checkbox(
                                              value: isSelected,
                                              onChanged:
                                                  (_) =>
                                                      _toggleBarristerSelection(
                                                        barrister,
                                                      ),
                                            ),
                                            onTap:
                                                () => _toggleBarristerSelection(
                                                  barrister,
                                                ),
                                          );
                                        },
                                      ),
                            ),
                          ],
                        ),

                        // Experts Tab
                        Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: ElevatedButton.icon(
                                onPressed: _showAddExpertDialog,
                                icon: const Icon(Icons.add),
                                label: const Text('Add New Expert Witness'),
                                style: ElevatedButton.styleFrom(
                                  minimumSize: const Size.fromHeight(40),
                                ),
                              ),
                            ),
                            Expanded(
                              child:
                                  experts.isEmpty
                                      ? const Center(
                                        child: Text(
                                          'No expert witnesses found',
                                        ),
                                      )
                                      : ListView.builder(
                                        itemCount: experts.length,
                                        itemBuilder: (context, index) {
                                          final expert = experts[index];
                                          final isSelected = selectedExperts
                                              .any((e) => e.id == expert.id);

                                          return ListTile(
                                            title: Text(
                                              expert.data['name'] ?? '',
                                            ),
                                            subtitle: Text(
                                              '${expert.data['firm_name'] ?? ''} (${expert.data['type'] ?? 'Unknown'})',
                                            ),
                                            trailing: Checkbox(
                                              value: isSelected,
                                              onChanged:
                                                  (_) => _toggleExpertSelection(
                                                    expert,
                                                  ),
                                            ),
                                            onTap:
                                                () => _toggleExpertSelection(
                                                  expert,
                                                ),
                                          );
                                        },
                                      ),
                            ),
                          ],
                        ),
                      ],
                    ),
          ),
        ],
      ),
    );
  }
}
