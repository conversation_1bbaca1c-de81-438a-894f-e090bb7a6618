# Google Drive Public Access Configuration

## Overview

The 3Pay Global Google Drive service has been updated to automatically configure public access for all uploaded files and folders. This eliminates the "Request Access" workflow and allows users to view documents immediately when clicking file links in the application.

## Key Changes Made

### 1. Updated Google Drive Service (`lib/src/core/services/google_drive_service.dart`)

- **Added `_applyPublicPermissions()` method**: Automatically applies "Anyone with the link" permissions to uploaded files and folders
- **Updated `uploadFile()` method**: Now calls `_applyPublicPermissions()` after each file upload
- **Updated `createFolder()` method**: Now applies public permissions to newly created folders
- **Enhanced error handling**: Public permission failures don't break the upload process

### 2. Configured Root Folder Structure

- **Root Folder**: `3PayGlobal` (ID: `1mxmvTB0M8c1rPbyh5CA90vdVBSFav8UX`)
- **Claims Folder**: `Claims` (ID: `11rRXKASUWO-O0HVTSrztOEjv1Ak5CM7q`)
- **Folder Structure**: `3PayGlobal/Claims/{fundingApplicationId}/Documents/`

### 3. Updated PocketBase Configuration

- **Corrected root folder ID** in `google_drive_config` collection
- **Service Account**: `<EMAIL>`
- **Credentials**: Located at `lib/pay-global-document-storage-9179f2284308.json`

## Public Access Configuration

### Automatic Permissions

All files and folders now automatically receive:
- **Type**: `anyone` (Anyone with the link)
- **Role**: `reader` (View and download only)
- **Searchable**: No (files are not discoverable through Google search)

### URL Formats

The service generates these public URL formats:

```dart
// View URL (opens in Google Drive viewer)
https://drive.google.com/file/d/{fileId}/view

// Download URL (direct download)
https://drive.google.com/uc?id={fileId}&export=download

// Folder URL (opens folder in Google Drive)
https://drive.google.com/drive/folders/{folderId}
```

## Testing Scripts

### 1. Configure Public Access
```bash
dart run scripts/configure_public_access.dart
```
- Sets up public permissions on root folders
- Configures existing files for public access

### 2. Test Public Access
```bash
dart run scripts/test_public_access.dart
```
- Verifies public permissions are working
- Tests URL accessibility without authentication

### 3. Test Complete Upload Flow
```bash
dart run scripts/test_complete_upload_flow.dart
```
- Simulates the full document upload process
- Verifies end-to-end public access functionality

## Usage in Application

### File Upload Process

1. **Folder Creation**: Folders are automatically created with public permissions
2. **File Upload**: Files are uploaded with metadata and public permissions
3. **URL Generation**: Public URLs are generated using the utility functions
4. **User Access**: Users can immediately access files without authentication

### Example Implementation

```dart
// Upload a file (automatically gets public permissions)
final result = await googleDriveService.uploadFile(
  fileName: 'Legal_Documents_v1.pdf',
  fileBytes: fileBytes,
  mimeType: 'application/pdf',
  folderId: documentsFolderId,
  metadata: {
    'funding_application_id': claimId,
    'logical_name': 'Legal_Documents',
  },
);

// Generate public URLs
final viewUrl = GoogleDriveUtils.generateViewUrl(result.id);
final downloadUrl = GoogleDriveUtils.generateDownloadUrl(result.id);

// Users can now access these URLs directly
```

## Security Considerations

### What's Public
- **Files**: Viewable and downloadable by anyone with the link
- **Folders**: Browsable by anyone with the link
- **Metadata**: Basic file information is accessible

### What's Protected
- **Not Searchable**: Files don't appear in Google search results
- **Link Required**: Users must have the specific link to access files
- **Read-Only**: Public users cannot edit, delete, or upload files
- **Service Account Control**: Only the service account can manage files

## Troubleshooting

### Common Issues

1. **Files Still Require Authentication**
   - Run `dart run scripts/configure_public_access.dart`
   - Check that the service account has proper permissions

2. **New Files Not Public**
   - Verify the `_applyPublicPermissions()` method is being called
   - Check service logs for permission errors

3. **Folder Access Issues**
   - Ensure parent folders have public permissions
   - Verify folder IDs in configuration

### Verification Steps

1. **Check File Permissions**:
   ```bash
   dart run scripts/test_public_access.dart
   ```

2. **Test URL Access**:
   - Open a file URL in an incognito browser window
   - Should load without login prompts

3. **Verify Service Configuration**:
   - Check PocketBase `google_drive_config` collection
   - Verify service account credentials

## Monitoring

### Logs to Watch

- **Upload Success**: `File uploaded successfully: {fileId}`
- **Permission Applied**: `Applied public permission to file: {fileId}`
- **Permission Warning**: `Failed to apply public permissions to file {fileId}: {error}`

### Performance Impact

- **Minimal Overhead**: Public permission application adds ~100ms per file
- **No User Impact**: Permission application happens in background
- **Graceful Degradation**: Upload succeeds even if permission application fails

## Maintenance

### Regular Tasks

1. **Monitor Quota Usage**: Check Google Drive API quota in Cloud Console
2. **Review Access Logs**: Monitor file access patterns
3. **Update Permissions**: Run configuration script if needed

### Backup Strategy

- **Service Account Backup**: Keep service account credentials secure
- **Configuration Backup**: Document folder IDs and settings
- **Permission Audit**: Periodically verify public access is working

## Support

For issues with the Google Drive public access configuration:

1. Check the troubleshooting section above
2. Run the test scripts to identify specific problems
3. Review service logs for detailed error information
4. Verify Google Cloud Console settings for the service account

The system is now configured to provide seamless, immediate access to uploaded documents without requiring users to authenticate or request access permissions.
