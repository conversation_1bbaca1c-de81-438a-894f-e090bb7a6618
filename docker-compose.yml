version: '3.8'

services:
  # Flutter Web Application
  web-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: 3pay-web-app
    ports:
      - "80:80"
    depends_on:
      - pocketbase
      - webhook-service
    restart: unless-stopped
    networks:
      - 3pay-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PocketBase Backend
  pocketbase:
    image: ghcr.io/muchobien/pocketbase:latest
    container_name: 3pay-pocketbase
    ports:
      - "8090:8090"
    volumes:
      - pocketbase_data:/pb_data
      - ./pocketbase:/pb_app
    environment:
      - POCKETBASE_ADMIN_EMAIL=${POCKETBASE_ADMIN_EMAIL:-<EMAIL>}
      - POCKETBASE_ADMIN_PASSWORD=${POCKETBASE_ADMIN_PASSWORD:-changeme123}
    restart: unless-stopped
    networks:
      - 3pay-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8090/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Webhook Service
  webhook-service:
    build:
      context: ./webhook-service-ts
      dockerfile: Dockerfile
    container_name: 3pay-webhook-service
    ports:
      - "8080:8080"
    environment:
      # Stripe Configuration
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}

      # PocketBase Configuration
      - POCKETBASE_URL=http://pocketbase:8090
      - POCKETBASE_ADMIN_TOKEN=${POCKETBASE_ADMIN_TOKEN}

      # Service Configuration
      - PORT=8080
      - DENO_ENV=production
    depends_on:
      - pocketbase
    restart: unless-stopped
    networks:
      - 3pay-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Email Service
  email-service:
    build:
      context: ./email-service-ts
      dockerfile: Dockerfile
    container_name: 3pay-email-service
    ports:
      - "8081:8081"
    environment:
      # SMTP Configuration
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}

      # PocketBase Configuration
      - POCKETBASE_URL=http://pocketbase:8090
      - POCKETBASE_ADMIN_TOKEN=${POCKETBASE_ADMIN_TOKEN}

      # Application URLs
      - APP_BASE_URL=${APP_BASE_URL:-https://3payglobal.com}
      - DASHBOARD_URL=${DASHBOARD_URL:-https://3payglobal.com/dashboard}
      - CLAIMS_URL=${CLAIMS_URL:-https://3payglobal.com/claims}
      - INVESTMENTS_URL=${INVESTMENTS_URL:-https://3payglobal.com/investments}

      # Service Configuration
      - PORT=8081
      - DENO_ENV=production
      - EMAIL_FROM_NAME=3Pay Global
      - EMAIL_FROM_ADDRESS=<EMAIL>
    depends_on:
      - pocketbase
    restart: unless-stopped
    networks:
      - 3pay-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Reverse Proxy (Optional - for production)
  # nginx-proxy:
  #   image: nginx:1.25-alpine
  #   container_name: 3pay-proxy
  #   ports:
  #     - "443:443"
  #   volumes:
  #     - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
  #     - ./nginx/ssl:/etc/nginx/ssl:ro
  #   depends_on:
  #     - web-app
  #     - webhook-service
  #   restart: unless-stopped
  #   networks:
  #     - 3pay-network

networks:
  3pay-network:
    driver: bridge

volumes:
  pocketbase_data:
    driver: local

# Environment Variables Required:
# Create a .env file in the root directory with:
#
# # Stripe Configuration
# STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
# STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
# STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
#
# # PocketBase Configuration
# POCKETBASE_ADMIN_EMAIL=<EMAIL>
# POCKETBASE_ADMIN_PASSWORD=your_secure_password
# POCKETBASE_ADMIN_TOKEN=your_admin_token
#
# # Email Service Configuration
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_specific_password
# APP_BASE_URL=https://3payglobal.com
# DASHBOARD_URL=https://3payglobal.com/dashboard
# CLAIMS_URL=https://3payglobal.com/claims
# INVESTMENTS_URL=https://3payglobal.com/investments
#
# Usage:
# docker-compose up -d                 # Start all services
# docker-compose logs -f web-app       # View web app logs
# docker-compose down                  # Stop all services
# docker-compose down -v               # Stop and remove volumes
