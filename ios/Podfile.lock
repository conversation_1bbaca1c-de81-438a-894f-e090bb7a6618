PODS:
  - app_badge_plus (1.2.3):
    - Flutter
  - audio_service (0.0.1):
    - Flutter
    - FlutterMacOS
  - audio_session (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Messaging (11.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.10.0)
  - firebase_core (3.13.1):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_messaging (15.2.6):
    - Firebase/Messaging (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_filex (0.0.2):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - Stripe (24.7.0):
    - StripeApplePay (= 24.7.0)
    - StripeCore (= 24.7.0)
    - StripePayments (= 24.7.0)
    - StripePaymentsUI (= 24.7.0)
    - StripeUICore (= 24.7.0)
  - stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 24.7.0)
    - stripe_ios/stripe_ios (= 0.0.1)
    - stripe_ios/stripe_objc (= 0.0.1)
    - StripeApplePay (~> 24.7.0)
    - StripeFinancialConnections (~> 24.7.0)
    - StripePayments (~> 24.7.0)
    - StripePaymentSheet (~> 24.7.0)
    - StripePaymentsUI (~> 24.7.0)
  - stripe_ios/stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 24.7.0)
    - stripe_ios/stripe_objc
    - StripeApplePay (~> 24.7.0)
    - StripeFinancialConnections (~> 24.7.0)
    - StripePayments (~> 24.7.0)
    - StripePaymentSheet (~> 24.7.0)
    - StripePaymentsUI (~> 24.7.0)
  - stripe_ios/stripe_objc (0.0.1):
    - Flutter
    - Stripe (~> 24.7.0)
    - StripeApplePay (~> 24.7.0)
    - StripeFinancialConnections (~> 24.7.0)
    - StripePayments (~> 24.7.0)
    - StripePaymentSheet (~> 24.7.0)
    - StripePaymentsUI (~> 24.7.0)
  - StripeApplePay (24.7.0):
    - StripeCore (= 24.7.0)
  - StripeCore (24.7.0)
  - StripeFinancialConnections (24.7.0):
    - StripeCore (= 24.7.0)
    - StripeUICore (= 24.7.0)
  - StripePayments (24.7.0):
    - StripeCore (= 24.7.0)
    - StripePayments/Stripe3DS2 (= 24.7.0)
  - StripePayments/Stripe3DS2 (24.7.0):
    - StripeCore (= 24.7.0)
  - StripePaymentSheet (24.7.0):
    - StripeApplePay (= 24.7.0)
    - StripeCore (= 24.7.0)
    - StripePayments (= 24.7.0)
    - StripePaymentsUI (= 24.7.0)
  - StripePaymentsUI (24.7.0):
    - StripeCore (= 24.7.0)
    - StripePayments (= 24.7.0)
    - StripeUICore (= 24.7.0)
  - StripeUICore (24.7.0):
    - StripeCore (= 24.7.0)
  - SwiftyGif (5.4.5)
  - syncfusion_flutter_pdfviewer (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_badge_plus (from `.symlinks/plugins/app_badge_plus/ios`)
  - audio_service (from `.symlinks/plugins/audio_service/darwin`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - stripe_ios (from `.symlinks/plugins/stripe_ios/ios`)
  - syncfusion_flutter_pdfviewer (from `.symlinks/plugins/syncfusion_flutter_pdfviewer/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - SDWebImage
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore
    - SwiftyGif

EXTERNAL SOURCES:
  app_badge_plus:
    :path: ".symlinks/plugins/app_badge_plus/ios"
  audio_service:
    :path: ".symlinks/plugins/audio_service/darwin"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  stripe_ios:
    :path: ".symlinks/plugins/stripe_ios/ios"
  syncfusion_flutter_pdfviewer:
    :path: ".symlinks/plugins/syncfusion_flutter_pdfviewer/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  app_badge_plus: 73079d53b6a318c723de4d8aded465fbc4e92897
  audio_service: cab6c1a0eaf01b5a35b567e11fa67d3cc1956910
  audio_session: 19e9480dbdd4e5f6c4543826b2e8b0e4ab6145fe
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: b159e0c068aef54932bb15dc9fd1571818edaf49
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_core: 3c2f323cae65c97a636a05a23b17730ef93df2cf
  firebase_messaging: 456e01ff29a451c90097d0b45925551d5be0c143
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseMessaging: 2b9f56aa4ed286e1f0ce2ee1d413aabb8f9f5cb9
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: ff50f8405aaa0ccdc7dcfb9022ca192e8ad9688f
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  just_audio: a42c63806f16995daf5b219ae1d679deb76e6a79
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_filex: 6e26e659846ec990262224a12ef1c528bb4edbe4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  Stripe: 8a03a78bfa16b197f9fac51e42670ac563b34388
  stripe_ios: 0f0884084e2e8b49df838f8fed2129b42c3516b9
  StripeApplePay: 3c1b43d9b5130f6b714863bf8c9482c24168ab27
  StripeCore: 4955c2af14446db04818ad043d19d8f97b73c5fa
  StripeFinancialConnections: 8cf97b04c2f354879a2a5473126efac38f11f406
  StripePayments: 91820845bece6117809bcfdcaef39c84c2b4cae5
  StripePaymentSheet: 1810187cbdbc73410b8fb86cecafaaa41c1481fc
  StripePaymentsUI: 326376e23caa369d1f58041bdb858c89c2b17ed4
  StripeUICore: 17a4f3adb81ae05ab885e1b353022a430176eab1
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  syncfusion_flutter_pdfviewer: cfcf23c03816192575902e615fa50adc9f95b724
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: ff0c54dc4eb1c2daf3c303d120c7aca218fdcd17

COCOAPODS: 1.16.2
