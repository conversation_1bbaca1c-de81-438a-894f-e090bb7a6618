<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>3Pay Global</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>3Pay Global</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>https</string>
		<string>http</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>

	<!-- Photo Library and File Access Permissions -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs access to your photo library to upload profile pictures and documents.</string>
	<key>NSCameraUsageDescription</key>
	<string>This app needs access to your camera to take profile pictures and document photos.</string>
	<key>NSDocumentsFolderUsageDescription</key>
	<string>This app needs access to your documents to upload files and documents.</string>

	<!-- Notification Permissions -->
	<key>NSUserNotificationUsageDescription</key>
	<string>3Pay Global needs notifications to keep you updated on claim status, funding opportunities, and important messages from our agents.</string>

	<!-- File Provider Support -->
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>UISupportsDocumentBrowser</key>
	<true/>

	<!-- Background Audio and Notification Configuration -->
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>background-processing</string>
		<string>remote-notification</string>
	</array>

	<!-- Audio Session Category -->
	<key>AVAudioSessionCategory</key>
	<string>AVAudioSessionCategoryPlayback</string>

	<!-- Audio Session Options -->
	<key>AVAudioSessionCategoryOptions</key>
	<array>
		<string>AVAudioSessionCategoryOptionMixWithOthers</string>
		<string>AVAudioSessionCategoryOptionDuckOthers</string>
	</array>
</dict>
</plist>
