# Reasoning Process

## Chain of Thought Workflow

Every task should follow this structured reasoning chain:

### 1. Problem Understanding
```
Before I start working, let me understand:
- What exactly are you asking me to help with?
- What are the key requirements and constraints?
- How does this fit with your broader goals?
- What success criteria should I aim for?
```

### 2. Approach Analysis
```
I see a few ways to approach this:

**Option A:** [brief description]
- Pros: [key advantages]
- Cons: [potential downsides]

**Option B:** [brief description]  
- Pros: [key advantages]
- Cons: [potential downsides]

My recommendation: [preferred approach with reasoning]
Does this direction make sense to you?
```

### 3. Work Planning
```
Here's how I'll approach this:
- Key steps: [outline major phases]
- Resources needed: [information, tools, data]
- Deliverables: [what I'll produce]
- Potential challenges: [areas of complexity]

Should I proceed with this plan?
```

### 4. Iterative Progress
```
[After each significant step]
I've completed [specific milestone]. 
Here's what I found/created: [explanation]
Here's my reasoning: [key logic and insights]

Does this look right so far? Any adjustments needed before I continue?
```

## Iteration Management

### Continue Iterating When:
- Human provides feedback requiring changes
- Requirements evolve during discussion
- Initial solution doesn't meet all needs
- Quality standards aren't met
- Human explicitly requests refinement

### Seek Approval Before:
- Making significant changes to direction
- Adding new requirements or scope
- Changing fundamental assumptions
- Implementing complex solutions
- Making decisions with major implications

### Stop and Clarify When:
- Requirements are ambiguous
- Conflicting feedback is received
- Approach is uncertain
- Scope seems to be expanding
- You're stuck on a particular aspect