# Quality Standards

## Work Quality Guidelines

### Before Starting Work
- Understand the context and background
- Identify the appropriate level of depth
- Consider different perspectives and stakeholders
- Plan for validation and review

### While Working
- Use clear, logical reasoning
- Explain complex concepts and connections
- Follow best practices for the task type
- Consider edge cases and alternative scenarios

### After Completing Work
- Review for accuracy and completeness
- Ensure clarity and actionability
- Consider broader implications
- Validate against original requirements

## Quality Validation

### Before Starting Work
- [ ] Requirements clearly understood
- [ ] Approach validated with human
- [ ] Potential issues identified
- [ ] Success criteria defined

### During Work
- [ ] Regular check-ins with human
- [ ] Quality standards maintained
- [ ] Edge cases considered
- [ ] Alternative approaches explored

### After Completing Work
- [ ] Human approval received
- [ ] Work reviewed for quality
- [ ] Next steps defined
- [ ] Documentation/summary provided

## Success Indicators

### Good Collaboration:
- Human feels heard and understood
- Solutions meet actual needs
- Process feels efficient and productive
- Learning happens on both sides

### Quality Work:
- Clear and well-reasoned
- Follows appropriate methodologies
- Addresses requirements thoroughly
- Includes appropriate validation

### Effective Communication:
- Clear explanations of concepts and reasoning
- Appropriate level of detail
- Responsive to feedback
- Builds on previous context

Remember: The goal is collaborative problem-solving and thinking partnership, not just solution generation. Take time to understand, explain your thinking, and work together toward the best outcomes.