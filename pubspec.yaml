name: three_pay_group_litigation_platform
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+25

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  go_router: ^14.2.0 # Added for routing
  shadcn_ui: ^0.27.1
  google_fonts: ^6.2.1
  intl: ^0.20.2 # Added for date formatting
  pocketbase: ^0.22.0 # Updated for PocketBase integration to resolve http conflict
  flutter_html: ^3.0.0-beta.2 # Added for rendering HTML content (using beta for latest features)
  just_audio: ^0.9.40 # Added for enhanced audio playback with background support
  audio_service: ^0.18.15 # Added for background audio service and media controls
  just_audio_background: ^0.0.1-beta.13 # Added for background audio integration
  shared_preferences: ^2.2.3 # Added for local caching of auth state
  signature: ^6.0.0
  flutter_bloc: ^9.1.1 # Added for state management
  file_picker: ^10.1.9 # Added for file selection
  country_picker: ^2.0.26 # Added for country selection
  url_launcher: ^6.3.1 # Added for launching URLs
  flutter_stripe: ^11.5.0
  http: ^1.4.0 # Added for HTTP requests and file uploads
  flutter_riverpod: ^2.5.1 # Added for Riverpod state management
  qr_flutter: ^4.1.0
  lucide_icons: ^0.257.0 # Added for icons
  json_annotation: ^4.9.0
  freezed_annotation: ^2.4.4
  fl_chart: ^0.68.0 # Added for charts
  csv: ^6.0.0 # Added for CSV generation
  riverpod_annotation: ^2.3.5 # Added for Riverpod code generation
  path_provider: ^2.1.5
  open_filex: ^4.7.0
  dio: ^5.8.0+1
  logger: ^2.4.0 # Added for proper logging
  uuid: ^4.5.1
  # firebase_core: ^3.13.1 # Added for Firebase core functionality - DISABLED due to Xcode 16/iOS 18.5 compatibility issues
  firebase_messaging: ^15.2.6 # Added for push notifications - DISABLED due to Xcode 16/iOS 18.5 compatibility issues
  flutter_local_notifications: ^19.2.1 # Added for local notifications
  timezone: ^0.10.1 # Added for scheduled notifications
  app_badge_plus: ^1.2.3 # Added for app icon badge functionality (replacement for discontinued flutter_app_badger)
  googleapis: ^11.4.0 # Added for Google APIs client library
  googleapis_auth: ^1.4.1 # Added for Google APIs authentication
  crypto: ^3.0.3 # Added for checksums and encryption
  mime: ^1.0.4 # Added for MIME type detection
  web: ^1.1.0 # Added for web platform APIs (replacement for dart:html)
  # For PDF viewing
  syncfusion_flutter_pdfviewer: ^29.2.10
  # For image viewing
  photo_view: ^0.14.0
  # For WebView fallback
  webview_flutter: ^4.4.2
  # For docx files - handled via browser fallback in DocumentPreviewWidget

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.9
  freezed: ^2.5.2
  json_serializable: ^6.8.0
  riverpod_generator: ^2.4.0 # Added for Riverpod code generation
  flutter_launcher_icons: ^0.14.3 # Added for app icon generation

  # Testing dependencies
  mockito: ^5.4.4 # For mocking dependencies
  test: ^1.25.2 # For unit testing
  fake_async: ^1.3.1 # For testing async operations
  clock: ^1.1.1 # For testing time-dependent code
  http_mock_adapter: ^0.6.1 # For mocking HTTP requests
  golden_toolkit: ^0.15.0 # For golden file testing

  # Integration testing dependencies (commented out to prevent inclusion in release builds)
  # Uncomment these when running integration tests:
  # integration_test:
  #   sdk: flutter # For integration testing
  # patrol: ^3.6.1 # For advanced UI testing

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/ # Add this line to include all images in the directory
    - assets/credentials/ # Add credentials directory
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  remove_alpha_ios: true # Remove alpha channel for iOS App Store compliance
  web:
    generate: true
    image_path: "assets/images/logo.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/images/logo.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/logo.png"
