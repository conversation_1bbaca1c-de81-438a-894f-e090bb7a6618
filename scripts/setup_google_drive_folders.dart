import 'dart:io' as io;
import 'dart:convert';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';

/// Script to set up the proper Google Drive folder structure for 3Pay Global
Future<void> main() async {
  try {
    print('🔧 Setting up Google Drive folder structure...');
    
    // Load service account credentials
    final credentialsFile = io.File('lib/pay-global-document-storage-9179f2284308.json');
    if (!await credentialsFile.exists()) {
      print('❌ Service account credentials file not found');
      return;
    }
    
    final credentialsContent = await credentialsFile.readAsString();
    final credentialsMap = jsonDecode(credentialsContent) as Map<String, dynamic>;
    final credentials = ServiceAccountCredentials.fromJson(credentialsMap);
    
    print('✅ Loaded service account credentials: ${credentials.email}');
    
    // Create authenticated client
    const scopes = [drive.DriveApi.driveScope];
    final client = await clientViaServiceAccount(credentials, scopes);
    final driveApi = drive.DriveApi(client);
    
    // Find the 3PayGlobal folder
    final rootFileList = await driveApi.files.list(
      pageSize: 50,
      $fields: 'files(id,name,mimeType)',
    );
    
    String? payGlobalFolderId;
    if (rootFileList.files != null) {
      for (final file in rootFileList.files!) {
        if (file.name == '3PayGlobal' && file.mimeType == 'application/vnd.google-apps.folder') {
          payGlobalFolderId = file.id;
          break;
        }
      }
    }
    
    if (payGlobalFolderId == null) {
      print('❌ 3PayGlobal folder not found');
      return;
    }
    
    print('✅ Found 3PayGlobal folder: $payGlobalFolderId');
    
    // Check if Claims folder exists
    final payGlobalContents = await driveApi.files.list(
      q: "'$payGlobalFolderId' in parents",
      $fields: 'files(id,name,mimeType)',
    );
    
    String? claimsFolderId;
    if (payGlobalContents.files != null) {
      for (final file in payGlobalContents.files!) {
        if (file.name == 'Claims' && file.mimeType == 'application/vnd.google-apps.folder') {
          claimsFolderId = file.id;
          break;
        }
      }
    }
    
    if (claimsFolderId == null) {
      print('📁 Creating Claims folder...');
      final claimsFolder = drive.File()
        ..name = 'Claims'
        ..mimeType = 'application/vnd.google-apps.folder'
        ..parents = [payGlobalFolderId];
      
      final createdClaimsFolder = await driveApi.files.create(claimsFolder);
      claimsFolderId = createdClaimsFolder.id!;
      print('✅ Created Claims folder: $claimsFolderId');
    } else {
      print('✅ Found existing Claims folder: $claimsFolderId');
    }
    
    // Check existing structure in Claims folder
    final claimsContents = await driveApi.files.list(
      q: "'$claimsFolderId' in parents",
      $fields: 'files(id,name,mimeType)',
    );
    
    print('\n📋 Current Claims folder contents:');
    if (claimsContents.files != null && claimsContents.files!.isNotEmpty) {
      for (final file in claimsContents.files!) {
        print('   - ${file.name} (${file.mimeType})');
      }
    } else {
      print('   (empty)');
    }
    
    // Create some standard subfolders for organization
    final standardFolders = ['Archived', 'Shared', 'Templates'];
    
    for (final folderName in standardFolders) {
      // Check if folder already exists
      bool folderExists = false;
      if (claimsContents.files != null) {
        for (final file in claimsContents.files!) {
          if (file.name == folderName && file.mimeType == 'application/vnd.google-apps.folder') {
            folderExists = true;
            print('✅ Found existing $folderName folder: ${file.id}');
            break;
          }
        }
      }
      
      if (!folderExists) {
        print('📁 Creating $folderName folder...');
        final folder = drive.File()
          ..name = folderName
          ..mimeType = 'application/vnd.google-apps.folder'
          ..parents = [claimsFolderId];
        
        final createdFolder = await driveApi.files.create(folder);
        print('✅ Created $folderName folder: ${createdFolder.id}');
      }
    }
    
    // Close the client
    client.close();
    
    print('\n🎉 Google Drive folder structure setup complete!');
    print('📁 Structure:');
    print('   3PayGlobal/');
    print('   └── Claims/');
    print('       ├── Archived/');
    print('       ├── Shared/');
    print('       ├── Templates/');
    print('       └── [Individual claim folders will be created here]');
    print('');
    print('✅ The Google Drive service should now work correctly with uploaded files.');
    
  } catch (e, stackTrace) {
    print('❌ Setup failed: $e');
    print('Stack trace: $stackTrace');
    io.exit(1);
  }
}
