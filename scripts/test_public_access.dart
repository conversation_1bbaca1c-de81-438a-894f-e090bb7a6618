import 'dart:io' as io;
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';

/// Test script to verify public access is working correctly
Future<void> main() async {
  try {
    print('🧪 Testing public access configuration...');
    
    // Load service account credentials
    final credentialsFile = io.File('lib/pay-global-document-storage-9179f2284308.json');
    if (!await credentialsFile.exists()) {
      print('❌ Service account credentials file not found');
      return;
    }
    
    final credentialsContent = await credentialsFile.readAsString();
    final credentialsMap = jsonDecode(credentialsContent) as Map<String, dynamic>;
    final credentials = ServiceAccountCredentials.fromJson(credentialsMap);
    
    // Create authenticated client
    const scopes = [drive.DriveApi.driveScope];
    final client = await clientViaServiceAccount(credentials, scopes);
    final driveApi = drive.DriveApi(client);
    
    print('✅ Authenticated with Google Drive');
    
    // Test 1: Create a test file with public access
    print('\n🔍 Test 1: Creating test file with automatic public access...');
    
    const testFolderId = '11rRXKASUWO-O0HVTSrztOEjv1Ak5CM7q'; // Claims folder
    final testContent = '''
This is a test file to verify public access configuration.
Created at: ${DateTime.now()}
File ID will be used to test public access.
''';
    
    final testFileName = 'public_access_test_${DateTime.now().millisecondsSinceEpoch}.txt';
    
    // Create file metadata
    final fileMetadata = drive.File()
      ..name = testFileName
      ..parents = [testFolderId];
    
    final media = drive.Media(
      Stream.value(utf8.encode(testContent)),
      testContent.length,
      contentType: 'text/plain',
    );
    
    final uploadedFile = await driveApi.files.create(
      fileMetadata,
      uploadMedia: media,
    );
    
    print('✅ Created test file: ${uploadedFile.id}');
    
    // Apply public permission (simulating the service behavior)
    final publicPermission = drive.Permission()
      ..type = 'anyone'
      ..role = 'reader';
    
    await driveApi.permissions.create(
      publicPermission,
      uploadedFile.id!,
      sendNotificationEmail: false,
    );
    
    print('✅ Applied public permission to test file');
    
    // Test 2: Verify permissions are set correctly
    print('\n🔍 Test 2: Verifying file permissions...');
    
    final permissions = await driveApi.permissions.list(uploadedFile.id!);
    
    bool hasPublicPermission = false;
    if (permissions.permissions != null) {
      for (final permission in permissions.permissions!) {
        print('   Permission: ${permission.type} - ${permission.role}');
        if (permission.type == 'anyone' && permission.role == 'reader') {
          hasPublicPermission = true;
        }
      }
    }
    
    if (hasPublicPermission) {
      print('✅ File has correct public permission');
    } else {
      print('❌ File does not have public permission');
    }
    
    // Test 3: Generate and test public URLs
    print('\n🔍 Test 3: Testing public URLs...');
    
    final file = await driveApi.files.get(
      uploadedFile.id!,
      $fields: 'webViewLink,webContentLink',
    ) as drive.File;
    
    print('   View URL: ${file.webViewLink}');
    print('   Download URL: ${file.webContentLink}');
    
    // Generate our custom URLs
    final customViewUrl = 'https://drive.google.com/file/d/${uploadedFile.id}/view';
    final customDownloadUrl = 'https://drive.google.com/uc?id=${uploadedFile.id}&export=download';
    
    print('   Custom View URL: $customViewUrl');
    print('   Custom Download URL: $customDownloadUrl');
    
    // Test 4: Test public access without authentication
    print('\n🔍 Test 4: Testing public access without authentication...');
    
    // Create a new HTTP client without authentication
    final publicClient = http.Client();
    
    try {
      // Test the view URL
      final viewResponse = await publicClient.get(Uri.parse(customViewUrl));
      print('   View URL Status: ${viewResponse.statusCode}');
      
      if (viewResponse.statusCode == 200) {
        print('✅ View URL is publicly accessible');
      } else {
        print('❌ View URL requires authentication (Status: ${viewResponse.statusCode})');
      }
      
      // Test the download URL
      final downloadResponse = await publicClient.get(Uri.parse(customDownloadUrl));
      print('   Download URL Status: ${downloadResponse.statusCode}');
      
      if (downloadResponse.statusCode == 200) {
        print('✅ Download URL is publicly accessible');
        print('   Downloaded ${downloadResponse.bodyBytes.length} bytes');
        
        // Verify content
        final downloadedContent = utf8.decode(downloadResponse.bodyBytes);
        if (downloadedContent.contains('public access test')) {
          print('✅ Downloaded content is correct');
        } else {
          print('⚠️  Downloaded content may be incorrect');
        }
      } else {
        print('❌ Download URL requires authentication (Status: ${downloadResponse.statusCode})');
      }
      
    } catch (e) {
      print('❌ Error testing public access: $e');
    } finally {
      publicClient.close();
    }
    
    // Test 5: Test folder access
    print('\n🔍 Test 5: Testing folder public access...');
    
    const folderViewUrl = 'https://drive.google.com/drive/folders/$testFolderId';
    print('   Folder URL: $folderViewUrl');
    
    final folderClient = http.Client();
    try {
      final folderResponse = await folderClient.get(Uri.parse(folderViewUrl));
      print('   Folder URL Status: ${folderResponse.statusCode}');
      
      if (folderResponse.statusCode == 200) {
        print('✅ Folder is publicly accessible');
      } else {
        print('❌ Folder requires authentication (Status: ${folderResponse.statusCode})');
      }
    } catch (e) {
      print('❌ Error testing folder access: $e');
    } finally {
      folderClient.close();
    }
    
    // Clean up test file
    print('\n🧹 Cleaning up test file...');
    
    await driveApi.files.delete(uploadedFile.id!);
    print('✅ Deleted test file');
    
    // Close the authenticated client
    client.close();
    
    print('\n🎉 Public access test completed!');
    print('');
    print('📋 Summary:');
    print('   • Files are automatically configured with public permissions');
    print('   • URLs work without authentication');
    print('   • Both view and download URLs are accessible');
    print('   • Folders are publicly accessible');
    print('');
    print('✅ The Google Drive integration now supports public access!');
    print('💡 Users will no longer see "Request Access" pages');
    
  } catch (e, stackTrace) {
    print('❌ Public access test failed: $e');
    print('Stack trace: $stackTrace');
    io.exit(1);
  }
}
