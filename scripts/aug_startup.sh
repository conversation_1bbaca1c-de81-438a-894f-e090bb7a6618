#!/bin/bash

# 3Pay Global Flutter Development Environment Setup
set -e

echo "🚀 Setting up Flutter development environment for 3Pay Global..."

# Update system packages
sudo apt-get update -y

# Install required system dependencies
sudo apt-get install -y \
    curl \
    git \
    unzip \
    xz-utils \
    zip \
    libglu1-mesa \
    clang \
    cmake \
    ninja-build \
    pkg-config \
    libgtk-3-dev \
    liblzma-dev \
    libstdc++-12-dev \
    wget

# Remove any existing Flutter installation
sudo rm -rf /opt/flutter
rm -rf $HOME/flutter

# Install Flutter in user directory
echo "📱 Installing Flutter SDK in user directory..."
cd $HOME
git clone https://github.com/flutter/flutter.git -b stable --depth 1
chmod -R 755 $HOME/flutter

# Add Flutter to PATH
echo 'export PATH="$HOME/flutter/bin:$PATH"' >> $HOME/.profile
export PATH="$HOME/flutter/bin:$PATH"

# Verify Flutter installation
echo "🔍 Verifying Flutter installation..."
flutter --version

# Navigate to project directory
cd /mnt/persist/workspace

# Configure Flutter for web and Linux development
flutter config --enable-web
flutter config --enable-linux-desktop
flutter config --no-analytics

# Clean any existing build artifacts
flutter clean

# Fix dependency conflicts by updating package versions in pubspec.yaml
echo "🔧 Fixing dependency conflicts..."
sed -i 's/intl: \^0\.19\.0/intl: ^0.20.2/' pubspec.yaml
sed -i 's/shadcn_ui: \^0\.25\.1/shadcn_ui: ^0.27.1/' pubspec.yaml

# Get Flutter dependencies
echo "📦 Getting Flutter dependencies..."
flutter pub get

# Run Flutter doctor to check setup
echo "🩺 Running Flutter doctor..."
flutter doctor -v

# Generate any required code (build_runner, etc.)
echo "🔧 Running code generation..."
flutter packages pub run build_runner build --delete-conflicting-outputs || echo "No build_runner tasks found"

# Verify project structure
echo "📁 Verifying project structure..."
ls -la
ls -la lib/
ls -la test/

echo "✅ Flutter development environment setup complete!"
echo "🧪 Environment ready for testing..."