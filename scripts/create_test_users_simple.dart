#!/usr/bin/env dart

import 'dart:math';

/// Simple script to generate test user data and random dates
/// This script outputs the data that can be used with MCP tools

void main() {
  print('🚀 Test Users and Random Dates Generator');
  print('==========================================');
  print('');

  // Generate test users
  final testUsers = generateTestUsers();

  print('👥 Test Users to Create:');
  print('========================');
  for (int i = 0; i < testUsers.length; i++) {
    final user = testUsers[i];
    print('${i + 1}. ${user['email']} (${user['user_type']})');
    print('   Name: ${user['name']}');
    print('   Status: ${user['status']}');
    print('   Verified: ${user['verified']}');
    if (user['law_firm_name'] != null) {
      print('   Law Firm: ${user['law_firm_name']}');
    }
    if (user['level'] != null) {
      print('   Level: ${user['level']}');
    }
    print('');
  }

  print('📅 Random Date Examples:');
  print('========================');
  final random = Random();
  final now = DateTime.now();

  for (int i = 0; i < 10; i++) {
    final dates = generateRandomDates(random, now);
    print('${i + 1}. Created: ${dates['created']!.toIso8601String()}');
    print('   Updated: ${dates['updated']!.toIso8601String()}');
    print('');
  }

  print('📋 Instructions:');
  print('================');
  print('1. Use the MCP create_user_node tool to create each user above');
  print(
    '2. Use the MCP update_record_node tool to update created/updated fields',
  );
  print('3. Use the random dates provided above for realistic test data');
  print('');
  print('✅ Data generation completed!');
}

/// Generate test users with different user types
List<Map<String, dynamic>> generateTestUsers() {
  return [
    {
      'email': '<EMAIL>',
      'password': 'admin123456',
      'passwordConfirm': 'admin123456',
      'user_type': 'admin',
      'name': 'System Administrator',
      'first_name': 'System',
      'last_name': 'Administrator',
      'status': 'approved',
      'verified': true,
    },
    {
      'email': '<EMAIL>',
      'password': 'solicitor123',
      'passwordConfirm': 'solicitor123',
      'user_type': 'solicitor',
      'name': 'John Smith',
      'first_name': 'John',
      'last_name': 'Smith',
      'law_firm_name': 'Smith & Associates',
      'sra_number': 'SRA123456',
      'position': 'Senior Partner',
      'status': 'approved',
      'verified': true,
    },
    {
      'email': '<EMAIL>',
      'password': 'solicitor123',
      'passwordConfirm': 'solicitor123',
      'user_type': 'solicitor',
      'name': 'Sarah Johnson',
      'first_name': 'Sarah',
      'last_name': 'Johnson',
      'law_firm_name': 'Legal Corp Ltd',
      'sra_number': 'SRA789012',
      'position': 'Partner',
      'status': 'approved',
      'verified': true,
    },
    {
      'email': '<EMAIL>',
      'password': 'cofunder123',
      'passwordConfirm': 'cofunder123',
      'user_type': 'co_funder',
      'name': 'Michael Brown',
      'first_name': 'Michael',
      'last_name': 'Brown',
      'level': '4',
      'status': 'approved',
      'verified': true,
    },
    {
      'email': '<EMAIL>',
      'password': 'cofunder123',
      'passwordConfirm': 'cofunder123',
      'user_type': 'co_funder',
      'name': 'Emma Wilson',
      'first_name': 'Emma',
      'last_name': 'Wilson',
      'level': '3',
      'status': 'approved',
      'verified': true,
    },
    {
      'email': '<EMAIL>',
      'password': 'claimant123',
      'passwordConfirm': 'claimant123',
      'user_type': 'claimant',
      'name': 'David Davis',
      'first_name': 'David',
      'last_name': 'Davis',
      'status': 'approved',
      'verified': true,
    },
    {
      'email': '<EMAIL>',
      'password': 'claimant123',
      'passwordConfirm': 'claimant123',
      'user_type': 'claimant',
      'name': 'Lisa Anderson',
      'first_name': 'Lisa',
      'last_name': 'Anderson',
      'status': 'pending',
      'verified': false,
    },
  ];
}

/// Generate random created and updated dates
/// Created date will be between 6 months ago and 1 month ago
/// Updated date will be between created date and now
Map<String, DateTime> generateRandomDates(Random random, DateTime now) {
  // Created date: between 6 months ago and 1 month ago
  final sixMonthsAgo = now.subtract(const Duration(days: 180));
  final oneMonthAgo = now.subtract(const Duration(days: 30));

  // Use days instead of milliseconds to avoid overflow
  final createdRangeDays = oneMonthAgo.difference(sixMonthsAgo).inDays;
  final createdDaysOffset = random.nextInt(createdRangeDays);
  final createdDate = sixMonthsAgo.add(Duration(days: createdDaysOffset));

  // Updated date: between created date and now
  final updatedRangeDays = now.difference(createdDate).inDays;
  final updatedDaysOffset =
      updatedRangeDays > 0 ? random.nextInt(updatedRangeDays + 1) : 0;
  final updatedDate = createdDate.add(Duration(days: updatedDaysOffset));

  return {'created': createdDate, 'updated': updatedDate};
}
