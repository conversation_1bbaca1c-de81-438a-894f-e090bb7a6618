import 'dart:io' as io;
import 'dart:convert';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';

/// Script to configure public access for the 3PayGlobal folder and all files
/// This eliminates the need for access requests when users click file links
Future<void> main() async {
  try {
    print('🌐 Configuring public access for 3PayGlobal folder...');
    
    // Load service account credentials
    final credentialsFile = io.File('lib/pay-global-document-storage-9179f2284308.json');
    if (!await credentialsFile.exists()) {
      print('❌ Service account credentials file not found');
      return;
    }
    
    final credentialsContent = await credentialsFile.readAsString();
    final credentialsMap = jsonDecode(credentialsContent) as Map<String, dynamic>;
    final credentials = ServiceAccountCredentials.fromJson(credentialsMap);
    
    // Create authenticated client
    const scopes = [drive.DriveApi.driveScope];
    final client = await clientViaServiceAccount(credentials, scopes);
    final driveApi = drive.DriveApi(client);
    
    print('✅ Authenticated with Google Drive');
    
    // Configure public access for the 3PayGlobal folder
    const payGlobalFolderId = '1mxmvTB0M8c1rPbyh5CA90vdVBSFav8UX';
    
    await configurePublicAccess(driveApi, payGlobalFolderId, '3PayGlobal');
    
    // Configure public access for the Claims folder
    const claimsFolderId = '11rRXKASUWO-O0HVTSrztOEjv1Ak5CM7q';
    
    await configurePublicAccess(driveApi, claimsFolderId, 'Claims');
    
    // Configure public access for all existing files in the folder structure
    await configureExistingFilesPublicAccess(driveApi, payGlobalFolderId);
    
    // Close the client
    client.close();
    
    print('\n🎉 Public access configuration complete!');
    print('');
    print('✅ Benefits:');
    print('   • Users can now access files directly without login');
    print('   • No more "Request Access" pages');
    print('   • File links work immediately for anyone');
    print('   • All future uploads will be publicly accessible');
    print('');
    print('🔗 You can test access with any file URL from the application');
    print('💡 Files are accessible to "Anyone with the link" but not searchable');
    
  } catch (e, stackTrace) {
    print('❌ Failed to configure public access: $e');
    print('Stack trace: $stackTrace');
    io.exit(1);
  }
}

/// Configure public access for a specific folder
Future<void> configurePublicAccess(
  drive.DriveApi driveApi,
  String folderId,
  String folderName,
) async {
  try {
    print('📁 Configuring public access for $folderName folder...');
    
    // Check existing permissions
    final existingPermissions = await driveApi.permissions.list(folderId);
    
    // Check if public permission already exists
    bool hasPublicPermission = false;
    if (existingPermissions.permissions != null) {
      for (final permission in existingPermissions.permissions!) {
        if (permission.type == 'anyone' && permission.role == 'reader') {
          hasPublicPermission = true;
          print('   ✅ Public permission already exists');
          break;
        }
      }
    }
    
    if (!hasPublicPermission) {
      // Create public permission: "Anyone with the link" can view
      final publicPermission = drive.Permission()
        ..type = 'anyone'
        ..role = 'reader'; // Read-only access for security
      
      await driveApi.permissions.create(
        publicPermission,
        folderId,
        sendNotificationEmail: false,
      );
      
      print('   ✅ Added public "Anyone with the link" permission');
    }
    
    // Get the folder's shareable link
    final folder = await driveApi.files.get(
      folderId,
      $fields: 'webViewLink,webContentLink',
    ) as drive.File;
    
    print('   🔗 Public link: ${folder.webViewLink}');
    
  } catch (e) {
    print('   ❌ Failed to configure public access for $folderName: $e');
    rethrow;
  }
}

/// Configure public access for all existing files in the folder structure
Future<void> configureExistingFilesPublicAccess(
  drive.DriveApi driveApi,
  String rootFolderId,
) async {
  try {
    print('\n📄 Configuring public access for existing files...');
    
    // Get all files recursively
    final allFiles = await getAllFilesRecursively(driveApi, rootFolderId);
    
    print('   Found ${allFiles.length} files to configure');
    
    int configuredCount = 0;
    int skippedCount = 0;
    
    for (final file in allFiles) {
      try {
        // Skip folders, only configure files
        if (file.mimeType == 'application/vnd.google-apps.folder') {
          continue;
        }
        
        // Check if file already has public permission
        final existingPermissions = await driveApi.permissions.list(file.id!);
        
        bool hasPublicPermission = false;
        if (existingPermissions.permissions != null) {
          for (final permission in existingPermissions.permissions!) {
            if (permission.type == 'anyone' && permission.role == 'reader') {
              hasPublicPermission = true;
              break;
            }
          }
        }
        
        if (!hasPublicPermission) {
          // Add public permission to the file
          final publicPermission = drive.Permission()
            ..type = 'anyone'
            ..role = 'reader';
          
          await driveApi.permissions.create(
            publicPermission,
            file.id!,
            sendNotificationEmail: false,
          );
          
          configuredCount++;
          print('   ✅ Configured: ${file.name}');
        } else {
          skippedCount++;
        }
        
      } catch (e) {
        print('   ⚠️  Failed to configure ${file.name}: $e');
      }
    }
    
    print('   📊 Summary: $configuredCount configured, $skippedCount already public');
    
  } catch (e) {
    print('   ❌ Failed to configure existing files: $e');
  }
}

/// Get all files recursively from a folder
Future<List<drive.File>> getAllFilesRecursively(
  drive.DriveApi driveApi,
  String folderId,
) async {
  final allFiles = <drive.File>[];
  
  try {
    // Get files in current folder
    final fileList = await driveApi.files.list(
      q: "'$folderId' in parents and trashed = false",
      pageSize: 1000,
      $fields: 'files(id,name,mimeType,parents)',
    );
    
    if (fileList.files != null) {
      for (final file in fileList.files!) {
        allFiles.add(file);
        
        // If it's a folder, recursively get its contents
        if (file.mimeType == 'application/vnd.google-apps.folder') {
          final subFiles = await getAllFilesRecursively(driveApi, file.id!);
          allFiles.addAll(subFiles);
        }
      }
    }
    
  } catch (e) {
    print('   ⚠️  Failed to list files in folder $folderId: $e');
  }
  
  return allFiles;
}
