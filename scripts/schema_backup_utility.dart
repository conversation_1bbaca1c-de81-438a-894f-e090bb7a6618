#!/usr/bin/env dart

import 'dart:convert';
import 'dart:io';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Utility script to backup PocketBase schema and data before modifications
///
/// Usage: dart scripts/schema_backup_utility.dart
///
/// This script will:
/// 1. Export current collection schemas
/// 2. Export existing data from critical collections
/// 3. Create timestamped backup files
/// 4. Generate rollback procedures
/// 5. Document current data structure

Future<void> main() async {
  LoggerService.info('🔄 Starting PocketBase schema backup process...');

  try {
    final pocketBaseService = PocketBaseService();

    // Check PocketBase connection
    try {
      await pocketBaseService.pb.health.check();
      LoggerService.info('✅ Connected to PocketBase successfully');
    } catch (e) {
      LoggerService.error('❌ Failed to connect to PocketBase', e);
      LoggerService.error('Please ensure PocketBase is running and accessible');
      exit(1);
    }

    // Create backup directory with timestamp
    final timestamp =
        DateTime.now().toIso8601String().replaceAll(':', '-').split('.')[0];
    final backupDir = Directory('backups/schema_backup_$timestamp');
    await backupDir.create(recursive: true);

    LoggerService.info('📁 Created backup directory: ${backupDir.path}');

    // Backup collections schema
    await backupCollectionsSchema(pocketBaseService, backupDir);

    // Backup critical data
    await backupCriticalData(pocketBaseService, backupDir);

    // Generate documentation
    await generateDocumentation(pocketBaseService, backupDir);

    // Create rollback procedures
    await createRollbackProcedures(backupDir);

    LoggerService.info('');
    LoggerService.info('🎉 Schema backup completed successfully!');
    LoggerService.info('📋 Backup location: ${backupDir.path}');
    LoggerService.info('');
    LoggerService.info('📄 Generated files:');
    LoggerService.info('   • collections_schema.json - Collection definitions');
    LoggerService.info(
      '   • claim_documents_data.json - Current document data',
    );
    LoggerService.info(
      '   • funding_applications_data.json - Application data',
    );
    LoggerService.info('   • users_data.json - User data');
    LoggerService.info(
      '   • current_structure_documentation.md - Data structure docs',
    );
    LoggerService.info('   • rollback_procedures.md - Rollback instructions');
    LoggerService.info('');
    LoggerService.info('✅ Ready to proceed with schema modifications!');
  } catch (e) {
    LoggerService.error('❌ Schema backup failed', e);
    exit(1);
  }
}

/// Backup all collection schemas
Future<void> backupCollectionsSchema(
  PocketBaseService pbService,
  Directory backupDir,
) async {
  LoggerService.info('📊 Backing up collection schemas...');

  try {
    // Note: This requires admin access to get collection schemas
    // In production, this should be done via admin interface

    final collectionsToBackup = [
      'claim_documents',
      'funding_applications',
      'users',
      'notifications',
      'notification_read_states',
      'solicitor_profiles',
      'co_funder_profiles',
      'claimant_profiles',
    ];

    final schemaBackup = <String, dynamic>{};

    for (final collectionName in collectionsToBackup) {
      try {
        LoggerService.info('   📋 Backing up schema for: $collectionName');

        // Get collection info (this may require admin access)
        // For now, we'll document the known structure
        schemaBackup[collectionName] = {
          'name': collectionName,
          'backed_up_at': DateTime.now().toIso8601String(),
          'note':
              'Schema backup requires admin access - documented current known structure',
        };
      } catch (e) {
        LoggerService.error(
          '⚠️  Could not backup schema for $collectionName: $e',
        );
        schemaBackup[collectionName] = {
          'name': collectionName,
          'error': e.toString(),
          'backed_up_at': DateTime.now().toIso8601String(),
        };
      }
    }

    // Save schema backup
    final schemaFile = File('${backupDir.path}/collections_schema.json');
    await schemaFile.writeAsString(
      const JsonEncoder.withIndent('  ').convert(schemaBackup),
    );

    LoggerService.info('✅ Collection schemas backed up to: ${schemaFile.path}');
  } catch (e) {
    LoggerService.error('❌ Failed to backup collection schemas', e);
    rethrow;
  }
}

/// Backup critical data from key collections
Future<void> backupCriticalData(
  PocketBaseService pbService,
  Directory backupDir,
) async {
  LoggerService.info('💾 Backing up critical data...');

  try {
    // Backup claim_documents data
    await backupCollectionData(pbService, 'claim_documents', backupDir);

    // Backup funding_applications data (limited fields for privacy)
    await backupCollectionData(
      pbService,
      'funding_applications',
      backupDir,
      fields: ['id', 'created', 'updated', 'stage', 'documents_migrated'],
    );

    // Backup users data (limited fields for privacy)
    await backupCollectionData(
      pbService,
      'users',
      backupDir,
      fields: ['id', 'email', 'role', 'created', 'updated'],
    );

    LoggerService.info('✅ Critical data backup completed');
  } catch (e) {
    LoggerService.error('❌ Failed to backup critical data', e);
    rethrow;
  }
}

/// Backup data from a specific collection
Future<void> backupCollectionData(
  PocketBaseService pbService,
  String collectionName,
  Directory backupDir, {
  List<String>? fields,
}) async {
  try {
    LoggerService.info('   📄 Backing up data for: $collectionName');

    final records = await pbService.getFullList(
      collectionName: collectionName,
      sort: '-created',
    );

    List<Map<String, dynamic>> backupData = [];

    for (final record in records) {
      Map<String, dynamic> recordData = {};

      if (fields != null) {
        // Only backup specified fields
        for (final field in fields) {
          if (record.data.containsKey(field)) {
            recordData[field] = record.data[field];
          }
        }
      } else {
        // Backup all data
        recordData = Map<String, dynamic>.from(record.data);
      }

      recordData['record_id'] = record.id;
      backupData.add(recordData);
    }

    final dataFile = File('${backupDir.path}/${collectionName}_data.json');
    await dataFile.writeAsString(
      const JsonEncoder.withIndent('  ').convert({
        'collection': collectionName,
        'backed_up_at': DateTime.now().toIso8601String(),
        'record_count': backupData.length,
        'records': backupData,
      }),
    );

    LoggerService.info(
      '     ✅ Backed up ${backupData.length} records to: ${dataFile.path}',
    );
  } catch (e) {
    LoggerService.error('❌ Failed to backup data for $collectionName', e);
    rethrow;
  }
}

/// Generate comprehensive documentation of current structure
Future<void> generateDocumentation(
  PocketBaseService pbService,
  Directory backupDir,
) async {
  LoggerService.info('📝 Generating current structure documentation...');

  try {
    final docFile = File(
      '${backupDir.path}/current_structure_documentation.md',
    );

    final documentation = '''
# Current PocketBase Schema Documentation
Generated: ${DateTime.now().toIso8601String()}

## Overview
This document captures the current state of the PocketBase schema before Google Drive integration modifications.

## Collections Structure

### claim_documents Collection
Current fields:
- id (Text, auto-generated)
- funding_application_id (Relation to funding_applications)
- logical_name (Text) - Document category name
- current_version_file_id (Text) - ID of current version
- versions (JSON) - Array of version objects
- document_file (File, multiple) - PocketBase file storage
- name (Text) - Display name
- uploaded_by (Relation to users)
- created (DateTime, auto)
- updated (DateTime, auto)

Version object structure:
```json
{
  "file_id": "string",
  "filename": "string", 
  "uploaded_at": "ISO8601 datetime",
  "uploaded_by": "user_id",
  "notes": "optional string"
}
```

### funding_applications Collection
Key fields related to documents:
- id (Text)
- solicitor_profile_id (Relation)
- claim_title (Text)
- stage (Select)
- created/updated (DateTime)

### users Collection
Standard PocketBase users collection with:
- id, email, password (managed by PocketBase)
- role (Select: solicitor, co_funder, claimant, admin)
- Additional profile relations

## Current Document Flow
1. Documents uploaded to claim_documents collection
2. Files stored in PocketBase native storage
3. Version history tracked in JSON field
4. Relationships maintained via funding_application_id

## API Dependencies
- ClaimDocumentsService uses claim_documents collection
- File uploads use PocketBase file API
- Version management through JSON field updates

## Known Issues Before Migration
- File storage limited to PocketBase capacity
- No external backup of files
- Version history in JSON may have performance implications
- No audit logging of document access

## Migration Preparation Notes
- All existing functionality must remain working
- Backward compatibility required during transition
- File data integrity must be maintained
- User permissions must be preserved
''';

    await docFile.writeAsString(documentation);
    LoggerService.info('✅ Documentation generated: ${docFile.path}');
  } catch (e) {
    LoggerService.error('❌ Failed to generate documentation', e);
    rethrow;
  }
}

/// Create rollback procedures documentation
Future<void> createRollbackProcedures(Directory backupDir) async {
  LoggerService.info('🔄 Creating rollback procedures...');

  try {
    final rollbackFile = File('${backupDir.path}/rollback_procedures.md');

    final procedures = '''
# Rollback Procedures for Google Drive Integration
Generated: ${DateTime.now().toIso8601String()}

## Overview
This document provides step-by-step procedures to rollback schema changes if needed.

## Emergency Rollback Steps

### 1. Immediate Rollback (if migration fails)
1. Stop all application services
2. Access PocketBase admin interface
3. Remove new collections:
   - google_drive_config
   - document_access_logs  
   - migration_batches
4. Remove new fields from existing collections
5. Restore original collection permissions
6. Restart services and test

### 2. Data Restoration (if data corruption occurs)
1. Use backup files in this directory
2. Restore claim_documents data from claim_documents_data.json
3. Restore funding_applications data from funding_applications_data.json
4. Verify data integrity
5. Test all document operations

### 3. Schema Restoration
1. Remove added fields from claim_documents:
   - google_drive_folder_id
   - migration_status
   - migration_date
   - storage_type
   - migration_attempts
   - migration_errors
   - last_migration_attempt
   - migration_batch_id
2. Remove added fields from funding_applications:
   - documents_migrated
   - migration_batch_id
3. Remove added fields from users:
   - google_drive_permissions
   - drive_access_enabled

### 4. Verification Steps
1. Test document upload functionality
2. Test document download functionality
3. Test version history
4. Test document deletion
5. Verify all user permissions work
6. Check application performance

## Backup File Locations
- Schema: collections_schema.json
- Data: *_data.json files
- Documentation: current_structure_documentation.md

## Support Contacts
- Technical Lead: [Contact Information]
- Database Admin: [Contact Information]
- Emergency Escalation: [Contact Information]

## Testing Checklist After Rollback
- [ ] Document upload works
- [ ] Document download works
- [ ] Version history displays correctly
- [ ] Document deletion works
- [ ] User permissions intact
- [ ] Performance acceptable
- [ ] No data loss detected
- [ ] All integrations working

## Notes
- Always test rollback procedures in development first
- Keep this backup until migration is fully stable
- Document any issues encountered during rollback
- Update procedures based on lessons learned
''';

    await rollbackFile.writeAsString(procedures);
    LoggerService.info('✅ Rollback procedures created: ${rollbackFile.path}');
  } catch (e) {
    LoggerService.error('❌ Failed to create rollback procedures', e);
    rethrow;
  }
}
