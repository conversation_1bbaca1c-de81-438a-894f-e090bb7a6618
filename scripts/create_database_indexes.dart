#!/usr/bin/env dart

import 'dart:io';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Database index creation script for Google Drive integration
///
/// Usage: dart scripts/create_database_indexes.dart
///
/// This script provides instructions for creating performance indexes
/// for the Google Drive integration schema modifications.
///
/// Note: Index creation must be done through PocketBase Admin Interface
/// as the API doesn't provide direct index management capabilities.

Future<void> main() async {
  LoggerService.info('📊 Generating database index creation instructions...');

  try {
    final pocketBaseService = PocketBaseService();

    // Check PocketBase connection
    try {
      await pocketBaseService.pb.health.check();
      LoggerService.info('✅ Connected to PocketBase successfully');
    } catch (e) {
      LoggerService.error('❌ Failed to connect to PocketBase', e);
      LoggerService.error('Please ensure PocketBase is running and accessible');
      exit(1);
    }

    // Generate index creation instructions
    await generateIndexInstructions();

    // Generate performance monitoring queries
    await generatePerformanceQueries();

    LoggerService.info('');
    LoggerService.info(
      '🎉 Database index instructions generated successfully!',
    );
    LoggerService.info('');
    LoggerService.info('📋 Next Steps:');
    LoggerService.info('1. Review generated instructions in database_indexes/');
    LoggerService.info('2. Apply indexes through PocketBase Admin Interface');
    LoggerService.info('3. Run performance tests to validate improvements');
    LoggerService.info('4. Monitor query performance in production');
  } catch (e) {
    LoggerService.error('❌ Index instruction generation failed', e);
    exit(1);
  }
}

/// Generate comprehensive index creation instructions
Future<void> generateIndexInstructions() async {
  LoggerService.info('📝 Generating index creation instructions...');

  // Create instructions directory
  final instructionsDir = Directory('database_indexes');
  await instructionsDir.create(recursive: true);

  // Generate index instructions for each collection
  await generateClaimDocumentsIndexes(instructionsDir);
  await generateDocumentAccessLogsIndexes(instructionsDir);
  await generateMigrationBatchesIndexes(instructionsDir);
  await generateCompositeIndexes(instructionsDir);

  LoggerService.info(
    '✅ Index instructions generated in: ${instructionsDir.path}',
  );
}

/// Generate claim_documents collection indexes
Future<void> generateClaimDocumentsIndexes(Directory instructionsDir) async {
  final file = File('${instructionsDir.path}/01_claim_documents_indexes.md');

  final content = '''
# Claim Documents Collection Indexes

## Overview
Create performance indexes for the claim_documents collection to optimize Google Drive integration queries.

## Indexes to Create

### 1. Migration Status Index
- **Field**: migration_status
- **Type**: Single field index
- **Purpose**: Fast filtering by migration status
- **Usage**: Finding documents by migration state (pending, completed, failed)

### 2. Storage Type Index
- **Field**: storage_type
- **Type**: Single field index
- **Purpose**: Fast filtering by storage location
- **Usage**: Separating PocketBase vs Google Drive documents

### 3. Funding Application Composite Index
- **Fields**: funding_application_id, logical_name
- **Type**: Composite index
- **Purpose**: Fast document lookup for specific applications
- **Usage**: Primary query pattern for document retrieval

### 4. Migration Batch Index
- **Field**: migration_batch_id
- **Type**: Single field index
- **Purpose**: Fast filtering by migration batch
- **Usage**: Tracking migration progress and batch operations

### 5. Last Migration Attempt Index
- **Field**: last_migration_attempt
- **Type**: Single field index (descending)
- **Purpose**: Finding recently attempted migrations
- **Usage**: Retry logic and migration monitoring

## Implementation Steps

### Through PocketBase Admin Interface:
1. Navigate to Collections → claim_documents
2. Go to the Indexes tab
3. Create each index as specified above
4. Test query performance after creation

### Index Creation Syntax (if using direct database access):
```sql
-- Migration status index
CREATE INDEX idx_claim_documents_migration_status ON claim_documents(migration_status);

-- Storage type index  
CREATE INDEX idx_claim_documents_storage_type ON claim_documents(storage_type);

-- Composite index for funding application + logical name
CREATE INDEX idx_claim_documents_funding_logical ON claim_documents(funding_application_id, logical_name);

-- Migration batch index
CREATE INDEX idx_claim_documents_migration_batch ON claim_documents(migration_batch_id);

-- Last migration attempt index (descending)
CREATE INDEX idx_claim_documents_last_migration ON claim_documents(last_migration_attempt DESC);
```

## Performance Impact
- **Query Speed**: 10-100x improvement for filtered queries
- **Migration Queries**: Significant improvement for batch operations
- **Document Lookup**: Faster retrieval by application and category
- **Storage Overhead**: Minimal (< 5% of collection size)

## Monitoring
After creating indexes, monitor:
- Query execution times
- Index usage statistics
- Storage overhead
- Write performance impact
''';

  await file.writeAsString(content);
  LoggerService.info('   📄 Created: ${file.path}');
}

/// Generate document_access_logs collection indexes
Future<void> generateDocumentAccessLogsIndexes(
  Directory instructionsDir,
) async {
  final file = File(
    '${instructionsDir.path}/02_document_access_logs_indexes.md',
  );

  final content = '''
# Document Access Logs Collection Indexes

## Overview
Create performance indexes for the document_access_logs collection to optimize audit queries.

## Indexes to Create

### 1. Timestamp Index (Primary)
- **Field**: timestamp
- **Type**: Single field index (descending)
- **Purpose**: Fast chronological queries
- **Usage**: Recent activity, time-based filtering

### 2. User ID Index
- **Field**: user_id
- **Type**: Single field index
- **Purpose**: Fast user activity lookup
- **Usage**: User-specific audit trails

### 3. Document ID Index
- **Field**: document_id
- **Type**: Single field index
- **Purpose**: Fast document activity lookup
- **Usage**: Document-specific audit trails

### 4. Action Type Index
- **Field**: action
- **Type**: Single field index
- **Purpose**: Fast filtering by action type
- **Usage**: Specific action analysis (uploads, downloads, etc.)

### 5. Success Status Index
- **Field**: success
- **Type**: Single field index
- **Purpose**: Fast filtering by success/failure
- **Usage**: Error analysis and monitoring

### 6. User-Document Composite Index
- **Fields**: user_id, document_id, timestamp
- **Type**: Composite index
- **Purpose**: Optimized user-document activity queries
- **Usage**: User access patterns for specific documents

## Implementation Steps

### Through PocketBase Admin Interface:
1. Navigate to Collections → document_access_logs
2. Go to the Indexes tab
3. Create each index as specified above
4. Test query performance after creation

### Index Creation Syntax (if using direct database access):
```sql
-- Timestamp index (descending for recent-first queries)
CREATE INDEX idx_access_logs_timestamp ON document_access_logs(timestamp DESC);

-- User ID index
CREATE INDEX idx_access_logs_user_id ON document_access_logs(user_id);

-- Document ID index
CREATE INDEX idx_access_logs_document_id ON document_access_logs(document_id);

-- Action type index
CREATE INDEX idx_access_logs_action ON document_access_logs(action);

-- Success status index
CREATE INDEX idx_access_logs_success ON document_access_logs(success);

-- Composite user-document-time index
CREATE INDEX idx_access_logs_user_doc_time ON document_access_logs(user_id, document_id, timestamp DESC);
```

## Performance Impact
- **Audit Queries**: 50-500x improvement for time-based queries
- **User Activity**: Fast user-specific audit trails
- **Document History**: Quick document access history
- **Error Analysis**: Efficient failure pattern analysis

## Data Retention Considerations
- Implement automatic cleanup for logs older than 90 days
- Archive old logs to separate storage
- Monitor index size growth over time
- Consider partitioning for very high-volume environments
''';

  await file.writeAsString(content);
  LoggerService.info('   📄 Created: ${file.path}');
}

/// Generate migration_batches collection indexes
Future<void> generateMigrationBatchesIndexes(Directory instructionsDir) async {
  final file = File('${instructionsDir.path}/03_migration_batches_indexes.md');

  final content = '''
# Migration Batches Collection Indexes

## Overview
Create performance indexes for the migration_batches collection to optimize migration tracking.

## Indexes to Create

### 1. Batch ID Index (Unique)
- **Field**: batch_id
- **Type**: Unique index
- **Purpose**: Fast batch lookup and uniqueness enforcement
- **Usage**: Primary batch identification

### 2. Status Index
- **Field**: status
- **Type**: Single field index
- **Purpose**: Fast filtering by batch status
- **Usage**: Finding active, completed, or failed batches

### 3. Start Time Index
- **Field**: start_time
- **Type**: Single field index (descending)
- **Purpose**: Chronological batch ordering
- **Usage**: Recent batches, time-based analysis

### 4. Created By Index
- **Field**: created_by
- **Type**: Single field index
- **Purpose**: Fast filtering by creator
- **Usage**: User-specific batch history

### 5. Migration Type Index
- **Field**: migration_type
- **Type**: Single field index
- **Purpose**: Fast filtering by migration type
- **Usage**: Analyzing different migration strategies

## Implementation Steps

### Through PocketBase Admin Interface:
1. Navigate to Collections → migration_batches
2. Go to the Indexes tab
3. Create each index as specified above
4. Ensure batch_id has unique constraint

### Index Creation Syntax (if using direct database access):
```sql
-- Unique batch ID index
CREATE UNIQUE INDEX idx_migration_batches_batch_id ON migration_batches(batch_id);

-- Status index
CREATE INDEX idx_migration_batches_status ON migration_batches(status);

-- Start time index (descending)
CREATE INDEX idx_migration_batches_start_time ON migration_batches(start_time DESC);

-- Created by index
CREATE INDEX idx_migration_batches_created_by ON migration_batches(created_by);

-- Migration type index
CREATE INDEX idx_migration_batches_type ON migration_batches(migration_type);
```

## Performance Impact
- **Batch Lookup**: Instant batch identification by ID
- **Status Queries**: Fast filtering of active/completed batches
- **Progress Monitoring**: Efficient batch progress tracking
- **Historical Analysis**: Quick access to migration history
''';

  await file.writeAsString(content);
  LoggerService.info('   📄 Created: ${file.path}');
}

/// Generate composite indexes for cross-collection queries
Future<void> generateCompositeIndexes(Directory instructionsDir) async {
  final file = File('${instructionsDir.path}/04_composite_indexes.md');

  final content = '''
# Composite Indexes for Cross-Collection Optimization

## Overview
Create composite indexes to optimize complex queries across multiple fields.

## Composite Indexes

### 1. Document Migration Progress Index
- **Collection**: claim_documents
- **Fields**: funding_application_id, migration_status, storage_type
- **Purpose**: Optimize migration progress queries per application
- **Usage**: Dashboard migration status displays

### 2. User Document Access Pattern Index
- **Collection**: document_access_logs
- **Fields**: user_id, action, timestamp
- **Purpose**: Optimize user activity analysis
- **Usage**: User behavior analytics and security monitoring

### 3. Failed Migration Analysis Index
- **Collection**: claim_documents
- **Fields**: migration_status, last_migration_attempt, migration_attempts
- **Purpose**: Optimize failed migration analysis
- **Usage**: Retry logic and error analysis

## Implementation Notes
- Composite indexes should be created after single-field indexes
- Monitor query patterns to identify additional composite index needs
- Consider index maintenance overhead vs. query performance benefits
- Test with realistic data volumes before production deployment

## Query Optimization Examples

### Migration Progress Query
```sql
-- Optimized by funding_application + migration_status + storage_type index
SELECT * FROM claim_documents 
WHERE funding_application_id = 'app123' 
  AND migration_status = 'completed' 
  AND storage_type = 'google_drive';
```

### User Activity Analysis
```sql
-- Optimized by user_id + action + timestamp index
SELECT * FROM document_access_logs 
WHERE user_id = 'user123' 
  AND action = 'download' 
  AND timestamp > '2024-01-01'
ORDER BY timestamp DESC;
```

### Failed Migration Analysis
```sql
-- Optimized by migration_status + last_migration_attempt + migration_attempts index
SELECT * FROM claim_documents 
WHERE migration_status = 'failed' 
  AND migration_attempts > 3 
  AND last_migration_attempt < '2024-01-01'
ORDER BY last_migration_attempt DESC;
```
''';

  await file.writeAsString(content);
  LoggerService.info('   📄 Created: ${file.path}');
}

/// Generate performance monitoring queries
Future<void> generatePerformanceQueries() async {
  LoggerService.info('📊 Generating performance monitoring queries...');

  final queriesDir = Directory('performance_monitoring');
  await queriesDir.create(recursive: true);

  final file = File('${queriesDir.path}/performance_monitoring_queries.md');

  final content = '''
# Performance Monitoring Queries

## Overview
Use these queries to monitor the performance impact of the new indexes and schema modifications.

## Index Usage Monitoring

### Check Index Usage (SQLite)
```sql
-- Check if indexes are being used
EXPLAIN QUERY PLAN SELECT * FROM claim_documents WHERE migration_status = 'pending';
EXPLAIN QUERY PLAN SELECT * FROM document_access_logs WHERE user_id = 'user123';
EXPLAIN QUERY PLAN SELECT * FROM migration_batches WHERE status = 'running';
```

## Performance Benchmarks

### Document Retrieval Performance
```sql
-- Measure document lookup performance
SELECT COUNT(*) FROM claim_documents 
WHERE funding_application_id = 'app123' AND logical_name = 'contract';

-- Measure migration status filtering
SELECT COUNT(*) FROM claim_documents WHERE migration_status = 'pending';
```

### Audit Log Performance
```sql
-- Measure recent activity queries
SELECT COUNT(*) FROM document_access_logs 
WHERE timestamp > datetime('now', '-1 day');

-- Measure user activity queries
SELECT COUNT(*) FROM document_access_logs WHERE user_id = 'user123';
```

## Storage Impact Analysis

### Index Size Monitoring
```sql
-- Check database size and index overhead
SELECT name, COUNT(*) as record_count 
FROM sqlite_master 
WHERE type = 'table' 
GROUP BY name;
```

## Query Performance Targets

### Acceptable Performance Thresholds
- Document lookup by application + logical name: < 10ms
- Migration status filtering: < 50ms
- User activity queries: < 100ms
- Batch status queries: < 25ms

### Performance Alerts
Monitor and alert if queries exceed these thresholds:
- Document queries > 100ms
- Audit queries > 500ms
- Migration queries > 200ms

## Optimization Recommendations

1. **Regular Maintenance**
   - Analyze query patterns monthly
   - Update statistics regularly
   - Monitor index fragmentation

2. **Capacity Planning**
   - Monitor storage growth
   - Plan for index size increases
   - Consider archival strategies

3. **Query Optimization**
   - Use EXPLAIN QUERY PLAN for slow queries
   - Optimize WHERE clause ordering
   - Consider additional indexes for new query patterns
''';

  await file.writeAsString(content);
  LoggerService.info(
    '✅ Performance monitoring queries generated: ${file.path}',
  );
}
