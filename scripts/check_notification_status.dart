#!/usr/bin/env dart

import 'dart:io';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Quick status check script to see the current state of notifications
/// 
/// Usage: dart scripts/check_notification_status.dart

Future<void> main() async {
  LoggerService.info('📊 Checking notification system status...');
  
  try {
    final pocketBaseService = PocketBaseService();
    
    // Check PocketBase connection
    await pocketBaseService.pb.health.check();
    LoggerService.info('✅ PocketBase connection: OK');
    
    // Check collections exist
    await _checkCollections(pocketBaseService);
    
    // Get notification counts
    await _getNotificationCounts(pocketBaseService);
    
    // Check read states
    await _checkReadStates(pocketBaseService);
    
    // Sample some data
    await _sampleData(pocketBaseService);
    
  } catch (e) {
    LoggerService.error('❌ Status check failed', e);
    exit(1);
  }
}

Future<void> _checkCollections(PocketBaseService pocketBaseService) async {
  LoggerService.info('');
  LoggerService.info('📋 Checking collections...');
  
  try {
    await pocketBaseService.pb.collection('notifications').getList(perPage: 1);
    LoggerService.info('✅ notifications collection: EXISTS');
  } catch (e) {
    LoggerService.error('❌ notifications collection: NOT FOUND');
  }
  
  try {
    await pocketBaseService.pb.collection('notification_read_states').getList(perPage: 1);
    LoggerService.info('✅ notification_read_states collection: EXISTS');
  } catch (e) {
    LoggerService.error('❌ notification_read_states collection: NOT FOUND');
  }
  
  try {
    await pocketBaseService.pb.collection('users').getList(perPage: 1);
    LoggerService.info('✅ users collection: EXISTS');
  } catch (e) {
    LoggerService.error('❌ users collection: NOT FOUND');
  }
}

Future<void> _getNotificationCounts(PocketBaseService pocketBaseService) async {
  LoggerService.info('');
  LoggerService.info('📊 Getting notification counts...');
  
  try {
    // Total notifications
    final allNotifications = await pocketBaseService.pb
        .collection('notifications')
        .getList(perPage: 1);
    LoggerService.info('📧 Total notifications: ${allNotifications.totalItems}');
    
    // Global notifications
    final globalNotifications = await pocketBaseService.pb
        .collection('notifications')
        .getList(filter: 'recipientId:length < 1', perPage: 1);
    LoggerService.info('🌍 Global notifications: ${globalNotifications.totalItems}');
    
    // User-specific notifications
    final userNotifications = await pocketBaseService.pb
        .collection('notifications')
        .getList(filter: 'recipientId:length > 0', perPage: 1);
    LoggerService.info('👤 User-specific notifications: ${userNotifications.totalItems}');
    
    // Read notifications (old system)
    final readNotifications = await pocketBaseService.pb
        .collection('notifications')
        .getList(filter: 'isRead = true', perPage: 1);
    LoggerService.info('✅ Notifications marked as read (old system): ${readNotifications.totalItems}');
    
    // Global notifications still marked as read (should be 0 after migration)
    final globalReadNotifications = await pocketBaseService.pb
        .collection('notifications')
        .getList(filter: 'recipientId:length < 1 && isRead = true', perPage: 1);
    
    if (globalReadNotifications.totalItems > 0) {
      LoggerService.warning('⚠️  Global notifications still marked as read: ${globalReadNotifications.totalItems}');
      LoggerService.warning('   This suggests migration may not have completed properly');
    } else {
      LoggerService.info('✅ Global notifications properly reset: 0 still marked as read');
    }
    
  } catch (e) {
    LoggerService.error('❌ Error getting notification counts', e);
  }
}

Future<void> _checkReadStates(PocketBaseService pocketBaseService) async {
  LoggerService.info('');
  LoggerService.info('📖 Checking read states...');
  
  try {
    final readStates = await pocketBaseService.pb
        .collection('notification_read_states')
        .getList(perPage: 1);
    LoggerService.info('📚 Total read state records: ${readStates.totalItems}');
    
    if (readStates.totalItems > 0) {
      // Count read vs unread
      final readStatesRead = await pocketBaseService.pb
          .collection('notification_read_states')
          .getList(filter: 'is_read = true', perPage: 1);
      LoggerService.info('✅ Read state records (read): ${readStatesRead.totalItems}');
      
      final readStatesUnread = await pocketBaseService.pb
          .collection('notification_read_states')
          .getList(filter: 'is_read = false', perPage: 1);
      LoggerService.info('📭 Read state records (unread): ${readStatesUnread.totalItems}');
    }
    
  } catch (e) {
    LoggerService.error('❌ Error checking read states', e);
  }
}

Future<void> _sampleData(PocketBaseService pocketBaseService) async {
  LoggerService.info('');
  LoggerService.info('🔍 Sampling data...');
  
  try {
    // Sample global notification
    final globalNotifications = await pocketBaseService.pb
        .collection('notifications')
        .getList(filter: 'recipientId:length < 1', perPage: 1);
    
    if (globalNotifications.items.isNotEmpty) {
      final notification = globalNotifications.items.first;
      LoggerService.info('📧 Sample global notification:');
      LoggerService.info('   • ID: ${notification.id}');
      LoggerService.info('   • Title: "${notification.data['title']}"');
      LoggerService.info('   • Is Read: ${notification.data['isRead']}');
      LoggerService.info('   • Recipients: ${notification.data['recipientId']}');
      
      // Check read states for this notification
      final readStates = await pocketBaseService.pb
          .collection('notification_read_states')
          .getList(filter: 'notification_id = "${notification.id}"');
      LoggerService.info('   • Read state records: ${readStates.totalItems}');
    }
    
    // Sample user-specific notification
    final userNotifications = await pocketBaseService.pb
        .collection('notifications')
        .getList(filter: 'recipientId:length > 0', perPage: 1);
    
    if (userNotifications.items.isNotEmpty) {
      final notification = userNotifications.items.first;
      LoggerService.info('👤 Sample user-specific notification:');
      LoggerService.info('   • ID: ${notification.id}');
      LoggerService.info('   • Title: "${notification.data['title']}"');
      LoggerService.info('   • Is Read: ${notification.data['isRead']}');
      LoggerService.info('   • Recipients: ${notification.data['recipientId']}');
      
      // Check read states for this notification (should be 0)
      final readStates = await pocketBaseService.pb
          .collection('notification_read_states')
          .getList(filter: 'notification_id = "${notification.id}"');
      
      if (readStates.totalItems > 0) {
        LoggerService.warning('   ⚠️  Read state records: ${readStates.totalItems} (should be 0)');
      } else {
        LoggerService.info('   ✅ Read state records: 0 (correct)');
      }
    }
    
    // Sample read state
    final readStates = await pocketBaseService.pb
        .collection('notification_read_states')
        .getList(perPage: 1);
    
    if (readStates.items.isNotEmpty) {
      final readState = readStates.items.first;
      LoggerService.info('📖 Sample read state:');
      LoggerService.info('   • ID: ${readState.id}');
      LoggerService.info('   • Notification ID: ${readState.data['notification_id']}');
      LoggerService.info('   • User ID: ${readState.data['user_id']}');
      LoggerService.info('   • Is Read: ${readState.data['is_read']}');
      LoggerService.info('   • Read At: ${readState.data['read_at']}');
    }
    
  } catch (e) {
    LoggerService.error('❌ Error sampling data', e);
  }
  
  LoggerService.info('');
  LoggerService.info('✅ Status check completed!');
}
