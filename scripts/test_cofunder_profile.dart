import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/services/cofunder_base_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_profile_model.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Test script to debug co-funder profile issues
void main() async {
  // ignore: avoid_print
    print('Testing Co-Funder Profile Functionality...\n');

  try {
    // Initialize services
    final pbService = PocketBaseService();
    final coFunderService = CoFunderBaseService();

    // Test authentication first
    // ignore: avoid_print
    print('1. Testing authentication...');
    final currentUser = pbService.currentUser;
    if (currentUser == null) {
      // ignore: avoid_print
    print('❌ No authenticated user found');
      // ignore: avoid_print
    print('Please authenticate first in the app');
      return;
    }
    // ignore: avoid_print
    print('✅ Authenticated user: ${currentUser.data['email']}');
    // ignore: avoid_print
    print('   User ID: ${currentUser.id}');
    // ignore: avoid_print
    print('   User Type: ${currentUser.data['user_type']}');

    // Test co-funder profile retrieval
    // ignore: avoid_print
    print('\n2. Testing co-funder profile retrieval...');
    try {
      final profile = await coFunderService.getCurrentCoFunderProfile();
      if (profile == null) {
        // ignore: avoid_print
    print('❌ No co-funder profile found');
        // ignore: avoid_print
    print('   This might be expected if the user is not a co-funder');

        // Try to create a profile if user is a co-funder
        if (currentUser.data['user_type'] == 'co_funder') {
          // ignore: avoid_print
    print('\n3. Attempting to create co-funder profile...');
          try {
            final newProfile = await coFunderService.createCoFunderProfile();
            // ignore: avoid_print
    print('✅ Created co-funder profile: ${newProfile.id}');
            // ignore: avoid_print
    print('   Current Level: ${newProfile.currentLevel}');
            // ignore: avoid_print
    print('   AML/KYC Status: ${newProfile.amlKycStatus}');
          } catch (e) {
            // ignore: avoid_print
    print('❌ Failed to create co-funder profile: $e');
          }
        }
      } else {
        // ignore: avoid_print
    print('✅ Co-funder profile found: ${profile.id}');
        // ignore: avoid_print
    print('   User ID: ${profile.userId}');
        // ignore: avoid_print
    print('   Current Level: ${profile.currentLevel}');
        // ignore: avoid_print
    print('   AML/KYC Status: ${profile.amlKycStatus}');
        // ignore: avoid_print
    print('   Level 4 Subscribed: ${profile.level4Subscribed}');
        // ignore: avoid_print
    print(
          '   Notification Preferences: ${profile.notificationPreferences}',
        );
        // ignore: avoid_print
    print('   Investment Preferences: ${profile.investmentPreferences}');

        // Test expanded user data
        if (profile.userExpanded != null) {
          // ignore: avoid_print
    print('   Expanded User Data:');
          // ignore: avoid_print
    print('     Name: ${profile.name}');
          // ignore: avoid_print
    print('     Email: ${profile.email}');
          // ignore: avoid_print
    print('     Phone: ${profile.phoneNumber}');
        } else {
          // ignore: avoid_print
    print('   ⚠️  No expanded user data found');
        }
      }
    } catch (e) {
      // ignore: avoid_print
    print('❌ Error retrieving co-funder profile: $e');
      LoggerService.error('Profile retrieval error', e);
    }

    // Test collection access
    // ignore: avoid_print
    print('\n4. Testing direct collection access...');
    try {
      final records = await pbService.client
          .collection('co_funder_profiles')
          .getList(filter: 'user_id = "${currentUser.id}"', expand: 'user_id');

      // ignore: avoid_print
    print('✅ Direct collection access successful');
      // ignore: avoid_print
    print('   Found ${records.items.length} records');

      if (records.items.isNotEmpty) {
        final record = records.items.first;
        // ignore: avoid_print
    print('   Record ID: ${record.id}');
        // ignore: avoid_print
    print('   Record Data Keys: ${record.data.keys.toList()}');
        // Note: Using deprecated expand for debugging - would use record.get<T>() in production

        // Test JSON conversion
        try {
          final json = record.toJson();
          // ignore: avoid_print
    print('   JSON conversion successful');
          // ignore: avoid_print
    print('   JSON Keys: ${json.keys.toList()}');

          // Test model creation
          final profile = CoFunderProfile.fromJson(json);
          // ignore: avoid_print
    print('   Model creation successful');
          // ignore: avoid_print
    print('   Profile ID: ${profile.id}');
        } catch (e) {
          // ignore: avoid_print
    print('   ❌ Model creation failed: $e');
        }
      }
    } catch (e) {
      // ignore: avoid_print
    print('❌ Direct collection access failed: $e');
    }

    // ignore: avoid_print
    print('\n✅ Test completed successfully');
  } catch (e) {
    // ignore: avoid_print
    print('❌ Test failed with error: $e');
    LoggerService.error('Test script error', e);
  }
}
