import 'dart:io';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Script to set up Google Drive configuration for development environment
Future<void> main() async {
  LoggerService.info('🔧 Setting up Google Drive configuration for development...');
  
  try {
    final pocketBaseService = PocketBaseService();
    
    // Check if PocketBase is accessible
    await pocketBaseService.pb.health.check();
    LoggerService.info('✅ PocketBase connection: OK');
    
    // Check if google_drive_config collection exists
    await _checkGoogleDriveConfigCollection(pocketBaseService);
    
    // Create development configuration
    await _createDevelopmentConfig(pocketBaseService);
    
    LoggerService.info('✅ Google Drive configuration setup completed successfully!');
    LoggerService.info('');
    LoggerService.info('📋 Next steps:');
    LoggerService.info('1. Place your Google service account credentials at:');
    LoggerService.info('   lib/pay-global-document-storage-9179f2284308.json');
    LoggerService.info('2. Or set the GOOGLE_SERVICE_ACCOUNT_CREDENTIALS environment variable');
    LoggerService.info('3. Update the root_folder_id in the configuration if needed');
    
  } catch (e) {
    LoggerService.error('❌ Setup failed', e);
    exit(1);
  }
}

Future<void> _checkGoogleDriveConfigCollection(PocketBaseService pocketBaseService) async {
  LoggerService.info('📋 Checking google_drive_config collection...');
  
  try {
    await pocketBaseService.pb.collection('google_drive_config').getList(perPage: 1);
    LoggerService.info('✅ google_drive_config collection: EXISTS');
  } catch (e) {
    LoggerService.error('❌ google_drive_config collection: NOT FOUND');
    LoggerService.info('');
    LoggerService.info('Please create the google_drive_config collection in PocketBase with the following fields:');
    LoggerService.info('- service_account_email (text)');
    LoggerService.info('- root_folder_id (text)');
    LoggerService.info('- api_quota_limit (number)');
    LoggerService.info('- api_quota_used (number)');
    LoggerService.info('- last_quota_reset (date)');
    LoggerService.info('- encryption_enabled (bool)');
    LoggerService.info('- backup_enabled (bool)');
    LoggerService.info('- environment (text)');
    throw Exception('google_drive_config collection not found');
  }
}

Future<void> _createDevelopmentConfig(PocketBaseService pocketBaseService) async {
  LoggerService.info('🔧 Creating development configuration...');
  
  try {
    // Check if development config already exists
    final existingConfigs = await pocketBaseService.pb
        .collection('google_drive_config')
        .getList(filter: 'environment = "development"');
    
    if (existingConfigs.items.isNotEmpty) {
      LoggerService.info('⚠️  Development configuration already exists');
      LoggerService.info('Current configuration:');
      final config = existingConfigs.items.first;
      LoggerService.info('  - Service Account: ${config.data['service_account_email']}');
      LoggerService.info('  - Root Folder ID: ${config.data['root_folder_id']}');
      LoggerService.info('  - API Quota Limit: ${config.data['api_quota_limit']}');
      LoggerService.info('  - API Quota Used: ${config.data['api_quota_used']}');
      LoggerService.info('  - Encryption Enabled: ${config.data['encryption_enabled']}');
      LoggerService.info('  - Backup Enabled: ${config.data['backup_enabled']}');
      
      // Ask if user wants to update
      LoggerService.info('');
      LoggerService.info('Configuration already exists. Skipping creation.');
      return;
    }
    
    // Create new development configuration
    final configData = {
      'service_account_email': '<EMAIL>',
      'root_folder_id': '1KEh7_66La9HtcZBBHeho1YzIjJuJjeNw',
      'api_quota_limit': 10000, // Daily quota limit
      'api_quota_used': 0,
      'last_quota_reset': DateTime.now().toIso8601String(),
      'encryption_enabled': true,
      'backup_enabled': true,
      'environment': 'production',
    };
    
    final record = await pocketBaseService.pb
        .collection('google_drive_config')
        .create(body: configData);
    
    LoggerService.info('✅ Development configuration created successfully!');
    LoggerService.info('Configuration ID: ${record.id}');
    LoggerService.info('Service Account: ${configData['service_account_email']}');
    LoggerService.info('Root Folder ID: ${configData['root_folder_id']}');
    LoggerService.info('');
    LoggerService.info('⚠️  IMPORTANT: Update the root_folder_id with your actual Google Drive folder ID');
    
  } catch (e) {
    LoggerService.error('Failed to create development configuration', e);
    rethrow;
  }
}
