import 'dart:io' as io;
import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';

/// Test script to verify the complete document upload flow
Future<void> main() async {
  try {
    print('🧪 Testing complete document upload flow...');

    // Load service account credentials
    final credentialsFile = io.File(
      'lib/pay-global-document-storage-9179f2284308.json',
    );
    if (!await credentialsFile.exists()) {
      print('❌ Service account credentials file not found');
      return;
    }

    final credentialsContent = await credentialsFile.readAsString();
    final credentialsMap =
        jsonDecode(credentialsContent) as Map<String, dynamic>;
    final credentials = ServiceAccountCredentials.fromJson(credentialsMap);

    // Create authenticated client
    const scopes = [drive.DriveApi.driveScope];
    final client = await clientViaServiceAccount(credentials, scopes);
    final driveApi = drive.DriveApi(client);

    print('✅ Authenticated with Google Drive');

    // Test the complete folder creation flow
    const testFundingApplicationId = 'test_claim_${*************}';
    const testLogicalName = 'Legal_Documents';

    print('\n🔍 Testing folder structure creation...');

    // Simulate the folder creation process from ClaimDocumentsService
    final folderId = await createTestFolderStructure(
      driveApi,
      testFundingApplicationId,
      testLogicalName,
    );

    print('✅ Created folder structure: $folderId');

    // Test file upload
    print('\n🔍 Testing file upload...');

    final testFileContent = '''
This is a test legal document for claim: $testFundingApplicationId
Category: $testLogicalName
Created at: ${DateTime.now()}

This file tests the complete upload flow from the 3Pay Global application
to Google Drive storage.
''';

    final testFileName =
        'Legal_Documents_v1_test_document_${DateTime.now().millisecondsSinceEpoch}.txt';

    // Create file metadata with proper naming convention
    final fileMetadata =
        drive.File()
          ..name = testFileName
          ..parents = [folderId]
          ..properties = {
            'funding_application_id': testFundingApplicationId,
            'logical_name': testLogicalName,
            'version_number': '1',
            'uploaded_by': 'test_system',
            'created_by': '3PayGlobal',
          };

    final media = drive.Media(
      Stream.value(utf8.encode(testFileContent)),
      testFileContent.length,
      contentType: 'text/plain',
    );

    final uploadedFile = await driveApi.files.create(
      fileMetadata,
      uploadMedia: media,
    );

    print('✅ Successfully uploaded test file: ${uploadedFile.id}');
    print('   File name: ${uploadedFile.name}');

    // Test file retrieval
    print('\n🔍 Testing file retrieval...');

    final retrievedFile =
        await driveApi.files.get(
              uploadedFile.id!,
              $fields: 'id,name,mimeType,size,parents,properties,createdTime',
            )
            as drive.File;

    print('✅ Successfully retrieved file metadata:');
    print('   ID: ${retrievedFile.id}');
    print('   Name: ${retrievedFile.name}');
    print('   Size: ${retrievedFile.size} bytes');
    print('   Created: ${retrievedFile.createdTime}');
    print('   Properties: ${retrievedFile.properties}');

    // Test file download
    print('\n🔍 Testing file download...');

    final downloadMedia =
        await driveApi.files.get(
              uploadedFile.id!,
              downloadOptions: drive.DownloadOptions.fullMedia,
            )
            as drive.Media;

    final bytes = <int>[];
    await for (final chunk in downloadMedia.stream) {
      bytes.addAll(chunk);
    }

    final downloadedContent = utf8.decode(bytes);
    print('✅ Successfully downloaded file content (${bytes.length} bytes)');
    print('   Content preview: ${downloadedContent.substring(0, 100)}...');

    // Test folder listing
    print('\n🔍 Testing folder listing...');

    final folderContents = await driveApi.files.list(
      q: "'$folderId' in parents",
      $fields: 'files(id,name,mimeType,size,properties)',
    );

    print('✅ Successfully listed folder contents:');
    print('   Found ${folderContents.files?.length ?? 0} files');

    if (folderContents.files != null) {
      for (final file in folderContents.files!) {
        print('   - ${file.name} (${file.size} bytes)');
      }
    }

    // Clean up test files
    print('\n🧹 Cleaning up test files...');

    if (uploadedFile.id != null) {
      await driveApi.files.delete(uploadedFile.id!);
      print('✅ Deleted test file');
    }

    // Delete the test folder structure
    await deleteTestFolderStructure(driveApi, testFundingApplicationId);
    print('✅ Deleted test folder structure');

    // Close the client
    client.close();

    print('\n🎉 All document upload flow tests passed successfully!');
    print('✅ The Google Drive integration is working correctly.');
    print(
      '✅ Files should now appear in your Google Drive when uploaded through the app.',
    );
  } catch (e, stackTrace) {
    print('❌ Document upload flow test failed: $e');
    print('Stack trace: $stackTrace');
    io.exit(1);
  }
}

/// Create test folder structure matching the app's logic
Future<String> createTestFolderStructure(
  drive.DriveApi driveApi,
  String fundingApplicationId,
  String logicalName,
) async {
  // Find 3PayGlobal folder
  const payGlobalFolderId = '1mxmvTB0M8c1rPbyh5CA90vdVBSFav8UX';

  // Find Claims folder
  const claimsFolderId = '11rRXKASUWO-O0HVTSrztOEjv1Ak5CM7q';

  // Create funding application folder
  final appFolderName = fundingApplicationId;
  final appFolder =
      drive.File()
        ..name = appFolderName
        ..mimeType = 'application/vnd.google-apps.folder'
        ..parents = [claimsFolderId]
        ..properties = {
          'funding_application_id': fundingApplicationId,
          'folder_type': 'claim_root',
        };

  final createdAppFolder = await driveApi.files.create(appFolder);

  // Create Documents subfolder
  final documentsFolder =
      drive.File()
        ..name = 'Documents'
        ..mimeType = 'application/vnd.google-apps.folder'
        ..parents = [createdAppFolder.id!]
        ..properties = {
          'funding_application_id': fundingApplicationId,
          'folder_type': 'documents',
        };

  final createdDocumentsFolder = await driveApi.files.create(documentsFolder);

  return createdDocumentsFolder.id!;
}

/// Delete test folder structure
Future<void> deleteTestFolderStructure(
  drive.DriveApi driveApi,
  String fundingApplicationId,
) async {
  try {
    // Find and delete the funding application folder
    const claimsFolderId = '11rRXKASUWO-O0HVTSrztOEjv1Ak5CM7q';

    final folderContents = await driveApi.files.list(
      q: "'$claimsFolderId' in parents and name = '$fundingApplicationId'",
      $fields: 'files(id,name)',
    );

    if (folderContents.files != null && folderContents.files!.isNotEmpty) {
      final appFolder = folderContents.files!.first;

      // Delete all contents first
      final appFolderContents = await driveApi.files.list(
        q: "'${appFolder.id}' in parents",
        $fields: 'files(id)',
      );

      if (appFolderContents.files != null) {
        for (final file in appFolderContents.files!) {
          await driveApi.files.delete(file.id!);
        }
      }

      // Delete the app folder itself
      await driveApi.files.delete(appFolder.id!);
    }
  } catch (e) {
    print('Warning: Failed to clean up test folder: $e');
  }
}
