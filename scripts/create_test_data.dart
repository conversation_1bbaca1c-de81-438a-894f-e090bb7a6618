#!/usr/bin/env dart

import 'dart:convert';
import 'dart:math';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Test data creation script for Google Drive integration schema
///
/// Usage: dart scripts/create_test_data.dart
///
/// This script creates test data to validate:
/// 1. New fields in existing collections work correctly
/// 2. New collections can store and retrieve data
/// 3. Migration status workflows function properly
/// 4. Audit logging captures events correctly

Future<void> main() async {
  LoggerService.info(
    '🧪 Creating test data for Google Drive integration schema...',
  );

  try {
    final pocketBaseService = PocketBaseService();

    // Check PocketBase connection
    try {
      await pocketBaseService.pb.health.check();
      LoggerService.info('✅ Connected to PocketBase successfully');
    } catch (e) {
      LoggerService.error('❌ Failed to connect to PocketBase', e);
      LoggerService.error('Please ensure PocketBase is running and accessible');
      return;
    }

    LoggerService.info('');
    LoggerService.info('🔧 Creating test data for schema validation...');

    // Create test data for new collections
    await createGoogleDriveConfigTestData(pocketBaseService);
    await createMigrationBatchesTestData(pocketBaseService);
    await createDocumentAccessLogsTestData(pocketBaseService);

    // Test new fields in existing collections
    await testClaimDocumentsNewFields(pocketBaseService);
    await testFundingApplicationsNewFields(pocketBaseService);
    await testUsersNewFields(pocketBaseService);

    LoggerService.info('');
    LoggerService.info('🎉 Test data creation completed successfully!');
    LoggerService.info('');
    LoggerService.info('✅ All new collections can store data');
    LoggerService.info('✅ New fields in existing collections work correctly');
    LoggerService.info('✅ Schema modifications validated with test data');
    LoggerService.info('✅ Ready for Google Drive service implementation');
  } catch (e) {
    LoggerService.error('❌ Test data creation failed', e);
    LoggerService.error(
      'This may indicate schema modifications are incomplete',
    );
  }
}

/// Create test data for google_drive_config collection
Future<void> createGoogleDriveConfigTestData(
  PocketBaseService pbService,
) async {
  LoggerService.info(
    '📊 Creating test data for google_drive_config collection...',
  );

  try {
    final testConfigs = [
      {
        'service_account_email':
            '<EMAIL>',
        'root_folder_id': '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
        'api_quota_limit': 100000,
        'api_quota_used': 0,
        'last_quota_reset': DateTime.now().toIso8601String(),
        'encryption_enabled': true,
        'backup_enabled': true,
        'environment': 'development',
      },
      {
        'service_account_email':
            '<EMAIL>',
        'root_folder_id': '1CxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
        'api_quota_limit': 500000,
        'api_quota_used': 1250,
        'last_quota_reset':
            DateTime.now()
                .subtract(const Duration(hours: 12))
                .toIso8601String(),
        'encryption_enabled': true,
        'backup_enabled': true,
        'environment': 'staging',
      },
    ];

    for (final config in testConfigs) {
      try {
        final record = await pbService.createRecord(
          collectionName: 'google_drive_config',
          data: config,
        );
        LoggerService.info(
          '   ✅ Created ${config['environment']} config: ${record.id}',
        );
      } catch (e) {
        LoggerService.error(
          '   ❌ Failed to create ${config['environment']} config',
          e,
        );
      }
    }

    LoggerService.info('✅ google_drive_config test data created');
  } catch (e) {
    LoggerService.error('❌ Failed to create google_drive_config test data', e);
    rethrow;
  }
}

/// Create test data for migration_batches collection
Future<void> createMigrationBatchesTestData(PocketBaseService pbService) async {
  LoggerService.info(
    '📊 Creating test data for migration_batches collection...',
  );

  try {
    // Get a test user ID (try to find an existing user)
    String? testUserId;
    try {
      final users = await pbService.getList(
        collectionName: 'users',
        perPage: 1,
      );
      if (users.items.isNotEmpty) {
        testUserId = users.items.first.id;
      }
    } catch (e) {
      LoggerService.info(
        '   ⚠️  No existing users found, using placeholder ID',
      );
      testUserId = 'test_user_id';
    }

    final testBatches = [
      {
        'batch_id': 'batch_${DateTime.now().millisecondsSinceEpoch}_1',
        'start_time':
            DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
        'end_time':
            DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
        'status': 'completed',
        'total_documents': 25,
        'migrated_documents': 25,
        'failed_documents': 0,
        'error_summary': null,
        'migration_type': 'full',
        'created_by': testUserId,
      },
      {
        'batch_id': 'batch_${DateTime.now().millisecondsSinceEpoch}_2',
        'start_time':
            DateTime.now()
                .subtract(const Duration(minutes: 30))
                .toIso8601String(),
        'status': 'running',
        'total_documents': 15,
        'migrated_documents': 8,
        'failed_documents': 1,
        'error_summary': jsonEncode([
          {
            'document_id': 'doc123',
            'error': 'File too large',
            'timestamp':
                DateTime.now()
                    .toIso8601String(), // Note: In production, use autodate fields
          },
        ]),
        'migration_type': 'incremental',
        'created_by': testUserId,
      },
    ];

    for (final batch in testBatches) {
      try {
        final record = await pbService.createRecord(
          collectionName: 'migration_batches',
          data: batch,
        );
        LoggerService.info(
          '   ✅ Created migration batch: ${batch['batch_id']} (${record.id})',
        );
      } catch (e) {
        LoggerService.error(
          '   ❌ Failed to create migration batch ${batch['batch_id']}',
          e,
        );
      }
    }

    LoggerService.info('✅ migration_batches test data created');
  } catch (e) {
    LoggerService.error('❌ Failed to create migration_batches test data', e);
    rethrow;
  }
}

/// Create test data for document_access_logs collection
Future<void> createDocumentAccessLogsTestData(
  PocketBaseService pbService,
) async {
  LoggerService.info(
    '📊 Creating test data for document_access_logs collection...',
  );

  try {
    // Get test IDs
    String? testUserId;
    String? testDocumentId;

    try {
      final users = await pbService.getList(
        collectionName: 'users',
        perPage: 1,
      );
      if (users.items.isNotEmpty) {
        testUserId = users.items.first.id;
      }

      final documents = await pbService.getList(
        collectionName: 'claim_documents',
        perPage: 1,
      );
      if (documents.items.isNotEmpty) {
        testDocumentId = documents.items.first.id;
      }
    } catch (e) {
      LoggerService.info('   ⚠️  Using placeholder IDs for relations');
      testUserId = 'test_user_id';
      testDocumentId = 'test_document_id';
    }

    final random = Random();
    final actions = ['upload', 'download', 'view', 'share', 'delete'];
    final storageTypes = ['pocketbase', 'google_drive'];

    final testLogs = List.generate(5, (index) {
      final action = actions[random.nextInt(actions.length)];
      final storageType = storageTypes[random.nextInt(storageTypes.length)];
      final success = random.nextBool();

      return {
        'document_id': testDocumentId,
        'user_id': testUserId,
        'action': action,
        'timestamp':
            DateTime.now()
                .subtract(Duration(minutes: index * 15))
                .toIso8601String(), // Note: In production, use autodate fields
        'ip_address': '192.168.1.${100 + index}',
        'user_agent': 'Mozilla/5.0 (Test Browser)',
        'success': success,
        'error_message':
            success ? null : 'Test error message for action $action',
        'storage_type': storageType,
        'google_drive_file_id':
            storageType == 'google_drive' ? '1TestFileId${index}' : null,
        'file_size': random.nextInt(10000000) + 1000,
        'duration_ms': random.nextInt(5000) + 100,
      };
    });

    for (int i = 0; i < testLogs.length; i++) {
      try {
        final record = await pbService.createRecord(
          collectionName: 'document_access_logs',
          data: testLogs[i],
        );
        LoggerService.info(
          '   ✅ Created access log ${i + 1}: ${testLogs[i]['action']} (${record.id})',
        );
      } catch (e) {
        LoggerService.error('   ❌ Failed to create access log ${i + 1}', e);
      }
    }

    LoggerService.info('✅ document_access_logs test data created');
  } catch (e) {
    LoggerService.error('❌ Failed to create document_access_logs test data', e);
    rethrow;
  }
}

/// Test new fields in claim_documents collection
Future<void> testClaimDocumentsNewFields(PocketBaseService pbService) async {
  LoggerService.info('🧪 Testing new fields in claim_documents collection...');

  try {
    // Get an existing document to update
    final existingDocs = await pbService.getList(
      collectionName: 'claim_documents',
      perPage: 1,
    );

    if (existingDocs.items.isNotEmpty) {
      final doc = existingDocs.items.first;

      // Test updating with new fields
      final updateData = {
        'google_drive_folder_id': '1TestFolderId123',
        'migration_status': 'pending',
        'storage_type': 'pocketbase',
        'migration_attempts': 0,
        'migration_errors': jsonEncode([]),
        'migration_batch_id': 'test_batch_123',
      };

      try {
        await pbService.updateRecord(
          collectionName: 'claim_documents',
          recordId: doc.id,
          data: updateData,
        );
        LoggerService.info(
          '   ✅ Successfully updated document with new fields: ${doc.id}',
        );
      } catch (e) {
        LoggerService.error(
          '   ❌ Failed to update document with new fields',
          e,
        );
      }
    } else {
      LoggerService.info(
        '   ⚠️  No existing documents found to test new fields',
      );
    }

    LoggerService.info('✅ claim_documents new fields test completed');
  } catch (e) {
    LoggerService.error('❌ Failed to test claim_documents new fields', e);
    rethrow;
  }
}

/// Test new fields in funding_applications collection
Future<void> testFundingApplicationsNewFields(
  PocketBaseService pbService,
) async {
  LoggerService.info(
    '🧪 Testing new fields in funding_applications collection...',
  );

  try {
    // Get an existing application to update
    final existingApps = await pbService.getList(
      collectionName: 'funding_applications',
      perPage: 1,
    );

    if (existingApps.items.isNotEmpty) {
      final app = existingApps.items.first;

      // Test updating with new fields
      final updateData = {
        'documents_migrated': false,
        'migration_batch_id': 'test_batch_456',
      };

      try {
        await pbService.updateRecord(
          collectionName: 'funding_applications',
          recordId: app.id,
          data: updateData,
        );
        LoggerService.info(
          '   ✅ Successfully updated application with new fields: ${app.id}',
        );
      } catch (e) {
        LoggerService.error(
          '   ❌ Failed to update application with new fields',
          e,
        );
      }
    } else {
      LoggerService.info(
        '   ⚠️  No existing applications found to test new fields',
      );
    }

    LoggerService.info('✅ funding_applications new fields test completed');
  } catch (e) {
    LoggerService.error('❌ Failed to test funding_applications new fields', e);
    rethrow;
  }
}

/// Test new fields in users collection
Future<void> testUsersNewFields(PocketBaseService pbService) async {
  LoggerService.info('🧪 Testing new fields in users collection...');

  try {
    // Get an existing user to update
    final existingUsers = await pbService.getList(
      collectionName: 'users',
      perPage: 1,
    );

    if (existingUsers.items.isNotEmpty) {
      final user = existingUsers.items.first;

      // Test updating with new fields
      final updateData = {
        'google_drive_permissions': jsonEncode({
          'can_access_drive': true,
          'folder_permissions': ['read', 'write'],
          'access_level': 'standard',
        }),
        'drive_access_enabled': true,
      };

      try {
        await pbService.updateRecord(
          collectionName: 'users',
          recordId: user.id,
          data: updateData,
        );
        LoggerService.info(
          '   ✅ Successfully updated user with new fields: ${user.id}',
        );
      } catch (e) {
        LoggerService.error('   ❌ Failed to update user with new fields', e);
      }
    } else {
      LoggerService.info('   ⚠️  No existing users found to test new fields');
    }

    LoggerService.info('✅ users new fields test completed');
  } catch (e) {
    LoggerService.error('❌ Failed to test users new fields', e);
    rethrow;
  }
}
