import 'dart:io' as io;
import 'dart:convert';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';

/// Simple test script to verify Google Drive upload functionality
Future<void> main() async {
  try {
    print('🔍 Testing Google Drive upload functionality...');

    // Load service account credentials
    final credentialsFile = io.File(
      'lib/pay-global-document-storage-9179f2284308.json',
    );
    if (!await credentialsFile.exists()) {
      print('❌ Service account credentials file not found');
      return;
    }

    final credentialsContent = await credentialsFile.readAsString();
    final credentialsMap =
        jsonDecode(credentialsContent) as Map<String, dynamic>;
    final credentials = ServiceAccountCredentials.fromJson(credentialsMap);

    print('✅ Loaded service account credentials: ${credentials.email}');

    // Create authenticated client
    const scopes = [drive.DriveApi.driveScope];
    final client = await clientViaServiceAccount(credentials, scopes);

    print('✅ Created authenticated client');

    // Create Drive API instance
    final driveApi = drive.DriveApi(client);

    // Test authentication by getting user info
    final about = await driveApi.about.get($fields: 'user,storageQuota');
    print('✅ Authentication successful');
    print('   User: ${about.user?.displayName} (${about.user?.emailAddress})');

    // Test folder listing in root folder (service account's root)
    print('\n🔍 Testing folder access...');

    // First, let's see what the service account can access in its root
    final rootFileList = await driveApi.files.list(
      pageSize: 20,
      $fields: 'files(id,name,mimeType,parents,shared)',
    );

    print('✅ Successfully accessed service account root');
    print(
      '   Found ${rootFileList.files?.length ?? 0} items accessible to service account:',
    );

    if (rootFileList.files != null) {
      for (final file in rootFileList.files!) {
        print('   - ${file.name} (${file.mimeType}) [ID: ${file.id}]');
        if (file.shared == true) print('     ^ This file is shared');
      }
    }

    // Try to find the 3PayGlobal folder
    String? payGlobalFolderId;
    if (rootFileList.files != null) {
      for (final file in rootFileList.files!) {
        if (file.name == '3PayGlobal' &&
            file.mimeType == 'application/vnd.google-apps.folder') {
          payGlobalFolderId = file.id;
          print('\n✅ Found 3PayGlobal folder: $payGlobalFolderId');
          break;
        }
      }
    }

    if (payGlobalFolderId == null) {
      print('\n⚠️  3PayGlobal folder not found, creating it...');
      final payGlobalFolder =
          drive.File()
            ..name = '3PayGlobal'
            ..mimeType = 'application/vnd.google-apps.folder';

      final createdPayGlobalFolder = await driveApi.files.create(
        payGlobalFolder,
      );
      payGlobalFolderId = createdPayGlobalFolder.id!;
      print('✅ Created 3PayGlobal folder: $payGlobalFolderId');
    }

    // Test creating a test folder
    print('\n🔍 Testing folder creation...');

    final testFolderName = 'Test_${DateTime.now().millisecondsSinceEpoch}';
    final folderMetadata =
        drive.File()
          ..name = testFolderName
          ..mimeType = 'application/vnd.google-apps.folder'
          ..parents = [payGlobalFolderId];

    final createdFolder = await driveApi.files.create(folderMetadata);
    print('✅ Successfully created test folder: ${createdFolder.id}');

    // Test uploading a simple text file
    print('\n🔍 Testing file upload...');

    final testContent = 'This is a test file created at ${DateTime.now()}';
    final testFileName =
        'test_file_${DateTime.now().millisecondsSinceEpoch}.txt';

    final fileMetadata =
        drive.File()
          ..name = testFileName
          ..parents = [createdFolder.id!];

    final media = drive.Media(
      Stream.value(utf8.encode(testContent)),
      testContent.length,
      contentType: 'text/plain',
    );

    final uploadedFile = await driveApi.files.create(
      fileMetadata,
      uploadMedia: media,
    );

    print('✅ Successfully uploaded test file: ${uploadedFile.id}');
    print('   File name: ${uploadedFile.name}');

    // Test listing files in the created folder
    print('\n🔍 Testing file listing in created folder...');

    final folderContents = await driveApi.files.list(
      q: "'${createdFolder.id}' in parents",
      $fields: 'files(id,name,mimeType,size)',
    );

    print('✅ Successfully listed folder contents');
    print('   Found ${folderContents.files?.length ?? 0} files:');

    if (folderContents.files != null) {
      for (final file in folderContents.files!) {
        print('   - ${file.name} (${file.size} bytes)');
      }
    }

    // Clean up - delete the test folder and file
    print('\n🧹 Cleaning up test files...');

    if (uploadedFile.id != null) {
      await driveApi.files.delete(uploadedFile.id!);
      print('✅ Deleted test file');
    }

    if (createdFolder.id != null) {
      await driveApi.files.delete(createdFolder.id!);
      print('✅ Deleted test folder');
    }

    // Close the client
    client.close();

    print('\n🎉 All Google Drive tests passed successfully!');
    print('   The Google Drive service is working correctly.');
  } catch (e, stackTrace) {
    print('❌ Google Drive test failed: $e');
    print('Stack trace: $stackTrace');
    io.exit(1);
  }
}
