import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';

/// Script to create or update the co_funder_profiles collection in PocketBase
void main() async {
  print('Creating/Updating co_funder_profiles collection...\n');

  try {
    final pbService = PocketBaseService();

    // Check if collection exists
    print('1. Checking if co_funder_profiles collection exists...');
    
    try {
      final collections = await pbService.client.collections.getFullList();
      final existingCollection = collections.where((c) => c.name == 'co_funder_profiles').firstOrNull;
      
      if (existingCollection != null) {
        print('✅ Collection co_funder_profiles already exists');
        print('   ID: ${existingCollection.id}');
        print('   Type: ${existingCollection.type}');
        return;
      }
    } catch (e) {
      print('⚠️  Could not check existing collections: $e');
    }

    print('❌ Collection co_funder_profiles does not exist');
    print('2. Creating co_funder_profiles collection...');

    // Create the collection using the MCP tool
    // This would typically be done through the PocketBase admin interface
    // or using the collections API if available
    
    print('''
To create the co_funder_profiles collection, please use the PocketBase admin interface or MCP tool with the following structure:

Collection Name: co_funder_profiles
Type: Base Collection

Fields:
1. user_id (Relation)
   - Collection: users
   - Type: Single
   - Required: true
   - Unique: true

2. current_level (Number)
   - Required: true
   - Default: 1
   - Min: 1, Max: 4

3. aml_kyc_status (Select)
   - Required: true
   - Default: "pending"
   - Options: pending, approved, rejected, under_review

4. read_educational_content (JSON)
   - Required: false
   - Default: []

5. investment_preferences (JSON)
   - Required: false
   - Default: {}

6. notification_preferences (JSON)
   - Required: false
   - Default: {}

7. funding_capacity (Number)
   - Required: false
   - Type: Float

8. preferred_sectors (JSON)
   - Required: false
   - Default: []

9. risk_tolerance (Select)
   - Required: false
   - Options: low, medium, high

10. level_4_subscribed (Bool)
    - Required: true
    - Default: false

11. profile_picture (File)
    - Required: false
    - Max files: 1
    - Max size: 5MB
    - Types: image/jpeg, image/png, image/webp

Access Rules:
- List: @request.auth.id != "" && (@request.auth.user_type = "co_funder" || @request.auth.user_type = "admin")
- View: @request.auth.id != "" && (@request.auth.user_type = "co_funder" || @request.auth.user_type = "admin")
- Create: @request.auth.id != "" && @request.auth.user_type = "co_funder"
- Update: @request.auth.id != "" && (@request.auth.user_type = "co_funder" || @request.auth.user_type = "admin")
- Delete: @request.auth.id != "" && @request.auth.user_type = "admin"

Additional Level 3+ Fields (for KYC):
12. residential_address (Text)
13. age (Number)
14. date_of_birth (Date)
15. occupation (Text)
16. mobile_number (Text)
17. nationality (Text)
18. country_of_residence (Text)
19. net_worth (Text)
20. assets_portfolio_summary (Text)
21. available_surplus_cash (Text)
22. risk_appetite (Select: low, medium, high)
23. identity_document (File)
24. proof_of_residence (File)
25. bank_account_details_encrypted (Text)
26. nda_signed_at (Date)
27. nda_signature (File)
28. subscription_status (Select)
    - Options: inactive, active_onetime, payment_failed_onetime
    - Default: inactive

Please create this collection manually in the PocketBase admin interface.
''');

    print('\n✅ Collection structure provided above');

  } catch (e) {
    print('❌ Error: $e');
    LoggerService.error('Collection creation script error', e);
  }
}
