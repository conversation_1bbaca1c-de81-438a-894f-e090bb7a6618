import 'dart:io' as io;
import 'dart:convert';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';

/// Script to share the 3PayGlobal folder with your personal Google account
Future<void> main(List<String> args) async {
  if (args.isEmpty) {
    print('Usage: dart run scripts/share_drive_folder.dart <<EMAIL>>');
    print('Example: dart run scripts/share_drive_folder.dart <EMAIL>');
    return;
  }

  final userEmail = args[0];
  
  try {
    print('🔗 Sharing 3PayGlobal folder with $userEmail...');
    
    // Load service account credentials
    final credentialsFile = io.File('lib/pay-global-document-storage-9179f2284308.json');
    if (!await credentialsFile.exists()) {
      print('❌ Service account credentials file not found');
      return;
    }
    
    final credentialsContent = await credentialsFile.readAsString();
    final credentialsMap = jsonDecode(credentialsContent) as Map<String, dynamic>;
    final credentials = ServiceAccountCredentials.fromJson(credentialsMap);
    
    // Create authenticated client
    const scopes = [drive.DriveApi.driveScope];
    final client = await clientViaServiceAccount(credentials, scopes);
    final driveApi = drive.DriveApi(client);
    
    print('✅ Authenticated with Google Drive');
    
    // Share the 3PayGlobal folder
    const payGlobalFolderId = '1mxmvTB0M8c1rPbyh5CA90vdVBSFav8UX';
    
    // Create permission for the user
    final permission = drive.Permission()
      ..type = 'user'
      ..role = 'writer'  // Can view, edit, and organize files
      ..emailAddress = userEmail;
    
    await driveApi.permissions.create(
      permission,
      payGlobalFolderId,
      sendNotificationEmail: true,
    );
    
    print('✅ Successfully shared 3PayGlobal folder with $userEmail');
    print('📧 A notification email has been sent to $userEmail');
    print('');
    print('🔗 You can now access the folder at:');
    print('   https://drive.google.com/drive/folders/$payGlobalFolderId');
    print('');
    print('📁 Folder structure you\'ll see:');
    print('   3PayGlobal/');
    print('   └── Claims/');
    print('       ├── Archived/');
    print('       ├── Shared/');
    print('       ├── Templates/');
    print('       └── [Individual claim folders]');
    
    // Also share the Claims subfolder for easier access
    const claimsFolderId = '11rRXKASUWO-O0HVTSrztOEjv1Ak5CM7q';
    
    final claimsPermission = drive.Permission()
      ..type = 'user'
      ..role = 'writer'
      ..emailAddress = userEmail;
    
    await driveApi.permissions.create(
      claimsPermission,
      claimsFolderId,
      sendNotificationEmail: false, // Don't send another email
    );
    
    print('✅ Also shared Claims subfolder for easier access');
    
    // Close the client
    client.close();
    
    print('\n🎉 Setup complete!');
    print('💡 Tips:');
    print('   • You can now view all uploaded files in your Google Drive');
    print('   • Files will appear in real-time as they are uploaded through the app');
    print('   • You can organize, download, or share files as needed');
    print('   • The service account will continue to have full access');
    
  } catch (e, stackTrace) {
    print('❌ Failed to share folder: $e');
    print('Stack trace: $stackTrace');
    io.exit(1);
  }
}
