{"models": {"main": {"provider": "google", "modelId": "gemini-2.0-flash", "maxTokens": 120000, "temperature": 0.2}, "research": {"provider": "openrouter", "modelId": "perplexity/sonar-pro", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "google", "modelId": "gemini-2.0-flash", "maxTokens": 120000, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseUrl": "http://localhost:11434/api", "azureOpenaiBaseUrl": "https://your-endpoint.openai.azure.com/", "userId": "**********"}}