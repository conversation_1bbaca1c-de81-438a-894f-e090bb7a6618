# Alternative Dockerfile using Ubuntu base (if preferred over official Flutter image)
FROM ubuntu:22.04 AS build-env

# Avoid interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install dependencies
RUN apt-get update && \
    apt-get install -y \
        curl \
        git \
        unzip \
        xz-utils \
        zip \
        libglu1-mesa \
        ca-certificates \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Flutter SDK (clone full repo for channel support)
RUN git clone https://github.com/flutter/flutter.git /usr/local/flutter

# Set Flutter environment
ENV PATH="${PATH}:/usr/local/flutter/bin:/usr/local/flutter/bin/cache/dart-sdk/bin"
ENV FLUTTER_ROOT="/usr/local/flutter"

# Configure Flutter and switch to stable channel
RUN flutter channel stable && \
    flutter upgrade && \
    flutter config --no-analytics && \
    flutter config --enable-web && \
    flutter doctor -v

# Set working directory
WORKDIR /usr/src/app

# Copy pubspec files first for better caching
COPY pubspec.yaml pubspec.lock ./

# Get dependencies (this layer will be cached if pubspec files don't change)
RUN flutter pub get

# Copy the rest of the application
COPY . .

# Make sure dotenv file is present (optional)
RUN if [ ! -f dotenv ]; then echo "Warning: dotenv file not found - this is optional"; fi

# Build the Flutter web app
RUN flutter build web --release --verbose

# Copy dotenv file to the build output directory (if it exists)
RUN if [ -f dotenv ]; then cp dotenv /usr/src/app/build/web/; echo "Copied dotenv file"; fi

# Copy nginx.conf to build directory if it exists
RUN if [ -f /usr/src/app/web/nginx.conf ]; then \
    cp /usr/src/app/web/nginx.conf /usr/src/app/build/web/nginx.conf; \
    echo "Found and copied nginx.conf"; \
fi

# Create a lightweight production image
FROM nginx:1.25-alpine

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache ca-certificates

# Copy the built app to the web directory
COPY --from=build-env /usr/src/app/build/web /usr/share/nginx/html

# Copy nginx config if it exists using a run command to avoid linting issues
RUN if [ -f /usr/share/nginx/html/nginx.conf ]; then \
    cp /usr/share/nginx/html/nginx.conf /etc/nginx/conf.d/default.conf; \
    echo "Found and applied custom nginx config"; \
fi

# Create a basic nginx configuration if not provided
RUN if [ ! -f /etc/nginx/conf.d/default.conf ]; then \
    echo 'server { \
    listen 80; \
    root /usr/share/nginx/html; \
    index index.html; \
    location / { \
        try_files $uri $uri/ /index.html; \
    } \
}' > /etc/nginx/conf.d/default.conf; \
    echo "Created default nginx configuration"; \
fi

# Expose the web server port
EXPOSE 80

# Start NGINX
CMD ["nginx", "-g", "daemon off;"]
